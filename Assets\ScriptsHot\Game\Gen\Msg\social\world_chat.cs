// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/social/world_chat.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.social {

  /// <summary>Holder for reflection information generated from protobuf/social/world_chat.proto</summary>
  public static partial class WorldChatReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/social/world_chat.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static WorldChatReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiBwcm90b2J1Zi9zb2NpYWwvd29ybGRfY2hhdC5wcm90bxobcHJvdG9idWYv",
            "YmFzaWMvZGlhbG9nLnByb3RvGhtwcm90b2J1Zi9iYXNpYy9jb21tb24ucHJv",
            "dG8iTQoFRnJhbWUSGgoEdGV4dBgBIAEoCzIKLlRleHRGcmFtZUgAEh8KBWF1",
            "ZGlvGAIgASgLMg4uQXVkaW9SYXdGcmFtZUgAQgcKBWZyYW1lIowBCg1BdWRp",
            "b1Jhd0ZyYW1lEgoKAmlkGAEgASgEEgwKBG5hbWUYAiABKAkSDQoFYXVkaW8Y",
            "AyABKAwSEwoLc2FtcGxlX3JhdGUYBCABKA0SFAoMbnVtX2NoYW5uZWxzGAUg",
            "ASgNEhEKCWJ1YmJsZV9pZBgGIAEoBBIUCgxpc19sYXN0X2NsaXAYByABKAgi",
            "MwoJVGV4dEZyYW1lEgoKAmlkGAEgASgEEgwKBG5hbWUYAiABKAkSDAoEdGV4",
            "dBgDIAEoCSI/Cg1CaXpFdmVudEZyYW1lEgoKAmlkGAEgASgEEgwKBG5hbWUY",
            "AiABKAkSFAoMYml6RXZlbnROYW1lGAMgASgJIocCCglTQ19PdXRwdXQSEAoI",
            "ZGlhbG9nSWQYASABKAkSEQoJc2Vzc2lvbklkGAIgASgJEg0KBW1zZ0lkGAMg",
            "ASgJEhAKCGJ1YmJsZUlkGAQgASgJEh8KCG1zZ093bmVyGAUgASgOMg0uUEJf",
            "TXNnQmVsb25nEiQKC2JpekRhdGFUeXBlGAYgASgOMg8uUEJfQml6RGF0YVR5",
            "cGUSHwoFYXVkaW8YByABKAsyDi5BdWRpb1Jhd0ZyYW1lSAASGgoEdGV4dBgI",
            "IAEoCzIKLlRleHRGcmFtZUgAEiIKCGJpekV2ZW50GAkgASgLMg4uQml6RXZl",
            "bnRGcmFtZUgAQgwKCm91dHB1dERhdGEiUgoaQ1NfQ3JlYXRlV29ybGRTdG9y",
            "eVRhc2tSZXESIgoKZGlhbG9nTW9kZRgBIAEoDjIOLlBCX0RpYWxvZ01vZGUS",
            "EAoIYXZhdGFySWQYAiABKAMiyQEKGlNDX0NyZWF0ZVdvcmxkU3RvcnlUYXNr",
            "QWNrEh8KBGNvZGUYASABKA4yES5QQl9DaGF0RXJyb3JDb2RlEhAKCGRpYWxv",
            "Z0lkGAIgASgDEhcKD2NvcnJlY3RBdmF0YXJJZBgDIAEoAxILCgN0aXAYBCAB",
            "KAkSEwoLYXZhdGFyVHRzSWQYBSABKAMSEgoKYXZhdGFyVGV4dBgGIAEoCRIS",
            "CgphdmF0YXJOYW1lGAcgASgJEhUKDWF2YXRhckhlYWRVcmwYCCABKAkiWwoR",
            "Q1NfU3RhcnREaWFsb2dSZXESEAoIZGlhbG9nSWQYASABKAMSIgoKZGlhbG9n",
            "TW9kZRgCIAEoDjIOLlBCX0RpYWxvZ01vZGUSEAoIYXZhdGFySWQYAyABKAMi",
            "VAoRU0NfU3RhcnREaWFsb2dBY2sSHwoEY29kZRgBIAEoDjIRLlBCX0NoYXRF",
            "cnJvckNvZGUSDQoFbXNnSWQYAiABKAUSDwoHbXNnRGF0YRgDIAEoDCJCChpD",
            "U19GaW5pc2hXb3JsZFN0b3J5VGFza1JlcRIQCghkaWFsb2dJZBgBIAEoAxIS",
            "Cgppc0ZpbmlzaGVkGAIgASgIIsYBChpTQ19GaW5pc2hXb3JsZFN0b3J5VGFz",
            "a0FjaxIfCgRjb2RlGAEgASgOMhEuUEJfQ2hhdEVycm9yQ29kZRINCgVzY29y",
            "ZRgCIAEoAxISCgppc0ZpbmlzaGVkGAMgASgIEg8KB2NvbnRlbnQYBCABKAkS",
            "JwoKYWN0aW9uTGlzdBgFIAMoCzITLlBCX0F2YXRhck9wZXJhdGlvbhIUCgxt",
            "YXN0ZXJQbGF5ZXIYBiABKAMSFAoMbmV4dEF2YXRhcklkGAcgASgDIkgKG0NT",
            "X0dldFdvcmxkU3RvcnlNb2RlSW5mb1JlcRIQCghhdmF0YXJJZBgBIAEoAxIX",
            "Cg9zdG9yeUJhY2tncm91bmQYAiABKAkiVwobU0NfR2V0V29ybGRTdG9yeU1v",
            "ZGVJbmZvQWNrEh8KBGNvZGUYASABKA4yES5QQl9DaGF0RXJyb3JDb2RlEhcK",
            "D3N0b3J5QmFja2dyb3VuZBgCIAEoCSJvChdTQ19Vc2VyRGlhbG9nQ29udGVu",
            "dE50ZhIQCghkaWFsb2dJZBgBIAEoAxIOCgZ1c2VySWQYAiABKAMSEAoIYnVi",
            "YmxlSWQYAyABKAMSDwoHY29udGVudBgEIAEoCRIPCgdoYXNNb3JlGAUgASgI",
            "InMKGVNDX0F2YXRhckRpYWxvZ0NvbnRlbnROdGYSEAoIZGlhbG9nSWQYASAB",
            "KAMSEAoIYXZhdGFySWQYAiABKAMSEAoIYnViYmxlSWQYAyABKAMSDwoHY29u",
            "dGVudBgEIAEoCRIPCgdoYXNNb3JlGAUgASgDIlgKEVNDX0F2YXRhckF1ZGlv",
            "TnRmEhAKCGRpYWxvZ0lkGAEgASgDEhAKCGF2YXRhcklkGAIgASgDEhAKCGJ1",
            "YmJsZUlkGAMgASgDEg0KBWF1ZGlvGAQgASgMImYKG1NDX0F2YXRhckRpYWxv",
            "Z1RyYW5zbGF0ZU50ZhIQCghkaWFsb2dJZBgBIAEoAxIQCghhdmF0YXJJZBgC",
            "IAEoAxIQCghidWJibGVJZBgDIAEoAxIRCgl0cmFuc2xhdGUYBCABKAkibgoS",
            "U0NfQXZhdGFyQWR2aWNlTnRmEhAKCGRpYWxvZ0lkGAEgASgDEhAKCGF2YXRh",
            "cklkGAIgASgDEhAKCGJ1YmJsZUlkGAMgASgDEg8KB2NvbnRlbnQYBCABKAkS",
            "EQoJdHJhbnNsYXRlGAUgASgJIm8KE1NDX0F2YXRhckV4YW1wbGVOdGYSEAoI",
            "ZGlhbG9nSWQYASABKAMSEAoIYXZhdGFySWQYAiABKAMSEAoIYnViYmxlSWQY",
            "AyABKAMSDwoHY29udGVudBgEIAEoCRIRCgl0cmFuc2xhdGUYBSABKAkiYAoX",
            "U0NfV29ybGRTdG9yeVByb2Nlc3NOdGYSEAoIZGlhbG9nSWQYASABKAMSEAoI",
            "YXZhdGFySWQYAiABKAMSDQoFc2NvcmUYAyABKAMSEgoKaXNGaW5pc2hlZBgE",
            "IAEoCCJpChZDU19TdGFydFdvcmxkRGlhbG9nUmVxEgwKBGRhdGEYASABKAwS",
            "EAoIYXZhdGFySWQYAiABKAMSDgoGdXNlcklkGAMgASgDEhAKCGRpYWxvZ0lk",
            "GAQgASgDEg0KBXJvdW5kGAUgASgDIlkKFlNDX1N0YXJ0V29ybGREaWFsb2dB",
            "Y2sSHwoEY29kZRgBIAEoDjIRLlBCX0NoYXRFcnJvckNvZGUSDQoFbXNnSWQY",
            "AiABKAUSDwoHbXNnRGF0YRgDIAEoDCKOAQoTQ1NfRGlhbG9nU2V0dGluZ1Jl",
            "cRIQCghkaWFsb2dJZBgBIAEoCRIiCgpkaWFsb2dNb2RlGAIgASgOMg4uUEJf",
            "RGlhbG9nTW9kZRIQCghhdmF0YXJJZBgDIAEoCRIOCgZ1c2VySWQYBCABKAkS",
            "DgoGdGFza0lkGAUgASgJEg8KB3JvdW5kSWQYBiABKAQqRwoQUEJfQ2hhdEVy",
            "cm9yQ29kZRIZChVDaGF0RXJyb3JDb2RlX1N1Y2Nlc3MQABIYChRDaGF0RXJy",
            "b3JDb2RlX0ZhaWxlZBABKtYDCg5QQl9CaXpEYXRhVHlwZRIZChVVTktOT1dO",
            "X0JJWl9EQVRBX1RZUEUQABINCglCSVpfRVZFTlQQARIeChpTUEVFQ0hfVE9f",
            "VEVYVF9SRUNPR05JWklORxACEh0KGVNQRUVDSF9UT19URVhUX1JFQ09HTkla",
            "RUQQAxISCg5URVhUX1RPX1NQRUVDSBAEEhAKDEFWQVRBUl9SRVBMWRAFEhoK",
            "FkFWQVRBUl9SRVBMWV9UUkFOU0xBVEUQBhIWChJVU0VSX1JFUExZX0VYQU1Q",
            "TEUQBxIgChxVU0VSX1JFUExZX0VYQU1QTEVfVFJBTlNMQVRFEAgSDAoITExN",
            "X1RURkIQCRIRCg1DVVJSRU5UX1JPVU5EEAoSFAoQSVNfRklOSVNIX0RJQUxP",
            "RxALEgoKBkFEVklDRRAMEhQKEEFEVklDRV9UUkFOU0xBVEUQDRIRCg1UQVNL",
            "X1BST0dSRVNTEA4SDwoLVEFTS19SRVNVTFQQDxIcChhTVUJfR09BTF9KVURH",
            "TUVOVF9SRVNVTFQQEBInCiNTUEVFQ0hfVE9fVEVYVF9SRUNPR05JWkVEX1RS",
            "QU5TTEFURRAREhsKF1VTRVJfRU5HTElTSF9DRUZSX0xFVkVMEBJCKFoZdmZf",
            "cHJvdG9idWYvc2VydmVyL3NvY2lhbKoCCk1zZy5zb2NpYWxiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.basic.DialogReflection.Descriptor, global::Msg.basic.CommonReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.social.PB_ChatErrorCode), typeof(global::Msg.social.PB_BizDataType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.Frame), global::Msg.social.Frame.Parser, new[]{ "text", "audio" }, new[]{ "frame" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.AudioRawFrame), global::Msg.social.AudioRawFrame.Parser, new[]{ "id", "name", "audio", "sample_rate", "num_channels", "bubble_id", "is_last_clip" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.TextFrame), global::Msg.social.TextFrame.Parser, new[]{ "id", "name", "text" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.BizEventFrame), global::Msg.social.BizEventFrame.Parser, new[]{ "id", "name", "bizEventName" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_Output), global::Msg.social.SC_Output.Parser, new[]{ "dialogId", "sessionId", "msgId", "bubbleId", "msgOwner", "bizDataType", "audio", "text", "bizEvent" }, new[]{ "outputData" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.CS_CreateWorldStoryTaskReq), global::Msg.social.CS_CreateWorldStoryTaskReq.Parser, new[]{ "dialogMode", "avatarId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_CreateWorldStoryTaskAck), global::Msg.social.SC_CreateWorldStoryTaskAck.Parser, new[]{ "code", "dialogId", "correctAvatarId", "tip", "avatarTtsId", "avatarText", "avatarName", "avatarHeadUrl" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.CS_StartDialogReq), global::Msg.social.CS_StartDialogReq.Parser, new[]{ "dialogId", "dialogMode", "avatarId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_StartDialogAck), global::Msg.social.SC_StartDialogAck.Parser, new[]{ "code", "msgId", "msgData" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.CS_FinishWorldStoryTaskReq), global::Msg.social.CS_FinishWorldStoryTaskReq.Parser, new[]{ "dialogId", "isFinished" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_FinishWorldStoryTaskAck), global::Msg.social.SC_FinishWorldStoryTaskAck.Parser, new[]{ "code", "score", "isFinished", "content", "actionList", "masterPlayer", "nextAvatarId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.CS_GetWorldStoryModeInfoReq), global::Msg.social.CS_GetWorldStoryModeInfoReq.Parser, new[]{ "avatarId", "storyBackground" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_GetWorldStoryModeInfoAck), global::Msg.social.SC_GetWorldStoryModeInfoAck.Parser, new[]{ "code", "storyBackground" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_UserDialogContentNtf), global::Msg.social.SC_UserDialogContentNtf.Parser, new[]{ "dialogId", "userId", "bubbleId", "content", "hasMore" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_AvatarDialogContentNtf), global::Msg.social.SC_AvatarDialogContentNtf.Parser, new[]{ "dialogId", "avatarId", "bubbleId", "content", "hasMore" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_AvatarAudioNtf), global::Msg.social.SC_AvatarAudioNtf.Parser, new[]{ "dialogId", "avatarId", "bubbleId", "audio" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_AvatarDialogTranslateNtf), global::Msg.social.SC_AvatarDialogTranslateNtf.Parser, new[]{ "dialogId", "avatarId", "bubbleId", "translate" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_AvatarAdviceNtf), global::Msg.social.SC_AvatarAdviceNtf.Parser, new[]{ "dialogId", "avatarId", "bubbleId", "content", "translate" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_AvatarExampleNtf), global::Msg.social.SC_AvatarExampleNtf.Parser, new[]{ "dialogId", "avatarId", "bubbleId", "content", "translate" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_WorldStoryProcessNtf), global::Msg.social.SC_WorldStoryProcessNtf.Parser, new[]{ "dialogId", "avatarId", "score", "isFinished" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.CS_StartWorldDialogReq), global::Msg.social.CS_StartWorldDialogReq.Parser, new[]{ "data", "avatarId", "userId", "dialogId", "round" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.SC_StartWorldDialogAck), global::Msg.social.SC_StartWorldDialogAck.Parser, new[]{ "code", "msgId", "msgData" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.social.CS_DialogSettingReq), global::Msg.social.CS_DialogSettingReq.Parser, new[]{ "dialogId", "dialogMode", "avatarId", "userId", "taskId", "roundId" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum PB_ChatErrorCode {
    [pbr::OriginalName("ChatErrorCode_Success")] ChatErrorCode_Success = 0,
    [pbr::OriginalName("ChatErrorCode_Failed")] ChatErrorCode_Failed = 1,
  }

  public enum PB_BizDataType {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("UNKNOWN_BIZ_DATA_TYPE")] UNKNOWN_BIZ_DATA_TYPE = 0,
    /// <summary>
    /// 业务事件
    /// </summary>
    [pbr::OriginalName("BIZ_EVENT")] BIZ_EVENT = 1,
    /// <summary>
    /// 语音识别过程结果
    /// </summary>
    [pbr::OriginalName("SPEECH_TO_TEXT_RECOGNIZING")] SPEECH_TO_TEXT_RECOGNIZING = 2,
    /// <summary>
    /// 语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SPEECH_TO_TEXT_RECOGNIZED")] SPEECH_TO_TEXT_RECOGNIZED = 3,
    /// <summary>
    /// 语音合成结果
    /// </summary>
    [pbr::OriginalName("TEXT_TO_SPEECH")] TEXT_TO_SPEECH = 4,
    /// <summary>
    /// Avatar回复文本
    /// </summary>
    [pbr::OriginalName("AVATAR_REPLY")] AVATAR_REPLY = 5,
    /// <summary>
    /// Avatar回复文本翻译
    /// </summary>
    [pbr::OriginalName("AVATAR_REPLY_TRANSLATE")] AVATAR_REPLY_TRANSLATE = 6,
    /// <summary>
    /// 用户回复示例
    /// </summary>
    [pbr::OriginalName("USER_REPLY_EXAMPLE")] USER_REPLY_EXAMPLE = 7,
    /// <summary>
    /// 用户回复示例翻译
    /// </summary>
    [pbr::OriginalName("USER_REPLY_EXAMPLE_TRANSLATE")] USER_REPLY_EXAMPLE_TRANSLATE = 8,
    /// <summary>
    /// LLM首包时延（TTFB 代表 "Time to First Byte"（首字节时间））
    /// </summary>
    [pbr::OriginalName("LLM_TTFB")] LLM_TTFB = 9,
    /// <summary>
    /// 当前轮次
    /// </summary>
    [pbr::OriginalName("CURRENT_ROUND")] CURRENT_ROUND = 10,
    /// <summary>
    /// 是否结束对话
    /// </summary>
    [pbr::OriginalName("IS_FINISH_DIALOG")] IS_FINISH_DIALOG = 11,
    /// <summary>
    /// 用户建议
    /// </summary>
    [pbr::OriginalName("ADVICE")] ADVICE = 12,
    /// <summary>
    /// 用户建议翻译
    /// </summary>
    [pbr::OriginalName("ADVICE_TRANSLATE")] ADVICE_TRANSLATE = 13,
    /// <summary>
    /// 任务进度
    /// </summary>
    [pbr::OriginalName("TASK_PROGRESS")] TASK_PROGRESS = 14,
    /// <summary>
    /// 任务完成结果
    /// </summary>
    [pbr::OriginalName("TASK_RESULT")] TASK_RESULT = 15,
    /// <summary>
    /// 子目标判定结果
    /// </summary>
    [pbr::OriginalName("SUB_GOAL_JUDGMENT_RESULT")] SUB_GOAL_JUDGMENT_RESULT = 16,
    /// <summary>
    /// 用户语音识别最终结果翻译
    /// </summary>
    [pbr::OriginalName("SPEECH_TO_TEXT_RECOGNIZED_TRANSLATE")] SPEECH_TO_TEXT_RECOGNIZED_TRANSLATE = 17,
    /// <summary>
    /// 用户英语CEFR等级
    /// </summary>
    [pbr::OriginalName("USER_ENGLISH_CEFR_LEVEL")] USER_ENGLISH_CEFR_LEVEL = 18,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Frame : pb::IMessage<Frame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Frame> _parser = new pb::MessageParser<Frame>(() => new Frame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Frame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Frame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Frame(Frame other) : this() {
      switch (other.frameCase) {
        case frameOneofCase.text:
          text = other.text.Clone();
          break;
        case frameOneofCase.audio:
          audio = other.audio.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Frame Clone() {
      return new Frame(this);
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.TextFrame text {
      get { return frameCase_ == frameOneofCase.text ? (global::Msg.social.TextFrame) frame_ : null; }
      set {
        frame_ = value;
        frameCase_ = value == null ? frameOneofCase.None : frameOneofCase.text;
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 2;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.AudioRawFrame audio {
      get { return frameCase_ == frameOneofCase.audio ? (global::Msg.social.AudioRawFrame) frame_ : null; }
      set {
        frame_ = value;
        frameCase_ = value == null ? frameOneofCase.None : frameOneofCase.audio;
      }
    }

    private object frame_;
    /// <summary>Enum of possible cases for the "frame" oneof.</summary>
    public enum frameOneofCase {
      None = 0,
      text = 1,
      audio = 2,
    }
    private frameOneofCase frameCase_ = frameOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public frameOneofCase frameCase {
      get { return frameCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void Clearframe() {
      frameCase_ = frameOneofCase.None;
      frame_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Frame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Frame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(text, other.text)) return false;
      if (!object.Equals(audio, other.audio)) return false;
      if (frameCase != other.frameCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (frameCase_ == frameOneofCase.text) hash ^= text.GetHashCode();
      if (frameCase_ == frameOneofCase.audio) hash ^= audio.GetHashCode();
      hash ^= (int) frameCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (frameCase_ == frameOneofCase.text) {
        output.WriteRawTag(10);
        output.WriteMessage(text);
      }
      if (frameCase_ == frameOneofCase.audio) {
        output.WriteRawTag(18);
        output.WriteMessage(audio);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (frameCase_ == frameOneofCase.text) {
        output.WriteRawTag(10);
        output.WriteMessage(text);
      }
      if (frameCase_ == frameOneofCase.audio) {
        output.WriteRawTag(18);
        output.WriteMessage(audio);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (frameCase_ == frameOneofCase.text) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(text);
      }
      if (frameCase_ == frameOneofCase.audio) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(audio);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Frame other) {
      if (other == null) {
        return;
      }
      switch (other.frameCase) {
        case frameOneofCase.text:
          if (text == null) {
            text = new global::Msg.social.TextFrame();
          }
          text.MergeFrom(other.text);
          break;
        case frameOneofCase.audio:
          if (audio == null) {
            audio = new global::Msg.social.AudioRawFrame();
          }
          audio.MergeFrom(other.audio);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::Msg.social.TextFrame subBuilder = new global::Msg.social.TextFrame();
            if (frameCase_ == frameOneofCase.text) {
              subBuilder.MergeFrom(text);
            }
            input.ReadMessage(subBuilder);
            text = subBuilder;
            break;
          }
          case 18: {
            global::Msg.social.AudioRawFrame subBuilder = new global::Msg.social.AudioRawFrame();
            if (frameCase_ == frameOneofCase.audio) {
              subBuilder.MergeFrom(audio);
            }
            input.ReadMessage(subBuilder);
            audio = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            global::Msg.social.TextFrame subBuilder = new global::Msg.social.TextFrame();
            if (frameCase_ == frameOneofCase.text) {
              subBuilder.MergeFrom(text);
            }
            input.ReadMessage(subBuilder);
            text = subBuilder;
            break;
          }
          case 18: {
            global::Msg.social.AudioRawFrame subBuilder = new global::Msg.social.AudioRawFrame();
            if (frameCase_ == frameOneofCase.audio) {
              subBuilder.MergeFrom(audio);
            }
            input.ReadMessage(subBuilder);
            audio = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 音频框架
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AudioRawFrame : pb::IMessage<AudioRawFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AudioRawFrame> _parser = new pb::MessageParser<AudioRawFrame>(() => new AudioRawFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AudioRawFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AudioRawFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AudioRawFrame(AudioRawFrame other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      audio_ = other.audio_;
      sample_rate_ = other.sample_rate_;
      num_channels_ = other.num_channels_;
      bubble_id_ = other.bubble_id_;
      is_last_clip_ = other.is_last_clip_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AudioRawFrame Clone() {
      return new AudioRawFrame(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int idFieldNumber = 1;
    private ulong id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int nameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 3;
    private pb::ByteString audio_ = pb::ByteString.Empty;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString audio {
      get { return audio_; }
      set {
        audio_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sample_rate" field.</summary>
    public const int sample_rateFieldNumber = 4;
    private uint sample_rate_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint sample_rate {
      get { return sample_rate_; }
      set {
        sample_rate_ = value;
      }
    }

    /// <summary>Field number for the "num_channels" field.</summary>
    public const int num_channelsFieldNumber = 5;
    private uint num_channels_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint num_channels {
      get { return num_channels_; }
      set {
        num_channels_ = value;
      }
    }

    /// <summary>Field number for the "bubble_id" field.</summary>
    public const int bubble_idFieldNumber = 6;
    private ulong bubble_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong bubble_id {
      get { return bubble_id_; }
      set {
        bubble_id_ = value;
      }
    }

    /// <summary>Field number for the "is_last_clip" field.</summary>
    public const int is_last_clipFieldNumber = 7;
    private bool is_last_clip_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_last_clip {
      get { return is_last_clip_; }
      set {
        is_last_clip_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AudioRawFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AudioRawFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (id != other.id) return false;
      if (name != other.name) return false;
      if (audio != other.audio) return false;
      if (sample_rate != other.sample_rate) return false;
      if (num_channels != other.num_channels) return false;
      if (bubble_id != other.bubble_id) return false;
      if (is_last_clip != other.is_last_clip) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (id != 0UL) hash ^= id.GetHashCode();
      if (name.Length != 0) hash ^= name.GetHashCode();
      if (audio.Length != 0) hash ^= audio.GetHashCode();
      if (sample_rate != 0) hash ^= sample_rate.GetHashCode();
      if (num_channels != 0) hash ^= num_channels.GetHashCode();
      if (bubble_id != 0UL) hash ^= bubble_id.GetHashCode();
      if (is_last_clip != false) hash ^= is_last_clip.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(num_channels);
      }
      if (bubble_id != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(bubble_id);
      }
      if (is_last_clip != false) {
        output.WriteRawTag(56);
        output.WriteBool(is_last_clip);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(num_channels);
      }
      if (bubble_id != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(bubble_id);
      }
      if (is_last_clip != false) {
        output.WriteRawTag(56);
        output.WriteBool(is_last_clip);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(id);
      }
      if (name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(name);
      }
      if (audio.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(audio);
      }
      if (sample_rate != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(sample_rate);
      }
      if (num_channels != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(num_channels);
      }
      if (bubble_id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(bubble_id);
      }
      if (is_last_clip != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AudioRawFrame other) {
      if (other == null) {
        return;
      }
      if (other.id != 0UL) {
        id = other.id;
      }
      if (other.name.Length != 0) {
        name = other.name;
      }
      if (other.audio.Length != 0) {
        audio = other.audio;
      }
      if (other.sample_rate != 0) {
        sample_rate = other.sample_rate;
      }
      if (other.num_channels != 0) {
        num_channels = other.num_channels;
      }
      if (other.bubble_id != 0UL) {
        bubble_id = other.bubble_id;
      }
      if (other.is_last_clip != false) {
        is_last_clip = other.is_last_clip;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            audio = input.ReadBytes();
            break;
          }
          case 32: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 40: {
            num_channels = input.ReadUInt32();
            break;
          }
          case 48: {
            bubble_id = input.ReadUInt64();
            break;
          }
          case 56: {
            is_last_clip = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            audio = input.ReadBytes();
            break;
          }
          case 32: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 40: {
            num_channels = input.ReadUInt32();
            break;
          }
          case 48: {
            bubble_id = input.ReadUInt64();
            break;
          }
          case 56: {
            is_last_clip = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 文本框架
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TextFrame : pb::IMessage<TextFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TextFrame> _parser = new pb::MessageParser<TextFrame>(() => new TextFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TextFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TextFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TextFrame(TextFrame other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      text_ = other.text_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TextFrame Clone() {
      return new TextFrame(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int idFieldNumber = 1;
    private ulong id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int nameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 3;
    private string text_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TextFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TextFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (id != other.id) return false;
      if (name != other.name) return false;
      if (text != other.text) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (id != 0UL) hash ^= id.GetHashCode();
      if (name.Length != 0) hash ^= name.GetHashCode();
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(id);
      }
      if (name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(name);
      }
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TextFrame other) {
      if (other == null) {
        return;
      }
      if (other.id != 0UL) {
        id = other.id;
      }
      if (other.name.Length != 0) {
        name = other.name;
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 业务事件框架
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BizEventFrame : pb::IMessage<BizEventFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BizEventFrame> _parser = new pb::MessageParser<BizEventFrame>(() => new BizEventFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BizEventFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BizEventFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BizEventFrame(BizEventFrame other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      bizEventName_ = other.bizEventName_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BizEventFrame Clone() {
      return new BizEventFrame(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int idFieldNumber = 1;
    private ulong id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int nameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bizEventName" field.</summary>
    public const int bizEventNameFieldNumber = 3;
    private string bizEventName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bizEventName {
      get { return bizEventName_; }
      set {
        bizEventName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BizEventFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BizEventFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (id != other.id) return false;
      if (name != other.name) return false;
      if (bizEventName != other.bizEventName) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (id != 0UL) hash ^= id.GetHashCode();
      if (name.Length != 0) hash ^= name.GetHashCode();
      if (bizEventName.Length != 0) hash ^= bizEventName.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (bizEventName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bizEventName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (bizEventName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bizEventName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(id);
      }
      if (name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(name);
      }
      if (bizEventName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bizEventName);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BizEventFrame other) {
      if (other == null) {
        return;
      }
      if (other.id != 0UL) {
        id = other.id;
      }
      if (other.name.Length != 0) {
        name = other.name;
      }
      if (other.bizEventName.Length != 0) {
        bizEventName = other.bizEventName;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            bizEventName = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            bizEventName = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_Output : pb::IMessage<SC_Output>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_Output> _parser = new pb::MessageParser<SC_Output>(() => new SC_Output());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_Output> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_Output() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_Output(SC_Output other) : this() {
      dialogId_ = other.dialogId_;
      sessionId_ = other.sessionId_;
      msgId_ = other.msgId_;
      bubbleId_ = other.bubbleId_;
      msgOwner_ = other.msgOwner_;
      bizDataType_ = other.bizDataType_;
      switch (other.outputDataCase) {
        case outputDataOneofCase.audio:
          audio = other.audio.Clone();
          break;
        case outputDataOneofCase.text:
          text = other.text.Clone();
          break;
        case outputDataOneofCase.bizEvent:
          bizEvent = other.bizEvent.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_Output Clone() {
      return new SC_Output(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private string dialogId_ = "";
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sessionId" field.</summary>
    public const int sessionIdFieldNumber = 2;
    private string sessionId_ = "";
    /// <summary>
    /// 会话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string sessionId {
      get { return sessionId_; }
      set {
        sessionId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "msgId" field.</summary>
    public const int msgIdFieldNumber = 3;
    private string msgId_ = "";
    /// <summary>
    /// 消息id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string msgId {
      get { return msgId_; }
      set {
        msgId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bubbleId" field.</summary>
    public const int bubbleIdFieldNumber = 4;
    private string bubbleId_ = "";
    /// <summary>
    /// 对话气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bubbleId {
      get { return bubbleId_; }
      set {
        bubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "msgOwner" field.</summary>
    public const int msgOwnerFieldNumber = 5;
    private global::Msg.basic.PB_MsgBelong msgOwner_ = global::Msg.basic.PB_MsgBelong.BNone;
    /// <summary>
    /// 消息归属方
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_MsgBelong msgOwner {
      get { return msgOwner_; }
      set {
        msgOwner_ = value;
      }
    }

    /// <summary>Field number for the "bizDataType" field.</summary>
    public const int bizDataTypeFieldNumber = 6;
    private global::Msg.social.PB_BizDataType bizDataType_ = global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE;
    /// <summary>
    /// 业务数据类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.PB_BizDataType bizDataType {
      get { return bizDataType_; }
      set {
        bizDataType_ = value;
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 7;
    /// <summary>
    /// 音频数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.AudioRawFrame audio {
      get { return outputDataCase_ == outputDataOneofCase.audio ? (global::Msg.social.AudioRawFrame) outputData_ : null; }
      set {
        outputData_ = value;
        outputDataCase_ = value == null ? outputDataOneofCase.None : outputDataOneofCase.audio;
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 8;
    /// <summary>
    /// 文本数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.TextFrame text {
      get { return outputDataCase_ == outputDataOneofCase.text ? (global::Msg.social.TextFrame) outputData_ : null; }
      set {
        outputData_ = value;
        outputDataCase_ = value == null ? outputDataOneofCase.None : outputDataOneofCase.text;
      }
    }

    /// <summary>Field number for the "bizEvent" field.</summary>
    public const int bizEventFieldNumber = 9;
    /// <summary>
    /// 业务事件数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.BizEventFrame bizEvent {
      get { return outputDataCase_ == outputDataOneofCase.bizEvent ? (global::Msg.social.BizEventFrame) outputData_ : null; }
      set {
        outputData_ = value;
        outputDataCase_ = value == null ? outputDataOneofCase.None : outputDataOneofCase.bizEvent;
      }
    }

    private object outputData_;
    /// <summary>Enum of possible cases for the "outputData" oneof.</summary>
    public enum outputDataOneofCase {
      None = 0,
      audio = 7,
      text = 8,
      bizEvent = 9,
    }
    private outputDataOneofCase outputDataCase_ = outputDataOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public outputDataOneofCase outputDataCase {
      get { return outputDataCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearoutputData() {
      outputDataCase_ = outputDataOneofCase.None;
      outputData_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_Output);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_Output other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (sessionId != other.sessionId) return false;
      if (msgId != other.msgId) return false;
      if (bubbleId != other.bubbleId) return false;
      if (msgOwner != other.msgOwner) return false;
      if (bizDataType != other.bizDataType) return false;
      if (!object.Equals(audio, other.audio)) return false;
      if (!object.Equals(text, other.text)) return false;
      if (!object.Equals(bizEvent, other.bizEvent)) return false;
      if (outputDataCase != other.outputDataCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId.Length != 0) hash ^= dialogId.GetHashCode();
      if (sessionId.Length != 0) hash ^= sessionId.GetHashCode();
      if (msgId.Length != 0) hash ^= msgId.GetHashCode();
      if (bubbleId.Length != 0) hash ^= bubbleId.GetHashCode();
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) hash ^= msgOwner.GetHashCode();
      if (bizDataType != global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE) hash ^= bizDataType.GetHashCode();
      if (outputDataCase_ == outputDataOneofCase.audio) hash ^= audio.GetHashCode();
      if (outputDataCase_ == outputDataOneofCase.text) hash ^= text.GetHashCode();
      if (outputDataCase_ == outputDataOneofCase.bizEvent) hash ^= bizEvent.GetHashCode();
      hash ^= (int) outputDataCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (sessionId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(sessionId);
      }
      if (msgId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(msgId);
      }
      if (bubbleId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(bubbleId);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) msgOwner);
      }
      if (bizDataType != global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE) {
        output.WriteRawTag(48);
        output.WriteEnum((int) bizDataType);
      }
      if (outputDataCase_ == outputDataOneofCase.audio) {
        output.WriteRawTag(58);
        output.WriteMessage(audio);
      }
      if (outputDataCase_ == outputDataOneofCase.text) {
        output.WriteRawTag(66);
        output.WriteMessage(text);
      }
      if (outputDataCase_ == outputDataOneofCase.bizEvent) {
        output.WriteRawTag(74);
        output.WriteMessage(bizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (sessionId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(sessionId);
      }
      if (msgId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(msgId);
      }
      if (bubbleId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(bubbleId);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) msgOwner);
      }
      if (bizDataType != global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE) {
        output.WriteRawTag(48);
        output.WriteEnum((int) bizDataType);
      }
      if (outputDataCase_ == outputDataOneofCase.audio) {
        output.WriteRawTag(58);
        output.WriteMessage(audio);
      }
      if (outputDataCase_ == outputDataOneofCase.text) {
        output.WriteRawTag(66);
        output.WriteMessage(text);
      }
      if (outputDataCase_ == outputDataOneofCase.bizEvent) {
        output.WriteRawTag(74);
        output.WriteMessage(bizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(dialogId);
      }
      if (sessionId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(sessionId);
      }
      if (msgId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(msgId);
      }
      if (bubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bubbleId);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) msgOwner);
      }
      if (bizDataType != global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) bizDataType);
      }
      if (outputDataCase_ == outputDataOneofCase.audio) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(audio);
      }
      if (outputDataCase_ == outputDataOneofCase.text) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(text);
      }
      if (outputDataCase_ == outputDataOneofCase.bizEvent) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(bizEvent);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_Output other) {
      if (other == null) {
        return;
      }
      if (other.dialogId.Length != 0) {
        dialogId = other.dialogId;
      }
      if (other.sessionId.Length != 0) {
        sessionId = other.sessionId;
      }
      if (other.msgId.Length != 0) {
        msgId = other.msgId;
      }
      if (other.bubbleId.Length != 0) {
        bubbleId = other.bubbleId;
      }
      if (other.msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        msgOwner = other.msgOwner;
      }
      if (other.bizDataType != global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE) {
        bizDataType = other.bizDataType;
      }
      switch (other.outputDataCase) {
        case outputDataOneofCase.audio:
          if (audio == null) {
            audio = new global::Msg.social.AudioRawFrame();
          }
          audio.MergeFrom(other.audio);
          break;
        case outputDataOneofCase.text:
          if (text == null) {
            text = new global::Msg.social.TextFrame();
          }
          text.MergeFrom(other.text);
          break;
        case outputDataOneofCase.bizEvent:
          if (bizEvent == null) {
            bizEvent = new global::Msg.social.BizEventFrame();
          }
          bizEvent.MergeFrom(other.bizEvent);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 18: {
            sessionId = input.ReadString();
            break;
          }
          case 26: {
            msgId = input.ReadString();
            break;
          }
          case 34: {
            bubbleId = input.ReadString();
            break;
          }
          case 40: {
            msgOwner = (global::Msg.basic.PB_MsgBelong) input.ReadEnum();
            break;
          }
          case 48: {
            bizDataType = (global::Msg.social.PB_BizDataType) input.ReadEnum();
            break;
          }
          case 58: {
            global::Msg.social.AudioRawFrame subBuilder = new global::Msg.social.AudioRawFrame();
            if (outputDataCase_ == outputDataOneofCase.audio) {
              subBuilder.MergeFrom(audio);
            }
            input.ReadMessage(subBuilder);
            audio = subBuilder;
            break;
          }
          case 66: {
            global::Msg.social.TextFrame subBuilder = new global::Msg.social.TextFrame();
            if (outputDataCase_ == outputDataOneofCase.text) {
              subBuilder.MergeFrom(text);
            }
            input.ReadMessage(subBuilder);
            text = subBuilder;
            break;
          }
          case 74: {
            global::Msg.social.BizEventFrame subBuilder = new global::Msg.social.BizEventFrame();
            if (outputDataCase_ == outputDataOneofCase.bizEvent) {
              subBuilder.MergeFrom(bizEvent);
            }
            input.ReadMessage(subBuilder);
            bizEvent = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 18: {
            sessionId = input.ReadString();
            break;
          }
          case 26: {
            msgId = input.ReadString();
            break;
          }
          case 34: {
            bubbleId = input.ReadString();
            break;
          }
          case 40: {
            msgOwner = (global::Msg.basic.PB_MsgBelong) input.ReadEnum();
            break;
          }
          case 48: {
            bizDataType = (global::Msg.social.PB_BizDataType) input.ReadEnum();
            break;
          }
          case 58: {
            global::Msg.social.AudioRawFrame subBuilder = new global::Msg.social.AudioRawFrame();
            if (outputDataCase_ == outputDataOneofCase.audio) {
              subBuilder.MergeFrom(audio);
            }
            input.ReadMessage(subBuilder);
            audio = subBuilder;
            break;
          }
          case 66: {
            global::Msg.social.TextFrame subBuilder = new global::Msg.social.TextFrame();
            if (outputDataCase_ == outputDataOneofCase.text) {
              subBuilder.MergeFrom(text);
            }
            input.ReadMessage(subBuilder);
            text = subBuilder;
            break;
          }
          case 74: {
            global::Msg.social.BizEventFrame subBuilder = new global::Msg.social.BizEventFrame();
            if (outputDataCase_ == outputDataOneofCase.bizEvent) {
              subBuilder.MergeFrom(bizEvent);
            }
            input.ReadMessage(subBuilder);
            bizEvent = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_CreateWorldStoryTaskReq : pb::IMessage<CS_CreateWorldStoryTaskReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_CreateWorldStoryTaskReq> _parser = new pb::MessageParser<CS_CreateWorldStoryTaskReq>(() => new CS_CreateWorldStoryTaskReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_CreateWorldStoryTaskReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_CreateWorldStoryTaskReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_CreateWorldStoryTaskReq(CS_CreateWorldStoryTaskReq other) : this() {
      dialogMode_ = other.dialogMode_;
      avatarId_ = other.avatarId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_CreateWorldStoryTaskReq Clone() {
      return new CS_CreateWorldStoryTaskReq(this);
    }

    /// <summary>Field number for the "dialogMode" field.</summary>
    public const int dialogModeFieldNumber = 1;
    private global::Msg.basic.PB_DialogMode dialogMode_ = global::Msg.basic.PB_DialogMode.MNone;
    /// <summary>
    /// 对话模式
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogMode dialogMode {
      get { return dialogMode_; }
      set {
        dialogMode_ = value;
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 2;
    private long avatarId_;
    /// <summary>
    /// avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_CreateWorldStoryTaskReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_CreateWorldStoryTaskReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogMode != other.dialogMode) return false;
      if (avatarId != other.avatarId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) hash ^= dialogMode.GetHashCode();
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(8);
        output.WriteEnum((int) dialogMode);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(8);
        output.WriteEnum((int) dialogMode);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) dialogMode);
      }
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_CreateWorldStoryTaskReq other) {
      if (other == null) {
        return;
      }
      if (other.dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        dialogMode = other.dialogMode;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_CreateWorldStoryTaskAck : pb::IMessage<SC_CreateWorldStoryTaskAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_CreateWorldStoryTaskAck> _parser = new pb::MessageParser<SC_CreateWorldStoryTaskAck>(() => new SC_CreateWorldStoryTaskAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_CreateWorldStoryTaskAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_CreateWorldStoryTaskAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_CreateWorldStoryTaskAck(SC_CreateWorldStoryTaskAck other) : this() {
      code_ = other.code_;
      dialogId_ = other.dialogId_;
      correctAvatarId_ = other.correctAvatarId_;
      tip_ = other.tip_;
      avatarTtsId_ = other.avatarTtsId_;
      avatarText_ = other.avatarText_;
      avatarName_ = other.avatarName_;
      avatarHeadUrl_ = other.avatarHeadUrl_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_CreateWorldStoryTaskAck Clone() {
      return new SC_CreateWorldStoryTaskAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.social.PB_ChatErrorCode code_ = global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success;
    /// <summary>
    /// 返回码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.PB_ChatErrorCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 2;
    private long dialogId_;
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "correctAvatarId" field.</summary>
    public const int correctAvatarIdFieldNumber = 3;
    private long correctAvatarId_;
    /// <summary>
    /// 正确的avatar(选错avatar时返回)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long correctAvatarId {
      get { return correctAvatarId_; }
      set {
        correctAvatarId_ = value;
      }
    }

    /// <summary>Field number for the "tip" field.</summary>
    public const int tipFieldNumber = 4;
    private string tip_ = "";
    /// <summary>
    /// 提示信息(选错avatar时返回)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string tip {
      get { return tip_; }
      set {
        tip_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "avatarTtsId" field.</summary>
    public const int avatarTtsIdFieldNumber = 5;
    private long avatarTtsId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarTtsId {
      get { return avatarTtsId_; }
      set {
        avatarTtsId_ = value;
      }
    }

    /// <summary>Field number for the "avatarText" field.</summary>
    public const int avatarTextFieldNumber = 6;
    private string avatarText_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string avatarText {
      get { return avatarText_; }
      set {
        avatarText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "avatarName" field.</summary>
    public const int avatarNameFieldNumber = 7;
    private string avatarName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string avatarName {
      get { return avatarName_; }
      set {
        avatarName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "avatarHeadUrl" field.</summary>
    public const int avatarHeadUrlFieldNumber = 8;
    private string avatarHeadUrl_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string avatarHeadUrl {
      get { return avatarHeadUrl_; }
      set {
        avatarHeadUrl_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_CreateWorldStoryTaskAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_CreateWorldStoryTaskAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (dialogId != other.dialogId) return false;
      if (correctAvatarId != other.correctAvatarId) return false;
      if (tip != other.tip) return false;
      if (avatarTtsId != other.avatarTtsId) return false;
      if (avatarText != other.avatarText) return false;
      if (avatarName != other.avatarName) return false;
      if (avatarHeadUrl != other.avatarHeadUrl) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) hash ^= code.GetHashCode();
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (correctAvatarId != 0L) hash ^= correctAvatarId.GetHashCode();
      if (tip.Length != 0) hash ^= tip.GetHashCode();
      if (avatarTtsId != 0L) hash ^= avatarTtsId.GetHashCode();
      if (avatarText.Length != 0) hash ^= avatarText.GetHashCode();
      if (avatarName.Length != 0) hash ^= avatarName.GetHashCode();
      if (avatarHeadUrl.Length != 0) hash ^= avatarHeadUrl.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (dialogId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(dialogId);
      }
      if (correctAvatarId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(correctAvatarId);
      }
      if (tip.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(tip);
      }
      if (avatarTtsId != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(avatarTtsId);
      }
      if (avatarText.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(avatarText);
      }
      if (avatarName.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(avatarName);
      }
      if (avatarHeadUrl.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(avatarHeadUrl);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (dialogId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(dialogId);
      }
      if (correctAvatarId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(correctAvatarId);
      }
      if (tip.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(tip);
      }
      if (avatarTtsId != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(avatarTtsId);
      }
      if (avatarText.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(avatarText);
      }
      if (avatarName.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(avatarName);
      }
      if (avatarHeadUrl.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(avatarHeadUrl);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (correctAvatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(correctAvatarId);
      }
      if (tip.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(tip);
      }
      if (avatarTtsId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarTtsId);
      }
      if (avatarText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(avatarText);
      }
      if (avatarName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(avatarName);
      }
      if (avatarHeadUrl.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(avatarHeadUrl);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_CreateWorldStoryTaskAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        code = other.code;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.correctAvatarId != 0L) {
        correctAvatarId = other.correctAvatarId;
      }
      if (other.tip.Length != 0) {
        tip = other.tip;
      }
      if (other.avatarTtsId != 0L) {
        avatarTtsId = other.avatarTtsId;
      }
      if (other.avatarText.Length != 0) {
        avatarText = other.avatarText;
      }
      if (other.avatarName.Length != 0) {
        avatarName = other.avatarName;
      }
      if (other.avatarHeadUrl.Length != 0) {
        avatarHeadUrl = other.avatarHeadUrl;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.social.PB_ChatErrorCode) input.ReadEnum();
            break;
          }
          case 16: {
            dialogId = input.ReadInt64();
            break;
          }
          case 24: {
            correctAvatarId = input.ReadInt64();
            break;
          }
          case 34: {
            tip = input.ReadString();
            break;
          }
          case 40: {
            avatarTtsId = input.ReadInt64();
            break;
          }
          case 50: {
            avatarText = input.ReadString();
            break;
          }
          case 58: {
            avatarName = input.ReadString();
            break;
          }
          case 66: {
            avatarHeadUrl = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.social.PB_ChatErrorCode) input.ReadEnum();
            break;
          }
          case 16: {
            dialogId = input.ReadInt64();
            break;
          }
          case 24: {
            correctAvatarId = input.ReadInt64();
            break;
          }
          case 34: {
            tip = input.ReadString();
            break;
          }
          case 40: {
            avatarTtsId = input.ReadInt64();
            break;
          }
          case 50: {
            avatarText = input.ReadString();
            break;
          }
          case 58: {
            avatarName = input.ReadString();
            break;
          }
          case 66: {
            avatarHeadUrl = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_StartDialogReq : pb::IMessage<CS_StartDialogReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_StartDialogReq> _parser = new pb::MessageParser<CS_StartDialogReq>(() => new CS_StartDialogReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_StartDialogReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_StartDialogReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_StartDialogReq(CS_StartDialogReq other) : this() {
      dialogId_ = other.dialogId_;
      dialogMode_ = other.dialogMode_;
      avatarId_ = other.avatarId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_StartDialogReq Clone() {
      return new CS_StartDialogReq(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private long dialogId_;
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "dialogMode" field.</summary>
    public const int dialogModeFieldNumber = 2;
    private global::Msg.basic.PB_DialogMode dialogMode_ = global::Msg.basic.PB_DialogMode.MNone;
    /// <summary>
    /// 对话模式
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogMode dialogMode {
      get { return dialogMode_; }
      set {
        dialogMode_ = value;
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 3;
    private long avatarId_;
    /// <summary>
    /// avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_StartDialogReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_StartDialogReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (dialogMode != other.dialogMode) return false;
      if (avatarId != other.avatarId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) hash ^= dialogMode.GetHashCode();
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) dialogMode);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(avatarId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) dialogMode);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(avatarId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) dialogMode);
      }
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_StartDialogReq other) {
      if (other == null) {
        return;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        dialogMode = other.dialogMode;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 24: {
            avatarId = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 24: {
            avatarId = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_StartDialogAck : pb::IMessage<SC_StartDialogAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_StartDialogAck> _parser = new pb::MessageParser<SC_StartDialogAck>(() => new SC_StartDialogAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_StartDialogAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_StartDialogAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_StartDialogAck(SC_StartDialogAck other) : this() {
      code_ = other.code_;
      msgId_ = other.msgId_;
      msgData_ = other.msgData_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_StartDialogAck Clone() {
      return new SC_StartDialogAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.social.PB_ChatErrorCode code_ = global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success;
    /// <summary>
    /// 返回码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.PB_ChatErrorCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "msgId" field.</summary>
    public const int msgIdFieldNumber = 2;
    private int msgId_;
    /// <summary>
    /// 回包消息id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int msgId {
      get { return msgId_; }
      set {
        msgId_ = value;
      }
    }

    /// <summary>Field number for the "msgData" field.</summary>
    public const int msgDataFieldNumber = 3;
    private pb::ByteString msgData_ = pb::ByteString.Empty;
    /// <summary>
    /// 回包数据流
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString msgData {
      get { return msgData_; }
      set {
        msgData_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_StartDialogAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_StartDialogAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (msgId != other.msgId) return false;
      if (msgData != other.msgData) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) hash ^= code.GetHashCode();
      if (msgId != 0) hash ^= msgId.GetHashCode();
      if (msgData.Length != 0) hash ^= msgData.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msgId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(msgId);
      }
      if (msgData.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(msgData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msgId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(msgId);
      }
      if (msgData.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(msgData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (msgId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(msgId);
      }
      if (msgData.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(msgData);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_StartDialogAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        code = other.code;
      }
      if (other.msgId != 0) {
        msgId = other.msgId;
      }
      if (other.msgData.Length != 0) {
        msgData = other.msgData;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.social.PB_ChatErrorCode) input.ReadEnum();
            break;
          }
          case 16: {
            msgId = input.ReadInt32();
            break;
          }
          case 26: {
            msgData = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.social.PB_ChatErrorCode) input.ReadEnum();
            break;
          }
          case 16: {
            msgId = input.ReadInt32();
            break;
          }
          case 26: {
            msgData = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_FinishWorldStoryTaskReq : pb::IMessage<CS_FinishWorldStoryTaskReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_FinishWorldStoryTaskReq> _parser = new pb::MessageParser<CS_FinishWorldStoryTaskReq>(() => new CS_FinishWorldStoryTaskReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_FinishWorldStoryTaskReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_FinishWorldStoryTaskReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_FinishWorldStoryTaskReq(CS_FinishWorldStoryTaskReq other) : this() {
      dialogId_ = other.dialogId_;
      isFinished_ = other.isFinished_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_FinishWorldStoryTaskReq Clone() {
      return new CS_FinishWorldStoryTaskReq(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private long dialogId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "isFinished" field.</summary>
    public const int isFinishedFieldNumber = 2;
    private bool isFinished_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool isFinished {
      get { return isFinished_; }
      set {
        isFinished_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_FinishWorldStoryTaskReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_FinishWorldStoryTaskReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (isFinished != other.isFinished) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (isFinished != false) hash ^= isFinished.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (isFinished != false) {
        output.WriteRawTag(16);
        output.WriteBool(isFinished);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (isFinished != false) {
        output.WriteRawTag(16);
        output.WriteBool(isFinished);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (isFinished != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_FinishWorldStoryTaskReq other) {
      if (other == null) {
        return;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.isFinished != false) {
        isFinished = other.isFinished;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            isFinished = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            isFinished = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_FinishWorldStoryTaskAck : pb::IMessage<SC_FinishWorldStoryTaskAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_FinishWorldStoryTaskAck> _parser = new pb::MessageParser<SC_FinishWorldStoryTaskAck>(() => new SC_FinishWorldStoryTaskAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_FinishWorldStoryTaskAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FinishWorldStoryTaskAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FinishWorldStoryTaskAck(SC_FinishWorldStoryTaskAck other) : this() {
      code_ = other.code_;
      score_ = other.score_;
      isFinished_ = other.isFinished_;
      content_ = other.content_;
      actionList_ = other.actionList_.Clone();
      masterPlayer_ = other.masterPlayer_;
      nextAvatarId_ = other.nextAvatarId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FinishWorldStoryTaskAck Clone() {
      return new SC_FinishWorldStoryTaskAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.social.PB_ChatErrorCode code_ = global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.PB_ChatErrorCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "score" field.</summary>
    public const int scoreFieldNumber = 2;
    private long score_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long score {
      get { return score_; }
      set {
        score_ = value;
      }
    }

    /// <summary>Field number for the "isFinished" field.</summary>
    public const int isFinishedFieldNumber = 3;
    private bool isFinished_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool isFinished {
      get { return isFinished_; }
      set {
        isFinished_ = value;
      }
    }

    /// <summary>Field number for the "content" field.</summary>
    public const int contentFieldNumber = 4;
    private string content_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string content {
      get { return content_; }
      set {
        content_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "actionList" field.</summary>
    public const int actionListFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Msg.basic.PB_AvatarOperation> _repeated_actionList_codec
        = pb::FieldCodec.ForMessage(42, global::Msg.basic.PB_AvatarOperation.Parser);
    private readonly pbc::RepeatedField<global::Msg.basic.PB_AvatarOperation> actionList_ = new pbc::RepeatedField<global::Msg.basic.PB_AvatarOperation>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.basic.PB_AvatarOperation> actionList {
      get { return actionList_; }
    }

    /// <summary>Field number for the "masterPlayer" field.</summary>
    public const int masterPlayerFieldNumber = 6;
    private long masterPlayer_;
    /// <summary>
    ///本次指令由谁发起
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long masterPlayer {
      get { return masterPlayer_; }
      set {
        masterPlayer_ = value;
      }
    }

    /// <summary>Field number for the "nextAvatarId" field.</summary>
    public const int nextAvatarIdFieldNumber = 7;
    private long nextAvatarId_;
    /// <summary>
    /// 下一个任务的avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long nextAvatarId {
      get { return nextAvatarId_; }
      set {
        nextAvatarId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_FinishWorldStoryTaskAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_FinishWorldStoryTaskAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (score != other.score) return false;
      if (isFinished != other.isFinished) return false;
      if (content != other.content) return false;
      if(!actionList_.Equals(other.actionList_)) return false;
      if (masterPlayer != other.masterPlayer) return false;
      if (nextAvatarId != other.nextAvatarId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) hash ^= code.GetHashCode();
      if (score != 0L) hash ^= score.GetHashCode();
      if (isFinished != false) hash ^= isFinished.GetHashCode();
      if (content.Length != 0) hash ^= content.GetHashCode();
      hash ^= actionList_.GetHashCode();
      if (masterPlayer != 0L) hash ^= masterPlayer.GetHashCode();
      if (nextAvatarId != 0L) hash ^= nextAvatarId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (score != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(score);
      }
      if (isFinished != false) {
        output.WriteRawTag(24);
        output.WriteBool(isFinished);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      actionList_.WriteTo(output, _repeated_actionList_codec);
      if (masterPlayer != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(masterPlayer);
      }
      if (nextAvatarId != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(nextAvatarId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (score != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(score);
      }
      if (isFinished != false) {
        output.WriteRawTag(24);
        output.WriteBool(isFinished);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      actionList_.WriteTo(ref output, _repeated_actionList_codec);
      if (masterPlayer != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(masterPlayer);
      }
      if (nextAvatarId != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(nextAvatarId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (score != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(score);
      }
      if (isFinished != false) {
        size += 1 + 1;
      }
      if (content.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(content);
      }
      size += actionList_.CalculateSize(_repeated_actionList_codec);
      if (masterPlayer != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(masterPlayer);
      }
      if (nextAvatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(nextAvatarId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_FinishWorldStoryTaskAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        code = other.code;
      }
      if (other.score != 0L) {
        score = other.score;
      }
      if (other.isFinished != false) {
        isFinished = other.isFinished;
      }
      if (other.content.Length != 0) {
        content = other.content;
      }
      actionList_.Add(other.actionList_);
      if (other.masterPlayer != 0L) {
        masterPlayer = other.masterPlayer;
      }
      if (other.nextAvatarId != 0L) {
        nextAvatarId = other.nextAvatarId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.social.PB_ChatErrorCode) input.ReadEnum();
            break;
          }
          case 16: {
            score = input.ReadInt64();
            break;
          }
          case 24: {
            isFinished = input.ReadBool();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 42: {
            actionList_.AddEntriesFrom(input, _repeated_actionList_codec);
            break;
          }
          case 48: {
            masterPlayer = input.ReadInt64();
            break;
          }
          case 56: {
            nextAvatarId = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.social.PB_ChatErrorCode) input.ReadEnum();
            break;
          }
          case 16: {
            score = input.ReadInt64();
            break;
          }
          case 24: {
            isFinished = input.ReadBool();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 42: {
            actionList_.AddEntriesFrom(ref input, _repeated_actionList_codec);
            break;
          }
          case 48: {
            masterPlayer = input.ReadInt64();
            break;
          }
          case 56: {
            nextAvatarId = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetWorldStoryModeInfoReq : pb::IMessage<CS_GetWorldStoryModeInfoReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetWorldStoryModeInfoReq> _parser = new pb::MessageParser<CS_GetWorldStoryModeInfoReq>(() => new CS_GetWorldStoryModeInfoReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetWorldStoryModeInfoReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetWorldStoryModeInfoReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetWorldStoryModeInfoReq(CS_GetWorldStoryModeInfoReq other) : this() {
      avatarId_ = other.avatarId_;
      storyBackground_ = other.storyBackground_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetWorldStoryModeInfoReq Clone() {
      return new CS_GetWorldStoryModeInfoReq(this);
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 1;
    private long avatarId_;
    /// <summary>
    /// avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "storyBackground" field.</summary>
    public const int storyBackgroundFieldNumber = 2;
    private string storyBackground_ = "";
    /// <summary>
    /// 故事背景
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string storyBackground {
      get { return storyBackground_; }
      set {
        storyBackground_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetWorldStoryModeInfoReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetWorldStoryModeInfoReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (avatarId != other.avatarId) return false;
      if (storyBackground != other.storyBackground) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (storyBackground.Length != 0) hash ^= storyBackground.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (avatarId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatarId);
      }
      if (storyBackground.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(storyBackground);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (avatarId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatarId);
      }
      if (storyBackground.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(storyBackground);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (storyBackground.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(storyBackground);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetWorldStoryModeInfoReq other) {
      if (other == null) {
        return;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.storyBackground.Length != 0) {
        storyBackground = other.storyBackground;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            avatarId = input.ReadInt64();
            break;
          }
          case 18: {
            storyBackground = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            avatarId = input.ReadInt64();
            break;
          }
          case 18: {
            storyBackground = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetWorldStoryModeInfoAck : pb::IMessage<SC_GetWorldStoryModeInfoAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetWorldStoryModeInfoAck> _parser = new pb::MessageParser<SC_GetWorldStoryModeInfoAck>(() => new SC_GetWorldStoryModeInfoAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetWorldStoryModeInfoAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetWorldStoryModeInfoAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetWorldStoryModeInfoAck(SC_GetWorldStoryModeInfoAck other) : this() {
      code_ = other.code_;
      storyBackground_ = other.storyBackground_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetWorldStoryModeInfoAck Clone() {
      return new SC_GetWorldStoryModeInfoAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.social.PB_ChatErrorCode code_ = global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.PB_ChatErrorCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "storyBackground" field.</summary>
    public const int storyBackgroundFieldNumber = 2;
    private string storyBackground_ = "";
    /// <summary>
    /// 故事背景
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string storyBackground {
      get { return storyBackground_; }
      set {
        storyBackground_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetWorldStoryModeInfoAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetWorldStoryModeInfoAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (storyBackground != other.storyBackground) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) hash ^= code.GetHashCode();
      if (storyBackground.Length != 0) hash ^= storyBackground.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (storyBackground.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(storyBackground);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (storyBackground.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(storyBackground);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (storyBackground.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(storyBackground);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetWorldStoryModeInfoAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        code = other.code;
      }
      if (other.storyBackground.Length != 0) {
        storyBackground = other.storyBackground;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.social.PB_ChatErrorCode) input.ReadEnum();
            break;
          }
          case 18: {
            storyBackground = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.social.PB_ChatErrorCode) input.ReadEnum();
            break;
          }
          case 18: {
            storyBackground = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_UserDialogContentNtf : pb::IMessage<SC_UserDialogContentNtf>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_UserDialogContentNtf> _parser = new pb::MessageParser<SC_UserDialogContentNtf>(() => new SC_UserDialogContentNtf());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_UserDialogContentNtf> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserDialogContentNtf() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserDialogContentNtf(SC_UserDialogContentNtf other) : this() {
      dialogId_ = other.dialogId_;
      userId_ = other.userId_;
      bubbleId_ = other.bubbleId_;
      content_ = other.content_;
      hasMore_ = other.hasMore_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserDialogContentNtf Clone() {
      return new SC_UserDialogContentNtf(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private long dialogId_;
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "userId" field.</summary>
    public const int userIdFieldNumber = 2;
    private long userId_;
    /// <summary>
    /// 用户id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long userId {
      get { return userId_; }
      set {
        userId_ = value;
      }
    }

    /// <summary>Field number for the "bubbleId" field.</summary>
    public const int bubbleIdFieldNumber = 3;
    private long bubbleId_;
    /// <summary>
    /// 气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long bubbleId {
      get { return bubbleId_; }
      set {
        bubbleId_ = value;
      }
    }

    /// <summary>Field number for the "content" field.</summary>
    public const int contentFieldNumber = 4;
    private string content_ = "";
    /// <summary>
    /// 对话文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string content {
      get { return content_; }
      set {
        content_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "hasMore" field.</summary>
    public const int hasMoreFieldNumber = 5;
    private bool hasMore_;
    /// <summary>
    /// 当前气泡是否还有更多内容
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool hasMore {
      get { return hasMore_; }
      set {
        hasMore_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_UserDialogContentNtf);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_UserDialogContentNtf other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (userId != other.userId) return false;
      if (bubbleId != other.bubbleId) return false;
      if (content != other.content) return false;
      if (hasMore != other.hasMore) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (userId != 0L) hash ^= userId.GetHashCode();
      if (bubbleId != 0L) hash ^= bubbleId.GetHashCode();
      if (content.Length != 0) hash ^= content.GetHashCode();
      if (hasMore != false) hash ^= hasMore.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (userId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(userId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      if (hasMore != false) {
        output.WriteRawTag(40);
        output.WriteBool(hasMore);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (userId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(userId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      if (hasMore != false) {
        output.WriteRawTag(40);
        output.WriteBool(hasMore);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (userId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(userId);
      }
      if (bubbleId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(bubbleId);
      }
      if (content.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(content);
      }
      if (hasMore != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_UserDialogContentNtf other) {
      if (other == null) {
        return;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.userId != 0L) {
        userId = other.userId;
      }
      if (other.bubbleId != 0L) {
        bubbleId = other.bubbleId;
      }
      if (other.content.Length != 0) {
        content = other.content;
      }
      if (other.hasMore != false) {
        hasMore = other.hasMore;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            userId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 40: {
            hasMore = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            userId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 40: {
            hasMore = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_AvatarDialogContentNtf : pb::IMessage<SC_AvatarDialogContentNtf>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_AvatarDialogContentNtf> _parser = new pb::MessageParser<SC_AvatarDialogContentNtf>(() => new SC_AvatarDialogContentNtf());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_AvatarDialogContentNtf> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarDialogContentNtf() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarDialogContentNtf(SC_AvatarDialogContentNtf other) : this() {
      dialogId_ = other.dialogId_;
      avatarId_ = other.avatarId_;
      bubbleId_ = other.bubbleId_;
      content_ = other.content_;
      hasMore_ = other.hasMore_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarDialogContentNtf Clone() {
      return new SC_AvatarDialogContentNtf(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private long dialogId_;
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 2;
    private long avatarId_;
    /// <summary>
    /// avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "bubbleId" field.</summary>
    public const int bubbleIdFieldNumber = 3;
    private long bubbleId_;
    /// <summary>
    /// 气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long bubbleId {
      get { return bubbleId_; }
      set {
        bubbleId_ = value;
      }
    }

    /// <summary>Field number for the "content" field.</summary>
    public const int contentFieldNumber = 4;
    private string content_ = "";
    /// <summary>
    /// 对话文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string content {
      get { return content_; }
      set {
        content_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "hasMore" field.</summary>
    public const int hasMoreFieldNumber = 5;
    private long hasMore_;
    /// <summary>
    /// 当前气泡是否还有更多内容
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long hasMore {
      get { return hasMore_; }
      set {
        hasMore_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_AvatarDialogContentNtf);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_AvatarDialogContentNtf other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (avatarId != other.avatarId) return false;
      if (bubbleId != other.bubbleId) return false;
      if (content != other.content) return false;
      if (hasMore != other.hasMore) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (bubbleId != 0L) hash ^= bubbleId.GetHashCode();
      if (content.Length != 0) hash ^= content.GetHashCode();
      if (hasMore != 0L) hash ^= hasMore.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      if (hasMore != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(hasMore);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      if (hasMore != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(hasMore);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (bubbleId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(bubbleId);
      }
      if (content.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(content);
      }
      if (hasMore != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(hasMore);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_AvatarDialogContentNtf other) {
      if (other == null) {
        return;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.bubbleId != 0L) {
        bubbleId = other.bubbleId;
      }
      if (other.content.Length != 0) {
        content = other.content;
      }
      if (other.hasMore != 0L) {
        hasMore = other.hasMore;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 40: {
            hasMore = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 40: {
            hasMore = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_AvatarAudioNtf : pb::IMessage<SC_AvatarAudioNtf>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_AvatarAudioNtf> _parser = new pb::MessageParser<SC_AvatarAudioNtf>(() => new SC_AvatarAudioNtf());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_AvatarAudioNtf> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarAudioNtf() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarAudioNtf(SC_AvatarAudioNtf other) : this() {
      dialogId_ = other.dialogId_;
      avatarId_ = other.avatarId_;
      bubbleId_ = other.bubbleId_;
      audio_ = other.audio_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarAudioNtf Clone() {
      return new SC_AvatarAudioNtf(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private long dialogId_;
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 2;
    private long avatarId_;
    /// <summary>
    /// avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "bubbleId" field.</summary>
    public const int bubbleIdFieldNumber = 3;
    private long bubbleId_;
    /// <summary>
    /// 气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long bubbleId {
      get { return bubbleId_; }
      set {
        bubbleId_ = value;
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 4;
    private pb::ByteString audio_ = pb::ByteString.Empty;
    /// <summary>
    /// 对话音频流
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString audio {
      get { return audio_; }
      set {
        audio_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_AvatarAudioNtf);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_AvatarAudioNtf other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (avatarId != other.avatarId) return false;
      if (bubbleId != other.bubbleId) return false;
      if (audio != other.audio) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (bubbleId != 0L) hash ^= bubbleId.GetHashCode();
      if (audio.Length != 0) hash ^= audio.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(34);
        output.WriteBytes(audio);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(34);
        output.WriteBytes(audio);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (bubbleId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(bubbleId);
      }
      if (audio.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(audio);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_AvatarAudioNtf other) {
      if (other == null) {
        return;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.bubbleId != 0L) {
        bubbleId = other.bubbleId;
      }
      if (other.audio.Length != 0) {
        audio = other.audio;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            audio = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            audio = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_AvatarDialogTranslateNtf : pb::IMessage<SC_AvatarDialogTranslateNtf>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_AvatarDialogTranslateNtf> _parser = new pb::MessageParser<SC_AvatarDialogTranslateNtf>(() => new SC_AvatarDialogTranslateNtf());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_AvatarDialogTranslateNtf> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarDialogTranslateNtf() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarDialogTranslateNtf(SC_AvatarDialogTranslateNtf other) : this() {
      dialogId_ = other.dialogId_;
      avatarId_ = other.avatarId_;
      bubbleId_ = other.bubbleId_;
      translate_ = other.translate_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarDialogTranslateNtf Clone() {
      return new SC_AvatarDialogTranslateNtf(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private long dialogId_;
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 2;
    private long avatarId_;
    /// <summary>
    /// avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "bubbleId" field.</summary>
    public const int bubbleIdFieldNumber = 3;
    private long bubbleId_;
    /// <summary>
    /// 气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long bubbleId {
      get { return bubbleId_; }
      set {
        bubbleId_ = value;
      }
    }

    /// <summary>Field number for the "translate" field.</summary>
    public const int translateFieldNumber = 4;
    private string translate_ = "";
    /// <summary>
    /// 对话文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string translate {
      get { return translate_; }
      set {
        translate_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_AvatarDialogTranslateNtf);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_AvatarDialogTranslateNtf other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (avatarId != other.avatarId) return false;
      if (bubbleId != other.bubbleId) return false;
      if (translate != other.translate) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (bubbleId != 0L) hash ^= bubbleId.GetHashCode();
      if (translate.Length != 0) hash ^= translate.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (translate.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(translate);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (translate.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(translate);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (bubbleId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(bubbleId);
      }
      if (translate.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(translate);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_AvatarDialogTranslateNtf other) {
      if (other == null) {
        return;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.bubbleId != 0L) {
        bubbleId = other.bubbleId;
      }
      if (other.translate.Length != 0) {
        translate = other.translate;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            translate = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            translate = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_AvatarAdviceNtf : pb::IMessage<SC_AvatarAdviceNtf>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_AvatarAdviceNtf> _parser = new pb::MessageParser<SC_AvatarAdviceNtf>(() => new SC_AvatarAdviceNtf());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_AvatarAdviceNtf> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarAdviceNtf() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarAdviceNtf(SC_AvatarAdviceNtf other) : this() {
      dialogId_ = other.dialogId_;
      avatarId_ = other.avatarId_;
      bubbleId_ = other.bubbleId_;
      content_ = other.content_;
      translate_ = other.translate_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarAdviceNtf Clone() {
      return new SC_AvatarAdviceNtf(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private long dialogId_;
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 2;
    private long avatarId_;
    /// <summary>
    /// avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "bubbleId" field.</summary>
    public const int bubbleIdFieldNumber = 3;
    private long bubbleId_;
    /// <summary>
    /// 气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long bubbleId {
      get { return bubbleId_; }
      set {
        bubbleId_ = value;
      }
    }

    /// <summary>Field number for the "content" field.</summary>
    public const int contentFieldNumber = 4;
    private string content_ = "";
    /// <summary>
    /// 建议文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string content {
      get { return content_; }
      set {
        content_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "translate" field.</summary>
    public const int translateFieldNumber = 5;
    private string translate_ = "";
    /// <summary>
    /// 建议翻译
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string translate {
      get { return translate_; }
      set {
        translate_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_AvatarAdviceNtf);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_AvatarAdviceNtf other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (avatarId != other.avatarId) return false;
      if (bubbleId != other.bubbleId) return false;
      if (content != other.content) return false;
      if (translate != other.translate) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (bubbleId != 0L) hash ^= bubbleId.GetHashCode();
      if (content.Length != 0) hash ^= content.GetHashCode();
      if (translate.Length != 0) hash ^= translate.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      if (translate.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(translate);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      if (translate.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(translate);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (bubbleId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(bubbleId);
      }
      if (content.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(content);
      }
      if (translate.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(translate);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_AvatarAdviceNtf other) {
      if (other == null) {
        return;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.bubbleId != 0L) {
        bubbleId = other.bubbleId;
      }
      if (other.content.Length != 0) {
        content = other.content;
      }
      if (other.translate.Length != 0) {
        translate = other.translate;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 42: {
            translate = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 42: {
            translate = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_AvatarExampleNtf : pb::IMessage<SC_AvatarExampleNtf>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_AvatarExampleNtf> _parser = new pb::MessageParser<SC_AvatarExampleNtf>(() => new SC_AvatarExampleNtf());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_AvatarExampleNtf> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarExampleNtf() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarExampleNtf(SC_AvatarExampleNtf other) : this() {
      dialogId_ = other.dialogId_;
      avatarId_ = other.avatarId_;
      bubbleId_ = other.bubbleId_;
      content_ = other.content_;
      translate_ = other.translate_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_AvatarExampleNtf Clone() {
      return new SC_AvatarExampleNtf(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private long dialogId_;
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 2;
    private long avatarId_;
    /// <summary>
    /// avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "bubbleId" field.</summary>
    public const int bubbleIdFieldNumber = 3;
    private long bubbleId_;
    /// <summary>
    /// 气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long bubbleId {
      get { return bubbleId_; }
      set {
        bubbleId_ = value;
      }
    }

    /// <summary>Field number for the "content" field.</summary>
    public const int contentFieldNumber = 4;
    private string content_ = "";
    /// <summary>
    /// 示例文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string content {
      get { return content_; }
      set {
        content_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "translate" field.</summary>
    public const int translateFieldNumber = 5;
    private string translate_ = "";
    /// <summary>
    /// 示例翻译
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string translate {
      get { return translate_; }
      set {
        translate_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_AvatarExampleNtf);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_AvatarExampleNtf other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (avatarId != other.avatarId) return false;
      if (bubbleId != other.bubbleId) return false;
      if (content != other.content) return false;
      if (translate != other.translate) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (bubbleId != 0L) hash ^= bubbleId.GetHashCode();
      if (content.Length != 0) hash ^= content.GetHashCode();
      if (translate.Length != 0) hash ^= translate.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      if (translate.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(translate);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (bubbleId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(bubbleId);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      if (translate.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(translate);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (bubbleId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(bubbleId);
      }
      if (content.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(content);
      }
      if (translate.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(translate);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_AvatarExampleNtf other) {
      if (other == null) {
        return;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.bubbleId != 0L) {
        bubbleId = other.bubbleId;
      }
      if (other.content.Length != 0) {
        content = other.content;
      }
      if (other.translate.Length != 0) {
        translate = other.translate;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 42: {
            translate = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            bubbleId = input.ReadInt64();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 42: {
            translate = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_WorldStoryProcessNtf : pb::IMessage<SC_WorldStoryProcessNtf>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_WorldStoryProcessNtf> _parser = new pb::MessageParser<SC_WorldStoryProcessNtf>(() => new SC_WorldStoryProcessNtf());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_WorldStoryProcessNtf> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_WorldStoryProcessNtf() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_WorldStoryProcessNtf(SC_WorldStoryProcessNtf other) : this() {
      dialogId_ = other.dialogId_;
      avatarId_ = other.avatarId_;
      score_ = other.score_;
      isFinished_ = other.isFinished_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_WorldStoryProcessNtf Clone() {
      return new SC_WorldStoryProcessNtf(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private long dialogId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 2;
    private long avatarId_;
    /// <summary>
    /// avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "score" field.</summary>
    public const int scoreFieldNumber = 3;
    private long score_;
    /// <summary>
    /// 分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long score {
      get { return score_; }
      set {
        score_ = value;
      }
    }

    /// <summary>Field number for the "isFinished" field.</summary>
    public const int isFinishedFieldNumber = 4;
    private bool isFinished_;
    /// <summary>
    /// 是否完成
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool isFinished {
      get { return isFinished_; }
      set {
        isFinished_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_WorldStoryProcessNtf);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_WorldStoryProcessNtf other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (avatarId != other.avatarId) return false;
      if (score != other.score) return false;
      if (isFinished != other.isFinished) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (score != 0L) hash ^= score.GetHashCode();
      if (isFinished != false) hash ^= isFinished.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (score != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(score);
      }
      if (isFinished != false) {
        output.WriteRawTag(32);
        output.WriteBool(isFinished);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (score != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(score);
      }
      if (isFinished != false) {
        output.WriteRawTag(32);
        output.WriteBool(isFinished);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (score != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(score);
      }
      if (isFinished != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_WorldStoryProcessNtf other) {
      if (other == null) {
        return;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.score != 0L) {
        score = other.score;
      }
      if (other.isFinished != false) {
        isFinished = other.isFinished;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            score = input.ReadInt64();
            break;
          }
          case 32: {
            isFinished = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            score = input.ReadInt64();
            break;
          }
          case 32: {
            isFinished = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_StartWorldDialogReq : pb::IMessage<CS_StartWorldDialogReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_StartWorldDialogReq> _parser = new pb::MessageParser<CS_StartWorldDialogReq>(() => new CS_StartWorldDialogReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_StartWorldDialogReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_StartWorldDialogReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_StartWorldDialogReq(CS_StartWorldDialogReq other) : this() {
      data_ = other.data_;
      avatarId_ = other.avatarId_;
      userId_ = other.userId_;
      dialogId_ = other.dialogId_;
      round_ = other.round_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_StartWorldDialogReq Clone() {
      return new CS_StartWorldDialogReq(this);
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 1;
    private pb::ByteString data_ = pb::ByteString.Empty;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString data {
      get { return data_; }
      set {
        data_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 2;
    private long avatarId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "userId" field.</summary>
    public const int userIdFieldNumber = 3;
    private long userId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long userId {
      get { return userId_; }
      set {
        userId_ = value;
      }
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 4;
    private long dialogId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "round" field.</summary>
    public const int roundFieldNumber = 5;
    private long round_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long round {
      get { return round_; }
      set {
        round_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_StartWorldDialogReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_StartWorldDialogReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (data != other.data) return false;
      if (avatarId != other.avatarId) return false;
      if (userId != other.userId) return false;
      if (dialogId != other.dialogId) return false;
      if (round != other.round) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (data.Length != 0) hash ^= data.GetHashCode();
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (userId != 0L) hash ^= userId.GetHashCode();
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (round != 0L) hash ^= round.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (data.Length != 0) {
        output.WriteRawTag(10);
        output.WriteBytes(data);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (userId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(userId);
      }
      if (dialogId != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(dialogId);
      }
      if (round != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(round);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (data.Length != 0) {
        output.WriteRawTag(10);
        output.WriteBytes(data);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(avatarId);
      }
      if (userId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(userId);
      }
      if (dialogId != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(dialogId);
      }
      if (round != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(round);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (data.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(data);
      }
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (userId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(userId);
      }
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (round != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(round);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_StartWorldDialogReq other) {
      if (other == null) {
        return;
      }
      if (other.data.Length != 0) {
        data = other.data;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.userId != 0L) {
        userId = other.userId;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.round != 0L) {
        round = other.round;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            data = input.ReadBytes();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            userId = input.ReadInt64();
            break;
          }
          case 32: {
            dialogId = input.ReadInt64();
            break;
          }
          case 40: {
            round = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            data = input.ReadBytes();
            break;
          }
          case 16: {
            avatarId = input.ReadInt64();
            break;
          }
          case 24: {
            userId = input.ReadInt64();
            break;
          }
          case 32: {
            dialogId = input.ReadInt64();
            break;
          }
          case 40: {
            round = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_StartWorldDialogAck : pb::IMessage<SC_StartWorldDialogAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_StartWorldDialogAck> _parser = new pb::MessageParser<SC_StartWorldDialogAck>(() => new SC_StartWorldDialogAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_StartWorldDialogAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_StartWorldDialogAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_StartWorldDialogAck(SC_StartWorldDialogAck other) : this() {
      code_ = other.code_;
      msgId_ = other.msgId_;
      msgData_ = other.msgData_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_StartWorldDialogAck Clone() {
      return new SC_StartWorldDialogAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.social.PB_ChatErrorCode code_ = global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success;
    /// <summary>
    /// 返回码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.PB_ChatErrorCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "msgId" field.</summary>
    public const int msgIdFieldNumber = 2;
    private int msgId_;
    /// <summary>
    /// 回包消息id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int msgId {
      get { return msgId_; }
      set {
        msgId_ = value;
      }
    }

    /// <summary>Field number for the "msgData" field.</summary>
    public const int msgDataFieldNumber = 3;
    private pb::ByteString msgData_ = pb::ByteString.Empty;
    /// <summary>
    /// 回包数据流
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString msgData {
      get { return msgData_; }
      set {
        msgData_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_StartWorldDialogAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_StartWorldDialogAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (msgId != other.msgId) return false;
      if (msgData != other.msgData) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) hash ^= code.GetHashCode();
      if (msgId != 0) hash ^= msgId.GetHashCode();
      if (msgData.Length != 0) hash ^= msgData.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msgId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(msgId);
      }
      if (msgData.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(msgData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msgId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(msgId);
      }
      if (msgData.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(msgData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (msgId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(msgId);
      }
      if (msgData.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(msgData);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_StartWorldDialogAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.social.PB_ChatErrorCode.ChatErrorCode_Success) {
        code = other.code;
      }
      if (other.msgId != 0) {
        msgId = other.msgId;
      }
      if (other.msgData.Length != 0) {
        msgData = other.msgData;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.social.PB_ChatErrorCode) input.ReadEnum();
            break;
          }
          case 16: {
            msgId = input.ReadInt32();
            break;
          }
          case 26: {
            msgData = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.social.PB_ChatErrorCode) input.ReadEnum();
            break;
          }
          case 16: {
            msgId = input.ReadInt32();
            break;
          }
          case 26: {
            msgData = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 对话设置 start 
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_DialogSettingReq : pb::IMessage<CS_DialogSettingReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_DialogSettingReq> _parser = new pb::MessageParser<CS_DialogSettingReq>(() => new CS_DialogSettingReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_DialogSettingReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.social.WorldChatReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_DialogSettingReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_DialogSettingReq(CS_DialogSettingReq other) : this() {
      dialogId_ = other.dialogId_;
      dialogMode_ = other.dialogMode_;
      avatarId_ = other.avatarId_;
      userId_ = other.userId_;
      taskId_ = other.taskId_;
      roundId_ = other.roundId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_DialogSettingReq Clone() {
      return new CS_DialogSettingReq(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private string dialogId_ = "";
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "dialogMode" field.</summary>
    public const int dialogModeFieldNumber = 2;
    private global::Msg.basic.PB_DialogMode dialogMode_ = global::Msg.basic.PB_DialogMode.MNone;
    /// <summary>
    /// 对话模式
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogMode dialogMode {
      get { return dialogMode_; }
      set {
        dialogMode_ = value;
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 3;
    private string avatarId_ = "";
    /// <summary>
    /// 对话avatar标识id，默认与谁对话”
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "userId" field.</summary>
    public const int userIdFieldNumber = 4;
    private string userId_ = "";
    /// <summary>
    /// 用户id（创建对话不在chat_agent完成必传）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string userId {
      get { return userId_; }
      set {
        userId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "taskId" field.</summary>
    public const int taskIdFieldNumber = 5;
    private string taskId_ = "";
    /// <summary>
    /// 任务id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string taskId {
      get { return taskId_; }
      set {
        taskId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "roundId" field.</summary>
    public const int roundIdFieldNumber = 6;
    private ulong roundId_;
    /// <summary>
    /// 重入轮次
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong roundId {
      get { return roundId_; }
      set {
        roundId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_DialogSettingReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_DialogSettingReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (dialogMode != other.dialogMode) return false;
      if (avatarId != other.avatarId) return false;
      if (userId != other.userId) return false;
      if (taskId != other.taskId) return false;
      if (roundId != other.roundId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId.Length != 0) hash ^= dialogId.GetHashCode();
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) hash ^= dialogMode.GetHashCode();
      if (avatarId.Length != 0) hash ^= avatarId.GetHashCode();
      if (userId.Length != 0) hash ^= userId.GetHashCode();
      if (taskId.Length != 0) hash ^= taskId.GetHashCode();
      if (roundId != 0UL) hash ^= roundId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) dialogMode);
      }
      if (avatarId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(avatarId);
      }
      if (userId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(userId);
      }
      if (taskId.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(taskId);
      }
      if (roundId != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(roundId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) dialogMode);
      }
      if (avatarId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(avatarId);
      }
      if (userId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(userId);
      }
      if (taskId.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(taskId);
      }
      if (roundId != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(roundId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(dialogId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) dialogMode);
      }
      if (avatarId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(avatarId);
      }
      if (userId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(userId);
      }
      if (taskId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(taskId);
      }
      if (roundId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(roundId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_DialogSettingReq other) {
      if (other == null) {
        return;
      }
      if (other.dialogId.Length != 0) {
        dialogId = other.dialogId;
      }
      if (other.dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        dialogMode = other.dialogMode;
      }
      if (other.avatarId.Length != 0) {
        avatarId = other.avatarId;
      }
      if (other.userId.Length != 0) {
        userId = other.userId;
      }
      if (other.taskId.Length != 0) {
        taskId = other.taskId;
      }
      if (other.roundId != 0UL) {
        roundId = other.roundId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 16: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 26: {
            avatarId = input.ReadString();
            break;
          }
          case 34: {
            userId = input.ReadString();
            break;
          }
          case 42: {
            taskId = input.ReadString();
            break;
          }
          case 48: {
            roundId = input.ReadUInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 16: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 26: {
            avatarId = input.ReadString();
            break;
          }
          case 34: {
            userId = input.ReadString();
            break;
          }
          case 42: {
            taskId = input.ReadString();
            break;
          }
          case 48: {
            roundId = input.ReadUInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

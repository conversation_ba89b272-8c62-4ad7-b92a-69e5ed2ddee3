// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/tts/service.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.tts {

  /// <summary>Holder for reflection information generated from protobuf/tts/service.proto</summary>
  public static partial class ServiceReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/tts/service.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ServiceReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Chpwcm90b2J1Zi90dHMvc2VydmljZS5wcm90bxoWcHJvdG9idWYvdHRzL3R0",
            "cy5wcm90bzKeAQoKVHRzU2VydmljZRI5CgtHZXRUVFNBdWRpbxISLkNTX0dl",
            "dFRUU0F1ZGlvUmVxGhIuU0NfR2V0VFRTQXVkaW9BY2siADABElUKFUdldFRU",
            "U0F1ZGlvVHJhbnNjcmlwdBIcLkNTX0dldFRUU0F1ZGlvVHJhbnNjcmlwdFJl",
            "cRocLlNDX0dldFRUU0F1ZGlvVHJhbnNjcmlwdEFjayIAMlYKDVR0c1dlYlNl",
            "cnZpY2USRQoPR2V0VFRTQXVkaW9GdWxsEhcuV2ViX0dldFRUU0F1ZGlvRnVs",
            "bFJlcRoXLldlYl9HZXRUVFNBdWRpb0Z1bGxBY2siADLuBAoPVHRzSW5uZXJT",
            "ZXJ2aWNlEk4KEk9mZmxpbmVHZXRUVFNBdWRpbxIZLlNTX09mZmxpbmVHZXRU",
            "VFNBdWRpb1JlcRoZLlNTX09mZmxpbmVHZXRUVFNBdWRpb0FjayIAMAESNwoL",
            "VGV4dFRvQXVkaW8SEi5TU19UZXh0VG9BdWRpb1JlcRoSLlNTX1RleHRUb0F1",
            "ZGlvQWNrIgASSQoRT25saW5lVGV4dFRvQXVkaW8SGC5TU19PbmxpbmVUZXh0",
            "VG9BdWRpb1JlcRoYLlNTX09ubGluZVRleHRUb0F1ZGlvQWNrIgASSQoRVGV4",
            "dFRvQXVkaW9JbXBvcnQSGC5TU19UZXh0VG9BdWRpb0ltcG9ydFJlcRoYLlNT",
            "X1RleHRUb0F1ZGlvSW1wb3J0QWNrIgASSQoRQWRkQmFzZVZvaWNlTW9kZWwS",
            "GC5TU19BZGRCYXNlVm9pY2VNb2RlbFJlcRoYLlNTX0FkZEJhc2VWb2ljZU1v",
            "ZGVsQWNrIgASTAoSR2V0QmFzZVZvaWNlTW9kZWxzEhkuU1NfR2V0QmFzZVZv",
            "aWNlTW9kZWxzUmVxGhkuU1NfR2V0QmFzZVZvaWNlTW9kZWxzQWNrIgASTwoT",
            "QWRkQ3VzdG9tVm9pY2VNb2RlbBIaLlNTX0FkZEN1c3RvbVZvaWNlTW9kZWxS",
            "ZXEaGi5TU19BZGRDdXN0b21Wb2ljZU1vZGVsQWNrIgASUgoUR2V0Q3VzdG9t",
            "Vm9pY2VNb2RlbHMSGy5TU19HZXRDdXN0b21Wb2ljZU1vZGVsc1JlcRobLlNT",
            "X0dldEN1c3RvbVZvaWNlTW9kZWxzQWNrIgBCIloWdmZfcHJvdG9idWYvc2Vy",
            "dmVyL3R0c6oCB01zZy50dHNiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.tts.TtsReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, null));
    }
    #endregion

  }
}

#endregion Designer generated code

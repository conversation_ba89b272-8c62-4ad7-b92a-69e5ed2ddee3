/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/22 21:20:57 星期三
# 功能：Nothing
****************************************************
*/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.NetworkInformation;
using Google.Protobuf.Collections;
using CommonUI;
using Cysharp.Threading.Tasks;
using Modules.DataDot;
using Msg.center;
using Msg.dialog_task;
using Msg.economic;
using Msg.incentive;
using Newtonsoft.Json;
using ScriptsHot.Game.UGUI.iOSWidget;
using ScriptsHot.Game.UGUI.iOSWidget.Objs;
using ScriptsN.Framework.sdk;
using TalkitWidgetIOS;
using TalkitWidgetIOS.Objs;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Sign
{
    public class SignController: BaseController
    {
        public SignController() : base(ModelConsts.Sign)
        {
            
        }
        private SignModel _signModel => GetModel<SignModel>(ModelConsts.Sign);
        public SignModel SignModel => _signModel;
        
        private bool _willOpenSignUI = false;

        private SignTabTypeEnum _signTabType;
        
        private string _timerKey = null;
        
        public override void OnInit()
        {
            this.RegisterModel(new SignModel());
            // 获取用户打卡信息
            MsgManager.instance.RegisterCallBack<SC_GetUserCheckinPortalDataAck>(OnGetUserCheckinDataAck);
            NetMsgProxy.instance.RegisterCacheMsg<CS_GetUserCheckinPortalDataReq, SC_GetUserCheckinPortalDataAck>();;
            
            // 领取每日奖励
            MsgManager.instance.RegisterCallBack<SC_DrawRewardAck>(OnDrawRewardAck);
        }
        
        public override void OnUIInit()
        {
            this.RegisterUI(new SignUI(UIConsts.SignUI));
            this.RegisterUI(new SignPersistUI(UIConsts.SignPersistUI));
            this.RegisterUI(new SignStreakUI(UIConsts.SignStreakUI));
            this.RegisterUI(new SignOnboardRewardUI(UIConsts.SignOnboardRewardUI));
            this.RegisterUI(new SignGoalRewardUI(UIConsts.SignGoalRewardUI));
            this.RegisterUI(new SignDailyRewardUI(UIConsts.SignDailyRewardUI));
            this.RegisterUI(new SignRepairUI(UIConsts.SignRepairUI));
            this.RegisterUI(new SignHelpPanelUI(UIConsts.SignHelpUI));
            this.RegisterUI(new FriendStreakAddUI(UIConsts.FriendStreakAddUI));
            this.RegisterUI(new FriendStreakInvitedUI(UIConsts.FriendStreakInvitedUI));
            this.RegisterUI(new FriendStreakOthersBreakUI(UIConsts.FriendStreakOthersBreakUI));
            this.RegisterUI(new FriendStreakSelfBreakUI(UIConsts.FriendStreakSelfBreakUI));
            //test
            // _signModel.SetSignInfoTmp();
        }

        public override void OnEnterGame()
        {
            base.OnEnterGame();
            GetSignDataForIOSWidget();
            ReqFriendStreakInfo();
        }

        private void GetSignDataForIOSWidget()
        {
            WidgetUtils.UpdateAndRefreshWidget();
        }
        
        public async void ReqFriendStreakInfo(Action<PB_FriendStreakPortalData> callback = null)
        {
            // 250506 被老板吐槽切换不丝滑，取消loading
            // GetUI(UIConsts.CommBusy).Show();
            // 获取好友签到数据
            var resp = await MsgManager.instance.SendAsyncMsg<SC_GetFriendStreakPortalAck>(new CS_GetFriendStreakPortalReq());
            // GetUI(UIConsts.CommBusy).Hide();
            if (resp == null)
                return;
            if (resp.code == 0)
            {
                _signModel.SetFriendStreakData(resp);
                callback?.Invoke(resp.data);
            }
        }
        
        public async void ReqFriendStreakRecommendListInfo(Action<PB_FriendStreakRecommendData> callback = null)
        {
            GetUI(UIConsts.CommBusy).Show();
            var resp = await MsgManager.instance.SendAsyncMsg<SC_GetFriendStreakRecommendListAck>(new CS_GetFriendStreakRecommendListReq());
            GetUI(UIConsts.CommBusy).Hide();
            if (resp == null)
                return;
            if (resp.code == 0)
            {
                callback?.Invoke(resp.data);
            }
        }
        
        public void EnterSign(SignTabTypeEnum tabType = SignTabTypeEnum.SignIn)
        {
            _willOpenSignUI = true;
            _signTabType = tabType;
            var dot = new ClickStreakEntrance();
            DataDotMgr.Collect(dot);
            GetUI(UIConsts.CommBusy).LazyShow();
            _timerKey = TimerManager.instance.RegisterTimer(a =>
            {
                GetUI(UIConsts.CommBusy).Hide();
                GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("webview_notice_timeout");
            }, 5000);
            NetMsgProxy.instance.SendMsg(new CS_GetUserCheckinPortalDataReq()
            {
            });
            
            // _willOpenSignUI = true;
            // var resp = await NetMsgProxy.instance.SendAsyncMsg<SC_GetUserCheckinPortalDataAck>(new CS_GetUserCheckinPortalDataReq());
            // OnGetUserCheckinDataAck(resp);
            
            //test
            // GetUI<SignUI>(UIConsts.SignUI).Show();
        }

        private void OnGetUserCheckinDataAck(SC_GetUserCheckinPortalDataAck msg)
        {
            if (msg.code == 0)
            {
                // 创建app store评分触发器
                if (!_signModel.signInfo.finish_checkin && msg.data.finish_checkin && _signModel.isSignInfoAssigned)
                {
                    GetUI<SignUI>(UIConsts.SignUI).willShowAppStoreReview = true;
                    GetUI<SignUI>(UIConsts.SignUI).willShowOpenNotifacationSetting = true;
                    
                    //新埋点：当日成功打卡
                    var dot = new SwitchStreakSuccessfully();
                    DataDotMgr.Collect(dot);
                }

                _signModel.SetSignInfo(msg.data);
                //test
                //_signModel.SetSignInfoTmp();
                
                if (_willOpenSignUI)
                {
                    UnRegisterTimer();
                    GetUI(UIConsts.CommBusy).Hide();
                    GetUI<SignUI>(UIConsts.SignUI).Show(_signTabType);
                    _willOpenSignUI = false;
                }

                if (GetUI<MainHeaderUI>(UIConsts.MainHeader).isShow)
                {
                    GetUI<MainHeaderUI>(UIConsts.MainHeader).Refresh();
                }
            }
        }
        
        private async void OnDrawRewardAck(SC_DrawRewardAck msg)
        {
            if (msg.code == 0)
            {
                if (GetUI<SignUI>(UIConsts.SignUI).isShow)
                {
                    // _signModel.SetSignInfo(msg.data);
                    var resp = await NetMsgProxy.instance.SendAsyncMsg<SC_GetUserCheckinPortalDataAck>(new CS_GetUserCheckinPortalDataReq(), null, true);
                    if (resp == null)
                    {
                        GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("webview_notice_timeout");
                        return;
                    }
                    
                    // 创建app store评分触发器
                    if (!_signModel.signInfo.finish_checkin && resp.data.finish_checkin)
                    {
                        GetUI<SignUI>(UIConsts.SignUI).willShowAppStoreReview = true;
                    }
                    _signModel.SetSignInfo(resp.data);
                    GetUI<SignUI>(UIConsts.SignUI).Refresh(false);

                    GetUI<CommonGetDiamondUI>(UIConsts.CommonGetDiamond).Open(CommonGetDiamondType.More, (int)_signModel.GetRewardCnt(msg.data.biz_code), null);
                    GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReq(GameEventName.GameEnter);
                    GetUI<MainHeaderUI>(UIConsts.MainHeader).Refresh();
                }
            }
        }

        private void UnRegisterTimer()
        {
            if (!string.IsNullOrEmpty(_timerKey))
            {
                TimerManager.instance.UnRegisterTimer(_timerKey);
                _timerKey = string.Empty;
            }
        }

        public override void OnApplicationPause(bool pause)
        {
            if (pause)
            {
                WidgetUtils.RefreshWidget();                
            }
        }
        
        public async UniTask TryRefreshSignAndStreak() 
        {
            try
            {
                var resp = await MsgManager.instance.SendAsyncMsg<SC_GetCheckinSummaryAck>(new CS_GetCheckinSummaryReq());
                VFDebug.Log("HSW CS_GetCheckinSummaryReq");
                if (resp != null)
                {
                    _signModel.SetSignSummary(resp.data);
                }
            }
            catch
            {

            }
        }
    }
}
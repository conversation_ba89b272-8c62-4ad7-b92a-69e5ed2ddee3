//#define SEED_ENABLED

using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace AnimationSystem
{
    public class DialogCharacterSpeakingState : AnimState
    {
        //Runtime
        private List<SingleSentenceInfo> sentenceInfos;
        private List<AnimGroupPlayData> animGroupPlayDatas;
        private float currentTime = 0f;
        private int currentSentenceIndex = 0;
        private float nextPlayTime = 0f;
        private bool hasDialogueData = false;
        private bool isPlaying = false;
        
        //兜底逻辑
        private AnimGroup overrideDefault = null;

        #region random

        private System.Random _customRandom;
       
        // 自定义随机数生成器
        private System.Random customRandom
        {
            get
            {
                if (_customRandom == null)
                {
                    _customRandom = new System.Random();
                }
                return _customRandom;
            }
        }
        
        /// <summary>
        /// 设置随机数种子，用于可重现的随机结果
        /// </summary>
        /// <param name="seed">随机数种子，-1表示使用系统时间</param>
        private void SetRandomSeed(int seed)
        {
            _customRandom = new System.Random(seed);
        }
        
        /// <summary>
        /// 自定义的Range函数，类似Unity的Random.Range但使用独立的随机数生成器
        /// </summary>
        /// <param name="min">最小值（包含）</param>
        /// <param name="max">最大值（不包含）</param>
        /// <returns>范围内的随机整数</returns>
        private int CustomRandomRange(int min, int max)
        {
            
            return customRandom.Next(min, max);
        }
        
        /// <summary>
        /// 自定义的Range函数，类似Unity的Random.Range但使用独立的随机数生成器
        /// </summary>
        /// <param name="min">最小值（包含）</param>
        /// <param name="max">最大值（包含）</param>
        /// <returns>范围内的随机浮点数</returns>
        private float CustomRandomRange(float min, float max)
        {
            return (float)(customRandom.NextDouble() * (max - min) + min);
        }

        #endregion
        
        //未来通过读表的方式来进行Initialize
        private void Initialize()
        {
            // 初始化自定义随机数生成器
            if (customRandom == null)
            {
                SetRandomSeed(-1); // 使用默认种子（系统时间）
            }
            
            //未来通过读表来初始化预定义动作组以及关系。现在初始化为demo。
            // Dictionary<SentenceSemanticType, List<AnimGroup>> animGroups = new Dictionary<SentenceSemanticType, List<AnimGroup>>();
            // List<AnimGroup> happyList = new List<AnimGroup>();
            // var idleDemoAnimGroup = new AnimGroup();
            // idleDemoAnimGroup.AddAnimInfo(
            //     new AnimInfo(Resources.Load<AnimationClip>("applaud1"),
            //         new Vector2(0.25f, 0f),
            //         true,
            //         false,
            //         Vector2.zero,
            //         true,
            //         new Vector2(0.8f, 1.2f),
            //         false,
            //         0.0f));
            // idleDemoAnimGroup.AddAnimInfo(
            //     new AnimInfo(Resources.Load<AnimationClip>("applaud2"),
            //         new Vector2(0.5f, 0.5f),
            //         true,
            //         true,
            //         new Vector2(1.0f,4.0f),
            //         true,
            //         new Vector2(0.8f, 1.2f),
            //         false,
            //         0.2f));
            // idleDemoAnimGroup.AddAnimInfo(
            //     new AnimInfo(Resources.Load<AnimationClip>("applaud3"),
            //         new Vector2(0.25f, 0f),
            //         true,
            //         false,
            //         Vector2.zero,
            //         true,
            //         new Vector2(0.8f, 1.2f),
            //         false,
            //         0.0f));
            // idleDemoAnimGroup.Init();
            // happyList.Add(idleDemoAnimGroup);
            // animGroups.Add(SentenceSemanticType.Happy, happyList);
            //未来在此读表来初始化动作组。现在硬编码配置。
            // this.defaultAnimGroup = new AnimGroup();
            // this.defaultAnimGroup.AddAnimInfo(new AnimInfo(Resources.Load<AnimationClip>("Girl_Idle"),
            //     new Vector2(0.5f,0.5f),
            //     true,
            //     true,
            //     new Vector2(1.0f,999.0f),
            //     false,
            //     Vector2.zero,
            //     false,0.0f));
            // this.defaultAnimGroup.bodyGroupID = "idle";
            // this.defaultAnimGroup.Init();
            // semanticAnimGroups = animGroups;

            //后续将使用talkingIdle.
            var talkingGroupList = Util.LoadAnimGenericTags("talkingIdle");
            if (talkingGroupList != null && talkingGroupList.Count > 0)
            {
                var talkingIdle = Util.LoadAnimGroup(talkingGroupList[0],manager.animTypeId);
                if(talkingIdle != null)this.defaultAnimGroup = talkingIdle;
            }
            
            //兜底逻辑：说完话自己先回idle,不等外部调用.
            var defaultIdle = Util.LoadAnimGenericTags("defaultIdle");
            if (defaultIdle != null && defaultIdle.Count > 0)
            {
                var talkingIdle = Util.LoadAnimGroup(defaultIdle[0],manager.animTypeId);
                if(talkingIdle != null)this.overrideDefault = talkingIdle;
            }
            
            //Deprecated:说希望说话状态是眉飞色舞状态。
            // this.manager.SetBrowControlMode(EmotionSB.BrowControlMode.Curve);
            // this.manager.SetBrowCurve(manager.avatarAnimationDataObject.browCurve,5.0f,true);
            
            //4.24.2025 说希望说话状态也是随机状态
            this.manager.SetBrowControlMode(EmotionSB.BrowControlMode.Random);
            this.manager.SetBrowData(EmotionSB.BrowRandomModePrefab.talkingState);
        }

        //对外的 传入一段对话数据,匹配语义的东西。
        //外部不调用这个之前,默认idle。
        //调用这个之后,开始执行对话逻辑。
        //如果在上一次对话没完成的时候调用这个,则直接开始这个逻辑。
        public void ProcessDialogue(List<SingleSentenceInfo> sentences)
        {
            // 如果当前正在播放对话动画，打断当前的播放
            if (isPlaying)
            {
                VFDebug.Log("打断当前对话动画，播放新的对话");
            }
            
            // 重置状态
            ResetState();
            
            #if SEED_ENABLED
            this.SetRandomSeed(this.manager.animTypeId * 1024 + 3000);
            #endif
            
            if (sentences != null && sentences.Count > 0)
            {
                sentenceInfos = sentences;
                GenerateAnimPlayData();
                hasDialogueData = true;
                isPlaying = true;
                // 设置第一段动画的延迟开始时间
                nextPlayTime = sentences[0].willStartAt;
            }
            else
            {
                VFDebug.LogWarning("传入的对话数据为空，将播放默认idle动画");
                hasDialogueData = false;
                PlayDefaultAnimation();
            }
        }

        // 重置状态方法
        private void ResetState()
        {
            currentTime = 0f;
            currentSentenceIndex = 0;
            nextPlayTime = 0f;
            isPlaying = false;
        }

        private void GenerateAnimPlayData()
        {
            if (sentenceInfos == null || sentenceInfos.Count == 0)
            {
                VFDebug.LogWarning("没有对话数据，无法生成动画播放数据");
                animGroupPlayDatas = new List<AnimGroupPlayData>();
                return;
            }
            
            animGroupPlayDatas = new List<AnimGroupPlayData>();
            float totalTime = 0f;

            for(int index = 0;index < sentenceInfos.Count;index++)
            {
                var sentence = sentenceInfos[index];
                AnimGroupPlayData playData = null;
                
                VFDebug.LogWarning($"<color=red>对于语义类型 {sentence.willStartAt}</color>");

                // 尝试获取对应语义类型的动作组列表
                var matchedKeys = SemanticMapping.Instance.MatchAllGroup(sentence.semanticType, sentence.semanticSideType);
                
                // 加载所有匹配的动作组
                var availableGroups = new List<AnimGroup>();
                foreach (var key in matchedKeys)
                {
                    var group = Util.LoadAnimGroup(key,manager.animTypeId);
                    if (group != null)
                    {
                        availableGroups.Add(group);
                    }
                }
                
                // 根据时长筛选合适的动作组
                var suitableGroups = availableGroups.Where(group => 
                {
                    // 确保动作组已经初始化
                    if (group.Duration == Vector2.zero)
                    {
                        group.Init();
                    }
                    
                    // 计算实际需要播放的动画时长
                    float actualPlayDuration = sentence.duration - sentence.willStartAt;

                    // 检查时长是否在可接受范围内
                    //     VFDebug.Log($"check group:{group.bodyGroupID} min<?<max::{group.Duration.x} <{actualPlayDuration}< {group.Duration.y}");
                    return actualPlayDuration >= group.Duration.x && actualPlayDuration <= group.Duration.y;
                }).ToList();
                
                if (suitableGroups.Count == 0)
                {
                    // VFDebug.LogError(
                    //     "在" + sentence.semanticType + "|" + sentence.semanticSideType + "和原始时长为" + sentence.duration + 
                    //     "的句子,没有找到与之匹配的动作!(搜寻时长 " + (sentence.duration - sentence.willStartAt) + ")");
                    //如果没有匹配的,那么移除掉[最大时长的判断条件] 再次匹配一遍。如果还是没有,那么说明就没办法播放;如果有,那么可以兜底。
                    suitableGroups = availableGroups.Where(group => 
                    {
                        // 确保动作组已经初始化
                        if (group.Duration == Vector2.zero)
                        {
                            group.Init();
                        }
                    
                        // 计算实际需要播放的动画时长
                        float actualPlayDuration = sentence.duration - sentence.willStartAt;
                    
                        // 检查时长是否在可接受范围内 *MagicNumber!*
                        return actualPlayDuration >= group.Duration.x;
                    }).ToList();
                }

                if (suitableGroups.Count > 0)
                {
                    // 使用权重法选择动作组
                    List<int> weights = new List<int>();
                    float actualPlayDuration = sentence.duration - sentence.willStartAt;
                    int totalWeight = 0;
                    
                    // 计算每个动作组的权重
                    foreach (var group in suitableGroups)
                    {
                        int weight = 100; // 基础权重为100
                        
                        // 如果动作时长不在最佳范围内，调整权重
                        if (actualPlayDuration > group.Duration.y)
                        {
                            // 权重 = 100 * (动作组最长时间/期望播放的时间)^4，最低为1
                            float ratio = group.Duration.y / actualPlayDuration;
                            weight = Mathf.Max(1, (int)(100 * Mathf.Pow(ratio, 4)));
                        }
                        
                        weights.Add(weight);
                        totalWeight += weight;
                    }
                    
                    // 根据权重随机选择一个动作组
                    int randomValue = CustomRandomRange(0, totalWeight);
                    int cumulativeWeight = 0;
                    AnimGroup selectedGroup = suitableGroups[0];
                    
                    for (int i = 0; i < suitableGroups.Count; i++)
                    {
                        cumulativeWeight += weights[i];
                        if (randomValue < cumulativeWeight)
                        {
                            selectedGroup = suitableGroups[i];
                         //   VFDebug.Log($"选择动作组 {selectedGroup.bodyGroupID}，权重: {weights[i]}/{totalWeight}，随机值: {randomValue}");
                            break;
                        }
                    }
                    
                    // 所有对话默认延迟0.3f结束 *MagicNumber!*
                    playData = selectedGroup.GetAnimStrategy(actualPlayDuration + ((index < sentenceInfos.Count-1) ? Mathf.Min(sentenceInfos[index+1].willStartAt,0.5f) : 0.5f));
                    VFDebug.LogWarning($"<color=red>对于语义类型 {sentence.semanticType} + {sentence.semanticSideType} 和时长 {sentence.duration - sentence.willStartAt} 秒的句子使用 {selectedGroup.bodyGroupID}，权重选择</color>");
                }

                // 如果没有找到合适的动画组或生成策略失败，使用默认动画组
                if (playData == null && defaultAnimGroup != null)
                {
                    VFDebug.LogWarning($"<color=red>对于语义类型 {sentence.semanticType} + {sentence.semanticSideType} 和时长 {sentence.duration - sentence.willStartAt} 秒的句子使用默认动画组</color>");
                    float actualPlayDuration = sentence.duration - sentence.willStartAt;
                    playData = defaultAnimGroup.GetAnimStrategy(actualPlayDuration);
                    
                    // 如果默认动画组也无法处理这个时长，强制使用默认动画组的最小时长
                    if (playData == null)
                    {
                        VFDebug.LogWarning($"默认动画组无法处理时长 {actualPlayDuration} 秒，将使用其最小时长");
                        playData = defaultAnimGroup.GetAnimStrategy(defaultAnimGroup.Duration.x);
                    }
                }

                if (playData != null)
                {
                    animGroupPlayDatas.Add(playData);
                    totalTime += sentence.duration;
                }
                else
                {
                    VFDebug.LogError($"严重错误：无法为句子生成动画数据，且默认动画组无效");
                }
            }

            if (animGroupPlayDatas.Count != sentenceInfos.Count)
            {
                VFDebug.LogError($"生成的动画数据数量 ({animGroupPlayDatas.Count}) 与句子数量 ({sentenceInfos.Count}) 不匹配");
            }
        }

        public override void OnEnter()
        {
            VFDebug.Log("DialogCharacterSpeakingState: 进入角色说话状态");
            
            // 重置状态
            ResetState();
            hasDialogueData = false;
            
            // 执行初始化方法
            Initialize();
            
            // 默认播放idle动画
            PlayDefaultAnimation();
            
            manager.SetHeadCustomRotation(Vector3.zero,0.4f);
            
            manager.SetMouthCurve(manager.avatarAnimationDataObject.mouthCurveOnTalking,manager.avatarAnimationDataObject.mouthCurveTalkingLength);
        }

       // public int tick;

        public override void OnUpdate()
        {
            // tick++;
            // if (tick % 6 == 0)
            // {
            //     manager.PlayAnimGroupData(this.overrideDefault.GetAnimStrategy(3f),false,1f);
            // }
            
            // 如果没有对话数据，保持播放idle动画
            if (!hasDialogueData || animGroupPlayDatas == null || animGroupPlayDatas.Count == 0)
            {
                return;
            }
            
            // 更新计时器
            currentTime += Time.deltaTime;

            // 检查是否需要播放下一个动画组
            if (currentTime >= nextPlayTime && currentSentenceIndex < animGroupPlayDatas.Count && isPlaying)
            {
                var currentSentence = sentenceInfos[currentSentenceIndex];
                float actualPlayDuration = currentSentence.duration - currentSentence.willStartAt;
                
                // VFDebug.Log($"<color=red>播放第 {currentSentenceIndex + 1} 个句子的动画</color>\n" +
                //           $"bodyAction：{ animGroupPlayDatas[currentSentenceIndex].ToString()}\n"+
                //           $"开始时间：{currentTime}，" +
                //           $"实际播放时长：{actualPlayDuration}秒，" +
                //           $"延迟开始：{currentSentence.willStartAt}秒");

                manager.PlayAnimGroupData(animGroupPlayDatas[currentSentenceIndex]);
                
                //
                
                // 基于currentIndex
                // if (sentenceInfos[currentSentenceIndex].semanticSideType == SentenceSemanticSideType.Strong)
                // {
                //     manager.SetBrowModeWeight(EmotionSB.BrowControlMode.Curve,1.0f,0.25f);
                // }
                // else
                // {
                //     manager.SetBrowModeWeight(EmotionSB.BrowControlMode.Curve,0.5f,0.25f);
                // }
                
                // 更新下一次播放时间
                // 1. 先加上当前句子的完整时长
                nextPlayTime += currentSentence.duration;
                // 2. 如果有下一句，加上下一句的willStartAt
                if (currentSentenceIndex + 1 < sentenceInfos.Count)
                {
                    nextPlayTime += sentenceInfos[currentSentenceIndex + 1].willStartAt;
                }
                // 3. 减去当前句子的willStartAt（因为已经包含在duration中了）
                nextPlayTime -= currentSentence.willStartAt;
                
                currentSentenceIndex++;
            }

            // 检查是否完成所有句子的播放
            if (currentTime >= nextPlayTime && currentSentenceIndex >= sentenceInfos.Count && isPlaying)
            {
                VFDebug.Log("所有对话句子的动画播放完毕，回到idle状态");
                // 对话播放完毕，回到idle状态
                isPlaying = false;
                hasDialogueData = false;
                PlayDefaultAnimation();
            }
        }

        public override void OnExit()
        {
            VFDebug.Log("DialogCharacterSpeakingState: 退出角色说话状态");
            
            // 关闭眉毛Curve
            manager.SetBrowControlMode(EmotionSB.BrowControlMode.Random);
            
            // 清理资源
            sentenceInfos = null;
            animGroupPlayDatas = null;
            ResetState();
            hasDialogueData = false;
        }

        public override void PlayDefaultAnimation(float duration = 0.5f)
        {
            if (defaultAnimGroup != null && manager != null && overrideDefault != null)
            {
                var animGroup = isPlaying ? defaultAnimGroup : overrideDefault;

                // 使用默认动画组的自然时长（不伸缩）
                float naturalDuration = 0f;

                // 如果动画组有动画信息，计算其自然时长
                if (animGroup.GetAnimInfos().Count > 0)
                {
                    foreach (var animInfo in animGroup.GetAnimInfos())
                    {
                        naturalDuration += animInfo.clip.length;
                    }
                }
                else
                {
                    // 如果没有动画信息，使用默认时长
                    naturalDuration = 3.0f;
                }

                // 获取播放数据
                var playData = animGroup.GetAnimStrategy(naturalDuration);
                if (playData != null)
                {
                    // 设置无限循环
                    foreach (var animPlayData in playData.animPlayDatas)
                    {
                        animPlayData.loopCount = 99999f;
                    }

                    //6.19,24.2025 宏福说让talkingIdle的前融合时间变长。
                    manager.PlayAnimGroupData(playData, true, 0.8F);
                }
            }
        }
    }
}

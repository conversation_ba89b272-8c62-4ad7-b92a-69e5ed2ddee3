%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &12342774
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 12342775}
  - component: {fileID: 12342776}
  m_Layer: 0
  m_Name: B1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &12342775
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 12342774}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 432577523}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &12342776
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 12342774}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c30edd1459a660b42ae0a8b22efda506, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  eventDataList:
  - rid: 6748164214851108866
  - rid: 6748164214851108867
  - rid: 6748164214851108872
  - rid: 6748164214851108871
  - rid: 6748164214851108873
  - rid: 6748164214851108874
  - rid: 6748164214851108875
  - rid: 6748164214851108876
  jsonDataList:
  - name: B1-0
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Talk
      about your weekend like a native.\",\n            \"segments\": [\n               
      {\n                    \"text\": \"Talk about your weekend\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"Talk\",\n                    \"term_start_second\": 0.0,\n                   
      \"segment_end_second\": 0.96,\n                    \"segment_start_second\":
      0.0\n                },\n                {\n                    \"text\": \"like
      a native\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"native\",\n                    \"term_start_second\": 1.3,\n                   
      \"segment_end_second\": 1.48,\n                    \"segment_start_second\":
      1\n                }\n            ]\n        },\n        {\n            \"text\":
      \"I say A, you say B.\",\n            \"segments\": [\n                {\n                   
      \"text\": \"I say A\",\n                    \"gesture_group\": \"PointSelf\",\n                   
      \"trigger_term\": \"I\",\n                    \"term_start_second\": 1.84,\n                   
      \"segment_end_second\": 2.48,\n                    \"segment_start_second\":
      1.84\n                },\n                {\n                    \"text\":
      \"you say B\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you\",\n                    \"term_start_second\": 2.68,\n                   
      \"segment_end_second\": 3.20,\n                    \"segment_start_second\":
      2.68\n                }\n            ]\n        }\n    ]\n}"
  - name: B1-1
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Do
      you already have any plans for this weekend?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"Do you already have any
      plans\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"plans\",\n                    \"term_start_second\": 0.8,\n                   
      \"segment_end_second\": 1,\n                    \"segment_start_second\": 0\n               
      },\n                {\n                    \"text\": \"for this weekend\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"weekend\",\n                    \"term_start_second\": 1.5,\n                   
      \"segment_end_second\": 1.76,\n                    \"segment_start_second\":
      1\n                }\n            ]\n        }\n    ]\n}"
  - name: B1-2
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"That
      sounds really lovely.\",\n            \"segments\": [\n                {\n                   
      \"text\": \"That sounds really lovely\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"sounds\",\n                   
      \"term_start_second\": 0.68,\n                    \"segment_end_second\": 1.48,\n                   
      \"segment_start_second\": 0.56\n                }\n            ]\n        },\n       
      {\n            \"text\": \"Is their house far from where you live?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Is their
      house far\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"Is\",\n                    \"term_start_second\": 2.2,\n                   
      \"segment_end_second\": 2.88,\n                    \"segment_start_second\":
      2.2\n                },\n                {\n                    \"text\": \"from
      where you live\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you\",\n                    \"term_start_second\": 3.24,\n                   
      \"segment_end_second\": 3.56,\n                    \"segment_start_second\":
      2.88\n                }\n            ]\n        }\n    ]\n}"
  - name: B1-3
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Are
      you planning to go there by train, or are you driving this time?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Are you
      planning to go there by train\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"planning\",\n                    \"term_start_second\":
      0.88,\n                    \"segment_end_second\": 2.04,\n                   
      \"segment_start_second\": 0.68\n                },\n                {\n                   
      \"text\": \"or are you driving this time\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"driving\",\n                   
      \"term_start_second\": 2.64,\n                    \"segment_end_second\": 3.32,\n                   
      \"segment_start_second\": 2.04\n                }\n            ]\n        }\n   
      ]\n}"
  - name: B1-4
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Nice.\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Nice\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"Nice\",\n                    \"term_start_second\": 0.72,\n                   
      \"segment_end_second\": 1.12,\n                    \"segment_start_second\":
      0.72\n                }\n            ]\n        },\n        {\n           
      \"text\": \"What do you usually do when you\u2019re there with your family?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"What do
      you usually do\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"do\",\n                    \"term_start_second\": 2.12,\n                   
      \"segment_end_second\": 2.88,\n                    \"segment_start_second\":
      2.12\n                },\n                {\n                    \"text\":
      \"when you\u2019re there with your family\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"there\",\n                   
      \"term_start_second\": 3.16,\n                    \"segment_end_second\": 4,\n                   
      \"segment_start_second\": 2.96\n                }\n            ]\n        }\n   
      ]\n}"
  - name: B1-5
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"That
      sounds peaceful.\",\n            \"segments\": [\n                {\n                   
      \"text\": \"That sounds peaceful\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"peaceful\",\n                   
      \"term_start_second\": 0.96,\n                    \"segment_end_second\": 1.48,\n                   
      \"segment_start_second\": 0.56\n                }\n            ]\n        },\n       
      {\n            \"text\": \"Do you enjoy getting out of the city once in a while?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Do you
      enjoy getting out of the city once in a while\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"enjoy\",\n                   
      \"term_start_second\": 2.8,\n                    \"segment_end_second\": 4.44,\n                   
      \"segment_start_second\": 2.52\n                }\n            ]\n        }\n   
      ]\n}"
  audioDataList:
  - name: A2
    audioClip: {fileID: 8300000, guid: 86ddba4ef520bc643836519618104e52, type: 3}
  - name: B1
    audioClip: {fileID: 8300000, guid: 4dfc03fbca834b849a245dda8d88b2d1, type: 3}
  isPlaying: 0
  currentTime: 0
  completedEvents: 0
  references:
    version: 2
    RefIds:
    - rid: 6748164214851108866
      type: {class: LoadAvatarAndSetupEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 0
        isActive: 1
        characterId: NPC_Girl_00001
        seed: 4024
    - rid: 6748164214851108867
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedJsonIndex: 0
    - rid: 6748164214851108871
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 6
        isActive: 1
        selectedJsonIndex: 1
    - rid: 6748164214851108872
      type: {class: PlayAudioEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedAudioIndex: 1
    - rid: 6748164214851108873
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 14
        isActive: 1
        selectedJsonIndex: 2
    - rid: 6748164214851108874
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 26
        isActive: 1
        selectedJsonIndex: 3
    - rid: 6748164214851108875
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 35
        isActive: 1
        selectedJsonIndex: 4
    - rid: 6748164214851108876
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 48
        isActive: 1
        selectedJsonIndex: 5
--- !u!1 &18910275
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 18910276}
  - component: {fileID: 18910277}
  m_Layer: 0
  m_Name: OldPrompt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &18910276
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 18910275}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 176430980}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &18910277
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 18910275}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f8e9d0c1b2a3456789012345678901ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  azureEndpoint: https://japan-east-gpt-01.openai.azure.com/
  deploymentName: gpt-4o-global-01
  apiKey: ********************************
  apiVersion: 2025-01-01-preview
  temperature: 0.7
  maxTokens: 1000
  topP: 0.95
  frequencyPenalty: 0
  presencePenalty: 0
  systemPrompt: "You are a professional English text sentence parser and action tag
    analyzer for character dialogue.\r\n\r\nTask: Divide the target text into independent
    sentences and assign two action tags to each sentence. These tags will guide
    appropriate gestures when the character delivers each line.\r\n\r\nParsing rules:\r\n-
    Process English text only\r\n- Use standard sentence boundaries (., ?, !), handle
    abbreviations, hyphens and dashes correctly without incorrect splitting\r\n-
    Combine very short phrases or sentences (e.g. 1-2 words) with the adjacent sentence\r\n-
    Preserve the original wording and punctuation of each sentence\r\n- Do not missing
    any part of the target text\r\n\r\nFor each sentence, assign:\r\n1. Function
    Tag (choose ONE):\r\n   - Self-Reference: Gestures directed toward oneself\r\n  
    - Audience-Direction: Gestures directed toward the listener/dialogue partner\r\n  
    - Objective Description: Gestures for objective statements or descriptions\r\n  
    - Emphasis & Contrast: Gestures that emphasize points or show contrast\r\n\r\n2.
    Intensity Tag (choose ONE):\r\n   - High: Larger, more pronounced gestures\r\n  
    - Low: Smaller, more subtle gestures\r\n\r\n- Consider character's role, personality,
    and conversation context when determining tags.\r\n- Avoid identical tag combinations
    in consecutive sentences (both function_tag and intensity_tag should not be identical
    for adjacent sentences)\r\n\r\nReturn ONLY this JSON format:\r\n{\r\n    \"sentences\":
    [\r\n        {\r\n            \"text\": \"sentence content\",\r\n           
    \"function_tag\": \"function tag\",\r\n            \"intensity_tag\": \"intensity
    tag\"\r\n        }\r\n    ]\r\n}\r\n\r\nExample input:\r\nCharacter Role: Photographer\r\nCharacter
    Personality: Relaxed; Observant; Curious; Patient; Unconventional\r\nPrevious
    line: \"What do you like the most about photography?\"\r\nTarget text: \"Ah,
    I love the light. It changes everything.\"\r\n\r\nExample output:\r\n{\r\n   
    \"sentences\": [\r\n        {\r\n            \"text\": \"Ah, I love the light.\",\r\n           
    \"function_tag\": \"Self-Reference\",\r\n            \"intensity_tag\": \"Low\"\r\n       
    },\r\n        {\r\n            \"text\": \"It changes everything.\",\r\n           
    \"function_tag\": \"Emphasis & Contrast\",\r\n            \"intensity_tag\":
    \"Low\"\r\n        }\r\n    ]\r\n}\r\n\r\nInput to analyze:\r\nCharacter Role:
    {{avatar_job_title}}\r\nCharacter Personality: {{avatar_personalities}}\r\nPrevious
    line from another speaker (optional): {{user_last_reply}}\r\nTarget text: {{avatar_last_reply}}"
  userPrompt: 'Character Role: chef

    Character Personality: Passionate, lively,
    cheerful, and helpful

    Previous line from another speaker (optional): nil

    Target
    text:Hey there, bestie!I''m Sarah, and literally obsessed with tackling exam
    stress together.How do you usually handle the pressure during exams?'
  apiResponse: "Azure GPT\u54CD\u5E94\uFF1A\n\n{\n    \"sentences\": [\n        {\n           
    \"text\": \"Hey there, bestie!\",\n            \"function_tag\": \"Audience-Direction\",\n           
    \"intensity_tag\": \"High\"\n        },\n        {\n            \"text\": \"I'm
    Sarah, and literally obsessed with tackling exam stress together.\",\n           
    \"function_tag\": \"Self-Reference\",\n            \"intensity_tag\": \"Low\"\n       
    },\n        {\n            \"text\": \"How do you usually handle the pressure
    during exams?\",\n            \"function_tag\": \"Audience-Direction\",\n           
    \"intensity_tag\": \"Low\"\n        }\n    ]\n}\n\n\U0001F4CA \u4F7F\u7528\u7EDF\u8BA1\uFF1A\n\u2022
    \u63D0\u793AToken: 561\n\u2022 \u5B8C\u6210Token: 123\n\u2022 \u603BToken: 684\n\n\u23F1\uFE0F
    \u8BF7\u6C42\u8017\u65F6\u7EDF\u8BA1\uFF1A\n\u2022 \u603B\u8017\u65F6: 4.08 \u79D2\n\u2022
    \u5F00\u59CB\u65F6\u95F4: 17:33:19.845\n\u2022 \u7ED3\u675F\u65F6\u95F4: 17:33:23.920\n\u2022
    \u6027\u80FD\u8BC4\u7EA7: \u26A1 \u5FEB\u901F\n"
  isProcessing: 0
  lastError: 
  lastRequestTime: 4.0755844
  performanceStats: "\u23F1\uFE0F \u8BF7\u6C42\u8017\u65F6\u7EDF\u8BA1\uFF1A\n\u2022
    \u603B\u8017\u65F6: 4.08 \u79D2\n\u2022 \u5F00\u59CB\u65F6\u95F4: 17:33:19.845\n\u2022
    \u7ED3\u675F\u65F6\u95F4: 17:33:23.920\n\u2022 \u6027\u80FD\u8BC4\u7EA7: \u26A1
    \u5FEB\u901F\n"
--- !u!1 &52150862
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 52150864}
  - component: {fileID: 52150863}
  - component: {fileID: 52150865}
  - component: {fileID: 52150866}
  m_Layer: 0
  m_Name: SimpleAnimTest
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &52150863
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 52150862}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7e4e2acfdaf29134a81d45e3580a9519, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _characterAnimator: {fileID: 0}
  _emotionAnimator: {fileID: 0}
  _emotionSkinnedMeshRenderer: {fileID: 0}
  _emotionAnimationDataObject: {fileID: 0}
  headMask: {fileID: 0}
  bodyMask: {fileID: 0}
  actionGroupConfigs:
  - groupName: "\u9ED8\u8BA4\u52A8\u4F5C\u7EC4"
    animInfos:
    - clip: {fileID: 7400000, guid: 5300f7d5ca8e1c445acc4f6708149033, type: 2}
      canBlend: 1
      blendInPercent: 0.25
      blendOutPercent: 0.25
      canLoop: 1
      minLoopMultiplier: 1
      maxLoopMultiplier: 10
      loopBlendTime: 0.2
      canChangeSpeed: 0
      minSpeedMultiplier: 1
      maxSpeedMultiplier: 1
      enableFootIK: 0
    expectedDuration: 0
  - groupName: "\u52A8\u4F5C\u7EC42"
    animInfos:
    - clip: {fileID: 7400000, guid: 6fab1da20f6ca4173a852d248d2b06c2, type: 2}
      canBlend: 1
      blendInPercent: 0.25
      blendOutPercent: 0
      canLoop: 0
      minLoopMultiplier: 1
      maxLoopMultiplier: 1.98
      loopBlendTime: 0.188
      canChangeSpeed: 1
      minSpeedMultiplier: 0.8
      maxSpeedMultiplier: 1.2
      enableFootIK: 0
    - clip: {fileID: 7400000, guid: c1c2b3be694f947eb8fa1ad006f9c442, type: 2}
      canBlend: 1
      blendInPercent: 0.5
      blendOutPercent: 0.5
      canLoop: 1
      minLoopMultiplier: 0
      maxLoopMultiplier: 4.92
      loopBlendTime: 0.2
      canChangeSpeed: 1
      minSpeedMultiplier: 0.8
      maxSpeedMultiplier: 1.2
      enableFootIK: 0
    - clip: {fileID: 7400000, guid: 85e88d761c64b44af89e578d7a1ced4b, type: 2}
      canBlend: 1
      blendInPercent: 0
      blendOutPercent: 0.25
      canLoop: 0
      minLoopMultiplier: 0
      maxLoopMultiplier: 0
      loopBlendTime: 0.2
      canChangeSpeed: 1
      minSpeedMultiplier: 0.8
      maxSpeedMultiplier: 1.2
      enableFootIK: 0
    expectedDuration: 6
  - groupName: "\u52A8\u4F5C\u7EC43"
    animInfos:
    - clip: {fileID: 7400000, guid: cc42dc2ee74cb44be8b3c7e6b388614b, type: 2}
      canBlend: 1
      blendInPercent: 0.25
      blendOutPercent: 0
      canLoop: 0
      minLoopMultiplier: 0
      maxLoopMultiplier: 0
      loopBlendTime: 0.2
      canChangeSpeed: 1
      minSpeedMultiplier: 0.8
      maxSpeedMultiplier: 1.2
      enableFootIK: 0
    - clip: {fileID: 7400000, guid: 974820439f90246f680c428201d4863d, type: 2}
      canBlend: 1
      blendInPercent: 0.5
      blendOutPercent: 0.5
      canLoop: 1
      minLoopMultiplier: 0
      maxLoopMultiplier: 2
      loopBlendTime: 0.2
      canChangeSpeed: 1
      minSpeedMultiplier: 0.8
      maxSpeedMultiplier: 1.2
      enableFootIK: 0
    - clip: {fileID: 7400000, guid: bef4a6d2ebf9c4b9ba41a25a1f1ec965, type: 2}
      canBlend: 1
      blendInPercent: 0
      blendOutPercent: 0.25
      canLoop: 0
      minLoopMultiplier: 0
      maxLoopMultiplier: 0
      loopBlendTime: 0.2
      canChangeSpeed: 1
      minSpeedMultiplier: 0.8
      maxSpeedMultiplier: 1.2
      enableFootIK: 0
    expectedDuration: 3
  transitionTime: 0.2
  playGroup1Button: {fileID: 0}
  playGroup2Button: {fileID: 0}
  playGroup3Button: {fileID: 0}
  stopButton: {fileID: 0}
  initButton: {fileID: 973902513}
  startRandomMoveButton: {fileID: 0}
  noddingButton: {fileID: 0}
  shakingButton: {fileID: 0}
  customRotateButton: {fileID: 0}
  lookAtButton: {fileID: 0}
  cancelLookAtButton: {fileID: 0}
  blinkButton: {fileID: 0}
  noddingSpeed: 1
  noddingCount: 7
  shakingSpeed: 1
  shakingCount: 7
  customRotation: {x: 15, y: 0, z: 0}
  customRotationDuration: 1
  lookAtDirection: {x: 0, y: 30, z: 0}
  lookAtTransitionDuration: 0.5
  lookAtHoldDuration: 2
  naturalCurveButton: {fileID: 0}
  defaultCurveButton: {fileID: 0}
  excelAnimGroupId: 
  loadExcelButton: {fileID: 0}
  blinkDuration: 0.2
  blinkMinInterval: 2
  blinkMaxInterval: 8
  autoBlinkEnabled: 1
  emotionClips:
  - {fileID: 7400000, guid: cad1adca877444f488abb88641d22454, type: 2}
  emotionButtons:
  - {fileID: 0}
  animManager: {fileID: 0}
--- !u!4 &52150864
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 52150862}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.009}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1882667841}
  m_Father: {fileID: 659749691}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &52150865
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 52150862}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3298fca3c3084334886c8f4d358fa3c8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  dialogManager: {fileID: 0}
  dialogueDataFile: {fileID: 0}
  dialogue1Audio: {fileID: 8300000, guid: 180ed0a7254da5346a298e60ce00b502, type: 3}
  dialogue2Audio: {fileID: 0}
  dialogue1:
  - duration: 1.4
    willStartAt: 0
    semanticType: 5
    semanticSideType: 1
  - duration: 1.5
    willStartAt: 0.2
    semanticType: 4
    semanticSideType: 1
  dialogue2:
  - duration: 5
    willStartAt: 0
    semanticType: 1
    semanticSideType: 1
  - duration: 3
    willStartAt: 0
    semanticType: 5
    semanticSideType: 1
  - duration: 6
    willStartAt: 0
    semanticType: 0
    semanticSideType: 1
  initializeButton: {fileID: 0}
  startTestButton: {fileID: 0}
  interruptButton: {fileID: 0}
  enterStateButton: {fileID: 0}
  characterSpeakingButton: {fileID: 0}
  idleStateButton: {fileID: 0}
  playerSpeakingButton: {fileID: 0}
  endStateButton: {fileID: 0}
--- !u!114 &52150866
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 52150862}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f9523ea75b34154bb7763a4e54eb4a9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  animationClip: {fileID: 7400000, guid: 06e03f5b89a92d042a0e98e102ae91ad, type: 2}
  animator: {fileID: 0}
  autoPlay: 1
  loop: 1
  speed: 1
  showDebugInfo: 1
--- !u!1 &176430979
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 176430980}
  - component: {fileID: 176430981}
  m_Layer: 0
  m_Name: GPTTest
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &176430980
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 176430979}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 421.7267, y: 658.32837, z: -34.687717}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 18910276}
  - {fileID: 464217956}
  - {fileID: 1369432281}
  - {fileID: 426681672}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &176430981
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 176430979}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f3a3720bf64159348994833bab8f918d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &255944802
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 255944804}
  - component: {fileID: 255944803}
  - component: {fileID: 255944805}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &255944803
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 255944802}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 1
  m_Shape: 0
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!4 &255944804
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 255944802}
  serializedVersion: 2
  m_LocalRotation: {x: 0.30562273, y: -0.14057504, z: 0.045675594, w: 0.94061}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 36, y: -17, z: 0}
--- !u!114 &255944805
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 255944802}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!1 &426681671
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 426681672}
  - component: {fileID: 426681673}
  m_Layer: 0
  m_Name: TestPrompt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &426681672
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 426681671}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 176430980}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &426681673
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 426681671}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f8e9d0c1b2a3456789012345678901ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  azureEndpoint: https://japan-east-gpt-01.openai.azure.com/
  deploymentName: gpt-4o-global-01
  apiKey: ********************************
  apiVersion: 2025-01-01-preview
  temperature: 0.7
  maxTokens: 1000
  topP: 0.95
  frequencyPenalty: 0
  presencePenalty: 0
  systemPrompt: Hello, please solve the math problem
  userPrompt: 'what''s the result of:

    3x5'
  apiResponse: "Azure GPT\u54CD\u5E94\uFF1A\n\nThe result of 3 times 5 is 15.\n\n\U0001F4CA
    \u4F7F\u7528\u7EDF\u8BA1\uFF1A\n\u2022 \u63D0\u793AToken: 27\n\u2022 \u5B8C\u6210Token:
    13\n\u2022 \u603BToken: 40\n\n\u23F1\uFE0F \u8BF7\u6C42\u8017\u65F6\u7EDF\u8BA1\uFF1A\n\u2022
    \u603B\u8017\u65F6: 2.69 \u79D2\n\u2022 \u5F00\u59CB\u65F6\u95F4: 17:33:19.874\n\u2022
    \u7ED3\u675F\u65F6\u95F4: 17:33:22.568\n\u2022 \u6027\u80FD\u8BC4\u7EA7: \u26A1
    \u5FEB\u901F\n"
  isProcessing: 0
  lastError: 
  lastRequestTime: 2.6927414
  performanceStats: "\u23F1\uFE0F \u8BF7\u6C42\u8017\u65F6\u7EDF\u8BA1\uFF1A\n\u2022
    \u603B\u8017\u65F6: 2.69 \u79D2\n\u2022 \u5F00\u59CB\u65F6\u95F4: 17:33:19.874\n\u2022
    \u7ED3\u675F\u65F6\u95F4: 17:33:22.568\n\u2022 \u6027\u80FD\u8BC4\u7EA7: \u26A1
    \u5FEB\u901F\n"
--- !u!1 &432577522
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 432577523}
  - component: {fileID: 432577525}
  m_Layer: 0
  m_Name: AnimPlayer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &432577523
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 432577522}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 99.99223, y: 1.3598176, z: 0.52692693}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 582661175}
  - {fileID: 1728179337}
  - {fileID: 12342775}
  - {fileID: 470172624}
  - {fileID: 2022420741}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &432577525
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 432577522}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c30edd1459a660b42ae0a8b22efda506, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  eventDataList:
  - rid: 6748164214851108866
  - rid: 6748164214851108867
  - rid: 6748164214851108872
  - rid: 6748164214851108870
  - rid: 6748164214851108871
  - rid: 6748164214851108873
  - rid: 6748164214851108874
  - rid: 6748164214851108875
  - rid: 6748164214851108876
  jsonDataList:
  - name: "A1\u5F00\u5934"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Let\u2019s
      go shopping in English!\",\n            \"segments\": [\n                {\n                   
      \"text\": \"Let\u2019s go shopping\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"Let\u2019s\",\n                   
      \"term_start_second\": 0.0,\n                    \"segment_end_second\": 1.56,\n                   
      \"segment_start_second\": 0.0\n                },\n                {\n                   
      \"text\": \"in English\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"English\",\n                    \"term_start_second\":
      2.00,\n                    \"segment_end_second\": 2.68,\n                   
      \"segment_start_second\": 1.80\n                }\n            ]\n        },\n       
      {\n            \"text\": \"I\u2019m A, you\u2019re B \u2014 repeat after me.\",\n           
      \"segments\": [\n                {\n                    \"text\": \"I\u2019m
      A\",\n                    \"gesture_group\": \"PointSelf\",\n                   
      \"trigger_term\": \"I\u2019m\",\n                    \"term_start_second\":
      3.16,\n                    \"segment_end_second\": 3.76,\n                   
      \"segment_start_second\": 3.16\n                },\n                {\n                   
      \"text\": \"you\u2019re B\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you\u2019re\",\n                    \"term_start_second\":
      4.04,\n                    \"segment_end_second\": 4.72,\n                   
      \"segment_start_second\": 4.04\n                },\n                {\n                   
      \"text\": \"repeat after me\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"repeat\",\n                    \"term_start_second\": 4.92,\n                   
      \"segment_end_second\": 5.96,\n                    \"segment_start_second\":
      4.92\n                }\n            ]\n        }\n    ]\n}"
  - name: "A1\u7B2C\u4E00\u53E5"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Do
      you go grocery shopping every week, or just when you need something?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Do you
      go grocery shopping every week\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"Do\",\n                    \"term_start_second\": 0,\n                   
      \"segment_end_second\": 2.12,\n                    \"segment_start_second\":
      0\n                },\n                {\n                    \"text\": \"or
      just when you need something\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"need\",\n                    \"term_start_second\": 2.96,\n                   
      \"segment_end_second\": 3.52,\n                    \"segment_start_second\":
      2.36\n                }\n            ]\n        }\n    ]\n}"
  - name: "A2\u7B2C\u4E8C\u53E5"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"What
      do you usually buy when you go shopping for food?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"What do you usually buy\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"What\",\n                    \"term_start_second\": 0.56,\n                   
      \"segment_end_second\": 1.64,\n                    \"segment_start_second\":
      0.56\n                },\n                {\n                    \"text\":
      \"when you go shopping for food\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"shopping\",\n                   
      \"term_start_second\": 2,\n                    \"segment_end_second\": 2.92,\n                   
      \"segment_start_second\": 1.68\n                }\n            ]\n        }\n   
      ]\n}"
  - name: "A2\u7B2C\u4E09\u53E5"
    jsonContent: "{\n    \"sentences\": [\n       {\n            \"text\": \"Do you
      always shop at the same store, or do you like to try different places?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Do you
      always shop at the same store\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"Do\",\n                    \"term_start_second\": 0.84,\n                   
      \"segment_end_second\": 2.68,\n                    \"segment_start_second\":
      0.84\n                },\n                {\n                    \"text\":
      \"or do you like to try different places\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"do\",\n                   
      \"term_start_second\": 3,\n                    \"segment_end_second\": 4.8,\n                   
      \"segment_start_second\": 2.72\n                }\n            ]\n        }\n   
      ]\n}"
  - name: "A2\u7B2C\u56DB\u53E5"
    jsonContent: "{\n    \"sentences\": [\n       {\n            \"text\": \"That\u2019s
      nice! Do you talk and plan meals together while shopping?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"That\u2019s nice\",\n                   
      \"gesture_group\": \"emphasis/contrast\",\n                    \"trigger_term\":
      \"nice\",\n                    \"term_start_second\": 0.4,\n                   
      \"segment_end_second\": 0.88,\n                    \"segment_start_second\":
      0.08\n                },\n                {\n                    \"text\":
      \"Do you talk and plan meals together while shopping\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"Do\",\n                    \"term_start_second\": 0.96,\n                   
      \"segment_end_second\": 3.6,\n                    \"segment_start_second\":
      0.96\n                }\n            ]\n        }\n    ]\n}"
  - name: "A2\u7B2C\u4E94\u53E5"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"I get
      that. It\u2019s easy to buy more than you planned when everything looks good.\",\n           
      \"segments\": [\n                {\n                    \"text\": \"I get that\",\n                   
      \"gesture_group\": \"PointSelf\",\n                    \"trigger_term\": \"I\",\n                   
      \"term_start_second\": 0.76,\n                    \"segment_end_second\": 1.4,\n                   
      \"segment_start_second\": 0.76\n                },\n                {\n                   
      \"text\": \"It\u2019s easy to buy more than you planned\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"easy\",\n                    \"term_start_second\": 1.88,\n                   
      \"segment_end_second\": 3.44,\n                    \"segment_start_second\":
      1.64\n                },\n                {\n                    \"text\":
      \"when everything looks good\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"looks\",\n                    \"term_start_second\": 4,\n                   
      \"segment_end_second\": 4.64,\n                    \"segment_start_second\":
      3.48\n                }\n            ]\n        }\n    ]\n}"
  audioDataList:
  - name: A2
    audioClip: {fileID: 8300000, guid: 86ddba4ef520bc643836519618104e52, type: 3}
  - name: Audio Data 1
    audioClip: {fileID: 8300000, guid: 6a9beee4c857fc94383b9b13bdbfb71e, type: 3}
  isPlaying: 0
  currentTime: 0
  completedEvents: 0
  references:
    version: 2
    RefIds:
    - rid: 6748164214851108866
      type: {class: LoadAvatarAndSetupEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 0
        isActive: 1
        characterId: NPC_Girl_00004
        seed: 4024
    - rid: 6748164214851108867
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedJsonIndex: 0
    - rid: 6748164214851108870
      type: {class: EnterDialogueIdleStateEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 7
        isActive: 1
    - rid: 6748164214851108871
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 8.24
        isActive: 1
        selectedJsonIndex: 1
    - rid: 6748164214851108872
      type: {class: PlayAudioEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedAudioIndex: 0
    - rid: 6748164214851108873
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 16
        isActive: 1
        selectedJsonIndex: 2
    - rid: 6748164214851108874
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 23
        isActive: 1
        selectedJsonIndex: 3
    - rid: 6748164214851108875
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 32
        isActive: 1
        selectedJsonIndex: 4
    - rid: 6748164214851108876
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 40
        isActive: 1
        selectedJsonIndex: 5
--- !u!1 &464217955
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 464217956}
  - component: {fileID: 464217957}
  m_Layer: 0
  m_Name: NewPrompt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &464217956
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 464217955}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 176430980}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &464217957
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 464217955}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f8e9d0c1b2a3456789012345678901ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  azureEndpoint: https://japan-east-gpt-01.openai.azure.com/
  deploymentName: gpt-4o-global-01
  apiKey: ********************************
  apiVersion: 2025-01-01-preview
  temperature: 0.8
  maxTokens: 1000
  topP: 0.8
  frequencyPenalty: 0
  presencePenalty: 0
  systemPrompt: "Role: You segment character speech and assign gesture triggers for
    real-time animation.\r\n\r\nTask Instructions:\r\nGiven an English character
    line, follow these steps:\r\n1. Divide the input into full sentences using standard
    punctuation (. ! ?).\r\n2. For each sentence, divide it further into one or more
    gesture segments.\r\n3. Each gesture segment must:\r\n   - Be a small chunk of
    speech that aligns with a natural gesture moment;\r\n   - Be assigned one gesture_group;\r\n  
    - Clearly identify a trigger_term where the gesture should begin.\r\n\r\nDefinition:
    Gesture Segment\r\n- A gesture segment is a sub-unit of a sentence aligned with
    rhythm, semantic, or emotional shifts.Examples: greetings, self-introductions,
    emphasis, contrast, asking a question, etc.\r\n- No fixed length requirement
    - follow natural speaking rhythm.\r\n- The trigger_term should align with the
    emotional or gestural emphasis in the segment.\r\n- One sentence may have one
    or multiple segments. \r\n- Avoid excessive consecutive segmentation (<= 4 words)
    without pause points ( , . ! ?) to prevent overly frequent gesture transitions.\r\n-
    Important: All fragments connected together must be the original complete sentence
    exactly. Do not skip, repeat, or change any words.\r\n\r\nGesture Group Options
    (select one per segment):\r\n Each gesture group comes with a specific physical
    motion. You should imagine whether the gesture is appropriate in context - do
    not assign a gesture based solely on text.\r\n\r\n1. \"PointSelf\"\r\n   - When
    the speaker talks explicitly about themselves (e.g., \"I am\", \"I think\", \"My
    name is\").\r\n   - Physical gesture: hand to chest.\r\n   - Use only if this
    gesture feels appropriate and meaningful.\r\n\r\n2. \"PointPlayer\"\r\n   - When
    addressing the user directly - asking a question, giving an invitation or suggestion
    (e.g., \"Can you...?\").\r\n   - Physical gesture: lean forward and point outward.\r\n\r\n3.
    \"emphasis/contrast\"\r\n   - Used for emphasis, correction, contrast, or surprise
    (e.g., \"but\", \"actually\", \"especially\").\r\n   - Physical gesture: same
    as state/default (no unique animation).\r\n\r\n4. \"state/default\"\r\n   - Used
    for neutral, descriptive, reflective, or fallback segments.\r\n   - Physical
    gesture: general-purpose gestures,acceptable in all scenarios.\r\n\r\n5.\"Greeting\"\r\n-
    When the speaker initiates a social greeting or friendly opening, especially
    at the beginning of an interaction (e.g., \"Hey there!\", \"Hi everyone\").\r\n-
    Physical gesture: casual wave or open-hand gesture near the head or shoulder.\r\n-
    Use only when the gesture serves a clear greeting purpose at the social or conversational
    level.\r\n\r\nExample Input:\r\nHey buddy, I'm Ray!\r\n\r\nExample Output Format:\r\nSentence:Return
    Origin full sentence here\r\n  - Segment: Return Full Segment Text here, Group:
    One of 5 gesture group, Trigger: trigger_term(one word)\r\n\r\nExample Output
    (strictly follow this format, No markdown or extra symbols.):\r\nSentence:Hey
    buddy, I'm Ray!\r\n  - Segment: Hey buddy, Group: Greeting, Trigger: Hey\r\n 
    - Segment: I\u2019m Ray, Group: PointSelf, Trigger: I'm\r\n"
  userPrompt: Practice real conversation with our AI avatar every day. Hit follow
    for more speaking drills like this!
  apiResponse: "Azure GPT\u54CD\u5E94\uFF1A\n\nSentence:Practice real conversation
    with our AI avatar every day.\r\n  - Segment: Practice real conversation, Group:
    state/default, Trigger: Practice\r\n  - Segment: with our AI avatar every day,
    Group: state/default, Trigger: avatar\r\nSentence:Hit follow for more speaking
    drills like this!\r\n  - Segment: Hit follow, Group: PointPlayer, Trigger: Hit\r\n 
    - Segment: for more speaking drills like this, Group: state/default, Trigger:
    more\n\n\U0001F4CA \u4F7F\u7528\u7EDF\u8BA1\uFF1A\n\u2022 \u63D0\u793AToken:
    665\n\u2022 \u5B8C\u6210Token: 96\n\u2022 \u603BToken: 761\n\n\u23F1\uFE0F \u8BF7\u6C42\u8017\u65F6\u7EDF\u8BA1\uFF1A\n\u2022
    \u603B\u8017\u65F6: 1.76 \u79D2\n\u2022 \u5F00\u59CB\u65F6\u95F4: 14:45:08.917\n\u2022
    \u7ED3\u675F\u65F6\u95F4: 14:45:10.680\n\u2022 \u6027\u80FD\u8BC4\u7EA7: \U0001F680
    \u6781\u5FEB\n"
  isProcessing: 0
  lastError: 
  lastRequestTime: 1.7617188
  performanceStats: "\u23F1\uFE0F \u8BF7\u6C42\u8017\u65F6\u7EDF\u8BA1\uFF1A\n\u2022
    \u603B\u8017\u65F6: 1.76 \u79D2\n\u2022 \u5F00\u59CB\u65F6\u95F4: 14:45:08.917\n\u2022
    \u7ED3\u675F\u65F6\u95F4: 14:45:10.680\n\u2022 \u6027\u80FD\u8BC4\u7EA7: \U0001F680
    \u6781\u5FEB\n"
--- !u!1 &470172623
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 470172624}
  - component: {fileID: 470172625}
  m_Layer: 0
  m_Name: A2-2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &470172624
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 470172623}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 432577523}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &470172625
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 470172623}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c30edd1459a660b42ae0a8b22efda506, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  eventDataList:
  - rid: 6748164214851108866
  - rid: 6748164214851108867
  - rid: 6748164214851108872
  jsonDataList:
  - name: B1-0
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Talk
      about your weekend like a native.\",\n            \"segments\": [\n               
      {\n                    \"text\": \"Talk about your weekend\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"Talk\",\n                    \"term_start_second\": 0.0,\n                   
      \"segment_end_second\": 0.96,\n                    \"segment_start_second\":
      0.0\n                },\n                {\n                    \"text\": \"like
      a native\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"native\",\n                    \"term_start_second\": 1.3,\n                   
      \"segment_end_second\": 1.48,\n                    \"segment_start_second\":
      1\n                }\n            ]\n        },\n        {\n            \"text\":
      \"I say A, you say B.\",\n            \"segments\": [\n                {\n                   
      \"text\": \"I say A\",\n                    \"gesture_group\": \"PointSelf\",\n                   
      \"trigger_term\": \"I\",\n                    \"term_start_second\": 1.84,\n                   
      \"segment_end_second\": 2.48,\n                    \"segment_start_second\":
      1.84\n                },\n                {\n                    \"text\":
      \"you say B\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you\",\n                    \"term_start_second\": 2.68,\n                   
      \"segment_end_second\": 3.20,\n                    \"segment_start_second\":
      2.68\n                }\n            ]\n        }\n    ]\n}"
  - name: B1-1
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Do
      you already have any plans for this weekend?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"Do you already have any
      plans\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"plans\",\n                    \"term_start_second\": 0.8,\n                   
      \"segment_end_second\": 1,\n                    \"segment_start_second\": 0\n               
      },\n                {\n                    \"text\": \"for this weekend\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"weekend\",\n                    \"term_start_second\": 1.5,\n                   
      \"segment_end_second\": 1.76,\n                    \"segment_start_second\":
      1\n                }\n            ]\n        }\n    ]\n}"
  - name: B1-2
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"That
      sounds really lovely.\",\n            \"segments\": [\n                {\n                   
      \"text\": \"That sounds really lovely\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"sounds\",\n                   
      \"term_start_second\": 0.68,\n                    \"segment_end_second\": 1.48,\n                   
      \"segment_start_second\": 0.56\n                }\n            ]\n        },\n       
      {\n            \"text\": \"Is their house far from where you live?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Is their
      house far\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"Is\",\n                    \"term_start_second\": 2.2,\n                   
      \"segment_end_second\": 2.88,\n                    \"segment_start_second\":
      2.2\n                },\n                {\n                    \"text\": \"from
      where you live\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you\",\n                    \"term_start_second\": 3.24,\n                   
      \"segment_end_second\": 3.56,\n                    \"segment_start_second\":
      2.88\n                }\n            ]\n        }\n    ]\n}"
  - name: B1-3
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Are
      you planning to go there by train, or are you driving this time?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Are you
      planning to go there by train\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"planning\",\n                    \"term_start_second\":
      0.88,\n                    \"segment_end_second\": 2.04,\n                   
      \"segment_start_second\": 0.68\n                },\n                {\n                   
      \"text\": \"or are you driving this time\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"driving\",\n                   
      \"term_start_second\": 2.64,\n                    \"segment_end_second\": 3.32,\n                   
      \"segment_start_second\": 2.04\n                }\n            ]\n        }\n   
      ]\n}"
  - name: B1-4
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Nice.\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Nice\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"Nice\",\n                    \"term_start_second\": 0.72,\n                   
      \"segment_end_second\": 1.12,\n                    \"segment_start_second\":
      0.72\n                }\n            ]\n        },\n        {\n           
      \"text\": \"What do you usually do when you\u2019re there with your family?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"What do
      you usually do\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"do\",\n                    \"term_start_second\": 2.12,\n                   
      \"segment_end_second\": 2.88,\n                    \"segment_start_second\":
      2.12\n                },\n                {\n                    \"text\":
      \"when you\u2019re there with your family\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"there\",\n                   
      \"term_start_second\": 3.16,\n                    \"segment_end_second\": 4,\n                   
      \"segment_start_second\": 2.96\n                }\n            ]\n        }\n   
      ]\n}"
  - name: B1-5
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"That
      sounds peaceful.\",\n            \"segments\": [\n                {\n                   
      \"text\": \"That sounds peaceful\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"peaceful\",\n                   
      \"term_start_second\": 0.96,\n                    \"segment_end_second\": 1.48,\n                   
      \"segment_start_second\": 0.56\n                }\n            ]\n        },\n       
      {\n            \"text\": \"Do you enjoy getting out of the city once in a while?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Do you
      enjoy getting out of the city once in a while\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"enjoy\",\n                   
      \"term_start_second\": 2.8,\n                    \"segment_end_second\": 4.44,\n                   
      \"segment_start_second\": 2.52\n                }\n            ]\n        }\n   
      ]\n}"
  - name: A2-2 Talking about hobbies
    jsonContent: "{\n    \"sentences\": [\n          {\n            \"text\": \"Let\u2019s
      practice everyday English!\",\n            \"segments\": [\n               
      {\n                    \"text\": \"Let\u2019s practice\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"Let\u2019s\",\n                    \"term_start_second\": 0.12,\n                   
      \"segment_end_second\": 1,\n                    \"segment_start_second\": 0.12\n               
      },\n                {\n                    \"text\": \"everyday English\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"everyday\",\n                    \"term_start_second\": 1,\n                   
      \"segment_end_second\": 2.12,\n                    \"segment_start_second\":
      1\n                }\n            ]\n        },\n        {\n            \"text\":
      \"I\u2019ll be A, you\u2019ll be B.\",\n            \"segments\": [\n               
      {\n                    \"text\": \"I\u2019ll be A\",\n                    \"gesture_group\":
      \"PointSelf\",\n                    \"trigger_term\": \"I'll\",\n                   
      \"term_start_second\": 2.84,\n                    \"segment_end_second\":3.6,\n                   
      \"segment_start_second\": 2.84\n                },\n                {\n                   
      \"text\": \"you\u2019ll be B\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you'll\",\n                    \"term_start_second\": 4.24,\n                   
      \"segment_end_second\":5,\n                    \"segment_start_second\": 4.24\n               
      }\n            ]\n        },\n        {\n            \"text\": \"So, what do
      you usually do in your free time?\",\n            \"segments\": [\n               
      {\n                    \"text\": \"So, what do you usually do\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"do\",\n                    \"term_start_second\": 7.56,\n                   
      \"segment_end_second\": 8.44,\n                    \"segment_start_second\":
      6.2\n                },\n                {\n                    \"text\": \"in
      your free time\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"free\",\n                    \"term_start_second\": 8.8,\n                   
      \"segment_end_second\": 9.32,\n                    \"segment_start_second\":
      8.48\n                }\n            ]\n        },\n        {\n           
      \"text\": \"That\u2019s cool!\",\n            \"segments\": [\n               
      {\n                    \"text\": \"That\u2019s cool\",\n                   
      \"gesture_group\": \"emphasis/contrast\",\n                    \"trigger_term\":
      \"cool\",\n                    \"term_start_second\": 12.68,\n                   
      \"segment_end_second\": 13.32,\n                    \"segment_start_second\":
      12.68\n                }\n            ]\n        },\n        {\n           
      \"text\": \"What kind of music do you listen to?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"What kind of music\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"music\",\n                    \"term_start_second\": 14.24,\n                   
      \"segment_end_second\": 15.08,\n                    \"segment_start_second\":
      14.24\n                },\n                {\n                    \"text\":
      \"do you listen to\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"listen\",\n                    \"term_start_second\":15.08,\n                   
      \"segment_end_second\": 15.82,\n                    \"segment_start_second\":
      15.08\n                }\n            ]\n        },\n        {\n           
      \"text\": \"Do you draw just for fun, or do you take classes?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Do you
      draw just for fun\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"draw\",\n                    \"term_start_second\": 20.32,\n                   
      \"segment_end_second\": 21.48,\n                    \"segment_start_second\":
      20.32\n                },\n                {\n                    \"text\":
      \"or do you take classes\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"take\",\n                    \"term_start_second\": 22.24,\n                   
      \"segment_end_second\": 23,\n                    \"segment_start_second\":
      21.88\n                }\n            ]\n        },\n        {\n           
      \"text\": \"That\u2019s great.\",\n            \"segments\": [\n               
      {\n                    \"text\": \"That\u2019s great\",\n                   
      \"gesture_group\": \"emphasis/contrast\",\n                    \"trigger_term\":
      \"great\",\n                    \"term_start_second\": 27.64,\n                   
      \"segment_end_second\": 28.2,\n                    \"segment_start_second\":
      27.64\n                }\n            ]\n        },\n        {\n           
      \"text\": \"Have you ever shared your drawings online?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"Have you ever shared
      your drawings\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"shared\",\n                    \"term_start_second\": 29.48,\n                   
      \"segment_end_second\":30.84,\n                    \"segment_start_second\":
      29\n                }\n            ]\n        },\n          {\n           
      \"text\": \"Practice with our AI avatar every day to speak more confidently.\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Practice
      with our AI avatar every day\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"Practice\",\n                    \"term_start_second\":
      34.68,\n                    \"segment_end_second\": 37.04,\n                   
      \"segment_start_second\": 34.68\n                },\n                {\n                   
      \"text\": \"to speak more confidently\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"speak\",\n                   
      \"term_start_second\": 37.08,\n                    \"segment_end_second\":
      38.36,\n                    \"segment_start_second\": 37.08\n               
      }\n            ]\n        },\n        {\n            \"text\": \"Hit follow
      for more beginner-friendly English!\",\n            \"segments\": [\n               
      {\n                    \"text\": \"Hit follow\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"Hit\",\n                   
      \"term_start_second\": 39.28,\n                    \"segment_end_second\":
      39.76,\n                    \"segment_start_second\": 39.28\n               
      },\n                {\n                    \"text\": \"for more beginner-friendly
      English\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"English\",\n                    \"term_start_second\":
      39.8,\n                    \"segment_end_second\": 41.44,\n                   
      \"segment_start_second\": 39.8\n                }\n            ]\n        }\n   
      ]\n}"
  audioDataList:
  - name: A2
    audioClip: {fileID: 8300000, guid: 35404dcb8d59b6f49b125ec3f4507fc1, type: 3}
  - name: B1
    audioClip: {fileID: 8300000, guid: 9369c44b503eb4b4781aa9d6894fd7d1, type: 3}
  isPlaying: 0
  currentTime: 0
  completedEvents: 0
  references:
    version: 2
    RefIds:
    - rid: 6748164214851108866
      type: {class: LoadAvatarAndSetupEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 0
        isActive: 1
        characterId: NPC_Boy_00001
        seed: 4024
    - rid: 6748164214851108867
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedJsonIndex: 6
    - rid: 6748164214851108872
      type: {class: PlayAudioEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedAudioIndex: 0
--- !u!1 &582661174
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 582661175}
  - component: {fileID: 582661176}
  m_Layer: 0
  m_Name: A2 1-1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &582661175
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 582661174}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 432577523}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &582661176
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 582661174}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c30edd1459a660b42ae0a8b22efda506, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  eventDataList:
  - rid: 6748164214851108866
  - rid: 6748164214851108867
  - rid: 6748164214851108872
  - rid: 6748164214851108870
  - rid: 6748164214851108871
  - rid: 6748164214851108873
  - rid: 6748164214851108874
  - rid: 6748164214851108875
  - rid: 6748164214851108876
  jsonDataList:
  - name: "A1\u5F00\u5934"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Let\u2019s
      go shopping in English!\",\n            \"segments\": [\n                {\n                   
      \"text\": \"Let\u2019s go shopping\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"Let\u2019s\",\n                   
      \"term_start_second\": 0.0,\n                    \"segment_end_second\": 1.56,\n                   
      \"segment_start_second\": 0.0\n                },\n                {\n                   
      \"text\": \"in English\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"English\",\n                    \"term_start_second\":
      2.00,\n                    \"segment_end_second\": 2.68,\n                   
      \"segment_start_second\": 1.80\n                }\n            ]\n        },\n       
      {\n            \"text\": \"I\u2019m A, you\u2019re B \u2014 repeat after me.\",\n           
      \"segments\": [\n                {\n                    \"text\": \"I\u2019m
      A\",\n                    \"gesture_group\": \"PointSelf\",\n                   
      \"trigger_term\": \"I\u2019m\",\n                    \"term_start_second\":
      3.16,\n                    \"segment_end_second\": 3.76,\n                   
      \"segment_start_second\": 3.16\n                },\n                {\n                   
      \"text\": \"you\u2019re B\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you\u2019re\",\n                    \"term_start_second\":
      4.04,\n                    \"segment_end_second\": 4.72,\n                   
      \"segment_start_second\": 4.04\n                },\n                {\n                   
      \"text\": \"repeat after me\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"repeat\",\n                    \"term_start_second\": 4.92,\n                   
      \"segment_end_second\": 5.96,\n                    \"segment_start_second\":
      4.92\n                }\n            ]\n        }\n    ]\n}"
  - name: "A1\u7B2C\u4E00\u53E5"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Do
      you go grocery shopping every week, or just when you need something?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Do you
      go grocery shopping every week\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"Do\",\n                    \"term_start_second\": 0,\n                   
      \"segment_end_second\": 2.12,\n                    \"segment_start_second\":
      0\n                },\n                {\n                    \"text\": \"or
      just when you need something\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"need\",\n                    \"term_start_second\": 2.96,\n                   
      \"segment_end_second\": 3.52,\n                    \"segment_start_second\":
      2.36\n                }\n            ]\n        }\n    ]\n}"
  - name: "A2\u7B2C\u4E8C\u53E5"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"What
      do you usually buy when you go shopping for food?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"What do you usually buy\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"What\",\n                    \"term_start_second\": 0.56,\n                   
      \"segment_end_second\": 1.64,\n                    \"segment_start_second\":
      0.56\n                },\n                {\n                    \"text\":
      \"when you go shopping for food\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"shopping\",\n                   
      \"term_start_second\": 2,\n                    \"segment_end_second\": 2.92,\n                   
      \"segment_start_second\": 1.68\n                }\n            ]\n        }\n   
      ]\n}"
  - name: "A2\u7B2C\u4E09\u53E5"
    jsonContent: "{\n    \"sentences\": [\n       {\n            \"text\": \"Do you
      always shop at the same store, or do you like to try different places?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Do you
      always shop at the same store\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"Do\",\n                    \"term_start_second\": 0.84,\n                   
      \"segment_end_second\": 2.68,\n                    \"segment_start_second\":
      0.84\n                },\n                {\n                    \"text\":
      \"or do you like to try different places\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"do\",\n                   
      \"term_start_second\": 3,\n                    \"segment_end_second\": 4.8,\n                   
      \"segment_start_second\": 2.72\n                }\n            ]\n        }\n   
      ]\n}"
  - name: "A2\u7B2C\u56DB\u53E5"
    jsonContent: "{\n    \"sentences\": [\n       {\n            \"text\": \"That\u2019s
      nice! Do you talk and plan meals together while shopping?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"That\u2019s nice\",\n                   
      \"gesture_group\": \"emphasis/contrast\",\n                    \"trigger_term\":
      \"nice\",\n                    \"term_start_second\": 0.4,\n                   
      \"segment_end_second\": 0.88,\n                    \"segment_start_second\":
      0.08\n                },\n                {\n                    \"text\":
      \"Do you talk and plan meals together while shopping\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"Do\",\n                    \"term_start_second\": 0.96,\n                   
      \"segment_end_second\": 3.6,\n                    \"segment_start_second\":
      0.96\n                }\n            ]\n        }\n    ]\n}"
  - name: "A2\u7B2C\u4E94\u53E5"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"I get
      that. It\u2019s easy to buy more than you planned when everything looks good.\",\n           
      \"segments\": [\n                {\n                    \"text\": \"I get that\",\n                   
      \"gesture_group\": \"PointSelf\",\n                    \"trigger_term\": \"I\",\n                   
      \"term_start_second\": 0.76,\n                    \"segment_end_second\": 1.4,\n                   
      \"segment_start_second\": 0.76\n                },\n                {\n                   
      \"text\": \"It\u2019s easy to buy more than you planned\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"easy\",\n                    \"term_start_second\": 1.88,\n                   
      \"segment_end_second\": 3.44,\n                    \"segment_start_second\":
      1.64\n                },\n                {\n                    \"text\":
      \"when everything looks good\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"looks\",\n                    \"term_start_second\": 4,\n                   
      \"segment_end_second\": 4.64,\n                    \"segment_start_second\":
      3.48\n                }\n            ]\n        }\n    ]\n}"
  audioDataList:
  - name: A2
    audioClip: {fileID: 8300000, guid: 86ddba4ef520bc643836519618104e52, type: 3}
  - name: Audio Data 1
    audioClip: {fileID: 8300000, guid: 6a9beee4c857fc94383b9b13bdbfb71e, type: 3}
  isPlaying: 0
  currentTime: 0
  completedEvents: 0
  references:
    version: 2
    RefIds:
    - rid: 6748164214851108866
      type: {class: LoadAvatarAndSetupEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 0
        isActive: 1
        characterId: NPC_Girl_00004
        seed: 4024
    - rid: 6748164214851108867
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedJsonIndex: 0
    - rid: 6748164214851108870
      type: {class: EnterDialogueIdleStateEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 7
        isActive: 1
    - rid: 6748164214851108871
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 8.24
        isActive: 1
        selectedJsonIndex: 1
    - rid: 6748164214851108872
      type: {class: PlayAudioEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedAudioIndex: 0
    - rid: 6748164214851108873
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 16
        isActive: 1
        selectedJsonIndex: 2
    - rid: 6748164214851108874
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 23
        isActive: 1
        selectedJsonIndex: 3
    - rid: 6748164214851108875
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 32
        isActive: 1
        selectedJsonIndex: 4
    - rid: 6748164214851108876
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 40
        isActive: 1
        selectedJsonIndex: 5
--- !u!1 &659749690
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 659749691}
  - component: {fileID: 659749692}
  - component: {fileID: 659749693}
  - component: {fileID: 659749694}
  m_Layer: 0
  m_Name: DEMOLOGIC
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &659749691
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 659749690}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1536027936}
  - {fileID: 1048808601}
  - {fileID: 1376814825}
  - {fileID: 52150864}
  - {fileID: 877673800}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &659749692
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 659749690}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 720dd0e9e9f4d4b358ba04217ab98f9f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  animator: {fileID: 0}
  animationName: 
  layerIndex: 0
  headLayerAvailable:
  - Girl_Approve_02
  - Girl_Diseent_02
  - Idle
  bodyLayerAvailable:
  - Girl_Applaud_01
  - Girl_Approve_01
  - Girl_Approve_02
  - Girl_Diseent_01
  - Girl_Stand_Greeting
  - Girl_Talking
  - Girl_Wait_01
  - Girl_Wait_02
  - Idle
  headLayer: 0
  bodyLayer: 0
  trigger: 0
  checkPathTest: 0
--- !u!114 &659749693
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 659749690}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 63f2840dce548a0469038280e7655d88, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Manager: {fileID: 0}
  trigger: 0
--- !u!114 &659749694
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 659749690}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f9523ea75b34154bb7763a4e54eb4a9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  animationClip: {fileID: 0}
  animator: {fileID: 0}
  autoPlay: 1
  loop: 1
  speed: 1
  showDebugInfo: 1
--- !u!1 &681209243
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 681209245}
  - component: {fileID: 681209244}
  m_Layer: 0
  m_Name: Square
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!212 &681209244
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 681209243}
  m_Enabled: 0
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 0
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &681209245
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 681209243}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.95, z: 4}
  m_LocalScale: {x: 0.55, y: 0.55, z: 0.55}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 1005041326}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!224 &877673800 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
    type: 3}
  m_PrefabInstance: {fileID: 1391032553}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &973902512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 973902516}
  - component: {fileID: 973902515}
  - component: {fileID: 973902514}
  - component: {fileID: 973902513}
  m_Layer: 0
  m_Name: INIT
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &973902513
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973902512}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 973902514}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &973902514
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973902512}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &973902515
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973902512}
  m_CullTransparentMesh: 1
--- !u!224 &973902516
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973902512}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1005777935}
  m_Father: {fileID: 1882667841}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.95}
  m_AnchorMax: {x: 0.5, y: 0.95}
  m_AnchoredPosition: {x: -250, y: 0}
  m_SizeDelta: {x: 160, y: 30}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1005041325
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1005041326}
  - component: {fileID: 1005041329}
  - component: {fileID: 1005041328}
  - component: {fileID: 1005041327}
  m_Layer: 0
  m_Name: Plane
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1005041326
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1005041325}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.8181819, y: 1.8181819, z: 1.8181819}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 681209245}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!64 &1005041327
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1005041325}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1005041328
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1005041325}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 2db75b957eaef104b9492de2cd63ed7d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1005041329
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1005041325}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1005777934
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1005777935}
  - component: {fileID: 1005777937}
  - component: {fileID: 1005777936}
  m_Layer: 0
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1005777935
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1005777934}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 973902516}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1005777936
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1005777934}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: INIT
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4281479730
  m_fontColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 24
  m_fontSizeBase: 24
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1005777937
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1005777934}
  m_CullTransparentMesh: 1
--- !u!1 &1048808598
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1048808601}
  - component: {fileID: 1048808600}
  - component: {fileID: 1048808599}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1048808599
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1048808598}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &1048808600
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1048808598}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &1048808601
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1048808598}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 659749691}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1216392127
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1216392128}
  m_Layer: 0
  m_Name: RoleSlot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1216392128
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1216392127}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -1, z: -0, w: 0}
  m_LocalPosition: {x: 0, y: -1.42, z: 0.4}
  m_LocalScale: {x: 1.4, y: 1.4, z: 1.4}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 1843840315}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: -180, z: 0}
--- !u!1 &1329430620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1329430624}
  - component: {fileID: 1329430623}
  - component: {fileID: 1329430622}
  - component: {fileID: 1329430621}
  m_Layer: 0
  m_Name: Plane
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!64 &1329430621
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1329430620}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1329430622
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1329430620}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 2a964ceb7533cfe4297694dc496782fd, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1329430623
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1329430620}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1329430624
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1329430620}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!1 &1369432280
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1369432281}
  - component: {fileID: 1369432282}
  m_Layer: 0
  m_Name: NewPromptLower
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1369432281
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1369432280}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 176430980}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1369432282
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1369432280}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f8e9d0c1b2a3456789012345678901ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  azureEndpoint: https://japan-east-gpt-01.openai.azure.com/
  deploymentName: gpt-4o-global-01
  apiKey: ********************************
  apiVersion: 2025-01-01-preview
  temperature: 0.7
  maxTokens: 1000
  topP: 0.95
  frequencyPenalty: 0
  presencePenalty: 0
  systemPrompt: 
  userPrompt: 
  apiResponse: "Azure GPT\u54CD\u5E94\uFF1A\n\n{\n   \"segments\": [\n      {\n        
    \"text\": \"Alright champions!\",\n         \"gesture_group\": \"state/default\",\n        
    \"trigger_term\": \"Alright\"\n      },\n      {\n         \"text\": \"I'm Jordan
    Carter, a tennis pro.\",\n         \"gesture_group\": \"PointSelf\",\n        
    \"trigger_term\": \"I'm\"\n      },\n      {\n         \"text\": \"Game on, mate!\",\n        
    \"gesture_group\": \"state/default\",\n         \"trigger_term\": \"Game\"\n     
    },\n      {\n         \"text\": \"I grew up in Sydney, surfing before school
    with dad.\",\n         \"gesture_group\": \"PointSelf\",\n         \"trigger_term\":
    \"I\"\n      },\n      {\n         \"text\": \"What's one sport you really enjoy?\",\n        
    \"gesture_group\": \"PointPlayer\",\n         \"trigger_term\": \"What's\"\n     
    },\n      {\n         \"text\": \"Hi there, I'm Claire Sullivan!\",\n        
    \"gesture_group\": \"PointSelf\",\n         \"trigger_term\": \"I'm\"\n     
    },\n      {\n         \"text\": \"I love digging into stories and finding the
    truth.\",\n         \"gesture_group\": \"PointSelf\",\n         \"trigger_term\":
    \"I\"\n      },\n      {\n         \"text\": \"Newspapers are my world.\",\n        
    \"gesture_group\": \"state/default\",\n         \"trigger_term\": \"Newspapers\"\n     
    },\n      {\n         \"text\": \"What section do you usually read first?\",\n        
    \"gesture_group\": \"PointPlayer\",\n         \"trigger_term\": \"What\"\n     
    },\n      {\n         \"text\": \"Hi there!\",\n         \"gesture_group\": \"state/default\",\n        
    \"trigger_term\": \"Hi\"\n      },\n      {\n         \"text\": \"I'm Julie,
    and I just love chatting about moving experiences.\",\n         \"gesture_group\":
    \"PointSelf\",\n         \"trigger_term\": \"I'm\"\n      },\n      {\n        
    \"text\": \"It's like every move is a new art piece, don't you agree?\",\n        
    \"gesture_group\": \"PointPlayer\",\n         \"trigger_term\": \"don't\"\n     
    }\n   ]\n}\n\n\U0001F4CA \u4F7F\u7528\u7EDF\u8BA1\uFF1A\n\u2022 \u63D0\u793AToken:
    515\n\u2022 \u5B8C\u6210Token: 418\n\u2022 \u603BToken: 933\n\n\u23F1\uFE0F \u8BF7\u6C42\u8017\u65F6\u7EDF\u8BA1\uFF1A\n\u2022
    \u603B\u8017\u65F6: 5.16 \u79D2\n\u2022 \u5F00\u59CB\u65F6\u95F4: 17:31:52.251\n\u2022
    \u7ED3\u675F\u65F6\u95F4: 17:31:57.407\n\u2022 \u6027\u80FD\u8BC4\u7EA7: \U0001F40C
    \u8F83\u6162\n"
  isProcessing: 0
  lastError: 
  lastRequestTime: 5.1557617
  performanceStats: "\u23F1\uFE0F \u8BF7\u6C42\u8017\u65F6\u7EDF\u8BA1\uFF1A\n\u2022
    \u603B\u8017\u65F6: 5.16 \u79D2\n\u2022 \u5F00\u59CB\u65F6\u95F4: 17:31:52.251\n\u2022
    \u7ED3\u675F\u65F6\u95F4: 17:31:57.407\n\u2022 \u6027\u80FD\u8BC4\u7EA7: \U0001F40C
    \u8F83\u6162\n"
--- !u!1 &1376814820
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1376814825}
  - component: {fileID: 1376814824}
  - component: {fileID: 1376814823}
  - component: {fileID: 1376814822}
  - component: {fileID: 1376814826}
  m_Layer: 5
  m_Name: Btns
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &1376814822
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376814820}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1376814823
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376814820}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1376814824
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376814820}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &1376814825
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376814820}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 659749691}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &1376814826
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376814820}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ef0f171a79305e4895df4ac1a5048e1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  animator: {fileID: 0}
  canvas: {fileID: 1376814824}
  buttonPrefab: {fileID: 3554877876909090620, guid: 1b13a9f95997f4c42935d3bab587579a,
    type: 3}
--- !u!1001 &1391032553
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 659749691}
    m_Modifications:
    - target: {fileID: 29691147084492574, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 690106328858333251, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.15
      objectReference: {fileID: 0}
    - target: {fileID: 690106328858333251, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.15
      objectReference: {fileID: 0}
    - target: {fileID: 690106328858333251, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 690106328858333251, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 690106328858333251, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -42.51
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1659090603314932154, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.15
      objectReference: {fileID: 0}
    - target: {fileID: 1659090603314932154, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.15
      objectReference: {fileID: 0}
    - target: {fileID: 1659090603314932154, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 1659090603314932154, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -36.92
      objectReference: {fileID: 0}
    - target: {fileID: 2311652608359721653, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: overrideAnimatorController
      value: 
      objectReference: {fileID: 9100000, guid: e8336b9a01abcd04aa5a0bbf6fd76ab1, type: 2}
    - target: {fileID: 2311652608359721653, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: overrideAnimatorControllerBoy
      value: 
      objectReference: {fileID: 9100000, guid: e8336b9a01abcd04aa5a0bbf6fd76ab1, type: 2}
    - target: {fileID: 2311652608359721653, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: overrideAnimatorControllerGirl
      value: 
      objectReference: {fileID: 9100000, guid: ae07ed78fa44653439bcff14ce7a4088, type: 2}
    - target: {fileID: 4248472966467696051, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_Name
      value: Canvas
      objectReference: {fileID: 0}
    - target: {fileID: 5891070004456200066, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6438431777543716251, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.15
      objectReference: {fileID: 0}
    - target: {fileID: 6438431777543716251, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.15
      objectReference: {fileID: 0}
    - target: {fileID: 6438431777543716251, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 6438431777543716251, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 180.59
      objectReference: {fileID: 0}
    - target: {fileID: 6438431777543716251, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 20.07
      objectReference: {fileID: 0}
    - target: {fileID: 7443188678320172807, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.95
      objectReference: {fileID: 0}
    - target: {fileID: 7443188678320172807, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.95
      objectReference: {fileID: 0}
    - target: {fileID: 7443188678320172807, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 200
      objectReference: {fileID: 0}
    - target: {fileID: 7443188678320172807, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7564253008027992221, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.15
      objectReference: {fileID: 0}
    - target: {fileID: 7564253008027992221, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.15
      objectReference: {fileID: 0}
    - target: {fileID: 7564253008027992221, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 60.299988
      objectReference: {fileID: 0}
    - target: {fileID: 7564253008027992221, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 22.1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects:
    - {fileID: 5891070004456200066, guid: e65127a5b92e9e342abf159977966e37, type: 3}
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 835177990382258471, guid: e65127a5b92e9e342abf159977966e37,
        type: 3}
      insertIndex: 0
      addedObject: {fileID: 2078249612}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e65127a5b92e9e342abf159977966e37, type: 3}
--- !u!1 &1410965053
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1410965056}
  - component: {fileID: 1410965055}
  - component: {fileID: 1410965054}
  - component: {fileID: 1410965057}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1410965054
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1410965053}
  m_Enabled: 1
--- !u!20 &1410965055
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1410965053}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 1.5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1410965056
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1410965053}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1410965057
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1410965053}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: -1
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
  m_TaaSettings:
    quality: 3
    frameInfluence: 0.1
    jitterScale: 1
    mipBias: 0
    varianceClampScale: 0.9
    contrastAdaptiveSharpening: 0
--- !u!1 &1536027934
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1536027936}
  - component: {fileID: 1536027935}
  - component: {fileID: 1536027937}
  m_Layer: 0
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1536027935
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1536027934}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 15bbea814d66e4410be3449ce2010cf4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &1536027936
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1536027934}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.7648801, y: 1.2639111, z: 1.4923368}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 659749691}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1536027937
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1536027934}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ad748c86e77464c67ab304ab2dbec8f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1728179336
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1728179337}
  - component: {fileID: 1728179338}
  m_Layer: 0
  m_Name: A2 1-1-B
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1728179337
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1728179336}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 432577523}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1728179338
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1728179336}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c30edd1459a660b42ae0a8b22efda506, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  eventDataList:
  - rid: 6748164214851108866
  - rid: 6748164214851108867
  - rid: 6748164214851108872
  - rid: 6748164214851108871
  - rid: 6748164214851108873
  - rid: 6748164214851108874
  - rid: 6748164214851108875
  - rid: 6748164214851108876
  - rid: 6748164242022334464
  - rid: 6748164242022334465
  jsonDataList:
  - name: "A1\u5F00\u5934"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Let\u2019s
      go shopping in English!\",\n            \"segments\": [\n                {\n                   
      \"text\": \"Let\u2019s go shopping\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"Let\u2019s\",\n                   
      \"term_start_second\": 0.08,\n                    \"segment_end_second\": 0.96,\n                   
      \"segment_start_second\": 0.08\n                },\n                {\n                   
      \"text\": \"in English\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"English\",\n                    \"term_start_second\":
      1.16,\n                    \"segment_end_second\": 1.68,\n                   
      \"segment_start_second\": 1\n                }\n            ]\n        },\n       
      {\n            \"text\": \"I\u2019m A, you\u2019re B \u2014 repeat after me.\",\n           
      \"segments\": [\n                {\n                    \"text\": \"I\u2019m
      A\",\n                    \"gesture_group\": \"PointSelf\",\n                   
      \"trigger_term\": \"I\u2019m\",\n                    \"term_start_second\":
      2.36,\n                    \"segment_end_second\": 2.92,\n                   
      \"segment_start_second\": 2.36\n                },\n                {\n                   
      \"text\": \"you\u2019re B\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you\u2019re\",\n                    \"term_start_second\":
      3.32,\n                    \"segment_end_second\": 3.8,\n                   
      \"segment_start_second\": 3.32\n                },\n                {\n                   
      \"text\": \"repeat after me\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"repeat\",\n                    \"term_start_second\": 4.52,\n                   
      \"segment_end_second\": 5.24,\n                    \"segment_start_second\":
      4.52\n                }\n            ]\n        }\n    ]\n}"
  - name: "A1\u7B2C\u4E00\u53E5"
    jsonContent: "{\r\n    \"sentences\": [\r\n        {\r\n            \"text\":
      \"Do you go grocery shopping every week, or just when you need something?\",\r\n           
      \"segments\": [\r\n                {\r\n                    \"text\": \"Do
      you go grocery shopping every week\",\r\n                    \"gesture_group\":
      \"PointPlayer\",\r\n                    \"trigger_term\": \"Do\",\r\n                   
      \"term_start_second\": 0.76,\r\n                    \"segment_end_second\":
      3.56,\r\n                    \"segment_start_second\": 0.76\r\n               
      },\r\n                {\r\n                    \"text\": \"or just when you
      need something\",\r\n                    \"gesture_group\": \"state/default\",\r\n                   
      \"trigger_term\": \"need\",\r\n                    \"term_start_second\": 5.58,\r\n                   
      \"segment_end_second\": 6,\r\n                    \"segment_start_second\":
      4.2\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}"
  - name: "A2\u7B2C\u4E8C\u53E5"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"What
      do you usually buy when you go shopping for food?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"What do you usually buy\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"What\",\n                    \"term_start_second\": 0.72,\n                   
      \"segment_end_second\": 1.92,\n                    \"segment_start_second\":
      0.72\n                },\n                {\n                    \"text\":
      \"when you go shopping for food\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"shopping\",\n                   
      \"term_start_second\": 2.64,\n                    \"segment_end_second\": 3,\n                   
      \"segment_start_second\": 2.24\n                }\n            ]\n        }\n   
      ]\n}"
  - name: "A2\u7B2C\u4E09\u53E5"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Do
      you always shop at the same store, or do you try different places?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Do you
      always shop at the same store\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"Do\",\n                    \"term_start_second\": 0.8,\n                   
      \"segment_end_second\": 2.9,\n                    \"segment_start_second\":
      0.8\n                },\n                {\n                    \"text\": \"or
      do you try different places\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"do\",\n                    \"term_start_second\": 3.4,\n                   
      \"segment_end_second\": 5.08,\n                    \"segment_start_second\":
      3.4\n                }\n            ]\n        }\n    ]\n}"
  - name: "A2\u7B2C\u56DB\u53E5"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"That\u2019s
      convenient. Do you go alone or with someone?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"That\u2019s convenient\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"convenient\",\n                    \"term_start_second\": 0.08,\n                   
      \"segment_end_second\": 0.76,\n                    \"segment_start_second\":
      0.08\n                }\n            ]\n        }\n    ]\n}"
  - name: "A2\u7B2C\u4E94\u53E5"
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"That\u2019s
      convenient. Do you go alone or with someone?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"Do you go alone or with
      someone\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"Do\",\n                    \"term_start_second\": 0.28,\n                   
      \"segment_end_second\": 2.44,\n                    \"segment_start_second\":
      0.28\n                }\n            ]\n        }\n    ]\n}"
  - name: JSON Data 6
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"That
      sounds nice. Do you plan meals together while shopping?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"That sounds nice\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"nice\",\n                    \"term_start_second\": 1.36,\n                   
      \"segment_end_second\": 1.68,\n                    \"segment_start_second\":
      0.92\n                },\n                {\n                    \"text\":
      \"Do you plan meals together while shopping\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"Do\",\n                   
      \"term_start_second\": 3.04,\n                    \"segment_end_second\": 5.04,\n                   
      \"segment_start_second\": 3.04\n                }\n            ]\n        }\n   
      ]\n}"
  - name: JSON Data 7
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Yeah,
      it\u2019s easy to buy more than you planned.\",\n            \"segments\":
      [\n                {\n                    \"text\": \"Yeah, it\u2019s easy
      to buy more than you planned\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"easy\",\n                    \"term_start_second\": 1.6,\n                   
      \"segment_end_second\": 2.96,\n                    \"segment_start_second\":
      0.68\n                }\n            ]\n        }\n    ]\n}"
  audioDataList:
  - name: A2
    audioClip: {fileID: 8300000, guid: 28a4d557a1223a44688a3377962b884f, type: 3}
  isPlaying: 0
  currentTime: 0
  completedEvents: 0
  references:
    version: 2
    RefIds:
    - rid: 6748164214851108866
      type: {class: LoadAvatarAndSetupEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 0
        isActive: 1
        characterId: NPC_Boy_00001
        seed: 4024
    - rid: 6748164214851108867
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedJsonIndex: 0
    - rid: 6748164214851108871
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 7
        isActive: 1
        selectedJsonIndex: 1
    - rid: 6748164214851108872
      type: {class: PlayAudioEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedAudioIndex: 0
    - rid: 6748164214851108873
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 17
        isActive: 1
        selectedJsonIndex: 2
    - rid: 6748164214851108874
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 25
        isActive: 1
        selectedJsonIndex: 3
    - rid: 6748164214851108875
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 34
        isActive: 1
        selectedJsonIndex: 4
    - rid: 6748164214851108876
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 36
        isActive: 1
        selectedJsonIndex: 5
    - rid: 6748164242022334464
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 40
        isActive: 1
        selectedJsonIndex: 6
    - rid: 6748164242022334465
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 48
        isActive: 1
        selectedJsonIndex: 7
--- !u!4 &1843840315 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 8473571392009387745, guid: 1c3e95cb6ca83334495c45b9daca67a2,
    type: 3}
  m_PrefabInstance: {fileID: 1971375161}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1882667840
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1882667841}
  - component: {fileID: 1882667844}
  - component: {fileID: 1882667843}
  - component: {fileID: 1882667842}
  m_Layer: 0
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1882667841
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1882667840}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 973902516}
  m_Father: {fileID: 52150864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &1882667842
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1882667840}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1882667843
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1882667840}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1882667844
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1882667840}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1001 &1971375161
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1216392128}
    m_Modifications:
    - target: {fileID: 1500860842918094281, guid: 1c3e95cb6ca83334495c45b9daca67a2,
        type: 3}
      propertyPath: m_Name
      value: sound
      objectReference: {fileID: 0}
    - target: {fileID: 8473571392009387745, guid: 1c3e95cb6ca83334495c45b9daca67a2,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8473571392009387745, guid: 1c3e95cb6ca83334495c45b9daca67a2,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8473571392009387745, guid: 1c3e95cb6ca83334495c45b9daca67a2,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8473571392009387745, guid: 1c3e95cb6ca83334495c45b9daca67a2,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8473571392009387745, guid: 1c3e95cb6ca83334495c45b9daca67a2,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8473571392009387745, guid: 1c3e95cb6ca83334495c45b9daca67a2,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8473571392009387745, guid: 1c3e95cb6ca83334495c45b9daca67a2,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8473571392009387745, guid: 1c3e95cb6ca83334495c45b9daca67a2,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8473571392009387745, guid: 1c3e95cb6ca83334495c45b9daca67a2,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8473571392009387745, guid: 1c3e95cb6ca83334495c45b9daca67a2,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 1c3e95cb6ca83334495c45b9daca67a2, type: 3}
--- !u!1 &2022420740
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2022420741}
  - component: {fileID: 2022420742}
  m_Layer: 0
  m_Name: B1-2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2022420741
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2022420740}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 432577523}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2022420742
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2022420740}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c30edd1459a660b42ae0a8b22efda506, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  eventDataList:
  - rid: 6748164214851108866
  - rid: 6748164214851108867
  - rid: 6748164214851108872
  jsonDataList:
  - name: B1-0
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Talk
      about your weekend like a native.\",\n            \"segments\": [\n               
      {\n                    \"text\": \"Talk about your weekend\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"Talk\",\n                    \"term_start_second\": 0.0,\n                   
      \"segment_end_second\": 0.96,\n                    \"segment_start_second\":
      0.0\n                },\n                {\n                    \"text\": \"like
      a native\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"native\",\n                    \"term_start_second\": 1.3,\n                   
      \"segment_end_second\": 1.48,\n                    \"segment_start_second\":
      1\n                }\n            ]\n        },\n        {\n            \"text\":
      \"I say A, you say B.\",\n            \"segments\": [\n                {\n                   
      \"text\": \"I say A\",\n                    \"gesture_group\": \"PointSelf\",\n                   
      \"trigger_term\": \"I\",\n                    \"term_start_second\": 1.84,\n                   
      \"segment_end_second\": 2.48,\n                    \"segment_start_second\":
      1.84\n                },\n                {\n                    \"text\":
      \"you say B\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you\",\n                    \"term_start_second\": 2.68,\n                   
      \"segment_end_second\": 3.20,\n                    \"segment_start_second\":
      2.68\n                }\n            ]\n        }\n    ]\n}"
  - name: B1-1
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Do
      you already have any plans for this weekend?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"Do you already have any
      plans\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"plans\",\n                    \"term_start_second\": 0.8,\n                   
      \"segment_end_second\": 1,\n                    \"segment_start_second\": 0\n               
      },\n                {\n                    \"text\": \"for this weekend\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"weekend\",\n                    \"term_start_second\": 1.5,\n                   
      \"segment_end_second\": 1.76,\n                    \"segment_start_second\":
      1\n                }\n            ]\n        }\n    ]\n}"
  - name: B1-2
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"That
      sounds really lovely.\",\n            \"segments\": [\n                {\n                   
      \"text\": \"That sounds really lovely\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"sounds\",\n                   
      \"term_start_second\": 0.68,\n                    \"segment_end_second\": 1.48,\n                   
      \"segment_start_second\": 0.56\n                }\n            ]\n        },\n       
      {\n            \"text\": \"Is their house far from where you live?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Is their
      house far\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"Is\",\n                    \"term_start_second\": 2.2,\n                   
      \"segment_end_second\": 2.88,\n                    \"segment_start_second\":
      2.2\n                },\n                {\n                    \"text\": \"from
      where you live\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you\",\n                    \"term_start_second\": 3.24,\n                   
      \"segment_end_second\": 3.56,\n                    \"segment_start_second\":
      2.88\n                }\n            ]\n        }\n    ]\n}"
  - name: B1-3
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Are
      you planning to go there by train, or are you driving this time?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Are you
      planning to go there by train\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"planning\",\n                    \"term_start_second\":
      0.88,\n                    \"segment_end_second\": 2.04,\n                   
      \"segment_start_second\": 0.68\n                },\n                {\n                   
      \"text\": \"or are you driving this time\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"driving\",\n                   
      \"term_start_second\": 2.64,\n                    \"segment_end_second\": 3.32,\n                   
      \"segment_start_second\": 2.04\n                }\n            ]\n        }\n   
      ]\n}"
  - name: B1-4
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Nice.\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Nice\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"Nice\",\n                    \"term_start_second\": 0.72,\n                   
      \"segment_end_second\": 1.12,\n                    \"segment_start_second\":
      0.72\n                }\n            ]\n        },\n        {\n           
      \"text\": \"What do you usually do when you\u2019re there with your family?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"What do
      you usually do\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"do\",\n                    \"term_start_second\": 2.12,\n                   
      \"segment_end_second\": 2.88,\n                    \"segment_start_second\":
      2.12\n                },\n                {\n                    \"text\":
      \"when you\u2019re there with your family\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"there\",\n                   
      \"term_start_second\": 3.16,\n                    \"segment_end_second\": 4,\n                   
      \"segment_start_second\": 2.96\n                }\n            ]\n        }\n   
      ]\n}"
  - name: B1-5
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"That
      sounds peaceful.\",\n            \"segments\": [\n                {\n                   
      \"text\": \"That sounds peaceful\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"peaceful\",\n                   
      \"term_start_second\": 0.96,\n                    \"segment_end_second\": 1.48,\n                   
      \"segment_start_second\": 0.56\n                }\n            ]\n        },\n       
      {\n            \"text\": \"Do you enjoy getting out of the city once in a while?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Do you
      enjoy getting out of the city once in a while\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"enjoy\",\n                   
      \"term_start_second\": 2.8,\n                    \"segment_end_second\": 4.44,\n                   
      \"segment_start_second\": 2.52\n                }\n            ]\n        }\n   
      ]\n}"
  - name: A2-2 Talking about hobbies
    jsonContent: "{\n    \"sentences\": [\n          {\n            \"text\": \"Let\u2019s
      practice everyday English!\",\n            \"segments\": [\n               
      {\n                    \"text\": \"Let\u2019s practice\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"Let\u2019s\",\n                    \"term_start_second\": 0.12,\n                   
      \"segment_end_second\": 1,\n                    \"segment_start_second\": 0.12\n               
      },\n                {\n                    \"text\": \"everyday English\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"everyday\",\n                    \"term_start_second\": 1,\n                   
      \"segment_end_second\": 2.12,\n                    \"segment_start_second\":
      1\n                }\n            ]\n        },\n        {\n            \"text\":
      \"I\u2019ll be A, you\u2019ll be B.\",\n            \"segments\": [\n               
      {\n                    \"text\": \"I\u2019ll be A\",\n                    \"gesture_group\":
      \"PointSelf\",\n                    \"trigger_term\": \"I'll\",\n                   
      \"term_start_second\": 2.84,\n                    \"segment_end_second\":3.6,\n                   
      \"segment_start_second\": 2.84\n                },\n                {\n                   
      \"text\": \"you\u2019ll be B\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you'll\",\n                    \"term_start_second\": 4.24,\n                   
      \"segment_end_second\":5,\n                    \"segment_start_second\": 4.24\n               
      }\n            ]\n        },\n        {\n            \"text\": \"So, what do
      you usually do in your free time?\",\n            \"segments\": [\n               
      {\n                    \"text\": \"So, what do you usually do\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"do\",\n                    \"term_start_second\": 7.56,\n                   
      \"segment_end_second\": 8.44,\n                    \"segment_start_second\":
      6.2\n                },\n                {\n                    \"text\": \"in
      your free time\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"free\",\n                    \"term_start_second\": 8.8,\n                   
      \"segment_end_second\": 9.32,\n                    \"segment_start_second\":
      8.48\n                }\n            ]\n        },\n        {\n           
      \"text\": \"That\u2019s cool!\",\n            \"segments\": [\n               
      {\n                    \"text\": \"That\u2019s cool\",\n                   
      \"gesture_group\": \"emphasis/contrast\",\n                    \"trigger_term\":
      \"cool\",\n                    \"term_start_second\": 12.68,\n                   
      \"segment_end_second\": 13.32,\n                    \"segment_start_second\":
      12.68\n                }\n            ]\n        },\n        {\n           
      \"text\": \"What kind of music do you listen to?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"What kind of music\",\n                   
      \"gesture_group\": \"PointPlayer\",\n                    \"trigger_term\":
      \"music\",\n                    \"term_start_second\": 14.24,\n                   
      \"segment_end_second\": 15.08,\n                    \"segment_start_second\":
      14.24\n                },\n                {\n                    \"text\":
      \"do you listen to\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"listen\",\n                    \"term_start_second\":15.08,\n                   
      \"segment_end_second\": 15.82,\n                    \"segment_start_second\":
      15.08\n                }\n            ]\n        },\n        {\n           
      \"text\": \"Do you draw just for fun, or do you take classes?\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Do you
      draw just for fun\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"draw\",\n                    \"term_start_second\": 20.32,\n                   
      \"segment_end_second\": 21.48,\n                    \"segment_start_second\":
      20.32\n                },\n                {\n                    \"text\":
      \"or do you take classes\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"take\",\n                    \"term_start_second\": 22.24,\n                   
      \"segment_end_second\": 23,\n                    \"segment_start_second\":
      21.88\n                }\n            ]\n        },\n        {\n           
      \"text\": \"That\u2019s great.\",\n            \"segments\": [\n               
      {\n                    \"text\": \"That\u2019s great\",\n                   
      \"gesture_group\": \"emphasis/contrast\",\n                    \"trigger_term\":
      \"great\",\n                    \"term_start_second\": 27.64,\n                   
      \"segment_end_second\": 28.2,\n                    \"segment_start_second\":
      27.64\n                }\n            ]\n        },\n        {\n           
      \"text\": \"Have you ever shared your drawings online?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"Have you ever shared
      your drawings\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"shared\",\n                    \"term_start_second\": 29.48,\n                   
      \"segment_end_second\":30.84,\n                    \"segment_start_second\":
      29\n                }\n            ]\n        },\n          {\n           
      \"text\": \"Practice with our AI avatar every day to speak more confidently.\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Practice
      with our AI avatar every day\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"Practice\",\n                    \"term_start_second\":
      34.68,\n                    \"segment_end_second\": 37.04,\n                   
      \"segment_start_second\": 34.68\n                },\n                {\n                   
      \"text\": \"to speak more confidently\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"speak\",\n                   
      \"term_start_second\": 37.08,\n                    \"segment_end_second\":
      38.36,\n                    \"segment_start_second\": 37.08\n               
      }\n            ]\n        },\n        {\n            \"text\": \"Hit follow
      for more beginner-friendly English!\",\n            \"segments\": [\n               
      {\n                    \"text\": \"Hit follow\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"Hit\",\n                   
      \"term_start_second\": 39.28,\n                    \"segment_end_second\":
      39.76,\n                    \"segment_start_second\": 39.28\n               
      },\n                {\n                    \"text\": \"for more beginner-friendly
      English\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"English\",\n                    \"term_start_second\":
      39.8,\n                    \"segment_end_second\": 41.44,\n                   
      \"segment_start_second\": 39.8\n                }\n            ]\n        }\n   
      ]\n}"
  - name: B1-2 Girl
    jsonContent: "{\n    \"sentences\": [\n        {\n            \"text\": \"Practice
      asking for directions in English!\",\n            \"segments\": [\n               
      {\n                    \"text\": \"Practice asking for directions in English\",\n                   
      \"gesture_group\": \"state/default\",\n                    \"trigger_term\":
      \"Practice\",\n                    \"term_start_second\": 0.04,\n                   
      \"segment_end_second\": 2.8,\n                    \"segment_start_second\":
      0.04\n                }\n            ]\n        },\n        {\n           
      \"text\": \"I\u2019ll be A, you\u2019ll be B.\",\n            \"segments\":
      [\n                {\n                    \"text\": \"I\u2019ll be A\",\n                   
      \"gesture_group\": \"PointSelf\",\n                    \"trigger_term\": \"I\u2019ll\",\n                   
      \"term_start_second\": 3.12,\n                    \"segment_end_second\": 4,\n                   
      \"segment_start_second\": 3.12\n                },\n                {\n                   
      \"text\": \"you\u2019ll be B\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"you'll\",\n                    \"term_start_second\": 4.36,\n                   
      \"segment_end_second\": 5.08,\n                    \"segment_start_second\":
      4.36\n                }\n            ]\n        },\n        {\n           
      \"text\": \"Excuse me, are you looking for something?\",\n            \"segments\":
      [\n                {\n                    \"text\": \"Excuse me\",\n                   
      \"gesture_group\": \"Greeting\",\n                    \"trigger_term\": \"Excuse\",\n                   
      \"term_start_second\": 5.68,\n                    \"segment_end_second\": 6.32,\n                   
      \"segment_start_second\": 5.68\n                },\n                {\n                   
      \"text\": \"are you looking for something\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"you\",\n                   
      \"term_start_second\":6.46,\n                    \"segment_end_second\": 7.6,\n                   
      \"segment_start_second\": 6.36\n                }\n            ]\n        },\n       
      {\n            \"text\": \"Yes, just walk straight for two blocks, then turn
      left at the traffic light.\",\n            \"segments\": [\n               
      {\n                    \"text\": \"Yes\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"Yes\",\n                   
      \"term_start_second\": 11.8,\n                    \"segment_end_second\": 12.4,\n                   
      \"segment_start_second\": 11.8\n                },\n                {\n                   
      \"text\": \"just walk straight for two blocks\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"walk\",\n                   
      \"term_start_second\": 12.64,\n                    \"segment_end_second\":
      14.32,\n                    \"segment_start_second\": 12.64\n               
      },\n                {\n                    \"text\": \"then turn left at the
      traffic light\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"turn\",\n                    \"term_start_second\": 14.52,\n                   
      \"segment_end_second\": 15.88,\n                    \"segment_start_second\":
      14.36\n                }\n            ]\n        },\n        {\n           
      \"text\": \"About ten minutes. You\u2019ll see a big park on your right before
      you get there.\",\n            \"segments\": [\n                {\n                   
      \"text\": \"About ten minutes\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"About\",\n                    \"term_start_second\":19.04,\n                   
      \"segment_end_second\": 20.16,\n                    \"segment_start_second\":
      19.04\n                },\n                {\n                    \"text\":
      \"You\u2019ll see a big park on your right\",\n                    \"gesture_group\":
      \"PointPlayer\",\n                    \"trigger_term\": \"You'll\",\n                   
      \"term_start_second\": 20.2,\n                    \"segment_end_second\": 21.6,\n                   
      \"segment_start_second\": 20.2\n                },\n                {\n                   
      \"text\": \"before you get there\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"before\",\n                   
      \"term_start_second\": 21.64,\n                    \"segment_end_second\":
      22.56,\n                    \"segment_start_second\": 21.64\n               
      }\n            ]\n        },\n        {\n            \"text\": \"No problem.
      Enjoy your visit!\",\n            \"segments\": [\n                {\n                   
      \"text\": \"No problem\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"problem\",\n                    \"term_start_second\":
      27.64,\n                    \"segment_end_second\": 28.2,\n                   
      \"segment_start_second\": 27.4\n                },\n                {\n                   
      \"text\": \"Enjoy your visit\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"Enjoy\",\n                    \"term_start_second\": 28.48,\n                   
      \"segment_end_second\": 29.32,\n                    \"segment_start_second\":
      28.48\n                }\n            ]\n        },\n          {\n           
      \"text\": \"Practice real conversation with our AI avatar every day.\",\n           
      \"segments\": [\n                {\n                    \"text\": \"Practice
      real conversation\",\n                    \"gesture_group\": \"state/default\",\n                   
      \"trigger_term\": \"Practice\",\n                    \"term_start_second\":
      32.48,\n                    \"segment_end_second\": 34.2,\n                   
      \"segment_start_second\": 32.48\n                },\n                {\n                   
      \"text\": \"with our AI avatar every day\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"avatar\",\n                   
      \"term_start_second\": 35.2,\n                    \"segment_end_second\": 36.36,\n                   
      \"segment_start_second\": 34.24\n                }\n            ]\n       
      },\n        {\n            \"text\": \"Hit follow for more speaking drills
      like this!\",\n            \"segments\": [\n                {\n                   
      \"text\": \"Hit follow\",\n                    \"gesture_group\": \"PointPlayer\",\n                   
      \"trigger_term\": \"Hit\",\n                    \"term_start_second\": 36.8,\n                   
      \"segment_end_second\": 37.32,\n                    \"segment_start_second\":
      36.8\n                },\n                {\n                    \"text\":
      \"for more speaking drills like this\",\n                    \"gesture_group\":
      \"state/default\",\n                    \"trigger_term\": \"more\",\n                   
      \"term_start_second\": 37.56,\n                    \"segment_end_second\":
      39.32,\n                    \"segment_start_second\": 37.36\n               
      }\n            ]\n        }\n    ]\n}"
  audioDataList:
  - name: A2
    audioClip: {fileID: 8300000, guid: 35404dcb8d59b6f49b125ec3f4507fc1, type: 3}
  - name: B1
    audioClip: {fileID: 8300000, guid: 9369c44b503eb4b4781aa9d6894fd7d1, type: 3}
  isPlaying: 0
  currentTime: 0
  completedEvents: 0
  references:
    version: 2
    RefIds:
    - rid: 6748164214851108866
      type: {class: LoadAvatarAndSetupEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 0
        isActive: 1
        characterId: NPC_Girl_00004
        seed: 4024
    - rid: 6748164214851108867
      type: {class: ProcessDialogueEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedJsonIndex: 7
    - rid: 6748164214851108872
      type: {class: PlayAudioEventData, ns: ZTest, asm: Assembly-CSharp}
      data:
        startTime: 1
        isActive: 1
        selectedAudioIndex: 1
--- !u!1 &2078249611
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2078249612}
  - component: {fileID: 2078249614}
  - component: {fileID: 2078249613}
  m_Layer: 5
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &2078249612
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2078249611}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 877673800}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2078249613
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2078249611}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: d1ec4cbecdcbd1242b532b60e4d7d1db, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2078249614
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2078249611}
  m_CullTransparentMesh: 1
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 1410965056}
  - {fileID: 255944804}
  - {fileID: 659749691}
  - {fileID: 1216392128}
  - {fileID: 681209245}
  - {fileID: 176430980}
  - {fileID: 432577523}
  - {fileID: 1329430624}

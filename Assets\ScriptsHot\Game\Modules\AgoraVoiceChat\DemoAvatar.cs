///
/// 临时方案：后面在改用通用的 avatar组件做
/// 这部分逻辑是onboardingAvatar的翻版
/// 

using System;
using AnimationSystem;
using FairyGUI;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Explore.UI;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using UIBind.Explore.Item;
using UnityEngine;


namespace ScriptsHot.Game.Modules.AgoraRtc
{
    public class DemoAvatar
    {
        private GLoader _loader3d;
        private RenderTexture _renderTexture;
        private Camera _modelCamera;
        private GameObject _modelInstance;
        private Level _gameScene;
        private int _layer = 1;
        private DialogManager ModelDialogManager;

        public void SetLoader(GLoader value)
        {
            _loader3d = value;
        }

        public void SetVisible(bool value)
        {
            _loader3d.visible = value;
        }
        
        public async global::System.Threading.Tasks.Task<GameObject> SetModel(Level lv,long entityId,long newAvatarId,string bgId)
        {
            _gameScene = lv;

            _modelCamera = CameraPool.GetCamera(1);
            _renderTexture =  RenderTexturePool.GetRenderTexture(); 
            _modelCamera.targetTexture = _renderTexture;
            _modelCamera.enabled = true;
            _modelCamera.name = entityId.ToString() + "_" + _layer;

            _loader3d.texture = new NTexture(_renderTexture);
            _loader3d.align = AlignType.Center;
            _loader3d.verticalAlign = VertAlignType.Middle;
            _loader3d.SetPivot(0f, 0f, true);

            _loader3d.image.position = new Vector2(0, 200);
            _loader3d.image.scale = new Vector2(1.00f, 1.00f);
            
            _loader3d.x = (GRoot.inst.width - _loader3d.width) / 2;
            _loader3d.y = GRoot.inst.height - _loader3d.height + ExploreConst.ScreenOffsetHeight;  

            _modelInstance = ModelPool.GetModel(newAvatarId, null);
            if (_modelInstance == null)
            {
                _modelInstance = await Load3DModel(newAvatarId);
            }

            _modelInstance.name = string.Concat(entityId, "_", newAvatarId, "_", _layer);
            _modelInstance.transform.SetParent(GameObject.Find("RenderTextureContainer").transform, false);
            _modelInstance.transform.localPosition = Vector3.zero;
            _modelInstance.transform.localRotation = Quaternion.Euler(0, 180, 0);
            _modelInstance.transform.localScale = Vector3.one;
            
            if (GetGenderById(newAvatarId) == 0)
            {
                _modelInstance.transform.localPosition = new Vector3(-0.027F, 0.1F, 0);
                _modelInstance.transform.localScale = new Vector3(0.95F,0.95F,0.95F);
            }
            LightManager.Instance.LoadLightForItem(bgId,_modelInstance,LayerMask.GetMask("RT" + _layer));
            ObjectUtils.SetLayer(_modelInstance, LayerMask.NameToLayer("RT" + _layer));
            _modelInstance.SetActive(true);

            return _modelInstance;

        }

        private AudioStreamPlayer _asp=null;
        /// <summary>
        /// 绑定 avatar 口型, 只触发一次就行
        /// </summary>
        public void UpdateAudioSource()
        {
            if (_modelInstance)
            {
                //ModelDialogManager = _modelInstance.GetComponentInChildren<DialogManager>();
                GAvatarCtrl avatarCtrl = _modelInstance.GetComponent<GAvatarCtrl>();
                _asp = _modelInstance.GetComponent<AudioStreamPlayer>();
                if (_asp == null)
                {
                    Debug.LogError("AudioStreamPlayer 赋值层级有误");
                } 

                GSoundManager.instance.SetCurrAvatarTTS(avatarCtrl.audioSource);
         
            }
            else
            {
                //ModelDialogManager = null;
                Debug.LogError("UpdateAudioSource 有可能错了");
                AudioSource audioSource = GSoundManager.instance.GetChannel("TTS");
                GSoundManager.instance.SetCurrAvatarTTS(audioSource);
            }
        }
        
        /// <summary>
        /// 绑定 avatar 口型
        /// </summary>
        public void StopAudioSource()
        {
            GSoundManager.instance.StopAvatarTTS();
        }
        
        private int GetGenderById(long avatarId)
        {
            //女性1,男性0,杂种2
            if (_gameScene.GetComponent<UnitComponent>().GetStyleNameByAvatarId(avatarId).Contains("Girl"))
            {
                return 1;
            }
            else if (_gameScene.GetComponent<UnitComponent>().GetStyleNameByAvatarId(avatarId).Contains("Boy"))
            {
                return 0;
            }
            else return 2;
        }
        
        private async global::System.Threading.Tasks.Task<GameObject> Load3DModel(long avatarID)
        {
            var styleName = _gameScene.GetComponent<UnitComponent>().GetStyleNameByAvatarId(avatarID);
            var modelGo = await _gameScene.GetComponent<AvatarComponent>().avatarLoader.LoadNAvatar(styleName,null,true);
            
            SceneController sceneController = ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene);
            
            //加载GameObject
            GameObject parentObject = sceneController.GetCharacterGameObject(ModelPreloader.Instance.GetCharacterGOPath());
            if (modelGo != null)
            {
                modelGo.transform.SetParent(parentObject.transform);
                modelGo.transform.localPosition = Vector3.zero;
                parentObject.transform.SetParent(GameObject.Find("RenderTextureContainer").transform);
                parentObject.transform.localPosition = Vector3.zero;
                parentObject.SetActive(true);
                
                PlayModelToIdle(modelGo);
                
                GAvatarCtrl avatarCtrl = ModelPreloader.Instance.GetComponentOrAdd<GAvatarCtrl>(parentObject);
                //1初始化｜添加 口型插件
                ModelPreloader.Instance.InitLipSyncPlugin(avatarCtrl);
                //2口型插件相关绑定
                ModelPreloader.Instance.MatchLipSyncToHeadNode(modelGo, styleName, avatarCtrl);
            }
            
            return parentObject;
        } 

        private void PlayModelToIdle(GameObject model)
        {
            if (model == null)
            {
                return;
            }

            var animator = model.GetComponentInChildren<Animator>();
            if (animator == null)
            {
                return;
            }

            animator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
            animator.Play("Idle");
        }

        //需要在avatar updateAudioSource之后使用
        public void StartAudio()
        {
            VFDebug.Log("soundManager StartAudio");
            //AudioStreamPlayer sP = GSoundManager.instance.AudioStreamPlayer;
            if (_asp == null)
            {
                VFDebug.LogError("soundManager StartAudio but _asp==null");
            }

            this._asp?.Begin(0, OnAvatarAudioEnd);
            
            // 因为不涉及 tts的recordid
            // if (sP)
            // {
            //     sP.Begin(info.tts_record_id,this.CurNetStrategy.StreamAudioPlayOver);
            //    
            //     // VFDebug.LogError("音频流开始  --  缓存");
            //     sP.StartAudio();
            //     bool isLast = false;//_controller.Model.GetStreamTTsReadyState(info.tts_record_id);
            //     sP.AddAudioData(_controller.Model.GetStreamAudioBytes(info.tts_record_id),isLast);
            //     
            // }
        }
        
        public void StopAudio()
        {
            //AudioStreamPlayer sP = GSoundManager.instance.AudioStreamPlayer;
            if (_asp == null)
            {
                VFDebug.LogError("soundManager StopAudio but _asp==null");
            }

            this._asp?.StopAudio();
        }

        private void OnAvatarAudioEnd(long recordID)
        {
            Debug.Log("OnAvatarAudioEnd "+recordID );
        }
        
    }
}
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Settlement
{
    public partial class CompLanguageItem : UIBindT
    {
        public override string pkgName => "Settlement";
        public override string comName => "CompLanguageItem";

        public Controller ctrlBack;
        public GComponent tfPoint;
        public GComponent tfExplain;
        public GTextField tfTemp;
        public GGraph tipPosFlag;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrlBack = com.GetControllerAt(0);
            tfPoint = (GComponent)com.GetChildAt(2);
            tfExplain = (GComponent)com.GetChildAt(3);
            tfTemp = (GTextField)com.GetChildAt(4);
            tipPosFlag = (GGraph)com.GetChildAt(5);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrlBack = null;
            tfPoint = null;
            tfExplain = null;
            tfTemp = null;
            tipPosFlag = null;
        }
    }
}
﻿/*
 ****************************************************
 * 作者：ZhangXiaoWu
 * 创建时间：2024/04/09 16:10:51 星期二
 * 功能：Nothing
 ****************************************************
 */

using System;
using System.Collections.Generic;
using System.Linq;
using CommonUI;
using DG.Tweening;
using FairyGUI;
using Msg.ai_recommend;
using Msg.basic;
using Msg.dialog_task;
using Msg.recommend;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Shop;
using Spine.Unity;
using UIBind.RecommendCard;
using UnityEngine;

public class RecommendCardUI : BaseUI<RecommendCardPanel>
{
    public RecommendCardUI(string name) : base(name) { }
    public override string uiLayer => UILayerConsts.Home;
    protected override bool isFullScreen => true;
    private RecommendCardModel _recommendCardModel => GetModel<RecommendCardModel>(ModelConsts.RecommendCard);
    private ChatController _chatController => GetController<ChatController>(ModelConsts.Chat);
    private ChatModel _chatModel => GetModel<ChatModel>(ModelConsts.Chat);
    private MainModel _MainModel => this.GetModel<MainModel>(ModelConsts.Main);
    public override EUIDeleteRule deleteRule => EUIDeleteRule.Never;
    private RecommendCardController _recommendCardController => GetController<RecommendCardController>(ModelConsts.RecommendCard);

    private CurrencyController _currencyController => GetController<CurrencyController>(ModelConsts.CurrencyController);
    private List<KeyValuePair<long,PB_Recommend_Task_Resp>> _taskList = new List<KeyValuePair<long, PB_Recommend_Task_Resp>>();
    
    private int _lastIndex = 0;
    private int _showIndex = 0;
    private Vector3 worldPos, worldPos1;
    private Transform originTransform, endTransform;
    private float speed = 0.65f;

    public void SetVisible(bool flag)
    {
        ui.com.visible = flag;
    }

    protected override void OnInit(GComponent uiCom)
    {
        ui.ingMesk.visible = false;
        _recommendCardController.SendRecommendInfo();
        AddUIEvent(ui.btnClose.onClick, CloseList);
        AddUIEvent(ui.btnCloseBig.onClick, CloseList);
        AddUIEvent(ui.btnEnter.onClick, OpenListForClick);
        ui.ctrlOpen.selectedPage = "close";
        ui.listCard.itemRenderer = RenderListCardItem;
        ui.listCard.SetVirtual();
        ui.listCard.scrollPane.decelerationRate = speed;
        ui.listCard.scrollPane.onScroll.Add(DoSpecialEffect);
        ui.listCard.scrollPane.onScrollEnd.Add(DoSpecialEffectEnd);
        ui.SpineEniter.spawnSpineBone = true;
    }

    private void OpenListForClick()
    {
        if (_recommendCardModel.GetTaskList() == null || _recommendCardModel.GetTaskList().Count <= 0)
        {
            DealErrorData();
            return;
        }
        DoSpecialEffect();
        DoSpecialEffectEnd();
        _recommendCardController.SendInTaskAvatar(true);//任务中
        ui.SpineEniter.playing = true;

        ui.ctrlOpen.selectedPage = "spine";
        ui.SpineEniter.spineAnimation.AnimationState.SetAnimation(
            0, "2", false).Complete += (a) =>
            {
                ui.ctrlOpen.selectedPage = "open";
                ui.aniEnter.Play();
                SetUI(true);
            };

        //新埋点：点击左下角任务卡片
        DataDotClickTaskCard dot = new DataDotClickTaskCard();
        DataDotMgr.Collect(dot);
    }

    private void OpenList()
    {
        if (_recommendCardModel.GetTaskList() == null || _recommendCardModel.GetTaskList().Count <= 0 )
        {
            DealErrorData();
            return;
        }
        DoSpecialEffect(); 
        DoSpecialEffectEnd(); 
        _recommendCardController.SendInTaskAvatar(true);//任务中
        ui.SpineEniter.playing = true;
        if (ui.ctrlOpen.selectedPage == "close")
        {
            ui.ctrlOpen.selectedPage = "spine";
            ui.SpineEniter.spineAnimation.AnimationState.SetAnimation(
                0, "2", false).Complete += (a) =>
            {
                ui.ctrlOpen.selectedPage = "open";
                ui.aniEnter.Play();
                SetUI(true);
            };   
        }
        //新埋点：点击左下角任务卡片
        DataDotClickTaskCard dot = new DataDotClickTaskCard();
        DataDotMgr.Collect(dot);
    }
    
    private void DealErrorData()
    {
        DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
        dataDotCutApiCase.Api_address = "SC_RecommendTaskAck";
        dataDotCutApiCase.Cut_mode = 2;
        DataDotMgr.Collect(dataDotCutApiCase);
        //点击返回出弹窗
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_reviewquestion_tips", () =>
        {
            _recommendCardController.SendRecommendInfo();
        }, null, 1, null, "chat_btn_cancel");
    }
    
    private void CloseList()
    {
        ui.ctrlOpen.selectedPage = "close";
        _recommendCardController.SendInTaskAvatar(false);
        ui.SpineEniter.playing = true;
        ui.SpineEniter.spineAnimation.AnimationState.SetAnimation(
            0, "3", false).Complete += (a) =>
        {
            ui.SpineEniter.animationName = "1";
            ui.SpineEniter.playing = true;
            ui.SpineEniter.loop = true;
        };
    }

    private void DoSpecialEffectEnd()
    {
        var scrollPos = ui.listCard.scrollPane.posX;
        var itemPos = ui.listCard.width + ui.listCard.columnGap;
        if (_lastIndex >=  _taskList.Count)
        {
            _lastIndex = _taskList.Count - 1;
        }
        if (_lastIndex!=(int)Mathf.Round(scrollPos / itemPos))
        {
            _recommendCardController.SendRecommendTaskTag();
            //新埋点：任务卡片切换
            DataDotClickTaskCardSwitch dot = new DataDotClickTaskCardSwitch();
            dot.Avatar_id = _taskList[_lastIndex].Value.avatar_id;
            dot.Task_id = _taskList[_lastIndex].Value.task_id;
            DataDotMgr.Collect(dot);
        }
        _lastIndex = (int)Mathf.Round(scrollPos / itemPos);
        if (_lastIndex >=  _taskList.Count)
        {
            _lastIndex = _taskList.Count - 1;
        }
        if (ui.listCard.numItems - 4 == _lastIndex)
        {
            _recommendCardController.SendRecommendInfo();
            ui.ingMesk.visible = true;
        }
        _recommendCardModel.SetCurTaskID(_taskList[_lastIndex].Value.task_id);
        

        ui.tfFlowInfo.text = _taskList[_lastIndex].Value.recommend_info;
        if (_taskList[_lastIndex].Value.recommend_info =="")
        {
            ui.tfFlowInfo.visible = false;
            ui.imgFlowInfoBG.visible = false;
            ui.imgFlowInfoArrow.visible = false;
        }
        else
        {
            ui.tfFlowInfo.visible = true;
            ui.imgFlowInfoBG.visible = true;
            ui.imgFlowInfoArrow.visible = true;
        }

    }

    private void DoSpecialEffect()
    {   
        float midX = ui.listCard.scrollPane.posX + ui.listCard.viewWidth / 2;
        int cnt = ui.listCard.numChildren;
        for (int i = 0; i < cnt; i++)
        {
            GObject obj = ui.listCard.GetChildAt(i);
            obj.SetScale(0.9f, 0.9f);
            float dist = Mathf.Abs(midX - obj.x - obj.width / 2);
            if (dist > obj.width) //no intersection
            {
                obj.SetScale(0.9f, 0.9f);
                obj.asCom.GetChild("imgMesk").asImage.alpha = 0.8f;
            }
            else
            {
                float ss = 0.9f + (1 - dist / obj.width)* 0.1f ;
                obj.SetScale(ss, ss);
                obj.asCom.GetChild("imgMesk").asImage.alpha = 0.8f + (1 - dist / obj.width)* -0.8f ;
            }
        }
    }

    protected override void OnShow()
    {
        Spine();
    }

    protected override void OnHide()
    {
        
    }
    
    
    //===============================UI=========================

    private void Spine()
    {
        Vector2 screenPos = ui.listCard.LocalToGlobal(Vector2.zero);
        screenPos.y = Screen.height - screenPos.y; 
        worldPos = StageCamera.main.ScreenToWorldPoint(screenPos);
        Vector2 screenPos1 = ui.btnEnter.LocalToGlobal(Vector2.zero);
        screenPos1.y = Screen.height - screenPos1.y; 
        worldPos1 = StageCamera.main.ScreenToWorldPoint(screenPos1);
        endTransform = ObjectUtils.FindChildRecursion(this.ui.SpineEniter.displayObject.gameObject.transform, "end");
        originTransform = ObjectUtils.FindChildRecursion(this.ui.SpineEniter.displayObject.gameObject.transform, "origin");
        endTransform.GetComponent<SkeletonUtilityBone>().mode = SkeletonUtilityBone.Mode.Override;
        originTransform.GetComponent<SkeletonUtilityBone>().mode = SkeletonUtilityBone.Mode.Override;
        TimerManager.instance.RegisterNextFrame(SpineSetPos);
    }

    private void SpineSetPos(int count)
    {
        endTransform.gameObject.transform.position =  worldPos;
        originTransform.gameObject.transform.position =  worldPos1;
        if (_recommendCardModel.isOpen)
        {
            return;
        }
        ui.SpineEniter.animationName = "1";
        Debug.Log("SpineSetPosSpineSetPos");
        ui.SpineEniter.playing = true;
        ui.SpineEniter.loop = true;
    }

    private void SetUI(bool isNew = false)
    {
        if (_recommendCardModel.GetTaskList() == null || _recommendCardModel.GetTaskList().Count()<=0)
        {
            return;
        }
        _taskList = _recommendCardModel.GetTaskList();
        ui.listCard.numItems = _taskList.Count;
        ui.ingMesk.visible = false;
        if (isNew)
        {
            ui.listCard.ScrollToView(_lastIndex);
        }
        else
        {
            ui.listCard.ScrollToView(_showIndex);//滑动更新
        }
        _showIndex = 2;
        if (_recommendCardModel.IsJump)//跳转下一章
        {
            var seq = DOTween.Sequence();
            seq.AppendInterval(0.3f * _recommendCardModel.JumpNum);
            seq.AppendCallback(() =>
            {
                ui.listCard.ScrollToView(_lastIndex + 1 ,true);
            });
            _recommendCardModel.SetJump(false);
            _recommendCardModel.SetJumpNum(0);
        }
    }

    private void RenderListCardItem(int index, GObject gObject)
    {
        if (_taskList == null) return;
        KeyValuePair<long, PB_Recommend_Task_Resp> data = _taskList[index];
        PB_Recommend_Task_Resp cardData = data.Value;
        RecomTaskCardItemCom item = new RecomTaskCardItemCom();
        item.Construct(gObject.asCom);
        var tfExt = item.tfExtTitle as TextFieldExtension;
        tfExt.Reset();
        
        tfExt.SetMaxRow(2);
        
        tfExt.SetNewFont(FontCfg.DinNextMedium,Color.white, 40);

        tfExt.AppendContent(new RichContent()
        {
            Color = Color.white,
            Content = cardData.task_title,
            IsLast = true,
            Type = RichContentType.Text
        });
        tfExt.Display(ShowMode.Normal);

        if (tfExt.IsBreak)
        {
            item.btnOpenInfo.visible = true;
            item.imgDown.visible = true;
            var tfExt2 = item.tfExtTitleInfo as TextFieldExtension;
            tfExt2.Reset();
      
            tfExt2.SetNewFont(FontCfg.DinNextMedium,Color.white, 40);

            tfExt2.SetMaxRow(8);
            tfExt2.AppendContent(new RichContent()
            {
                Color = Color.white,
                Content = cardData.task_title,
                IsLast = true,
                Type = RichContentType.Text
            });
            tfExt2.Display(ShowMode.Normal);
        }
        else
        {
            item.btnOpenInfo.visible = false;
            item.imgDown.visible = false;
        }

        item.comTag1.ctrlState.selectedIndex = (int)cardData.level;
        
        item.comTag.ctrlState.selectedIndex = (int)cardData.level;
        
        // item.comTag.loaderBg.url = ResUtils.GetIconPath(cardData.level.ToString());
        // var format = string.Format(I18N.inst.UiStr("ui_recommend_Mins"),MathF.Floor(data.Value.spend_time/60));
        item.comTag.tfTime.SetKeyArgs("ui_recommend_Mins",MathF.Floor(data.Value.spend_time/60));
        item.comTag1.tfTime.SetKeyArgs("ui_recommend_Mins",MathF.Floor(data.Value.spend_time/60));

        item.comTag.tfJob.text = cardData.tag_name;  
        item.comTag1.tfJob.text = cardData.tag_name;

        if (GResManager.instance.CheckLocationValid(ResUtils.GetIconPath($"Occupation/{cardData.tag_name}")))
        {
            item.comTag.loaderIcon.url = ResUtils.GetIconPath($"Occupation/{cardData.tag_name}");
            item.comTag1.loaderIcon.url = ResUtils.GetIconPath($"Occupation/{cardData.tag_name}");
        }
        else
        {
            item.comTag.loaderIcon.url = ResUtils.GetIconPath("Occupation/Other");
            item.comTag1.loaderIcon.url = ResUtils.GetIconPath("Occupation/Other");
        }

        item.comTag.grp.SetBoundsChangedFlag();
        item.comTag.grp.EnsureBoundsCorrect();
        item.comTag1.grp.SetBoundsChangedFlag();
        item.comTag1.grp.EnsureBoundsCorrect();
        this.RefreshPostion(item);
        item.loaderBgImg.url = cardData.task_icon_url;
        item.btnLeft.data = cardData;
        item.btnRight.data = cardData;
        item.btnCloseInfo.data = item;
        item.btnOpenInfo.data = item;
        AddUIEvent(item.btnLeft.onClick,OnBtnLeftClick);
        AddUIEvent(item.btnRight.onClick,OnBtnRightClick);
        AddUIEvent(item.btnCloseInfo.onClick,OnBtnPackUp );
        AddUIEvent(item.btnOpenInfo.onClick,OnBtnUnFold);
        item.listInfo.SetVirtual();
        item.listInfo.itemRenderer = (i, o) =>
        {
            List<PB_TaskKeyPoint> taskKey = new List<PB_TaskKeyPoint>();
            PB_TaskKeyPoint one = new PB_TaskKeyPoint()
            {
                foreign_lang_word = I18N.inst.MoStr("ui_recommend_title"),
                first_lang_word =  data.Value.task_desc,
            };
            taskKey.Add(one);
            PB_TaskKeyPoint two = new PB_TaskKeyPoint()
            {
                foreign_lang_word = I18N.inst.MoStr("ui_recommend_title1"),
                first_lang_word =  data.Value.task_desc,
            };
            taskKey.Add(two);
            taskKey.AddRange(cardData.key_points);
            CardInfo(i, o, taskKey.ToList());
        };
        item.listInfo.scrollPane.SetPercY(0, false);
        item.listInfo.numItems = cardData.key_points.Count + 2;
        item.tfTitleInfoLeft.SetKey("chat_mode_warmup");
        item.tfTitleInfoRight.SetKey("chat_mode_challenge");
        item.tfBalanceNum.text = "-" + cardData.gas_price.ToString();
        item.tfBalanceNum.color = _currencyController.IsEnough(cardData.gas_price) ? Color.white : Color.red;
        foreach (var modeList in cardData.mode_list)
        {
            if (modeList.task_mode == PB_DialogMode.Exercise) //联系
            {
                item.ctrlLeft.selectedPage =  modeList.is_completed ? "finish" : "notFinish";
            }
            else if (modeList.task_mode == PB_DialogMode.Challenge) //挑战
            {
                item.ctrlRight.selectedPage = modeList.is_completed ? "finish" : "notFinish";
                if (modeList.star_info.normal_get_star_cnt>0)
                {
                    var seq = DOTween.Sequence();
                    seq.AppendCallback(() =>
                    {
                        item.star_0.animationName = "2";
                        item.star_0.playing = true;
                    });
                    if (modeList.star_info.normal_get_star_cnt > 1)
                    {
                        seq.AppendInterval(0.2f);
                        seq.AppendCallback(() =>
                        {
                            item.star_1.animationName = "2";
                            item.star_1.playing = true;
                        }); 
                    }
                    if (modeList.star_info.normal_get_star_cnt > 2)
                    {
                        seq.AppendInterval(0.2f);
                        seq.AppendCallback(() =>
                        {
                            item.star_2.animationName = "2";
                            item.star_2.playing = true;
                        }); 
                    }

                    if (modeList.star_info.normal_get_star_cnt > 2)
                    {
                        
                    }
                }
                else
                {
                    item.star_0.animationName = "1";
                    item.star_0.playing = true;
                    item.star_1.animationName = "1";
                    item.star_1.playing = true;
                    item.star_2.animationName = "1";
                    item.star_2.playing = true;
                }
            }
        }
    }

    private void RefreshPostion(RecomTaskCardItemCom item)
    {
        item.comTag1.group1.SetBoundsChangedFlag();
        item.comTag1.group1.EnsureBoundsCorrect();
        item.comTag1.group2.SetBoundsChangedFlag();
        item.comTag1.group2.EnsureBoundsCorrect();
        item.comTag1.group3.SetBoundsChangedFlag();
        item.comTag1.group3.EnsureBoundsCorrect();
        item.comTag1.group4.SetBoundsChangedFlag();
        item.comTag1.group4.EnsureBoundsCorrect();
        item.comTag1.group5.SetBoundsChangedFlag();
        item.comTag1.group5.EnsureBoundsCorrect();
        item.comTag1.group6.SetBoundsChangedFlag();
        item.comTag1.group6.EnsureBoundsCorrect();
        item.comTag.group1.SetBoundsChangedFlag();
        item.comTag.group1.EnsureBoundsCorrect();
        item.comTag.group2.SetBoundsChangedFlag();
        item.comTag.group2.EnsureBoundsCorrect();
        item.comTag.group3.SetBoundsChangedFlag();
        item.comTag.group3.EnsureBoundsCorrect();
        item.comTag.group4.SetBoundsChangedFlag();
        item.comTag.group4.EnsureBoundsCorrect();
        item.comTag.group5.SetBoundsChangedFlag();
        item.comTag.group5.EnsureBoundsCorrect();
        item.comTag.group6.SetBoundsChangedFlag();
        item.comTag.group6.EnsureBoundsCorrect();
    }
    
    private void CardInfo(int index, GObject gObject,List<PB_TaskKeyPoint> taskInfoData)
    {
        if (taskInfoData == null) return;
        PB_TaskKeyPoint data = taskInfoData[index];
        RecomTaskItemCom item = new RecomTaskItemCom();
        item.Construct(gObject.asCom);
        if (index ==0)
        {
            item.ctrlState.selectedPage = "title";
            item.tfTitle.text = data.foreign_lang_word;
            item.tfDec.text = data.first_lang_word;
            item.gropOne.SetBoundsChangedFlag();
            item.gropOne.EnsureBoundsCorrect();
            item.com.height = item.gropOne.height;
        }
        else if(index == 1)
        { 
            item.ctrlState.selectedPage = "keyTitle";
            item.tfKeyTitle.text = data.foreign_lang_word;
            item.gropTwo.SetBoundsChangedFlag();
            item.gropTwo.EnsureBoundsCorrect();
            item.com.height = item.gropTwo.height;
        }
        else
        {
            item.ctrlState.selectedPage = "keywords";
            item.tfKeyLeft.text = data.foreign_lang_word;
            item.tfKeyRight.text = data.first_lang_word;
            item.gropThree.SetBoundsChangedFlag();
            item.gropThree.EnsureBoundsCorrect();
            item.com.height = item.bg.height + 7;
        }
        item.gropOut.EnsureBoundsCorrect();
    }

    
    private void OnBtnLeftClick(EventContext context)
    {
        PB_Recommend_Task_Resp cardData = (context.sender as GButton).data as PB_Recommend_Task_Resp ;
        _chatModel.SetChatMode(PB_DialogMode.Exercise);
        GoTask();

        //新埋点：点击热身模式
        DataDotClickWarmupModel model = new DataDotClickWarmupModel();
        DataDotMgr.Collect(model);
    }
    
    private void OnBtnRightClick(EventContext context)
    {
        PB_Recommend_Task_Resp cardData = (context.sender as GButton).data as PB_Recommend_Task_Resp ;
        if (!_currencyController.IsEnough(cardData.gas_price))
        {
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_common_lack_diamond",true);
            GetController<ShopController>(ModelConsts.Shop).EnterShop();
            return;
        }
        _currencyController.SendBuyDialogTicketReq(mode: PB_DialogMode.Challenge, () =>
        {
            _chatModel.SetChatMode(PB_DialogMode.Challenge);
            GoTask();
            //新埋点：点击挑战模式
            DataDotClickChallengingModel model = new DataDotClickChallengingModel();
            DataDotMgr.Collect(model);
        }, taskID: cardData.task_id);
    }

    private void GoTask()
    {
        _MainModel.SetChatEnterPos(ChatEnterPos.RecommendCard);
        ui.ctrlOpen.selectedPage = "talk";
        var dataInfo = _recommendCardModel.GetCurTaskData();
        SceneStateTaskParam data = new SceneStateTaskParam()
        {
            avatarId = dataInfo.avatar_id,
            taskId = dataInfo.task_id,
        };
        if (dataInfo.avatar_id == 0)
        {
            Debug.LogError("avatarID is null");
            return;
        }
        GetController<MainController>(ModelConsts.Main).ChangeToTaskState(data);
        //缓存接口 挪到了买票处统一缓存
        // CS_CreateDialogTaskCacheReq dataa = new CS_CreateDialogTaskCacheReq()
        // {
        //     avatar_id = dataInfo.avatar_id,
        //     task_id = dataInfo.task_id,
        // };
        //MsgManager.instance.SendMsg(dataa);
        SoundManger.instance.PlayUI("button_start");
    }

    private void OnBtnPackUp(EventContext context)
    {
        RecomTaskCardItemCom item = (context.sender as GButton).data as RecomTaskCardItemCom ;
        item.ctrlDesInfo.selectedPage = "packUp";
    }    
    
    private void OnBtnUnFold(EventContext context)
    {
        RecomTaskCardItemCom item = (context.sender as GButton).data as RecomTaskCardItemCom ;
        item.ctrlDesInfo.selectedPage = "unFold";
    }

    public void SetTalkState()
    {
        ui.ctrlOpen.selectedPage = "talk";
    }

    //===============================事件=========================
        protected override void HandleNotification(string name, object body)
        {
            switch (name)
            {
                case RecommendCardCallEvent.OnRefreshUI:
                    SetUI();
                    if (_recommendCardModel.isOpen)
                    {
                        OpenList();
                    }
                    break;
                case RecommendCardCallEvent.OnRefreshTask:
                    _recommendCardController.SendTaskModeInfo();
                    break;
                case RecommendCardCallEvent.OnRefreshSetTask:
                    ui.SpineEniter.playing = true;
                    ui.ctrlOpen.selectedPage = "spine";
                    ui.SpineEniter.spineAnimation.AnimationState.SetAnimation(
                        0, "2", false).Complete += (a) =>
                    {
                        ui.ctrlOpen.selectedPage = "open";
                        ui.aniEnter.Play(() => { SetUI(true);});
                    };
                    break;
                case RecommendCardCallEvent.OnResetRecommentState:
                    ui.ctrlOpen.selectedPage = "close";
                    ui.SpineEniter.animationName = "1";
                    ui.SpineEniter.playing = true;
                    ui.SpineEniter.loop = true;
                    break;
            }
        }

        protected override string[] ListNotificationInterests()
        {
            return new string[]
            {
                RecommendCardCallEvent.OnRefreshUI,
                RecommendCardCallEvent.OnRefreshTask,
                RecommendCardCallEvent.OnRefreshSetTask,
                RecommendCardCallEvent.OnResetRecommentState
            };
        }
    
    
}
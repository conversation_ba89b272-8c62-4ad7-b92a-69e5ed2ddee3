﻿using FairyGUI;
using ScriptsHot.Game.Modules.MainPath;
using UIBind.ExplorePush;

namespace ScriptsHot.Game.Modules.ExplorePush
{
    public class ExplorePushUI : BaseUI<ExplorePushPanel>
    {
        public ExplorePushUI(string name) : base(name) { }
        public override string uiLayer => UILayerConsts.Top;
        private ExplorePushModel Model => GetModel<ExplorePushModel>(ModelConsts.ExplorePush);
        private ExplorePushController Controller => GetController<ExplorePushController>(ModelConsts.ExplorePush);
        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            AddUIEvent(ui.btnNext.onClick, OnClickNext);
            AddUIEvent(ui.btnGo.onClick, OnClickGo);
        }

        private ExplorePushModel.ExplorePushEntrance _entrance = ExplorePushModel.ExplorePushEntrance.none;
        
        protected override void OnShow()
        {
            base.OnShow();
            if (args.Length > 0)
                _entrance = (ExplorePushModel.ExplorePushEntrance)args[0];
            
            ui.tfNext.SetKey("ui_explore_push_next");
            RefreshExplorePush();
        }

        protected override void OnHide()
        {
            base.OnHide();
            Controller.ReqSetShowState();
        }

        protected override bool isFullScreen => true;

        private string _trafficSource;
        private void RefreshExplorePush()
        {
            if (Model.NavigationIndex == 1)
            {
                switch (_entrance)
                {
                    case ExplorePushModel.ExplorePushEntrance.finish_course_back_main_path:
                        ui.tfContent.SetKey("ui_explore_push_content");
                        _trafficSource = "session1_completed";
                        break;
                    case ExplorePushModel.ExplorePushEntrance.un_finish_course_back_main_path:
                        ui.tfContent.SetKey("ui_explore_push_ready");
                        _trafficSource = "session1_incompleted";
                        break;
                    default:
                        ui.tfContent.SetKey("ui_explore_push_topic");
                        _trafficSource = "leave_in_session";
                        break;
                }

                ui.first.selectedIndex = 0;
                ui.tfGo.SetKey("ui_explore_push_go");
            }
            else
            {
                _trafficSource = "0";
                ui.first.selectedIndex = 1;
                ui.tfGo.SetKey("ui_explore_push_jump");
            }
            
            DotAppearSessionToExplorePopup dot = new DotAppearSessionToExplorePopup();
            dot.popup_type = Model.NavigationIndex;
            dot.traffic_source = _trafficSource;
            DataDotMgr.Collect(dot);
        }

        private void OnClickNext()
        {
            DotClickSessionToExplorePopupButton dot = new DotClickSessionToExplorePopupButton();
            dot.popup_type = Model.NavigationIndex;
            dot.traffic_source = _trafficSource;
            dot.button_type = "session";
            DataDotMgr.Collect(dot);
            
            Hide();
            GetController<MainPathController>(ModelConsts.MainPath).EnterCurrentLevel();
        }

        private void OnClickGo()
        {
            DotClickSessionToExplorePopupButton dot = new DotClickSessionToExplorePopupButton();
            dot.popup_type = Model.NavigationIndex;
            dot.traffic_source = _trafficSource;
            dot.button_type = "explore";
            DataDotMgr.Collect(dot);
            
            Hide();
            GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage).SwitchTab(TabIndex.Explore, false, true);
        }
    }
}
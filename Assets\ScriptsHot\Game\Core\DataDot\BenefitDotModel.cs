/*
****************************************************
# 作者：Huangshiwen
# 创建时间：   2025/03/18 11:15:57 星期二
# 功能：Nothing
****************************************************
*/

namespace Modules.DataDot
{
    public enum DotMemberStatusEnum
    {
        member,
        non_member,
        unrenewed_member,
    }
    
    public enum DotBenefitSourcePageEnum
    {
        non_member_diamond_shop_page,
        membership_icon,
    }
    
    public enum DotBenefitRootPageEnum
    {
        popup,
        bottom_bar,
        result_page,
        non_member_membership_page,
    }
    
// 用户点击会员icon
    public class DotClickMembershipIcon:PayWallDotHelperBase0
    {
        public override string Event_name => "Click_membership_icon";

    }

// 出现年会员钻石小店页面
    public class DotAppearYearlyMemberDiamondShopPage : DataDotBase
    {
        public override string Event_name => "Appear_yearly_member_diamond_shop_page";
    }

// 出现升级到年会员钻石小店页面
    public class DotAppearUpgradeMemberDiamondShopPage : DataDotBase
    {
        public override string Event_name => "Appear_upgrade_member_diamond_shop_page";
    }

// 出现非会员钻石小店页面
    public class DotAppearNonMemberDiamondShopPage : DataDotBase
    {
        public override string Event_name => "Appear_non_member_diamond_shop_page";
    }

// 出现续订会员钻石小店页面
    public class DotAppearRenewMemberDiamondShopPage : DataDotBase
    {
        public override string Event_name => "Appear_renew_member_diamond_shop_page";
    }

// 在非会员钻石小店页面点击试用
    public class DotClickNonMemberDiamondShopPageTrial : PayWallDotHelperBase0
    {
        public override string Event_name => "Click_non_member_diamond_shop_page_trial";
    }

// 在升级到年会员钻石小店页面点击升级
    public class DotClickUpgradeDiamondShopPageUpgrade : DataDotBase
    {
        public override string Event_name => "Click_upgrade_diamond_shop_page_upgrade";
    }

// 在续订会员钻石小店页面点击keep my subscription
    public class DotClickRenewMemberDiamondShopPageKeep : DataDotBase
    {
        public override string Event_name => "Click_renew_member_diamond_shop_page_keep";
    }

// 出现非会员状态会员权益说明页
    public class DotAppearNonMemberMembershipPage : DataDotBase
    {
        public override string Event_name => "Appear_non_member_membership_page";
        public string source_page;
    }

// 出现年会员状态会员权益说明页
    public class DotAppearYearlyMemberMembershipPage : DataDotBase
    {
        public override string Event_name => "Appear_yearly_member_membership_page";
        public string source_page;
    }

// 出现月会员状态会员权益说明页
    public class DotAppearUpgradeMemberMembershipPage : DataDotBase
    {
        public override string Event_name => "Appear_upgrade_member_membership_page";
        public string source_page;
    }

// 出现续订会员状态权益说明页
    public class DotAppearRenewMemberMembershipPage : DataDotBase
    {
        public override string Event_name => "Appear_renew_member_membership_page";
        public string source_page;
    }

// （覆盖Click_Home_page_diamond旧点）用户点击钻石icon
    public class DotClickDiamondIcon : PayWallDotHelperBase
    {
        public override string Event_name => "Click_diamond_icon";
        
    }
    
    //Appear_diamond_shop_page
    public class DotAppearDiamondShopPage : PayWallDotHelperBase0
    {
        public override string Event_name => "Appear_diamond_shop_page";
    }

// 用户点击会员权益页的升级按钮
    public class DotClickMembershipPageUpgrade : DataDotBase
    {
        public override string Event_name => "Click_membership_page_upgrade";
    }

// 用户点击会员权益页的stay on premium按钮
    public class DotClickMembershipPageStayOnPremium : DataDotBase
    {
        public override string Event_name => "Click_membership_page_stay_on_premium";
    }
    
    public class DotAppearMonetizationDiscountBottomBar : DataDotBase
    {
        public override string Event_name => "appear_monetization_discount_bottom_bar";
    }
    
    public class DotAppearMonetizationDiscountBottomBarDiscountIcon : DataDotBase
    {
        public override string Event_name => "appear_monetization_discount_bottom_bar_discount_icon";
    }
    
    public class DotClickMonetizationDiscountBottomBar : DataDotBase
    {
        public override string Event_name => "click_monetization_discount_bottom_bar";
    }
    
    public class DotClickMonetizationDiscountBottomBarDiscountIcon : DataDotBase
    {
        public override string Event_name => "click_monetization_discount_bottom_bar_discount_icon";
    }
    
    public class DotClickMonetizationDiscountBottomBarExitButton : DataDotBase
    {
        public override string Event_name => "click_monetization_discount_bottom_bar_exit_button";
    }
    
    public class DotAppearMonetizationDiscountPopup : DataDotBase
    {
        public override string Event_name => "appear_monetization_discount_popup";
    }
    
    public class DotClickMonetizationDiscountPopupExitButton : DataDotBase
    {
        public override string Event_name => "click_monetization_discount_popup_exit_button";
    }
    
    public class DotClickMonetizationDiscountPopupGetButton : DataDotBase
    {
        public override string Event_name => "click_monetization_discount_popup_get_button";
    }
}
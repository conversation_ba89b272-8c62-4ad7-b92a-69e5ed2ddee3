﻿/* 
****************************************************
* 作者：liuxinmiao
* 创建时间：2024/09/18 17:57:55 星期三
* 功能：Nothing
****************************************************
*/


using System;
using System.Collections.Generic;
using System.Linq;
using CommonUI;
using FairyGUI;
using Msg;
using Msg.basic;
using Msg.dialog_task;
using Msg.economic;
using Msg.question;
using Msg.social;
using Msg.speech;
using Msg.talkitpush;
using ScriptsHot.Game.Modules.System;
using ScriptsHot.Game.UGUI.WebView;
using Unity.Mathematics;
using UnityEngine;
using static SocialChatModel;
using pb = Google.Protobuf;

public class SocialChatController : BaseController
{
    public SocialChatController() : base(ModelConsts.SocialChat)
    {
    }

    private SocialChatStateMachine _stateMachine = null;
    private SocialChatModel _socialChatModel => GetModel<SocialChatModel>(ModelConsts.SocialChat);
    private HeadUrlModel _headUrlModel => GetModel<HeadUrlModel>(ModelConsts.HeadUrl);
    private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);

    private List<string> ignoreList = new List<string>(){"SceneLocate","MainHeader","Flow","EnterUI","LittleMapUI"};
    public Action<SC_ClickWordAck> NewWordResp;

    public Action<int> AudioStop;

    public override void OnUIInit()
    {
        RegisterUI(new SocialChatUI(UIConsts.SocialChatUI));
        MsgManager.instance.RegisterCallBack<SC_ChatMessageNtf>(ChatMessageNtf);
        MsgManager.instance.RegisterCallBack<SC_ChatRecordStatusNtf>(OnChatRecordStatusNtf);
        MsgManager.instance.RegisterCallBack<SC_DialogTranslateAck>(OnSCDialogTranslateAck);
        MsgManager.instance.RegisterCallBack<SC_ClickWordAck>(OnClickWordAck);
        MsgManager.instance.RegisterCallBack<SC_ChatRecordStatusSyncAck>(OnChatRecordStatusSyncAck);
        AudioStop = AudioStopCallBack;
    }

    public override void OnInit()
    {
        RegisterModel(new SocialChatModel());
    }

    public override void OnUpdate(int interval)
    {
        if (_stateMachine != null)
            _stateMachine.Update(interval);
    }

    /// <summary>
    /// 检测Index(本地已读index高于本地数据index)
    /// </summary>
    /// <returns>返回参数1，index差异属性。返回参数2，本地数据最后一条的Index数</returns>
    private (long, long) CheckIndex()
    {
        var list = _socialChatModel.GetMessageList();
        if (list.Count > 0)
        {
            var data = _socialChatModel.GetMessage(list[list.Count - 1]);
            if (data != null)
            {
                if (data.indexId < _socialChatModel.curIndex)
                {
                    var num = _socialChatModel.curIndex - data.indexId;
                    return (num, data.indexId);
                }
            }
        }
        else
        {
            return (_socialChatModel.curIndex, 0);
        }

        return (-1, 0);
    }
    
    /// <summary>
    /// 外部点击聊天入口
    /// </summary>
    /// <param name="uid">角色uid</param>
    /// <param name="playerName">名字</param>
    /// <param name="uniqueName">昵称</param>
    /// <param name="headUrl">头像地址</param>
    /// <param name="unreadCount">未读数</param>
    /// <param name="chatId">聊天ID</param>
    /// <param name="memberType">会员类型</param>
    /// <param name="userReadIndex">服务器记录的已读Index</param>
    public async void OnClickChat(long uid, string playerName, string uniqueName, string headUrl, long unreadCount,
        long chatId, MemberType memberType, long userReadIndex)
    {
        _stateMachine = new SocialChatStateMachine(this);
        _socialChatModel.userId = uid;
        _socialChatModel.playerName = playerName;
        _socialChatModel.uniqueName = uniqueName;
        _socialChatModel.headUrl = headUrl;
        _socialChatModel.chatId = chatId;
        _socialChatModel.memberType = memberType;
        
        //加载本地数据
        _socialChatModel.LoadLocalData();

        var checkInfo = CheckIndex();
        
        if (unreadCount != 0  || checkInfo.Item1 != -1)
        {
            var timer = TimerManager.instance.RegisterTimer(TimeOut, 10 * 1000, 1);
            
            //开始查找消息的index(从index之后不包含index)
            var startIndex = checkInfo.Item1 != -1 ? checkInfo.Item2 : _socialChatModel.curIndex;
            //查找的数量
            var num = checkInfo.Item1 != -1 ? unreadCount + checkInfo.Item1 : unreadCount;
            
            var resp = await MsgManager.instance.SendAsyncMsg<SC_QueryChatMessageListAck>(
                this.CreateQueryChatMessageList(chatId, true,startIndex, num));

            if (resp.code == 0)
            {
                var data = resp.messageList.ToList();
                _socialChatModel.SetMessageData(resp.chatId, data);
                if (data.Count > 0)
                {
                    //查找方向为升序（isAsc为true）,返回的列表是正序 ， （如果isAsc为flase,返回的列表是逆序）
                    _socialChatModel.lastIndex = data[data.Count - 1].index;
                    UpdateChatIndex(resp.chatId, _socialChatModel.lastIndex);
                }

                //hasMore为true，表示还有数据可拉取
                if (resp.hasMore)
                {
                    RequestQueryChatMessageList(resp.chatId, true, data[data.Count - 1].index, 100);
                }
                else
                {
                    //刷新对话UI到指定下标
                    SendNotification(SocialChatEvent.OnShowSocialChatUI, _socialChatModel.lastIndex);
                }
            }
            else
            {
             
                TimeOut(0);
            }
            TimerManager.instance.UnRegisterTimer(timer);
            GetUI(UIConsts.SocialChatUI).Show();
        }
        else
        {
            GetUI(UIConsts.SocialChatUI).Show();
        }
    }
    /// <summary>
    /// 超时Toast
    /// </summary>
    /// <param name="time"></param>
    private void TimeOut(int time)
    {
        GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_socialchat_network_timeout",true);
        GetUI(UIConsts.CommBusy).Hide();
    }

    /// <summary>
    /// 创建获取信息列表请求
    /// </summary>
    /// <param name="chatId">对话ID</param>
    /// <param name="isAsc">是否升序</param>
    /// <param name="startIndex">起始Index</param>
    /// <param name="pageSize">查找数量</param>
    /// <returns></returns>
    private CS_QueryChatMessageListReq CreateQueryChatMessageList(long chatId, bool isAsc, long startIndex,
        long pageSize)
    {
        CS_QueryChatMessageListReq req = new CS_QueryChatMessageListReq();
        req.chatId = chatId;
        req.isAsc = isAsc;
        req.startIndex = startIndex;
        req.pageSize = pageSize;
        return req;
    }

    /// <summary>
    /// 发起获取信息列表请求
    /// </summary>
    /// <param name="chatId"></param>
    /// <param name="isAsc"></param>
    /// <param name="startIndex"></param>
    /// <param name="pageSize"></param>
    public async void RequestQueryChatMessageList(long chatId, bool isAsc, long startIndex, long pageSize)
    {
        var resp = await MsgManager.instance.SendAsyncMsg<SC_QueryChatMessageListAck>(
            this.CreateQueryChatMessageList(chatId, isAsc, startIndex, pageSize));

        if (resp.code == 0)
        {
            var data = resp.messageList.ToList();
            _socialChatModel.SetMessageData(resp.chatId, data);
            long preIndex = 0; 
            if (data.Count > 0)
            {
                if (isAsc)
                    _socialChatModel.lastIndex = data[data.Count - 1].index;
                else
                    preIndex = data[0].index;
                UpdateChatIndex(resp.chatId, _socialChatModel.lastIndex);
            }

            if (resp.hasMore)
            {
                RequestQueryChatMessageList(resp.chatId, isAsc, data[data.Count - 1].index, pageSize);
            }
            else
            {
                if (!isAsc)
                {
                   SendNotification(SocialChatEvent.OnShowSocialChatUI, preIndex);
                }
                else
                { 
                    SendNotification(SocialChatEvent.OnShowSocialChatUI, _socialChatModel.lastIndex);
                }
            }
        }
    }
    /// <summary>
    /// 推送消息
    /// </summary>
    /// <param name="ntf"></param>
    private void ChatMessageNtf(SC_ChatMessageNtf ntf)
    {
        //添加本地缓存
        _socialChatModel.SetMessageData(ntf.chatId, ntf.message);
        
        //是否添加本地未读数量
        var isAddUnreadCount = false;
        
        if (ntf.chatId == _socialChatModel.chatId)
        {
            //对话中， 直接更新最新下标
            _socialChatModel.lastIndex = ntf.message.index;
            UpdateChatIndex(ntf.chatId, ntf.message.index);
        }
        else
        {
            isAddUnreadCount = true;
        }
    
        //判定push弹窗的条件与样式
        if (GetUI<SocialChatUI>(UIConsts.SocialChatUI).isShow)
        {
            //对话中 push弹窗
            if (ntf.chatId != _socialChatModel.chatId)
            {
                var isAdd = _socialChatModel.AddPushList(ntf.chatId);
                if(isAdd)
                    GetUI<CommonPushUI>(UIConsts.CommonPush).Open(PushType.Chat,ntf.user.playerName,ntf.user.headUrl,I18N.inst.MoStr("ui_commonpush_tips"));
            }
        }
        else
        {
            //非对话中，判断当前显示的UI，有忽略列表以外的UI展示,不弹push
            var dic = UIManager.instance.GetShowUI();
            var isShow = !WebViewCtl.isOpen;
            foreach (var key in dic.Keys)
            {
                if (!ignoreList.Contains(key))
                {
                    isShow = false;
                    break;
                }
            }
            if(isShow) 
                GetUI<CommonPushUI>(UIConsts.CommonPush).Open(PushType.Scene,ntf.user.playerName,ntf.user.headUrl);
        }
        
        //发起刷新红点的请求
        SendNotification(SocialChatEvent.OnRefreshUnreadCount);
        //更新本地聊天列表
        UpdateMessagesData(ntf.chatId, ntf.message.timestamp,ntf.user,isAddUnreadCount);
    }

    /// <summary>
    /// 刷新聊天列表（用于关闭对话后）
    /// </summary>
    private void RefreshMessagesData()
    {
        if (!GetUI<SocialChatUI>(UIConsts.SocialChatUI).isShow)
        {
            GetController<MessagesController>(ModelConsts.Messages).RefreshData();
        }
    }

    /// <summary>
    /// 更新本地聊天列表数据
    /// </summary>
    /// <param name="chatId"></param>
    /// <param name="updateTime"></param>
    /// <param name="userInfo"></param>
    /// <param name="isAddUnreadCount"></param>
    private void UpdateMessagesData(long chatId, long updateTime,SingleChatUser userInfo,bool isAddUnreadCount)
    {
        GetController<MessagesController>(ModelConsts.Messages).UpdateLocalChatItemInfo(chatId,updateTime,userInfo,isAddUnreadCount);
    }
    
    /// <summary>
    /// 发送当前对话中的录音状态请求
    /// </summary>
    /// <param name="status"></param>
    public void RequestChatRecordStatus(Social_RecordStatus status)
    {
        CS_ChatRecordStatusSyncReq req = new CS_ChatRecordStatusSyncReq();
        req.chatId = _socialChatModel.chatId;
        req.userId =  _mainModel.userID;
        req.status = status;
        MsgManager.instance.SendMsg(req);
    }

    /// <summary>
    /// 录音状态回复
    /// </summary>
    /// <param name="ack"></param>
    private void OnChatRecordStatusSyncAck(SC_ChatRecordStatusSyncAck ack)
    {
        if (ack.code != 0)
        {
            VFDebug.Log("ChatRecordStatusrError");
        }
    }
    /// <summary>
    /// 对话对象的录音状态推送
    /// </summary>
    /// <param name="ntf"></param>
    private void OnChatRecordStatusNtf(SC_ChatRecordStatusNtf ntf)
    {
        if (ntf.chatId == _socialChatModel.chatId && ntf.userId == _socialChatModel.userId)
        {
            GetUI<SocialChatUI>(UIConsts.SocialChatUI).RecordingStateShow(ntf.status);
        }
    }
    /// <summary>
    /// 获取指定状态机的当前状态
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public string GetState(SocialStateMachineType type)
    {
        var state = _stateMachine.RecordStateMachine.GetState();
        if(SocialStateMachineType.Dialog == type)
            state = _stateMachine.DialogStateMachine.GetState();
        return state;
    }

    /// <summary>
    /// 改变指定状态机的状态
    /// </summary>
    /// <param name="name"></param>
    /// <param name="type"></param>
    /// <param name="args"></param>
    public void ChangeState(string name, SocialStateMachineType type, params object[] args)
    {
        if (_stateMachine == null)
        {
            return;
        }

        Debug.Log("SocialChta:ChangeState  " + name);
        if (type == SocialStateMachineType.Dialog)
            _stateMachine.DialogStateMachine.ChangeState(name, args);
        else if (type == SocialStateMachineType.Record)
            _stateMachine.RecordStateMachine.ChangeState(name, args);
        else
            _stateMachine.ChangeState(name, args);
    }
    
    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="content"></param>
    /// <param name="asrId"></param>
    public async void SendMessage(string content, long asrId)
    {
        CS_SendChatMessageReq req = new CS_SendChatMessageReq();
        req.chatId = _socialChatModel.chatId;
        req.content = content;
        req.asrId = asrId;
        req.uniqueId = asrId;
        var resp = await MsgManager.instance.SendAsyncMsg<SC_SendChatMessageAck>(req);
        if (resp.code == 0)
        {
            UpdateChatIndex(_socialChatModel.chatId, resp.sentMessage.index);
            _socialChatModel.SetMessageData(_socialChatModel.chatId, resp.sentMessage);
            if (resp.messageList.Count > 0)
            {
                _socialChatModel.SetMessageData(_socialChatModel.chatId, resp.messageList.ToList());
            }
        }
        _stateMachine.RecordStateMachine.ChangeState(SocialRecordState.RecordIdle);
    }
    
    /// <summary>
    /// 更新已读下标
    /// </summary>
    /// <param name="chatId"></param>
    /// <param name="index"></param>
    public async void UpdateChatIndex(long chatId, long index)
    {
        CS_UpdateChatIndexReq req = new CS_UpdateChatIndexReq();
        req.chatId = chatId;
        req.index = index;

        var resp = await MsgManager.instance.SendAsyncMsg<SC_UpdateChatIndexAck>(req);
        if (resp.code == 0)
        {
            _socialChatModel.lastIndex = resp.index;
            _socialChatModel.UpdateLocalIndex(resp.chatId, resp.index);
        }
    }
    
    /// <summary>
    /// 更新玩家的状态
    /// </summary>
    /// <param name="chatId"></param>
    /// <param name="state"></param>
    public void UpdatePlayState(long chatId, int state)
    {
        _socialChatModel.UpdatePlayState(chatId, state);
    }

    /// <summary>
    /// 脚手架请求&&返回
    /// </summary>
    /// <param name="msg"></param>
    public async void ScaffoldReq(ChatMessage msg)
    {
        msg.scaffoldState = ChatScaffoldState.Request;
        CS_ChatScaffoldReq req = new CS_ChatScaffoldReq();
        req.chatId = msg.dialogId;
        req.index = msg.indexId;
        var resp = await MsgManager.instance.SendAsyncMsg<SC_ChatScaffoldAck>(req);
        if (resp.code == 0)
        {
            msg.scaffoldContext = resp.content;
            msg.scaffoldTranslation = resp.translate;
            msg.scaffoldTTsId = resp.ttsId;
            msg.scaffoldState = ChatScaffoldState.Show;
            SendNotification(SocialChatEvent.OnRefreshSocialChatUI, msg.indexId);
        }
    }

    /// <summary>
    /// 请求other聊天内容翻译
    /// </summary>
    /// <param name="msg"></param>
    public async void DialogTranslateReq(ChatMessage msg)
    {
        msg.translationState = ChatOtherTranslationState.Request;
        CS_DialogTranslateReq req = new CS_DialogTranslateReq();
        req.dialog_id = msg.dialogId;
        req.round_id = (int)msg.indexId;
        req.content = msg.context;

        MsgManager.instance.SendMsg(req);
    }

    /// <summary>
    /// other聊天翻译返回
    /// </summary>
    /// <param name="msg"></param>
    private void OnSCDialogTranslateAck(SC_DialogTranslateAck msg)
    {
        if (msg.code != PB_Code.Normal || msg.data == null)
        {
            VFDebug.LogError("SC_DialogTranslateAck iserror: " + msg);
            return;
        }

        ChatMessage chatMsg = _socialChatModel.GetMessage(msg.data.dialog_id, msg.data.round_id);
        if (chatMsg != null)
        {
            chatMsg.translationState = ChatOtherTranslationState.Show;
            chatMsg.translation = msg.data.trans_content;
            SendNotification(SocialChatEvent.OnRefreshSocialChatUI, chatMsg.indexId);
        }
    }

    /// <summary>
    /// 生词查询 返回
    /// </summary>
    /// <param name="msg"></param>
    public void OnClickWordAck(SC_ClickWordAck msg)
    {
        if (NewWordResp != null)
        {
            NewWordResp.Invoke(msg);
        }
    }

    public void LoadhistoryData()
    {
        _socialChatModel.LoadLocalMessageData();
    }

    public void ClearDataCache()
    {
        _socialChatModel.ClearMessageCache();
        _socialChatModel.ClearPushList();
        _stateMachine.ClearAllData();
        _stateMachine = null;
        GetUI<RecordUI>(UIConsts.RecordUI).ExitSocialRecord();
        RefreshMessagesData();
    }

    private CS_GetASRAudioReq CreateGetASRAudioReq(long asr_id)
    {
        CS_GetASRAudioReq req = new CS_GetASRAudioReq();
        req.record_id = asr_id;
        return req;
    }

    public async void RequestGetASRAudio(long asr_id, long timeStamp, int index)
    {
        var info = ClientSQLiteManager.Instance().GetASRInfo(asr_id);
        if (info == null )
        {
            var req = CreateGetASRAudioReq(asr_id);
            var resp = await MsgManager.instance.SendAsyncMsg<SC_GetASRAudioAck>(req);
            if (resp.code == 0)
            {
                ChatASRInfo chatASRInfo = new ChatASRInfo();
                chatASRInfo.asrId = asr_id;
                chatASRInfo.asr = resp.data.audio.ToByteArray();
                chatASRInfo.timeStamp = (int)timeStamp;
                ClientSQLiteManager.Instance().SaveASRInfo(chatASRInfo);
                var success = AudioFileManager.instance.SaveToLocal(asr_id, chatASRInfo.asr);
                if (success)
                {
                    GetAudioClip(asr_id, index);
                }
            }
        }
        else
        {
            if (AudioFileManager.instance.UrlExistedForRecordId(asr_id))
            {
                GetAudioClip(asr_id, index);
            }
            else
            {
                var success = AudioFileManager.instance.SaveToLocal(asr_id, info);
                if (success)
                {
                    GetAudioClip(asr_id, index);
                }
            }
        }
    }

    private async void GetAudioClip(long asr_id, int index)
    {
        if (asr_id == 0)
        {
            VFDebug.LogError("asr_id == 0 stack = SocialChatController.GetAudioClip");
        }
        var audioClip = await AudioFileManager.instance.GetAudioClip(asr_id);
        // var info = ChatSQLiteManager.Instance().GetASRInfo(asr_id);
        //
        // var audioClip = AudioManager.instance.LoadMp3Audio(info, asr_id.ToString());
        // Debug.LogError(audioClip.length);
        var state = _stateMachine.RecordStateMachine.GetState();
        if (state != SocialRecordState.Recording)
        {
            var list = _socialChatModel.GetMessageList();
            var data = _socialChatModel.GetMessage(list[index]);
            _stateMachine.RecordStateMachine.ChangeState(SocialRecordState.PlayAudio, audioClip, AudioStop, index,
                (int)audioClip.length);
            SendNotification(SocialChatEvent.OnShowSocialChatUI, data.indexId);
        }
        else
        {
            AudioStopCallBack(index);
        }
    }

    private void AudioStopCallBack(int index)
    {
        var list = _socialChatModel.GetMessageList();
        if (index >= list.Count)
            return;
        var data = _socialChatModel.GetMessage(list[index]);
        data.playState = 2;
        if (GetUI<SocialChatUI>(UIConsts.SocialChatUI).isShow)
        {
            SendNotification(SocialChatEvent.OnShowSocialChatUI, data.indexId);
        }
    }

    public async void RequestChatUserList(long uid, GLoader loader = null)
    {
        CS_QueryChatUserListReq req = new CS_QueryChatUserListReq();
        req.userId.Add( uid);
        
        var resp = await MsgManager.instance.SendAsyncMsg<SC_QueryChatUserListAck>(req);
        if (resp.code == 0)
        {
            if (resp.userList.Count > 0)
            {
                for (int i = 0; i < resp.userList.Count; i++)
                {
                    var data = resp.userList[i];
                    _headUrlModel.AddUrlData(data.headUrl, data.userId);
                }

                if (loader != null)
                    loader.url = resp.userList[0].headUrl;
            }
        }
    }

    public void FollowingCreateSingleChatDialog(long uid)
    {
        MsgManager.instance.SendAsyncMsg<SC_GetSingleChatDialogAck>(new CS_GetSingleChatDialogReq()
            { receiverId =uid });
    }

    public async void GetSingleChatDialog(long uid, string playerName, string uniqueName, string headUrl, MemberType memberType)
    {
        var resp = await MsgManager.instance.SendAsyncMsg<SC_GetSingleChatDialogAck>(new CS_GetSingleChatDialogReq()
            { receiverId =uid });
       _socialChatModel.SetLocalRecord(resp.chatId, resp.groupId, "single", resp.userReadIndex);
       _socialChatModel.SetLocalUserInfo(uid, playerName, (int)memberType, uniqueName, headUrl);
       OnClickChat(uid, playerName, uniqueName, headUrl, resp.unreadCount, resp.chatId, memberType, resp.userReadIndex);
    }

    public async void PushEnter(long uid,string groupType)
    {
        if (groupType.Equals("single"))
        {
            CS_QueryChatUserListReq req = new CS_QueryChatUserListReq();
            req.userId.Add(uid);
        
            var resp = await MsgManager.instance.SendAsyncMsg<SC_QueryChatUserListAck>(req);
            if (resp.code == 0)
            {
                if (resp.userList.Count > 0)
                {
                    for (int i = 0; i < resp.userList.Count; i++)
                    {
                        var data = resp.userList[i];
                        GetSingleChatDialog(uid, data.playerName, data.uniqueName, data.headUrl, data.memberType);
                    }
                }
            }
        }
    }
}

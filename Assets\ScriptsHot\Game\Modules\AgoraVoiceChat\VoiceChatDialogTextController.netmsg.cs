﻿using Msg.explore;
using UnityEngine;
namespace ScriptsHot.Game.Modules.AgoraRtc
{

    public partial class VoiceChatDialogTextController
    {
        private VoiceChatDialogTextModel Model;
        internal void Init(VoiceChatDialogTextModel model)
        {
            Model = model;
        }

        internal void InitRegister()
        {
            //用户自己说话数据
            MsgManager.instance.RegisterCallBack<SC_UserChatDownMsgForUserRecognizing>(OnUserDialogContentNtf2);
            MsgManager.instance.RegisterCallBack<SC_UserChatDownMsgForUserRecognized>(OnUserDialogContentNtf);

            MsgManager.instance.RegisterCallBack<SC_UserChatDownMsgForOtherUserRecognizing>(OnOtherDialogContentNtf2);
            MsgManager.instance.RegisterCallBack<SC_UserChatDownMsgForOtherUserRecognized>(OnOtherDialogContentNtf);
            MsgManager.instance.RegisterCallBack<SC_UserChatDownMsgForOtherUserReplyTranslate>(OnOtherDialogContentTranslateNtf);
            MsgManager.instance.RegisterCallBack<SC_UserChatDownMsgForUserReplyExample>(OnExampleNtf);
            MsgManager.instance.RegisterCallBack<SC_UserChatDownMsgForUserReplyExampleTranslate>(OnExampleTranslateNtf);
            MsgManager.instance.RegisterCallBack<SC_UserChatDownMsgForBizEvent>(OnDownMsg);
        }

        private void UnInitRegister()
        {
            MsgManager.instance.UnRegisterCallBack<SC_UserChatDownMsgForUserRecognized>(OnUserDialogContentNtf);
            MsgManager.instance.UnRegisterCallBack<SC_UserChatDownMsgForOtherUserRecognized>(OnOtherDialogContentNtf);
            MsgManager.instance.UnRegisterCallBack<SC_UserChatDownMsgForOtherUserReplyTranslate>(OnOtherDialogContentTranslateNtf);
            MsgManager.instance.UnRegisterCallBack<SC_UserChatDownMsgForUserReplyExample>(OnExampleNtf);
            MsgManager.instance.UnRegisterCallBack<SC_UserChatDownMsgForUserReplyExampleTranslate>(OnExampleTranslateNtf);
            MsgManager.instance.UnRegisterCallBack<SC_UserChatDownMsgForBizEvent>(OnDownMsg);
        }

        /// <summary>
        /// 用户自己说话数据--流返回
        /// </summary>
        /// <param name="msg"></param>
        private void OnUserDialogContentNtf(SC_UserChatDownMsgForUserRecognized msg)
        {
            Debug.Log("/用户自己说话数据--返回:taskId::" + msg.commonData.msgId);
            if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
            {
                Model.SetUserDialogInfo(msg);
            }
        }
        /// <summary>
        /// 用户自己说话数据--流返回
        /// </summary>
        /// <param name="msg"></param>
        private void OnUserDialogContentNtf2(SC_UserChatDownMsgForUserRecognizing msg)
        {
            Debug.Log("/用户自己说话数据--返回:taskId::" + msg.commonData.msgId);
            if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
            {
                Model.SetUserDialogInfo2(msg);
            }
        }

        /// <summary>
        /// 他人语音识别最终结果
        /// </summary>
        /// <param name="msg"></param>
        private void OnOtherDialogContentNtf(SC_UserChatDownMsgForOtherUserRecognized msg)
        {
            Debug.Log("/他人语音识别最终结果--返回:taskId::" + msg.commonData.msgId);
            if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
            {
                Model.SetOtherDialogInfo(msg);
            }
        }

        private void OnOtherDialogContentNtf2(SC_UserChatDownMsgForOtherUserRecognizing msg)
        {
            Debug.Log("/他人语音识别最终结果--返回:taskId::" + msg.commonData.msgId);
            if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
            {
                Model.SetOtherDialogInfo2(msg);
            }
        }

        
        /// <summary>
        /// 他人回复翻译
        /// </summary>
        /// <param name="msg"></param>
        private void OnOtherDialogContentTranslateNtf(SC_UserChatDownMsgForOtherUserReplyTranslate msg)
        {
            if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
            {
                Model.SetOtherDialogTranlate(msg);
            }
        }
        /// <summary>
        /// 用户回复示例
        /// </summary>
        /// <param name="msg"></param>
        private void OnExampleNtf(SC_UserChatDownMsgForUserReplyExample msg)
        {
            if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
            {
                Model.SetExample(msg);
            }
        }
        /// <summary>
        /// 用户回复示例翻译
        /// </summary>
        /// <param name="msg"></param>
        private void OnExampleTranslateNtf(SC_UserChatDownMsgForUserReplyExampleTranslate msg)
        {
            if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
            {
                Model.SetExampleTranslate(msg);
            }
        }
    
        /// <summary>
        /// 用户聊天下行 - 业务事件
        /// </summary>
        /// <param name="msg"></param>
        private void OnDownMsg(SC_UserChatDownMsgForBizEvent msg)
        {
        }
    }
}
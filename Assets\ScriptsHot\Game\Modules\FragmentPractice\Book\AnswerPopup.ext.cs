using FairyGUI;
using FairyGUI.Utils;
using ScriptsHot.Game.Modules.FragmentPractice;
using UIBind.common;
using UnityEngine;
namespace UIBind.FragmentPractice
{
    public partial class AnswerPopup : GComponent
    {
        private AutoSizeLoader LoaderAnswer => loaderAnswer as AutoSizeLoader;
        private AFragAnswer content => LoaderAnswer.content as AFragAnswer;
        public AFragAnswer LoadPractice(APracticeData practice, AFragQuestion question = null)
        {
            Clear();
            title.text = (practice as IBookPracticeData)?.GetInstruction() ?? "";

            string answerUrl = practice.GetAnswerComponentUrl();
            LoaderAnswer.Load(answerUrl, true);

            content.Init(true, practice);
            content.ShowPractice(question);
           
            content.ForceCorrect = true;
            content.FitToPreferedHeight();
            EnsureBoundsCorrect();
            loaderAnswer.y = layout.position.y + layout.height / 2 - loaderAnswer.height / 2;
             
            CollectData(practice);
            if (AppConst.IsDebug)
            { 
                tfDebugInfo.text = $"Qid {practice.QuestionId}";
                tfDebugInfo.visible = true;
            }
            else
            {
                tfDebugInfo.visible = false;
            }

            return content;
        }
        
        public void CollectData(APracticeData practice)
        {            
            DotPracticeManager.Instance.qid = practice.QuestionId;
            DotPracticeManager.Instance.Collect(new DataDot_AppearPage());
        }

        public void Clear()
        {
            LoaderAnswer.Load(null);
        }
    }
}
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExplorePanel : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExplorePanel";

        public Controller page;
        public Controller ctrlPay;
        public GGraph imgBG;
        public GImage btnClose;
        public GList listContainer;
        public GButton btnSetting;
        public GTextField txtTime;
        public GGraph btnPay;
        public GGraph btnVip;
        public GGroup comTime;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            page = com.GetControllerAt(0);
            ctrlPay = com.GetControllerAt(1);
            imgBG = (GGraph)com.GetChildAt(0);
            btnClose = (GImage)com.GetChildAt(1);
            listContainer = (GList)com.GetChildAt(2);
            btnSetting = (GButton)com.GetChildAt(3);
            txtTime = (GTextField)com.GetChildAt(8);
            btnPay = (GGraph)com.GetChildAt(9);
            btnVip = (GGraph)com.GetChildAt(11);
            comTime = (GGroup)com.GetChildAt(12);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            page = null;
            ctrlPay = null;
            imgBG = null;
            btnClose = null;
            listContainer = null;
            btnSetting = null;
            txtTime = null;
            btnPay = null;
            btnVip = null;
            comTime = null;
        }
    }
}
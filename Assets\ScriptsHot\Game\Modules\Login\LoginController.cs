using UnityEngine;
using Msg;
using Lit<PERSON>son;
using Msg.basic;
using Msg.core;
using Msg.dialog_task;
using Msg.task_process;
using Msg.social;



using Cysharp.Threading.Tasks;
using System;
using CommonUI;
using Game;

using Msg.incentive;

#if (UNITY_IOS || UNITY_ANDROID) && !UNITY_EDITOR
using MTPush;
using JPush;
#endif

#if UNITY_IOS && !UNITY_EDITOR
using UnityEngine.iOS;
#endif
using Msg.notification;
using ScriptsHot.Game.Modules.Login.OnBoarding;
using ScriptsHot.Game.Modules.Sign;
using ScriptsHot.Game.Modules.Shop;
using ScriptsHot.Game.Modules.System;
using ScriptsHot.Game.Modules.WebView;
using ScriptsHot.Game.UGUI.iOSWidget;
using ScriptsHot.Game.Modules.ChatStart;
using ScriptsHot.Game.Modules.MainPath;
using ScriptsHot.Game.Modules.Managers;
using UIBind.Login;

//todo1 分离：基础登录逻辑 + 登录中的onboading阶段的能力 + onboading之后其他特殊阶段的强制引导能力
public partial class LoginController : BaseController
{
    public LoginController() : base(ModelConsts.Login)
    {
    }

    public bool isShowFlowTips { get; private set; }
    public bool isLoginComplete { get; private set; }
    private LoginOnBoardingModel _boardingModel => GetModel<LoginOnBoardingModel>(ModelConsts.Login);
    private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);

    private MainController _MainController => GetController<MainController>(ModelConsts.Main);
    private uint _reconnectSession;
    private bool isVerifyToken = false;//是否是快速登录
    private bool _autoEnterGameSrv = true;
    public bool isIDBinded { get; private set; } = false;
    public string phone { get; private set; } = "";
    private RecommendCardModel _recommendCardModel => GetModel<RecommendCardModel>(ModelConsts.RecommendCard);
    // private LearnPathController _learnPathController => GetController<LearnPathController>(ModelConsts.LearnPath);
    private ChatStartController chatStartCtrl => GetController<ChatStartController>(ModelConsts.ChatStartController);
    private bool _isFirstLogin = true;
    
    private bool _isOnRequest = false;
    public bool isOnRequestFirstTask{ get; private set; } = false;
    
    private string _timerUnlockLogin = String.Empty;
    private string _tcpAddress;
    private int _tcpPort;

    private string timerKey=string.Empty;
    
    private float _lastEnterHomepageTime = -100f;
    private const float EnterHomepageInterval = 1f; //间隔

    /// <summary>
    /// 是否是新用户
    /// </summary>
    public bool IsNewPlayer = true;
    public override void OnUIInit()
    {
        RegisterUI(new LoginUI(UIConsts.Login));
        RegisterUI(new LoginCNUI(UIConsts.LoginCN));     
        RegisterUI(new OnBoardUI(UIConsts.OnBoardUI));
        RegisterUI(new SignUpUI(UIConsts.SignUpUI));
        RegisterUI(new SelectRoleUI(UIConsts.SelectRoleUI));
        RegisterUI(new OnBoardInfoUI(UIConsts.OnBoardInfoUI));
        //RegisterUI(new CreateRoleUI(UIConsts.CreateRole)); //选角色的入口UI已经禁用
        
        InputCom.Bind();
        isLoginComplete = false;
    }

    public override void OnInit()
    {
        this.RegisterModel(new LoginOnBoardingModel());
        MsgManager.instance.RegisterCallBack<SC_LoginAck>(this.OnSCLoginAck);

        MsgManager.instance.RegisterCallBack<SC_EnterGameZoneAck>(this.OnSCEnterGameZoneAck);
        MsgManager.instance.RegisterCallBack<SC_HeartBeatAck>(this.OnSCHeartBeatAck);
        MsgManager.instance.RegisterCallBack<SC_GetUserInfoAck>(OnSCGetUserInfoAck);
        MsgManager.instance.RegisterCallBack<SC_GetTaskInfoAck>(OnOpenOnboarding);
        MsgManager.instance.RegisterCallBack<SC_GetAudioForFlowAck>(OnBoardingAudio);
        MsgManager.instance.RegisterCallBack<SC_LeaveGSAck>(OnLeaveGSAck);
        MsgManager.instance.RegisterCallBack<SC_QueryChatUserListAck>(OnSCQueryChatUserListAck);
        
        // GSDKManager..onSDKLogin += jsonStr => this.OnAppleLogin(jsonStr);
    }



    private void OnOpenOnboarding(SC_GetTaskInfoAck msg)
    {
        _recommendCardModel.SetCurTaskID(msg.task_info.task_id);
       
        SendNotification(OnBoardingCallEvent.OnTaskRecommen,msg);
    }

    public async void ExecuteEnterGame()
    {
        if (this._isFirstLogin)
        {

            //Debug.Log("do Cfg.SetFirstLanguage:"+ I18N.GetSystemLanguage().ToString());
            //完成登录前 如果抛异常时，如果需要依赖母语来展示什么的话，用本机系统语言
            //登录完成后 母语用用户原来存储的语言
            I18NTool.InitLanguageList();
            I18N.inst.Init();
            // I18N.inst.MotherLanguage = I18NTool.ConvertSupportedMotherLangIntoLanguageCode(I18N.GetSystemLanguage());
            this._isFirstLogin = false;

            //新埋点：点开app
            DataDotStartupUser dot = new DataDotStartupUser();
            DataDotMgr.Collect(dot);

            this.TrySetDebugSrv();

            //启用热更后，不需要在此位置进行冷更检测
            //var versionResult = await this.CheckVersion();
            //if (!versionResult) return;

            //首次执行时附加 闲时性能调节器
            PerformanceSaver.instance.UseNormalFPS();
        }
        
#if PHONE4CN && !UNITY_EDITOR
        DoLogin();
#else
        if (AppConst.AppChannel == LoginChannel.debug)
        {
            //For EditorDebug
            this.DoDebugLogin();
        }
        else
        {
            //For normal app
            DoLogin();
        }    
#endif
    }

    public void LogOut()
    {
        if (AppConst.AppChannel == LoginChannel.debug)
        {
            //For EditorDebug
            this.DoDebugLogin(1);
        }
        else
        {
            OpenLoginUI_OnLogOut();
            DoLogin();
        }
    }

    
    private void TrySetDebugSrv()
    {
        if (AppConst.IsPublicDebugSrv)
        {
            string debugSrvHost = LocalCfgMgr.instance.GetGlobal("debugSrvHost");
            if (!string.IsNullOrEmpty(debugSrvHost))
                AppConst.LoginSrv = debugSrvHost;
        }
    }

    public override void OnEnterGame()
    {
        if (_isOnRequest)
            return;
        _isOnRequest = true;
        base.OnEnterGame();
        //PurchasingManager.instance.InitializePurchasing();
        MsgManager.instance.SendMsg(new CS_GetUserInfoReq() , (type, message) =>
        {
            Debug.Log("resend GetUserInfoReq");
            TimerManager.instance.RegisterTimer((c) =>
            {
                _isOnRequest = false;
                OnEnterGame();
            }, 3000, 1);
        });
        //MsgManager.instance.SendMsg(new CS_GetInProgressDialogReq());

        //GetController<ChatController>(ModelConsts.Chat).TestProgressDialogAck();
        //GetController<ChatController>(ModelConsts.Chat).TestCarrerProgressDialogAck();
    }


   

    public void BackToLogin()
    {
        NetManager.instance.Close();
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Token, "");
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Id, "");
        this.LogOut();
    }

    private bool CheckTokenIsExpired()
    {
        long timestamp = LocalCfgMgr.instance.GetGlobal(LoginConst.Key_User_Token_Timestamp, 0);
        if (timestamp <= 0 || TimeExt.LocalTimestamp - timestamp > TimeExt.OneDay * 10)
        {
            return true;
        }
        return false;
    }


    private void DoLogin()
    {
        string token = LocalCfgMgr.instance.GetGlobal(LoginConst.Key_User_Token);
        if (CheckTokenIsExpired())
        {
            token = null;
        }
        string user_id_str = LocalCfgMgr.instance.GetGlobal(LoginConst.Key_User_Id);
        string lastState = LocalCfgMgr.instance.GetGlobal(LoginConst.Key_LastLogin_State);
        
        Debug.Log($"======lastState = {lastState} user_id = {user_id_str} token = {token}");

        if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(user_id_str) || (lastState != "apple" && lastState != "google" && lastState != "phone" && lastState != "email"))
        {
            TimerLogHelper.Ins.SaveTimeStart(TimerLogHelper.TimeType.PlayerLogin);
            SlowStartLogHelper.Ins.Pause();
            //刷新Token并登录
            OpenLoginUI();
        }
        else
        {
            Debug.Log($"======auto login");
            
            long user_id = long.Parse(user_id_str);
            string loginJson = JsonMapper.ToJson(new LoginVerifytokenReq
            {
                user_id = user_id,
                access_token = token
            });
            string url = "/account/verifytoken";
            string totalUrl = AppConst.LoginSrv + url;
            bool usePreLoginData = LoginPreprocessor.Ins.IsSame(totalUrl, loginJson);
            // usePreLoginData = false;
            if (usePreLoginData)
            {
                if (LoginPreprocessor.Ins.HasData(out string data, RespLoginToLoginSrv))
                {
                    Debug.Log("=== LoginPreprocessor.Ins.HasData true: RespLoginToLoginSrv ===");
                    RespLoginToLoginSrv(data);//缓存命中时
                }
                else {
                    Debug.Log("=== LoginPreprocessor.Ins.HasData false ===");
                }
            }
            else
            {
                //直接通过网关刷新Token并登录
                this.DoLoginToLoginSrv(url, loginJson);
                LoginPreprocessor.Ins.SaveLoginCache(totalUrl);
            }

            LocalCfgMgr.instance.SetGlobal(LoginConst.Key_LastLogin_State, "apple");
        }
    }

    //For EditorDebug
    private void DoDebugLogin(int state = 0)
    {
        OpenLoginUI();
    }

    public void DoOnBoardLoginFromUI(string inputPhone = null)
    {
        if (AppConst.AppChannel == LoginChannel.debug)
        {
            if (string.IsNullOrEmpty(inputPhone))
            {
                GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToast("内部测试请输入手机号！");
                return;
            }
            else
            {
                phone = inputPhone; 
            }
        }

        this.DoLoginToLoginSrv("/account/login", JsonMapper.ToJson(new LoginReq
        {
            type = "onboarding",
            device_id = _boardingModel.DeviceID,
            version = AppConstExt.LogicVersion,
            system_lang = I18NTool.ConvertSupportedMotherLangIntoLanguageCode(I18N.GetSystemLanguage()).ToString(),
            appsflyer_id = AFHelper.GetAppFlyerId(),
            firebase_id = SDKManager.instance.firebaseAppInstanceId,
            country_code = OsFuncAdapter.Ins.GetCountryCode(),
        }), true);
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_LastLogin_State, "onboarding");
    }

    // 从非登陆页面登陆
    public void DoOnBoardLoginFromGame()
    {
        if (AppConst.AppChannel == LoginChannel.debug &&  string.IsNullOrEmpty(phone)) return;
        if (!isIDBinded)
        {
            this.DoLoginToLoginSrv("/account/login", JsonMapper.ToJson(new LoginReq
            {
                type = "onboarding",
                device_id = _boardingModel.DeviceID,
                version = AppConstExt.LogicVersion,
            	system_lang = I18NTool.ConvertSupportedMotherLangIntoLanguageCode(I18N.GetSystemLanguage()).ToString(),
                appsflyer_id = AFHelper.GetAppFlyerId(),
                firebase_id = SDKManager.instance.firebaseAppInstanceId,
                country_code = OsFuncAdapter.Ins.GetCountryCode(),
            }), true);
        }
        else
        {
            AppleLogin();
        }
    }

    public void DoUILoginStart(string debugAccount , bool isDebug = false)
    {
        if (isDebug)
        {
            if (string.IsNullOrEmpty(debugAccount)) return;
            phone = debugAccount;
            long id = _mainModel.userID;
            
            this.DoLoginToLoginSrv("/account/login", JsonMapper.ToJson(new LoginReq
            {
                type = "debug",
                phone = debugAccount,
                device_id = _boardingModel.DeviceID,
                user_id = id,
                version = AppConstExt.LogicVersion,
            	system_lang = I18NTool.ConvertSupportedMotherLangIntoLanguageCode(I18N.GetSystemLanguage()).ToString(),
                appsflyer_id = AFHelper.GetAppFlyerId(),
                firebase_id = SDKManager.instance.firebaseAppInstanceId,
                country_code = OsFuncAdapter.Ins.GetCountryCode(),
            }));
            LocalCfgMgr.instance.SetGlobal(LoginConst.Key_LastLogin_State, "debug");
        }
        else
        {
            #region 接 signInWithApple
            AFHelper.Click_Apple_id();
            SetLoginUIBtnStartDisable(false);
            SignInAdapter.SignIn(OnLoginSuccess,OnLoginFail);
            #endregion
        }
    }
    
    public void DoAppleSignInStart()
    {
        AFHelper.Click_Apple_id();
        SetLoginUIBtnStartDisable(false);
        SignInAdapter.SignIn_Apple(OnLoginSuccess,OnLoginFail);
    }
    public void DoGoogleSignInStart()
    {
        AFHelper.Click_Apple_id();
        SetLoginUIBtnStartDisable(false);
        SignInAdapter.SignIn_Google(OnLoginSuccess,OnLoginFail);
    }

    public void OnLoginFail()
    {
        SetLoginUIBtnStartDisable(true);
    }

    public void OnLoginSuccess(string userID , string token , string email,string planform)
    {
        DelayUnlockLogin();

        long id = _mainModel.userID;


        Debug.Log($"OnLoginSuccess - userID = {userID} token = {token} email = {email} planform = {planform} id = {id}");
        if (string.IsNullOrEmpty(userID) || string.IsNullOrEmpty(token))
        {
            Debug.Log(token);
            Debug.Log(userID);
            return;
        }
        
        TimerLogHelper.Ins.SaveTimeEnd(TimerLogHelper.TimeType.PlayerLogin);
        SlowStartLogHelper.Ins.ReStart();
        
        //AFHelper.Register_success();
        LoginReq req = new LoginReq
        {
            type = planform,
            device_id = _boardingModel.DeviceID,
            user_id = id,
            identifier = userID,
            credential = token,
            version = AppConstExt.LogicVersion,
            email = email,
            system_lang = I18NTool.ConvertSupportedMotherLangIntoLanguageCode(I18N.GetSystemLanguage()).ToString(),
            appsflyer_id = AFHelper.GetAppFlyerId(),
            firebase_id = SDKManager.instance.firebaseAppInstanceId,
            country_code = OsFuncAdapter.Ins.GetCountryCode(),
        };

        this.DoLoginToLoginSrv("/account/login", JsonMapper.ToJson(req));
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_LastLogin_State, planform);
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_Apple_Token, token);
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_Apple_Id, userID);
        AFDots.Cut_Register_apple_success();
        Debug.Log($"======lastState = {planform} user_id = {userID} token = {token}");
    }
    
    public void OnLoginSuccess_Mail(string email,string psw)
    {
        DelayUnlockLogin();
        
        string planform = "email";
        
        TimerLogHelper.Ins.SaveTimeEnd(TimerLogHelper.TimeType.PlayerLogin);
        SlowStartLogHelper.Ins.ReStart();

        LoginReq req = new LoginReq
        {
            type = planform,
            device_id = _boardingModel.DeviceID,
            version = AppConstExt.LogicVersion,
            email = email,
            password = psw,
            system_lang = I18NTool.ConvertSupportedMotherLangIntoLanguageCode(I18N.GetSystemLanguage()).ToString(),
            appsflyer_id = AFHelper.GetAppFlyerId(),
            firebase_id = SDKManager.instance.firebaseAppInstanceId,
            country_code = OsFuncAdapter.Ins.GetCountryCode(),
        };

        this.DoLoginToLoginSrv("/account/login", JsonMapper.ToJson(req));
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_LastLogin_State, planform);
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Email, email);
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Email_Password, psw);
    }

    private void LoginFailFromMail()
    {
        if (LoginUIIsShow)
        {
            (GetUI<LoginUI>(UIConsts.Login)).LoginFailFromMail();
        }
    }


    //延迟解锁login状态 因为等待服务器回复的过程中用户可能会点击按钮 通过几轮测试比较安全的时间是5s
    private void DelayUnlockLogin()
    {
        if (!string.IsNullOrEmpty(_timerUnlockLogin))
        {
            TimerManager.instance.UnRegisterTimer(_timerUnlockLogin);
            _timerUnlockLogin = string.Empty;
        }
        _timerUnlockLogin = TimerManager.instance.RegisterTimer((a) =>
        {
            if (LoginUIIsShow)
            {
                SetLoginUIBtnStartDisable(true);
            }
        }, 5000, 1);
        
    }

    public void AppleLogin()
    {
        if (AppConst.AppChannel == LoginChannel.debug)
        {
            if (string.IsNullOrEmpty(phone)) return;
            this.DoLoginToLoginSrv("/account/login", JsonMapper.ToJson(new LoginReq
            {
                type = "debug",
                phone = phone,
                device_id = _boardingModel.DeviceID,
                user_id = 0,
                version = AppConstExt.LogicVersion,
                system_lang = I18NTool.ConvertSupportedMotherLangIntoLanguageCode(I18N.GetSystemLanguage()).ToString(),
                appsflyer_id = AFHelper.GetAppFlyerId(),
                firebase_id = SDKManager.instance.firebaseAppInstanceId,
                country_code = OsFuncAdapter.Ins.GetCountryCode(),
            }));
            LocalCfgMgr.instance.SetGlobal(LoginConst.Key_LastLogin_State, "debug");
        }
        else
        {
            string appleUserID = LocalCfgMgr.instance.GetGlobal(LoginConst.Key_Apple_Id);
            string appleToken = LocalCfgMgr.instance.GetGlobal(LoginConst.Key_Apple_Token);
            string email = LocalCfgMgr.instance.GetGlobal(LoginConst.Key_User_Email);


            DoLoginToLoginSrv("/account/login", JsonMapper.ToJson(new LoginReq
            {
                type = SignInAdapter.Platform,
                device_id = _boardingModel.DeviceID,
                user_id = 0,
                identifier = appleUserID,
                credential = appleToken,
                version = AppConstExt.LogicVersion,
				email = email,
                system_lang = I18NTool.ConvertSupportedMotherLangIntoLanguageCode(I18N.GetSystemLanguage()).ToString(),
                appsflyer_id = AFHelper.GetAppFlyerId(),
                firebase_id = SDKManager.instance.firebaseAppInstanceId,
                country_code = OsFuncAdapter.Ins.GetCountryCode(),
            }));
            LocalCfgMgr.instance.SetGlobal(LoginConst.Key_LastLogin_State, "apple");
        } 
    }

    private async void DoPassTimezone()
    {
        var countryCode = OsFuncAdapter.Ins.GetCountryCode();
        
        var zoneInfo = new ZoneInfo
        {
            user_id = long.Parse(LocalCfgMgr.instance.GetGlobal(LoginConst.Key_User_Id)),
            time_zone = TimeZone.CurrentTimeZone.GetUtcOffset(DateTime.Now).ToString(),
            country = countryCode
        };

        var result = await GHttpManager.instance.PostAsync(AppConst.LoginSrv + "/account/setuserregionalinfo",
            JsonMapper.ToJson(zoneInfo));
        if (result.code == 200)
        {
            Debug.Log($"Passed Time Zone:{zoneInfo.time_zone}");
        }
    }

    private async UniTask DoPassMTPushInfo()
    {
        GameObject receiverMTPush = GameObject.Find("GameMgr");
        if (receiverMTPush == null ||  receiverMTPush.GetComponentsInChildren<SDKManager>().Length == 0)
        {
            Debug.LogError("SDKManager is not exist!");
            return;
        }
        
        var rkStr = AppRegionInfo.GetCurrRegionKeyAsStr();
        var appKey = AppConst.APPKEY_TALKIT_FR;
        
        if (rkStr == AppRegionKey.jpTest.ToString())
        {
#if UNITY_IOS && !UNITY_EDITOR
            //仅日本体验环境走国内版极光
            //JPushBinding.SetDebug(true);
            JPushBinding.Init(receiverMTPush.name);
            string registrationId = JPushBinding.GetRegistrationId();
            JPushBinding.ResetBadge();
            VFDebug.Log("JPush initialized with rid:" + registrationId);
            VFDebug.Log("JPush initialized with advertisingId" + Device.advertisingIdentifier);
            if (!string.IsNullOrEmpty(registrationId))
            {
                MsgManager.instance.SendMsg(new CS_RegisterUserDeviceReq()
                {
                    device_id = registrationId,
                    device_type = PB_DeviceType.PB_DeviceType_IOS,
                    channel = PB_Channel.PB_Channel_Engagelab,
                    device_uk = Device.advertisingIdentifier,
                });
            }
#elif UNITY_ANDROID && !UNITY_EDITOR
            JPushBinding.Init(receiverMTPush.name);
            JPushBinding.RequestPermission();
            string registrationId = JPushBinding.GetRegistrationId();
            VFDebug.Log("JPush initialized with rid:" + registrationId);
            VFDebug.Log("JPush initialized with advertisingId" + SystemInfo.deviceUniqueIdentifier);
            if (!string.IsNullOrEmpty(registrationId))
            {
                MsgManager.instance.SendMsg(new CS_RegisterUserDeviceReq()
                {
                    device_id = registrationId,
                    device_type = PB_DeviceType.PB_DeviceType_Android,
                    channel = PB_Channel.PB_Channel_Engagelab,
                    device_uk = SystemInfo.deviceUniqueIdentifier,
                });
            }
#endif
        }
        else if (rkStr == AppRegionKey.eu.ToString())
        {
            Debug.Log("MTPush start initialize.");
            Debug.Log("MTPush appKey:" + appKey);
#if UNITY_ANDROID && !UNITY_EDITOR
            MTPushBinding.InitMTPushAndroid(receiverMTPush.name);
#endif

#if UNITY_IOS && !UNITY_EDITOR
        // MTPushBinding.ConfigDebugMode(true);
        SDKManager.instance.RegisterAction(SDKEvents.OnReSendMTPushRid, OnMTPushRestBadge);
        MTPushBinding.InitMTPushIos(receiverMTPush.name, appKey, false, "talkit", false);
        // TimerManager.instance.RegisterTimer((a) =>
        // {
        //     MTPushBinding.ResetNotificationBadge();
        // }, 5000);
        string registrationId = MTPushBinding.GetRegistrationId();
        VFDebug.Log("MTPush initialized with rid:" + registrationId);
        VFDebug.Log("MTPush initialized with advertisingId" + Device.advertisingIdentifier);
        if (!string.IsNullOrEmpty(registrationId))
        {
            MsgManager.instance.SendMsg(new CS_RegisterUserDeviceReq()
            {
                device_id = registrationId,
                device_type = PB_DeviceType.PB_DeviceType_IOS,
                channel = PB_Channel.PB_Channel_Engagelab,
                device_uk = Device.advertisingIdentifier,
            });
        }
        else
        {
            SDKManager.instance.RegisterAction(SDKEvents.OnReSendMTPushRid, OnReSendMTPushRid);
        }
#elif UNITY_ANDROID && !UNITY_EDITOR
        MTPushBinding.InitMTPushAndroid(receiverMTPush.name);
        string registrationId = MTPushBinding.GetRegistrationId();
        VFDebug.Log("MTPush initialized with rid:" + registrationId);
        VFDebug.Log("MTPush initialized with advertisingId" + SystemInfo.deviceUniqueIdentifier);
        if (!string.IsNullOrEmpty(registrationId))
        {
            MsgManager.instance.SendMsg(new CS_RegisterUserDeviceReq()
            {
                device_id = registrationId,
                device_type = PB_DeviceType.PB_DeviceType_Android,
                channel = PB_Channel.PB_Channel_Engagelab,
                device_uk = SystemInfo.deviceUniqueIdentifier,
            });
        }
        else
        {
            SDKManager.instance.RegisterAction(SDKEvents.OnReSendMTPushRid, OnReSendMTPushRid);
        }
#endif
        }
    }

    private void OnMTPushRestBadge(object param)
    {
#if UNITY_IOS && !UNITY_EDITOR
        MTPushBinding.ResetNotificationBadge();
#endif
    }
  
    //这个地方不要删 不是没有引用是因为有宏隔离了
    private void OnReSendMTPushRid(object param)
    {
#if UNITY_IOS && !UNITY_EDITOR
        string registrationId = MTPushBinding.GetRegistrationId();
        VFDebug.Log("MTPush send rid:" + registrationId);
        if (!string.IsNullOrEmpty(registrationId))
        {
            MsgManager.instance.SendMsg(new CS_RegisterUserDeviceReq()
            {
                device_id = registrationId,
                device_type = PB_DeviceType.PB_DeviceType_IOS,
                channel = PB_Channel.PB_Channel_Engagelab,
            });
        }
#elif UNITY_ANDROID && !UNITY_EDITOR
        string registrationId = MTPushBinding.GetRegistrationId();
        VFDebug.Log("MTPush send rid:" + registrationId);
        if (!string.IsNullOrEmpty(registrationId))
        {
            MsgManager.instance.SendMsg(new CS_RegisterUserDeviceReq()
            {
                device_id = registrationId,
                device_type = PB_DeviceType.PB_DeviceType_Android,
                channel = PB_Channel.PB_Channel_Engagelab,
                device_uk = SystemInfo.deviceUniqueIdentifier,
            });
        }
#endif
        //todo android
    }    

    //请求通过登录服登录
    private async void DoLoginToLoginSrv(string api, string apiData ,bool isOnboarding = false)
    {
        TimerLogHelper.Ins.SaveTimeStart(TimerLogHelper.TimeType.HttpLogin);
        Debug.Log($"DoLoginToLoginSrv!!!");
        var result = await GHttpManager.instance.PostAsync(AppConst.LoginSrv + api, apiData);//, (code, data) =>
        TimerLogHelper.Ins.SaveTimeEnd(TimerLogHelper.TimeType.HttpLogin);
        if (result.code == 200)
        {
            Debug.Log("=== DoLoginToLoginSrv : RespLoginToLoginSrv ===");
            RespLoginToLoginSrv(result.data , isOnboarding);
        }
        else
        {
            Debug.Log($"LoginSrv: login srv cannot connect. code:{result.code}");
            OnShowNetErrorConfirm();
        }
    }
    
    private void RespLoginToLoginSrv(string data, bool isOnboarding = false)
    {
        LoginResp resp = null;
        try
        {
            resp = JsonMapper.ToObject<LoginResp>(data);
        }
        catch (Exception e)
        {
            Debug.LogError(e.ToString());
            Debug.LogError($"LoginSrv: login srv fail to parse resp.data={data}");
            resp = null;
        }

        AFHelper.login_complete();
        this.isLoginComplete = true;
        AFHelper.af_login();
        StartUpAmazonBI.cut_login_complate();
        
        
        if (resp == null)
        {
            OnShowNetErrorConfirm();
        }
        else if (resp.error_code == 0)
        {
            if (resp.timestamp > 0)
            {
                Debug.Log("SetMyTime LoginToLoginSrv:" + resp.timestamp);
                TimeExt.SetServerTime(resp.timestamp);
            }

            if (string.IsNullOrEmpty(resp.cur_first_lang))
            {
                Debug.LogError("LoginResp - cur_first_lang is null");
            }
            else
            {
                I18N.inst.OnLogin(resp.cur_first_lang, resp.cur_foreign_lang);
            }

            // this.SetSrvFirstLanguage(I18NTool.ConvertSupportedMotherLangIntoLanguageCode(I18N.GetSystemLanguage()),resp.user_id);
            AFHelper.SetAFUid(resp.user_id);
            NetManager.instance.Close();
            PushManager.instance.StopWork();
            

            //GetController<ShopController>(ModelConsts.Shop).ReqShopInfo();
            //Game.PurchasingManager.instance.InitializePurchasing();
            if (!isOnboarding)
            {
                isIDBinded = true;
            }
            timerKey = string.Empty;
            //已经忘记这里要延迟处理的原因了
            timerKey = TimerManager.instance.RegisterTimer(c =>
            {
                //防御性编程 强制增加一个时间锁，之前有概率进入两轮？
                if (timerKey != string.Empty) {
                    //刷新本地Token
                    LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Token, resp.access_token);
                    LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Token_Timestamp, TimeExt.LocalTimestamp);
                    LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Id, resp.user_id.ToString());

                    string loginJson = JsonMapper.ToJson(new LoginVerifytokenReq
                    {
                        user_id = resp.user_id,
                        access_token = resp.access_token
                    });
                    LoginPreprocessor.Ins.SaveLoginCacheLoginJson(loginJson);

                    SetOnBoardingState(resp);

                    TimerLogHelper.Ins.AddLog("DoTLogin0");//DoTrullyLogin start

                    ProcessLoginRespData(resp);
                    GetController<WebViewController>(ModelConsts.WebView).SetConfigAndPreload(resp.h5_config);
                    TimerManager.instance.UnRegisterTimer(timerKey);
                    timerKey = string.Empty;
                }
                
            }, 100, 1);
        }
        else if (resp.error_code == 10025) //token过期
        {
            LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Token, "");
            OpenLoginUI();
        }
        else if (resp.error_code == 10088)
        {
            string errorMsg1 = LoginConst.GetLoginError(resp.error_code);
            if (string.IsNullOrEmpty(errorMsg1))
                errorMsg1 = I18N.inst.MoStr(LoginConst.GetErrorKey());
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToast(errorMsg1);
            DoUILoginStart(AppConst.AppChannel == LoginChannel.debug ? phone : "", false);
            // 如果是onboarding帐号遗留的事件堆栈需要清理
            GetController<BottomBubbleController>(ModelConsts.BottomBubbleController).ClearEventQueue();

        }
        else if (resp.error_code == NetErrorCode.ERROR_PASSWORD_WRONG || 
                 resp.error_code == NetErrorCode.ERROR_EMAIL_NOT_REGISTERED)
        {
            string errorMsg1 = LoginConst.GetLoginError(resp.error_code);
            if (!string.IsNullOrEmpty(errorMsg1))
            {
                GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToast(errorMsg1);
            }
        }
        else
        {
            Debug.Log($"LoginSrv: login srv resp error code. code:{resp.error_code}");
            string errorMsg = LoginConst.GetLoginError(resp.error_code);
            if (string.IsNullOrEmpty(errorMsg))
                errorMsg = I18N.inst.MoStr("common_netError");
            GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open(errorMsg, () => { }, null, 1);
        }
    }

    private void SetOnBoardingState(LoginResp resp)
    {
        TaskRecord task_node;
        try
        {
            task_node = Enum.Parse<TaskRecord>(resp.task_node);
        }
        catch (ArgumentException)
        {            
            // 失传的老用户存档点一概当作完成            
            VFDebug.Log($"OnBoard Step: convert {resp.task_node} to Finish");
            task_node = TaskRecord.Finish;
        }

        if (task_node != TaskRecord.Finish)
        {
            _autoEnterGameSrv = false;
            AFHelper.Appear_Onboarding();

            if (task_node == TaskRecord.CreateRole)
            {
                AFHelper.SetIsNewPlayer(resp.user_id);
            }
        }
        else
        {
            AFHelper.Appear_Onboarding_finished();
        }

        _boardingModel.SetCurOnBoardingStateFromServer(task_node);
        ChangerOnBoardingState(task_node);
    }

    private void ChangerOnBoardingState(TaskRecord respTaskNode)
    {
        var be = _MainController.BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (be)//处于onboard 场景
        {
            //--不清楚什么情况会走到这里
            switch (respTaskNode)
            {
                case TaskRecord.None:
                case TaskRecord.CreateRole:
                case TaskRecord.Dialogue:
                case TaskRecord.ShowPay:
                    GetController<MainController>(ModelConsts.Main).SetOnBoardToSelect();
                    HideLoginUI();
                    GetUI(UIConsts.OnBoardUI).Show();
                    break;
            }
        }
        else
        {
            switch (respTaskNode)
            {
                case TaskRecord.None:
                case TaskRecord.CreateRole:
                case TaskRecord.Dialogue:
                case TaskRecord.ShowPay:
                    // GetUI<LoginUI>(UIConsts.Login).OnBtnLoginUpClick();
                    
                    GotoOnBoard();
                    HideLoginUI();
                    break;
            }
        }
    }

    /// <summary>
    /// 只有进入onboarding时候 走这个 确定玩家进入游戏时候 tab
    /// </summary>
    private void OnBoardingAbTest()
    {
        AbTestUtil.ReqAbTestValue("onboarding-entrance",
            msg =>
            {
                var mtUI= UIManager.instance.GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage);//注意 这时的 this._mtUI可能还未赋值不要用
                string type = msg.data.value;
                VFDebug.Log("onboardingmsg : onboarding-entrance: " + type);
                OnBoardFlowController flowCtrl = GetController<OnBoardFlowController>(ModelConsts.OnBoardFlow);
                flowCtrl.EnterGameTabAB = type;
                if (type == "COURSE")
                {
                    GameEntry.MainPathC.SetOnBoardingDatas(); //自动打开课程
                    mtUI.InitalTabIdx = 0;
                }
                else if(type == "EXPLORE")
                {
                    mtUI.InitalTabIdx = 1;
                }
            }
        );

        AbTestUtil.ReqAbTestValue("onboarding-paywall-ui",
            msg =>
            {
                GetModel<ShopModel>(ModelConsts.Shop).SetOnBoardPaywallModel(msg.data.value);
            });
    }

    /// <summary>
    /// 不经过 Onboarding 进入游戏，也就是老用户进入有 走这个ab  确定玩家进入游戏时候 tab
    /// </summary>
    private void NoOnBoardingAbTest()
    {
        IsNewPlayer = false;
        //abtest 
        AbTestUtil.ReqAbTestValue("explore_toggle_ab",
            msg =>
            {
                bool isShowExplore = msg.data.on;
                VFDebug.Log("onboardingmsg : explore_toggle_ab: " + isShowExplore);
                var mtUI= UIManager.instance.GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage);
                //经过和红彬沟通 启动这个 ab ，同时不对法国进行特殊处理
                mtUI.InitalTabIdx = isShowExplore?1:0;
// #if MACRO_FR_PROD || MACRO_FR_PRE
//
//                 mtUI.InitalTabIdx = 1;//命中使用exp作为首页的ab
//
//                 //mtUI.SetTabVisible(TabIndex.Explore, isShowExplore);
// #endif
            }
        );
    }
    

    private void GotoOnBoard()
    {
        if (_boardingModel.CurOnBoardingState == TaskRecord.None 
            || _boardingModel.CurOnBoardingState == TaskRecord.CreateRole 
            || _boardingModel.CurOnBoardingState == TaskRecord.Dialogue
            || _boardingModel.CurOnBoardingState == TaskRecord.ShowPay
            )
        {
            AFHelper.Onboarding_start();
            LoginBIHelper.Appear_Onboarding_page();
            var be = _MainController.BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
            if (!be)
            {
                GameEntry.SceneC.LoadSceneById(Cfg.T.TbConst.OnBoardingSceneId);
            }
        }
    }



    public async UniTask SetSrvFirstLanguage(LanguageCode languageCode,long userID)
    {
        SetUserLangReq apiData = new SetUserLangReq()
        {
            user_id = userID,
            first_lang = I18NTool.ToServerStr(languageCode),
        };
        var result = await GHttpManager.instance.PostAsync(AppConst.LoginSrv + "/account/setuserlang", JsonMapper.ToJson(apiData), allowMaxRetryNum:3);
        if (result.code != 200)
        {
            Debug.Log($"SetSrvFirstLanguage: . code:{result.code}");
            OnShowNetErrorConfirm();
            return;
        }
        Debug.Log("SetSrvFirstLanguage");
    }
    
    //真正的登录，同时登录tcp和grpc
    private void ProcessLoginRespData(LoginResp resp)
    {
        long userID = resp.user_id;
        string token = resp.access_token;
        string talkit_gateway = resp.talkit_gateway;
        string game_gateway = resp.game_gateway;
        string first_lang = resp.cur_first_lang;
        string foreign_lang = resp.cur_foreign_lang;
        
        Debug.Log("==== ProcessLoginRespData=> DoTrullyLogin ====");
        Debug.Log("==>1 LoginController firstLang " + first_lang + ", foreignLang " + foreign_lang);
        string version = AppConstExt.LogicVersion;

#if !UNITY_EDITOR
        CrashSightAgent.SetUserId(userID.ToString());
#endif
        _mainModel.SetUserID(userID);
        _mainModel.SetToken(token);

        ClientSQLiteManager.Instance().InitClientDB(userID);
        DataDotMgr.user_id = userID;

        I18N.inst.OnLogin(first_lang, foreign_lang);
      
        if (!talkit_gateway.StartsWith("http://"))
            talkit_gateway = "http://" + talkit_gateway;
        // talkit_gateway = "http://10.80.0.238:8080";
        Debug.Log("GRPCManager Start");
        GRPCManager.instance.Start(talkit_gateway, token);
        //链接探索grpc
        Notifier.instance.SendNotification(NotifyConsts.ExploreNetConect);
        //onboarding 对话数据
        Debug.Log("onboarding 对话数据 申请");
        Notifier.instance.SendNotification(NotifyConsts.Onboard_AskPreLoadData);
        
        string[] gameWayArr = game_gateway.Split(":");
        if (gameWayArr.Length != 2)
        {
            OnShowNetErrorConfirmUIOpen(I18N.inst.MoStr("common_netError"), null, null, 0);
            Debug.LogError($"Login: game_gateway format error. address: {game_gateway}");
            return;
        }

        _tcpAddress = gameWayArr[0];
        _tcpPort = int.Parse(gameWayArr[1]);

        PushManager.instance.StartGRPC($"http://{_tcpAddress}:{_tcpPort - 1000}");

        this.StartTCPConn();//需要_tcpAddress，_tcpPort赋值后调用

        DoPassTimezone();
        DoPassMTPushInfo();
        
        SetRoleInfo(resp);
    }

    public void StartTCPConn() {
        return;
        TimerLogHelper.Ins.SaveTimeStart(TimerLogHelper.TimeType.TcpLogin);
        if (string.IsNullOrEmpty(_tcpAddress) || _tcpPort==0) {
            Debug.LogError("Fail to StartTCPConn, wrong parameters");
            return;
        }
        NetManager.instance.Connect(_tcpAddress, _tcpPort, () =>
        {
            //连接成功时触发，触发一轮LoginReq
            OnReConnectCallback(_mainModel.userID, _mainModel.toKen, AppConstExt.LogicVersion);
        }, () =>
        {
            OnReConnectCallback(_mainModel.toKen, AppConstExt.LogicVersion);
        }, () =>
        {

            var sceneCtrl = ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene);
            if (sceneCtrl != null && sceneCtrl.IsUnderWorldScene())
            {
                Debug.Log("在世界场景中 retry耗尽");
                //仅在世界场景中触发强制重连弹窗，重试10次
                OnShowNetErrorConfirmUIOpen(I18N.inst.MoStr("common_reconnect"),
                    () => { NetManager.instance.ForceReConnect(); }, null, 1);
            }
            else
            {
                Debug.Log("在非世界场景中 retry耗尽->关闭");
                Debug.LogWarning("TCP Error NotRetry");
                NetManager.instance.Close();
            }
        }, () =>
        {
            //心跳超时
            Debug.Log("Error: 心跳超时.");
            NetManager.instance.OnClose();
        });
    }
    private void SetRoleInfo(LoginResp resp)
    {
        long player_id = resp.player_id;
        string player_name = resp.player_name;
        string style = resp.style;
        string roleUniqueName = resp.roleUniqueName;

        if (int.TryParse(style, out var styleInt))
        {
            _boardingModel.SetStyle(styleInt);
        }
        else
        {
            Debug.Log($"loginResp error  styleInt = {styleInt}");
        }

        LocalCfgMgr.instance.SetRoleCfgKey(GameUtils.MD5Sign(player_id.ToString()));
        _mainModel.SetPlayerID(player_id);
        _mainModel.SetPlayerName(player_name);
        _mainModel.SetRoleUniqueName(roleUniqueName);
        string lastState = LocalCfgMgr.instance.GetGlobal(LoginConst.Key_LastLogin_State);

        if (_boardingModel.CurOnBoardingState == TaskRecord.Finish ||
            _boardingModel.CurOnBoardingState == TaskRecord.ShowDiamond ||
            _autoEnterGameSrv)
        {
            // 如果完成onboarding走真登陆
            if (lastState == "onboarding" && _boardingModel.CurOnBoardingState == TaskRecord.Finish)
            {
                OpenLoginUI();
                WidgetUtils.UpdateAndRefreshWidget();
                return;
            }

            NoOnBoardingAbTest();
            _boardingModel.SetRoleCount(1);
            this.EnterHomepageAfterLogin(); //不走世界时就直接进入
        }
        else if(_boardingModel.CurOnBoardingState != TaskRecord.CreateRole 
                && _boardingModel.CurOnBoardingState != TaskRecord.Dialogue
                && _boardingModel.CurOnBoardingState != TaskRecord.ShowPay
                )
        {
            // CreateRole 是第一个状态所以不报错
            Debug.LogError(
                $"Internal error,  user lastState = {lastState}  CurOnBoardingState={_boardingModel.CurOnBoardingState.ToString()}");
            WidgetUtils.UpdateAndRefreshWidget();
        }
        else if(_boardingModel.CurOnBoardingState == TaskRecord.CreateRole 
                || _boardingModel.CurOnBoardingState == TaskRecord.Dialogue
                || _boardingModel.CurOnBoardingState != TaskRecord.ShowPay
        )
        {
            OnBoardingAbTest();
        }

    }
    
    

    private void OnReConnectCallback(long accoutID , string token, string version)
    {
        var msg = new CS_LoginReq { };
        msg.accountID = accoutID;
        msg.accessToken = token;
        msg.version = version;
        msg.sessionID = 0;
        msg.isReconnect = false;
        MsgManager.instance.SendMsg(msg , (type, message) =>
        {
            TimerManager.instance.RegisterTimer((c) =>
            {
                OnReConnectCallback(accoutID , token , version);
            }, 3000);
        });
        Debug.Log("==>2 Send CS_LoginReq");
    }
    
    private void OnReConnectCallback(string token,string version)
    {
        MsgManager.instance.SendMsg(new CS_LoginReq
        {
            isReconnect = true,
            accountID = _mainModel.userID,
            accessToken = token,
            sessionID = this._reconnectSession,
            version = version
        }, (type, message) =>
        {
            TimerManager.instance.RegisterTimer((c) =>
            {
                OnReConnectCallback(token , version);
            }, 3000, 1);
        });
    }


    private void OnSCLoginAck(SC_LoginAck msg)
    {
        TimerLogHelper.Ins.SaveTimeEnd(TimerLogHelper.TimeType.TcpLogin);
        if (msg.err == GameErrID.Ack_Success)
        {
            Debug.Log($"==>2 Login: On SC_LoginAck Success.");
            if (!msg.isReconnect)
            {
                this._reconnectSession = msg.sessionID;
                PushManager.instance.StopWork();
                PushManager.instance.SetUserIDAndSession(_mainModel.userID, msg.sessionID);
                PushManager.instance.OnTcpReConnect();
                PushManager.instance.StartWork();
            }
        }
        else
        {
            Debug.Log($"Login: On SC_LoginAck Erorr.{msg.err}");
            var errorMsg = $"{I18N.inst.MoStr("common_netError")}.\nLoginAck:{msg.err}";
            GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open(errorMsg, () => { }, null, 1);
        }
    }
    
    private void SendEnterGameZoneReq(long playerId)
    {
        MsgManager.instance.SendMsg(new CS_EnterGameZoneReq
        {
            account = _mainModel.userID,
            playerID = playerId
        }, (type, message) =>
       {
           TimerManager.instance.RegisterTimer((c) =>
           {
               SendEnterGameZoneReq(playerId);
           }, 3000);
       });
    }


    //todo1 从社交api获取自身头像信息url 后续再看怎么改
    public void SendGetSelfHeaderURL(long playerID)
    {
        CS_QueryChatUserListReq req = new CS_QueryChatUserListReq();
        req.userId.Add(playerID);

        MsgManager.instance.SendMsg(req
        , (type, message) =>
        {
            TimerManager.instance.RegisterTimer((c) =>
            {
                SendGetSelfHeaderURL(playerID);
            }, 3000);
        });
    }

    private void OnSCQueryChatUserListAck(SC_QueryChatUserListAck msg)
    {

        if (msg.code == 0)
        {
            if (msg.userList.Count > 0)
            {
                var data = msg.userList[0];
        
                _mainModel.SetMemberType(data.memberType);
                _mainModel.headerURL = data.headUrl;
                _MainController.GetUI<MainHeaderUI>(UIConsts.MainHeader).Refresh();
                Debug.Log("set HeaderURL");
            }
        }
        else {
            Debug.LogError("Fail to handle ChatUserList,");
        }
    }
    
    private void OnSCEnterGameZoneAck(SC_EnterGameZoneAck msg)
    {
        TimerLogHelper.Ins.SaveTimeEnd(TimerLogHelper.TimeType.EnterGameZone);
        if (!msg.isReconnect)
        {
            LocalCfgMgr.instance.SetRoleCfgKey(GameUtils.MD5Sign(msg.playerID.ToString()));
            _mainModel.SetPlayerID(msg.playerID);
            //将从其他tcp 的selfnotify回包处理进入世界
        }
        else
        {
            PushManager.instance.OnTcpReConnect();

            //重连成功
            if (msg.err == GameErrID.Ack_Success)
            {
                Debug.Log($"重连:SC_ReconnectAck Success.");
                ControllerManager.instance.OnNetReConn();
            }
            else
            {
                //重连失败 回到登陆UI
                Debug.Log($"重连:SC_ReconnectAck Error.Code:{msg.err}");
                NetManager.instance.Close();
                OnShowNetErrorConfirmUIOpen(I18N.inst.MoStr("common_netError"), () =>
                {
                    //this.BackToLogin();
                }, null, 1);
            }
        }
    }
 

    //一次登录 只触发一次，除非登录过期（目前还没有真正处理过期问题）
    public void EnterHomepageAfterLogin()
    {
        if (Time.realtimeSinceStartup - _lastEnterHomepageTime < EnterHomepageInterval)
        {
            Debug.LogWarning("EnterHomepageScene 调用过于频繁，已拦截");
            return;
        }
        _lastEnterHomepageTime = Time.realtimeSinceStartup;
        
        //=== 加载新的UI 撤销顶部遮挡的UI===
        //为了目前跳过 OnBoard进入世界 记录已有角色信息
        SceneController sceneController = GetController<SceneController>(ModelConsts.Scene);

        VFDebug.Log("=====LoadScene - Begin --");
        
        AFHelper.EnterHomepage_start();
        LoginBIHelper.Appear_home_page();
        sceneController.EnterHomepageScene(
            ()=> {
                this.TryHideAnyLoadedUI();
            },
            ()=> {
                //申请探索数据
                Notifier.instance.SendNotification(NotifyConsts.ExploreInfoAsk);
                //学习路径数据
                this.GetController<MainController>(ModelConsts.Main).DoEnterGame();
            },
            ()=> {
                TimerLogHelper.Ins.SaveTimeEnd(TimerLogHelper.TimeType.EnterHomepage);
                VFDebug.Log("=====LoadScene - End ");
            }
        );
        TimerLogHelper.Ins.SaveTimeStart(TimerLogHelper.TimeType.EnterHomepage);
        
        AFHelper.OnLogin(_mainModel.userID);
        
        PushManager.instance.StartWork();
        
    }

    //当后续流程拉起来 sceneloadingUI时,其他前置的UI就可以让位了
    public void TryHideAnyLoadedUI() {
        GameObject hotfixGo = GameObject.Find("HotfixPanel");
        if (hotfixGo != null)
        {
            hotfixGo.SetActive(false);
            GameObject.Destroy(hotfixGo);
        }
        MainLoadingUI.Ins.HideDefaultCanvas();
        HideLoginUI();
    }
    public override void OnNetDisConn()
    {
        Debug.Log("Game Net DisConn.");
        ////退出到登陆
        //OnShowNetErrorConfirmUIOpen(I18N.inst.UiStr("common_netError"), () =>
        //{
        //    //this.BackToLogin();
        //}, null, 1);
    }

    //回Srv心跳包
    private void OnSCHeartBeatAck(SC_HeartBeatAck msg)
    {
        MsgManager.instance.SendMsg(new CS_HeartBeatReq { });
    }

    private void OnSCGetUserInfoAck(SC_GetUserInfoAck msg)
    {
        isShowFlowTips = msg.data.is_flow_help_tips_show;
        if (isShowFlowTips)
        {
            MsgManager.instance.SendMsg(
                new CS_GetAudioForFlowReq()
                    { content = I18N.inst.MoStr("chat_flowHelper_placeHolder"), unique_id = TextToAudioConsts.FlowFirst },(a,n)=>OnBoardingAudioFail(TextToAudioConsts.FlowFirst));
        }

        _isOnRequest = false;
    }

    public void SendLoginReq()
    {
        MsgManager.instance.SendMsg(new CS_LoginReq 
        {
            isReconnect = true,
            accountID = _mainModel.userID,
            accessToken = PlayerPrefs.GetString(LoginConst.Key_User_Token, ""),
            sessionID = this._reconnectSession,
            version = AppConstExt.LogicVersion
        }, (type, message) =>
        {
            TimerManager.instance.RegisterTimer((c) =>
            {
                SendLoginReq();
            }, 3000);
        });
    }

    public void SendEnterGameZoneReq(long userId, long playerId)
    {
        MsgManager.instance.SendMsg(new CS_EnterGameZoneReq
        {
            account = userId,
            playerID = playerId
        });
    }

    public void SendLeaveGSReq()
    {
        Debug.Log("离开世界");
        MsgManager.instance.SendMsg(new CS_LeaveGSReq
        {
        });
         
    }
    
    public void SendGetFlowRecodeId(string text,string type)
    {
        CS_GetAudioForFlowReq msg = new CS_GetAudioForFlowReq();
        msg.content = text;
        msg.unique_id = type;
        MsgManager.instance.SendMsg(msg,(a,n)=>OnBoardingAudioFail(type));
    }
    
    private void OnBoardingAudio(SC_GetAudioForFlowAck msg)
    {
        if (msg.code != PB_Code.Normal )
        {
            Debug.LogError("LoginController OnBoardingAudio is error "+msg.code);
            //return;
        }
        SendNotification(OnBoardingCallEvent.OnBoardingAudioID , msg.data);
    }

    //
    private void OnLeaveGSAck(SC_LeaveGSAck msg)
    {
        //处理退出game服务逻辑
        Debug.Log("OnLeaveGSAck:" + msg);
    }

    //请求数据失败
    private void OnBoardingAudioFail(string unique_id)
    {
        SC_GetAudioForFlowAck msg = new SC_GetAudioForFlowAck();
        msg.data = new PB_GetAudioForFlowResp();
        msg.data.unique_id = unique_id;
        msg.data.audio_id = 0;
        SendNotification(OnBoardingCallEvent.OnBoardingAudioID , msg.data);   
    }

    public override void OnApplicationQuit()
    {
        MsgManager.instance.SendMsg(new CS_ExitGameReq { });
    }
	 public override void OnApplicationPause(bool pause)
    {
        if (pause)
        {
            //新埋点：APP切换到APP后台
            DataDotSwtichBackground dot = new DataDotSwtichBackground();
            DataDotMgr.Collect(dot);
        }
    }

 
    #region app背底
    private void OpenLoginUI()
    {
        AFHelper.login_ui_show();
        StartUpAmazonBI.appear_login_ui();
        GetController<MainController>(ModelConsts.Main).ChangeState(GameState.NotLogin);
        Debug.Log("OpenLoginUI");
        OpenLoginUI_only();
    }

    private void OnShowNetErrorConfirm()
    {
        MainLoadingUI.Ins.OnShowNetErrorConfirmUIOpen(I18N.inst.MoStr("common_netError"), () => { }, null, 1);
    }


    public void HideDefaultCanvas()
    {
        MainLoadingUI.Ins.HideDefaultCanvas();
    }
    #endregion

    #region NetErrorConfirmUI
    private void OnShowNetErrorConfirmUIOpen(string content, Action confirmFunc, Action cancelFunc = null, int type = 2,
        string confirmLabel = "", string cancelLabel = "", bool block = false,int iconType = 0)
    {
        MainLoadingUI.Ins.OnShowNetErrorConfirmUIOpen(content,confirmFunc,cancelFunc,type,confirmLabel,cancelLabel,block,iconType);
    }

    private void OnConfirmUIHide()
    {
        MainLoadingUI.Ins.OnConfirmUIHide();
    }


    
    public void ShowSignUpUI()
    {
        _boardingModel.SetCurOnBoardingState(TaskRecord.Finish, 
            (m) =>
            {
                if (!isIDBinded)
                    GetUI<SignUpUI>(UIConsts.SignUpUI).Show();
                else
                {
                    GetController<BottomBubbleController>(ModelConsts.BottomBubbleController).DequeueAndPrint();
                } 
            }
        );
    }

    public void ShowGuidClickEnter()
    {
        //todo00 手指点击引导的问题待处理

        //这里写显示enter带手指点击引导ui逻辑 
        //var ui = GetUI<EnterUI>(UIConsts.EnterUI);
        //if (ui.isShow)
        //    GetUI<EnterUI>(UIConsts.EnterUI).setHandVisble(true);
        //else
        //    ui.willShowHand = true;

        _boardingModel.SetCurOnBoardingState(TaskRecord.Finish);
    }

    public void ShowGuidClickChallenge()
    {
        //todo
        //这里写显示对应chapter并引导点击挑战对话的逻辑
        // _learnPathController.ChapterGuide = true;
        //执行完点击chapter里的挑战对话点击事件里加入下面的代码
        //_boardingModel.SetCurOnBoardingState(LoginType.Finish);
    }

    public void ShowGoalDrivenBreathGuide()
    {
        VFDebug.Log("OnBoard Step: Show Goal Driven Breath Effect.");
        // GetController<LearnPathController>(ModelConsts.LearnPath)
        //     .SendGetUserGoalNodeOnEntergame(0, chatStartCtrl.PreLoadChatStartData);
        if (isIDBinded)
        {
            // GetUI<ChapterUI>(UIConsts.ChapterUI).TryShow();
            UnlockGuide();
            _boardingModel.SetCurOnBoardingState(TaskRecord.Finish);
        }
        else
        {
            //GetUI<ChapterUI>(UIConsts.ChapterUI).Show().onCompleted += () =>
            //{
            //    GetUI<ChapterUI>(UIConsts.ChapterUI).SetPlayBreathEffectGuide(()=>
            //    {
            //        UnlockGuide();
            //        GetController<LoginController>(ModelConsts.Login).ShowSignUpUI();
            //    });
            //};
            // GetUI<ChapterUI>(UIConsts.ChapterUI).TryShow();//需要复测
            // GetUI<ChapterUI>(UIConsts.ChapterUI).SetPlayBreathEffectGuide(() =>
            // {
            //        UnlockGuide();
            //        GetController<LoginController>(ModelConsts.Login).ShowSignUpUI();
            // });
        }
    }

    private void UnlockGuide()
    {
        GetController<MainController>(ModelConsts.Main).SetInputEventActive(true);
        GetUI<MainHeaderUI>(UIConsts.MainHeader).Show();
        GetUI<MainHeaderUI>(UIConsts.MainHeader).SetHeader(0);
    }

    // 临时方案
    public async void TryReconnectGuide()
    {
        isOnRequestFirstTask = false;
        VFDebug.Log("尝试重新请求Onboard");
        var resp = await NetMsgProxy.instance.SendAsyncMsg<SC_GetUserCheckinPortalDataAck>(new CS_GetUserCheckinPortalDataReq(), null, true);
        GetModel<SignModel>(ModelConsts.Sign).SetSignInfo(resp.data);

        GetUI(UIConsts.MainHeader).Show();
        VFDebug.Log($"OnBoard Step: Set State {_boardingModel.CurOnBoardingState}");        
        _boardingModel.SetCurOnBoardingState(_boardingModel.CurOnBoardingState, (m) => { TryShowGuideGoalDriven(resp.data.finish_checkin); });
    }

    private void TryShowGuideGoalDriven(bool isFinishCheckin)
    {
        if (isOnRequestFirstTask)
            return;
        VFDebug.Log("TryShowGuideGoalDriven");
        var confirmUI = GetUI(UIConsts.CommConfirm);
        if (confirmUI.isShow)
            confirmUI.Hide();

        if (isFinishCheckin)
        {
            GetUI(UIConsts.MainHeader).Hide();
            VFDebug.Log("OnBoard Step: Open Sign UI.");
            ShowGoalDrivenBreathGuide();
        }
        else
        {
            VFDebug.Log($"OnBoard Step: Show Finish Goal Driven");
            UnlockGuide();
            ShowSignUpUI();
        }
    }

    #endregion
    
    
}
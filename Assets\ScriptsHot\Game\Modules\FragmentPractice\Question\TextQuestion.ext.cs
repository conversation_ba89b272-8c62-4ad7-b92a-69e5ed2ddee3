using CommonUI;
using FairyGUI;
using Msg.question;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using static ScriptsHot.Game.Modules.FragmentPractice.FragmentPracticeUI;
using Game.Modules.FragmentPractice;
using Game.Modules.Record;

namespace UIBind.FragmentPractice
{
    public interface ITextMask
    {
        // 如果答案需要自定义掩码
        string GetMask();
    }

    public partial class TextQuestion : AFragQuestion
    {
        public TextFieldExtension TfQuestion => tfQuestion as TextFieldExtension;

        override protected void OnAddedToStage()
        {
            TfQuestion.SetNewFont(FontCfg.DinNextRegular, new Color(17f / 255f, 17f / 255f, 17f / 255f, 1), 36);
            TfQuestion.TextGtf.textFormat.lineSpacing = 28;
            TfQuestion.EnableCache = true;
        }

        #region refresh
        override public void ShowPractice(AFragAnswer answerComp)
        {
            RefreshAudio(base.Practice, answerComp);

            string mask = "mmmmm";
            if (answerComp is ITextMask textMask)
            {
                mask = textMask.GetMask();
            }
            FillQuestionText(mask);


            if (Practice is IImageQuestion imageQuestion)
            {
                imgQuestion.url = imageQuestion.GetImageUrl();
                ctrlImage.selectedIndex = string.IsNullOrEmpty(imgQuestion.url) ? 0 : 1;
            }
            else
            {
                ctrlImage.selectedIndex = 0;
            }
            grpOutside.EnsureBoundsCorrect();

            height = Mathf.Max(grpOutside.y + grpOutside.height, btnPlayAudio.y + btnPlayAudio.height);
        }

        private void RefreshAudio(APracticeData question, AFragAnswer answerComp)
        {
            var playAudio = question.AudioId != 0;
            audio.selectedPage = playAudio ? "visible" : "invisible";
            btnPlayAudio.AudioId = question.AudioId;

            if (playAudio && IsCurrent)
            {
                btnPlayAudio.Play();
            }
            btnPlayAudio.onClick.Add(OnBtnPlayAudioClick);
        }

        private void OnBtnPlayAudioClick(EventContext context)
        {
            RecordEventManager.Instance.DispatchRecordCancel();
            DotPracticeManager.Instance.Collect(new DataDot_PlayTTS());
        }

        // todo 走事件
        private void FillQuestionText(string filler)
        {
            TextFieldExtension textExt = TfQuestion;
            textExt.Reset();

            // 翻译题原文是母语，不需要翻译
            if (Practice.QuestionType != PB_QuickPracticeType.TranslateSenAndTapSen)
            {
                textExt.onTranslated += (word, translation) =>
                {
                    DotPracticeManager.Instance.Collect(new DataDot_Translate(word, translation));
                };  
                textExt.StartNewWordTrans(); 
            }
            List<RichContent> contents = RichContentUtil.SplitPracticeWords(
                Practice.GetStem().Trim(),
                Practice.QuestionType, Practice.GetBlankRanges(),
                QuestionEventManager.Instance.State == QuestStateEnum.Submit, filledInBlank: filler);

            foreach (var richContent in contents)
            {
                textExt.AppendContent(richContent);
            }

            textExt.TextGtf.textFormat.font = FontCfg.DinNextRegular;

            if (Practice.QuestionType == PB_QuickPracticeType.Cloze)
            {
                textExt.UnderlineOffset = 14;
            }
            textExt.Display(ShowMode.QuestionNormal);
            if (Practice.QuestionType != PB_QuickPracticeType.TranslateSenAndTapSen)
            {
                textExt.AddDot(contents, true);
            }
            textExt.ResizeToFit();


            TimerManager.instance.RegisterNextFrame((c)=>LoadClozePositions());
        }
        #endregion
    }
}
﻿using UnityEngine;

namespace AnimationSystem
{
    
    // 动作片段附加信息封装类
    public class AnimInfo
    {
        public AnimInfo(AnimationClip clip, Vector2 blendRange, bool canBlend, bool canLoop, Vector2 loopMultiplierRange, bool canChangeSpeed, Vector2 speedMultiplierRange, bool enableFootIK, float loopBlendTime)
        {
            this.clip = clip;
            this.blendRange = blendRange;
            this.canBlend = canBlend;
            this.canLoop = canLoop;
            this.loopMultiplierRange = loopMultiplierRange;
            this.canChangeSpeed = canChangeSpeed;
            this.speedMultiplierRange = speedMultiplierRange;
            this.enableFootIK = enableFootIK;
            this.loopBlendTime = loopBlendTime;
        }
        
        public AnimInfo()
        {

        }

        public string animName;
        public AnimationClip clip;                // 原始动画引用
        public Vector2 blendRange;                // 融合范围（x:最早可接受前百分比, y:最晚可接受后百分比）
        public bool canBlend;                     // 是否支持融合
        public bool canLoop;                      // 是否支持循环
        public Vector2 loopMultiplierRange;       // 循环倍率范围（时间Multiplier）
        public bool canChangeSpeed;               // 是否支持变速
        public Vector2 speedMultiplierRange;      // 速度倍率范围（速度Multiplier）
        public bool enableFootIK = false;          // 是否启用FootIK，默认为false
        public float loopBlendTime = 0.2f;       // 循环时的融合时间配置
    }
}
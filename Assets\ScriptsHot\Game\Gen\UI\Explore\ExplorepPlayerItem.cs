/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExplorepPlayerItem : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExplorepPlayerItem";

        public GRichTextField txtName;
        public GGroup grpTitle;
        public GLoader3D sp;
        public GTextField tfOrigin;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            txtName = (GRichTextField)com.GetChildAt(2);
            grpTitle = (GGroup)com.GetChildAt(3);
            sp = (GLoader3D)com.GetChildAt(4);
            tfOrigin = (GTextField)com.GetChildAt(5);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            txtName = null;
            grpTitle = null;
            sp = null;
            tfOrigin = null;
        }
    }
}
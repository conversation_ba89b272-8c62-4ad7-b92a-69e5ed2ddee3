/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/16 15:58:36 星期四
# 功能：Nothing
****************************************************
*/

using System;
using System.Collections.Generic;
using FairyGUI;
using Firebase.Analytics;
using Game;
using Modules.DataDot;
using Msg.economic;
using ScriptsHot.Game.Modules.Settlement;
using UIBind.Shop;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Shop
{
    [Obsolete]
    public class PlanSelectUI : ShopUIBase<UIBind.Shop.PlanSelectPanel>, IBaseUIUpdate
    {
        public PlanSelectUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top; //主UI层
        
        private ShopModel _shopModel => GetModel<ShopModel>(ModelConsts.Shop);
        
        private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);
        
        private CurrencyModel _currencyModel => GetModel<CurrencyModel>(ModelConsts.CurrencyController);

        protected override bool isFullScreen { get; } = true;

        //private string _curPlan = "YearPremium";
        private float _lastUpdateTime;
        private string _preBtnStr;

        //250410 根据业务调整下面按钮固定为月，上面按钮根据地区设置为年或者季，需要动态替换
        private string _topMemberTypeName;
        private ShopModel.SubscribeType _topMemberType = ShopModel.SubscribeType.None;
        private List<PlanItemCom> items = new List<PlanItemCom>();
        
        private Dictionary<ShopModel.SubscribeType , PB_SubscriptionInfo> dicSubscriptionInfo = new Dictionary<ShopModel.SubscribeType, PB_SubscriptionInfo>();
        private ShopModel.SubscribeType selectItemType;
        private bool isDiscount;
        private bool isLifeTime;
        
        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(this.ui.btnExit.onClick, OnBtnExitClicked);
            AddUIEvent(this.ui.comPlanSelectedContent.btnNext.onClick, OnBtnNextClicked);
            //AddUIEvent(this.ui.comPlanSelectedContent.btnMorePlans.onClick, OnBtnMorePlansClicked);
            //AddUIEvent(ui.btnServiceDetail.onClick, OnBtnServiceDetailClicked);
            //AddUIEvent(ui.tfRevertPurchase.onClick, OnBtnRestorePurchaseClicked);
            //AddUIEvent(ui.btnPrivate.onClick, OnBtnPrivacyClicked);
            InitUI();
        }
        
        private void InitUI()
        {

            
#if UNITY_ANDROID
            ui.comPlanSelectedContent.tfDesc8.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_login_tips1_android");
#else
            ui.comPlanSelectedContent.tfDesc8.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_selected_desc9");
#endif       

            // ui.comPlanSelectedContent.tfDesc9.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_selected_desc10");
            //ui.tfServiceDetail.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_service");
            //ui.tfPrivate.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_privacy");
            //ui.tfRevertPurchase.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_restore");
            //ui.comPlanSelectedContent.tfMorePlans.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_12");
        }

        public void Update(int interval)
        {
            if (Time.time - _lastUpdateTime > 0.2f)
            {
                _lastUpdateTime = Time.time;
                UpdateYearly();
                UpdateMonthly();
            }
        }
        
        private void UpdateYearly()
        {
            PlanItemCom com = items[0];
            
            var cfg_year = _shopModel.GetMemberTypeByName(_topMemberTypeName);
            long remainingMillis = cfg_year.promoted_purchase_remains_days - TimeExt.serverTimestamp / 1000;
            if (remainingMillis < 0)
            {
                com.grpOnsale.visible = false;
                return;
            }
            com.grpOnsale.visible = true;
            // 计算天、小时、分钟
            long totalSeconds = remainingMillis;
            long days = totalSeconds / (24 * 3600);
            long hours = (totalSeconds % (24 * 3600)) / 3600;
            long minutes = (totalSeconds % 3600) / 60;
            if (days > 0)
            {
                com.tfTime.text = days + " Days";

            }
            else if (hours > 0)
            { 
                com.tfTime.text = hours + " hours";

            }
            else
            {
                com.tfTime.text = minutes + " Mins";
            }
            com.grpOnsaleTime.SetBoundsChangedFlag();
            com.grpOnsaleTime.EnsureBoundsCorrect();
            com.grpOnsale.SetBoundsChangedFlag();
            com.grpOnsale.EnsureBoundsCorrect();
        }

        private void UpdateMonthly()
        {
            PlanItemCom com = items[1];
            
            var cfg_month = _shopModel.GetMemberTypeByName("premium_monthly");
            long remainingMillis = cfg_month.promoted_purchase_remains_days - TimeExt.serverTimestamp / 1000;
            if (remainingMillis < 0)
            {
                com.grpOnsale.visible = false;
                return;
            }
            com.grpOnsale.visible = true;
            // 计算天、小时、分钟
            long totalSeconds = remainingMillis;
            long days = totalSeconds / (24 * 3600);
            long hours = (totalSeconds % (24 * 3600)) / 3600;
            long minutes = (totalSeconds % 3600) / 60;
            if (days > 0)
            {
                com.tfTime.text = days + " Days";

            }
            else if (hours > 0)
            {
                com.tfTime.text = hours + " hours";

            }
            else
            {
                com.tfTime.text = minutes + " Mins";
            }
            com.grpOnsaleTime.SetBoundsChangedFlag();
            com.grpOnsaleTime.EnsureBoundsCorrect();
            com.grpOnsale.SetBoundsChangedFlag();
            com.grpOnsale.EnsureBoundsCorrect();
        }

        private void SetUITouchAble(bool able)
        {
            ui.comPlanSelectedContent.comLoading.visible = !able;
            ui.comPlanSelectedContent.btnNext.touchable = able;
            foreach (var planItemCom in items)
            {
                planItemCom.selectBtn.touchable = able;
            }
            ui.btnExit.touchable = able;
        }

        protected override void OnShow()
        {
            DataDotMgr.Collect(new AppearTrialChoosePage());
            
            selectItemType = ShopModel.SubscribeType.None;
            isDiscount = GameEntry.ShopC.ShopModel.IsDiscountType;
            isLifeTime = GameEntry.ShopC.ShopModel.IsLifeTime;
            if (_shopModel.TryGetMemberTypeByName(ShopController.MEMBER_TYPE_QUARTER, out var cfg_quarterly))
            {
                _topMemberTypeName = ShopController.MEMBER_TYPE_QUARTER;
                _topMemberType = ShopModel.SubscribeType.Quarter;
                dicSubscriptionInfo.TryAdd(ShopModel.SubscribeType.Quarter, cfg_quarterly);
            }
            else if (_shopModel.TryGetMemberTypeByName(ShopController.MEMBER_TYPE_YEAR, out var cfg_yearly))
            {
                _topMemberTypeName = ShopController.MEMBER_TYPE_YEAR;
                _topMemberType = ShopModel.SubscribeType.Year;
                dicSubscriptionInfo.TryAdd(ShopModel.SubscribeType.Year, cfg_yearly);
            }
            else
            {
                VFDebug.LogError("下发的订阅数据中 应该含有 YEAR|QUARTER 二者之一");
            }

            if (_shopModel.TryGetMemberTypeByName(ShopController.MEMBER_TYPE_MONTH, out var cfg_momnthly))
            {
                dicSubscriptionInfo.TryAdd(ShopModel.SubscribeType.Month, cfg_momnthly);
            }

            CheckDiscount();
            CheckLifeTime();
            RefreshUI();
            SetUITouchAble(true);

            if (isLifeTime)
            {
                OnLifeTimeItemSelect();
            }
            else
            {
                UnSelectItem(1);
                OnItemSelect(0);
            }

            if (!isLifeTime)
            {
                //要求grpBtnNext固定距离底部40px
                ui.comPlanSelectedContent.com.EnsureBoundsCorrect();
                var parentGroup = ui.comPlanSelectedContent.com;
                var btnGroup = ui.comPlanSelectedContent.grpBtnNext;
                float targetYInGlobal = GRoot.inst.height - 20 - btnGroup.height - UIManager.instance.safeAreaBottomHeight;
                Vector2 localPos = parentGroup.GlobalToLocal(new Vector2(0, targetYInGlobal));
                ui.comPlanSelectedContent.grpBtnNext.y = localPos.y;
                ui.comPlanSelectedContent.com.EnsureBoundsCorrect();
            }
        }

        private void CheckDiscount()
        {
            if (isLifeTime || isDiscount)
            {
                ui.comPlanSelectedContent.tfDesc9.text = I18N.inst.MoStr("ui_shop_discount_07");
            }
            else
            {
                ui.comPlanSelectedContent.tfDesc9.text = I18N.inst.MoStr("ui_plan_selected_desc10");
            }

            if (isLifeTime)
            {
                ui.comPlanSelectedContent.tfDesc10.text = I18N.inst.MoStr("ui_shop_lifetime_14");                
            }
            else if (isDiscount)
            {
                ui.comPlanSelectedContent.tfDesc10.text = I18N.inst.MoStr("ui_shop_discount_09");
            }
            else
            {
                ui.comPlanSelectedContent.tfDesc10.text = String.Empty;
            }

            bool showBottonLable = selectItemType == ShopModel.SubscribeType.Month ||
                                   selectItemType == ShopModel.SubscribeType.Quarter ||
                                   selectItemType == ShopModel.SubscribeType.Year;

            ui.comPlanSelectedContent.tfDesc10.visible = showBottonLable;
        }

        private void RefreshUI()
        {
            RefreshLifeTimeItem();
            RefreshItems();
        }
        
        private void RefreshItems()
        {
            //首次刷新时初始化 item数据容器
            if (items.Count == 0)
            {
                GGroup listContent = ui.comPlanSelectedContent.itemList;
                for (int i = 1; i < listContent.GetChildren().Count; i++)
                {
                    PlanItemCom obj = new PlanItemCom();
                    obj.Construct(listContent.GetChildren()[i] as GComponent);
                    obj.imgMostYear.visible = false;
                    obj.tfDesc1.visible = false;
                    obj.imgRecomendTop.visible = false;
                    obj.selected.visible = false;
                    obj.saleoffSelectBg.visible = false;
                    obj.saleoffUnSelectBg.visible = true;
                    obj.discountType.selectedIndex = isDiscount ? 1 : 0;
                    int index = i - 1;
                    
                    AddUIEvent(obj.selectBtn.onClick , () =>
                    {
                        OnItemSelect(index);
                    });
                    
                    items.Add(obj);
                }
                
                listContent.EnsureBoundsCorrect();
            }
            
            /*
             * _shopModel.SubscriptionInfoDic 目前最多（20250701）同时有2个plan，且year和quartar的不同时存在
             * 目前强制 第一计划为 月度的plan
             * _topMemberTypeName 是当前onshow时运行决策出来的选择信息
             */
            if (_shopModel.SubscriptionInfoDic.Count != items.Count)
            {
                VFDebug.LogError("商品Subscription信息下发数量 与展示数量 不匹配");//comPlanSelectedContent 目前也只配了2个UI item资源
            }
            
            if ( items.Count <2)
            {
                VFDebug.LogError("商品Subscriptio plan最大展示量 与此前约定不同");
            }
            else
            {
                if (_topMemberType != ShopModel.SubscribeType.None)
                {
                    RefreshItem(items[0],_topMemberType);//0号元素指向最稳定的 month
                } else
                {
                    VFDebug.LogError("商品Subscription信息下发信息中 既无year也无Quarter plan，与此前约定不同");
                }
                RefreshItem(items[1],ShopModel.SubscribeType.Month);//0号元素指向最稳定的 month
                
            }

          
        }

        private void UnSelectItem(int index )
        {
            if (selectItemType == ShopModel.SubscribeType.None)
            {
                return;
            }

            //int index = selectItemType == ShopModel.SubscribeType.Month ? 1 : 0;//假定month在第二个位置
            
            PlanItemCom com = items[index];
            com.imgMostYear.visible = false;
            com.tfDesc1.visible = false;
            com.selected.visible = false;
            
     
            
            if (isDiscount)
            {
                com.saleoffSelectBg.visible = false;
                com.saleoffUnSelectBg.visible = true;
            }
        }
        private void OnItemSelect(int index)
        {
            ShopModel.SubscribeType subscribeType =ShopModel.SubscribeType.None;
            if (index == 0)
            {
                subscribeType = _topMemberType;
                UnSelectItem(1);
            }
            else if(index == 1)
            {
                subscribeType =ShopModel.SubscribeType.Month;
                UnSelectItem(0);
            }
            else
            {
                VFDebug.LogError("PlanSelectUI 原定只考虑2个plan元素，超出的逻辑需要重新设计");
                return;
            }

            UnSelectLifeTimeItem();
            
            //  已选中的 再选一次
            if (selectItemType == subscribeType)
            {
                return;
            }
            
            
            PlanItemCom com = items[index];

            if (!isDiscount && !isLifeTime && subscribeType != ShopModel.SubscribeType.Month)
            {
                com.imgMostYear.visible = true;//不是月时，就给额外的背景提示色块 代表更优惠
                com.tfDesc1.visible = true;
            }

            if (isDiscount)
            {
                com.saleoffSelectBg.visible = true;
                com.saleoffUnSelectBg.visible = false;
            }

            com.selected.visible = true;
            
            if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.FreeTrialCanceled ||
                _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Canceled)
            {
                _preBtnStr = I18N.inst.MoStr("ui_plan_common_btn_stay");
                ui.comPlanSelectedContent.tfBtnNext.SetLanguage(LanguageType.MotherTongue, refresh:true).SetKey("ui_plan_common_btn_stay");
            }
            else
            {
                bool isDiscount = GameEntry.ShopC.ShopModel.IsDiscountType;
                if (isDiscount)
                {
                    PB_SubscriptionInfo pb_cfg = null;
                    if (dicSubscriptionInfo.TryGetValue(subscribeType, out pb_cfg))
                    {
                        _preBtnStr = string.Format(I18N.inst.MoStr("ui_shop_discount_01"),
                            $"{ShopUIUtil.GetSaleOffValue(pb_cfg)}%");
                        ui.comPlanSelectedContent.tfBtnNext.text = _preBtnStr;
                    }            
                }
                else
                {
                    PB_SubscriptionInfo pb_cfg = null;
                    if (dicSubscriptionInfo.TryGetValue(subscribeType, out pb_cfg))
                    {
                        _preBtnStr = string.Format(I18N.inst.MoStr("ui_plan_selected_desc16"),
                            pb_cfg.free_days);
                        ui.comPlanSelectedContent.tfBtnNext.text = _preBtnStr;
                    }
                }
            }

            selectItemType = subscribeType;
            
            switch (subscribeType)
            {
                case ShopModel.SubscribeType.Month:
                    DataDotMgr.Collect(new DataDotTrialChooseMonthly());
                    break;
                case ShopModel.SubscribeType.Year:
                    DataDotMgr.Collect(new DataDotTrialChooseYearly());
                    break;
                case ShopModel.SubscribeType.Quarter:
                    DataDotMgr.Collect( new DataDotTrialChooseQuarterly());
                    break;
            }
            

            CheckDiscount();
            CheckLifeTime();
        }

        private void RefreshItem(PlanItemCom obj , ShopModel.SubscribeType subscribeType)
        {
            PB_SubscriptionInfo info = null;
            if (dicSubscriptionInfo.TryGetValue(subscribeType, out info))
            {
                if (isDiscount)
                {
                    obj.tfPricePerMonth1.text = string.Format(I18N.inst.MoStr("ui_plan_selected_desc17") , info.price_per_month_in_display);
                    if (subscribeType == ShopModel.SubscribeType.Month)
                    {
                        obj.offValueTxt.text = string.Format(I18N.inst.MoStr("ui_shop_discount_12") , GameEntry.ShopC.ShopModel.GetDiscountStr(subscribeType));
                    }
                    else if (subscribeType == ShopModel.SubscribeType.Quarter)
                    {
                        obj.offValueTxt.text = string.Format(I18N.inst.MoStr("ui_shop_discount_13") , GameEntry.ShopC.ShopModel.GetDiscountStr(subscribeType));
                    }
                    else
                    {
                        obj.offValueTxt.text = string.Format(I18N.inst.MoStr("ui_shop_discount_08") , GameEntry.ShopC.ShopModel.GetDiscountStr(subscribeType));
                    }

                }
                else
                {
                    obj.tfPricePerMonth1.text = string.Format(I18N.inst.MoStr("ui_plan_selected_desc17") , info.price_per_month_in_display);
                }

                obj.priceNow.text = info.price_in_display;
                obj.pricePre.text = info.origin_price_in_display;
                if (subscribeType == ShopModel.SubscribeType.Month)
                {
                    obj.priceDesc.text = string.Format(I18N.inst.MoStr("ui_shop_discount_14"),
                        info.origin_price_in_display);
                }
                else
                {
                    obj.priceDesc.text = _topMemberTypeName == ShopController.MEMBER_TYPE_YEAR?
                        string.Format(I18N.inst.MoStr("ui_shop_discount_11"),info.origin_price_in_display):
                        string.Format(I18N.inst.MoStr("ui_shop_discount_15"),info.origin_price_in_display);
                    
                }
            }
            
            obj.tfEquivalent1.SetKey("ui_plan_selected_desc13");
            obj.tfDesc1.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_selected_desc1");
            if (subscribeType == ShopModel.SubscribeType.Month)
            {
                obj.tfDesc2.text = I18N.inst.MoStr("ui_plan_selected_desc5");
                obj.tfDesc3.visible = false;
                obj.imgRecomendTop.visible = false;
            }
            else
            {
                obj.tfDesc2.text = I18N.inst.MoStr(_topMemberTypeName == ShopController.MEMBER_TYPE_YEAR ? "ui_plan_selected_desc2" : "ui_plan_selected_desc14");
                obj.tfDesc3.visible = true;
                obj.tfDesc3.text = string.Format(I18N.inst.MoStr("ui_plan_selected_desc15") , info.month_count,
                    info.origin_price_in_display);
                obj.imgRecomendTop.visible = !isDiscount && !isLifeTime;
            }
        }

        private void OnBtnExitClicked()
        {
            bool needHide = true;
            if (args != null && args.Length > 0)
            {
                var source = (int)args[0];
                
                var cfg =  _shopModel.GetMemberTypeByName(IsSelectYear() ? _topMemberTypeName : ShopController.MEMBER_TYPE_MONTH);
                var dot = new DataDotTrialChooseQuit();
                
                int selectType = (int)selectItemType;
                ShopModel.SubscribeTypeFullName typeName = (ShopModel.SubscribeTypeFullName)selectType;
                dot.clicked_member_type = nameof(typeName);
                dot.subscribe_price =  cfg.price_in_cents;
                dot.price_currency =  OsFuncAdapter.Ins.GetCountryCode();
                
                string testStr =  source == 1 ? "popup" :
                    source == 2 ? "bottom_bar" :
                    source == 5 ? "onboarding_popup" : "result_page";
                
                if (testStr != dot.source_page)
                {
                    Debug.LogError($"sourcepage 埋点可能存在潜在异常,testStr={testStr} dot2.source_page ={dot.source_page}");
                }

                
                DataDotMgr.Collect(dot);   
                if (source == 4)
                {
                    GetController<SettlementController>(ModelConsts.Settlement).ShowNextView( ()=> { Hide(); });
                }
            }
            if (needHide) {
                GetController<ShopController>(ModelConsts.Shop).CheckSendOnBoardOverMsg();
                Hide();
            }
            
        }

        private PB_SubscriptionInfo GetSelectSubscriptionInfo()
        {
            if (selectItemType == ShopModel.SubscribeType.Lifetime)
            {
                return _shopModel.GetLifetimeData();
            }
            else if (selectItemType == ShopModel.SubscribeType.Year ||
                     selectItemType == ShopModel.SubscribeType.Quarter)
            {
                return _shopModel.GetMemberTypeByName(_topMemberTypeName);
            }
            return _shopModel.GetMemberTypeByName(ShopController.MEMBER_TYPE_MONTH);
        }
        
        private PB_ProductType GetProductType()
        {
            if (selectItemType == ShopModel.SubscribeType.Lifetime)
            {
                return PB_ProductType.PB_ProductType_Subscribe;
            }
            else
            {
                return PB_ProductType.PB_ProductType_Subscribe;
            }
        }
        
        private void OnBtnNextClicked()
        {
            SetUITouchAble(false);
            ui.comPlanSelectedContent.tfBtnNext.SetLanguage(LanguageType.MotherTongue, refresh:true).SetKey("common_shop_create_payment");

            var cfg = GetSelectSubscriptionInfo();
            PB_ProductType productType = GetProductType();
            PurchasingManager.instance.StartPurchasing(cfg.product_id, productType, OnSubscribeSuccess, OnSubscribeFail, OnSubScribePaid);
            AFHelper.Click_Trial_choose_next(cfg.product_id);
            
            //新埋点：未免费订阅过的用户，在免费试用选包页点开始体验
            DataDotTrialChooseNext dot = new DataDotTrialChooseNext();
            
            int selectType = (int)selectItemType;
            ShopModel.SubscribeTypeFullName typeName = (ShopModel.SubscribeTypeFullName)selectType;
            dot.clicked_member_type = nameof(typeName);
            dot.subscribe_price =  cfg.price_in_cents;
            dot.price_currency =  OsFuncAdapter.Ins.GetCountryCode();
            DataDotMgr.Collect(dot);
            
           
            
            product_id = cfg.product_id;
            product_type = PB_ProductType.PB_ProductType_Subscribe.ToString();
            if (args.Length > 0)
            {
                MyFirebaseAnalytics.LogEvent("trial_get");
            }

            var plan = IsSelectYear() ? _topMemberTypeName : ShopController.MEMBER_TYPE_MONTH;
            AFDots.Click_Package_page_get(plan);
            MyFirebaseAnalytics.LogEvent("trial_package", new Parameter[]{new Parameter("package_type",plan)});
        }

        private bool IsSelectYear()
        {
            return selectItemType == ShopModel.SubscribeType.Year ||  selectItemType == ShopModel.SubscribeType.Quarter;
        }
        
        private void OnSubScribePaid()
        {
            ui.comPlanSelectedContent.tfBtnNext.SetLanguage(LanguageType.MotherTongue, refresh:true).SetKey("common_shop_verify_payment");
        }

        private void OnSubscribeSuccess()
        {
            SetUITouchAble(true);
            ui.comPlanSelectedContent.tfBtnNext.text = _preBtnStr;
            OnBuySuccess();
            GetUI<SubscribeSuccessUI>(UIConsts.SubscribeSuccessUI).IsLifeTime = selectItemType == ShopModel.SubscribeType.Lifetime;            
            GetUI<SubscribeSuccessUI>(UIConsts.SubscribeSuccessUI).prefix = "Premium";
            GetUI<SubscribeSuccessUI>(UIConsts.SubscribeSuccessUI).Show(args);
            Hide();
            SendNotification(NotifyConsts.HideHomepageBanner);
        }

        private void OnSubscribeFail(string msg, bool showError)
        {
            SetUITouchAble(true);
            ui.comPlanSelectedContent.tfBtnNext.text = _preBtnStr;
            OnBuyFail(msg, showError);
            GetController<ShopController>(ModelConsts.Shop).CheckSendOnBoardOverMsg();
        }

        private void OnBtnMorePlansClicked()
        {
            GetUI<PlanInstructionUI>(UIConsts.PlanInstructionUI).Show();
            //新埋点：未免费订阅过的用户，在免费试用选包页点更多套餐
            DataDotTrialMorePackage dot = new DataDotTrialMorePackage();
            DataDotMgr.Collect(dot);
        }
        
        private void OnBtnPrivacyClicked()
        {
            OnPrivacyClicked();
            //新埋点：用户点击服务隐私协议
            DataDotTrialPrivate dot = new DataDotTrialPrivate();
            DataDotMgr.Collect(dot);
        }
        
        private void OnBtnServiceDetailClicked()
        {
            OnServiceDetailClicked();
            //新埋点：用户点击服务协议
            DataDotTrialService dot = new DataDotTrialService();
            DataDotMgr.Collect(dot);
        }

        private void OnBtnRestorePurchaseClicked()
        {
            OnRestorePurchaseClicked();
            //新埋点：用户点击恢复购买
            DataDotTrialRestorePurchase dot = new DataDotTrialRestorePurchase();
            DataDotMgr.Collect(dot);
        }
        
        protected override void HandleNotification(string name, object body)
        {
            switch (name)
            {
                case NotifyConsts.OnApplicationPaused:
                    ui.comPlanSelectedContent.tfBtnNext.SetLanguage(LanguageType.MotherTongue, refresh:true).SetKey("common_shop_process_payment");
                    break;
            }
        }
        
        protected override string[] ListNotificationInterests()
        {
            return new string[]
            {
                NotifyConsts.OnApplicationPaused
            };
        }

        protected override void OnHide()
        {
            items.Clear();
            lifeTimeItem = null;
        }


        #region lifetime相关
        LifeTimePlanItemCom lifeTimeItem;
        private void RefreshLifeTimeItem()
        {
            if (lifeTimeItem == null)
            {
                GGroup listContent = ui.comPlanSelectedContent.itemList;
                for (int i = 0; i < listContent.GetChildren().Count; i++)
                {
                    lifeTimeItem = new LifeTimePlanItemCom();
                    lifeTimeItem.Construct(listContent.GetChildren()[0] as GComponent);
                    
                    AddUIEvent(lifeTimeItem.selectBtn.onClick , () =>
                    {
                        OnLifeTimeItemSelect();
                    });
                }
                listContent.EnsureBoundsCorrect();
            }

            lifeTimeItem.com.visible = isLifeTime;
            if (isLifeTime)
            {
                PB_SubscriptionInfo info = _shopModel.GetLifetimeData();
                lifeTimeItem.cornerTxt.text = I18N.inst.MoStr("ui_shop_lifetime_8");
                lifeTimeItem.tfDesc2.text = I18N.inst.MoStr("ui_shop_lifetime_9");
                lifeTimeItem.priceNow.text = info.price_in_display;
                lifeTimeItem.pricePre.text = I18N.inst.MoStr("ui_shop_lifetime_10");
            }
        }
        
        private void UnSelectLifeTimeItem()
        {
            if (selectItemType == ShopModel.SubscribeType.None)
            {
                return;
            }

            lifeTimeItem.isSelect.selectedIndex = 0;
        }
        private void OnLifeTimeItemSelect()
        {
            ShopModel.SubscribeType subscribeType =ShopModel.SubscribeType.Lifetime;
            
            UnSelectItem(0);
            UnSelectItem(1);
            //  已选中的 再选一次
            if (selectItemType == subscribeType)
            {
                return;
            }
 
            selectItemType = subscribeType;
            lifeTimeItem.isSelect.selectedIndex = 1;
            
            _preBtnStr = I18N.inst.MoStr("ui_shop_lifetime_11");
            ui.comPlanSelectedContent.tfBtnNext.text = _preBtnStr;
            
            // DataDotMgr.Collect( new DataDotTrialChooseQuarterly());
            CheckDiscount();
            CheckLifeTime();
        }

        private void CheckLifeTime()
        {
            ui.comPlanSelectedContent.tfDesc8.visible = selectItemType != ShopModel.SubscribeType.Lifetime;
        }
        #endregion
    }
}
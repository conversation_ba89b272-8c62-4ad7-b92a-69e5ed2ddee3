/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Shop
{
    public partial class SpeakPlanBottomSubscribePanel : UIBindT
    {
        public override string pkgName => "Shop";
        public override string comName => "SpeakPlanBottomSubscribePanel";

        public Controller ctrlType;
        public Controller discount;
        public GGraph imgBG;
        public GGraph btnBG;
        public GButton mask;
        public GImage bg;
        public GGraph btnClose;
        public GTextField discountTxt;
        public GButton btnSubscribe;
        public GRichTextField tfBtnPractice;
        public GRichTextField tfDesc;
        public GRichTextField tfTitle;
        public GRichTextField tfTitle2;
        public GRichTextField tfDesc2;
        public GGraph lifetimeNode;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrlType = com.GetControllerAt(0);
            discount = com.GetControllerAt(1);
            imgBG = (GGraph)com.GetChildAt(0);
            btnBG = (GGraph)com.GetChildAt(1);
            mask = (GButton)com.GetChildAt(2);
            bg = (GImage)com.GetChildAt(3);
            btnClose = (GGraph)com.GetChildAt(9);
            discountTxt = (GTextField)com.GetChildAt(11);
            btnSubscribe = (GButton)com.GetChildAt(12);
            tfBtnPractice = (GRichTextField)com.GetChildAt(13);
            tfDesc = (GRichTextField)com.GetChildAt(17);
            tfTitle = (GRichTextField)com.GetChildAt(18);
            tfTitle2 = (GRichTextField)com.GetChildAt(22);
            tfDesc2 = (GRichTextField)com.GetChildAt(23);
            lifetimeNode = (GGraph)com.GetChildAt(25);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrlType = null;
            discount = null;
            imgBG = null;
            btnBG = null;
            mask = null;
            bg = null;
            btnClose = null;
            discountTxt = null;
            btnSubscribe = null;
            tfBtnPractice = null;
            tfDesc = null;
            tfTitle = null;
            tfTitle2 = null;
            tfDesc2 = null;
            lifetimeNode = null;
        }
    }
}
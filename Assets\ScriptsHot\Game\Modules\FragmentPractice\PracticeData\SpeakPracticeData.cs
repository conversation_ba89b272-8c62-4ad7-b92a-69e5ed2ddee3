using System;
using System.Collections.Generic;
using System.Linq;
using Msg.question;
using UIBind.FragmentPractice;

namespace ScriptsHot.Game.Modules.FragmentPractice
{
    public class SpeakPracticeData : APracticeData
    {
        public const string RightAnswer = "yes";
        /// <summary>
        /// 跟读题
        /// </summary>
        public PB_ReadSenAndSpeakSenQuestion Practice { get; private set; }

        public SpeakPracticeData(Int64 grpId, PB_QuickPracticeInfo data) : base(grpId, data)
        {
            Practice = data.read_sen_and_speak_sen;
            AudioId = Practice.tts_id;
        }

        protected override string GetCorrectAnswer()
        {
            return RightAnswer;
        }

        protected override string GetAnswerContent()
        {
            return Practice.question;
        }

        public override string[] GetAnswerOptions()
        {
            return new string[] {};
        }

        protected override object GetQuestionObject()
        {
            return Practice;
        }

        public override string GetQuestionTypeLanguageKey()
        {
            return "ui_fragment_question_type_speak";
        }

        public override string GetQuestionComponentUrl()
        {
            return SimpleTextQuestion.url;
        }

        public override string GetAnswerComponentUrl()
        {
            return RecordAnswer.url;
        }

        public override string GetStem()
        {
            return Practice.question;
        }

        public override List<PB_BlankRange> GetBlankRanges()
        {
            return new List<PB_BlankRange>(0);
        }

        public override int[] GetAnswerIndexes()
        {
            return new int[0] {};
        }

        public override int GetOptionsCount()
        {
            return 0;
        }
        
        public override long[] GetAnswerOptionTts()
        {
            return new long[] { };
        }
        
        public override string[] GetAnswerOptionImage()
        {
            return new string[] { };
        }

        protected override string GetMeaningContent()
        {
            return Practice.translation;
        }
    }
}
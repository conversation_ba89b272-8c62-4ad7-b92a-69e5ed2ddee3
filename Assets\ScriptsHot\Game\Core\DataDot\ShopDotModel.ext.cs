using System.Collections;
using System.Collections.Generic;
using UnityEngine;



    public class PayWallDotHelper
    {
        public static UserSubscribeInfo SubscribeInfo= new UserSubscribeInfo();    //根据协议自动更新
        public static UserPropertyInfo PropertyInfo= new UserPropertyInfo();
      
        
        //替换曾经 ShopModel 在用的Purchase_source属性
        public static PaywallEntrySourceType LastSourcePage = PaywallEntrySourceType.none; //每过点入 entry的入口时 必须改写1次；退出时暂不做要求；如果访问路径高度复杂化需要重新设计
        
    }
    
    
    //
    //我们的订阅 subscribe 【表达行为中间态】 vs   member【结果状态】
    
    //1 
    //  member_status， member_type 目前都已经被丁然写进 最基础的BI参数里面了
    //  member_status 底层PB是subscribe_status相关的【需要这轮0714改】
    //  member 底层是 MemberType 暗含了是否已订阅的信息
    public class UserSubscribeInfo
    {
        private string subscribe_status;
        private string member_type;

        public void SetInfo(string subscribe_status, string member_type)
        {
            this.subscribe_status = subscribe_status;
            this.member_type = member_type;
        }
        public void Deconstruct(out string subscribe_status, out string member_type)
        {
            subscribe_status = this.subscribe_status;
            member_type = this.member_type;
        }
    }
    
    
    //2 subscribe_type 说的是所看见的UI页面本质是 display_page_type:包括 免费试用，折扣，未来还有原价
    public enum PaywallSubscribePageType{
        freetrial,
        discount,
        //origin 
    }
    
    //3 source_page: 非常不好定义的参数
    //  如果 首个点击入口处，立了flag，flag要管多久？ 如果用last flag则需要都跑一边看看 点击流程的拓扑形态
    //source_page:此页面的来源页面，e.g. popup（首页浮窗）/bottom_bar（首页底bar）/result_page（结算页）/onboarding_popup（引导页）/diamond_shop（钻石小店）/membership_icon（会员权益icon）/heart_icon（红心icon）/explore（探索页）/discount_popup（老用户折扣浮窗）；
    public enum PaywallEntrySourceType{
        none, //未设置时
        popup,//首页弹窗
        bottom_bar,// 首页bar(原来是黄色的)
        result_page,//（结算页）TODO 改名
        onboarding_popup,//（引导页）
        diamond_shop,//
        membership_icon, //首页右上角 icon）
        heart_icon,//（红心icon）
        homepage,
        explore,//ToDo-recheck 未赋值 （探索页的商业化paywall入口） 
        discount_popup,//（老用户折扣浮窗）
        discount_bottom_bar,//（老用户折扣bar？）
        onboarding_customize,
    }
    
    //4 users_diamond_amount:回传该行为触发时用户账户中的钻石量；
    //  users_temp_diamond_amount:回传该行为触发时用户账户中的临时钻石量；
    public class UserPropertyInfo
    {
        private CurrencyModel _currencyModel = null;  
        public void Deconstruct(out long users_diamond_amount, out long users_temp_diamond_amount)
        {
            if (_currencyModel == null)
            {
                _currencyModel= ModelManager.instance.GetModel(ModelConsts.CurrencyController) as CurrencyModel;
            } 
            users_diamond_amount = _currencyModel.GetEconomicInfo(EconomicType.Diamond).CurNum;
            users_temp_diamond_amount = _currencyModel.GetEconomicInfo(EconomicType.TemporaryDiamond).CurNum;
        }
    }

    
    //5 payAction  
    //subscribe_price:回传此次购买支付的价格
    //price_currency:回传此次购买支付价格的货币，e.g. EUR-欧元，USD-美元，GBP-英镑，JPY-日元，KRW-韩元
    //举例点击行为过近 特殊逻辑多，不纳入公共处理范畴 public static UserPayInfo PayInfo = new UserPayInfo();
    

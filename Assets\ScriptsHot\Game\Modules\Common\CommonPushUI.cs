﻿/* 
****************************************************
* 作者：LiuXinmiao
* 创建时间：2024/10/10 15:09:43 星期四
* 功能：Nothing
****************************************************
*/


using FairyGUI;

public class CommonPushUI: BaseUI<UIBind.CommonUI.CommonPushPanel>
{
    private string content;
    private string playerName;
    private string headUrl;
    private PushType type;
    private string timer;
    private int interval;
    private bool isLock;
    public CommonPushUI(string name) : base(name) { }
    public override string uiLayer => UILayerConsts.Top;
    protected override bool isFullScreen => true;
    protected override void OnInit(GComponent uiCom)
    {
     
    }

    protected override void OnShow()
    {
        if (isLock)
            return;
        if (!string.IsNullOrEmpty(timer))
        {
            TimerManager.instance.UnRegisterTimer(timer);
        }
        SoundManger.instance.PlayUI("click_Avatar");
        Refresh();
        interval = 2;
        isLock = true;
        timer = TimerManager.instance.RegisterTimer(Close, 1 * 1000, 2);
    }

    private void Close(int time)
    {
        isLock = false;
        interval = interval - 1;
        if (interval <= 0)
        {
            this.Hide();
        }
    }
    
    public void Open(PushType type,string playerName, string hearUrl, string content = null)
    {
        this.type = type;
        this.playerName = playerName;
        this.headUrl = hearUrl;
        this.content = content;
        if (this.isShow)
        {
            Refresh();
        }
        else
        {
            this.Show();
        }
    }

    private void Refresh()
    {
        if (type == PushType.Chat)
        {
            ui.pushCtrl.selectedIndex = 0;
            ui.headLoader.loader.url = headUrl;
            ui.playerName.text = playerName;
            ui.content.text = content;
        }
        else
        {
            ui.pushCtrl.selectedIndex = 1;
            ui.headLoader2.loader.url = headUrl;
            ui.playerName2.text = playerName;
            var mainModel = ModelManager.instance.GetModel<MainModel>(ModelConsts.Main);
            if (mainModel.btnMode == MainHeaderMode.Indoor)
            {
                ui.pushPosCtrl.selectedPage = "inDoor";
            }else if (mainModel.btnMode == MainHeaderMode.Outdoor)
            {
                ui.pushPosCtrl.selectedPage = "outDoor";
            }
        }
    }
}

public enum PushType
{
    Chat,
    Scene,
}

﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Grpc.Core;
using Msg;
using Msg.tts;
using UnityEngine;

public class TTSPreloader
{
    private static TTSPreloader _instance;
    public static TTSPreloader Instance
    {
        get
        {
            if (_instance == null)
                _instance = new TTSPreloader();
            return _instance;
        }
    }

    private const string AUDIO_FOLDER_NAME = "Audio";
    private const int MAX_CONCURRENT_DOWNLOADS = 8;
    private Queue<TTSVO> _downloadQueue = new();
    private List<Task> _activeDownloads = new();
    private Action _preloadCallback;
    private int _totalTasks;
    private int _completedTasks;

    public async Task PreloadTTS(List<long> ttsIds, Action preloadCallback = null)
    {
        _preloadCallback = preloadCallback;
        _completedTasks = 0;
        _totalTasks = ttsIds.Count;

        // 先检查哪些需要下载
        foreach (var ttsId in ttsIds)
        {
            if (!UrlExistedForRecordId(ttsId))
            {
                TTSVO vo = new TTSVO();
                vo.recordId = ttsId;
                _downloadQueue.Enqueue(vo);
            }
            else
            {
                _completedTasks++;
            }
        }

        //VFDebug.Log("TTSPreload load count :: " + _downloadQueue.Count);
        // 如果所有文件都已存在，直接回调
        if (_downloadQueue.Count == 0)
        {
            _preloadCallback?.Invoke();
            return;
        }

        // 开始下载
        await StartDownloads();
    }

    public void Clear()
    {
        _totalTasks = 0;
        _completedTasks = 0;
        _downloadQueue.Clear();
        _activeDownloads.Clear();
    }

    /// <summary>
    /// 初始化音频缓存目录（创建目录并清空旧文件）
    /// </summary>
    public void InitAudioCache()
    {
        // try
        // {
        //     // 获取音频文件夹路径
        //     string audioFolderPath = GetAudioFolderPath();
        //
        //     // 如果文件夹存在，删除所有文件
        //     if (Directory.Exists(audioFolderPath))
        //     {
        //         string[] files = Directory.GetFiles(audioFolderPath);
        //         foreach (string file in files)
        //         {
        //             try
        //             {
        //                 File.Delete(file);
        //             }
        //             catch (Exception ex)
        //             {
        //                 VFDebug.LogError($"Explore memory删除音频文件失败: {file}, 错误: {ex.Message}");
        //             }
        //         }
        //
        //         VFDebug.Log($"Explore memory清空音频文件夹，删除了 {files.Length} 个文件");
        //     }
        //     else
        //     {
        //         // 创建音频文件夹
        //         Directory.CreateDirectory(audioFolderPath);
        //         VFDebug.Log($"Explore memory创建音频文件夹: {audioFolderPath}");
        //     }
        // }
        // catch (Exception ex)
        // {
        //     VFDebug.LogError($"初始化音频缓存出错: {ex.Message}");
        // }
    }
    
    private async Task StartDownloads()
    {
        while (_downloadQueue.Count > 0 || _activeDownloads.Count > 0)
        {
            // 移除已完成的任务
            for (int i = _activeDownloads.Count - 1; i >= 0; i--)
            {
                if (_activeDownloads[i].IsCompleted)
                {
                    _activeDownloads.RemoveAt(i);
                    _completedTasks++;
                }
            }

            // 添加新任务
            while (_activeDownloads.Count < MAX_CONCURRENT_DOWNLOADS && _downloadQueue.Count > 0)
            {
                var ttsvo = _downloadQueue.Dequeue();
                var downloadTask = DownloadTTS(ttsvo);
                _activeDownloads.Add(downloadTask);
            }

            // 检查是否全部完成
            if (_completedTasks >= _totalTasks)
            {
                _preloadCallback?.Invoke();
                return;
            }

            await Task.Delay(100); // 避免CPU占用过高
        }
    }

    private async Task DownloadTTS(TTSVO ttsVO)
    {
        try
        {
            var reqMsg = new CS_GetTTSAudioReq { tts_record_id = ttsVO.recordId };
            var client = GRPCManager.instance.GetGrpcClient<TtsService.TtsServiceClient>((short)GameMSGID.CS_GetTTSAudioReq_ID);
            var call = client.GetTTSAudio(reqMsg);

            await foreach (var result in call.ResponseStream.ReadAllAsync())
            {
                if (result.code != (int)GameErrID.Ack_Success)
                {
                    VFDebug.LogError($"TTS: get rpc data error. RecordId: {ttsVO.recordId}, error:{result.code}");
                    return;
                }
                // 处理数据
                
                ttsVO.recordId = ttsVO.recordId;
                DealWithRpcData(ttsVO, result.data);
                if (result.data.is_last_audio && result.data.audio_clip.is_last_clip)
                {
                    SaveToLocal(ttsVO);
                    ttsVO.audioClipData = null;
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            VFDebug.LogError($"TTS download error for recordId {ttsVO.recordId}: {ex.Message}");
        }
    }

    private void DealWithRpcData(TTSVO ttsVO, PB_GetTTSAudioResp audioData)
    {
        var data = audioData.audio_clip.audio_clip.ToByteArray();
        if (data == null) return;
        int nowAudioLength = ttsVO.audioClipData.Length;
        Array.Resize(ref ttsVO.audioClipData, nowAudioLength + data.Length);
        Array.Copy(data, 0, ttsVO.audioClipData, nowAudioLength, data.Length);
    }

    private void SaveToLocal(TTSVO ttsVO)
    {
        bool success = true;
        var data = ttsVO.audioClipData;
        if (data.Length == 0)
        {
            VFDebug.Log("TTSManager TTS data isPreload length 0 id " + ttsVO.recordId);
        }
        try
        {
            // 修改保存路径到ExploreAudio子文件夹
            string url = UrlForSaveRecordId(ttsVO.recordId);
            
            File.WriteAllBytes(url, data);
        }
        catch (Exception ex)
        {
            VFDebug.Log("TTSManager 写文件失败: " + ex.Message);
        }
    }
    
    public bool UrlExistedForRecordId(long recordId)
    {
        string url = UrlForSaveRecordId(recordId);
        return File.Exists(url);
    }
    
    /// <summary>
    /// 获取音频文件夹路径
    /// </summary>
    private string GetAudioFolderPath()
    {
        return Application.persistentDataPath + "/" + AUDIO_FOLDER_NAME;
    }
    
    public string UrlForSaveRecordId(long recordId)
    {
        // 确保音频文件夹存在
        string audioFolderPath = GetAudioFolderPath();
        if (!Directory.Exists(audioFolderPath))
        {
            Directory.CreateDirectory(audioFolderPath);
        }
            
        // 修改保存路径到ExploreAudio子文件夹
        return Path.Combine(audioFolderPath, recordId.ToString());
    }
    
    public string UrlForLoadRecordId(long recordId)
    {
        string ret = UrlForSaveRecordId(recordId);
        //Debug.Log("before ret url: " + ret);
#if UNITY_IOS || UNITY_STANDALONE_OSX || UNITY_ANDROID 
        ret = "file://" + ret;
        // VFDebug.Log("TTSManager url: " + ret);
#endif         
        return ret;
    }
}

class TTSVO
{
    public long recordId;
    public byte[] audioClipData = new byte[0];
}



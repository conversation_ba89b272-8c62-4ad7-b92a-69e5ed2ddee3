/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Settlement
{
    public partial class SettlementAUAPanel : UIBindT
    {
        public override string pkgName => "Settlement";
        public override string comName => "SettlementAUAPanel";

        public GImage imgBG;
        public CompAUASettle compAUA;
        public CompBottom compBottom;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            imgBG = (GImage)com.GetChildAt(0);
            compAUA = new CompAUASettle();
            compAUA.Construct(com.GetChildAt(1).asCom);
            compBottom = new CompBottom();
            compBottom.Construct(com.GetChildAt(2).asCom);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            imgBG = null;
            compAUA.Dispose();
            compAUA = null;
            compBottom.Dispose();
            compBottom = null;
        }
    }
}
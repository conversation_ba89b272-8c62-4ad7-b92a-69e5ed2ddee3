﻿namespace ScriptsHot.Game.Modules.ChatLogicNew.ChatState
{

    using Msg.basic;
    using Scene.Level;
    using Scene.Level.Component;
    using Settlement;
    using Task;
    using UnityEngine;

    public class ChatLogicStateEndProgress : ChatLogicStateBase
    {
        public ChatLogicStateEndProgress(ChatTypeBase chat) : base(ChatStateName.EndProgress,chat)
        {
        }
        private string timerName = "";
        private const int TWEENTIME = 500;
        private ChatLogicModel ChatLogicModel => ModelManager.instance.GetModel<ChatLogicModel>(ModelConsts.ChatLogic);
        public override void OnEnter(params object[] args)
        {
            base.OnEnter(args);
            if (!string.IsNullOrEmpty(timerName))
            {
                TimerManager.instance.UnRegisterTimer(timerName);
                timerName = "";
            }
            Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
            Notifier.instance.SendNotification(NotifyConsts.StopStreamAudioTTs);
            Notifier.instance.SendNotification(NotifyConsts.ChatNewExit);

            // ControllerManager.instance.GetController<LearnPathController>(ModelConsts.LearnPath).SendGetUserGoalNodeOnEntergame();

            timerName = TimerManager.instance.RegisterTimer((a)=>PlayTween(), 500, 1);

            ControllerManager.instance.GetController<SettlementController>(ModelConsts.Settlement)
                .RequestDialogResult(_curChat.DialogTaskAck.data.dialog_id, _curChat.CurChatInfo.chatMode,
                    ChatLogicModel.CurRoundId, _curChat.DialogTaskAck.data.task_record_id,_curChat.CurChatInfo.mainPathParams,
                    _curChat.CurChatInfo.avatarId, _curChat.CurChatInfo.taskId, _curChat.CurChatInfo.topicId);
        }

        private void PlayTween()
        {
            if (!string.IsNullOrEmpty(timerName))
            {
                TimerManager.instance.UnRegisterTimer(timerName);
                timerName = "";
            }

            Notifier.instance.SendNotification(NotifyConsts.PlayTweenShowCell, new Vector3(0.1f, 0.4f, 50));
            Notifier.instance.SendNotification(NotifyConsts.DismissWithAnimation);
            ControllerManager.instance.GetController<TaskController>(ModelConsts.Task).DismissWithAnimation();
            Notifier.instance.SendNotification(NotifyConsts.MainHeaderVisible);
            timerName = TimerManager.instance.RegisterTimer((a) => TweenOver(), TWEENTIME, 1);
        }

        private void TweenOver()
        {
            if (!string.IsNullOrEmpty(timerName))
            {
                TimerManager.instance.UnRegisterTimer(timerName);
                timerName = "";
            }
            if (this._manager.GetModel<ChatModel>(ModelConsts.Chat).chatMode == PB_DialogMode.OnBoarding)
            {
                this._manager.GetController<MainController>(ModelConsts.Main).PlayNpcToMeet();
            }
            else
            {
                PlayAction("clapping");
            }

            timerName = TimerManager.instance.RegisterTimer((a)=>PlayActionOver(), 3000, 1);
            
        }

        private void PlayAction(string actionName)
        {
            SceneController sceneController = this._manager.GetController<SceneController>(ModelConsts.Scene);
            Level scene = sceneController.scene;
            if (scene.sceneType != ESceneType.HomepageRoom)
            {
                Unit unit = scene.GetComponent<UnitComponent>().GetUnitByAvatarTid(_manager.CurChat.CurChatInfo.avatarId);
                if (unit == null)
                {
                    Debug.LogError("modAvatar  unit.uid is error ");
                    PlayActionOver();
                    return;
                }

                global::Avatar avatar = scene.GetComponent<AvatarComponent>().GetAvatar(unit.uid);
                if (avatar != null)
                    avatar.PlayAction(actionName);
            }
            else
            {
                var hpLevel =  scene as ScriptsHot.Game.Modules.Scene.Level.Homepage.CenterHomeLevel;
                var avatar = hpLevel.GetComponent<AvatarComponent>().GetHomePageChatAvatar();
                if (avatar != null)
                    avatar.PlayAction(actionName);
            }
        }

        private void PlayActionOver()
        {
            Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.ChatHomePage);
            Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.ChatScore);
            this._manager.GetController<TaskController>(ModelConsts.Task).HideProgressTaskUI();
            this._manager.GetModel<TaskModel>(ModelConsts.Task).ClearAllData();
            if (!string.IsNullOrEmpty(timerName))
            {
                TimerManager.instance.UnRegisterTimer(timerName);
                timerName = "";
            }

            _curChat.ChangeState(ChatStateName.Exit);
            _curChat.ChangeState(ChatStateName.Settlement);
        }

        public override void OnExit()
        {
            base.OnExit();
            if (this._curChat.CurChatInfo.chatMode != PB_DialogMode.OnBoarding)
                PlayAction("Idle");
            if (!string.IsNullOrEmpty(timerName))
            {
                TimerManager.instance.UnRegisterTimer(timerName);
                timerName = "";
            }
        }
    }
}
// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/notification/notification.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.notification {

  /// <summary>Holder for reflection information generated from protobuf/notification/notification.proto</summary>
  public static partial class NotificationReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/notification/notification.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static NotificationReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cihwcm90b2J1Zi9ub3RpZmljYXRpb24vbm90aWZpY2F0aW9uLnByb3RvGhtw",
            "cm90b2J1Zi9iYXNpYy9jb21tb24ucHJvdG8aIHByb3RvYnVmL25vdGlmaWNh",
            "dGlvbi9iYXNlLnByb3RvIoMBChhDU19SZWdpc3RlclVzZXJEZXZpY2VSZXES",
            "EQoJZGV2aWNlX2lkGAEgASgJEiMKC2RldmljZV90eXBlGAIgASgOMg4uUEJf",
            "RGV2aWNlVHlwZRIcCgdjaGFubmVsGAMgASgOMgsuUEJfQ2hhbm5lbBIRCglk",
            "ZXZpY2VfdWsYBCABKAkiNQoYU0NfUmVnaXN0ZXJVc2VyRGV2aWNlQWNrEhkK",
            "BGNvZGUYASABKA4yCy5QQl9CaXpDb2RlIuQDChZTU19TZW5kTm90aWZpY2F0",
            "aW9uUmVxEjEKDGNoYW5uZWxfdHlwZRgBIAEoDjIbLlBCX05vdGlmaWNhdGlv",
            "bkNoYW5uZWxUeXBlEg0KBXRpdGxlGAIgASgJEg8KB2NvbnRlbnQYAyABKAkS",
            "MwoGZXh0cmFzGAQgAygLMiMuU1NfU2VuZE5vdGlmaWNhdGlvblJlcS5FeHRy",
            "YXNFbnRyeRIPCgd1c2VyX2lkGAUgASgDEj8KDHJlcGxhY2VtZW50cxgGIAMo",
            "CzIpLlNTX1NlbmROb3RpZmljYXRpb25SZXEuUmVwbGFjZW1lbnRzRW50cnkS",
            "RAoPdWlfcmVwbGFjZW1lbnRzGAcgAygLMisuU1NfU2VuZE5vdGlmaWNhdGlv",
            "blJlcS5VaVJlcGxhY2VtZW50c0VudHJ5Eg8KB3BsYW5faWQYCCABKAkaLQoL",
            "RXh0cmFzRW50cnkSCwoDa2V5GAEgASgJEg0KBXZhbHVlGAIgASgJOgI4ARoz",
            "ChFSZXBsYWNlbWVudHNFbnRyeRILCgNrZXkYASABKAkSDQoFdmFsdWUYAiAB",
            "KAk6AjgBGjUKE1VpUmVwbGFjZW1lbnRzRW50cnkSCwoDa2V5GAEgASgJEg0K",
            "BXZhbHVlGAIgASgJOgI4ASIzChZTU19TZW5kTm90aWZpY2F0aW9uQWNrEhkK",
            "BGNvZGUYASABKA4yCy5QQl9CaXpDb2RlIhcKFUNTX0dldFN5c3RlbU5vdGlj",
            "ZVJlcSJSChVTQ19HZXRTeXN0ZW1Ob3RpY2VBY2sSGQoEY29kZRgBIAEoDjIL",
            "LlBCX0JpekNvZGUSHgoEZGF0YRgCIAEoCzIQLlBCX1N5c3RlbU5vdGljZSKL",
            "AQoPUEJfU3lzdGVtTm90aWNlEiIKBHR5cGUYASABKA4yFC5QQl9TeXN0ZW1O",
            "b3RpY2VUeXBlEg4KBmJpel9pZBgCIAEoCRINCgV0aXRsZRgDIAEoCRIPCgdj",
            "b250ZW50GAQgASgJEiQKBWV4dHJhGAUgASgLMhUuUEJfU3lzdGVtTm90aWNl",
            "RXh0cmEiOQoUUEJfU3lzdGVtTm90aWNlRXh0cmESIQoHYnV0dG9ucxgBIAMo",
            "CzIQLlBCX05vdGljZUJ1dHRvbiJhCg9QQl9Ob3RpY2VCdXR0b24SDAoEdGV4",
            "dBgBIAEoCRIiCgR0eXBlGAIgASgOMhQuUEJfTm90aWNlQnV0dG9uVHlwZRIL",
            "CgN1cmwYAyABKAkSDwoHaWNvbl9pZBgEIAEoBSozChNQQl9TeXN0ZW1Ob3Rp",
            "Y2VUeXBlEg4KClNOX1VOS05PV04QABIMCghIT01FUEFHRRABKjkKE1BCX05v",
            "dGljZUJ1dHRvblR5cGUSDgoKTkJfVU5LTk9XThAAEgcKA1VSTBABEgkKBUNM",
            "T1NFEAJCNFofdmZfcHJvdG9idWYvc2VydmVyL25vdGlmaWNhdGlvbqoCEE1z",
            "Zy5ub3RpZmljYXRpb25iBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.basic.CommonReflection.Descriptor, global::Msg.notification.BaseReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.notification.PB_SystemNoticeType), typeof(global::Msg.notification.PB_NoticeButtonType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.notification.CS_RegisterUserDeviceReq), global::Msg.notification.CS_RegisterUserDeviceReq.Parser, new[]{ "device_id", "device_type", "channel", "device_uk" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.notification.SC_RegisterUserDeviceAck), global::Msg.notification.SC_RegisterUserDeviceAck.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.notification.SS_SendNotificationReq), global::Msg.notification.SS_SendNotificationReq.Parser, new[]{ "channel_type", "title", "content", "extras", "user_id", "replacements", "ui_replacements", "plan_id" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, null, null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.notification.SS_SendNotificationAck), global::Msg.notification.SS_SendNotificationAck.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.notification.CS_GetSystemNoticeReq), global::Msg.notification.CS_GetSystemNoticeReq.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.notification.SC_GetSystemNoticeAck), global::Msg.notification.SC_GetSystemNoticeAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.notification.PB_SystemNotice), global::Msg.notification.PB_SystemNotice.Parser, new[]{ "type", "biz_id", "title", "content", "extra" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.notification.PB_SystemNoticeExtra), global::Msg.notification.PB_SystemNoticeExtra.Parser, new[]{ "buttons" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.notification.PB_NoticeButton), global::Msg.notification.PB_NoticeButton.Parser, new[]{ "text", "type", "url", "icon_id" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum PB_SystemNoticeType {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("SN_UNKNOWN")] SN_UNKNOWN = 0,
    /// <summary>
    /// 首页公告
    /// </summary>
    [pbr::OriginalName("HOMEPAGE")] HOMEPAGE = 1,
  }

  public enum PB_NoticeButtonType {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("NB_UNKNOWN")] NB_UNKNOWN = 0,
    /// <summary>
    /// 跳转url
    /// </summary>
    [pbr::OriginalName("URL")] URL = 1,
    /// <summary>
    /// 关闭
    /// </summary>
    [pbr::OriginalName("CLOSE")] CLOSE = 2,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_RegisterUserDeviceReq : pb::IMessage<CS_RegisterUserDeviceReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_RegisterUserDeviceReq> _parser = new pb::MessageParser<CS_RegisterUserDeviceReq>(() => new CS_RegisterUserDeviceReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_RegisterUserDeviceReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.notification.NotificationReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_RegisterUserDeviceReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_RegisterUserDeviceReq(CS_RegisterUserDeviceReq other) : this() {
      device_id_ = other.device_id_;
      device_type_ = other.device_type_;
      channel_ = other.channel_;
      device_uk_ = other.device_uk_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_RegisterUserDeviceReq Clone() {
      return new CS_RegisterUserDeviceReq(this);
    }

    /// <summary>Field number for the "device_id" field.</summary>
    public const int device_idFieldNumber = 1;
    private string device_id_ = "";
    /// <summary>
    /// 设备ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string device_id {
      get { return device_id_; }
      set {
        device_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "device_type" field.</summary>
    public const int device_typeFieldNumber = 2;
    private global::Msg.notification.PB_DeviceType device_type_ = global::Msg.notification.PB_DeviceType.PB_DeviceType_None;
    /// <summary>
    /// 设备类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.notification.PB_DeviceType device_type {
      get { return device_type_; }
      set {
        device_type_ = value;
      }
    }

    /// <summary>Field number for the "channel" field.</summary>
    public const int channelFieldNumber = 3;
    private global::Msg.notification.PB_Channel channel_ = global::Msg.notification.PB_Channel.PB_Channel_None;
    /// <summary>
    /// 通道来源
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.notification.PB_Channel channel {
      get { return channel_; }
      set {
        channel_ = value;
      }
    }

    /// <summary>Field number for the "device_uk" field.</summary>
    public const int device_ukFieldNumber = 4;
    private string device_uk_ = "";
    /// <summary>
    ///  用户设备号
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string device_uk {
      get { return device_uk_; }
      set {
        device_uk_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_RegisterUserDeviceReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_RegisterUserDeviceReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (device_id != other.device_id) return false;
      if (device_type != other.device_type) return false;
      if (channel != other.channel) return false;
      if (device_uk != other.device_uk) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (device_id.Length != 0) hash ^= device_id.GetHashCode();
      if (device_type != global::Msg.notification.PB_DeviceType.PB_DeviceType_None) hash ^= device_type.GetHashCode();
      if (channel != global::Msg.notification.PB_Channel.PB_Channel_None) hash ^= channel.GetHashCode();
      if (device_uk.Length != 0) hash ^= device_uk.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (device_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(device_id);
      }
      if (device_type != global::Msg.notification.PB_DeviceType.PB_DeviceType_None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) device_type);
      }
      if (channel != global::Msg.notification.PB_Channel.PB_Channel_None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) channel);
      }
      if (device_uk.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(device_uk);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (device_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(device_id);
      }
      if (device_type != global::Msg.notification.PB_DeviceType.PB_DeviceType_None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) device_type);
      }
      if (channel != global::Msg.notification.PB_Channel.PB_Channel_None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) channel);
      }
      if (device_uk.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(device_uk);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (device_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(device_id);
      }
      if (device_type != global::Msg.notification.PB_DeviceType.PB_DeviceType_None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) device_type);
      }
      if (channel != global::Msg.notification.PB_Channel.PB_Channel_None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) channel);
      }
      if (device_uk.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(device_uk);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_RegisterUserDeviceReq other) {
      if (other == null) {
        return;
      }
      if (other.device_id.Length != 0) {
        device_id = other.device_id;
      }
      if (other.device_type != global::Msg.notification.PB_DeviceType.PB_DeviceType_None) {
        device_type = other.device_type;
      }
      if (other.channel != global::Msg.notification.PB_Channel.PB_Channel_None) {
        channel = other.channel;
      }
      if (other.device_uk.Length != 0) {
        device_uk = other.device_uk;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            device_id = input.ReadString();
            break;
          }
          case 16: {
            device_type = (global::Msg.notification.PB_DeviceType) input.ReadEnum();
            break;
          }
          case 24: {
            channel = (global::Msg.notification.PB_Channel) input.ReadEnum();
            break;
          }
          case 34: {
            device_uk = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            device_id = input.ReadString();
            break;
          }
          case 16: {
            device_type = (global::Msg.notification.PB_DeviceType) input.ReadEnum();
            break;
          }
          case 24: {
            channel = (global::Msg.notification.PB_Channel) input.ReadEnum();
            break;
          }
          case 34: {
            device_uk = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_RegisterUserDeviceAck : pb::IMessage<SC_RegisterUserDeviceAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_RegisterUserDeviceAck> _parser = new pb::MessageParser<SC_RegisterUserDeviceAck>(() => new SC_RegisterUserDeviceAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_RegisterUserDeviceAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.notification.NotificationReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RegisterUserDeviceAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RegisterUserDeviceAck(SC_RegisterUserDeviceAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RegisterUserDeviceAck Clone() {
      return new SC_RegisterUserDeviceAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_RegisterUserDeviceAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_RegisterUserDeviceAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_RegisterUserDeviceAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SendNotificationReq : pb::IMessage<SS_SendNotificationReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SendNotificationReq> _parser = new pb::MessageParser<SS_SendNotificationReq>(() => new SS_SendNotificationReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SendNotificationReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.notification.NotificationReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SendNotificationReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SendNotificationReq(SS_SendNotificationReq other) : this() {
      channel_type_ = other.channel_type_;
      title_ = other.title_;
      content_ = other.content_;
      extras_ = other.extras_.Clone();
      user_id_ = other.user_id_;
      replacements_ = other.replacements_.Clone();
      ui_replacements_ = other.ui_replacements_.Clone();
      plan_id_ = other.plan_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SendNotificationReq Clone() {
      return new SS_SendNotificationReq(this);
    }

    /// <summary>Field number for the "channel_type" field.</summary>
    public const int channel_typeFieldNumber = 1;
    private global::Msg.notification.PB_NotificationChannelType channel_type_ = global::Msg.notification.PB_NotificationChannelType.PB_NotificationChannelType_None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.notification.PB_NotificationChannelType channel_type {
      get { return channel_type_; }
      set {
        channel_type_ = value;
      }
    }

    /// <summary>Field number for the "title" field.</summary>
    public const int titleFieldNumber = 2;
    private string title_ = "";
    /// <summary>
    /// 未使用
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string title {
      get { return title_; }
      set {
        title_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "content" field.</summary>
    public const int contentFieldNumber = 3;
    private string content_ = "";
    /// <summary>
    /// 可以是文案，也可以是多语言表中的 key，推荐使用key。https://visionflow.feishu.cn/wiki/FibVwX0GKiL4YykfPJAc0myHnGb?table=tblSe10tsv0TnLIe&amp;view=vew7i5O5gg
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string content {
      get { return content_; }
      set {
        content_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "extras" field.</summary>
    public const int extrasFieldNumber = 4;
    private static readonly pbc::MapField<string, string>.Codec _map_extras_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 34);
    private readonly pbc::MapField<string, string> extras_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> extras {
      get { return extras_; }
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 5;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "replacements" field.</summary>
    public const int replacementsFieldNumber = 6;
    private static readonly pbc::MapField<string, string>.Codec _map_replacements_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 50);
    private readonly pbc::MapField<string, string> replacements_ = new pbc::MapField<string, string>();
    /// <summary>
    /// 替换占位符数据&lt;key，value>，发送服务会将这些数据在文案中做替换
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> replacements {
      get { return replacements_; }
    }

    /// <summary>Field number for the "ui_replacements" field.</summary>
    public const int ui_replacementsFieldNumber = 7;
    private static readonly pbc::MapField<string, string>.Codec _map_ui_replacements_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 58);
    private readonly pbc::MapField<string, string> ui_replacements_ = new pbc::MapField<string, string>();
    /// <summary>
    /// 替换占位符数据&lt;key，value>，发送服务会将这些数据在跳转ui做替换
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> ui_replacements {
      get { return ui_replacements_; }
    }

    /// <summary>Field number for the "plan_id" field.</summary>
    public const int plan_idFieldNumber = 8;
    private string plan_id_ = "";
    /// <summary>
    /// 计划id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string plan_id {
      get { return plan_id_; }
      set {
        plan_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SendNotificationReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SendNotificationReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (channel_type != other.channel_type) return false;
      if (title != other.title) return false;
      if (content != other.content) return false;
      if (!extras.Equals(other.extras)) return false;
      if (user_id != other.user_id) return false;
      if (!replacements.Equals(other.replacements)) return false;
      if (!ui_replacements.Equals(other.ui_replacements)) return false;
      if (plan_id != other.plan_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (channel_type != global::Msg.notification.PB_NotificationChannelType.PB_NotificationChannelType_None) hash ^= channel_type.GetHashCode();
      if (title.Length != 0) hash ^= title.GetHashCode();
      if (content.Length != 0) hash ^= content.GetHashCode();
      hash ^= extras.GetHashCode();
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      hash ^= replacements.GetHashCode();
      hash ^= ui_replacements.GetHashCode();
      if (plan_id.Length != 0) hash ^= plan_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (channel_type != global::Msg.notification.PB_NotificationChannelType.PB_NotificationChannelType_None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) channel_type);
      }
      if (title.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(title);
      }
      if (content.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(content);
      }
      extras_.WriteTo(output, _map_extras_codec);
      if (user_id != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(user_id);
      }
      replacements_.WriteTo(output, _map_replacements_codec);
      ui_replacements_.WriteTo(output, _map_ui_replacements_codec);
      if (plan_id.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(plan_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (channel_type != global::Msg.notification.PB_NotificationChannelType.PB_NotificationChannelType_None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) channel_type);
      }
      if (title.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(title);
      }
      if (content.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(content);
      }
      extras_.WriteTo(ref output, _map_extras_codec);
      if (user_id != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(user_id);
      }
      replacements_.WriteTo(ref output, _map_replacements_codec);
      ui_replacements_.WriteTo(ref output, _map_ui_replacements_codec);
      if (plan_id.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(plan_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (channel_type != global::Msg.notification.PB_NotificationChannelType.PB_NotificationChannelType_None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) channel_type);
      }
      if (title.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(title);
      }
      if (content.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(content);
      }
      size += extras_.CalculateSize(_map_extras_codec);
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      size += replacements_.CalculateSize(_map_replacements_codec);
      size += ui_replacements_.CalculateSize(_map_ui_replacements_codec);
      if (plan_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(plan_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SendNotificationReq other) {
      if (other == null) {
        return;
      }
      if (other.channel_type != global::Msg.notification.PB_NotificationChannelType.PB_NotificationChannelType_None) {
        channel_type = other.channel_type;
      }
      if (other.title.Length != 0) {
        title = other.title;
      }
      if (other.content.Length != 0) {
        content = other.content;
      }
      extras_.MergeFrom(other.extras_);
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      replacements_.MergeFrom(other.replacements_);
      ui_replacements_.MergeFrom(other.ui_replacements_);
      if (other.plan_id.Length != 0) {
        plan_id = other.plan_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            channel_type = (global::Msg.notification.PB_NotificationChannelType) input.ReadEnum();
            break;
          }
          case 18: {
            title = input.ReadString();
            break;
          }
          case 26: {
            content = input.ReadString();
            break;
          }
          case 34: {
            extras_.AddEntriesFrom(input, _map_extras_codec);
            break;
          }
          case 40: {
            user_id = input.ReadInt64();
            break;
          }
          case 50: {
            replacements_.AddEntriesFrom(input, _map_replacements_codec);
            break;
          }
          case 58: {
            ui_replacements_.AddEntriesFrom(input, _map_ui_replacements_codec);
            break;
          }
          case 66: {
            plan_id = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            channel_type = (global::Msg.notification.PB_NotificationChannelType) input.ReadEnum();
            break;
          }
          case 18: {
            title = input.ReadString();
            break;
          }
          case 26: {
            content = input.ReadString();
            break;
          }
          case 34: {
            extras_.AddEntriesFrom(ref input, _map_extras_codec);
            break;
          }
          case 40: {
            user_id = input.ReadInt64();
            break;
          }
          case 50: {
            replacements_.AddEntriesFrom(ref input, _map_replacements_codec);
            break;
          }
          case 58: {
            ui_replacements_.AddEntriesFrom(ref input, _map_ui_replacements_codec);
            break;
          }
          case 66: {
            plan_id = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SendNotificationAck : pb::IMessage<SS_SendNotificationAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SendNotificationAck> _parser = new pb::MessageParser<SS_SendNotificationAck>(() => new SS_SendNotificationAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SendNotificationAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.notification.NotificationReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SendNotificationAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SendNotificationAck(SS_SendNotificationAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SendNotificationAck Clone() {
      return new SS_SendNotificationAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SendNotificationAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SendNotificationAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SendNotificationAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetSystemNoticeReq : pb::IMessage<CS_GetSystemNoticeReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetSystemNoticeReq> _parser = new pb::MessageParser<CS_GetSystemNoticeReq>(() => new CS_GetSystemNoticeReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetSystemNoticeReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.notification.NotificationReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetSystemNoticeReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetSystemNoticeReq(CS_GetSystemNoticeReq other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetSystemNoticeReq Clone() {
      return new CS_GetSystemNoticeReq(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetSystemNoticeReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetSystemNoticeReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetSystemNoticeReq other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetSystemNoticeAck : pb::IMessage<SC_GetSystemNoticeAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetSystemNoticeAck> _parser = new pb::MessageParser<SC_GetSystemNoticeAck>(() => new SC_GetSystemNoticeAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetSystemNoticeAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.notification.NotificationReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetSystemNoticeAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetSystemNoticeAck(SC_GetSystemNoticeAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetSystemNoticeAck Clone() {
      return new SC_GetSystemNoticeAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.notification.PB_SystemNotice data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.notification.PB_SystemNotice data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetSystemNoticeAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetSystemNoticeAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetSystemNoticeAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.notification.PB_SystemNotice();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.notification.PB_SystemNotice();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.notification.PB_SystemNotice();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_SystemNotice : pb::IMessage<PB_SystemNotice>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_SystemNotice> _parser = new pb::MessageParser<PB_SystemNotice>(() => new PB_SystemNotice());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_SystemNotice> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.notification.NotificationReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SystemNotice() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SystemNotice(PB_SystemNotice other) : this() {
      type_ = other.type_;
      biz_id_ = other.biz_id_;
      title_ = other.title_;
      content_ = other.content_;
      extra_ = other.extra_ != null ? other.extra_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SystemNotice Clone() {
      return new PB_SystemNotice(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int typeFieldNumber = 1;
    private global::Msg.notification.PB_SystemNoticeType type_ = global::Msg.notification.PB_SystemNoticeType.SN_UNKNOWN;
    /// <summary>
    /// 公告类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.notification.PB_SystemNoticeType type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "biz_id" field.</summary>
    public const int biz_idFieldNumber = 2;
    private string biz_id_ = "";
    /// <summary>
    /// 公告id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string biz_id {
      get { return biz_id_; }
      set {
        biz_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "title" field.</summary>
    public const int titleFieldNumber = 3;
    private string title_ = "";
    /// <summary>
    /// 标题
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string title {
      get { return title_; }
      set {
        title_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "content" field.</summary>
    public const int contentFieldNumber = 4;
    private string content_ = "";
    /// <summary>
    /// 内容
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string content {
      get { return content_; }
      set {
        content_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "extra" field.</summary>
    public const int extraFieldNumber = 5;
    private global::Msg.notification.PB_SystemNoticeExtra extra_;
    /// <summary>
    /// 额外信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.notification.PB_SystemNoticeExtra extra {
      get { return extra_; }
      set {
        extra_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_SystemNotice);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_SystemNotice other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (type != other.type) return false;
      if (biz_id != other.biz_id) return false;
      if (title != other.title) return false;
      if (content != other.content) return false;
      if (!object.Equals(extra, other.extra)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (type != global::Msg.notification.PB_SystemNoticeType.SN_UNKNOWN) hash ^= type.GetHashCode();
      if (biz_id.Length != 0) hash ^= biz_id.GetHashCode();
      if (title.Length != 0) hash ^= title.GetHashCode();
      if (content.Length != 0) hash ^= content.GetHashCode();
      if (extra_ != null) hash ^= extra.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (type != global::Msg.notification.PB_SystemNoticeType.SN_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) type);
      }
      if (biz_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(biz_id);
      }
      if (title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(title);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      if (extra_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(extra);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (type != global::Msg.notification.PB_SystemNoticeType.SN_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) type);
      }
      if (biz_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(biz_id);
      }
      if (title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(title);
      }
      if (content.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(content);
      }
      if (extra_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(extra);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (type != global::Msg.notification.PB_SystemNoticeType.SN_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) type);
      }
      if (biz_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(biz_id);
      }
      if (title.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(title);
      }
      if (content.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(content);
      }
      if (extra_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(extra);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_SystemNotice other) {
      if (other == null) {
        return;
      }
      if (other.type != global::Msg.notification.PB_SystemNoticeType.SN_UNKNOWN) {
        type = other.type;
      }
      if (other.biz_id.Length != 0) {
        biz_id = other.biz_id;
      }
      if (other.title.Length != 0) {
        title = other.title;
      }
      if (other.content.Length != 0) {
        content = other.content;
      }
      if (other.extra_ != null) {
        if (extra_ == null) {
          extra = new global::Msg.notification.PB_SystemNoticeExtra();
        }
        extra.MergeFrom(other.extra);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            type = (global::Msg.notification.PB_SystemNoticeType) input.ReadEnum();
            break;
          }
          case 18: {
            biz_id = input.ReadString();
            break;
          }
          case 26: {
            title = input.ReadString();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 42: {
            if (extra_ == null) {
              extra = new global::Msg.notification.PB_SystemNoticeExtra();
            }
            input.ReadMessage(extra);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            type = (global::Msg.notification.PB_SystemNoticeType) input.ReadEnum();
            break;
          }
          case 18: {
            biz_id = input.ReadString();
            break;
          }
          case 26: {
            title = input.ReadString();
            break;
          }
          case 34: {
            content = input.ReadString();
            break;
          }
          case 42: {
            if (extra_ == null) {
              extra = new global::Msg.notification.PB_SystemNoticeExtra();
            }
            input.ReadMessage(extra);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_SystemNoticeExtra : pb::IMessage<PB_SystemNoticeExtra>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_SystemNoticeExtra> _parser = new pb::MessageParser<PB_SystemNoticeExtra>(() => new PB_SystemNoticeExtra());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_SystemNoticeExtra> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.notification.NotificationReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SystemNoticeExtra() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SystemNoticeExtra(PB_SystemNoticeExtra other) : this() {
      buttons_ = other.buttons_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SystemNoticeExtra Clone() {
      return new PB_SystemNoticeExtra(this);
    }

    /// <summary>Field number for the "buttons" field.</summary>
    public const int buttonsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.notification.PB_NoticeButton> _repeated_buttons_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.notification.PB_NoticeButton.Parser);
    private readonly pbc::RepeatedField<global::Msg.notification.PB_NoticeButton> buttons_ = new pbc::RepeatedField<global::Msg.notification.PB_NoticeButton>();
    /// <summary>
    /// 按钮列表 
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.notification.PB_NoticeButton> buttons {
      get { return buttons_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_SystemNoticeExtra);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_SystemNoticeExtra other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!buttons_.Equals(other.buttons_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= buttons_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      buttons_.WriteTo(output, _repeated_buttons_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      buttons_.WriteTo(ref output, _repeated_buttons_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += buttons_.CalculateSize(_repeated_buttons_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_SystemNoticeExtra other) {
      if (other == null) {
        return;
      }
      buttons_.Add(other.buttons_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            buttons_.AddEntriesFrom(input, _repeated_buttons_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            buttons_.AddEntriesFrom(ref input, _repeated_buttons_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_NoticeButton : pb::IMessage<PB_NoticeButton>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_NoticeButton> _parser = new pb::MessageParser<PB_NoticeButton>(() => new PB_NoticeButton());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_NoticeButton> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.notification.NotificationReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_NoticeButton() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_NoticeButton(PB_NoticeButton other) : this() {
      text_ = other.text_;
      type_ = other.type_;
      url_ = other.url_;
      icon_id_ = other.icon_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_NoticeButton Clone() {
      return new PB_NoticeButton(this);
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 1;
    private string text_ = "";
    /// <summary>
    /// 按钮文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int typeFieldNumber = 2;
    private global::Msg.notification.PB_NoticeButtonType type_ = global::Msg.notification.PB_NoticeButtonType.NB_UNKNOWN;
    /// <summary>
    /// 按钮类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.notification.PB_NoticeButtonType type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "url" field.</summary>
    public const int urlFieldNumber = 3;
    private string url_ = "";
    /// <summary>
    /// 跳转url
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string url {
      get { return url_; }
      set {
        url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "icon_id" field.</summary>
    public const int icon_idFieldNumber = 4;
    private int icon_id_;
    /// <summary>
    /// 图标id, 如果为空，则不显示图标。1：whatsapp的电话图标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int icon_id {
      get { return icon_id_; }
      set {
        icon_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_NoticeButton);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_NoticeButton other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (text != other.text) return false;
      if (type != other.type) return false;
      if (url != other.url) return false;
      if (icon_id != other.icon_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (type != global::Msg.notification.PB_NoticeButtonType.NB_UNKNOWN) hash ^= type.GetHashCode();
      if (url.Length != 0) hash ^= url.GetHashCode();
      if (icon_id != 0) hash ^= icon_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (text.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(text);
      }
      if (type != global::Msg.notification.PB_NoticeButtonType.NB_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) type);
      }
      if (url.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(url);
      }
      if (icon_id != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(icon_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (text.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(text);
      }
      if (type != global::Msg.notification.PB_NoticeButtonType.NB_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) type);
      }
      if (url.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(url);
      }
      if (icon_id != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(icon_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (type != global::Msg.notification.PB_NoticeButtonType.NB_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) type);
      }
      if (url.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(url);
      }
      if (icon_id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(icon_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_NoticeButton other) {
      if (other == null) {
        return;
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      if (other.type != global::Msg.notification.PB_NoticeButtonType.NB_UNKNOWN) {
        type = other.type;
      }
      if (other.url.Length != 0) {
        url = other.url;
      }
      if (other.icon_id != 0) {
        icon_id = other.icon_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            text = input.ReadString();
            break;
          }
          case 16: {
            type = (global::Msg.notification.PB_NoticeButtonType) input.ReadEnum();
            break;
          }
          case 26: {
            url = input.ReadString();
            break;
          }
          case 32: {
            icon_id = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            text = input.ReadString();
            break;
          }
          case 16: {
            type = (global::Msg.notification.PB_NoticeButtonType) input.ReadEnum();
            break;
          }
          case 26: {
            url = input.ReadString();
            break;
          }
          case 32: {
            icon_id = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

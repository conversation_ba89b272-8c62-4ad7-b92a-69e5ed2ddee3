﻿namespace ScriptsHot.Game.Modules.Explore
{
    public class ExploreConst
    {
        public static float ScreenOffsetHeight = 50;
        /// <summary>
        /// 自动 avatar 跳转 translate 间隔  （毫秒）
        /// </summary>
        public static int AvatarToTranlate = 3000;
        
        public static float CellAphla = 0.15f;
        
        /// <summary>
        /// 上下滑动 手指引导 停留时间
        /// </summary>
        public static int NewhandDragTime = 5000;
        
        /// <summary>
        /// player气泡上墙后+2s（可调整），还没得到avatar回复时，出云彩
        /// </summary>
        public static int PlayerCellToAvatarCellInterval = 3000;
        
        /// <summary>
        /// 加载图片打点
        /// </summary>
        public static int LoadingImgDot1 = 1000;
        
        /// <summary>
        /// 加载图片打点
        /// </summary>
        public static int LoadingImgDot2 = 2000;
        
        /// <summary>
        /// 加载图片打点
        /// </summary>
        public static int LoadingImgDot3 = 3000;
        
        /// <summary>
        /// roleplay step 完成动画时长
        /// </summary>
        public static int RolePlayStepCompleteTime = 1500;
        
        /// <summary>
        /// 奖励 显示
        /// </summary>
        public static int AwardShowTime = 2000;
        /// <summary>
        /// 奖励 Fade时长
        /// </summary>
        public static float AwardFadeTime = 0.5f;
        
        /// <summary>
        /// 开始录音 一直没收到服务器返回，保底时长
        /// </summary>
        public static int MicStartDelayTime = 10000;
        
        /// <summary>
        /// 网络重连间隔
        /// </summary>
        public static int NetConnectTime = 2000;
    }
    
    public enum ExploreStep
    {
        Normal, 
        Enter,  
        Begin,
        Avatar,    
        Player,
        Image,
        Social,
        Recording,
        Exit,    
    }
    
    public class ExploreAwardNewHandData
    {
        public ExploreAwardNewHandDataType Type;
        public long Value;
        public long Param1;
        public long Param2; 
        public long Param3; 
        public bool ParamBool1; 
    }
    
    public enum ExploreAwardNewHandDataType
    {
        Exp, 
        Vocabulary,  
        Review   
    }
    
    /// <summary>
    /// 音频动作
    /// </summary>
    public class ExploreAudioPlayEffect
    {
        public bool Effect = false;
        public ulong AudioId;
      
    }
    
}
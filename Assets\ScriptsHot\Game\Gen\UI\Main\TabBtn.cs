/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Main
{
    public partial class TabBtn : UIBindT
    {
        public override string pkgName => "Main";
        public override string comName => "TabBtn";

        public Controller iconType;
        public GGraph clickRegion;
        public GImage homeTabBtn;
        public GImage chapterTabBtn;
        public GImage rankTabBtn;
        public GImage exploreTabBtn;
        public GImage testTabBtn;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            iconType = com.GetControllerAt(0);
            clickRegion = (GGraph)com.GetChildAt(0);
            homeTabBtn = (GImage)com.GetChildAt(1);
            chapterTabBtn = (GImage)com.GetChildAt(2);
            rankTabBtn = (GImage)com.GetChildAt(3);
            exploreTabBtn = (GImage)com.GetChildAt(4);
            testTabBtn = (GImage)com.GetChildAt(5);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            iconType = null;
            clickRegion = null;
            homeTabBtn = null;
            chapterTabBtn = null;
            rankTabBtn = null;
            exploreTabBtn = null;
            testTabBtn = null;
        }
    }
}
﻿using System.Collections.Generic;
using FairyGUI;
using Modules.DataDot;
using Msg.basic;
using Msg.dialog_task;
using ScriptsHot.Game.Modules.ChatLogicNew.UI.Item;
using ScriptsHot.Game.Modules.ChatLogicNew.UI.Item.AUA;
using ScriptsHot.Game.Modules.Common;
using UIBind.AUATask;
using Unity.Mathematics;

namespace ScriptsHot.Game.Modules.ChatLogicNew.UI.ChatAUA
{
    public class ChatAUAUI : BaseUI<AUATaskPanel>, IBaseUIUpdate
    {
        public override void OnBackBtnClick()
        {
            OnClickClose();
        }

        public ChatAUAUI(string name) : base(name)
        {
        }
        private int _bubbleId;
        private ChatLogicController ChatController => this.GetController<ChatLogicController>(ModelConsts.ChatLogic);
        private Dictionary<int, ChatCellBase> _allCell = new();
        public override string uiLayer => UILayerConsts.Home;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            AddUIEvent(ui.btnClose.onClick, OnClickClose);
            AddUIEvent(ui.compContinue.btnContinue.com.onClick, OnClickContinue);

            ui.compContinue.btnContinue.tfContinue.SetKey("common_continue");
        }

        protected override void OnShow()
        {
            base.OnShow();
            AddEvent();
            ui.complete.selectedIndex = 0;
        }

        protected override void OnHide()
        {
            base.OnHide();
            RemoveEvent();
            ClearData();
            ui.complete.selectedIndex = 0;
            ui.dialogList.itemPool.Clear();
        }

        protected override bool isFullScreen => true;

        public void Update(int interval)
        {
        }

        private void AddEvent()
        {
            Notifier.instance.RegisterNotification(NotifyConsts.NewChatAddCell, OnAddCell);
            Notifier.instance.RegisterNotification(NotifyConsts.AddBlankCell, OnAddBlankCell);
            Notifier.instance.RegisterNotification(NotifyConsts.ShowAUATitleEvent, ShowTitle);
            Notifier.instance.RegisterNotification(NotifyConsts.ChatAudioPlayStart, OnAudioPlay);
            Notifier.instance.RegisterNotification(NotifyConsts.RemoveBlankCell, OnRemoveBlankCell);
            Notifier.instance.RegisterNotification(NotifyConsts.ShowTranslateInfo, OnShowTranslate);
            Notifier.instance.RegisterNotification(NotifyConsts.CheckAddScaffold, OnCheckAddScaffold);
            Notifier.instance.RegisterNotification(NotifyConsts.UpdatePlayerCellState, UpdatePlayerCellState);
            Notifier.instance.RegisterNotification(NotifyConsts.ChangeAUACompleteStateEvent, OnChangeCompleteState);

        }

        private void RemoveEvent()
        {
            Notifier.instance.UnRegisterNotification(NotifyConsts.NewChatAddCell, OnAddCell);
            Notifier.instance.UnRegisterNotification(NotifyConsts.AddBlankCell, OnAddBlankCell);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ShowAUATitleEvent, ShowTitle);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ChatAudioPlayStart, OnAudioPlay);
            Notifier.instance.UnRegisterNotification(NotifyConsts.RemoveBlankCell, OnRemoveBlankCell);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ShowTranslateInfo, OnShowTranslate);
            Notifier.instance.UnRegisterNotification(NotifyConsts.CheckAddScaffold, OnCheckAddScaffold);
            Notifier.instance.UnRegisterNotification(NotifyConsts.UpdatePlayerCellState, UpdatePlayerCellState);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ChangeAUACompleteStateEvent, OnChangeCompleteState);
        }

        private void OnAddCell(string s, object body)
        {
            ChatCellInfo info = (ChatCellInfo) body;
            ChatCellBase cell = null;
            _bubbleId = info.bubbleId;
            ChatController.CurChat.CurBubbleId = _bubbleId;
            switch (info.type)
            {
                case ChatCellType.AvatarHeadNormal:
                    cell = new ChatAvatarHeadNormalCell();
                    ui.dialogList.defaultItem = "ui://qvcglg4fqqo4sxxxc4";
                    AddChatCellCom(cell,info.type,info.data);
                    cell.UpdateText(_bubbleId);
                    ChatController.CurChat.LastAvatarBubbleId = _bubbleId;
                    break;
                case ChatCellType.AvatarHeadSuggest:
                    cell = new ChatAvatarHeadSuggestCell();
                    ui.dialogList.defaultItem = "ui://qvcglg4fqqo4h";
                    AddChatCellCom(cell,info.type,info.data);
                    cell.UpdateText(_bubbleId);
                    ChatController.CurChat.LastAvatarBubbleId = _bubbleId;
                    break;
                case ChatCellType.AvatarHeadTip:
                    cell = new ChatAvatarHeadTipCell();
                    ui.dialogList.defaultItem = "ui://qvcglg4fqqo4sxxxc2";
                    AddChatCellCom(cell,info.type,info.data);
                    cell.UpdateText(_bubbleId);
                    break;
                case ChatCellType.PlayerHeadNormal:
                    cell = new ChatPlayerHeadNormalCell();
                    ui.dialogList.defaultItem = "ui://qvcglg4fqqo4sxxxc7";
                    AddChatCellCom(cell,info.type,info.data);
                    cell.UpdateText(_bubbleId);
                    break;
                case ChatCellType.AUA_Scaffold:
                    cell = new ChatCenterScaffoldCell();
                    ui.dialogList.defaultItem = "ui://qvcglg4fqqo4sxxxc6";
                    AddChatCellCom(cell,info.type,info.data);
                    cell.UpdateText(_bubbleId);
                    break;
                case ChatCellType.AUA_CutItem:
                    cell = new ChatCutItem();
                    ui.dialogList.defaultItem = "ui://qvcglg4fqqo4e";
                    cell.OnInit(ui.dialogList.AddItemFromPool().asCom,info.type,info.bubbleId,ChatController.Model.CurRoundId);
                    break;
                case ChatCellType.AUA_SectionItem:
                    cell = new ChatSectionItem();
                    ui.dialogList.defaultItem = "ui://qvcglg4fqqo4sxxxc8";
                    cell.OnInit(ui.dialogList.AddItemFromPool().asCom,info.type,info.bubbleId,ChatController.Model.CurRoundId);
                    break;
            }
            VFDebug.Log("_bubbleId::------------------------------------------------ " + _bubbleId);
            ui.dialogList.scrollPane.SetPercY(1f, false);
            _allCell.Add(_bubbleId,cell);
        }

        private BlankItem _blankItem = new();
        
        //主要是因为有个半透的图挡住下面的边 需要往上挤一挤
        private void OnAddBlankCell(string s, object body)
        {
            ui.dialogList.defaultItem = "ui://qvcglg4f9sltsxxxca";
            GComponent comp = ui.dialogList.AddItemFromPool().asCom;
            _blankItem.Construct(comp);
            ui.dialogList.scrollPane.SetPercY(1f, false);
        }

        private void OnRemoveBlankCell(string s, object body)
        {
            if (_blankItem.com != null)
                ui.dialogList.RemoveChildToPool(_blankItem.com);
        }
        
        private void OnAudioPlay(string s, object body)
        {
            ChatCellInfo info = (ChatCellInfo)body;

            foreach (var kv in _allCell)
            {
                ChatCellBase item = kv.Value;
                if(info.type != item.cellType)
                    continue;
                switch (info.type)
                {
                    case ChatCellType.AvatarHeadNormal:
                    case ChatCellType.AvatarHeadSuggest:
                    case ChatCellType.AvatarNormal:
                        if (item.AvatarData.avatar_reply_data.tts_record_id == info.tts_record_id && item.AvatarData.bubble_id == info.bubbleId)
                        {
                            VFDebug.Log("找到avatar item  开始播放：" + info.tts_record_id);
                            item.TriggerBtnPlay();
                        }

                        break;
                    case ChatCellType.Narration:
                        if (item.AvatarData.dialog_narration.tts_record_id == info.tts_record_id)
                        {
                            VFDebug.Log("找到narration item  开始播放：" + info.tts_record_id);
                            item.TriggerBtnPlay();
                        }
                        break;
                }
            }
        }
        
        private void OnCheckAddScaffold(string s,object param)
        {
            if ((int)param != ChatController.Model.CurRoundId) return;
            RoundData roundData = ChatController.Model.GetRoundDataByID(ChatController.Model.CurRoundId);
            if (!ChatController.IsAddScaffold && ChatController.ScaffoldCanShow && roundData.ScaffoldAck != null)
            {
                Notifier.instance.SendNotification(NotifyConsts.RemoveBlankCell);
                ChatCellInfo cellInfo = new ChatCellInfo();
                cellInfo.type = ChatCellType.AUA_Scaffold;
                cellInfo.data = roundData.ScaffoldAck;
                cellInfo.tts_record_id = roundData.ScaffoldAck.data.tts_record_id;
                cellInfo.bubbleId = ChatController.GetBubbleId();
                Notifier.instance.SendNotification(NotifyConsts.NewChatAddCell, cellInfo);
                Notifier.instance.SendNotification(NotifyConsts.AddBlankCell);
                Notifier.instance.SendNotification(NotifyConsts.SetScaffoldShowState, true);
            }
        }

        private void OnShowTranslate(string s, object body)
        {
            SC_DialogTranslateAck info = (SC_DialogTranslateAck) body;
            if (_allCell.TryGetValue(info.data.bubble_id, out ChatCellBase cell))
            {
                if (cell is ChatAvatarHeadNormalCell)
                {
                    ChatAvatarHeadNormalCell avatarHeadNormalCell = cell as ChatAvatarHeadNormalCell;
                    RichContent richContent = new RichContent
                    {
                        Content = info.data.trans_content,
                        Type = RichContentType.Text,
                        IsLast = true
                    };
                    avatarHeadNormalCell.AppendTransRichContent(richContent);
                }
                else if (cell is ChatAvatarHeadSuggestCell)
                {
                    ChatAvatarHeadSuggestCell suggestCell = cell as ChatAvatarHeadSuggestCell;
                    RichContent richContent = new RichContent
                    {
                        Content = info.data.trans_content,
                        Type = RichContentType.Text,
                        IsLast = true
                    };
                    suggestCell.AppendTransRichContent(richContent);
                }
            }
        }

        private void OnChangeCompleteState(string s, object body)
        {
            bool showState = (bool)body;
            if (showState)
            {
                DotAppearSpeakCompletedPanel dot = new DotAppearSpeakCompletedPanel();
                dot.session_id = ChatController.CurChat.CurChatInfo.taskId;
                dot.level_type = PB_LevelTypeEnum.LTAua.ToString();
                dot.part_title = ChatController.CurChat.DialogTaskAck.data.flash_task_info
                    .section_info_list[ChatController.Model.CurSectionIndex]?.section_title;
                dot.part_order = ChatController.Model.CurSectionIndex;
                dot.Dialogue_id = ChatController.CurChat.DialogTaskAck.data.dialog_id;
                dot.session_record_id = ChatController.CurChat.DialogTaskAck.data.session_record_id;
                DataDotMgr.Collect(dot);
                
                ui.compContinue.spComplete.spineAnimation.AnimationState.SetAnimation(0, "1", false);
                if (ChatController.Model.IsFinish)
                    ui.compContinue.tfComplete.SetKey("ui_aua_dialog_complete");
                else
                    ui.compContinue.tfComplete.SetKeyArgs("ui_aua_dialog_title",
                        ChatController.Model.CurSectionIndex + 1,
                        ChatController.CurChat.DialogTaskAck.data.flash_task_info.section_info_list.Count);
                ui.complete.selectedIndex = 1;
            }
            else
                ui.complete.selectedIndex = 0;
            
        }

        private void UpdatePlayerCellState(string s, object body)
        {
            if (body is PB_DialogTaskMsgItem data)
            {
                if (_allCell.TryGetValue(data.bubble_id, out ChatCellBase value))
                {
                    (value as ChatPlayerHeadNormalCell)?.ChangeEvaluateState(data.task_status_data);
                }
            }
        }
        
        private void OnClickClose()
        {
            SendNotification(NotifyConsts.DoTopLeftBack);
        }

        private void OnClickContinue()
        {
            OnChangeCompleteState("", false);
            SendNotification(NotifyConsts.ClickAUAUIContinueEvent);

            DotClickSpeakCompletedPanelContinueButton dot = new DotClickSpeakCompletedPanelContinueButton();
            dot.session_id = ChatController.CurChat.CurChatInfo.taskId;
            dot.level_type = PB_LevelTypeEnum.LTAua.ToString();
            dot.part_title = ChatController.CurChat.DialogTaskAck.data.flash_task_info
                .section_info_list[ChatController.Model.CurSectionIndex]?.section_title;
            dot.part_order = ChatController.Model.CurSectionIndex;
            dot.Dialogue_id = ChatController.CurChat.DialogTaskAck.data.dialog_id;
            dot.session_record_id = ChatController.CurChat.DialogTaskAck.data.session_record_id;
            DataDotMgr.Collect(dot);
        }

        private void AddChatCellCom(ChatCellBase baseChatCell, ChatCellType cellType, object data)
        {
            GComponent gCom = ui.dialogList.AddItemFromPool().asCom;
            gCom.name = _bubbleId.ToString();
            int roundId = ChatController.Model.CurRoundId;
            baseChatCell.OnInit(gCom, cellType, _bubbleId, roundId);
            baseChatCell.SetUI(ui.com);
            if (cellType == ChatCellType.AvatarNormal || cellType == ChatCellType.AvatarHeadNormal ||
                cellType == ChatCellType.AvatarHeadSuggest || cellType == ChatCellType.AvatarHeadTip)
            {
                baseChatCell.SetAvatarData((PB_DialogTaskMsgItem)data, _bubbleId);
            }
            else if (cellType == ChatCellType.PlayerNormal || cellType == ChatCellType.PlayerHeadNormal)
            {
                baseChatCell.SetPlayerData((ASRCompleteVO)data, _bubbleId);
                
            }
            else if (cellType == ChatCellType.Scaffold || cellType == ChatCellType.AUA_Scaffold)
            {
                baseChatCell.SetScaffoldData((SC_GetDialogScaffoldAck)data, _bubbleId);
            }
            else if (cellType == ChatCellType.AvatarHeadTip)
            {
                baseChatCell.SetNoticeData((PB_ReplyNotice)data, _bubbleId);
            }

            ui.dialogList.ScrollToView(math.max(0, _allCell.Count - 1));
            DotAppearBubble(cellType);
        }
        
        private void DotAppearBubble(ChatCellType cellType)
        {
            string bubbleType = "";
            switch (cellType)
            {
                case ChatCellType.AvatarHeadNormal:
                    bubbleType = "Avatar";
                    break;
                case ChatCellType.AvatarHeadSuggest:
                    bubbleType = "Avatar";
                    break;
                case ChatCellType.AvatarHeadTip:
                    bubbleType = "Avatar";
                    break;
                case ChatCellType.PlayerHeadNormal:
                    bubbleType = "User";
                    break;
                case ChatCellType.AUA_Scaffold:
                    bubbleType = "Scaffold";
                    break;
            }
            DataDotAppear_Dialogue_bubble dot = new DataDotAppear_Dialogue_bubble();
            dot.Session_id = ChatController.CurChat.CurChatInfo.taskId;
            dot.level_type = PB_LevelTypeEnum.LTAua.ToString();
            dot.part_title = ChatController.CurChat.DialogTaskAck.data.flash_task_info
                .section_info_list[ChatController.Model.CurSectionIndex]?.section_title;
            dot.part_order = ChatController.Model.CurSectionIndex;
            dot.Dialogue_id = ChatController.CurChat.DialogTaskAck.data.dialog_id;
            dot.Bubble_type = bubbleType;
            dot.Dialogue_round = ChatController.Model.CurRoundId;
            dot.session_record_id = ChatController.CurChat.DialogTaskAck.data.session_record_id;
            dot.Bubble_mark = cellType == ChatCellType.AvatarHeadSuggest ? "mark" : "0";
            dot.question_id = ChatController.CurChat.DialogTaskAck.data.flash_task_info
                .section_info_list[ChatController.Model.CurSectionIndex]
                ?.question_info_list[ChatController.Model.CurQuestionIndex].question_id ?? 0;
            dot.question_index = ChatController.CurChat.DialogTaskAck.data.flash_task_info
                .section_info_list[ChatController.Model.CurSectionIndex]
                ?.question_info_list[ChatController.Model.CurQuestionIndex].question_index ?? 0;
            DataDotMgr.Collect(dot);
        }

        private void ShowTitle(string str, object body)
        {
            ui.tfTitle.text = ChatController.CurChat.DialogTaskAck.data.flash_task_info.task_title;
            
            DotAppearSpeakAuaPage dot = new DotAppearSpeakAuaPage();
            dot.session_id = ChatController.CurChat.DialogTaskAck.data.task_id;
            dot.level_type = PB_LevelTypeEnum.LTAua.ToString();
            dot.part_title = ChatController.CurChat.DialogTaskAck.data.flash_task_info
                .section_info_list[ChatController.Model.CurSectionIndex]?.section_title;
            dot.part_order = ChatController.Model.CurSectionIndex;
            dot.Dialogue_id = ChatController.CurChat.DialogTaskAck.data.dialog_id;
            dot.session_record_id = ChatController.CurChat.DialogTaskAck.data.session_record_id;
            DataDotMgr.Collect(dot);
        }

        private void ClearData()
        {
            _bubbleId = 0;
            ui.dialogList.RemoveChildrenToPool();
            foreach (var cellValue in _allCell.Values)
                cellValue.Dispose();
            _allCell.Clear();
            
        }
    }
}
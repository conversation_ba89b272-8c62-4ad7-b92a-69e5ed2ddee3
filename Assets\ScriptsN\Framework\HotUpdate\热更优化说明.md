# 热更逻辑优化说明

## 优化目标
将原来的单一强更模式优化为**先强更后非强更**的两阶段热更模式：
1. **强更阶段**：必须完成的关键更新，阻塞用户进入游戏
2. **非强更阶段**：后台静默下载，用户无感知，有流量控制

## 核心问题与解决方案

### 1. YooAssets限制问题
**问题**: 
- `YooAssets.Initialize()` 只能调用一次
- `ResourcePackage.InitializeAsync()` 只能调用一次
- `RemoteServices` 的URL在初始化时固定，无法动态更改

**解决方案**:
- 保存 `RemoteServices` 实例的引用，通过新增的 `UpdateServerUrls()` 方法动态更新URL
- 使用 `PreDownloadContentAsync()` API进行非强更的预下载

### 2. 数据结构扩展

#### HotUpdateResult 类扩展
```csharp
public class HotUpdateResult
{
    // 原有字段
    public HotUpdateMode Mode = HotUpdateMode.None;
    public string CdnUrl;
    public ResourceDownloaderOperation downloader;
    public string PkgVer;
    
    // 新增非强更字段
    public string OptionalCdnUrl;  // 非强更的CDN地址
    public string OptionalPkgVer;  // 非强更的包版本
    public ResourceDownloaderOperation optionalDownloader; // 非强更下载器
    public bool HasOptionalUpdate = false; // 是否有非强更内容
}
```

#### RemoteServices 类扩展
```csharp
public class RemoteServices : IRemoteServices
{
    private string _defaultHostServer;  // 改为可变
    private string _fallbackHostServer; // 改为可变
    
    // 新增方法：动态更新服务器地址
    public void UpdateServerUrls(string defaultHostServer, string fallbackHostServer)
    {
        _defaultHostServer = defaultHostServer;
        _fallbackHostServer = fallbackHostServer;
    }
}
```

### 3. 服务器数据格式扩展

服务器返回的版本数据需要包含非强更版本信息：

```json
{
    "1.0.0": {
        "ios_version_hash": "main_v1.0.1",
        "android_version_hash": "main_v1.0.1", 
        "avatar_ios_version_hash": "avatar_v1.0.1",
        "avatar_android_version_hash": "avatar_v1.0.1",
        
        // 新增非强更版本字段
        "optional_ios_version_hash": "main_v1.0.2",
        "optional_android_version_hash": "main_v1.0.2",
        "optional_avatar_ios_version_hash": "avatar_v1.0.2", 
        "optional_avatar_android_version_hash": "avatar_v1.0.2",
        
        "cdn": "http://cdn.example.com/"
    }
}
```

## 优化后的热更流程

### 阶段1: 强更流程（必须更新）
1. 请求版本信息
2. 解析强更版本和非强更版本
3. 初始化YooAssets包（使用强更CDN地址）
4. 下载强更内容
5. 保存RemoteServices实例引用

### 阶段2: 非强更流程（后台静默下载）
1. 用户正常进入游戏，不被阻塞
2. 延迟5秒后开始后台检查
3. 检查网络环境（仅WiFi下进行下载）
4. 版本号比较（仅当目标版本 > 当前版本时下载）
5. 动态更新RemoteServices的URL到非强更地址
6. 使用`PreDownloadContentAsync()`进行预下载
7. 创建下载器并执行非强更下载（低并发数，流量控制）
8. 非强更失败不影响游戏运行

## 核心方法说明

### StartOptionalDownloadInBackground()
```csharp
public void StartOptionalDownloadInBackground()
```
- 启动非强更后台下载流程
- 不阻塞用户进入游戏
- 异步执行，用户无感知

### CompareVersionNumbers()
```csharp
private int CompareVersionNumbers(string version1, string version2)
```
- 比较全数字版本号（如：250101022720）
- 返回值：1(v1>v2), 0(相等), -1(v1<v2)
- 支持纯数字和字符串比较

### IsNetworkSuitableForOptionalDownload()
```csharp
private bool IsNetworkSuitableForOptionalDownload()
```
- 检查网络环境是否适合非强更下载
- 仅WiFi环境下返回true
- 移动网络下返回false（节省用户流量）

### StartMainOptionalDownload() / StartAvatarOptionalDownload()
- 使用`PreDownloadContentAsync()`进行预下载
- 创建下载器执行实际下载
- 提供下载进度回调

## 使用方式

### 1. 服务器配置
在版本配置中添加非强更版本字段：
- `optional_ios_version_hash`
- `optional_android_version_hash` 
- `optional_avatar_ios_version_hash`
- `optional_avatar_android_version_hash`

### 2. 客户端调用
强更完成后自动启动非强更后台下载：
```csharp
// 强更完成后，启动后台下载（不阻塞用户）
core.StartOptionalDownloadInBackground();
```

## 优势

1. **兼容性**: 完全兼容现有强更逻辑
2. **用户体验**: 用户无感知，不阻塞游戏启动
3. **流量控制**: 仅WiFi环境下进行非强更下载
4. **版本控制**: 智能版本比较，避免无效下载
5. **灵活性**: 非强更失败不影响游戏运行
6. **渐进式**: 可以逐步推送非关键更新

## 注意事项

1. **版本号格式**: 使用全数字版本号（如：250101022720）
2. **版本比较**: 仅当非强更版本 > 当前版本时才下载
3. **网络限制**: 仅WiFi环境下进行非强更下载
4. **流量控制**: 使用低并发数（2个文件同时下载）
5. **延迟启动**: 游戏启动5秒后才开始非强更下载
6. **错误处理**: 非强更失败不影响游戏运行
7. **服务器配置**: 需要同时提供强更和非强更的资源文件

## 测试验证

使用 `HotUpdateOptimizationTest.cs` 脚本进行功能验证：
- RemoteServices URL动态更新测试
- HotUpdateResult非强更字段测试
- 模拟版本数据格式测试

// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/course/service.proto
// </auto-generated>
#pragma warning disable 0414, 1591, 8981, 0612
#region Designer generated code

using grpc = global::Grpc.Core;

namespace Msg.course {
  public static partial class CourseService
  {
    static readonly string __ServiceName = "CourseService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.CS_GetUserCourseReq> __Marshaller_CS_GetUserCourseReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.CS_GetUserCourseReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SC_GetUserCourseAck> __Marshaller_SC_GetUserCourseAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SC_GetUserCourseAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.CS_SkipCourseReq> __Marshaller_CS_SkipCourseReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.CS_SkipCourseReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SC_SkipCourseAck> __Marshaller_SC_SkipCourseAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SC_SkipCourseAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.CS_RewardBoxReq> __Marshaller_CS_RewardBoxReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.CS_RewardBoxReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SC_RewardBoxAck> __Marshaller_SC_RewardBoxAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SC_RewardBoxAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.CS_GetBookDataReq> __Marshaller_CS_GetBookDataReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.CS_GetBookDataReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SC_GetBookDataAck> __Marshaller_SC_GetBookDataAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SC_GetBookDataAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.CS_GetRadioDataReq> __Marshaller_CS_GetRadioDataReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.CS_GetRadioDataReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SC_GetRadioDataAck> __Marshaller_SC_GetRadioDataAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SC_GetRadioDataAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.CS_GetCourseSettlementReq> __Marshaller_CS_GetCourseSettlementReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.CS_GetCourseSettlementReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SC_GetCourseSettlementAck> __Marshaller_SC_GetCourseSettlementAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SC_GetCourseSettlementAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.CS_BuyCourseTicketReq> __Marshaller_CS_BuyCourseTicketReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.CS_BuyCourseTicketReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SC_BuyCourseTicketAck> __Marshaller_SC_BuyCourseTicketAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SC_BuyCourseTicketAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SS_ChangeCourseProgressReq> __Marshaller_SS_ChangeCourseProgressReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SS_ChangeCourseProgressReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SS_ChangeCourseProgressAck> __Marshaller_SS_ChangeCourseProgressAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SS_ChangeCourseProgressAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SS_CreateCourseSessionRecordReq> __Marshaller_SS_CreateCourseSessionRecordReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SS_CreateCourseSessionRecordReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SS_CreateCourseSessionRecordAck> __Marshaller_SS_CreateCourseSessionRecordAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SS_CreateCourseSessionRecordAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SS_ExitCourseSessionReq> __Marshaller_SS_ExitCourseSessionReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SS_ExitCourseSessionReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SS_ExitCourseSessionAck> __Marshaller_SS_ExitCourseSessionAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SS_ExitCourseSessionAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SS_SetUserProgressReq> __Marshaller_SS_SetUserProgressReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SS_SetUserProgressReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SS_SetUserProgressAck> __Marshaller_SS_SetUserProgressAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SS_SetUserProgressAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.CS_GetUserShelfReq> __Marshaller_CS_GetUserShelfReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.CS_GetUserShelfReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.course.SC_GetUserShelfAck> __Marshaller_SC_GetUserShelfAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.course.SC_GetUserShelfAck.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.CS_GetUserCourseReq, global::Msg.course.SC_GetUserCourseAck> __Method_GetUserCourse = new grpc::Method<global::Msg.course.CS_GetUserCourseReq, global::Msg.course.SC_GetUserCourseAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUserCourse",
        __Marshaller_CS_GetUserCourseReq,
        __Marshaller_SC_GetUserCourseAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.CS_SkipCourseReq, global::Msg.course.SC_SkipCourseAck> __Method_SkipCourse = new grpc::Method<global::Msg.course.CS_SkipCourseReq, global::Msg.course.SC_SkipCourseAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "SkipCourse",
        __Marshaller_CS_SkipCourseReq,
        __Marshaller_SC_SkipCourseAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.CS_RewardBoxReq, global::Msg.course.SC_RewardBoxAck> __Method_RewardBox = new grpc::Method<global::Msg.course.CS_RewardBoxReq, global::Msg.course.SC_RewardBoxAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "RewardBox",
        __Marshaller_CS_RewardBoxReq,
        __Marshaller_SC_RewardBoxAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.CS_GetBookDataReq, global::Msg.course.SC_GetBookDataAck> __Method_GetBookData = new grpc::Method<global::Msg.course.CS_GetBookDataReq, global::Msg.course.SC_GetBookDataAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetBookData",
        __Marshaller_CS_GetBookDataReq,
        __Marshaller_SC_GetBookDataAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.CS_GetRadioDataReq, global::Msg.course.SC_GetRadioDataAck> __Method_GetRadioData = new grpc::Method<global::Msg.course.CS_GetRadioDataReq, global::Msg.course.SC_GetRadioDataAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetRadioData",
        __Marshaller_CS_GetRadioDataReq,
        __Marshaller_SC_GetRadioDataAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.CS_GetCourseSettlementReq, global::Msg.course.SC_GetCourseSettlementAck> __Method_GetCourseSettlement = new grpc::Method<global::Msg.course.CS_GetCourseSettlementReq, global::Msg.course.SC_GetCourseSettlementAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetCourseSettlement",
        __Marshaller_CS_GetCourseSettlementReq,
        __Marshaller_SC_GetCourseSettlementAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.CS_BuyCourseTicketReq, global::Msg.course.SC_BuyCourseTicketAck> __Method_BuyCourseTicket = new grpc::Method<global::Msg.course.CS_BuyCourseTicketReq, global::Msg.course.SC_BuyCourseTicketAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "BuyCourseTicket",
        __Marshaller_CS_BuyCourseTicketReq,
        __Marshaller_SC_BuyCourseTicketAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.SS_ChangeCourseProgressReq, global::Msg.course.SS_ChangeCourseProgressAck> __Method_ChangeCourseProgress = new grpc::Method<global::Msg.course.SS_ChangeCourseProgressReq, global::Msg.course.SS_ChangeCourseProgressAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "ChangeCourseProgress",
        __Marshaller_SS_ChangeCourseProgressReq,
        __Marshaller_SS_ChangeCourseProgressAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.SS_CreateCourseSessionRecordReq, global::Msg.course.SS_CreateCourseSessionRecordAck> __Method_CreateCourseSessionRecord = new grpc::Method<global::Msg.course.SS_CreateCourseSessionRecordReq, global::Msg.course.SS_CreateCourseSessionRecordAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "CreateCourseSessionRecord",
        __Marshaller_SS_CreateCourseSessionRecordReq,
        __Marshaller_SS_CreateCourseSessionRecordAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.SS_ExitCourseSessionReq, global::Msg.course.SS_ExitCourseSessionAck> __Method_ExitCourseSession = new grpc::Method<global::Msg.course.SS_ExitCourseSessionReq, global::Msg.course.SS_ExitCourseSessionAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "ExitCourseSession",
        __Marshaller_SS_ExitCourseSessionReq,
        __Marshaller_SS_ExitCourseSessionAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.SS_SetUserProgressReq, global::Msg.course.SS_SetUserProgressAck> __Method_SetUserProgress = new grpc::Method<global::Msg.course.SS_SetUserProgressReq, global::Msg.course.SS_SetUserProgressAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "SetUserProgress",
        __Marshaller_SS_SetUserProgressReq,
        __Marshaller_SS_SetUserProgressAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.course.CS_GetUserShelfReq, global::Msg.course.SC_GetUserShelfAck> __Method_GetUserShelf = new grpc::Method<global::Msg.course.CS_GetUserShelfReq, global::Msg.course.SC_GetUserShelfAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUserShelf",
        __Marshaller_CS_GetUserShelfReq,
        __Marshaller_SC_GetUserShelfAck);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.course.ServiceReflection.Descriptor.Services[0]; }
    }

    /// <summary>Base class for server-side implementations of CourseService</summary>
    [grpc::BindServiceMethod(typeof(CourseService), "BindService")]
    public abstract partial class CourseServiceBase
    {
      /// <summary>
      /// 获取用户课程信息
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SC_GetUserCourseAck> GetUserCourse(global::Msg.course.CS_GetUserCourseReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 跳过课程
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SC_SkipCourseAck> SkipCourse(global::Msg.course.CS_SkipCourseReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 领取宝箱奖励
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SC_RewardBoxAck> RewardBox(global::Msg.course.CS_RewardBoxReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 书本数据
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SC_GetBookDataAck> GetBookData(global::Msg.course.CS_GetBookDataReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 耳机数据
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SC_GetRadioDataAck> GetRadioData(global::Msg.course.CS_GetRadioDataReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 结算
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SC_GetCourseSettlementAck> GetCourseSettlement(global::Msg.course.CS_GetCourseSettlementReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 买票
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SC_BuyCourseTicketAck> BuyCourseTicket(global::Msg.course.CS_BuyCourseTicketReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 更新course进度(内部)
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SS_ChangeCourseProgressAck> ChangeCourseProgress(global::Msg.course.SS_ChangeCourseProgressReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 创建session记录
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SS_CreateCourseSessionRecordAck> CreateCourseSessionRecord(global::Msg.course.SS_CreateCourseSessionRecordReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 退出session
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SS_ExitCourseSessionAck> ExitCourseSession(global::Msg.course.SS_ExitCourseSessionReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 设置用户进度
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SS_SetUserProgressAck> SetUserProgress(global::Msg.course.SS_SetUserProgressReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 获取用户书架
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.course.SC_GetUserShelfAck> GetUserShelf(global::Msg.course.CS_GetUserShelfReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for CourseService</summary>
    public partial class CourseServiceClient : grpc::ClientBase<CourseServiceClient>
    {
      /// <summary>Creates a new client for CourseService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public CourseServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for CourseService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public CourseServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected CourseServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected CourseServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// 获取用户课程信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_GetUserCourseAck GetUserCourse(global::Msg.course.CS_GetUserCourseReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserCourse(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户课程信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_GetUserCourseAck GetUserCourse(global::Msg.course.CS_GetUserCourseReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUserCourse, null, options, request);
      }
      /// <summary>
      /// 获取用户课程信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_GetUserCourseAck> GetUserCourseAsync(global::Msg.course.CS_GetUserCourseReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserCourseAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户课程信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_GetUserCourseAck> GetUserCourseAsync(global::Msg.course.CS_GetUserCourseReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUserCourse, null, options, request);
      }
      /// <summary>
      /// 跳过课程
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_SkipCourseAck SkipCourse(global::Msg.course.CS_SkipCourseReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SkipCourse(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 跳过课程
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_SkipCourseAck SkipCourse(global::Msg.course.CS_SkipCourseReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_SkipCourse, null, options, request);
      }
      /// <summary>
      /// 跳过课程
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_SkipCourseAck> SkipCourseAsync(global::Msg.course.CS_SkipCourseReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SkipCourseAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 跳过课程
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_SkipCourseAck> SkipCourseAsync(global::Msg.course.CS_SkipCourseReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_SkipCourse, null, options, request);
      }
      /// <summary>
      /// 领取宝箱奖励
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_RewardBoxAck RewardBox(global::Msg.course.CS_RewardBoxReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return RewardBox(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 领取宝箱奖励
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_RewardBoxAck RewardBox(global::Msg.course.CS_RewardBoxReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_RewardBox, null, options, request);
      }
      /// <summary>
      /// 领取宝箱奖励
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_RewardBoxAck> RewardBoxAsync(global::Msg.course.CS_RewardBoxReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return RewardBoxAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 领取宝箱奖励
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_RewardBoxAck> RewardBoxAsync(global::Msg.course.CS_RewardBoxReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_RewardBox, null, options, request);
      }
      /// <summary>
      /// 书本数据
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_GetBookDataAck GetBookData(global::Msg.course.CS_GetBookDataReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetBookData(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 书本数据
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_GetBookDataAck GetBookData(global::Msg.course.CS_GetBookDataReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetBookData, null, options, request);
      }
      /// <summary>
      /// 书本数据
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_GetBookDataAck> GetBookDataAsync(global::Msg.course.CS_GetBookDataReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetBookDataAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 书本数据
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_GetBookDataAck> GetBookDataAsync(global::Msg.course.CS_GetBookDataReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetBookData, null, options, request);
      }
      /// <summary>
      /// 耳机数据
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_GetRadioDataAck GetRadioData(global::Msg.course.CS_GetRadioDataReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetRadioData(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 耳机数据
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_GetRadioDataAck GetRadioData(global::Msg.course.CS_GetRadioDataReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetRadioData, null, options, request);
      }
      /// <summary>
      /// 耳机数据
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_GetRadioDataAck> GetRadioDataAsync(global::Msg.course.CS_GetRadioDataReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetRadioDataAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 耳机数据
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_GetRadioDataAck> GetRadioDataAsync(global::Msg.course.CS_GetRadioDataReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetRadioData, null, options, request);
      }
      /// <summary>
      /// 结算
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_GetCourseSettlementAck GetCourseSettlement(global::Msg.course.CS_GetCourseSettlementReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetCourseSettlement(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 结算
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_GetCourseSettlementAck GetCourseSettlement(global::Msg.course.CS_GetCourseSettlementReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetCourseSettlement, null, options, request);
      }
      /// <summary>
      /// 结算
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_GetCourseSettlementAck> GetCourseSettlementAsync(global::Msg.course.CS_GetCourseSettlementReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetCourseSettlementAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 结算
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_GetCourseSettlementAck> GetCourseSettlementAsync(global::Msg.course.CS_GetCourseSettlementReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetCourseSettlement, null, options, request);
      }
      /// <summary>
      /// 买票
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_BuyCourseTicketAck BuyCourseTicket(global::Msg.course.CS_BuyCourseTicketReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return BuyCourseTicket(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 买票
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_BuyCourseTicketAck BuyCourseTicket(global::Msg.course.CS_BuyCourseTicketReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_BuyCourseTicket, null, options, request);
      }
      /// <summary>
      /// 买票
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_BuyCourseTicketAck> BuyCourseTicketAsync(global::Msg.course.CS_BuyCourseTicketReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return BuyCourseTicketAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 买票
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_BuyCourseTicketAck> BuyCourseTicketAsync(global::Msg.course.CS_BuyCourseTicketReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_BuyCourseTicket, null, options, request);
      }
      /// <summary>
      /// 更新course进度(内部)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SS_ChangeCourseProgressAck ChangeCourseProgress(global::Msg.course.SS_ChangeCourseProgressReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return ChangeCourseProgress(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 更新course进度(内部)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SS_ChangeCourseProgressAck ChangeCourseProgress(global::Msg.course.SS_ChangeCourseProgressReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_ChangeCourseProgress, null, options, request);
      }
      /// <summary>
      /// 更新course进度(内部)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SS_ChangeCourseProgressAck> ChangeCourseProgressAsync(global::Msg.course.SS_ChangeCourseProgressReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return ChangeCourseProgressAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 更新course进度(内部)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SS_ChangeCourseProgressAck> ChangeCourseProgressAsync(global::Msg.course.SS_ChangeCourseProgressReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_ChangeCourseProgress, null, options, request);
      }
      /// <summary>
      /// 创建session记录
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SS_CreateCourseSessionRecordAck CreateCourseSessionRecord(global::Msg.course.SS_CreateCourseSessionRecordReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CreateCourseSessionRecord(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 创建session记录
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SS_CreateCourseSessionRecordAck CreateCourseSessionRecord(global::Msg.course.SS_CreateCourseSessionRecordReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_CreateCourseSessionRecord, null, options, request);
      }
      /// <summary>
      /// 创建session记录
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SS_CreateCourseSessionRecordAck> CreateCourseSessionRecordAsync(global::Msg.course.SS_CreateCourseSessionRecordReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CreateCourseSessionRecordAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 创建session记录
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SS_CreateCourseSessionRecordAck> CreateCourseSessionRecordAsync(global::Msg.course.SS_CreateCourseSessionRecordReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_CreateCourseSessionRecord, null, options, request);
      }
      /// <summary>
      /// 退出session
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SS_ExitCourseSessionAck ExitCourseSession(global::Msg.course.SS_ExitCourseSessionReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return ExitCourseSession(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 退出session
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SS_ExitCourseSessionAck ExitCourseSession(global::Msg.course.SS_ExitCourseSessionReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_ExitCourseSession, null, options, request);
      }
      /// <summary>
      /// 退出session
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SS_ExitCourseSessionAck> ExitCourseSessionAsync(global::Msg.course.SS_ExitCourseSessionReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return ExitCourseSessionAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 退出session
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SS_ExitCourseSessionAck> ExitCourseSessionAsync(global::Msg.course.SS_ExitCourseSessionReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_ExitCourseSession, null, options, request);
      }
      /// <summary>
      /// 设置用户进度
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SS_SetUserProgressAck SetUserProgress(global::Msg.course.SS_SetUserProgressReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SetUserProgress(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 设置用户进度
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SS_SetUserProgressAck SetUserProgress(global::Msg.course.SS_SetUserProgressReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_SetUserProgress, null, options, request);
      }
      /// <summary>
      /// 设置用户进度
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SS_SetUserProgressAck> SetUserProgressAsync(global::Msg.course.SS_SetUserProgressReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SetUserProgressAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 设置用户进度
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SS_SetUserProgressAck> SetUserProgressAsync(global::Msg.course.SS_SetUserProgressReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_SetUserProgress, null, options, request);
      }
      /// <summary>
      /// 获取用户书架
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_GetUserShelfAck GetUserShelf(global::Msg.course.CS_GetUserShelfReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserShelf(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户书架
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.course.SC_GetUserShelfAck GetUserShelf(global::Msg.course.CS_GetUserShelfReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUserShelf, null, options, request);
      }
      /// <summary>
      /// 获取用户书架
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_GetUserShelfAck> GetUserShelfAsync(global::Msg.course.CS_GetUserShelfReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserShelfAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户书架
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.course.SC_GetUserShelfAck> GetUserShelfAsync(global::Msg.course.CS_GetUserShelfReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUserShelf, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override CourseServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new CourseServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(CourseServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GetUserCourse, serviceImpl.GetUserCourse)
          .AddMethod(__Method_SkipCourse, serviceImpl.SkipCourse)
          .AddMethod(__Method_RewardBox, serviceImpl.RewardBox)
          .AddMethod(__Method_GetBookData, serviceImpl.GetBookData)
          .AddMethod(__Method_GetRadioData, serviceImpl.GetRadioData)
          .AddMethod(__Method_GetCourseSettlement, serviceImpl.GetCourseSettlement)
          .AddMethod(__Method_BuyCourseTicket, serviceImpl.BuyCourseTicket)
          .AddMethod(__Method_ChangeCourseProgress, serviceImpl.ChangeCourseProgress)
          .AddMethod(__Method_CreateCourseSessionRecord, serviceImpl.CreateCourseSessionRecord)
          .AddMethod(__Method_ExitCourseSession, serviceImpl.ExitCourseSession)
          .AddMethod(__Method_SetUserProgress, serviceImpl.SetUserProgress)
          .AddMethod(__Method_GetUserShelf, serviceImpl.GetUserShelf).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, CourseServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GetUserCourse, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.CS_GetUserCourseReq, global::Msg.course.SC_GetUserCourseAck>(serviceImpl.GetUserCourse));
      serviceBinder.AddMethod(__Method_SkipCourse, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.CS_SkipCourseReq, global::Msg.course.SC_SkipCourseAck>(serviceImpl.SkipCourse));
      serviceBinder.AddMethod(__Method_RewardBox, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.CS_RewardBoxReq, global::Msg.course.SC_RewardBoxAck>(serviceImpl.RewardBox));
      serviceBinder.AddMethod(__Method_GetBookData, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.CS_GetBookDataReq, global::Msg.course.SC_GetBookDataAck>(serviceImpl.GetBookData));
      serviceBinder.AddMethod(__Method_GetRadioData, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.CS_GetRadioDataReq, global::Msg.course.SC_GetRadioDataAck>(serviceImpl.GetRadioData));
      serviceBinder.AddMethod(__Method_GetCourseSettlement, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.CS_GetCourseSettlementReq, global::Msg.course.SC_GetCourseSettlementAck>(serviceImpl.GetCourseSettlement));
      serviceBinder.AddMethod(__Method_BuyCourseTicket, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.CS_BuyCourseTicketReq, global::Msg.course.SC_BuyCourseTicketAck>(serviceImpl.BuyCourseTicket));
      serviceBinder.AddMethod(__Method_ChangeCourseProgress, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.SS_ChangeCourseProgressReq, global::Msg.course.SS_ChangeCourseProgressAck>(serviceImpl.ChangeCourseProgress));
      serviceBinder.AddMethod(__Method_CreateCourseSessionRecord, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.SS_CreateCourseSessionRecordReq, global::Msg.course.SS_CreateCourseSessionRecordAck>(serviceImpl.CreateCourseSessionRecord));
      serviceBinder.AddMethod(__Method_ExitCourseSession, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.SS_ExitCourseSessionReq, global::Msg.course.SS_ExitCourseSessionAck>(serviceImpl.ExitCourseSession));
      serviceBinder.AddMethod(__Method_SetUserProgress, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.SS_SetUserProgressReq, global::Msg.course.SS_SetUserProgressAck>(serviceImpl.SetUserProgress));
      serviceBinder.AddMethod(__Method_GetUserShelf, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.course.CS_GetUserShelfReq, global::Msg.course.SC_GetUserShelfAck>(serviceImpl.GetUserShelf));
    }

  }
}
#endregion

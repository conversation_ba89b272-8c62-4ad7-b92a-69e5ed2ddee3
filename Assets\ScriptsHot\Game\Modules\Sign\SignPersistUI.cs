/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/08/13 19:29:07 星期二
# 功能：Nothing
****************************************************
*/

using System;
using System.Collections.Generic;
using FairyGUI;
using Firebase.Analytics;
using Modules.DataDot;
using ScriptsHot.Game.Modules.Settlement;
using UIBind.Sign;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Sign
{
    public class SignPersistUI : BaseUI<UIBind.Sign.SignPersistPanel>
    {
        public SignPersistUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top; //主UI层
        protected override bool isFullScreen => true;
        
        private SettlementModel _model => this.GetModel<SettlementModel>(ModelConsts.Settlement);
        private SettlementController _settlementController => GetController<SettlementController>(ModelConsts.Settlement);
        
        private List<TwinkComponent> _twinkComs = new();
        
        private enum DayCmpState
        {
            none,
            done,
            repair,
            hide,
        }
        
        private LinkedList<SignPersistDayComponent> _daycmps = new();
        
        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(this.ui.btnConfirm.onClick, OnBtnConfirmClick);
        }

        private void OnBtnConfirmClick()
        {
            // GetUI<SignStreakUI>(UIConsts.SignStreakUI).PlayFinishTerminal();
            
            ClickStreakConfirm dot = new ClickStreakConfirm();
            DataDotMgr.Collect(dot);
            this.GetController<SettlementController>(ModelConsts.Settlement).ShowNextView(() => { Hide(); });

            var dot2 = new AppearStreakTargetSet();
            dot2.Current_target = _model.CheckInData.milestone.target_milestone.target_checkin_days.ToString();
            DataDotMgr.Collect(dot2);
            
            var dot3 = new AppearStreakProgress();
            dot3.Streak_days = _model.CheckInData.continue_checkin_days.ToString();
            DataDotMgr.Collect(dot3);
        }

        protected override void OnShow()
        {
            base.OnShow();
            
            Refresh();
            ui.trasition_sign_success.Play();
            SoundManger.instance.PlayUI("sign_flow_jump");
            AFDots.Appear_Rewards_streak_page();
            AFHelper.af_streak();
            FacebookHelper.EVENT_NAME_STREAK();
            
            MyFirebaseAnalytics.LogEvent("streak");
        }

        private void Refresh()
        {
            RefreshElements();
            RefreshList();
        }

        private void RefreshElements()
        {
            ui.tfStreak1.SetKey("ui_sign_persist_title");
            ui.tfStreak2.SetKey("ui_sign_persist_title");
            ui.tfdesc.SetKey("ui_sign_persist_desc");
            ui.tfDayPre.text = (_model.CheckInData.continue_checkin_days - 1).ToString();
            ui.tfDayNow.text = $"<font color=#FF632B,#FF4557>{_model.CheckInData.continue_checkin_days.ToString()}</font>";
            ui.btnConfirm.SetKey("ui_sign_persist_btn");
        }

        private void RefreshList()
        {
            _daycmps = new();
            ui.comSignPersistClalendar.listDays.RemoveChildren();
            // DateTimeOffset dateTime = DateTimeOffset.FromUnixTimeSeconds(TimeExt.serverTimeStamp / 1000);
            // var curDay = GetDayOfWeekKey(dateTime.LocalDateTime.DayOfWeek);
            DateTime dateTime = DateTime.Parse(_model.CheckInData.checkin_list[^1].date);
            var signDay = GetDayOfWeekKey(dateTime.DayOfWeek);
            var curDay = 7;
            var startDate = dateTime.AddDays(signDay == 7 ? 0 : -signDay);
            var startDate2 = startDate;
            var isContinue = true;
            var completedCnt = 0;
            var startCnt = -1;
            for (int i = 0; i < 7; i++)
            {
                completedCnt++;
                var date = startDate2.AddDays(i).ToString("yyyy-MM-dd");
                if (IsNew(date) && startCnt == -1)
                    startCnt = i;
                var state = GetDateState(date);
                if (state == DayCmpState.none || state == DayCmpState.repair)
                    isContinue = false;
                if (date == dateTime.ToString("yyyy-MM-dd"))
                    break;
            }

            if (isContinue)
            {
                ui.comSignPersistClalendar.imgProgressBarBg.visible = true;
                ui.comSignPersistClalendar.imgProgressBar.visible = true;
                ui.comSignPersistClalendar.spProgressBar.visible = true;
                ui.comSignPersistClalendar.spFire.visible = true;
                var size = ui.comSignPersistClalendar.imgProgressBarBg.size;
                var rate = startCnt == -1 ? 0 : startCnt;
                ui.comSignPersistClalendar.imgProgressBar.size = new Vector2(rate / 7f * size.x, size.y);
                ui.comSignPersistClalendar.spFire.spineAnimation.AnimationState.SetAnimation(0, "1", false); 
                RegisterTimer((t) =>
                {
                    VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);
                    ui.comSignPersistClalendar.imgProgressBar.TweenResize(new Vector2(completedCnt / 7f * size.x, size.y), 1f).OnComplete(() =>
                    {
                        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Vibrate);
                        if (completedCnt >= 7)
                        {
                            DrawTwinkCom();
                            ui.comSignPersistClalendar.spFire.spineAnimation.AnimationState.ClearListenerNotifications();
                            ui.comSignPersistClalendar.spFire.spineAnimation.AnimationState.SetAnimation(0, "2", false).Complete += (t) =>
                            {
                                ui.comSignPersistClalendar.spFire.spineAnimation.AnimationState.SetAnimation(0, "3", true);
                            };
                        }
                    });
                }, 1500);
            }
            else
            {
                ui.comSignPersistClalendar.imgProgressBarBg.visible = false;
                ui.comSignPersistClalendar.imgProgressBar.visible = false;
                ui.comSignPersistClalendar.spProgressBar.visible = false;
                ui.comSignPersistClalendar.spFire.visible = false;
            }
            
            
            for (int i = 0; i < 7; i++)
            {
                var date = startDate.AddDays(i).ToString("yyyy-MM-dd");
                CreateShopItem($"common_day_of_week_short_{curDay}", i, isContinue? DayCmpState.hide : GetDateState(date), IsNew(date));
                curDay = GetNextDay(curDay);
            }
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Success);
        }
        
        private void DrawTwinkCom()
        {
            var com = ui.comSignPersistClalendar.imgProgressBarBg;
            var rangeY = com.size.y - 40;
            var interval = 80f;
            var count = Mathf.Floor(com.size.x / interval);

            for (var i = 0; i < count; i++)
            {
                TwinkComponent twinkComponent = new();
                twinkComponent.Construct(UIPackage.GetByName("Sign").CreateObject("TwinkComponent").asCom);
                ui.comSignPersistClalendar.com.AddChildAt(twinkComponent.com, ui.comSignPersistClalendar.com.GetChildIndex(ui.comSignPersistClalendar.imgProgressBar) + 1);
                float rate1 = UnityEngine.Random.Range(0f, 1f);
                float rate2 = UnityEngine.Random.Range(0f, 1f);
                float rate3 = UnityEngine.Random.Range(0.5f, 1f);
                twinkComponent.com.xy = new Vector2((i + rate1) * interval + com.x, rate2 * rangeY + com.y);
                twinkComponent.com.size = rate3 * twinkComponent.com.size;
                twinkComponent.com.GetTransition("blick").timeScale = rate3;
                _twinkComs.Add(twinkComponent);
            }
        }

        private DayCmpState GetDateState(string date)
        {
            foreach (var checkinItem in _model.CheckInData.checkin_list)
            {
                if (checkinItem.date == date)
                {
                    if (checkinItem.is_checkin)
                        return DayCmpState.done;
                    if (checkinItem.is_recheckin)
                        return DayCmpState.repair;
                    return DayCmpState.none;
                }
            }
            return DayCmpState.none;
        }
        
        private bool IsNew(string date)
        {
            foreach (var checkinItem in _model.CheckInData.checkin_list)
            {
                if (string.Equals(checkinItem.date, date))
                {
                    if (string.Equals(checkinItem.update_date, TimeExt.serverToLocalTime.ToString("yyyy-MM-dd")))
                        return true;
                }
            }

            return false;
        }

        private SignPersistDayComponent CreateShopItem(string dayKey, int idx, DayCmpState dayCmpState, bool isNew)
        {
            SignPersistDayComponent dayCmp = new();
            dayCmp.Construct(UIPackage.GetByName("Sign").CreateObject("SignPersistDayComponent").asCom);
            dayCmp.tfDay.SetKey(dayKey);
            dayCmp.dummy_index.text = idx.ToString();
            dayCmp.state.selectedPage = dayCmpState.ToString();
            if (dayCmpState == DayCmpState.done)
            {
                if (isNew)
                {
                    dayCmp.spCheckin.spineAnimation.AnimationState.ClearTracks();
                    dayCmp.spCheckin.spineAnimation.AnimationState.SetAnimation(0, "yellow1", false).Complete += (t) =>
                    {
                        dayCmp.spCheckin.spineAnimation.AnimationState.SetAnimation(0, "yellow2", false);
                    };   
                }
                else
                {
                    dayCmp.spCheckin.spineAnimation.AnimationState.SetAnimation(0, "yellow2", false);
                }
            }
            else if (dayCmpState == DayCmpState.repair)
            {
                if (isNew)
                {
                    dayCmp.spCheckin.spineAnimation.AnimationState.ClearTracks();
                    dayCmp.spCheckin.spineAnimation.AnimationState.SetAnimation(0, "ice1", false).Complete += (t) =>
                    {
                        dayCmp.spCheckin.spineAnimation.AnimationState.SetAnimation(0, "ice2", false);
                    };
                }
                else
                {
                    dayCmp.spCheckin.spineAnimation.AnimationState.SetAnimation(0, "ice2", false);
                }
            }
            ui.comSignPersistClalendar.listDays.AddChildAt(dayCmp.com, ui.comSignPersistClalendar.listDays.numChildren);
            _daycmps.AddFirst(dayCmp);
            return dayCmp;
        }

        private int GetDayOfWeekKey(DayOfWeek date)
        {
            switch (date)
            {
                case DayOfWeek.Monday:
                    return 1;
                case DayOfWeek.Tuesday:
                    return 2;
                case DayOfWeek.Wednesday:
                    return 3;
                case DayOfWeek.Thursday:
                    return 4;
                case DayOfWeek.Friday:
                    return 5;
                case DayOfWeek.Saturday:
                    return 6;
                case DayOfWeek.Sunday:
                    return 7;
            }

            return 0;
        }

        private int GetNextDay(int day)
        {
            return day >= 7 ? 1 : day + 1;
        }
        
        private void ClearTwinkCom()
        {
            foreach (var item in _twinkComs)
            {
                item.com.Dispose();
            }
            _twinkComs.Clear();
        }

        protected override void OnHide()
        {
            base.OnHide();
            ClearTwinkCom();
        }
    }
}
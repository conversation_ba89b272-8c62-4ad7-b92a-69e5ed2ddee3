/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.FragmentPractice
{
    public partial class BtnImage : UIBindT
    {
        public override string pkgName => "FragmentPractice";
        public override string comName => "BtnImage";

        public BtnBaseAnswer btnAnswer;
        public ImageMaskComp imageComp;
        public GGraph holder;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            btnAnswer = (BtnBaseAnswer)com.GetChildAt(0);
            imageComp = new ImageMaskComp();
            imageComp.Construct(com.GetChildAt(1).asCom);
            holder = (GGraph)com.GetChildAt(2);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            btnAnswer = null;
            imageComp.Dispose();
            imageComp = null;
            holder = null;
        }
    }
}
﻿
using System;
using System.Threading;
using Msg.speech;
using UnityEngine;

public partial class ChatNetStrategy
{
    protected const int MaxASRTime = 3590000;   //ASR最大时长(s)
    protected const int ASRGapTime = 200;     //ASR上传间隔(ms)

    protected ASRvo _asrVO = null;
    protected int _lastCopyMicPhoneData = 0;
    protected byte[] _micPhoneData = new byte[0];

    /// <summary>
    /// 测试使用的 自动玩家语音
    /// </summary>
    private bool _testAutoPlayer = false;
    public void StartASR(ASRReqParam reqParam, Action<ASRProgressVO> progressCallback, Action<ASRCompleteVO> completeCallback,
        Action<ASRFailVO> failCallback,bool autoPlayer = false)
    {
        VFDebug.Log("ChatNetStrategy StartASR ");
        _testAutoPlayer = autoPlayer;
        if (this._asrVO != null)
        {
            this._asrVO.Dispose();
            this._asrVO = null;
            GMicrophoneManager.instance.EndRecord();
        }
        this._asrVO = new ASRvo
        {
            startTime = TimeExt.currTime,
            reqParam = reqParam,
            progressCallback = progressCallback,
            completeCallback = completeCallback,
            failCallback = failCallback,
            isRpcOver = false
        };

        if (!autoPlayer)
        {
            this.StartASRInternal();
        }
        else
        {
            this.StartTestASRInternal();
        }

    }

    protected void StartASRInternal()
    {
        GMicrophoneManager.instance.BeginRecord(MaxASRTime / 1000f);
        //
        CancellationTokenSource rpcToken = new CancellationTokenSource();
        this._asrVO.rpcToken = rpcToken;
        ASRvo asrVO = this._asrVO;
        //
        if (_tokenSource != null)
        {
            _tokenSource.Cancel();
        }
        this._micPhoneData = new byte[0];
        _tokenSource = rpcToken;
        this.StartASRStream(asrVO, rpcToken);
    }
    
    protected void StartTestASRInternal()
    {
        CancellationTokenSource rpcToken = new CancellationTokenSource();
        this._asrVO.rpcToken = rpcToken;
        ASRvo asrVO = this._asrVO;
        //
        if (_tokenSource != null)
        {
            _tokenSource.Cancel();
        }
        _tokenSource = rpcToken;
        this.StartASRStream(asrVO, rpcToken);
    }

    protected virtual async void StartASRStream(ASRvo asrVO, CancellationTokenSource rpcToken)
    {
        
        
    }
    
    private void UpdateASR(int interval)
    {
        if (_testAutoPlayer) return;
        if (this._asrVO == null) return;
        if (TimeExt.currTime - this._asrVO.startTime >= MaxASRTime)
        {
            this.StopASRInternal(ASRStatus.Timeout);
        }
        //固定从MicroPhone拷贝数据，防止Task线程冲突
        if (TimeExt.currTime - this._lastCopyMicPhoneData >= ASRGapTime)
        {
            this._lastCopyMicPhoneData = TimeExt.currTime;
            int nowDataLength = this._micPhoneData.Length;
            byte[] data = GMicrophoneManager.instance.GetRecordData();
            Array.Resize(ref this._micPhoneData, nowDataLength + data.Length);
            Array.Copy(data, 0, this._micPhoneData, nowDataLength, data.Length);
        }
    }

    
    //录音结束,提交ASR
    public AudioClip StopASR()
    {
        VFDebug.LogWarning("StopASR :" + this._asrVO);
        if (this._asrVO == null) return null;
        return this.StopASRInternal(ASRStatus.ClientComplete);
    }

    //主动终止ASR
    public void StopASRWithCancel()
    {
        VFDebug.LogWarning("StopASRWithCancel");
        if (this._asrVO == null)
        {
            return;
        }
        
        this.StopASRInternal(ASRStatus.ClientCancel);
    }

    protected AudioClip StopASRInternal(ASRStatus status)
    {
        if (this._asrVO == null) return null;
        VFDebug.Log("ChatNetStrategy StopASRInternal " + status);

        if (status == ASRStatus.ClientCancel)
        {
            // this._asrVO.rpcToken.Cancel();
            this._asrVO.isRpcCancel = true;
        }
        else
        {
            VFDebug.Log("StopASRInternal isRpcOver");
            this._asrVO.isRpcOver = true;
        }
        this._asrVO = null;
        GMicrophoneManager.instance.EndRecord();
        return GMicrophoneManager.instance.GetAudioClip();
    }
}

public class ASRvo
{
    public int startTime = 0;
    public ASRReqParam reqParam;
    public Action<ASRProgressVO> progressCallback;
    public Action<ASRCompleteVO> completeCallback;
    public Action<ASRFailVO> failCallback;
    public CancellationTokenSource rpcToken;
    public int rpcClipIndex = 0;
    public PB_ASRRecord rpcRecord;  //最后一次ASR结果
    public PB_PronunciationAssessmentRecord rpcAssessment;  //最后一次评测结果
    public bool isRpcOver;
    public bool isRpcCancel;

    public void Dispose()
    {
        reqParam = null;
        progressCallback = null;
        completeCallback = null;
        failCallback = null;
        rpcToken.Dispose();
        rpcToken = null;
    }
}

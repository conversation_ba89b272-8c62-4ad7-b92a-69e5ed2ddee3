﻿using ScriptsHot.Game.Modules.Explore.ExploreType.Base;
using ScriptsHot.Game.Modules.Explore.State.Base;
using ScriptsHot.Game.Modules.Procedure;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Explore.State.ExploreChat
{
    public class ExploreChatActiveStart:ExploreStateBase
    {
        private ExploreController _controller;
        
        private ExploreParam _param;

        private bool _isRuning = false;
        public ExploreChatActiveStart( ExploreEntityBase chat) : base(ExploreStateName.ExploreChatActiveStart, chat)
        {
        }
        
        public override void OnEnter(params object[] args)
        {
            base.OnEnter(args);
            _isRuning = false;
            // Debug.LogError("ExploreChatActiveStart");
            _param = (ExploreParam)args[0];
            _controller = ControllerManager.instance.GetController<ExploreController>(ModelConsts.Explore) as ExploreController;
           
        }
        
        override public void Update(int interval)
        {
            if (_param != null && _param.Entity.UI.GetModelLoaded())
            {
                Run();
            }
        }

        private void Run()
        {
            if (_isRuning) return;
            _isRuning = true;
            
            VFDebug.Log($"ExploreChatActiveStart:: Run  模型加载完毕开始播放进入语音");
            ProcedureParams p = new ProcedureParams();
            p.param = _param;
            p.type = EProcedureType.ExploreEnterAudio;
            //清空队列
            Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
            
            ProcedureParams p1 = new ProcedureParams();
            p1.param = _controller.IsSeniorPlayer;
            p1.type = EProcedureType.ExploreAvatarCallFirst;
            
            if (_controller.IsSeniorPlayer)
            {
                Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_enter_audio_play,p);
                Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_avatar_first_cell_show,p1);
            }
            else
            {
                Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_enter_audio_play,p);
                Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_avatar_first_cell_show,p1);
                // 翻译和内容 改为一个 cell中显示了 所以不存在这个 队列
                // Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_avatar_translate_show);
                //因为这个 不能被打断 所以目前不能放入队列
                // Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_scaffold_show,p);
            }
        }

        public override void OnReEnter(params object[] args)
        {
            base.OnReEnter(args);
        }

        public override void OnExit()
        {
            base.OnExit();
            _isRuning = false;
        }
    }
}
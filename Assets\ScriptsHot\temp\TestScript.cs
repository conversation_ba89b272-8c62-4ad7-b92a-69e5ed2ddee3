
using System;
using System.Collections.Generic;
using AnimationSystem;
using demo;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;

public class TestScruot : MonoBeh<PERSON>our
{
    public static List<string> characters = new List<string>
    {
        "NPC_Boy_00001", "NPC_Boy_00003", "NPC_Boy_00004", "NPC_Boy_00005",
        "NPC_Boy_00006", "NPC_Boy_00007", "NPC_Boy_00008", "NPC_Boy_00009", "NPC_Boy_00010",
        "NPC_Boy_00011", "NPC_Boy_00012", "NPC_Boy_00013", "NPC_Boy_00014", "NPC_Boy_00015",
        "NPC_Boy_00016", "NPC_Boy_00017", "NPC_Boy_00018", "NPC_Boy_00019", "NPC_Boy_00020",
        "NPC_Boy_00021", "NPC_Boy_00022", "NPC_Boy_00023", "NPC_Boy_00024", "NPC_Boy_00025", 
        "NPC_Boy_00027", "NPC_Boy_00028",
        "NPC_Girl_00001", "NPC_Girl_00002", "NPC_Girl_00003", "NPC_Girl_00004", "NPC_Girl_00005",
        "NPC_Girl_00006", "NPC_Girl_00007", "NPC_Girl_00008", "NPC_Girl_00009", "NPC_Girl_00010",
        "NPC_Girl_00011", "NPC_Girl_00012", "NPC_Girl_00013", "NPC_Girl_00014", "NPC_Girl_00015",
        "NPC_Girl_00016", "NPC_Girl_00017", "NPC_Girl_00019", "NPC_Girl_00020",
        "NPC_Girl_00021", "NPC_Girl_00022", "NPC_Girl_00023", "NPC_Girl_00024", "NPC_Girl_00025",
        "NPC_Girl_00026", "NPC_Girl_00027"
    };
    
    public AvatarLoader avatarLoader;

    public int currentIndex = 0;
    public TextMeshProUGUI text;
    public TMP_InputField inputField;
    
    public RuntimeAnimatorController overrideAnimatorControllerBoy = null;
    public RuntimeAnimatorController overrideAnimatorControllerGirl = null;
    
    // Start is called before the first frame update
    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        
    }

    public async void OnClickNext()
    {
        try
        {
            currentIndex = (currentIndex + 1) % characters.Count;
            string character = characters[currentIndex];
            text.text = character;

            GameObject avatarRoot = GameObject.Find("RoleSlot");
            for (int i = 0; i < avatarRoot.transform.childCount; i++)
            {
                DestroyImmediate(avatarRoot.transform.GetChild(i).gameObject);
                i--;
            }

            if (avatarRoot.GetComponent<AvatarActionDirector>() != null)
            {
                var component = avatarRoot.GetComponent<AvatarActionDirector>();
                component.enabled = false;
            }
            
            AvatarLoadExData data = new AvatarLoadExData();
            data.overrideAnimatorControllerBoy = overrideAnimatorControllerBoy;
            data.overrideAnimatorControllerGirl = overrideAnimatorControllerGirl;
            
            GameObject go = await avatarLoader.LoadNAvatar(character, avatarRoot.transform,exData:data,initAnimationManager:Util.CheckDEMO());
            avatarRoot.transform.GetChild(0).gameObject.SetActive(true);
            SetLayerRecursively(avatarRoot.transform.GetChild(0).gameObject, LayerMask.NameToLayer("player"));
            
#if UNITY_EDITOR
            GameObject Btns = GameObject.Find("Btns");

            // if (Btns != null)
            // {
            //     
            //     AnimatorUIController animatorUIController = Btns.GetComponent<AnimatorUIController>();
            //     animatorUIController.animator = GameObject.Find("RoleSlot").GetComponentInChildren<Animator>();
            //     if (animatorUIController != null)
            //     {
            //         animatorUIController.ReGen();
            //     }
            // }
            
            if (Util.CheckDEMO() && GameObject.Find("SimpleAnimTest") != null)
            {
                var simgo = GameObject.Find("SimpleAnimTest");
                var simpleAnimTest = simgo.GetComponent<SimpleAnimTest>();
                simpleAnimTest.animManager = avatarRoot.GetComponentInChildren<AnimationAvatarManager>();
                var dialogTest = simgo.GetComponent<DialogTest>();
                dialogTest.dialogManager = avatarRoot.GetComponentInChildren<DialogManager>();
            }
        
#endif
        }
        catch (Exception e)
        {
            Debug.LogError(e);
        }
    }
    
    public async void OnClickGOTO()
    {
        string character = inputField.text;
        if (character.StartsWith("G"))
        {
            character = "NPC_Girl_" + character.Substring(1);
        }
        else if (character.StartsWith("B"))
        {
            character = "NPC_Boy_" + character.Substring(1);
        }
        text.text = character;

        GameObject avatarRoot = GameObject.Find("RoleSlot");
        for (int i = 0; i < avatarRoot.transform.childCount; i++)
        {
            DestroyImmediate(avatarRoot.transform.GetChild(i).gameObject);
            i--;
        }

        if (avatarRoot.GetComponent<AvatarActionDirector>() != null)
        {
            var component = avatarRoot.GetComponent<AvatarActionDirector>();
            component.enabled = false;
        }
        
        AvatarLoadExData data = new AvatarLoadExData();
        data.overrideAnimatorControllerBoy = overrideAnimatorControllerBoy;
        data.overrideAnimatorControllerGirl = overrideAnimatorControllerGirl;
            
        GameObject go = await avatarLoader.LoadNAvatar(character, avatarRoot.transform,exData:data,initAnimationManager:Util.CheckDEMO());
        avatarRoot.transform.GetChild(0).gameObject.SetActive(true);
        SetLayerRecursively(avatarRoot.transform.GetChild(0).gameObject, LayerMask.NameToLayer("player"));
        
        
        
    #if UNITY_EDITOR
        GameObject Btns = GameObject.Find("Btns");

        // if (Btns != null)
        // {
        //     AnimatorUIController animatorUIController = Btns.GetComponent<AnimatorUIController>();
        //     animatorUIController.animator = GameObject.Find("RoleSlot").GetComponentInChildren<Animator>();
        //     if (animatorUIController != null)
        //     {
        //         animatorUIController.ReGen();
        //     }
        // }

        if (Util.CheckDEMO() && GameObject.Find("SimpleAnimTest") != null)
        {
            var simgo = GameObject.Find("SimpleAnimTest");
            var simpleAnimTest = simgo.GetComponent<SimpleAnimTest>();
            simpleAnimTest.animManager = avatarRoot.GetComponentInChildren<AnimationAvatarManager>();
            var dialogTest = simgo.GetComponent<DialogTest>();
            dialogTest.dialogManager = avatarRoot.GetComponentInChildren<DialogManager>();
        }
        
    #endif
    }
    
    // 设置指定 GameObject 及其所有子物体的层级
    public static void SetLayerRecursively(GameObject go, int layer)
    {
        // 设置当前 GameObject 的层级
        go.layer = layer;

        // 遍历所有子物体，并递归设置它们的层级
        foreach (Transform child in go.transform)
        {
            SetLayerRecursively(child.gameObject, layer);
        }
    }

    public void OnClickPlay()
    {
        //demo请使用女角色。
        GameObject avatarBody = GameObject.Find("Cartoon_Girl_Body_LOD(Clone)");
        if (avatarBody != null)
        {
            var dongzuomoni = avatarBody.GetComponent<dongzuomoni>();
            if (!dongzuomoni)
            {
                dongzuomoni = avatarBody.AddComponent<dongzuomoni>();
            }

            dongzuomoni.demoVoice = true;
        }
    }
}
using System;
using System.Collections;
using System.Collections.Generic;
using Cinemachine;
using UnityEngine;
using UIBind.Main;
using FairyGUI;
using DG.Tweening;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.ExplorePush;
using VerticesMatcher;

//using UnityEditor;

public class TabData
{
    public TabIndex TargetIndex;
    public TabIndex LastIndex;
}

/* 扩增tabIndex & icon说明
 * 1 fgui中的tabBtn需要增加新的icon，注意其大小 pivot，分配有效的ctrl编号
 * 2 根据其ctrl编号顺序制定下面的tabIndex要11对应
 */
public enum TabIndex
{
    Chapter =0,//1.2.0中已废弃雪藏了
    Home = 1,//这里实际表示的以前叫做centerhome，1.2.0后与chapter合体
    Explore = 2,
    Rank =3,
    Test =4 //
}

public enum BottomTabState
{
    showing,
    show2hide,
    hidding,
    hide2show,
}

public class MultiTabHomepageUI : BaseUI<MultiTabFramework>, IBaseUIUpdate
{
    private static bool EnterBiFlag = false;

    private const float _tabAniTime = 0.3f;
    public override string uiLayer => UILayerConsts.HomePage;//主UI层

    protected override bool isFullScreen => true;

    public CenterHomeUI CenterHome => UIManager.instance.GetUI<CenterHomeUI>(UIConsts.CenterHome);

    // private LearnPathModel learnPathModel = ModelManager.instance.GetModel<LearnPathModel>(ModelConsts.LearnPath);
    private MainModel mainModel => GetModel<MainModel>(ModelConsts.Main);
    // private LearnPathController learnPathController = ControllerManager.instance.GetController<LearnPathController>(ModelConsts.LearnPath);


    public MultiTabHomepageUI(string name) : base(name) { }


    public TabGList tabGList;

    public int TabNum = 4;//关键配置项 todo比较危险不应该允许随便修改
    public int InitalTabIdx = 0;//初始tab的 scrollIndex编号, 新课程为Centerpage实际值为0；探索页为1

    private bool allowScrollAnimWhenClickTabBtn = false;
    private long lastUpdateTime;
    private List<TabBtn> tabIconList = new List<TabBtn>();

    private Dictionary<TabIndex, int> currTabIndex2ScrollIndexDic = new Dictionary<TabIndex, int>();
    private Dictionary<int, TabIndex> currScrollIndex2TabIndexDic = new Dictionary<int, TabIndex>();
    private List<TabIndex> rawTabList = new List<TabIndex>();
    private float _tabRegionWidth;
    private float _tabCellWidth;



    private CinemachineVirtualCamera virtualCamera;
    private Camera mainCamera;
    private CinemachineCameraOffset cameraOffset;

    protected override void OnInit(GComponent uiCom)
    {
        ////声明tab动态元素
        //UIManager.instance.LoadPackage("Main", () =>
        //{
        //    UIObjectFactory.SetPackageItemExtension(UIPackage.GetItemURL("Main", "TabBtn"), typeof(TabBtn));
        //}
        //);

        tabGList = ui.TabGList as TabGList;


        tabGList.scrollItemToViewOnClick = false;         //点击时 自动滚动对齐view到所点的那个item

        //todo000 使用751 而非750的问题是因为 运行时scrollPane里的真实宽度被重算过
        tabGList.defaultItemSize = new Vector2(751, 1468);//必须要设，影响条目 宽度计算 
        //条目滑动时的临界位置吸附

        tabGList.OnTabChanged = this.OnTabChanged;//增加且tab时的事件触发

        tabGList.itemRenderer = TabItemRender;
        tabGList.itemProvider = TabItemProvider; //提供自身的资源图标
        tabGList.SetVirtual();

        ConfigDynamicTab();


        //没有scrollPane
        tabGList.autoResizeItem = false;                  //避免 imgBg元素被限制大小
        tabGList.scrollPane.snapToItem = true;
        tabGList.scrollPane.onScroll.Add(OnScrollMove);
        tabGList.scrollPane.onScrollEnd.Add(OnScrollMoveEnd);//拖拽过程中不会触发此事件

        //tabGList.scrollPane.onPullDownRelease.Add(this.OnPullDownRelease);//松手时判决一次
        //tabGList.scrollPane.onPullUpRelease.Add(this.OnPullUpRelease);//松手时判决一次

        tabGList.scrollPane.normalScrollVelocity = 10;

        ScrollPane.TWEEN_TIME_GO = 0.6f;//点击按钮的滑动完成的总缓动时长从0.3加大到0.6

        //递归类减速率，log函数细节参看ScrollPane L2133附近
        //原始标准值Mathf.Log(0.12, 0.967) 大约代表会有1s即60帧的缓动时间，
        //目前定义Mathf.Log(0.12, 0.935f)大约代表会有30帧（60fps下）的缓动时间
        tabGList.scrollPane.decelerationRate = 0.935f;

        SetUIContainerVisible(false);
    }

    public GComponent SetUIContainerVisible(bool value)
    {
        VFDebug.Log("SetUIContainerVisible::::" + value);
        this.ui.insertContainer.com.visible = value;
        return this.ui.insertContainer.com;
    }


    #region 处理icon  增减扩容
    private List<(TabIndex, bool)> tabVisibleInfoList = new List<(TabIndex, bool)>();
    public void SetTabVisible(TabIndex tabIndex, bool isVisible)
    {
        Debug.Log("TabSetting:  SetTabVisible,tab=" + tabIndex.ToString() +" vis:"+isVisible);
        if (tabVisibleInfoList.Count == 0) {
            foreach (TabIndex value in Enum.GetValues(typeof(TabIndex)))
            {
                tabVisibleInfoList.Add((value, true));//默认都展示
            }
        }

        for(int i=0;i<tabVisibleInfoList.Count;i++) {
            var item = tabVisibleInfoList[i];
            if (item.Item1 == tabIndex)
            {
                if (item.Item2 == isVisible)
                {
                    //目前暂时只考虑 从显示变为不可见的处理，
                    //skip 
                    return;
                }
                else {
                    tabVisibleInfoList[i] = (tabIndex, isVisible);
                }
            }
        }
    }
    private void ConfigDynamicTab()
 
    {
        this.SetTabVisible(TabIndex.Chapter, false);
        

        this.SetTabVisible(TabIndex.Test, AppConst.AllowShowChattingTab);
        
        Debug.Log("TabSetting:begin configDynamicTab");
        //如果没触发过 SetTabVisible预置兜底数据
        if (tabVisibleInfoList.Count == 0)
        {
            foreach (TabIndex value in Enum.GetValues(typeof(TabIndex)))
            {
                tabVisibleInfoList.Add((value, true));//默认都展示
            }
        }

        foreach (TabIndex value in Enum.GetValues(typeof(TabIndex)))
        {
            rawTabList.Add(value);
        }

        //从满tab状态，逐一剔除tab,过滤后的rawTabList只剩下可见tab
        Debug.Assert(rawTabList.Count == tabVisibleInfoList.Count, "初始的rawTab和tabVisibleInfo 数量必须是一致的 ");//有概率出现部分枚举值测ab用不上

        for (int i = 0; i < tabVisibleInfoList.Count; i++)
        {
            bool isVisible = tabVisibleInfoList[i].Item2;
            if (!isVisible) {
                rawTabList.Remove(tabVisibleInfoList[i].Item1);//剔除chapter
            }
        }


        foreach (var tab in rawTabList) {
            Debug.Log("Raw tab:"+tab.ToString());
        }


        TabNum = rawTabList.Count;

        //这两个dic是运行时tab顺序的双向反查表
        currScrollIndex2TabIndexDic.Clear();
        currTabIndex2ScrollIndexDic.Clear();
        for (int i = 0; i < rawTabList.Count; i++)
        {
            currTabIndex2ScrollIndexDic.Add(rawTabList[i], i);
            currScrollIndex2TabIndexDic.Add(i, rawTabList[i]);
        }

        //检查数量
        Debug.Assert(TabNum <= rawTabList.Count, "tab数量必须在总tabIndex枚举数量之内 ");//有概率出现部分枚举值测ab用不上
        tabGList.SetCurrIndex(this.InitalTabIdx, TabChangeReason.Inital);//设置0号元素为默认的index
    }

    public void ReInit() {

        
        //config数值

        //cfg1:BottomTabSelected 动态计算
        this._tabRegionWidth = this.ui.BottomTabRegion.com.width;//运行时根据真实手机宽度会改大小
        this._tabCellWidth = _tabRegionWidth / rawTabList.Count;//
        this.ui.BottomTabRegion.BottomTabSelected.width = _tabCellWidth;//要动态改写大小

        float tabBtnRealWidth = 80f / 750f * this.ui.BottomTabRegion.com.width; //考虑缩放

        //cfg2:padding 动态计算
        float tabBtnPaddingSpace = (this.ui.BottomTabRegion.com.width - (tabBtnRealWidth * rawTabList.Count)) / rawTabList.Count / 2;//（总宽- tab宽x tab数量）=没有tab部分时的剩余宽度， 剩余宽度/tabIndexValues.Length/2= padding宽度
        //Debug.Log("tabBtnPaddingSpace="+ tabBtnPaddingSpace);

        //== 动态组织 tabIcon容器==
        tabIconList.Clear();
        
        for (int i = 0; i < rawTabList.Count; i++)
        {

            //这里先假定
            int tabIndexVal = (int)rawTabList[i];//rawTabList 里的i号元素，不一定还是 TabIndex的i号枚举了

            Debug.Log("i="+i+" intval="+ tabIndexVal);

            var currTabBtn = new TabBtn();
            var tmpCom = UIPackage.CreateObject("Main", "TabBtn").asCom;
            tmpCom.name = ((TabIndex)tabIndexVal).ToString();
            currTabBtn.Construct(tmpCom);//手动强制构造

            currTabBtn.iconType.selectedIndex = tabIndexVal;
            tabIconList.Add(currTabBtn);
       
            this.ui.BottomTabRegion.com.AddChild(currTabBtn.com);
            currTabBtn.com.visible = true;
            if (i == 0)
            {
                currTabBtn.com.x = tabBtnPaddingSpace;
            }
            else
            {
                currTabBtn.com.x = tabBtnPaddingSpace+ _tabCellWidth * (i);
            }
        
            currTabBtn.com.y = 20;//效果图中是10整体缩放后是20

            
            //currTabBtn.com.pivotX = 0.5f;//涉及后续缩放变换必须指派
            //currTabBtn.com.pivotY = 0.75f;//这里不用1的原因是，用1时向上的突出效果太大，后续新UI还可能会调整

            AddUIEvent(tabIconList[i].com.onClick, () => {
                
                var scrollIndex = tabGList.GetCurrIndex();
                TabIndex currTabIndex = this.currScrollIndex2TabIndexDic[scrollIndex];

                Debug.Log("click tab :"+ ((TabIndex)tabIndexVal).ToString() + ",indexIntVal:" + tabIndexVal + " currScrollIndex:" + scrollIndex +" currTabIndex:"+(int)currTabIndex);
                if ((int)currTabIndex == tabIndexVal)
                {
                    Debug.Log("click tab skip");
                    return;
                }

                SetUIContainerVisible(false);
                SwitchTab((TabIndex)tabIndexVal, this.allowScrollAnimWhenClickTabBtn, true);//第三个参数会改tab的层级结构
            });

        }
        
 
        AddUIEvent(ui.showTabsBtn.com.onClick, () => {
            Debug.Log("click showtabs");
            this.ShowTabs();
        });

    }
    
    private void OnTabChanged(TabChangeReason reason)
    {
        //这个埋点位置不对先测试下
        MultiTabClickDataDot.TabEventType evt = MultiTabClickDataDot.TabEventType.click_home_bottom_bar_home_icon;
        int currIdx = this.tabGList.GetCurrIndex();
        int lastIdx = this.tabGList.GetLastIndex();

        bool isChanged2Explore = false;
        //TabIndex currTabIndex = (TabIndex)currIdx;
        TabIndex currTabIndex = this.currScrollIndex2TabIndexDic[currIdx];
        TabIndex lastTabIndex = this.currScrollIndex2TabIndexDic[lastIdx];

        switch (currTabIndex)
        {
            case TabIndex.Chapter:
                evt = MultiTabClickDataDot.TabEventType.click_home_bottom_bar_chapter_icon;
                break;
            case TabIndex.Explore:
                isChanged2Explore = true;
                evt = MultiTabClickDataDot.TabEventType.click_home_bottom_bar_explore_icon;
                break;
            case TabIndex.Home:
                evt = MultiTabClickDataDot.TabEventType.click_home_bottom_bar_home_icon;
                GetController<ExplorePushController>(ModelConsts.ExplorePush).ReqGetHomepageGuideItem();
                break;
            case TabIndex.Rank:
                evt = MultiTabClickDataDot.TabEventType.click_home_bottom_bar_rank_icon;
                break;
            case TabIndex.Test:
                evt = MultiTabClickDataDot.TabEventType.click_home_bottom_bar_test_icon;
                break;
        }

        if (isChanged2Explore)
        {
            this.ui.BottomTabRegion.BottomTabRegionBg_black.visible = true;
            this.ui.BottomTabRegion.BottomTabRegionBg_white.visible = false;
        }
        else {
            this.ui.BottomTabRegion.BottomTabRegionBg_black.visible = false;
            this.ui.BottomTabRegion.BottomTabRegionBg_white.visible = true;
        }

        Notifier.instance.SendNotification(NotifyConsts.MainTabChange,
            new TabData() { TargetIndex = currTabIndex, LastIndex = lastTabIndex });

        //点击和滑动区分上报
        if (reason == TabChangeReason.ClickTab)
        {
            var dot = new MultiTabClickDataDot(evt);
            DataDotMgr.Collect(dot);
        }
        else if(reason == TabChangeReason.SwipePage)
        {
            var dot = new MultiTabSwipeDataDot();

            dot.swipe_side = currIdx > lastIdx ? MultiTabSwipeDataDot.SwipeSide.right.ToString() : MultiTabSwipeDataDot.SwipeSide.left.ToString();
            dot.before_page = lastTabIndex.ToString().ToLower();
            dot.after_page = currTabIndex.ToString().ToLower();
            Debug.Log("swipe from "+ lastTabIndex.ToString() +" to "+ currTabIndex.ToString());
            DataDotMgr.Collect(dot);
        }
  
    }


    void TabItemRender(int index, GObject obj)
    {
        var camera = Camera.main;
        mainCamera = camera;

        CinemachineBrain cinemachineBrain = camera.GetComponent<CinemachineBrain>();
        if (cinemachineBrain)
        {
            //有没有什么路径,能够直接获取到此Camera? 能不能在开始时就初始化,而不是一直Update?
            CinemachineVirtualCamera activeVirtualCamera = cinemachineBrain.ActiveVirtualCamera as CinemachineVirtualCamera;
            if (activeVirtualCamera)
            {
                //camera.orthographic = true;
                activeVirtualCamera.m_Lens.Orthographic = true;
                activeVirtualCamera.m_Lens.OrthographicSize = 1.5f;
                virtualCamera = activeVirtualCamera;
                CinemachineCameraOffset cameraOffset = virtualCamera.GetComponent<CinemachineCameraOffset>();
                if (cameraOffset) this.cameraOffset = cameraOffset;
            }
        }
        IBaseUI baseUI=null;


        //var tabIdx = (TabIndex)index;
        if (!currScrollIndex2TabIndexDic.ContainsKey(index)) {
            Debug.LogError("render index fail,currScrollIndex2TabIndexDic not has key :"+index);
            return;
        }
        var tabIdx = currScrollIndex2TabIndexDic[index];//输入的scrollIndex实际映射到哪个类别动态决定
        switch (tabIdx)
        {
            // case TabIndex.Chapter:
            //     baseUI = GetUI<ChapterUI>(UIConsts.ChapterUI);
            //     if (!baseUI.isShow)
            //     {
            //         Debug.Log(" ChapterUI not show");
            //     }
            //     baseUI.Show4Tab();
            //     break;
            case TabIndex.Explore:
                baseUI = GetUI<ExplorePanelUI>(UIConsts.ExploreUI);
                baseUI.onCompleted = () => {
                    
                    ExplorePanelUI eUI = baseUI as ExplorePanelUI;
                    if (!eUI.IsCreated)
                    {
                        eUI.IsCreated = true;
                        if (this.InitalTabIdx == index)
                        {
                            //首轮创建 且 explore作为起始页时的补丁
                            
                            this.OnTabChanged(TabChangeReason.Inital);
                        }
                    }
                };

                if (!baseUI.isShow)
                {
                    Debug.Log(" Explosre not show");
                }
                baseUI.Show4Tab();
                
                break;
            case TabIndex.Rank:
                baseUI = GetUI<RankUI>(UIConsts.RankUI);//todo0 待替换
                if (!baseUI.isShow)
                {
                    Debug.Log(" Rank not show");
                }
                baseUI.Show4Tab();
                (baseUI as RankUI).RefreshUI();//强制刷一次数据
                
                break;

            case TabIndex.Home:
                baseUI = GetUI<CenterHomeUI>(UIConsts.CenterHome);
                if (!baseUI.isShow)
                {
                    Debug.Log(" Home not show");
                }
                baseUI.Show4Tab();
                
                break;
            case TabIndex.Test:
                baseUI = GetUI<TestTabUI>(UIConsts.TestTabUI);//todo0  测试类Tab功能
                if (!baseUI.isShow)
                {
                    Debug.Log(" Test not show");
                }
                baseUI.Show4Tab();
                break;
        }

        if (baseUI != null) {
            baseUI.uiComponent.SetSize(ui.TabGList.width, ui.TabGList.height);
        }
    }

    string TabItemProvider(int index)
    {
        Debug.Log("TabItemProvider idx=" + index);
        //var tabIdx = (TabIndex)index;
        var tabIdx = currScrollIndex2TabIndexDic[index];//输入的scrollIndex实际映射到哪个类别动态决定
        switch (tabIdx)
        {
            case TabIndex.Chapter:
                
                return UIConsts.ChapterUI;
            case TabIndex.Explore:
                
                return UIConsts.ExploreUI;
            case TabIndex.Rank:
                
                return UIConsts.RankUI;

            case TabIndex.Home:
                
                return UIConsts.CenterHome;
            case TabIndex.Test:

                return UIConsts.TestTabUI;
        }
        return "";
    }
    #endregion
    protected override void OnShow()
    {
        base.OnShow();
        //待设置
        

        this.originalBottomBarHeight = this.ui.bottomBar.height;//初始值设定

        ReInit();

        this.tabGList.numItems = TabNum;//这里会触发 centerHomeUI的渲染，需要放在ReInit之后

        if (!EnterBiFlag)
        {
            EnterBiFlag = true;
            AFHelper.EnterHomepage_finish();
            LoginBIHelper.Cut_home_page();

            Notifier.instance.SendNotification(NotifyConsts.InitMultiTabFramework);
        }

        PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.homepage;
    }

    public void SwitchTab(TabIndex tabIndex,bool ani,bool setFirst) {
        
        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
        int indexInScrollView = currTabIndex2ScrollIndexDic[tabIndex];//因为TabIndex 设定后，可能实际并不适用所有的TabIndex，会做AB实验
        tabGList.ScrollToView(indexInScrollView, ani, setFirst);
    }

    //初始 enterHomepage时，切换到 内部设置的默认index tab页，而非最左侧的tab页
    public void SwitchLastTab( bool ani, bool setFirst)
    {
        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
        var idx = tabGList.GetCurrIndex();
        //Debug.Log("SwitchLastTab idx="+idx);
        tabGList.ScrollToView(idx, ani, setFirst);
    }

    /// <summary>
    /// 当前是否在探索页签
    /// </summary>
    public bool IsExploreTab()
    {
        if (this.tabGList == null) return false;
        int currIdx = this.tabGList.GetCurrIndex();
        TabIndex currTabEnum = currScrollIndex2TabIndexDic[currIdx];
        return currTabEnum == TabIndex.Explore;
    }
    
    /// <summary>
    /// 当前拖拽到探索页面，处理拖拽放手瞬间，是否显示探索页面
    /// </summary>
    public bool IfMoveToExploreTab()
    {
        //之所以在这定义，因为游戏中目前显示3个tab,TabNum 却=4 ,无法使用
        int tabCount = 3;
        int exploreTab = 2; //第二个页签
        float min = 1f / (tabCount + 1);
        float max = 1 - min;
        // Debug.LogError($"_scrollXPercent={_scrollXPercent},min={min},max={max}");
        return _scrollXPercent > min && _scrollXPercent < max;
    }

    #region  MultiTab中的bottomTab的显示隐藏控制
    private BottomTabState _currTabState = BottomTabState.showing;
    public BottomTabState CurrTabState => _currTabState;
    private float originalBottomBarHeight;//应该是

    //[MenuItem("Tools/ShowTabs")]
    //public static void MenuShowTabs() {
    //    var ctrl = ControllerManager.instance.GetController(ModelConsts.Homepage) as HomepageController;
    //    ctrl._mtHpUI?.ShowTabs();
    //}

    //[MenuItem("Tools/HideTabs")]
    //public static void MenuHideTabs() {
    //    var ctrl =ControllerManager.instance.GetController(ModelConsts.Homepage) as HomepageController;
    //    ctrl._mtHpUI?.HideTabs();
    //}

    public void SetVisiable4ShowTabsBtn(bool visiable)
    {
        this.ui.showTabsBtn.showTabsBtn.visible = visiable;
    }

    public void ShowTabs()
    {
        if (_currTabState == BottomTabState.hidding)
        {
            _currTabState = BottomTabState.hide2show;
            this.tabGList.scrollPane.CancelDragging();//防御：先强制取消拖拽
            this.DoShowTabsAnim(() => {
                _currTabState = BottomTabState.showing;
                this.tabGList.scrollPane.touchEffect = true;//完全恢复后，启用滑屏
            });
        }
        else
        {
            Debug.LogWarning("Ignore ShowTabs, currState=" + _currTabState);
        }
        Notifier.instance.SendNotification(NotifyConsts.MultiTabShow);
    }

    public void HideTabs(TabIndex tabDuringHide,bool toHide = false,bool isDisplayShowTabIcon=true )
    {
        // Debug.LogError("HideTabs------------------------------");
        if (_currTabState == BottomTabState.showing)
        {
            _currTabState = BottomTabState.show2hide;

            //如果准备隐藏式 本身还在dragging的过程中

            if (this.tabGList.scrollPane.isDragged)
            {
                Debug.Log("===> isDrag + Hide");
                this.tabGList.scrollPane.CancelTouch();//强制模拟松手
                this.tabGList.scrollPane.CancelDragging();//防御：先强制取消拖拽
            }

            //无论是这个瞬间强制中断drag还是自己手动恰好松开，都要再等 TWEEN_TIME_DEFAULT 的惯性回弹时间后再重新做决策
            //需要等待模拟松手后 + TWEEN_TIME_DEFAULT时延的惯性滑动结束后，再执行hide & 禁用
            TimerManager.instance.RegisterTimer(
                count =>
                {

                    int tabIndex = currTabIndex2ScrollIndexDic[tabDuringHide];
                    if (this.tabGList.GetCurrIndex() == tabIndex)
                    {

                        Debug.Log("===> delay触发 cancelDrag的效果");


                        this.tabGList.scrollPane.touchEffect = false;//禁用滑屏

                        this.DoHideTabsAnim(() =>
                        {
                            _currTabState = BottomTabState.hidding;
                            Notifier.instance.SendNotification(NotifyConsts.MultiTabHide);
                        },isDisplayShowTabIcon);
                    }
                    else
                    {
                        //需要等待模拟松手后，如果切换到的tab页已经不再是 希望隐藏tab那个瞬间想要待的tab时，状态回滚 
                        _currTabState = BottomTabState.showing;
                        Debug.Log("TabState 状态回滚，hidetab失败，不在走隐藏逻辑，这时应该已经不是tabDuringHide：" + tabDuringHide.ToString());
                    }

                },
                Mathf.FloorToInt(ScrollPane.TWEEN_TIME_DEFAULT * 1000 + 30),//增加30ms 是在完成惯性部分后，多+1帧
                1);

         
        }
        else
        {
            if (toHide)
            {
                Notifier.instance.SendNotification(NotifyConsts.MultiTabHide);
            }
        }
    }
 

    private void DoShowTabsAnim(Action onComplete)
    {
        this.ui.showTabsBtn.com.visible = false;
        DOTween.To(
            ()=> this.ui.bottomBar.height,
            h => this.ui.bottomBar.height = h,
            this.originalBottomBarHeight, //回复初始高度
            _tabAniTime
        ).OnComplete(()=> {
            
            if (onComplete != null)
            {
                onComplete.Invoke();
            }
        });
    }

    private void DoHideTabsAnim(Action onComplete, bool isDisplayShowTabIcon=true)
    {
        DOTween.To(
             () => this.ui.bottomBar.height,
             h => this.ui.bottomBar.height = h,
             0,
             _tabAniTime
        ).OnComplete(() => {
            Debug.Log("showTabsBtn.visible = true");
            
             this.ui.showTabsBtn.com.visible = isDisplayShowTabIcon;//todo 这个是否加初始动画？
             if (onComplete != null)
             {
                 onComplete.Invoke();
             }
        });
    }

    #endregion 


    public float GetBottomTabHeight() {
        return this.ui.BottomTabRegion.BottomTabSelected.height;//目前和region同高
    }

    // public bool RefreshView(LearnPathNextTaskItemInfo result) 
    // {
    //     var _hpUI = UIManager.instance.GetUI<CenterHomeUI>( UIConsts.CenterHome);
    //     //step2 如果在显示ui
    //     if (_hpUI != null && _hpUI.isShow  )
    //     {
    //         Debug.Log("homepageCtrl do prepare");
    //
    //         _hpUI.RefreshBottomTab(result);
    //
    //         return true;
    //     }
    //     else
    //     {
    //         bool reason1 = _hpUI != null && _hpUI.isShow;
    //         //Debug.LogError($"Refresh fail: some data not ready, reasons->1:{reason1 } ");
    //         return false;
    //     }
    //
    // }


    void OnScrollMove()
    {
        var x = tabGList.scrollPane.scrollingPosX;
        //var y = tabGList.scrollPane.posY;
        //Debug.Log("==== scroll x="+x + " x2="+ tabGList.scrollPane.scrollingPosX);

        if (cameraOffset)
        {
            float aspect = (float)Screen.width / (float)Screen.height;
            var viewWidth = tabGList.scrollPane.viewWidth;
            MoveCamera_VirtualCamera((x - viewWidth)/ viewWidth, (1.5f * 2)*aspect,cameraOffset);
        }
        UpdateBottomTabOnScroll(x);
        //TryUpdateVisibilityOfBg(x);

        //BgOfBg组件挖洞 如需扩展 需要改FairyGUI此组件
        //float maskPageIdx = (int)TabIndex.Chapter;// pageIdx 从0开始, maskPage是留空没有bg的槽位
        float firstPageIdx = 0;// pageIdx 从0开始

        //float firstPagePosX = firstPageIdx * tabGList.scrollPane.viewWidth;
        //float maskPagePosX = maskPageIdx * tabGList.scrollPane.viewWidth;

        //float initPosX = -(ui.BgOfBg.com.width / 2 - tabGList.scrollPane.viewWidth / 2);
        //ui.BgOfBg.com.position = new Vector3(initPosX - (x - tabGList.scrollPane.viewWidth), ui.BgOfBg.com.position.y, 0);
        ui.BgOfBg.com.position = new Vector3( -x + -1*(firstPageIdx* tabGList.scrollPane.viewWidth), ui.BgOfBg.com.position.y, 0);
    }

    private float _scrollXPercent = 0;
    void UpdateBottomTabOnScroll(float scrollX) {
        //currSelected x range = 0~500
        var totalMovingRange = _tabCellWidth * (TabNum- 1);//总tab-1 & 单位宽度
        var totalScrollRange = _tabRegionWidth * (TabNum - 1);//3是总tab数目

        var scrollXPercent = scrollX / totalScrollRange;
        _scrollXPercent = scrollXPercent;
        this.ui.BottomTabRegion.BottomTabSelected.x = totalMovingRange * scrollXPercent;

        //icon大小缩放 根据x变化
        //cap min=100% max= 160%
        for (int i = 0; i < TabNum; i++) {
            var tabBtn = tabIconList[i];
            if (tabBtn == null)
            {
                continue;
            }
            else {
                //Debug.Log("UpdateBottomTabOnScroll icon idx="+i);
            }

            // (0~750) : (160%~100%)
            // (i0= ix0~750) : (160%~100%)

            float targetMaxCap = _tabRegionWidth * i;
            float absDisPrecent = Mathf.Abs(scrollX - targetMaxCap) / _tabRegionWidth;

            float scaleMultiplyPrecent = Mathf.Clamp( 1- absDisPrecent,0,1);//当absDisPrecent为0时最大

            float iconScale = 1f + 0.6f * scaleMultiplyPrecent;
          
            tabBtn.com.scale = new Vector2(iconScale, iconScale); 
        }
    }

    //处理两侧drag到极限值 露出天空盒的问题
    void TryUpdateVisibilityOfBg(float scrollX) {
        // if (scrollX < 1 || scrollX >= _tabRegionWidth*(TabNum - 1)+1) //todo 这里有临界极限 751 ！=750的问题，_tabRegionWidth是750
        // {
        //     if (this.ui.BgOfBg.visible)
        //     {
        //         // skip
        //     }
        //     else {
        //         this.ui.BgOfBg.visible = true;
        //     }
        // }
        // else
        // {
        //     // 非临界范围时不展示
        //     if (!this.ui.BgOfBg.visible)
        //     {
        //         // skip
        //     }
        //     else
        //     {
        //         this.ui.BgOfBg.visible = false;
        //     }
        // }
    }

 

    void OnScrollAutoUpdateIndex() {
        var scrollX = tabGList.scrollPane.posX;
        float ratio = scrollX / _tabRegionWidth;
        int basePart =Mathf.FloorToInt(ratio);
        float percentPart = ratio - (float)(basePart);
        if (percentPart > 0.5) {
            basePart++;
        }
        this.tabGList.SetCurrIndex(basePart, TabChangeReason.SwipePage);
        Debug.Log("OnScrollEnd auto SetIndex=" + basePart+" x="+scrollX+" w="+ _tabRegionWidth);
    }

    void OnScrollMoveEnd()
    {
        Debug.Log("==== scroll end ====");
        
        var x = tabGList.scrollPane.posX;
        if (cameraOffset)
        {
            float aspect = (float)Screen.width / (float)Screen.height;
            var viewWidth = tabGList.scrollPane.viewWidth;
            MoveCamera_VirtualCamera((x - viewWidth)/ viewWidth, (1.5f * 2)*aspect,cameraOffset);
        }

        OnScrollAutoUpdateIndex();

        if (tabGList.GetChildAt(0)?.name == UIConsts.CenterHome)
        {
            this.CenterHome.OnScrollToTop();
        }

        
    }

  


    public void Update(int interval)
    {
        if (isShow)
        {
            if (TimeExt.serverTimestamp - lastUpdateTime > 1000 * 60)
            {
                GetController<RankController>(ModelConsts.Rank).RefreshRankDataUI();
                lastUpdateTime = TimeExt.serverTimestamp;
            }
        }
    }
    
    #region 移动摄像机
    
    static Vector3 cameraPosition = new Vector3(4.45856f,1.698748f,3.527631f);
    
    public void OnUpdate3DView(float currentX,Camera camera)
    {
        float move = ((currentX - 750.0f) / 750.0f);
        MoveCamera(move,1.5f,cameraPosition,camera);
    }

    /// <summary>
    /// </summary>
    /// <param name="moveMultiplier">传递+1代表完全右滑出屏幕;传递-1代表完全左滑出屏幕</param>
    /// <param name="sizeOrDistance">透视相机传主体与相机的距离;非透视相机传Size.</param>
    /// <param name="origin">传相机原始位置</param>
    /// <param name="camera"></param>
    void MoveCamera(float moveMultiplier,float sizeOrDistance,Vector3 origin,Camera camera)
    {
        Transform cameraTransform = camera.transform;
        Vector3 cameraRight = cameraTransform.right;
        float cameraMoveMultiplier = sizeOrDistance * moveMultiplier;
        camera.transform.position = new (
            cameraPosition.x + cameraRight.x * cameraMoveMultiplier,
            cameraPosition.y + cameraRight.y * cameraMoveMultiplier,
            cameraPosition.z + cameraRight.z * cameraMoveMultiplier);
        
    }

    /// <summary>
    /// </summary>
    /// <param name="moveMultiplier">传递+1代表完全右滑出屏幕;传递-1代表完全左滑出屏幕</param>
    /// <param name="sizeOrDistance">透视相机传主体与相机的距离;非透视相机传Size.</param>
    /// <param name="virtualCameraOffset"></param>
    void MoveCamera_VirtualCamera(float moveMultiplier,float sizeOrDistance,CinemachineCameraOffset virtualCameraOffset)
    {
        virtualCameraOffset.m_Offset = new(
            moveMultiplier * sizeOrDistance,
            0);
    }

    #endregion

    protected override string[] ListNotificationInterests()
    {
        return new string[]
        {
            NotifyConsts.LearnPathReShowEvent,
        };
    }

    protected override void HandleNotification(string name, object body)
    {
        var enterPos = mainModel.ChatEnterPos;
        switch (name)
        {
            // case NotifyConsts.LearnPathReShowEvent:
            //     Debug.Log("Homepage HandleNotify OnRefreshProgress");
            //
            //     learnPathController.SendGetChapterProgressInfo();
            //
            //     //learnPathController.SendGetUserGoalNodeOnEntergame();
            //     mainModel.SetLearnPathAutoShow(true);
            //
            //     //if (_learnPathModel.NeedAutoOpen == true)
            //     //{
            //
            //     //    if (_learnPathModel.learnPathTaskState == true)
            //     //    {
            //     //        GetUI(UIConsts.ChapterUI).Show();
            //     //        _learnPathController.SendGetUserChapterInfo();
            //
            //     //    }
            //
            //     //}
            //     if (enterPos != ChatEnterPos.LearnPath) return;
            //     CheckAutoOpenLearnPath();
            //
            //     break;

        }
    }

    // public void CheckAutoOpenLearnPath()
    // {
    //     if (learnPathModel.NeedAutoOpen && mainModel.LearnPathAutoShowFlag)
    //     {
    //         mainModel.SetLearnPathAutoShow(false);
    //
    //         //this.SwitchTab(TabIndex.Chapter); //todo0 需要确认
    //         SendNotification(LearnPathCallEvent.OnRefreshChapterUI);
    //
    //
    //         //GetUI(UIConsts.ChapterUI).Show().onCompleted = () => { SendNotification(LearnPathCallEvent.OnRefreshChapterUI); };
    //
    //     }
    // }

    
    public bool IsChapterTab()
    {
        int currIdx = this.tabGList.GetCurrIndex();
        TabIndex currTabEnum = (TabIndex)currIdx;
        return currTabEnum == TabIndex.Chapter && _currTabState == BottomTabState.showing;
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using Google.Protobuf.Collections;
using Msg.basic;
using Msg.question;
using UnityEngine;

namespace ScriptsHot.Game.Modules.FragmentPractice
{
    public partial class FragmentPracticeModel : BaseModel
    {
        public FragmentPracticeModel() : base(ModelConsts.FragmentPractice) { }

        /// <summary>
        /// 当前题目
        /// </summary>
        public APracticeData CurQuestion;

        /// <summary>
        /// 轮次
        /// </summary>
        public int Round = 0;

        /// <summary>
        /// 上次轮次
        /// </summary>
        public int LastRound = 0;

        /// <summary>
        /// 首次作答记录 但是times要是最新的时候更新一下
        /// </summary>
        public Dictionary<long, QuestionAnswerData> FirstAnswerDecisions = new();

        /// <summary>
        /// 当前题索引
        /// </summary>
        public int CurQuestionIdx = 0;
        
        /// <summary>
        /// 当前题组
        /// </summary>
        public List<APracticeData> CurQuestions = new List<APracticeData>();

        /// <summary>
        /// 下次题组
        /// </summary>
        public List<APracticeData> NextQuestions = new List<APracticeData>();
        
        /// <summary>
        /// 总错题数
        /// </summary>
        public int ErrorQuestionCount = 0;

        /// <summary>
        /// 连对数
        /// </summary>
        public int ComboNum { get; private set; } = 0;

        /// <summary>
        /// 连错数
        /// </summary>
        public int ComboErrorNum = 0;

        /// <summary>
        /// 开始时间戳 秒
        /// </summary>
        public double StartTimeStamp = 0;

        /// <summary>
        /// 当前已做题目数
        /// </summary>
        public int CurQuestionNum = 0;

        /// <summary>
        /// 总正确题目数
        /// </summary>
        public int CorrectNum;

        public float InitProgress = 7f;

        /// <summary>
        /// 备胎题
        /// </summary>
        private APracticeData[] AlterList;
        private int alterListIdx = 0;

        /// <summary>
        /// 挑战题
        /// </summary>
        private APracticeData[] ChallengeList;
        private int challengeListIdx = 0;


        /// <summary>
        /// 是否为跳关模式
        /// </summary>
        public bool JumpMode { get; private set; } = false;
        
        public int JumpTaskShieldCurNum { get; private set; }
        public int JumpTaskShieldTotalNum { get; private set; }
        
        /// <summary>
        /// taskId 可为0 仅在路径进不可为0
        /// </summary>
        public long TaskId { get; private set; }

        public PB_DialogSourceEnum DialogSource { get; private set; }
        
        public bool IsJumpSpeak { get; private set; }
        
        public bool IsJumpListen { get; private set; }

        public void Clear()
        {
            CurQuestion = null;
            Round = 0;
            LastRound = 0;
            FirstAnswerDecisions.Clear();
            CurQuestionIdx = 0;
            CurQuestions.Clear();
            NextQuestions.Clear();
            ErrorQuestionCount = 0;
            ComboNum = 0;
            ComboErrorNum = 0;
            StartTimeStamp = 0;
            CurQuestionNum = 0;
            CorrectNum = 0;
            AlterList = null;
            alterListIdx = 0;
            ChallengeList = null;
            challengeListIdx = 0;
            JumpMode = false;
            JumpTaskShieldCurNum = 0;
            JumpTaskShieldTotalNum = 0;
            TaskId = 0;
            DialogSource = default;
            IsJumpSpeak = false;
            IsJumpListen = false;
        }

        public void SetJumpMode(bool state = false, int times = 0)
        {
            JumpMode = state;
            JumpTaskShieldTotalNum = times;
            JumpTaskShieldCurNum = times;
        }

        public void CutShieldNum()
        {
            JumpTaskShieldCurNum--;
        }

        public void SetTaskId(long taskId)
        {
            TaskId = taskId;
        }

        public void SetDialogSource(PB_DialogSourceEnum source)
        {
            DialogSource = source;
        }

        public void SetIsJumpSpeak(bool state)
        {
            IsJumpSpeak = state;
        }
        
        public void SetIsJumpListen(bool state)
        {
            IsJumpListen = state;
        }

        public void SetAlterList(RepeatedField<PB_QuickPracticeInfo> alterList)
        {
            AlterList = alterList.Select(p => APracticeData.TryCreate(0, p)).Where(p => p != null).ToArray();
            alterListIdx = 0;
        }

        public void SetChallengeList(RepeatedField<PB_QuickPracticeInfo> challengeList)
        {
            ChallengeList = challengeList.Select(p => APracticeData.TryCreate(0, p)).Where(p => p != null).ToArray();
            challengeListIdx = 0;
        }

        public APracticeData GetNextAlterPractice()
        {
            if (AlterList == null || AlterList.Length == 0 || alterListIdx >= AlterList.Length)
                return null;

            for (; alterListIdx < AlterList.Length; alterListIdx++)
            {
                if (CanReplaceWith(AlterList[alterListIdx]))
                {
                    return AlterList[alterListIdx++];  // 返回当前题目并移动到下一个
                }
            }
            return null;
        }

        public void IncreaseComboNum()
        {
            ComboNum++;
            if (ComboNum == 12)
            {
                // 连对达到12触发挑战题
                ReplaceChallengePractices();
            }
        }

        private void ReplaceChallengePractices()
        {
            const int maxCount = 3;  // 最多替换3道题
            int count = 0;
            int replacedIdx = CurQuestionIdx + 1;
            for (; challengeListIdx < ChallengeList.Length && count < maxCount && replacedIdx < CurQuestions.Count; challengeListIdx++)
            {
                // 找到下一个允许的题目
                while (challengeListIdx < ChallengeList.Length && !CanReplaceWith(ChallengeList[challengeListIdx]))
                {
                    challengeListIdx++;
                }
                if (challengeListIdx >= ChallengeList.Length) break;  // 没有更多题目可以替换

                // 替换
                var challengePractice = ChallengeList[challengeListIdx];
                CurQuestions[replacedIdx] = challengePractice;
                count++;
                replacedIdx++;
            }
        }

        private bool CanReplaceWith(APracticeData practice)
        {
            bool isAllowed = !IsJumpListen || !practice.IsListenPractice();
            isAllowed = isAllowed && (!IsJumpSpeak || !practice.IsSpeakPractice()); 
            return isAllowed;
        }

        public void ResetComboNum()
        {
            ComboNum = 0;
        }


        public struct QuestionAnswerData
        {
            public long Id;
            public PB_QuickPracticeType QuestionType;
            public bool IsRight;
            public double Duration;  //单题耗时，单位秒
            public PB_RecommendInfo RecommendInfo;
            public int CheckTimes;
        }
    }
}
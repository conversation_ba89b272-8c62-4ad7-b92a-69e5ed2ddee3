/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class BtnAudio : ABtnAudio
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "BtnAudio";
        public static string url => "ui://cmoz5osjz7rm35";

        public Controller ctrlPlaying;
        public Transition playing;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(BtnAudio));
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            ctrlPlaying = GetControllerAt(0);
            playing = GetTransitionAt(0);
        }
        public override void Dispose()
        {
            ctrlPlaying = null;
            playing = null;

            base.Dispose();
        }
    }
}
/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/16 15:50:42 星期四
# 功能：Nothing
****************************************************
*/

using System;
using FairyGUI;
using Game;
using Modules.DataDot;
using Msg.economic;
using ScriptsHot.Game.Modules.Settlement;

namespace ScriptsHot.Game.Modules.Shop
{
    [Obsolete]
    public class PlanPromotionStep1UI: ShopUIBase<UIBind.Shop.PlanPromotionStep1Panel>
    {
        public PlanPromotionStep1UI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top; //主UI层
        
        private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);
        
        private ShopModel _shopModel => GetModel<ShopModel>(ModelConsts.Shop);
        
        private CurrencyModel _currencyModel => GetModel<CurrencyModel>(ModelConsts.CurrencyController);
        protected override bool isFullScreen { get; } = true;
        private string _cfgTop;

        private enum PageState
        {
            normal = 0,
            premium_month,
            premium_year,
        }

        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(this.ui.btnExit.onClick, OnBtnExitClicked);
            AddUIEvent(this.ui.comPlanPromotionContent.btnNext.onClick, OnbBtnNextClicked);
            InitUI();
        }

        protected override void OnShow()
        {
            // 250327 中国版过审修改
            if (AppConst.IsCN)
            {
                Hide();
                return;
            }
            if (args != null && args.Length > 0)
            {
                var dot = new MembershipPage();
                DataDotMgr.Collect(dot);
                AFDots.Appear_membership_page();
                MyFirebaseAnalytics.LogEvent("membership");
            }
            ui.comPlanPromotionContent.spFlow.spineAnimation.AnimationState.SetAnimation(0,"1",false).Complete += (te) =>
            {
                ui.comPlanPromotionContent.spFlow.spineAnimation.AnimationState.SetAnimation(0, "b2", true);
            };
            ui.comPlanPromotionContent.Cutin.Play();
            
            PageState pageState = PageState.normal;
            if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Subscribing && _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthPremium && _mainModel.incentiveData.homepage_economic_info.member_info.is_member)
            {
                pageState = PageState.premium_month;
            }
            else if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Subscribing && 
                     (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.YearPremium ||
                      _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.QuarterPremium ||
                      _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.LifetimePremium)&& 
                     _mainModel.incentiveData.homepage_economic_info.member_info.is_member)
            {
                pageState = PageState.premium_year;
            }
            else if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.FreeTrial && _mainModel.incentiveData.homepage_economic_info.member_info.is_member)
            {
                pageState = PageState.premium_year;
            }

            if (pageState != PageState.normal)
            {
                if (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.LifetimePremium)
                {
                    ui.comPlanPromotionContent.tfSubscribeEndtime2.text = I18N.inst.MoStr("ui_shop_lifetime_12");
                }
                else
                {
                    ui.comPlanPromotionContent.tfSubscribeEndtime2.text = string.Format(I18N.inst.MoStr("ui_plan_instruction_desc13"),
                        GetModel<ShopModel>(ModelConsts.Shop).GetMemberExpiredTime().ToString(I18N.inst.MoStr("ui_plan_promotion_desc20")));
                }

            }

            // ui.comPlanPromotionContent.tfbtnPromotion.SetLanguage(LanguageType.MotherTongue, refresh: false).SetKey(
            //     _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Retrying? "ui_shop_btn_plan_retry":
            //     _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.UnSubscribe? "ui_plan_promotion_desc1"
            //         : _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.FreeTrialCanceled || _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Canceled
            //             ? "ui_plan_common_btn_stay"
            //             : "ui_plan_common_btn_subscribe");

            // ui.comPlanPromotionContent.tfPromotionTitle.SetLanguage(LanguageType.MotherTongue, refresh: false).SetKey(
            //     _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Retrying
            //         ? "ui_planPromotion_desc_plan_retry"
            //         : "ui_plan_promotion_desc7");
            
            
            ui.comPlanPromotionContent.state.selectedIndex = (int)pageState;
            
            if (_shopModel.TryGetMemberTypeByName(ShopController.MEMBER_TYPE_QUARTER, out var cfg_quarterly))
            {
                _cfgTop = ShopController.MEMBER_TYPE_QUARTER;
            }
            else if (_shopModel.TryGetMemberTypeByName(ShopController.MEMBER_TYPE_YEAR, out var cfg_yearly))
            {
                _cfgTop = ShopController.MEMBER_TYPE_YEAR;
            }

            CheckDiscount();
            CheckLifetime();
            CheckTitle();
        }
        
        private void CheckTitle()
        {
            var tmpTitle = GameEntry.ShopC.PlanPromotionStep1PanelTitle;
            if (!string.IsNullOrEmpty(tmpTitle))
            {
                ui.comPlanPromotionContent.tfPromotionTitle.text = tmpTitle;
            }
        }
        
        private void CheckDiscount()
        {
            bool isDiscount = GameEntry.ShopC.ShopModel.IsDiscountType && _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.UnSubscribe;
  
            if (isDiscount)
            {
                ui.comPlanPromotionContent.tfPromotionTitle.text = string.Format(I18N.inst.MoStr("ui_shop_discount_05")  , GameEntry.ShopC.ShopModel.GetMaxDiscountStr());
                ui.comPlanPromotionContent.tfbtnPromotion.text = string.Format(I18N.inst.MoStr("ui_shop_discount_06")  , GameEntry.ShopC.ShopModel.GetMaxDiscountStr());
            }
            else
            {
                ui.comPlanPromotionContent.tfPromotionTitle.text = I18N.inst.MoStr(
                    _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Retrying
                        ? "ui_planPromotion_desc_plan_retry"
                        : "ui_plan_promotion_desc7");
                
                ui.comPlanPromotionContent.tfbtnPromotion.text = I18N.inst.MoStr(
                    _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Retrying? "ui_shop_btn_plan_retry":
                        _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.UnSubscribe? "ui_plan_promotion_desc1"
                            : _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.FreeTrialCanceled || _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Canceled
                                ? "ui_plan_common_btn_stay"
                                : "ui_plan_common_btn_subscribe");
            }
        }

        private void CheckLifetime()
        {
            bool isLifeTime = GameEntry.ShopC.ShopModel.IsLifeTime;
            ui.comPlanPromotionContent.isLifetime.selectedIndex = isLifeTime ? 1 : 0;
            if (isLifeTime)
            {
                ui.comPlanPromotionContent.tfPromotionDesc4.text = I18N.inst.MoStr("ui_shop_lifetime_6");
                ui.comPlanPromotionContent.tfPromotionTitle.text = I18N.inst.MoStr("ui_shop_lifetime_5");
                ui.comPlanPromotionContent.tfbtnPromotion.text = I18N.inst.MoStr("ui_shop_lifetime_7");

            }

        }

        private void InitUI()
        {
            ui.comPlanPromotionContent.tfPromotionDesc1.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_promotion_desc21");
            ui.comPlanPromotionContent.tfPromotionDesc2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_promotion_desc22");
            ui.comPlanPromotionContent.tfPromotionDesc3.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_promotion_desc4");
            ui.comPlanPromotionContent.tfBtnUpgrade.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_btn_upgrade");
        }

        private void OnbBtnNextClicked()
        {
            if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Retrying)
            {
                DoUpdate();
            }
            else if (ui.comPlanPromotionContent.state.selectedIndex == (int)PageState.normal && _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus != SubscribeStatus.UnSubscribe)
            {
                //250424 业务调整为暂时下架直接在此页订阅，进入订阅比较页流程
                //DoSubscribe();
                DoNext();
            }
            else if (ui.comPlanPromotionContent.state.selectedIndex == (int)PageState.premium_month)
            {
                DoUpgrade();
            }
            else
            {
                DoNext();
            }
        }
        
        private void OnBtnExitClicked()
        {
            //新埋点：未免费订阅过的用户，点击开始体验后，在详细描述页点击退出
            DataDotDiamondFreeQuit dot = new DataDotDiamondFreeQuit();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            DataDotMgr.Collect(dot);

            bool needHide = true;
            if (args != null && args.Length > 0)
            {
                var source = (int)args[0];
                if (source == 4)
                {
                    needHide = false;
                    GetController<SettlementController>(ModelConsts.Settlement).ShowNextView(()
                    =>
                    { 
                        GetController<ShopController>(ModelConsts.Shop).CheckSendOnBoardOverMsg();
                       Hide();
                    });
                }
            }
            if (needHide)
            {
                GetController<ShopController>(ModelConsts.Shop).CheckSendOnBoardOverMsg();
                Hide();
            }
        }

        private void DoNext()
        {
            
            if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.FreeTrialCanceled)
            {
                GetUI<PlanSelectUI>(UIConsts.PlanSelectUI).Show(args);
            }
            else
            {
                bool isDiscount = GameEntry.ShopC.ShopModel.IsDiscountType;
                bool isLifetime = GameEntry.ShopC.ShopModel.IsLifeTime;
                if (!isDiscount && !isLifetime)
                {
                    GetUI<PlanPromotionStep3UI>(UIConsts.PlanPromotionStep3UI).Show(args);
                }
                else
                {
                    GetUI<PlanSelectUI>(UIConsts.PlanSelectUI).Show(args);
                }
            }
            Hide();
            //新埋点：未免费订阅过的用户，在详细描述页点击免费体验，进入下一个免费对比页
            // DataDotDiamondFreeTrial dot = new DataDotDiamondFreeTrial();
            
            
            // dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            // dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            // DataDotMgr.Collect(dot);
            DataDotMgr.Collect(new MembershipPageTrial());
            Hide();
        }

        private void DoUpdate()
        {
            GetController<ShopController>(ModelConsts.Shop).OpenSubscriptionUpdate();
            Hide();
        }
        
        private void DoSubscribe()
        {
            var cfg = _shopModel.GetMemberTypeByName(_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthPremium? "premium_monthly" : _cfgTop);
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            PurchasingManager.instance.StartPurchasing(cfg.product_id, PB_ProductType.PB_ProductType_Subscribe, OnUpgradeSuccess, OnBuyFail);
            //新埋点：用户在钻石商店第一页，点击订阅
            DataDotDiamondSubscribe dot = new DataDotDiamondSubscribe();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            dot.subscribe_price = cfg.price_in_cents;
            dot.price_currency = AppStoreInfoProvider.GetCountryCode();
            dot.source_page = nameof(PayWallDotHelper.LastSourcePage);
            DataDotMgr.Collect(dot);
            
           
            
            product_id = cfg.product_id;
            product_type = PB_ProductType.PB_ProductType_Subscribe.ToString();
        }
        
        private void DoUpgrade()
        {
            var cfg = _shopModel.GetMemberTypeByName(_cfgTop);
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            PurchasingManager.instance.StartPurchasing(cfg.product_id, PB_ProductType.PB_ProductType_Subscribe, OnBuySuccess, OnBuyFail);
            DataDotSubscriptionPurchase dot = new DataDotSubscriptionPurchase();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            dot.clicked_member_type = "YearPremium";
            dot.subscribe_price = cfg.price_in_cents;
            dot.price_currency = AppStoreInfoProvider.GetCountryCode();
            dot.source_page = nameof(PayWallDotHelper.LastSourcePage);
            DataDotMgr.Collect(dot);
            
           
            
            product_id = cfg.product_id;
            product_type = PB_ProductType.PB_ProductType_Subscribe.ToString();
        }

        private void OnUpgradeSuccess()
        {
            Hide();
            OnBuySuccess();
        }
    }
}
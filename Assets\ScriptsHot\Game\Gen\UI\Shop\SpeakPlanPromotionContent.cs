/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Shop
{
    public partial class SpeakPlanPromotionContent : UIBindT
    {
        public override string pkgName => "Shop";
        public override string comName => "SpeakPlanPromotionContent";

        public Controller state;
        public GRichTextField tfPromotionTitle;
        public GGraph btnNext;
        public GTextField tfbtnPromotion;
        public GRichTextField tfFreeTrail;
        public GButton btnUpgrade;
        public GTextField tfBtnUpgrade;
        public GTextField tfPromotionDesc1;
        public GTextField tfPromotionDesc2;
        public GTextField tfPromotionDesc3;
        public GTextField tfSubscribeEndtime2;
        public Transition Cutin;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            state = com.GetControllerAt(0);
            tfPromotionTitle = (GRichTextField)com.GetChildAt(1);
            btnNext = (GGraph)com.GetChildAt(2);
            tfbtnPromotion = (GTextField)com.GetChildAt(4);
            tfFreeTrail = (GRichTextField)com.GetChildAt(5);
            btnUpgrade = (GButton)com.GetChildAt(6);
            tfBtnUpgrade = (GTextField)com.GetChildAt(7);
            tfPromotionDesc1 = (GTextField)com.GetChildAt(10);
            tfPromotionDesc2 = (GTextField)com.GetChildAt(11);
            tfPromotionDesc3 = (GTextField)com.GetChildAt(12);
            tfSubscribeEndtime2 = (GTextField)com.GetChildAt(14);
            Cutin = com.GetTransitionAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            state = null;
            tfPromotionTitle = null;
            btnNext = null;
            tfbtnPromotion = null;
            tfFreeTrail = null;
            btnUpgrade = null;
            tfBtnUpgrade = null;
            tfPromotionDesc1 = null;
            tfPromotionDesc2 = null;
            tfPromotionDesc3 = null;
            tfSubscribeEndtime2 = null;
            Cutin = null;
        }
    }
}
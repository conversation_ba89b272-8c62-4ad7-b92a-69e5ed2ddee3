﻿

using LitJson;
using Msg.basic;
using Msg.explore;
using UnityEngine;
using UnityEngine.UIElements;

namespace ScriptsHot.Game.Modules.AgoraRtc
{
    public class VoiceChatDialogTextModel : BaseModel
    {
        private SC_UserChatDownMsgForUserRecognizing _streamUserDialogInfo2;
        public SC_UserChatDownMsgForUserRecognizing UserDialogInfo2 => _streamUserDialogInfo2;

        private SC_UserChatDownMsgForUserRecognized _streamUserDialogInfo;
        public SC_UserChatDownMsgForUserRecognized UserDialogInfo => _streamUserDialogInfo;



        private SC_UserChatDownMsgForOtherUserRecognizing _streamOtherDialogInfo2;
        public SC_UserChatDownMsgForOtherUserRecognizing OtherDialogInfo2 => _streamOtherDialogInfo2;

        private SC_UserChatDownMsgForOtherUserRecognized _streamOtherDialogInfo;
        public SC_UserChatDownMsgForOtherUserRecognized OtherDialogInfo => _streamOtherDialogInfo;

        private SC_UserChatDownMsgForOtherUserReplyTranslate _streamOtherDialogTranslate;
        public SC_UserChatDownMsgForOtherUserReplyTranslate OtherDialogTranslate => _streamOtherDialogTranslate;



        public VoiceChatDialogTextModel() : base(ModelConsts.VoiceChatDialogText)
        {
            
        }
        
     

        /// <summary>
        /// 用户自己说话数据--流返回
        /// </summary>
        /// <param name="ack"></param>
        public void SetUserDialogInfo(SC_UserChatDownMsgForUserRecognized ack)
        {
            _streamUserDialogInfo = ack;
            Debug.Log("VoiceChatDialogTextModel -> VoiceChatDialogTextModel");
            Notifier.instance.SendNotification(NotifyConsts.P2P_UserDialog);
        }

        /// <summary>
        /// 用户自己说话数据--中间的流返回 
        /// </summary>
        /// <param name="ack"></param>
        public void SetUserDialogInfo2(SC_UserChatDownMsgForUserRecognizing ack)
        {
            _streamUserDialogInfo2 = ack;
            Debug.Log("VoiceChatDialogTextModel -> VoiceChatDialogTextModel");
            Notifier.instance.SendNotification(NotifyConsts.P2P_UserDialog2);
        }

        /// <summary>
        /// 他人语音识别最终结果
        /// </summary>
        /// <param name="ack"></param>
        public void SetOtherDialogInfo(SC_UserChatDownMsgForOtherUserRecognized ack)
        {
            _streamOtherDialogInfo = ack;
            Debug.Log("VoiceChatDialogTextModel -> SetOtherDialogInfo");
            Notifier.instance.SendNotification(NotifyConsts.P2P_OtherDialog);
        }

        /// <summary>
        /// 他人语音识别最终结果
        /// </summary>
        /// <param name="ack"></param>
        public void SetOtherDialogInfo2(SC_UserChatDownMsgForOtherUserRecognizing ack)
        {
            _streamOtherDialogInfo2 = ack;
            Debug.Log("VoiceChatDialogTextModel -> SetOtherDialogInfo2");
            Notifier.instance.SendNotification(NotifyConsts.P2P_OtherDialog2);
        }


        /// <summary>
        /// 他人语音识别最终结果
        /// </summary>
        /// <param name="ack"></param>
        public void SetOtherDialogTranlate(SC_UserChatDownMsgForOtherUserReplyTranslate ack)
        {
            _streamOtherDialogTranslate = ack;
            Debug.Log("VoiceChatDialogTextModel -> SetOtherDialogTranlate");
            Notifier.instance.SendNotification(NotifyConsts.P2P_OtherDialogTranslate);
        }
        
        private SC_UserChatDownMsgForUserReplyExample _streamExample;
        public SC_UserChatDownMsgForUserReplyExample DialogExample => _streamExample;

        /// <summary>
        /// 用户回复示例
        /// </summary>
        /// <param name="ack"></param>
        public void SetExample(SC_UserChatDownMsgForUserReplyExample ack)
        {
            _streamExample = ack;
            Notifier.instance.SendNotification(NotifyConsts.P2P_DialogExample);
        }
        
        private SC_UserChatDownMsgForUserReplyExampleTranslate _streamExampleTranslate;
        public SC_UserChatDownMsgForUserReplyExampleTranslate DialogExampleTranslate => _streamExampleTranslate;

        /// <summary>
        /// 用户回复示例翻译
        /// </summary>
        /// <param name="ack"></param>
        public void SetExampleTranslate(SC_UserChatDownMsgForUserReplyExampleTranslate ack)
        {
            _streamExampleTranslate = ack;
            Notifier.instance.SendNotification(NotifyConsts.P2P_DialogExampleTranslate);
        }

        
        
        
    }
}
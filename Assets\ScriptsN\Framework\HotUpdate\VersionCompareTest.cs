using UnityEngine;

/// <summary>
/// 版本号比较测试脚本
/// 测试全数字版本号的比较逻辑
/// </summary>
public class VersionCompareTest : MonoBehaviour
{
    [Header("版本比较测试")]
    public bool runTest = false;
    
    [Header("测试版本号")]
    public string version1 = "250101022720";
    public string version2 = "250101022719";
    
    private void Start()
    {
        if (runTest)
        {
            RunVersionCompareTests();
        }
    }

    /// <summary>
    /// 运行版本比较测试
    /// </summary>
    private void RunVersionCompareTests()
    {
        Debug.Log("=== 版本号比较测试开始 ===");
        
        // 测试用例1：正常数字版本号比较
        TestVersionCompare("250101022720", "250101022719", 1, "新版本 > 旧版本");
        TestVersionCompare("250101022719", "250101022720", -1, "旧版本 < 新版本");
        TestVersionCompare("250101022720", "250101022720", 0, "相同版本");
        
        // 测试用例2：不同长度的版本号
        TestVersionCompare("250101022720", "25010102272", 1, "长版本号 > 短版本号");
        TestVersionCompare("25010102272", "250101022720", -1, "短版本号 < 长版本号");
        
        // 测试用例3：边界情况
        TestVersionCompare("1", "2", -1, "单位数比较");
        TestVersionCompare("10", "9", 1, "双位数 vs 单位数");
        TestVersionCompare("999999999999", "1000000000000", -1, "大数字比较");
        
        // 测试用例4：异常情况
        TestVersionCompare("", "250101022720", 0, "空字符串");
        TestVersionCompare("250101022720", "", 0, "空字符串");
        TestVersionCompare("abc", "250101022720", 0, "非数字字符串");
        TestVersionCompare("250101022720", "def", 0, "非数字字符串");
        
        // 测试用例5：实际场景
        TestVersionCompare("250101022720", "250101022721", -1, "同一天的不同版本");
        TestVersionCompare("250101022720", "250102022720", -1, "不同月份");
        TestVersionCompare("250101022720", "260101022720", -1, "不同年份");
        
        Debug.Log("=== 版本号比较测试完成 ===");
    }

    /// <summary>
    /// 测试版本比较
    /// </summary>
    private void TestVersionCompare(string v1, string v2, int expected, string description)
    {
        int result = CompareVersionNumbers(v1, v2);
        bool passed = result == expected;
        
        string status = passed ? "✓ PASS" : "✗ FAIL";
        string resultDesc = result > 0 ? ">" : (result < 0 ? "<" : "=");
        
        Debug.Log($"{status} {description}: {v1} {resultDesc} {v2} (期望: {expected}, 实际: {result})");
        
        if (!passed)
        {
            Debug.LogError($"测试失败: {description}");
        }
    }

    /// <summary>
    /// 比较版本号（复制自HotUpdateCore的逻辑）
    /// </summary>
    private int CompareVersionNumbers(string version1, string version2)
    {
        if (string.IsNullOrEmpty(version1) || string.IsNullOrEmpty(version2))
        {
            Debug.LogWarning($"版本号比较失败，存在空值: v1={version1}, v2={version2}");
            return 0;
        }

        // 尝试解析为数字进行比较
        if (long.TryParse(version1, out long v1) && long.TryParse(version2, out long v2))
        {
            if (v1 > v2) return 1;
            if (v1 < v2) return -1;
            return 0;
        }
        else
        {
            // 如果不是纯数字，则使用字符串比较
            Debug.LogWarning($"版本号不是纯数字格式，使用字符串比较: v1={version1}, v2={version2}");
            return string.Compare(version1, version2, System.StringComparison.Ordinal);
        }
    }

    /// <summary>
    /// 生成测试版本号
    /// </summary>
    private string GenerateVersionNumber(int year, int month, int day, int hour, int minute, int second)
    {
        // 格式：YYMMDDHHMMSS (12位数字)
        return $"{year:D2}{month:D2}{day:D2}{hour:D2}{minute:D2}{second:D2}";
    }

    /// <summary>
    /// 解析版本号
    /// </summary>
    private void ParseVersionNumber(string version)
    {
        if (version.Length == 12 && long.TryParse(version, out _))
        {
            string year = "20" + version.Substring(0, 2);
            string month = version.Substring(2, 2);
            string day = version.Substring(4, 2);
            string hour = version.Substring(6, 2);
            string minute = version.Substring(8, 2);
            string second = version.Substring(10, 2);
            
            Debug.Log($"版本号 {version} 解析为: {year}-{month}-{day} {hour}:{minute}:{second}");
        }
        else
        {
            Debug.LogWarning($"版本号 {version} 格式不正确，应为12位数字");
        }
    }

    /// <summary>
    /// 演示版本号使用场景
    /// </summary>
    private void DemonstrateVersionUsage()
    {
        Debug.Log("=== 版本号使用场景演示 ===");
        
        // 当前版本（强更）
        string currentVersion = "250101022720"; // 2025-01-01 02:27:20
        
        // 非强更版本
        string optionalVersion = "250101023000"; // 2025-01-01 02:30:00
        
        Debug.Log($"当前强更版本: {currentVersion}");
        Debug.Log($"非强更版本: {optionalVersion}");
        
        ParseVersionNumber(currentVersion);
        ParseVersionNumber(optionalVersion);
        
        int compareResult = CompareVersionNumbers(optionalVersion, currentVersion);
        if (compareResult > 0)
        {
            Debug.Log("✓ 非强更版本较新，可以进行更新");
        }
        else if (compareResult == 0)
        {
            Debug.Log("= 版本相同，无需更新");
        }
        else
        {
            Debug.Log("✗ 非强更版本较旧，跳过更新");
        }
    }

    private void OnGUI()
    {
        if (!runTest) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 400, 300));
        GUILayout.Label("版本号比较测试", GUI.skin.box);
        
        GUILayout.BeginHorizontal();
        GUILayout.Label("版本1:");
        version1 = GUILayout.TextField(version1, GUILayout.Width(120));
        GUILayout.EndHorizontal();
        
        GUILayout.BeginHorizontal();
        GUILayout.Label("版本2:");
        version2 = GUILayout.TextField(version2, GUILayout.Width(120));
        GUILayout.EndHorizontal();
        
        if (GUILayout.Button("比较版本"))
        {
            int result = CompareVersionNumbers(version1, version2);
            string resultText = result > 0 ? $"{version1} > {version2}" : 
                               result < 0 ? $"{version1} < {version2}" : 
                               $"{version1} = {version2}";
            Debug.Log($"比较结果: {resultText}");
        }
        
        if (GUILayout.Button("运行全部测试"))
        {
            RunVersionCompareTests();
        }
        
        if (GUILayout.Button("演示使用场景"))
        {
            DemonstrateVersionUsage();
        }
        
        if (GUILayout.Button("解析版本号"))
        {
            ParseVersionNumber(version1);
            ParseVersionNumber(version2);
        }
        
        GUILayout.EndArea();
    }
}

// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/service.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/service.proto</summary>
  public static partial class ServiceReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/service.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ServiceReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Ch5wcm90b2J1Zi9leHBsb3JlL3NlcnZpY2UucHJvdG8aHHByb3RvYnVmL2V4",
            "cGxvcmUvaW5uZXIucHJvdG8aIHByb3RvYnVmL2V4cGxvcmUvcmVjb21tZW5k",
            "LnByb3RvGiFwcm90b2J1Zi9leHBsb3JlL29uYm9hcmRpbmcucHJvdG8aHnBy",
            "b3RvYnVmL2V4cGxvcmUvZ2F0ZXdheS5wcm90bzJiChdFeHBsb3JlUmVjb21t",
            "ZW5kU2VydmljZRJHChBHZXRSZWNvbW1lbmRMaXN0EhcuQ1NfR2V0UmVjb21t",
            "ZW5kTGlzdFJlcRoYLlNDX0dldFJlY29tbWVuZExpc3RSZXNwIgAyhwEKGEV4",
            "cGxvcmVPbmJvYXJkaW5nU2VydmljZRJrChxHZXRPbmJvYXJkaW5nQ2hhdFBy",
            "ZWxvYWREYXRhEiMuQ1NfR2V0T25ib2FyZGluZ0NoYXRQcmVsb2FkRGF0YVJl",
            "cRokLlNDX0dldE9uYm9hcmRpbmdDaGF0UHJlbG9hZERhdGFSZXNwIgAyWAoT",
            "RXhwbG9yZUlubmVyU2VydmljZRJBCg5HZXRVc2VyVXNlRGF0YRIVLlNTX0dl",
            "dFVzZXJVc2VEYXRhUmVxGhYuU1NfR2V0VXNlclVzZURhdGFSZXNwIgAyVAoR",
            "RXhwbG9yZUJpelNlcnZpY2USPwoRRXhwbG9yZUNvbm5lY3Rpb24SEC5DU19F",
            "eHBsb3JlVXBNc2caEi5TQ19FeHBsb3JlRG93bk1zZyIAKAEwAUIqWhp2Zl9w",
            "cm90b2J1Zi9zZXJ2ZXIvZXhwbG9yZaoCC01zZy5leHBsb3JlYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.explore.InnerReflection.Descriptor, global::Msg.explore.RecommendReflection.Descriptor, global::Msg.explore.OnboardingReflection.Descriptor, global::Msg.explore.GatewayReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, null));
    }
    #endregion

  }
}

#endregion Designer generated code

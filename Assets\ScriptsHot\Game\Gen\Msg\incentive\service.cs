// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/incentive/service.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.incentive {

  /// <summary>Holder for reflection information generated from protobuf/incentive/service.proto</summary>
  public static partial class ServiceReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/incentive/service.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ServiceReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiBwcm90b2J1Zi9pbmNlbnRpdmUvc2VydmljZS5wcm90bxogcHJvdG9idWYv",
            "aW5jZW50aXZlL3Byb2ZpbGUucHJvdG8aIHByb3RvYnVmL2luY2VudGl2ZS9j",
            "aGVja2luLnByb3RvGiJwcm90b2J1Zi9pbmNlbnRpdmUvcmVjaGVja2luLnBy",
            "b3RvGiFwcm90b2J1Zi9pbmNlbnRpdmUvYWN0aXZhdGUucHJvdG8aInByb3Rv",
            "YnVmL2luY2VudGl2ZS9pbmNlbnRpdmUucHJvdG8aIHByb3RvYnVmL2luY2Vu",
            "dGl2ZS9yYW5raW5nLnByb3RvGidwcm90b2J1Zi9pbmNlbnRpdmUvaW5jZW50",
            "aXZlX3Rhc2sucHJvdG8aH3Byb3RvYnVmL2luY2VudGl2ZS9ncm93dGgucHJv",
            "dG8aI3Byb3RvYnVmL2luY2VudGl2ZS9mcmllbmRzaGlwLnByb3RvMtYKChBJ",
            "bmNlbnRpdmVTZXJ2aWNlEkUKDkdldFVzZXJQcm9maWxlEhUuQ1NfR2V0VXNl",
            "clByb2ZpbGVSZXEaGi5TQ19HZXRVc2VyUHJvZmlsZVJlc3BvbnNlIgASPwoM",
            "R2V0TXlQcm9maWxlEhMuQ1NfR2V0TXlQcm9maWxlUmVxGhguU0NfR2V0TXlQ",
            "cm9maWxlUmVzcG9uc2UiABJcChdTZXRVc2VyUHJvZmlsZUF1dGhvcml0eRIe",
            "LkNTX1NldFVzZXJQcm9maWxlQXV0aG9yaXR5UmVxGh8uU0NfU2V0VXNlclBy",
            "b2ZpbGVBdXRob3JpdHlSZXNwIgASXAoXR2V0VXNlclByb2ZpbGVBdXRob3Jp",
            "dHkSHi5DU19HZXRVc2VyUHJvZmlsZUF1dGhvcml0eVJlcRofLlNDX0dldFVz",
            "ZXJQcm9maWxlQXV0aG9yaXR5UmVzcCIAEjsKDFNlbmRGZWVkYmFjaxITLkNT",
            "X1NlbmRGZWVkYmFja1JlcRoULlNDX1NlbmRGZWVkYmFja1Jlc3AiABJbChdT",
            "ZXRVc2VyVm9pY2VJbmZvcm1hdGlvbhIeLkNTX1NldFVzZXJWb2ljZUluZm9y",
            "bWF0aW9uUmVxGh4uU0NfU2V0VXNlclZvaWNlSW5mb3JtYXRpb25BY2siABJG",
            "ChBTZXRVc2VyUG9ydHJhaXRzEhcuQ1NfU2V0VXNlclBvcnRyYWl0c1JlcRoX",
            "LlNDX1NldFVzZXJQb3J0cmFpdHNBY2siABJSChRTZXRVc2VyTGFuZ3VhZ2VM",
            "ZXZlbBIbLkNTX1NldFVzZXJMYW5ndWFnZUxldmVsUmVxGhsuU0NfU2V0VXNl",
            "ckxhbmd1YWdlTGV2ZWxBY2siABJGChBHZXRVc2VyUG9ydHJhaXRzEhcuQ1Nf",
            "R2V0VXNlclBvcnRyYWl0c1JlcRoXLlNDX0dldFVzZXJQb3J0cmFpdHNBY2si",
            "ABJhChlHZXRJbmNlbnRpdmVEYXRhRm9yUG9ydGFsEiAuQ1NfR2V0SW5jZW50",
            "aXZlRGF0YUZvclBvcnRhbFJlcRogLlNDX0dldEluY2VudGl2ZURhdGFGb3JQ",
            "b3J0YWxBY2siABJACg5TZXRVc2VyRHJlc3NVcBIVLkNTX1NldFVzZXJEcmVz",
            "c1VwUmVxGhUuU0NfU2V0VXNlckRyZXNzVXBBY2siABJtCh1HZXRVc2VyRHJl",
            "c3NVcE1lcmNoYW5kaXNlRGF0YRIkLkNTX0dldFVzZXJEcmVzc1VwTWVyY2hh",
            "bmRpc2VEYXRhUmVxGiQuU0NfR2V0VXNlckRyZXNzVXBNZXJjaGFuZGlzZURh",
            "dGFBY2siABJDCg9TZXRVc2VySGVhZEl0ZW0SFi5DU19TZXRVc2VySGVhZEl0",
            "ZW1SZXEaFi5TQ19TZXRVc2VySGVhZEl0ZW1BY2siABJJChFTZW5kR2lmdEZv",
            "ckZyaWVuZBIYLkNTX1NlbmRHaWZ0Rm9yRnJpZW5kUmVxGhguU0NfU2VuZEdp",
            "ZnRGb3JGcmllbmRBY2siABI6CgxTZXRTaG93U3RhdGUSEy5DU19TZXRTaG93",
            "U3RhdGVSZXEaEy5TQ19TZXRTaG93U3RhdGVBY2siABJkChpHZXRJbmNlbnRp",
            "dmVEYXRhRm9yRXhwbG9yZRIhLkNTX0dldEluY2VudGl2ZURhdGFGb3JFeHBs",
            "b3JlUmVxGiEuU0NfR2V0SW5jZW50aXZlRGF0YUZvckV4cGxvcmVBY2siABI6",
            "CgxTdGFydENvbnN1bWUSEy5DU19TdGFydENvbnN1bWVSZXEaEy5TQ19TdGFy",
            "dENvbnN1bWVBY2siADKABAoVSW5jZW50aXZlSW5uZXJTZXJ2aWNlEkYKEEdl",
            "dFVzZXJQb3J0cmFpdHMSFy5TU19HZXRVc2VyUG9ydHJhaXRzUmVxGhcuU1Nf",
            "R2V0VXNlclBvcnRyYWl0c0FjayIAEj0KDUdldFVzZXJEZXRhaWwSFC5TU19H",
            "ZXRVc2VyRGV0YWlsUmVxGhQuU1NfR2V0VXNlckRldGFpbEFjayIAEk8KE0lu",
            "Y2VudGl2ZVNldHRsZW1lbnQSGi5TU19JbmNlbnRpdmVTZXR0bGVtZW50UmVx",
            "GhouU1NfSW5jZW50aXZlU2V0dGxlbWVudEFjayIAElgKFlN5bmNPcmlnaW5E",
            "YXRhRm9yS2Fma2ESHS5TU19TeW5jT3JpZ2luRGF0YUZvckthZmthUmVxGh0u",
            "U1NfU3luY09yaWdpbkRhdGFGb3JLYWZrYUFjayIAEmEKGUluY2VudGl2ZVNl",
            "dHRsZUZvckV4cGxvcmUSIC5TU19JbmNlbnRpdmVTZXR0bGVGb3JFeHBsb3Jl",
            "UmVxGiAuU1NfSW5jZW50aXZlU2V0dGxlRm9yRXhwbG9yZUFjayIAElIKFFNl",
            "dFVzZXJMYW5ndWFnZUxldmVsEhsuU1NfU2V0VXNlckxhbmd1YWdlTGV2ZWxS",
            "ZXEaGy5TU19TZXRVc2VyTGFuZ3VhZ2VMZXZlbEFjayIAMtQICg5DaGVja2lu",
            "U2VydmljZRJPChJHZXRVc2VyQ2hlY2tpbkRhdGESGS5DU19HZXRVc2VyQ2hl",
            "Y2tpbkRhdGFSZXEaGS5TQ19HZXRVc2VyQ2hlY2tpbkRhdGFBY2siA4gCARJM",
            "ChFEcmF3Q2hlY2tpblJld2FyZBIYLkNTX0RyYXdDaGVja2luUmV3YXJkUmVx",
            "GhguU0NfRHJhd0NoZWNraW5SZXdhcmRBY2siA4gCARJPChNTZXRDaGVja2lu",
            "TWlsZXN0b25lEhouQ1NfU2V0Q2hlY2tpbk1pbGVzdG9uZVJlcRoaLlNDX1Nl",
            "dENoZWNraW5NaWxlc3RvbmVBY2siABJJChFHZXRDaGVja2luU3VtbWFyeRIY",
            "LkNTX0dldENoZWNraW5TdW1tYXJ5UmVxGhguU0NfR2V0Q2hlY2tpblN1bW1h",
            "cnlBY2siABIxCglSZWNoZWNraW4SEC5DU19SZWNoZWNraW5SZXEaEC5TQ19S",
            "ZWNoZWNraW5BY2siABJeChhHZXRVc2VyQ2hlY2tpblBvcnRhbERhdGESHy5D",
            "U19HZXRVc2VyQ2hlY2tpblBvcnRhbERhdGFSZXEaHy5TQ19HZXRVc2VyQ2hl",
            "Y2tpblBvcnRhbERhdGFBY2siABI0CgpEcmF3UmV3YXJkEhEuQ1NfRHJhd1Jl",
            "d2FyZFJlcRoRLlNDX0RyYXdSZXdhcmRBY2siABJ2Ch9HZXRVc2VyQ2hlY2tp",
            "bkRhdGFGb3JUYXNrRmluaXNoEiYuQ1NfR2V0VXNlckNoZWNraW5EYXRhRm9y",
            "VGFza0ZpbmlzaFJlcRomLlNDX0dldFVzZXJDaGVja2luRGF0YUZvclRhc2tG",
            "aW5pc2hBY2siA4gCARJYChZHZXRVc2VyQ2hlY2tpbkNhbGVuZGFyEh0uQ1Nf",
            "R2V0VXNlckNoZWNraW5DYWxlbmRhclJlcRodLlNDX0dldFVzZXJDaGVja2lu",
            "Q2FsZW5kYXJBY2siABJDCg9SZWZ1c2VSZWNoZWNraW4SFi5DU19SZWZ1c2VS",
            "ZWNoZWNraW5SZXEaFi5TQ19SZWZ1c2VSZWNoZWNraW5BY2siABJVChVHZXRG",
            "cmllbmRTdHJlYWtQb3J0YWwSHC5DU19HZXRGcmllbmRTdHJlYWtQb3J0YWxS",
            "ZXEaHC5TQ19HZXRGcmllbmRTdHJlYWtQb3J0YWxBY2siABJkChpVcGRhdGVG",
            "cmllbmRTdHJlYWtSZWxhdGlvbhIhLkNTX1VwZGF0ZUZyaWVuZFN0cmVha1Jl",
            "bGF0aW9uUmVxGiEuU0NfVXBkYXRlRnJpZW5kU3RyZWFrUmVsYXRpb25BY2si",
            "ABJqChxHZXRGcmllbmRTdHJlYWtSZWNvbW1lbmRMaXN0EiMuQ1NfR2V0RnJp",
            "ZW5kU3RyZWFrUmVjb21tZW5kTGlzdFJlcRojLlNDX0dldEZyaWVuZFN0cmVh",
            "a1JlY29tbWVuZExpc3RBY2siADKjBwoTQ2hlY2tpbklubmVyU2VydmljZRJR",
            "ChdHZXRVc2VyQ2hlY2tpbkRhdGFJbm5lchIZLlNTX0dldFVzZXJDaGVja2lu",
            "RGF0YVJlcRoZLlNTX0dldFVzZXJDaGVja2luRGF0YUFjayIAElEKEFN1Ym1p",
            "dFRhc2tGaW5pc2gSGy5TU19TdWJtaXRUYXNrRmluaXNoSW5mb1JlcRobLlNT",
            "X1N1Ym1pdFRhc2tGaW5pc2hJbmZvQWNrIgOIAgESWwoXR2V0VXNlckNoZWNr",
            "aW5NaWxlc3RvbmUSHi5TU19HZXRVc2VyQ2hlY2tpbk1pbGVzdG9uZVJlcRoe",
            "LlNTX0dldFVzZXJDaGVja2luTWlsZXN0b25lQWNrIgASWAoWR2V0Q2hlY2tp",
            "blN1bW1hcnlJbm5lchIdLlNTX0dldENoZWNraW5TdW1tYXJ5SW5uZXJSZXEa",
            "HS5TU19HZXRDaGVja2luU3VtbWFyeUlubmVyQWNrIgASUgoUR2V0Q2hlY2tp",
            "blN0YXRpc3RpY3MSGy5TU19HZXRDaGVja2luU3RhdGlzdGljc1JlcRobLlNT",
            "X0dldENoZWNraW5TdGF0aXN0aWNzQWNrIgASSQoRQ2hlY2tpblNldHRsZW1l",
            "bnQSGC5TU19DaGVja2luU2V0dGxlbWVudFJlcRoYLlNTX0NoZWNraW5TZXR0",
            "bGVtZW50QWNrIgASTwoTTWlsZXN0b25lU2V0dGxlbWVudBIaLlNTX01pbGVz",
            "dG9uZVNldHRsZW1lbnRSZXEaGi5TU19NaWxlc3RvbmVTZXR0bGVtZW50QWNr",
            "IgASagocR2V0Q2hlY2tpblN1bW1hcnlCeURhdGVSYW5nZRIjLlNTX0dldENo",
            "ZWNraW5TdW1tYXJ5QnlEYXRlUmFuZ2VSZXEaIy5TU19HZXRDaGVja2luU3Vt",
            "bWFyeUJ5RGF0ZVJhbmdlQWNrIgAScAoeR2V0RnJpZW5kU3RyZWFrSW5mb0Zv",
            "ckhvbWVwYWdlEiUuU1NfR2V0RnJpZW5kU3RyZWFrSW5mb0ZvckhvbWVwYWdl",
            "UmVxGiUuU1NfR2V0RnJpZW5kU3RyZWFrSW5mb0ZvckhvbWVwYWdlQWNrIgAS",
            "YQoZR2V0QWN0aXZlRnJpZW5kU3RyZWFrTGlzdBIgLlNTX0dldEFjdGl2ZUZy",
            "aWVuZFN0cmVha0xpc3RSZXEaIC5TU19HZXRBY3RpdmVGcmllbmRTdHJlYWtM",
            "aXN0QWNrIgAySwoNQ291cG9uU2VydmljZRI6CgxSZWRlZW1Db3Vwb24SEy5D",
            "U19SZWRlZW1Db3Vwb25SZXEaEy5TQ19SZWRlZW1Db3Vwb25BY2siADKAAgoO",
            "UmFua2luZ1NlcnZpY2USXgoYR2V0VXNlclJhbmtpbmdQb3J0YWxEYXRhEh8u",
            "Q1NfR2V0VXNlclJhbmtpbmdQb3J0YWxEYXRhUmVxGh8uU0NfR2V0VXNlclJh",
            "bmtpbmdQb3J0YWxEYXRhQWNrIgASNwoLSm9pblJhbmtpbmcSEi5DU19Kb2lu",
            "UmFua2luZ1JlcRoSLlNDX0pvaW5SYW5raW5nQWNrIgASVQoVU2V0UmFua2lu",
            "Z0NoYW5nZUNsaWNrEhwuQ1NfU2V0UmFua2luZ0NoYW5nZUNsaWNrUmVxGhwu",
            "U0NfU2V0UmFua2luZ0NoYW5nZUNsaWNrQWNrIgAy1QQKE1JhbmtpbmdJbm5l",
            "clNlcnZpY2USVQoVR2V0VXNlclJhbmtpbmdTdW1tYXJ5EhwuU1NfR2V0VXNl",
            "clJhbmtpbmdTdW1tYXJ5UmVxGhwuU1NfR2V0VXNlclJhbmtpbmdTdW1tYXJ5",
            "QWNrIgASWwoXR2V0VGFza0VzdGltYXRlRXhwQmF0Y2gSHi5TU19HZXRUYXNr",
            "RXN0aW1hdGVFeHBCYXRjaFJlcRoeLlNTX0dldFRhc2tFc3RpbWF0ZUV4cEJh",
            "dGNoQWNrIgASYQoUVHJpZ2dlclJhbmtpbmdVcGRhdGUSIS5TU19UcmlnZ2Vy",
            "UmFua2luZ1VwZGF0ZUJ5VGFza1JlcRohLlNTX1RyaWdnZXJSYW5raW5nVXBk",
            "YXRlQnlUYXNrQWNrIgOIAgESSQoRRHJhd1JhbmtpbmdSZXdhcmQSGC5TU19E",
            "cmF3UmFua2luZ1Jld2FyZFJlcRoYLlNTX0RyYXdSYW5raW5nUmV3YXJkQWNr",
            "IgASUgoUR2V0UmFua2luZ1N0YXRpc3RpY3MSGy5TU19HZXRSYW5raW5nU3Rh",
            "dGlzdGljc1JlcRobLlNTX0dldFJhbmtpbmdTdGF0aXN0aWNzQWNrIgASSQoR",
            "UmFua2luZ1NldHRsZW1lbnQSGC5TU19SYW5raW5nU2V0dGxlbWVudFJlcRoY",
            "LlNTX1JhbmtpbmdTZXR0bGVtZW50QWNrIgASPQoNU2V0dGxlRXhwSW5mbxIU",
            "LlNTX1NldHRsZUV4cEluZm9SZXEaFC5TU19TZXR0bGVFeHBJbmZvQWNrIgAy",
            "FgoUSW5jZW50aXZlVGFza1NlcnZpY2UysgUKGUluY2VudGl2ZVRhc2tJbm5l",
            "clNlcnZpY2USQAoOVXNlckZpbmlzaFRhc2sSFS5TU19Vc2VyRmluaXNoVGFz",
            "a1JlcRoVLlNTX1VzZXJGaW5pc2hUYXNrQWNrIgASQAoORHJhd1Rhc2tSZXdh",
            "cmQSFS5TU19EcmF3VGFza1Jld2FyZFJlcRoVLlNTX0RyYXdUYXNrUmV3YXJk",
            "QWNrIgASSQoRR2V0VXNlclRhc2tQb3J0YWwSGC5TU19HZXRVc2VyVGFza1Bv",
            "cnRhbFJlcRoYLlNTX0dldFVzZXJUYXNrUG9ydGFsQWNrIgASXgoYSW5jZW50",
            "aXZlUXVlc3RTZXR0bGVtZW50Eh8uU1NfSW5jZW50aXZlUXVlc3RTZXR0bGVt",
            "ZW50UmVxGh8uU1NfSW5jZW50aXZlUXVlc3RTZXR0bGVtZW50QWNrIgASSQoR",
            "R2V0RnJpZW5kc2hpcFRhc2sSGC5TU19HZXRGcmllbmRzaGlwVGFza1JlcRoY",
            "LlNTX0dldEZyaWVuZHNoaXBUYXNrQWNrIgASdgogRnJpZW5kc2hpcFRhc2tQ",
            "cm9ncmVzc1NldHRsZW1lbnQSJy5TU19GcmllbmRzaGlwVGFza1Byb2dyZXNz",
            "U2V0dGxlbWVudFJlcRonLlNTX0ZyaWVuZHNoaXBUYXNrUHJvZ3Jlc3NTZXR0",
            "bGVtZW50QWNrIgASUgoUQ3JlYXRlRnJpZW5kU2hpcFRhc2sSGy5TU19DcmVh",
            "dGVGcmllbmRTaGlwVGFza1JlcRobLlNTX0NyZWF0ZUZyaWVuZFNoaXBUYXNr",
            "QWNrIgASTwoTQ2hlY2tGcmllbmRTaGlwVGFzaxIaLlNTX0NoZWNrRnJpZW5k",
            "U2hpcFRhc2tSZXEaGi5TU19DaGVja0ZyaWVuZFNoaXBUYXNrQWNrIgAy5wIK",
            "Ekdyb3d0aElubmVyU2VydmljZRJSChRVc2VyR3Jvd3RoU2V0dGxlbWVudBIb",
            "LlNTX1VzZXJHcm93dGhTZXR0bGVtZW50UmVxGhsuU1NfVXNlckdyb3d0aFNl",
            "dHRsZW1lbnRBY2siABJYChZVcGRhdGVVc2VyR3Jvd3RoRmFjdG9yEh0uU1Nf",
            "VXBkYXRlVXNlckdyb3d0aEZhY3RvclJlcRodLlNTX1VwZGF0ZVVzZXJHcm93",
            "dGhGYWN0b3JBY2siABJJChFHZXRVc2VyR3Jvd3RoRGF0YRIYLlNTX0dldFVz",
            "ZXJHcm93dGhEYXRhUmVxGhguU1NfR2V0VXNlckdyb3d0aERhdGFBY2siABJY",
            "ChZEcmF3R3Jvd3RoV2Vla2x5UmV3YXJkEh0uU1NfRHJhd0dyb3d0aFdlZWts",
            "eVJld2FyZFJlcRodLlNTX0RyYXdHcm93dGhXZWVrbHlSZXdhcmRBY2siADJg",
            "Cg1Hcm93dGhTZXJ2aWNlEk8KE0dldEdyb3d0aFdlZWtseUdpZnQSGi5DU19H",
            "ZXRHcm93dGhXZWVrbHlHaWZ0UmVxGhouU0NfR2V0R3Jvd3RoV2Vla2x5R2lm",
            "dEFjayIAMtMEChFGcmllbmRzaGlwU2VydmljZRJJChFHZXRVc2VyRnJpZW5k",
            "TGlzdBIYLkNTX0dldFVzZXJGcmllbmRMaXN0UmVxGhguU0NfR2V0VXNlckZy",
            "aWVuZExpc3RBY2siABIxCglBZGRGcmllbmQSEC5DU19BZGRGcmllbmRSZXEa",
            "EC5TQ19BZGRGcmllbmRBY2siABI6CgxSZW1vdmVGcmllbmQSEy5DU19SZW1v",
            "dmVGcmllbmRSZXEaEy5TQ19SZW1vdmVGcmllbmRBY2siABJeChhHZXRSZWNv",
            "bW1lbmRlZEZyaWVuZExpc3QSHy5DU19HZXRSZWNvbW1lbmRlZEZyaWVuZExp",
            "c3RSZXEaHy5TQ19HZXRSZWNvbW1lbmRlZEZyaWVuZExpc3RBY2siABI0CgpT",
            "ZWFyY2hVc2VyEhEuQ1NfU2VhcmNoVXNlclJlcRoRLlNDX1NlYXJjaFVzZXJB",
            "Y2siABJVChVHZXRVc2VyR2lmdFBvcnRhbERhdGESHC5DU19HZXRVc2VyR2lm",
            "dFBvcnRhbERhdGFSZXEaHC5TQ19HZXRVc2VyR2lmdFBvcnRhbERhdGFBY2si",
            "ABJPChNNYXRjaEZyaWVuZFNoaXBUYXNrEhouQ1NfTWF0Y2hGcmllbmRTaGlw",
            "VGFza1JlcRoaLlNDX01hdGNoRnJpZW5kU2hpcFRhc2tBY2siABJGChBGcmll",
            "bmRTaGlwTm90aWZ5EhcuQ1NfRnJpZW5kU2hpcE5vdGlmeVJlcRoXLlNDX0Zy",
            "aWVuZFNoaXBOb3RpZnlBY2siADKlBAoWRnJpZW5kc2hpcElubmVyU2Vydmlj",
            "ZRJeChhVc2VyRnJpZW5kc2hpcFNldHRsZW1lbnQSHy5TU19Vc2VyRnJpZW5k",
            "c2hpcFNldHRsZW1lbnRSZXEaHy5TU19Vc2VyRnJpZW5kc2hpcFNldHRsZW1l",
            "bnRBY2siABJJChFHZXRGcmllbmRzaGlwVHlwZRIYLlNTX0dldEZyaWVuZHNo",
            "aXBUeXBlUmVxGhguU1NfR2V0RnJpZW5kc2hpcFR5cGVBY2siABJeChhGcmll",
            "bmRzaGlwVGFza1NldHRsZW1lbnQSHy5TU19GcmllbmRzaGlwVGFza1NldHRs",
            "ZW1lbnRSZXEaHy5TU19GcmllbmRzaGlwVGFza1NldHRsZW1lbnRBY2siABJJ",
            "ChFTZXRGcmllbmRHaWZ0SW5mbxIYLlNTX1NldEZyaWVuZEdpZnRJbmZvUmVx",
            "GhguU1NfU2V0RnJpZW5kR2lmdEluZm9BY2siABJhChlHZXRGcmllbmRzaGlw",
            "VGFza1VzZXJMaXN0EiAuU1NfR2V0RnJpZW5kc2hpcFRhc2tVc2VyTGlzdFJl",
            "cRogLlNTX0dldEZyaWVuZHNoaXBUYXNrVXNlckxpc3RBY2siABJSChRHZXRV",
            "c2VyQWxsRnJpZW5kTGlzdBIbLlNTX0dldFVzZXJBbGxGcmllbmRMaXN0UmVx",
            "GhsuU1NfR2V0VXNlckFsbEZyaWVuZExpc3RBY2siAEIuWhx2Zl9wcm90b2J1",
            "Zi9zZXJ2ZXIvaW5jZW50aXZlqgINTXNnLmluY2VudGl2ZWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.incentive.ProfileReflection.Descriptor, global::Msg.incentive.CheckinReflection.Descriptor, global::Msg.incentive.RecheckinReflection.Descriptor, global::Msg.incentive.ActivateReflection.Descriptor, global::Msg.incentive.IncentiveReflection.Descriptor, global::Msg.incentive.RankingReflection.Descriptor, global::Msg.incentive.IncentiveTaskReflection.Descriptor, global::Msg.incentive.GrowthReflection.Descriptor, global::Msg.incentive.FriendshipReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, null));
    }
    #endregion

  }
}

#endregion Designer generated code

/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.FragmentPractice
{
    public partial class PhoneCoreComp : UIBindT
    {
        public override string pkgName => "FragmentPractice";
        public override string comName => "PhoneCoreComp";

        public Controller questionType;
        public GGraph popupbg;
        public GLoader instructionLoader;
        public GLoader answerLoader;
        public GLoader questionLoader;
        public AduioCom audioCom;
        public GTextField tfDebugInfo;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            questionType = com.GetControllerAt(0);
            popupbg = (GGraph)com.GetChildAt(0);
            instructionLoader = (GLoader)com.GetChildAt(1);
            answerLoader = (GLoader)com.GetChildAt(2);
            questionLoader = (GLoader)com.GetChildAt(3);
            audioCom = new AduioCom();
            audioCom.Construct(com.GetChildAt(4).asCom);
            tfDebugInfo = (GTextField)com.GetChildAt(5);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            questionType = null;
            popupbg = null;
            instructionLoader = null;
            answerLoader = null;
            questionLoader = null;
            audioCom.Dispose();
            audioCom = null;
            tfDebugInfo = null;
        }
    }
}
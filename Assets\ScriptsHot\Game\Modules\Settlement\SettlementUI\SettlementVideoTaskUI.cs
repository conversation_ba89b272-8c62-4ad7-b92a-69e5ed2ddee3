﻿using System.Collections.Generic;
using FairyGUI;
using Modules.DataDot;
using Msg.basic;
using UIBind.Settlement;
using AudioItem = UIBind.Settlement.AudioItem;

namespace ScriptsHot.Game.Modules.Settlement.SettlementUI
{
    public class SettlementVideoTaskUI : BaseUI<SettlementVideoTaskPanel>
    {
        public SettlementVideoTaskUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top;
        private SettlementModel SettlementMod => GetModel<SettlementModel>(ModelConsts.Settlement);
        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            AddUIEvent(ui.compBottom.btnStart.onClick, OnClickNext);
            
            ui.compVideoDetail.compContent.list.itemRenderer = OnRendererLanguagePoint;
            
            ui.compVideoDetail.tfCongra.SetKey("freeTalk_Reward");
            ui.compBottom.tfReward.SetKey("common_continue");
        }

        protected override void OnShow()
        {
            base.OnShow();
            SoundManger.instance.PlayUI("settlement");
            ui.compBottom.state.selectedIndex = 1;
            RefreshCompVideoDetail();
        }

        protected override void OnHide()
        {
            base.OnHide();
            _dicBtnPlay.Clear();
            _curPlayIndex = -1;
        }

        protected override bool isFullScreen => true;
        
        private void RefreshCompVideoDetail()
        {
            PB_DialogSettlementData settlementData = SettlementMod.SettlementData;
            ui.compVideoDetail.tfTimes.text = settlementData.task_cost_time;
            ui.compVideoDetail.timeGrp.EnsureBoundsCorrect();
            ui.compVideoDetail.tf_xp_num.text = $"+{settlementData.experience}";
            ui.compVideoDetail.xpGrp.EnsureBoundsCorrect();
            ui.compVideoDetail.compContent.list.numItems = settlementData.video_knowledge_list.Count;
            ui.compVideoDetail.compContent.list.ResizeToFit();
            
            DotAppearSpeakResultPage dot = new DotAppearSpeakResultPage();
            dot.result_time = settlementData.task_cost_time;
            dot.result_exp = settlementData.experience;
            dot.level_type = PB_LevelTypeEnum.LTVideo.ToString();
            dot.session_record_id = settlementData.session_record_id;
            DataDotMgr.Collect(dot);
        }
        
        private void OnClickNext()
        {
            DataDotClick_Dialogue_result_continue dot = new DataDotClick_Dialogue_result_continue();
            dot.Dialogue_type = PB_DialogMode.Video;
            DataDotMgr.Collect(dot);
            
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            GetController<SettlementController>(ModelConsts.Settlement).ShowNextView(() => { Hide(); });
        }
        private Dictionary<GObject, int> _dicBtnPlay = new();
        private int _curPlayIndex = -1;
        private void OnRendererLanguagePoint(int index, GObject obj)
        {
            AudioItem item = new();
            item.Construct(obj as GComponent);
            PB_DialogSettlementData data = SettlementMod.SettlementData;
            if (!_dicBtnPlay.ContainsKey(item.btnPlay)) 
            {
                AddUIEvent(item.btnPlay.onClick, OnClickBtnPlay);
                _dicBtnPlay.Add(item.btnPlay, index);
            }

            if (_curPlayIndex == index)
                item.btnPlay.asCom.GetTransition("t0").Play();
            else
                item.btnPlay.asCom.GetTransition("t0").Stop();
            item.tfTitle.text = data.video_knowledge_list[index].sentence;
            item.tfContent.text = data.video_knowledge_list[index].translation;
            item.showLine.selectedIndex = index == data.video_knowledge_list.Count - 1 ? 0 : 1;
        }
        
        private void OnClickBtnPlay(EventContext ext)
        {
            GComponent comp = ext.sender as GComponent;
            if (comp == null) return;
            PB_DialogSettlementData data = SettlementMod.SettlementData;
            if (_dicBtnPlay.TryGetValue(comp,out int index))
            {
                if (_curPlayIndex == index)
                    TTSManager.instance.StopTTS();
                else
                {
                    TTSManager.instance.PlayTTS(data.video_knowledge_list[index].tts_id, () =>
                    {
                        _curPlayIndex = -1;
                        ui.compVideoDetail.compContent.list.numItems = data.video_knowledge_list.Count;
                    });
                    _curPlayIndex = index;
                    ui.compVideoDetail.compContent.list.numItems = data.video_knowledge_list.Count;
                }
            }
        }
    }
}
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/**
 * 统一规范资源加载路径
 * lizhuangzhuang
 * 2024年1月18日20:06:01
 */
public class ResUtils
{
    public static string GetStarX5SoundPath(string name)
    {
        return $"Assets/Build/Sound/StarX5/{name}";
    }
    
    public static string GetBinConfigPath(string name)
    {
        return $"Assets/Build/Config/{name}.bytes";
    }

    public static string GetSpinePath(string name)
    {
        return $"Assets/Build/Spine/{name}";
    }

    public static string GetSoundPath(string name)
    {
        return $"Assets/Build/Sound/{name}";
    }
    
    public static string GetMusicPath(string name)
    {
        return $"Assets/Build/Music/{name}";
    }
    
    public static string GetDramaSoundPath(string name)
    {
        return $"Assets/Build/Sound/DramaAvatarSound/{name}";
    }

    public static string GetEffectPath(string name)
    {
        return $"Assets/Build/Effect/{name}";
    }
    
    public static string GetUIModelPath(string name)
    {
        return $"Assets/Build/Model/UI/{name}";
    }
    
    public static string GetIconPath(string name)
    {
        return $"Assets/Build/Icon/{name}";
    }

    public static string GetRoleHeadPath(string name)
    {
        return $"Assets/Build/Icon/RoleHead/{name}";
    }
    
    public static string GetModelPath(string name)
    {
        return $"Assets/Build/Model/{name}";
    }

    public static string GetMaterialPath(string name)
    {
        return $"Assets/Build/Material/{name}";
    }

    public static string GetChatBgImgPath(string name)
    {
        return $"Assets/Build/ChatBgImg/{name}";
    }
    
    public static string GetScriptableObjectPath(string name)
    {
        return $"Assets/Build/ScriptableObject/{name}";
    }
    
    public static string GetExploreBgImgPath(string name)
    {
        return $"Assets/Build/ExploreBG/{name}";
    }
    
    public static string GetAvatarHeadPath(string name)
    {
        return $"Assets/Build/Avatar/avatar_{name.Replace(" ", "")}";
    }
    
    public static string GetAvatarBodyPath(string name)
    {
        return $"Assets/Build/Avatar/body_avatar_{name.Replace(" ", "")}";
    }
}

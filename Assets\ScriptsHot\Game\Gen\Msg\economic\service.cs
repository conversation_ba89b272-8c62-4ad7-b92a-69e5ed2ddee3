// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/economic/service.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.economic {

  /// <summary>Holder for reflection information generated from protobuf/economic/service.proto</summary>
  public static partial class ServiceReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/economic/service.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ServiceReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Ch9wcm90b2J1Zi9lY29ub21pYy9zZXJ2aWNlLnByb3RvGh1wcm90b2J1Zi9l",
            "Y29ub21pYy90cmFkZS5wcm90bxoccHJvdG9idWYvZWNvbm9taWMvY29pbi5w",
            "cm90bxodcHJvdG9idWYvZWNvbm9taWMvb3JkZXIucHJvdG8aH3Byb3RvYnVm",
            "L2Vjb25vbWljL2JlbmVmaXQucHJvdG8aH3Byb3RvYnVmL2Vjb25vbWljL2Fj",
            "Y291bnQucHJvdG8aG3Byb3RvYnVmL2Vjb25vbWljL3BheS5wcm90bxojcHJv",
            "dG9idWYvZWNvbm9taWMvbWVyY2hhbmRpc2UucHJvdG8yqAEKD0Vjb25vbWlj",
            "U2VydmljZRJQCg5HZXRDb2luQmFsYW5jZRIdLkNTX0Vjb25vbWljQ29pbkdl",
            "dEJhbGFuY2VSZXEaHS5TQ19FY29ub21pY0NvaW5HZXRCYWxhbmNlQWNrIgAS",
            "QwoPR2V0RWNvbm9taWNJbmZvEhYuQ1NfR2V0RWNvbm9taWNJbmZvUmVxGhYu",
            "U0NfR2V0RWNvbm9taWNJbmZvQWNrIgAy8gEKFEVjb25vbWljSW5uZXJTZXJ2",
            "aWNlEjQKCkdlblRyYWRlSUQSES5TU19HZW5UcmFkZUlkUmVxGhEuU1NfR2Vu",
            "VHJhZGVJZEFjayIAEisKB0FkZENvaW4SDi5TU19BZGRDb2luUmVxGg4uU1Nf",
            "QWRkQ29pbkFjayIAEjQKCkRlZHVjdENvaW4SES5TU19EZWR1Y3RDb2luUmVx",
            "GhEuU1NfRGVkdWN0Q29pbkFjayIAEkEKF1N1YnNjcmliZUNvaW5TZXR0bGVt",
            "ZW50EhEuU1NfU2V0dGxlQ29pblJlcRoRLlNTX1NldHRsZUNvaW5BY2siADLN",
            "AQoMT3JkZXJTZXJ2aWNlEjcKC0NyZWF0ZU9yZGVyEhIuQ1NfQ3JlYXRlT3Jk",
            "ZXJSZXEaEi5TQ19DcmVhdGVPcmRlckFjayIAEjcKC0NhbmNlbE9yZGVyEhIu",
            "Q1NfQ2FuY2VsT3JkZXJSZXEaEi5TQ19DYW5jZWxPcmRlckFjayIAEksKGUdl",
            "dFVucGFpZE9yZGVyQnlQcm9kdWN0SWQSFS5DU19HZXRVbnBhaWRPcmRlclJl",
            "cRoVLlNDX0dldFVucGFpZE9yZGVyQWNrIgAyoQIKEU9yZGVySW5uZXJTZXJ2",
            "aWNlEkwKEk9yZGVyUGF5bWVudENoYW5nZRIZLlNTX09yZGVyUGF5bWVudENo",
            "YW5nZVJlcRoZLlNTX09yZGVyUGF5bWVudENoYW5nZUFjayIAEkAKDkdldE9y",
            "ZGVyRGV0YWlsEhUuU1NfR2V0T3JkZXJEZXRhaWxSZXEaFS5TU19HZXRPcmRl",
            "ckRldGFpbEFjayIAEnwKIkdldFJlY2VudGx5Q3JlYXRlZE9yZGVyQnlQcm9k",
            "dWN0SWQSKS5TU19HZXRSZWNlbnRseUNyZWF0ZWRPcmRlckJ5UHJvZHVjdElk",
            "UmVxGikuU1NfR2V0UmVjZW50bHlDcmVhdGVkT3JkZXJCeVByb2R1Y3RJZEFj",
            "ayIAMq0CCg5CZW5lZml0U2VydmljZRJDCg9RdWVyeU1lbWJlckluZm8SFi5D",
            "U19RdWVyeU1lbWJlckluZm9SZXEaFi5TQ19RdWVyeU1lbWJlckluZm9BY2si",
            "ABJPChNOb3RpZnlNZW1iZXJCZW5lZml0EhouU0NfTm90aWZ5TWVtYmVyQmVu",
            "ZWZpdFJlcRoaLkNTX05vdGlmeU1lbWJlckJlbmVmaXRBY2siABJACg5NZW1i",
            "ZXJUeXBlUHVzaBIVLlNDX01lbWJlclR5cGVQdXNoUmVxGhUuQ1NfTWVtYmVy",
            "VHlwZVB1c2hBY2siABJDCg9QdXJjaGFzZVN0YW1pbmESFi5DU19QdXJjaGFz",
            "ZVN0YW1pbmFSZXEaFi5TQ19QdXJjaGFzZVN0YW1pbmFBY2siADKhAwoTQmVu",
            "ZWZpdElubmVyU2VydmljZRJVChVOb3RpZnlTdWJzY3JpYmVTdGF0dXMSHC5T",
            "U19Ob3RpZnlTdWJzY3JpYmVTdGF0dXNSZXEaHC5TU19Ob3RpZnlTdWJzY3Jp",
            "YmVTdGF0dXNBY2siABJSChRUcmlnZ2VyTWVtYmVyQmVuZWZpdBIbLlNTX1Ry",
            "aWdnZXJNZW1iZXJCZW5lZml0UmVxGhsuU1NfVHJpZ2dlck1lbWJlckJlbmVm",
            "aXRBY2siABJhChlUcmlnZ2VyVXNlclN0YW1pbmFCZW5lZml0EiAuU1NfVHJp",
            "Z2dlclVzZXJTdGFtaW5hQmVuZWZpdFJlcRogLlNTX1RyaWdnZXJVc2VyU3Rh",
            "bWluYUJlbmVmaXRBY2siABI9Cg1HZXRNZW1iZXJJbmZvEhQuU1NfR2V0TWVt",
            "YmVySW5mb1JlcRoULlNTX0dldE1lbWJlckluZm9BY2siABI9Cg1TdGFtaW5h",
            "UmV3YXJkEhQuU1NfU3RhbWluYVJld2FyZFJlcRoULlNTX1N0YW1pbmFSZXdh",
            "cmRBY2siADKmAwoYQXNzZXRBY2NvdW50SW5uZXJTZXJ2aWNlEkMKD0dldEFz",
            "c2V0QWNjb3VudBIWLlNTX0dldEFzc2V0QWNjb3VudFJlcRoWLlNTX0dldEFz",
            "c2V0QWNjb3VudEFjayIAEjoKDEdyYW50QmVuZWZpdBITLlNTX0dyYW50QmVu",
            "ZWZpdFJlcRoTLlNTX0dyYW50QmVuZWZpdEFjayIAEkMKD0dyYW50T3JkZXJB",
            "c3NldBIWLlNTX0dyYW50T3JkZXJBc3NldFJlcRoWLlNTX0dyYW50T3JkZXJB",
            "c3NldEFjayIAEkAKDkdyYW50Q2hhdEFzc2V0EhUuU1NfR3JhbnRDaGF0QXNz",
            "ZXRSZXEaFS5TU19HcmFudENoYXRBc3NldEFjayIAEkAKDkdyYW50VGFza0Fz",
            "c2V0EhUuU1NfR3JhbnRUYXNrQXNzZXRSZXEaFS5TU19HcmFudFRhc2tBc3Nl",
            "dEFjayIAEkAKDkluY2VudGl2ZUFzc2V0EhUuU1NfSW5jZW50aXZlQXNzZXRS",
            "ZXEaFS5TU19JbmNlbnRpdmVBc3NldEFjayIAMlQKClBheVNlcnZpY2USRgoN",
            "VmVyaWZ5UmVjZWlwdBIYLkNTX1ZlcmlmeVJlY2VpcHREYXRhUmVxGhkuU0Nf",
            "VmVyaWZ5UmVjZWlwdERhdGFSZXNwIgAykQEKEk1lcmNoYW5kaXNlU2Vydmlj",
            "ZRI4CgtHZXRTaG9wSW5mbxISLkNTX0dldFNob3BJbmZvUmVxGhMuU0NfR2V0",
            "U2hvcEluZm9SZXNwIgASQQoOUGF5TWVyY2hhbmRpc2USFS5DU19QYXlNZXJj",
            "aGFuZGlzZVJlcRoWLlNDX1BheU1lcmNoYW5kaXNlUmVzcCIAMp8GChdNZXJj",
            "aGFuZGlzZUlubmVyU2VydmljZRJfChhHZXRNZXJjaGFuZGlzZUNoYW5nZUlu",
            "Zm8SHy5TU19HZXRNZXJjaGFuZGlzZUNoYW5nZUluZm9SZXEaIC5TU19HZXRN",
            "ZXJjaGFuZGlzZUNoYW5nZUluZm9SZXNwIgASWQoWR2V0VXNlck1lcmNoYW5k",
            "aXNlSW5mbxIdLlNTX0dldFVzZXJNZXJjaGFuZGlzZUluZm9SZXEaHi5TU19H",
            "ZXRVc2VyTWVyY2hhbmRpc2VJbmZvUmVzcCIAElkKFlNldFVzZXJNZXJjaGFu",
            "ZGlzZUluZm8SHS5TU19TZXRVc2VyTWVyY2hhbmRpc2VJbmZvUmVxGh4uU1Nf",
            "U2V0VXNlck1lcmNoYW5kaXNlSW5mb1Jlc3AiABJTChRHZXRNZXJjaGFuZGlz",
            "ZURldGFpbBIbLlNTX0dldE1lcmNoYW5kaXNlRGV0YWlsUmVxGhwuU1NfR2V0",
            "TWVyY2hhbmRpc2VEZXRhaWxSZXNwIgASWQoWR2V0VXNlckludGltYWN5SW5m",
            "b0JveBIdLlNTX0dldFVzZXJJbnRpbWFjeUJveEluZm9SZXEaHi5TU19HZXRV",
            "c2VySW50aW1hY3lCb3hJbmZvUmVzcCIAElMKFEdldE1lcmNoYW5kaXNlQnlU",
            "eXBlEhsuU1NfR2V0TWVyY2hhbmRpc2VCeVR5cGVSZXEaHC5TU19HZXRNZXJj",
            "aGFuZGlzZUJ5VHlwZVJlc3AiABJBCg5Vc2VNZXJjaGFuZGlzZRIVLlNTX1Vz",
            "ZU1lcmNoYW5kaXNlUmVxGhYuU1NfVXNlTWVyY2hhbmRpc2VSZXNwIgASUwoU",
            "R2V0VXNlckxpbWl0UHJvcEluZm8SGy5TU19HZXRVc2VyTGltaXRQcm9wSW5m",
            "b1JlcRocLlNTX0dldFVzZXJMaW1pdFByb3BJbmZvUmVzcCIAElAKE0luaXRV",
            "c2VyTWVyY2hhbmRpc2USGi5TU19Jbml0VXNlck1lcmNoYW5kaXNlUmVxGhsu",
            "U1NfSW5pdFVzZXJNZXJjaGFuZGlzZVJlc3AiAEIsWht2Zl9wcm90b2J1Zi9z",
            "ZXJ2ZXIvZWNvbm9taWOqAgxNc2cuZWNvbm9taWNiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.economic.TradeReflection.Descriptor, global::Msg.economic.CoinReflection.Descriptor, global::Msg.economic.OrderReflection.Descriptor, global::Msg.economic.BenefitReflection.Descriptor, global::Msg.economic.AccountReflection.Descriptor, global::Msg.economic.PayReflection.Descriptor, global::Msg.economic.MerchandiseReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, null));
    }
    #endregion

  }
}

#endregion Designer generated code

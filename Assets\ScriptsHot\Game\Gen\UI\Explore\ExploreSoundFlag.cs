/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreSoundFlag : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreSoundFlag";

        public Controller ctrlState;
        public GMovieClip comEffect;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrlState = com.GetControllerAt(0);
            comEffect = (GMovieClip)com.GetChildAt(1);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrlState = null;
            comEffect = null;
        }
    }
}
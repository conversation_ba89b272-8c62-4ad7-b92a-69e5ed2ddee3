﻿using System;
using System.Linq;
using AnimationSystem;
using Msg.explore;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Explore.ExploreType.Base;
using ScriptsHot.Game.Modules.Procedure;
using UIBind.Explore.Item;
using UnityEngine;

public class ExploreNormalEntityActiveCom:ExploreComponentBase
    {

        private ExploreItemUI _cellItem => _cell as ExploreItemUI;
        private int _cellItemId;
        

        public ExploreNormalEntityActiveCom(IComponetOwner owner) : base(owner)
        {
        }

        public override void AddEvent()
        {
            base.AddEvent();
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreStreamUserDialogInfoUpdate,OnUserInfo);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreAvatarTxt,OnAvatarTxt);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreStroyStreamAvatarDialogTranslate,OnAvatarTranslate);
            
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreEnterAudioPlay,OnEnterPlayAudio);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreAvatarAudioPlay,OnAvatarAudioPlay);
            
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreChatFristAvatarShow,OnShowFirstAvatarCell);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreChatFirstAvatarTranslateShow,OnShowAvatarFristTranslateCell);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreChatAvatarShow,OnShowAvatarCell);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreChatPlayerShow,OnShowPlayerCell);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreChatPlayerShowOver,OnHidePlayerCell);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreClearScreen,OnExploreClearScreen);
            
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreShowActive,OnScaffoldActive);
            
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreDialogDownAwardMsg,OnAwardShow);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreShowScaffold,OnScaffoldShow);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreScaffoldAudio,OnScaffoldAudioPlay);
            
            Notifier.instance.RegisterNotification(NotifyConsts.MultiTabShow,OnMultiTabShow);
            Notifier.instance.RegisterNotification(NotifyConsts.MultiTabHide,OnMultiTabHide);
            
            Notifier.instance.RegisterNotification(NotifyConsts.NewWorldAudioStart,OnNewWorldAudioStart);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreStopCellAudioEffect,OnNewWorldAudioStart);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreGRPCFail,OnExploreGRPCFail);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreDownMsgForServerBasic,OnExploreGRPCReConnect);
            
            
            
        }

        public override void RemoveEvent()
        {
            base.RemoveEvent();
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreStreamUserDialogInfoUpdate,OnUserInfo);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreAvatarTxt,OnAvatarTxt);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreStroyStreamAvatarDialogTranslate,OnAvatarTranslate);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreEnterAudioPlay,OnEnterPlayAudio);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreAvatarAudioPlay,OnAvatarAudioPlay);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreChatFristAvatarShow,OnShowFirstAvatarCell);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreChatFirstAvatarTranslateShow,OnShowAvatarFristTranslateCell);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreChatAvatarShow,OnShowAvatarCell);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreChatPlayerShow,OnShowPlayerCell);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreChatPlayerShowOver,OnHidePlayerCell);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreClearScreen,OnExploreClearScreen);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreShowActive,OnScaffoldActive);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreDialogDownAwardMsg,OnAwardShow);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreShowScaffold,OnScaffoldShow);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreScaffoldAudio,OnScaffoldAudioPlay);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.MultiTabShow,OnMultiTabShow);
            Notifier.instance.UnRegisterNotification(NotifyConsts.MultiTabHide,OnMultiTabHide);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.NewWorldAudioStart,OnNewWorldAudioStart);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreStopCellAudioEffect,OnNewWorldAudioStart);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreGRPCFail,OnExploreGRPCFail);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreDownMsgForServerBasic,OnExploreGRPCReConnect);
            
        }

        private void OnExploreGRPCReConnect(string name, object body)
        {
            //VFDebug.LogError("收到 GRPC初始化消息，断线重连");
            Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.NoNetworkTipUI);
            _cellItem.SetBubbleLoadingVisible(false);
            _cellItem.SetContinueBtnVisible(false);
            // if (_cellItem.ContinueBtn.com.visible) return;
            // 处理用户发送消息后 断网 一直收不到返回的情况
            if (_cellItem.LogicEntity != null && _cellItem.LogicEntity.Step == ExploreStep.Player)
            {
                //隐藏底部 显示麦克风
                _controller.GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage).HideTabs(TabIndex.Explore,true);
            }
        }

        private void OnExploreGRPCFail(string name, object body)
        {
            _cellItem.SetBubbleLoadingVisible(false);
        }

        private void OnNewWorldAudioStart(string name, object body)
        {
            _cellItem.IsPlayAvatarAudio = false;
            _cellItem.IsPlayScaffoldAudio = false;
            _cellItem.AudioOver();
        }

        #region Tab
        private void OnMultiTabShow(string name, object body)
        {
            // Debug.LogError("Tab OnMultiTabShow");
            _controller.IfCanMicrophone = false;
            _controller.CloseRecordUI();
            //停止声音
            Notifier.instance.SendNotification(NotifyConsts.ExploreSoundStop);
            //停止动画
            Notifier.instance.SendNotification(NotifyConsts.ExploreStopCellAudioEffect);
            //清空队列
            Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
            _cellItem.SetContinueBtnVisible(true);
            
            if (this._cellItem.LogicEntity != null && _cellItem.LogicEntity.Round == 0)
            {
                //显示第一个 avatar cell
                Notifier.instance.SendNotification(NotifyConsts.ExploreChatFristAvatarShow,true);
            }
            
            CLICK_HIDDEN_TAB_CLOSE data2 = new CLICK_HIDDEN_TAB_CLOSE();
            data2.task_id = _controller.CurTaskId;
            data2.dialogue_id = _controller.CurEnterEntity.LogicEntity.DialogId;
            data2.dialogue_round = _controller.CurEnterEntity.LogicEntity.Round;
            DataDotMgr.Collect(data2);
            
            Notifier.instance.SendNotification(NotifyConsts.ExploreBGMopen,false);
        }
        
        private void OnMultiTabHide(string name, object body)
        {
            Notifier.instance.SendNotification(NotifyConsts.ExploreBGMopen,true);
            VFDebug.Log("OnMultiTabHide:" + _cellItem.LogicEntity.Step);
            if (_cellItem.LogicEntity.Step == ExploreStep.Recording) return;
            
            _controller.ShowRecordUI(!_controller.IsSeniorPlayer);
        }
        
        #endregion
      

        public override void Update(int interval)
        {
            int currentId = _cell.GetHashCode();
            if (currentId != _cellItemId)
            {
                VFDebug.Log($"警告: _cell 引用已变化! 原哈希码: {_cellItemId}, 当前哈希码: {currentId}");
                _cellItemId = currentId;
            }
            
            // 检查 Avatar 音频播放状态
            if (_cellItem.IsPlayAvatarAudio)
            {
                AudioSource audioSource = GSoundManager.instance.CurAvatarAudioSource;
                // 增加 null 检查
                if (audioSource == null || audioSource.clip == null)
                {
                    VFDebug.Log($"Avatar 音频源无效，重置播放状态 - Item ID: {_cellItem.Data?.taskId}");
                    _cellItem.IsPlayAvatarAudio = false;
                    return;
                }
                
                // Debug.Log($"Avatar 音频状态: isPlaying={audioSource.isPlaying}, time={audioSource.time}, length={audioSource.clip.length}");
                
                // 检查是否停止播放 或 接近音频结束
                if (!audioSource.isPlaying && audioSource.time > 0 || 
                    (audioSource.time > 0 && audioSource.time >= audioSource.clip.length - 0.1f))
                {
                    VFDebug.Log($"Avatar TTS音频播放完毕 - time: {audioSource.time}, length: {audioSource.clip.length}");
                    _cellItem.IsPlayAvatarAudio = false;
                    _cellItem.AudioOver();
                    Notifier.instance.SendNotification(NotifyConsts.ExploreAvatarAudioPlayOver, (this.GetOwner() as ExploreEntityBase).CurTtsAudioId);
                    
                    _controller.ModelDialogManager?.EnterDialogueIdleState();
                }
            }

            // 检查脚手架音频播放状态
            if (_cellItem.IsPlayScaffoldAudio)
            {
                AudioSource audioSource = GSoundManager.instance.GetChannel("TTS");
                // 增加 null 检查
                if (audioSource == null || audioSource.clip == null)
                {
                    VFDebug.LogError($"脚手架音频源无效，重置播放状态 - Item ID: {_cellItem.Data?.taskId}");
                    _cellItem.IsPlayScaffoldAudio = false;
                    return;
                }
                
                // Debug.Log($"脚手架音频状态: isPlaying={audioSource.isPlaying}, time={audioSource.time}, length={audioSource.clip.length}");
                
                // 检查是否停止播放 或 接近音频结束
                if (!audioSource.isPlaying && audioSource.time > 0 || 
                    (audioSource.time > 0 && audioSource.time >= audioSource.clip.length - 0.1f))
                {
                    VFDebug.Log($"脚手架 TTS音频播放完毕 - time: {audioSource.time}, length: {audioSource.clip.length}");
                    _cellItem.IsPlayScaffoldAudio = false;
                    _cellItem.AudioOver();
                }
            }
        }

        public void CheckScaffoldAudio()
        {
        }

        /// <summary>
        /// 清屏
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private void OnExploreClearScreen(string name, object body)
        {
            _cellItem.AvatarItem.Com.com.visible = false;
            _cellItem.AvatarTranslate.Com.com.visible = false;
            _cellItem.PlayerItem.Com.com.visible = false;
            _cellItem.ScaffoldItem.Com.com.visible = false;
            _cellItem.AdviceItem.Com.com.visible = false;
            _cellItem.ContinueBtn.com.visible = false;
            _cellItem.SetBubbleLoadingVisible(false);
        }

        public override void Clear()
        {
            VFDebug.Log($"ExploreNormalEntityActiveCom 被清理 - Item ID: {_cellItem.Data?.taskId}, IsPlayAvatarAudio: {_cellItem.IsPlayAvatarAudio}, IsPlayScaffoldAudio: {_cellItem.IsPlayScaffoldAudio}");
            
            // 确保音频状态被重置
            _cellItem.IsPlayAvatarAudio = false;
            _cellItem.IsPlayScaffoldAudio = false;
            _cellItem.AudioOver();
            //_cellItem.StopAudioSource();//全局唯一的清理
            
            base.Clear();
        }
        
        public override void OnInit()
        {
            base.OnInit();
            _cellItemId = _cell.GetHashCode();
            VFDebug.Log($"OnInit: _cell 哈希码 = {_cellItemId}");
            this.OnExploreClearScreen(string.Empty, null);
        }

        #region avatar

        /// <summary>
        /// 进入 entity 开始播放语音
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private async void OnEnterPlayAudio(string name, object body)
        {
            ExploreParam param = body as ExploreParam;
            this._cellItem.UpdateAudioSource();
            
            (this.GetOwner() as ExploreEntityBase).CurTtsAudioId = _cellItem.Data.firstRoundData.replyAudio.id;
            
            PB_Task_First_Round_Data firstData = _cellItem.Data.firstRoundData;

            PB_Explore_DialogAudioDownFrame avatarTTs = firstData.replyAudio;
            
            PB_Explore_DialogAudioDownFrame exampleTTs = firstData.exampleAudio;
            //avatar语音
            _model.AddStreamAudio(true, avatarTTs.audio.ToByteArray(), firstData.replyBubbleId, avatarTTs.id, true, avatarTTs,false);
            //脚手架语音
            _model.AddStreamAudio(false, exampleTTs.audio.ToByteArray(), firstData.exampleBubbleId, exampleTTs.id, true, exampleTTs,false);
      
            // 先加载音频，再设置播放状态
            AudioClip clip = await _model.GetAudioByRecordId(_cellItem.Data.firstRoundData.replyAudio.id);
            
            // 确保音频加载成功
            if (clip == null)
            {
                VFDebug.LogError("音频加载失败，不设置播放状态");
                return;
            }
            
            // 确保音频源可用
            AudioSource audioSource = GSoundManager.instance.CurAvatarAudioSource;
            if (audioSource == null)
            {
                VFDebug.LogError("音频源不可用，不设置播放状态");
                return;
            }
            
            // 设置音频源的音频片段
            audioSource.clip = clip;
            
            // 音频准备完毕，设置播放状态
            _cellItem.IsPlayAvatarAudio = true;
            
            // 播放音频
            GSoundManager.instance.PlayAvatarTTS(clip, _controller.GetAudioSpeed());
            
            PLAY_EXPLORE_AVATAR_FIRST_AUDIO dotData = new PLAY_EXPLORE_AVATAR_FIRST_AUDIO();
            dotData.task_id = _cellItem.Data.taskId;
            DataDotMgr.Collect(dotData);

            if (_controller.ModelDialogManager)
            {
                _controller.ModelDialogManager.EnterCharacterSpeakingState();
                _controller.ModelDialogManager.ProcessDialogue(firstData.emotionAnalysisResult.sentence_list.ToList());
            }
        }
        
        /// <summary>
        /// avatar 音频加载完毕
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private async void OnAvatarAudioPlay(string name, object body)
        {
            Notifier.instance.SendNotification(NotifyConsts.ExploreSoundStop);
            ExploreAudioPlayEffect effect = body as ExploreAudioPlayEffect;

            ulong audioId = effect.AudioId;
            
            try
            {
                // 内存压力检查
                if (IsMemoryLow())
                {
                    VFDebug.Log("检测到内存压力，清理音频缓存");
                    _model.ClearAllAudioCache();
                }
                
                AudioClip clip = await _model.GetAudioByRecordId(audioId);
                (this.GetOwner() as ExploreEntityBase).CurTtsAudioId = audioId;
                
                if (clip == null)
                {
                    VFDebug.LogError("avatar播放的音频加载失败：：：audioId：" + audioId);
                    _cellItem.AudioOver();
                    return;
                }

                if (effect.Effect && _controller.ModelDialogManager && _model.DialogDownMsgForAvatarReplyTTS != null)
                {
                    _controller.ModelDialogManager.EnterCharacterSpeakingState();
                    _controller.ModelDialogManager.ProcessDialogue(_model.DialogDownMsgForAvatarReplyTTS.emotionAnalysisResult.sentence_list.ToList());
                }

                GSoundManager.instance.PlayAvatarTTS(clip, _controller.GetAudioSpeed());
                _cellItem.IsPlayAvatarAudio = true;
            }
            catch (Exception ex)
            {
                VFDebug.LogError($"播放音频失败: {ex.Message}");
                _cellItem.AudioOver();
            }
        }
        
        /// <summary>
        /// 第一次显示 avatar cell
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private void OnShowFirstAvatarCell(string name, object body)
        {
            bool onlyShow = false;
            if (body != null)
            {
                onlyShow = (bool) body;
            }
            
            DoFirstAvatarCellInfo();
            if (onlyShow) return;

            if (_controller.GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage).IfMoveToExploreTab())
            {
                // 显示麦克风
                _controller.ShowRecordUI(!_controller.IsSeniorPlayer); //简单用户 自动出现脚手架
            }
            HIDE_TAB_BAR dat = new HIDE_TAB_BAR();
            dat.task_id = _cellItem.Data.taskId;
            dat.dialogue_id = _cellItem.LogicEntity.DialogId;
            dat.dialogue_round = _cellItem.LogicEntity.Round;
            DataDotMgr.Collect(dat);
            _controller.GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage).HideTabs(TabIndex.Explore);
        }

        private void DoFirstAvatarCellInfo()
        {
            _cellItem.AvatarItem.Com.com.visible = true;
            string txt = (this.GetOwner() as ExploreEntityBase).InitData.firstRoundData.replyText;
            // Debug.LogError($"avatar txt ::{txt}");
            
            string txtTranslate = (this.GetOwner() as ExploreEntityBase).InitData.firstRoundData.replyTranslateText;
            // Debug.LogError($"avatar replyTranslateText ::{txtTranslate}");
            
            _cellItem.AvatarItem.SetInfo(0,0);
            _cellItem.AvatarItem.ShowAvatarTxt((this.GetOwner() as ExploreEntityBase).InitData.firstRoundData.replyBubbleId,txt,txtTranslate);
            _cellItem.AvatarTranslate.SetInfo(0,0);
            _cellItem.AvatarTranslate.ShowTranslateTxt(txtTranslate);
        }

        /// <summary>
        /// 第一次显示 avatar Translate cell
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private void OnShowAvatarFristTranslateCell(string name, object body)
        {
            _cellItem.AvatarItem.AlphaToZore();
        }
        
        
        /// <summary>
        /// 显示 avatar cell
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private void OnShowAvatarCell(string name, object body)
        {
            _cellItem.PlayerItem.Com.com.visible = false;
            _cellItem.AvatarItem.Com.com.visible = true;
            _cellItem.SetBubbleLoadingVisible(false);
            string txt = _model.AvatarDialogInfo.replyText;
            string transformTxt = _model.AvatarDialogTranslate.replyTranslateText;
            // Debug.LogError($"avatar txt ::{txt} + _model.AvatarDialogTranslate：：{_model.AvatarDialogTranslate}");
            _cellItem.AvatarItem.SetInfo(_model.AvatarDialogInfo.commonData.dialogId,_model.AvatarDialogInfo.commonData.round);
            _cellItem.AvatarItem.ShowAvatarTxt(_model.AvatarDialogInfo.commonData.bubbleId,txt,transformTxt);
        }
  
        
        /// <summary>
        /// 收到服务器 avatar txt 数据
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private void OnAvatarTxt(string name, object body)
        {
            Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_avatar_cell_show);
        }
        
        private void OnAvatarTranslate(string name, object body)
        {
            string txt = _model.AvatarDialogTranslate.replyTranslateText;
            // Debug.LogError($"avatar replyTranslateText ::{txt}");
            _cellItem.AvatarTranslate.ShowTranslateTxt(txt);
        }
        #endregion

        #region player
        /// <summary>
        /// player 文本数据
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private void OnUserInfo(string name, object body)
        {
            SC_DialogDownMsgForASR userDialogInfo = _controller.Model.UserDialogInfo;
            // VFDebug.LogError($"bubbleId::userDialogInfo.commonData.round::{userDialogInfo.commonData.round}");
            this._cellItem.LogicEntity.Round = userDialogInfo.commonData.round;
            this._cellItem.LogicEntity.DialogId = userDialogInfo.commonData.dialogId;
            Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_player_cell_show);
        }
        
        private void OnShowPlayerCell(string name, object body)
        {
            _cellItem.PlayerItem.SetInfo(_controller.Model.UserDialogInfo.commonData.dialogId,_controller.Model.UserDialogInfo.commonData.round);
            _cellItem.PlayerItem.ShowPlayerTxt(_controller.Model.UserDialogInfo.text);
            _cellItem.PlayerItem.Com.com.visible = true;
            _cellItem.ScaffoldItem.Com.com.visible = false;
            _cellItem.AdviceItem.Com.com.visible = false;
            // Debug.LogError("显示Palyer");
        }
        
        private void OnHidePlayerCell(string name, object body)
        {
            // 改到 avatar 气泡出现再消失
            // _cellItem.PlayerItem.Com.com.visible = false;
            _cellItem.SetBubbleLoadingVisible(true);
            // Debug.LogError("隐藏Palyer");
        }

        #endregion

        #region 脚手架
        private string _scaffoldEffectTimeFlag = String.Empty;
        private void OnScaffoldShow(string name, object body)
        {
            bool isClick = (bool) body;
            _cellItem.PlayerItem.Com.com.visible = false;
            _cellItem.AdviceItem.SetEffectVisible(false,isClick);
            
            if (_cellItem.LogicEntity.Round <= 0)
            {
                _cellItem.ScaffoldItem.SetInfo(0,0);
                _cellItem.ScaffoldItem.ShowTxt(_cellItem.Data.firstRoundData.exampleBubbleId,_cellItem.Data.firstRoundData.exampleText,_cellItem.Data.firstRoundData.exampleTranslateText);
                _cellItem.ScaffoldItem.Show(_cellItem.Data.firstRoundData.exampleText,_cellItem.Data.taskId,0,_cellItem.LogicEntity.Round);
            }
            else
            {
                SC_DialogDownMsgForUserReplyExample scaffoldInfo = _controller.Model.ScaffoldInfo;
                if (scaffoldInfo == null) return; //说明这时候 脚手架 服务器还没有返回
                SC_DialogDownMsgForUserReplyExampleTranslate scaffoldTransInfo = _controller.Model.ScaffoldTranslate;

                if (scaffoldInfo.commonData.taskId == this._cellItem.LogicEntity.EntityId)
                {
                    // Debug.LogError($"bubbleId::scaffoldInfo.commonData.bubbleId:::{scaffoldInfo.commonData.bubbleId}");
                    _cellItem.ScaffoldItem.SetInfo(scaffoldInfo.commonData.dialogId,scaffoldInfo.commonData.round);
                    _cellItem.ScaffoldItem.ShowTxt(scaffoldInfo.commonData.bubbleId,scaffoldInfo.exampleText,scaffoldTransInfo.exampleTranslateText);
                    _cellItem.ScaffoldItem.Show(scaffoldInfo.exampleText,scaffoldInfo.commonData.taskId,scaffoldInfo.commonData.dialogId,_cellItem.LogicEntity.Round);
                }
            }
            if(_scaffoldEffectTimeFlag != String.Empty)
            TimerManager.instance.UnRegisterTimer(_scaffoldEffectTimeFlag);
            if (isClick)
            {
                _scaffoldEffectTimeFlag = TimerManager.instance.RegisterTimer((a) =>
                {
                    _cellItem.ScaffoldItem.SetEffectVisible(true,isClick);
                }, Mathf.FloorToInt(ExploreConst.CellAphla * 1000), 1);
            }
            else
            {
                _cellItem.ScaffoldItem.SetEffectVisible(true,isClick);
            }

        }
        
        private async void OnScaffoldAudioPlay(string name, object body)
        {
            Notifier.instance.SendNotification(NotifyConsts.ExploreSoundStop);
            ulong audioId = (ulong)body;

            try
            {
                // 内存压力检查
                if (IsMemoryLow())
                {
                    VFDebug.Log("检测到内存压力，清理音频缓存");
                    _model.ClearAllAudioCache();
                }
                
                AudioClip clip = await _model.GetAudioByRecordId(audioId);
                (this.GetOwner() as ExploreEntityBase).CurTtsAudioId = audioId;
                if (clip == null)
                {
                    VFDebug.LogError("scaffold播放的音频加载失败：：：audioId：" + audioId);
                    _cellItem.AudioOver();
                    return;
                }

                GSoundManager.instance.PlayTTS(clip, _controller.GetAudioSpeed());
                _cellItem.IsPlayScaffoldAudio = true;
            }
            catch (Exception ex)
            {
                VFDebug.LogError($"播放脚手架音频失败: {ex.Message}");
                _cellItem.AudioOver();
            }
        }
        
        #endregion
        
        #region active
        
        private string _activeEffectTimeFlag = String.Empty;
        private void OnScaffoldActive(string name, object body)
        {
            bool isClick = (bool) body;
            _cellItem.PlayerItem.Com.com.visible = false;
            _cellItem.ScaffoldItem.SetEffectVisible(false,isClick);
            
            if (_cellItem.LogicEntity.Round <= 0)
            {
                _cellItem.AdviceItem.SetInfo(0,0);
                _cellItem.AdviceItem.ShowTxt(_cellItem.Data.firstRoundData.adviceBubbleId,_cellItem.Data.firstRoundData.adviceText);
                _cellItem.AdviceItem.Show(_cellItem.Data.firstRoundData.exampleText,_cellItem.Data.taskId,0,_cellItem.LogicEntity.Round);
            }
            else
            {
                SC_DialogDownMsgForAdvice adviceInfo = _controller.Model.AdviceInfo;
                if (adviceInfo == null) return; 

                if (adviceInfo.commonData.taskId == this._cellItem.LogicEntity.EntityId)
                {
                    _cellItem.AdviceItem.SetInfo(adviceInfo.commonData.dialogId,adviceInfo.commonData.round);
                    _cellItem.AdviceItem.ShowTxt(adviceInfo.commonData.bubbleId,adviceInfo.adviceText);
                    _cellItem.AdviceItem.Show(adviceInfo.adviceText,adviceInfo.commonData.taskId,adviceInfo.commonData.dialogId,_cellItem.LogicEntity.Round);
                }
            }
            
            if(_activeEffectTimeFlag != String.Empty)
            TimerManager.instance.UnRegisterTimer(_activeEffectTimeFlag);
            if (isClick)
            {
                _activeEffectTimeFlag = TimerManager.instance.RegisterTimer((a) =>
                {
                    _cellItem.AdviceItem.SetEffectVisible(true,isClick);
                }, Mathf.FloorToInt(ExploreConst.CellAphla * 1000), 1);
            }
            else
            {
                _cellItem.AdviceItem.SetEffectVisible(true,isClick);
            }
        }
        
        #endregion
        
        /// <summary>
        /// 奖励显示
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private void OnAwardShow(string name, object body)
        {
            _cellItem.AwardPanel.UpdateData();
        }

        // 简单的内存压力检测  TODO 重新处理 解决AOT + 映射java代码问题
        private bool IsMemoryLow()
        {
            
#if UNITY_ANDROID && !UNITY_EDITOR
            // Android 平台下检测内存情况
            try {
                // 获取当前应用可用内存
                long availableMemory = GetAndroidAvailableMemory();
                Debug.Log("当前android可用内存：" + availableMemory);
                return availableMemory < 600 * 1024 * 1024;
            } catch (Exception e) {
                VFDebug.LogError($"内存检测异常: {e.Message}");
                return false;
            }
#elif UNITY_IOS && !UNITY_EDITOR
            // iOS 平台下检测内存情况
            return SystemInfo.systemMemorySize < 600; // 少于600MB可用内存
#else
            // 其他平台或编辑器模式
            return false;
#endif
        }
        
        private long GetAndroidAvailableMemory()
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            using (var unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))
            using (var currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity"))
            using (var activityService = currentActivity.Call<AndroidJavaObject>("getSystemService", "activity"))
            using (var memoryInfo = new AndroidJavaObject("android.app.ActivityManager$MemoryInfo"))
            {
                activityService.Call("getMemoryInfo", memoryInfo);
                return memoryInfo.Get<long>("availMem");
            }
#else
            return 0;
#endif
        }
    }
    
using CommonUI;
using FairyGUI;
using FairyGUI.Utils;
using Msg.question;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using System;
using System.Collections.Generic;
using UnityEngine;
using static ScriptsHot.Game.Modules.FragmentPractice.FragmentPracticeUI;

namespace UIBind.FragmentPractice
{
    public partial class EssayQuestion : AFragQuestion
    {
        TextFieldExtension TfQuestion => tfQuestion as TextFieldExtension;
        
        override protected void OnAddedToStage()
        {
            TfQuestion.SetNewFont(FontCfg.DinNextRegular, new Color(17f / 255f, 17f / 255f, 17f / 255f, 1), 36);
            TfQuestion.TextGtf.textFormat.lineSpacing = 28;
        }

        override protected void OnRemovedFromStage()
        {
        }
        
        #region refresh
        override public void ShowPractice(AFragAnswer answerComp)
        {
            var playAudio = Practice.AudioId != 0;
            btnPlayAudio.AudioId = Practice.AudioId;
            audio.selectedPage = playAudio ? "visible" : "invisible";
            
            if (playAudio && IsCurrent)
            {
                btnPlayAudio.Play();
            }

            FillQuestionText();
        }
        
        
        private void FillQuestionText()
        {
            TextFieldExtension textExt = TfQuestion;
            textExt.Reset();
            textExt.onTranslated += (word, translation) =>
            {
                DotPracticeManager.Instance.Collect(new DataDot_Translate(word, translation));
            };
            textExt.StartNewWordTrans();
            List<RichContent> contents = new List<RichContent>();

            contents = RichContentUtil.SplitPracticeWords(Practice.GetStem(), Practice.QuestionType);

            foreach (var richContent in contents)
                textExt.AppendContent(richContent);

            textExt.TextGtf.textFormat.font = FontCfg.DinNextRegular;
            textExt.Display(ShowMode.QuestionNormal);
            textExt.AddDot(contents, true);
            textExt.ResizeToFit();
        }

        #endregion 
    }
}
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Animations;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace AnimationSystem
{
    /// <summary>
    /// 简化版动画测试组件，专注于测试动作组播放和融合功能
    /// </summary>
    public class SimpleAnimTest : MonoBehaviour
    {
        [Serializable]
        public class AnimInfoConfig
        {
            public AnimationClip clip;
            
            [Header("混合设置")]
            [Tooltip("是否支持混合")]
            public bool canBlend = true;
            [Tooltip("最早可接受混合的时间点（动画开始后的百分比）")]
            [Range(0, 1)]
            public float blendInPercent = 0.25f;
            [Tooltip("最晚可接受混合的时间点（动画结束前的百分比）")]
            [Range(0, 1)]
            public float blendOutPercent = 0.25f;
            
            [Header("循环设置")]
            [Tooltip("是否支持循环播放")]
            public bool canLoop = true;
            [Tooltip("最小循环倍率（1.0表示播放一次，不循环）")]
            [Range(1.0f, 10.0f)]
            public float minLoopMultiplier = 1.0f;
            [Tooltip("最大循环倍率（例如2.0表示最多播放两次）")]
            [Range(1.0f, 10.0f)]
            public float maxLoopMultiplier = 3.0f;
            [Tooltip("循环时的融合时间（秒）")]
            [Range(0.0f, 1.0f)]
            public float loopBlendTime = 0.2f;
            
            [Header("速度设置")]
            [Tooltip("是否支持变速")]
            public bool canChangeSpeed = true;
            [Tooltip("最小播放速度")]
            [Range(0.1f, 2.0f)]
            public float minSpeedMultiplier = 0.8f;
            [Tooltip("最大播放速度")]
            [Range(0.1f, 2.0f)]
            public float maxSpeedMultiplier = 1.2f;
            
            [Header("IK设置")]
            [Tooltip("是否启用脚部IK")]
            public bool enableFootIK = false;
        }

        [Serializable]
        public class AnimGroupConfig
        {
            public string groupName;
            public AnimInfoConfig[] animInfos;
            [Tooltip("期望的动作组播放时长（秒）。设为0则使用自然时长")]
            public float expectedDuration = 0f;
        }

        [Header("初始化信息配置")]
        [SerializeField] private Animator _characterAnimator;
        public Animator characterAnimator
        {
            get { return _characterAnimator; }
            set 
            { 
                if (_characterAnimator != value)
                {
                    _characterAnimator = value;
                    // 如果动画管理器已初始化，则更新Animator引用
                    if (animManager != null && animManager.IsInitialized())
                    {
                        Debug.Log($"Animator已更改: {(value != null ? value.name : "null")}");
                        animManager.SetAnimator(value);
                    }
                }
            }
        }
        
        [SerializeField] private Animator _emotionAnimator; // 表情动画控制器
        [SerializeField] private SkinnedMeshRenderer _emotionSkinnedMeshRenderer; // 表情网格渲染器
        [SerializeField] private AvatarAnimationDataObject _emotionAnimationDataObject;
        
        public Animator emotionAnimator
        {
            get { return _emotionAnimator; }
            set 
            { 
                if (_emotionAnimator != value)
                {
                    _emotionAnimator = value;
                    // 如果动画管理器已初始化，则更新表情Animator引用
                    if (animManager != null && animManager.IsInitialized())
                    {
                        Debug.Log($"表情Animator已更改: {(value != null ? value.name : "null")}");
                        // 使用新添加的方法
                        animManager.SetEmotionAnimator(value);
                    }
                }
            }
        }
        
        public SkinnedMeshRenderer emotionSkinnedMeshRenderer
        {
            get { return _emotionSkinnedMeshRenderer; }
            set 
            { 
                if (_emotionSkinnedMeshRenderer != value)
                {
                    _emotionSkinnedMeshRenderer = value;
                    // 如果动画管理器已初始化，则更新表情网格渲染器引用
                    if (animManager != null && animManager.IsInitialized())
                    {
                        Debug.Log($"表情网格渲染器已更改: {(value != null ? value.name : "null")}");
                        // 使用新添加的方法
                        animManager.SetEmotionSkinnedMeshRenderer(value);
                    }
                }
            }
        }
        
        [Header("遮罩设置")]
        [SerializeField] private AvatarMask headMask; // 头部遮罩
        [SerializeField] private AvatarMask bodyMask; // 身体遮罩

        [FormerlySerializedAs("animGroupConfigs")] [Header("动作集合配置")]
        public AnimGroupConfig[] actionGroupConfigs = new AnimGroupConfig[3];

        [Header("过渡设置")]
        public float transitionTime = 0.2f;

        [Header("UI控制")]
        public Button playGroup1Button;
        public Button playGroup2Button;
        public Button playGroup3Button;
        public Button stopButton;
        public Button initButton; // 初始化按钮
        
        [Header("头部控制")]
        public Button startRandomMoveButton; // 启动随机移动按钮
        public Button noddingButton; // 点头按钮
        public Button shakingButton; // 摇头按钮
        public Button customRotateButton; // 自定义旋转按钮
        public Button lookAtButton; // 看向方向按钮
        public Button cancelLookAtButton; // 取消看向按钮
        public Button blinkButton; // 眨眼按钮
        
        [Tooltip("点头频率 (Hz)")]
        [Range(0.1f, 6.0f)]
        public float noddingSpeed = 1.0f;
        
        [Tooltip("点头次数")]
        [Range(1, 10)]
        public int noddingCount = 3;
        
        [Tooltip("摇头频率 (Hz)")]
        [Range(0.1f, 6.0f)]
        public float shakingSpeed = 1.0f;
        
        [Tooltip("摇头次数")]
        [Range(1, 10)]
        public int shakingCount = 3;
        
        [Tooltip("自定义旋转角度")]
        public Vector3 customRotation = new Vector3(15f, 0f, 0f);
        
        [Tooltip("自定义旋转过渡时间 (秒)")]
        [Range(0.1f, 3.0f)]
        public float customRotationDuration = 1.0f;
        
        [Header("看向设置")]
        [Tooltip("看向方向")]
        public Vector3 lookAtDirection = new Vector3(0f, 30f, 0f);
        
        [Tooltip("看向过渡时间 (秒)")]
        [Range(0.1f, 3.0f)]
        public float lookAtTransitionDuration = 0.5f;
        
        [Tooltip("看向持续时间 (秒)")]
        [Range(0.5f, 10.0f)]
        public float lookAtHoldDuration = 2.0f;

        // 贝塞尔曲线设置
        [Header("贝塞尔曲线设置")]
        public Button naturalCurveButton;  // 自然曲线按钮
        public Button defaultCurveButton;  // 默认曲线按钮

        [Header("Excel配置")]
        [Tooltip("Excel中配置的动作组ID")]
        public string excelAnimGroupId;
        [Tooltip("加载Excel配置的按钮")]
        public Button loadExcelButton;

        [Header("眨眼设置")]
        [Tooltip("眨眼持续时间（秒）")]
        [Range(0.1f, 1.0f)]
        public float blinkDuration = 0.2f;
        
        [Tooltip("眨眼最小间隔（秒）")]
        [Range(1.0f, 10.0f)]
        public float blinkMinInterval = 2.0f;
        
        [Tooltip("眨眼最大间隔（秒）")]
        [Range(1.0f, 20.0f)]
        public float blinkMaxInterval = 8.0f;
        
        [Tooltip("是否启用自动眨眼")]
        public bool autoBlinkEnabled = true;

        [Header("表情设置")]
        [Tooltip("表情动画片段数组")]
        [SerializeField] private AnimationClip[] emotionClips;

        [Tooltip("表情播放按钮数组")]
        [SerializeField] private Button[] emotionButtons;

        public AnimationAvatarManager animManager;
        private AnimGroup[] animGroups = new AnimGroup[3];
        private string[] groupNames = new string[3]; // 存储动作组名称
        private int defaultGroupIndex = 0;
        private SimpleAnimStateImpl animState;
        private bool isInitialized = false;

        // 生命周期方法
        private void Awake()
        {
            // 找到AnimationAvatarManager组件
            animManager = GetComponentInChildren<AnimationAvatarManager>();
            if (animManager == null)
            {
                // 如果没有找到，创建一个
                // GameObject managerObj = new GameObject("AnimationManager");
                // managerObj.transform.SetParent(transform);
                animManager = this.gameObject.AddComponent<AnimationAvatarManager>();
            }
            
            // 初始化动画组和名称数组
            animGroups = new AnimGroup[actionGroupConfigs.Length];
            groupNames = new string[actionGroupConfigs.Length];
            
            // 设置按钮监听
            if (initButton != null) initButton.onClick.AddListener(InitializeAnimSystem);
            
            // 设置播放/停止按钮监听
            if (playGroup1Button != null) playGroup1Button.onClick.AddListener(() => PlayAnimGroup(0));
            if (playGroup2Button != null) playGroup2Button.onClick.AddListener(() => PlayAnimGroup(1));
            if (playGroup3Button != null) playGroup3Button.onClick.AddListener(() => PlayAnimGroup(2));
            if (stopButton != null) stopButton.onClick.AddListener(StopAnimation);
            
            // 设置Excel加载按钮监听
            if (loadExcelButton != null) loadExcelButton.onClick.AddListener(LoadExcelAnimGroup);
            
            // 初始时禁用播放按钮，直到系统初始化完成
            SetPlayButtonsInteractable(false);
            SetHeadControlButtonsInteractable(false);
            if (loadExcelButton != null) loadExcelButton.interactable = false;
        }
        
        private void Start()
        {
            // 初始化UI
            InitUI();
        }
        
        // 初始化动画系统
        public void InitializeAnimSystem()
        {
            if (animManager == null)
            {
                Debug.LogError("AnimationAvatarManager不存在，无法初始化");
                return;
            }

            // 调用初始化方法
            bool initSuccess;
            
            if (!animManager.IsInitialized())
            {
                // 设置必要参数
                animManager.SetAnimator(characterAnimator);
                animManager.SetEmotionAnimator(emotionAnimator);
                animManager.SetEmotionSkinnedMeshRenderer(emotionSkinnedMeshRenderer);
            
                // 如果没有设置遮罩，则自动创建
                if (headMask == null)
                {
                    Debug.Log("头部遮罩未设置，使用默认头部遮罩");
                    headMask = AvatarMaskHelper.CreateHeadMask();
                }
            
                if (bodyMask == null)
                {
                    Debug.Log("身体遮罩未设置，使用默认身体遮罩");
                    bodyMask = AvatarMaskHelper.CreateBodyMask();
                }
            
                animManager.SetHeadMask(headMask);
                animManager.SetBodyMask(bodyMask);
            
                animManager.SetAnimDataObject(this._emotionAnimationDataObject);
                
                initSuccess = animManager.InitializePlayableGraph();
            }
            else
            {
                initSuccess = true;
            }
            
            if (initSuccess)
            {
                Debug.Log("动画系统初始化成功");
                
                // 设置眨眼参数
                animManager.SetBlinkParameters(blinkDuration, blinkMinInterval, blinkMaxInterval);
                animManager.SetAutoBlinkEnabled(autoBlinkEnabled);
                
                // 初始化动画组
                CreateAnimGroups();
                
                // 创建并设置动画状态
                SetupAnimState();
                
                // 更新UI状态
                if (initButton != null) initButton.interactable = false;
                SetPlayButtonsInteractable(true);
                SetHeadControlButtonsInteractable(true);
                
                isInitialized = true;
            }
            else
            {
                Debug.LogError("动画系统初始化失败，请检查参数设置");
                
                // 初始化按钮保持可用
                if (initButton != null) initButton.interactable = true;
                SetPlayButtonsInteractable(false);
                SetHeadControlButtonsInteractable(false);
            }
        }
        
        // 创建动画组
        private void CreateAnimGroups()
        {
            for (int i = 0; i < actionGroupConfigs.Length; i++)
            {
                if (actionGroupConfigs[i] != null && actionGroupConfigs[i].animInfos != null && actionGroupConfigs[i].animInfos.Length > 0)
                {
                    animGroups[i] = CreateAnimGroup(actionGroupConfigs[i]);
                    groupNames[i] = actionGroupConfigs[i].groupName; // 存储动作组名称
                    animGroups[i].Init(); // 初始化动作组
                }
                }
            }

        // 设置动画状态
        private void SetupAnimState()
        {
            animState = new SimpleAnimStateImpl();
            animState.manager = animManager;
            if (animGroups[defaultGroupIndex] != null)
            {
                // 确保默认动画组被正确初始化
                animGroups[defaultGroupIndex].Init();
                animState.defaultAnimGroup = animGroups[defaultGroupIndex];
            }
            animManager.SetState(animState);
        }
        
        // 设置播放按钮是否可交互
        private void SetPlayButtonsInteractable(bool interactable)
        {
            if (playGroup1Button != null) playGroup1Button.interactable = interactable;
            if (playGroup2Button != null) playGroup2Button.interactable = interactable;
            if (playGroup3Button != null) playGroup3Button.interactable = interactable;
            if (stopButton != null) stopButton.interactable = interactable;
            if (loadExcelButton != null) loadExcelButton.interactable = interactable;
        }

        private AnimGroup CreateAnimGroup(AnimGroupConfig config)
        {
            AnimGroup group = new AnimGroup();

            foreach (var infoConfig in config.animInfos)
            {
                if (infoConfig.clip != null)
                {
                    AnimInfo animInfo = new AnimInfo();
                    animInfo.clip = infoConfig.clip;
                    
                    // 设置混合参数
                    animInfo.canBlend = infoConfig.canBlend;
                    animInfo.blendRange = new Vector2(infoConfig.blendInPercent, infoConfig.blendOutPercent);
                    
                    // 设置循环参数
                    animInfo.canLoop = infoConfig.canLoop;
                    animInfo.loopMultiplierRange = new Vector2(infoConfig.minLoopMultiplier, infoConfig.maxLoopMultiplier);
                    animInfo.loopBlendTime = infoConfig.loopBlendTime;
                    
                    // 设置速度参数
                    animInfo.canChangeSpeed = infoConfig.canChangeSpeed;
                    animInfo.speedMultiplierRange = new Vector2(infoConfig.minSpeedMultiplier, infoConfig.maxSpeedMultiplier);

                    // 设置IK参数
                    animInfo.enableFootIK = infoConfig.enableFootIK;

                    group.AddAnimInfo(animInfo);
                }
            }

            return group;
        }

        public void PlayAnimGroup(int groupIndex)
        {
            if (!isInitialized)
            {
                Debug.LogWarning("动画系统未初始化，无法播放动画");
                return;
            }
            
            if (groupIndex >= 0 && groupIndex < animGroups.Length && animGroups[groupIndex] != null)
            {
                Debug.Log($"播放动作组: {groupNames[groupIndex]}");
                
                // 获取期望的播放时长
                float duration = GetExpectedDuration(groupIndex);
                
                // 播放动作组，永远传入 false 作为 isDefault
                AnimGroupPlayData groupPlayData = animGroups[groupIndex].GetAnimStrategy(duration);
                animManager.PlayAnimGroupData(groupPlayData, false);
            }
            else
            {
                Debug.LogWarning($"动作组 {groupIndex} 不存在或未配置");
            }
        }

        public void StopAnimation()
        {
            if (!isInitialized)
            {
                Debug.LogWarning("动画系统未初始化，无法停止动画");
                return;
            }
            
            // 使用 animState 的 PlayDefaultAnimation
            animState.PlayDefaultAnimation();
        }

        // 获取动作组期望的播放时长
        private float GetExpectedDuration(int groupIndex)
        {
            // 如果配置了期望时长且大于0，则使用配置的时长
            if (actionGroupConfigs[groupIndex].expectedDuration > 0)
            {
                return actionGroupConfigs[groupIndex].expectedDuration;
            }
            
            // 否则计算动作组的自然时长（所有片段长度之和）
            float naturalDuration = 0f;
            foreach (var animInfo in animGroups[groupIndex].GetAnimInfos())
            {
                naturalDuration += animInfo.clip.length;
            }
            
            return naturalDuration > 0 ? naturalDuration : 3.0f; // 默认至少3秒
        }

        // 添加OnValidate方法，用于在编辑器中修改属性时触发
        private void OnValidate()
        {
            // 如果在运行时修改了Animator引用，确保调用SetAnimator
            if (Application.isPlaying && animManager != null && animManager.IsInitialized())
            {
                if (_characterAnimator != null)
            {
                animManager.SetAnimator(_characterAnimator);
                }
                
                if (_emotionAnimator != null)
                {
                    animManager.SetEmotionAnimator(_emotionAnimator);
                }
                
                if (_emotionSkinnedMeshRenderer != null)
                {
                    animManager.SetEmotionSkinnedMeshRenderer(_emotionSkinnedMeshRenderer);
                }
                
                // 如果眨眼参数被修改，更新眨眼设置
                animManager.SetBlinkParameters(blinkDuration, blinkMinInterval, blinkMaxInterval);
                animManager.SetAutoBlinkEnabled(autoBlinkEnabled);

                // 验证表情相关字段
                if (emotionButtons != null && emotionClips != null)
                {
                    // 确保按钮数量与动画片段数量匹配
                    if (emotionButtons.Length != emotionClips.Length)
                    {
                        Debug.LogWarning("表情按钮数量与动画片段数量不匹配");
                    }
                }
            }
        }
        
        // 启动随机移动模式
        public void StartRandomHead()
        {
            if (!isInitialized || animManager == null)
            {
                Debug.LogWarning("动画系统未初始化，请先初始化");
                return;
            }
            
            animManager.StartRandomMove();
        }
        
        // 点头动作
        public void PerformNodding()
        {
            if (!isInitialized || animManager == null)
            {
                Debug.LogWarning("动画系统未初始化，请先初始化");
                return;
            }
            
            animManager.PerformHeadNodding(noddingSpeed, noddingCount);
        }
        
        // 摇头动作
        public void PerformShaking()
        {
            if (!isInitialized || animManager == null)
            {
                Debug.LogWarning("动画系统未初始化，请先初始化");
                return;
            }
            
            animManager.PerformHeadShaking(shakingSpeed, shakingCount);
        }
        
        // 自定义旋转
        public void PerformCustomRotate()
        {
            if (!isInitialized || animManager == null)
            {
                Debug.LogWarning("动画系统未初始化，请先初始化");
                return;
            }
            
            animManager.SetHeadCustomRotation(customRotation, customRotationDuration);
        }

        // 设置头部控制按钮是否可交互
        private void SetHeadControlButtonsInteractable(bool interactable)
        {
            if (startRandomMoveButton != null) startRandomMoveButton.interactable = interactable;
            if (noddingButton != null) noddingButton.interactable = interactable;
            if (shakingButton != null) shakingButton.interactable = interactable;
            if (customRotateButton != null) customRotateButton.interactable = interactable;
            if (lookAtButton != null) lookAtButton.interactable = interactable;
            if (cancelLookAtButton != null) cancelLookAtButton.interactable = interactable;
            if (blinkButton != null) blinkButton.interactable = interactable;
        }

        // 执行看向动作
        public void PerformLookAt()
        {
            if (!isInitialized || animManager == null)
            {
                Debug.LogWarning("动画系统未初始化，请先初始化");
                return;
            }
            
            animManager.LookAtDirection(lookAtDirection, lookAtTransitionDuration, lookAtHoldDuration);
            Debug.Log($"执行看向动作: 方向={lookAtDirection}, 过渡时间={lookAtTransitionDuration}秒, 持续时间={lookAtHoldDuration}秒");
        }

        public void QuickResetHeadRotation()
        {
            if (animManager == null || !animManager.IsInitialized()) return;
            
            // 调用Avatar管理器中的头部快速重置方法
            animManager.QuickResetHeadRotation();
            
            // 更新UI按钮状态
            SetHeadControlButtonsInteractable(true);
        }

        // 初始化UI
        private void InitUI()
        {
            // 设置头部控制按钮事件
            if (startRandomMoveButton != null) startRandomMoveButton.onClick.AddListener(StartRandomHead);
            if (noddingButton != null) noddingButton.onClick.AddListener(PerformNodding);
            if (shakingButton != null) shakingButton.onClick.AddListener(PerformShaking);
            if (customRotateButton != null) customRotateButton.onClick.AddListener(PerformCustomRotate);
            if (lookAtButton != null) lookAtButton.onClick.AddListener(PerformLookAt);
            if (cancelLookAtButton != null) cancelLookAtButton.onClick.AddListener(QuickResetHeadRotation);
            if (blinkButton != null) blinkButton.onClick.AddListener(TriggerBlink);
            
            // 设置表情按钮事件
            if (emotionButtons != null && emotionClips != null)
            {
                for (int i = 0; i < emotionButtons.Length; i++)
                {
                    int index = i; // 创建局部变量以在lambda中使用
                    emotionButtons[i].onClick.AddListener(() => PlayEmotionAnimation(index));
                }
            }
            
            // 贝塞尔曲线按钮事件
            if (naturalCurveButton != null) naturalCurveButton.onClick.AddListener(SetNaturalCurve);
            if (defaultCurveButton != null) defaultCurveButton.onClick.AddListener(SetDefaultCurve);
            
            // 初始化Body控制按钮
            // ... existing code ...
        }
        
        // 设置自然曲线
        public void SetNaturalCurve()
        {
            if (!isInitialized || animManager == null)
            {
                Debug.LogWarning("动画系统未初始化，请先初始化");
                return;
            }
            
            animManager.SetHeadBezierCurveType(BezierCurveType.Natural);
            Debug.Log("设置头部旋转为自然曲线");
        }
        
        // 设置默认曲线
        public void SetDefaultCurve()
        {
            if (!isInitialized || animManager == null)
            {
                Debug.LogWarning("动画系统未初始化，请先初始化");
                return;
            }
            
            animManager.SetHeadBezierCurveType(BezierCurveType.Default);
            Debug.Log("设置头部旋转为默认曲线");
        }

        // 加载Excel配置的动作组
        private void LoadExcelAnimGroup()
        {
            if (!isInitialized)
            {
                Debug.LogWarning("动画系统未初始化，无法加载Excel配置");
                return;
            }

            if (string.IsNullOrEmpty(excelAnimGroupId))
            {
                Debug.LogError("Excel动作组ID未设置");
                return;
            }

            // 从Excel加载动作组
            AnimGroup excelGroup = Util.LoadAnimGroup(excelAnimGroupId,1);
            if (excelGroup == null)
            {
                Debug.LogError($"无法从Excel加载动作组: {excelAnimGroupId}");
                return;
            }

            // 创建新的AnimGroupConfig
            AnimGroupConfig newConfig = new AnimGroupConfig();
            newConfig.groupName = excelAnimGroupId;
            newConfig.animInfos = new AnimInfoConfig[excelGroup.GetAnimInfos().Count];
            
            // 将Excel中的AnimInfo转换为AnimInfoConfig
            int index = 0;
            foreach (var animInfo in excelGroup.GetAnimInfos())
            {
                AnimInfoConfig config = new AnimInfoConfig();
                config.clip = animInfo.clip;
                config.canBlend = animInfo.canBlend;
                config.blendInPercent = animInfo.blendRange.x;
                config.blendOutPercent = animInfo.blendRange.y;
                config.canLoop = animInfo.canLoop;
                config.minLoopMultiplier = animInfo.loopMultiplierRange.x;
                config.maxLoopMultiplier = animInfo.loopMultiplierRange.y;
                config.loopBlendTime = animInfo.loopBlendTime;
                config.canChangeSpeed = animInfo.canChangeSpeed;
                config.minSpeedMultiplier = animInfo.speedMultiplierRange.x;
                config.maxSpeedMultiplier = animInfo.speedMultiplierRange.y;
                config.enableFootIK = animInfo.enableFootIK;
                
                newConfig.animInfos[index++] = config;
            }

            // 替换动作组3的配置
            actionGroupConfigs[2] = newConfig;
            
            // 计算并设置默认播放时长为动作组的原始时长
            float naturalDuration = 0f;
            foreach (var animInfo in excelGroup.GetAnimInfos())
            {
                naturalDuration += animInfo.clip.length;
            }
            newConfig.expectedDuration = naturalDuration;
            
            animGroups[2] = CreateAnimGroup(newConfig);
            animGroups[2].Init();
            
            Debug.Log($"成功加载Excel动作组: {excelAnimGroupId}, 默认播放时长: {naturalDuration}秒");
        }
        
        /// <summary>
        /// 设置自动眨眼状态
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void SetAutoBlinkEnabled(bool enabled)
        {
            if (!isInitialized || animManager == null)
            {
                Debug.LogWarning("动画系统未初始化，无法设置自动眨眼状态");
                return;
            }
            
            autoBlinkEnabled = enabled;
            animManager.SetAutoBlinkEnabled(enabled);
            Debug.Log($"设置自动眨眼状态: {(enabled ? "启用" : "禁用")}");
        }
        
        /// <summary>
        /// 设置眨眼参数
        /// </summary>
        public void SetBlinkParameters()
        {
            if (!isInitialized || animManager == null)
            {
                Debug.LogWarning("动画系统未初始化，无法设置眨眼参数");
                return;
            }
            
            animManager.SetBlinkParameters(blinkDuration, blinkMinInterval, blinkMaxInterval);
            Debug.Log($"设置眨眼参数: 持续时间={blinkDuration}秒, 最小间隔={blinkMinInterval}秒, 最大间隔={blinkMaxInterval}秒");
        }
        
        /// <summary>
        /// 触发眨眼动画
        /// </summary>
        public void TriggerBlink()
        {
            if (!isInitialized)
            {
                Debug.LogWarning("动画系统未初始化，无法触发眨眼");
                return;
            }
            
            if (animManager != null)
            {
                Debug.Log("触发眨眼动画");
                animManager.TriggerBlink();
            }
        }

        /// <summary>
        /// 播放指定索引的表情动画
        /// </summary>
        /// <param name="index">表情动画索引</param>
        public void PlayEmotionAnimation(int index)
        {
            if (!isInitialized || animManager == null)
            {
                Debug.LogWarning("动画系统未初始化，无法播放表情动画");
                return;
            }
            
            if (emotionClips == null || index < 0 || index >= emotionClips.Length)
            {
                Debug.LogWarning($"无效的表情动画索引: {index}");
                return;
            }
            
            animManager.PlayEmotionAnimation(emotionClips[index]);
            Debug.Log($"播放表情动画: {emotionClips[index].name}");
        }
    }

    // 简单的AnimState实现
    public class SimpleAnimStateImpl : AnimState
    {
        public override void OnUpdate()
        {
            // 简单实现，不需要特殊逻辑
        }

        public override void OnEnter()
        {
            base.OnEnter();
            Debug.Log("SimpleAnimStateImpl.OnEnter: 播放默认动画");
        }

        public override void OnExit()
        {
            Debug.Log("SimpleAnimStateImpl.OnExit: 退出状态");
        }
    }
} 
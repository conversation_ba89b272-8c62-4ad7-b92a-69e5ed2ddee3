/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Debugger
{
    public partial class DebugPracticePanel : UIBindT
    {
        public override string pkgName => "Debugger";
        public override string comName => "DebugPracticePanel";

        public GComponent imgBG;
        public GComponent practice;
        public GButton btnClose;
        public GGraph btnLevel;
        public GGraph btnBook;
        public GComponent checker;
        public GTextField tfQuestionInfo;
        public ComboId cbId;
        public GTextInput tfQid;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            imgBG = (GComponent)com.GetChildAt(0);
            practice = (GComponent)com.GetChildAt(1);
            btnClose = (GButton)com.GetChildAt(3);
            btnLevel = (GGraph)com.GetChildAt(4);
            btnBook = (GGraph)com.GetChildAt(6);
            checker = (GComponent)com.GetChildAt(8);
            tfQuestionInfo = (GTextField)com.GetChildAt(9);
            cbId = (ComboId)com.GetChildAt(10);
            tfQid = (GTextInput)com.GetChildAt(11);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            imgBG = null;
            practice = null;
            btnClose = null;
            btnLevel = null;
            btnBook = null;
            checker = null;
            tfQuestionInfo = null;
            cbId = null;
            tfQid = null;
        }
    }
}
// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/basic/code.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.basic {

  /// <summary>Holder for reflection information generated from protobuf/basic/code.proto</summary>
  public static partial class CodeReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/basic/code.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static CodeReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Chlwcm90b2J1Zi9iYXNpYy9jb2RlLnByb3RvKsgNCgdQQl9Db2RlEgoKBk5v",
            "cm1hbBAAEhgKFENyZWF0ZURpYWxvZ1Rhc2tGYWlsEAESEQoNTXNnSGFuZGxl",
            "RmFpbBACEhIKDlBhcmFtQ2hlY2tGYWlsEAMSGQoVRGlhbG9nQ3JlYXRlQ2Fj",
            "aGVGYWlsEAQSFQoRRGlhbG9nTW9kZUlsbGVnYWwQBRISCg5FeGl0RGlhbG9n",
            "RmFpbBAGEhQKEEdldE5leHRSb3VuZEZhaWwQBxIQCgxGbG93SGVscEZhaWwQ",
            "CBILCgdUdHNGYWlsEAkSFgoSQ2hhbmdlTWFya0luZm9GYWlsEAoSFgoSR2V0",
            "Q29pbkJhbGFuY2VGYWlsEAsSEwoPRmVlZGJhY2tSZXFGYWlsEAwSFgoSQ2hh",
            "dEdwdFJpc2tDb250cm9sEA0SFwoTQnV5RGlhbG9nVGlja2V0RmFpbBAOEhcK",
            "E0RpYWxvZ1RpY2tldE5vRXhpc3QQDxIbChdHZXRJblByb2dyZXNzRGlhbG9n",
            "RmFpbBAQEhcKE0dldEVjb25vbWljSW5mb0ZhaWwQERISCg5Ob3RFbm91Z2hB",
            "c3NldBASEhcKE0dldFVzZXJHb2FsSW5mb0ZhaWwQExIfChtHZXRDaGFwdGVy",
            "UmVjb21tZW5kTGlzdEZhaWwQFBIaChZHZXRVc2VyQ2hhcHRlckluZm9GYWls",
            "EBUSFQoRU2VsZWN0Q2hhcHRlckZhaWwQFhIYChRHZXRDaGFwdGVyUmV3YXJk",
            "RmFpbBAXEh4KGkdldENoYXB0ZXJQcm9ncmVzc0luZm9GYWlsEBgSHAoYR2V0",
            "TGVhcm5QYXRoUGFnZVR5cGVGYWlsEBkSHAoYTm90aWZ5QW5pbWF0aW9uU3Rh",
            "dGVGYWlsEBoSFAoQQ3JlYXRlRGlhbG9nRmFpbBAbEhkKFVNlbmREaWFsb2dD",
            "b250ZW50RmFpbBAcEhIKDkRpYWxvZ0hlbHBGYWlsEB0SGQoVUXVlcnlEaWFs",
            "b2dSZWNvcmRGYWlsEB4SGQoVR2V0RGlhbG9nU2NhZmZvbGRGYWlsEB8SIQod",
            "R2V0QXZhdGFyTGlzdEJ5RGlhbG9nTW9kZUZhaWwQIBIYChREaWFsb2dTdWdn",
            "ZXN0aW9uRmFpbBAhEhMKD0dldE1vZGVJbmZvRmFpbBAiEhkKFUdldFVzZXJH",
            "b2FsRGV0YWlsRmFpbBAjEhcKE0xlYXJuUGF0aFJld2FyZEZhaWwQJBIXChNE",
            "aWFsb2dUcmFuc2xhdGVGYWlsECUSGQoVR2V0QXZhdGFyVGFza0luZm9GYWls",
            "ECYSGAoUR2V0U3RhcnRQYWdlSW5mb0ZhaWwQJxIaChZTZXRVc2VyQXNzaXN0",
            "TGV2ZWxGYWlsECgSGgoWUXVpY2tQcmFjdGljZVVuaXRFbXB0eRApEhgKFFF1",
            "aWNrUHJhY3RpY2VVbml0RW5kECoSFwoTR2V0VXNlckNvbnRhY3RzRmFpbBAr",
            "Eh0KGUF2YXRhckZhdm9yaXRlU2V0dGluZ0ZhaWwQLBIXChNHZXRVc2VyR29h",
            "bE5vZGVGYWlsEC0SFAoQRGVsTGVhcm5QYXRoRmFpbBAuEhUKEU9wZW5SZXdh",
            "cmRCb3hGYWlsEC8SEAoMRGF0YU5vdEZvdW5kEDASFwoTR2V0RGlhbG9nUmVz",
            "dWx0RmFpbBAxEhEKDURhdGFCYXRjaEZhaWwQMhIVChFHZXREaWFsb2dMaXN0",
            "RmFpbBAzEhcKE0dldERpYWxvZ0RldGFpbEZhaWwQNBIUChBHZXRCb3hSZXdh",
            "cmRGYWlsEDUSHgoaRnJpZW5kc2hpcFRhc2tTZWxmT2NjdXBpZWQQNhIgChxG",
            "cmllbmRzaGlwVGFza0ZyaWVuZE9jY3VwaWVkEDcSGgoWRnJpZW5kc2hpcFRh",
            "c2tOb0ZyaWVuZBA4EiEKHUdldFVzZXJUb3BpY0RpYWxvZ0hpc3RvcnlGYWls",
            "EDkSHAoYQ3JlYXRlUXVlc3Rpb25EaWFsb2dGYWlsEDoSDwoLR2V0RGF0YUZh",
            "aWwQOxIVChFHZXRDb3Vyc2VJbmZvRmFpbBA8EhIKDlNraXBDb3Vyc2VGYWls",
            "ED0SEwoPR2V0Qm9va0RhdGFGYWlsED4SFAoQR2V0UmFkaW9EYXRhRmFpbBA/",
            "EhcKE0J1eUNvdXJzZVRpY2tldEZhaWwQQBIbChdHZXRDb3Vyc2VTZXR0bGVt",
            "ZW50RmFpbBBBEhwKGENoYW5nZUNvdXJzZVByb2dyZXNzRmFpbBBCEiEKHUNy",
            "ZWF0ZUNvdXJzZVNlc3Npb25SZWNvcmRGYWlsEEMSGQoVRXhpdENvdXJzZVNl",
            "c3Npb25GYWlsEERCJloYdmZfcHJvdG9idWYvc2VydmVyL2Jhc2ljqgIJTXNn",
            "LmJhc2ljYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.basic.PB_Code), }, null, null));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// 状态码
  /// </summary>
  public enum PB_Code {
    /// <summary>
    /// 正常
    /// </summary>
    [pbr::OriginalName("Normal")] Normal = 0,
    /// <summary>
    /// 创建任务对话失败
    /// </summary>
    [pbr::OriginalName("CreateDialogTaskFail")] CreateDialogTaskFail = 1,
    /// <summary>
    /// 对话请求失败
    /// </summary>
    [pbr::OriginalName("MsgHandleFail")] MsgHandleFail = 2,
    /// <summary>
    /// 参数校验失败
    /// </summary>
    [pbr::OriginalName("ParamCheckFail")] ParamCheckFail = 3,
    /// <summary>
    /// 对话创建缓存失败
    /// </summary>
    [pbr::OriginalName("DialogCreateCacheFail")] DialogCreateCacheFail = 4,
    /// <summary>
    /// 对话模式非法
    /// </summary>
    [pbr::OriginalName("DialogModeIllegal")] DialogModeIllegal = 5,
    /// <summary>
    /// 退出对话失败
    /// </summary>
    [pbr::OriginalName("ExitDialogFail")] ExitDialogFail = 6,
    /// <summary>
    /// 获取下一轮对话数据失败
    /// </summary>
    [pbr::OriginalName("GetNextRoundFail")] GetNextRoundFail = 7,
    /// <summary>
    /// flow帮助请求失败
    /// </summary>
    [pbr::OriginalName("FlowHelpFail")] FlowHelpFail = 8,
    /// <summary>
    /// 文本转语音失败
    /// </summary>
    [pbr::OriginalName("TtsFail")] TtsFail = 9,
    /// <summary>
    /// 修改标识请求失败
    /// </summary>
    [pbr::OriginalName("ChangeMarkInfoFail")] ChangeMarkInfoFail = 10,
    /// <summary>
    /// 获取金币余额失败
    /// </summary>
    [pbr::OriginalName("GetCoinBalanceFail")] GetCoinBalanceFail = 11,
    /// <summary>
    /// 用户反馈请求失败
    /// </summary>
    [pbr::OriginalName("FeedbackReqFail")] FeedbackReqFail = 12,
    /// <summary>
    /// gpt风控
    /// </summary>
    [pbr::OriginalName("ChatGptRiskControl")] ChatGptRiskControl = 13,
    /// <summary>
    /// 买票请求失败
    /// </summary>
    [pbr::OriginalName("BuyDialogTicketFail")] BuyDialogTicketFail = 14,
    /// <summary>
    /// 没有对话门票
    /// </summary>
    [pbr::OriginalName("DialogTicketNoExist")] DialogTicketNoExist = 15,
    /// <summary>
    /// 获取进行中对话请求失败
    /// </summary>
    [pbr::OriginalName("GetInProgressDialogFail")] GetInProgressDialogFail = 16,
    /// <summary>
    /// 获取经济详情数据请求失败
    /// </summary>
    [pbr::OriginalName("GetEconomicInfoFail")] GetEconomicInfoFail = 17,
    /// <summary>
    /// 操作资产余额不足
    /// </summary>
    [pbr::OriginalName("NotEnoughAsset")] NotEnoughAsset = 18,
    /// <summary>
    /// 获取用户goal数据失败
    /// </summary>
    [pbr::OriginalName("GetUserGoalInfoFail")] GetUserGoalInfoFail = 19,
    /// <summary>
    /// 获取chapter推荐列表失败
    /// </summary>
    [pbr::OriginalName("GetChapterRecommendListFail")] GetChapterRecommendListFail = 20,
    /// <summary>
    /// 获取用户进行中的chapter信息失败
    /// </summary>
    [pbr::OriginalName("GetUserChapterInfoFail")] GetUserChapterInfoFail = 21,
    /// <summary>
    /// chapter选择失败
    /// </summary>
    [pbr::OriginalName("SelectChapterFail")] SelectChapterFail = 22,
    /// <summary>
    /// 领取chapter奖励失败
    /// </summary>
    [pbr::OriginalName("GetChapterRewardFail")] GetChapterRewardFail = 23,
    /// <summary>
    /// 获取当前chapter进度数据失败
    /// </summary>
    [pbr::OriginalName("GetChapterProgressInfoFail")] GetChapterProgressInfoFail = 24,
    /// <summary>
    /// 获取学习路径页面类型值失败
    /// </summary>
    [pbr::OriginalName("GetLearnPathPageTypeFail")] GetLearnPathPageTypeFail = 25,
    /// <summary>
    /// 动效完成态通知失败
    /// </summary>
    [pbr::OriginalName("NotifyAnimationStateFail")] NotifyAnimationStateFail = 26,
    /// <summary>
    /// 创建对话失败
    /// </summary>
    [pbr::OriginalName("CreateDialogFail")] CreateDialogFail = 27,
    /// <summary>
    /// 同步对话内容失败
    /// </summary>
    [pbr::OriginalName("SendDialogContentFail")] SendDialogContentFail = 28,
    /// <summary>
    /// 对话帮助失败
    /// </summary>
    [pbr::OriginalName("DialogHelpFail")] DialogHelpFail = 29,
    /// <summary>
    /// 查询对话记录失败
    /// </summary>
    [pbr::OriginalName("QueryDialogRecordFail")] QueryDialogRecordFail = 30,
    /// <summary>
    /// 获取对话脚手架失败
    /// </summary>
    [pbr::OriginalName("GetDialogScaffoldFail")] GetDialogScaffoldFail = 31,
    /// <summary>
    /// 获取对话avatar列表失败
    /// </summary>
    [pbr::OriginalName("GetAvatarListByDialogModeFail")] GetAvatarListByDialogModeFail = 32,
    /// <summary>
    /// 获取对话建议失败
    /// </summary>
    [pbr::OriginalName("DialogSuggestionFail")] DialogSuggestionFail = 33,
    /// <summary>
    /// 获取模式数据失败（扉页）
    /// </summary>
    [pbr::OriginalName("GetModeInfoFail")] GetModeInfoFail = 34,
    /// <summary>
    /// 获取用户goal列表详情失败
    /// </summary>
    [pbr::OriginalName("GetUserGoalDetailFail")] GetUserGoalDetailFail = 35,
    /// <summary>
    /// 学习路径奖励失败
    /// </summary>
    [pbr::OriginalName("LearnPathRewardFail")] LearnPathRewardFail = 36,
    /// <summary>
    /// 对话翻译失败
    /// </summary>
    [pbr::OriginalName("DialogTranslateFail")] DialogTranslateFail = 37,
    /// <summary>
    /// 获取avatar任务信息失败
    /// </summary>
    [pbr::OriginalName("GetAvatarTaskInfoFail")] GetAvatarTaskInfoFail = 38,
    /// <summary>
    /// 获取起始页信息失败
    /// </summary>
    [pbr::OriginalName("GetStartPageInfoFail")] GetStartPageInfoFail = 39,
    /// <summary>
    /// 设置用户援助等级失败
    /// </summary>
    [pbr::OriginalName("SetUserAssistLevelFail")] SetUserAssistLevelFail = 40,
    /// <summary>
    /// 碎片化练习story下没有关卡
    /// </summary>
    [pbr::OriginalName("QuickPracticeUnitEmpty")] QuickPracticeUnitEmpty = 41,
    /// <summary>
    /// 碎片化练习关卡耗尽
    /// </summary>
    [pbr::OriginalName("QuickPracticeUnitEnd")] QuickPracticeUnitEnd = 42,
    /// <summary>
    ///获取通讯录失败
    /// </summary>
    [pbr::OriginalName("GetUserContactsFail")] GetUserContactsFail = 43,
    /// <summary>
    /// avatar收藏失败
    /// </summary>
    [pbr::OriginalName("AvatarFavoriteSettingFail")] AvatarFavoriteSettingFail = 44,
    /// <summary>
    /// 获取用户goal节点失败 路径平铺
    /// </summary>
    [pbr::OriginalName("GetUserGoalNodeFail")] GetUserGoalNodeFail = 45,
    /// <summary>
    /// 删除用户学习路径数据失败
    /// </summary>
    [pbr::OriginalName("DelLearnPathFail")] DelLearnPathFail = 46,
    /// <summary>
    /// 打开宝箱节点失败
    /// </summary>
    [pbr::OriginalName("OpenRewardBoxFail")] OpenRewardBoxFail = 47,
    /// <summary>
    /// 数据查询失败
    /// </summary>
    [pbr::OriginalName("DataNotFound")] DataNotFound = 48,
    /// <summary>
    /// 获取对话结算失败
    /// </summary>
    [pbr::OriginalName("GetDialogResultFail")] GetDialogResultFail = 49,
    /// <summary>
    /// 数据批量获取失败
    /// </summary>
    [pbr::OriginalName("DataBatchFail")] DataBatchFail = 50,
    /// <summary>
    ///获取对话记录列表失败
    /// </summary>
    [pbr::OriginalName("GetDialogListFail")] GetDialogListFail = 51,
    /// <summary>
    ///获取对话详情失败
    /// </summary>
    [pbr::OriginalName("GetDialogDetailFail")] GetDialogDetailFail = 52,
    /// <summary>
    /// 宝箱领取失败
    /// </summary>
    [pbr::OriginalName("GetBoxRewardFail")] GetBoxRewardFail = 53,
    /// <summary>
    /// 好友任务自己被占领
    /// </summary>
    [pbr::OriginalName("FriendshipTaskSelfOccupied")] FriendshipTaskSelfOccupied = 54,
    /// <summary>
    /// 好友任务搭子被占领
    /// </summary>
    [pbr::OriginalName("FriendshipTaskFriendOccupied")] FriendshipTaskFriendOccupied = 55,
    /// <summary>
    /// 好友任务无可用好友
    /// </summary>
    [pbr::OriginalName("FriendshipTaskNoFriend")] FriendshipTaskNoFriend = 56,
    /// <summary>
    /// 获取topic历史消息记录失败
    /// </summary>
    [pbr::OriginalName("GetUserTopicDialogHistoryFail")] GetUserTopicDialogHistoryFail = 57,
    /// <summary>
    /// 创建问题对话失败
    /// </summary>
    [pbr::OriginalName("CreateQuestionDialogFail")] CreateQuestionDialogFail = 58,
    /// <summary>
    /// 获取数据失败
    /// </summary>
    [pbr::OriginalName("GetDataFail")] GetDataFail = 59,
    /// <summary>
    /// 获取课程信息失败
    /// </summary>
    [pbr::OriginalName("GetCourseInfoFail")] GetCourseInfoFail = 60,
    /// <summary>
    /// 跳过课程失败
    /// </summary>
    [pbr::OriginalName("SkipCourseFail")] SkipCourseFail = 61,
    /// <summary>
    /// 获取书本数据失败
    /// </summary>
    [pbr::OriginalName("GetBookDataFail")] GetBookDataFail = 62,
    /// <summary>
    /// 获取耳机数据失败
    /// </summary>
    [pbr::OriginalName("GetRadioDataFail")] GetRadioDataFail = 63,
    /// <summary>
    /// 购买课程门票失败
    /// </summary>
    [pbr::OriginalName("BuyCourseTicketFail")] BuyCourseTicketFail = 64,
    /// <summary>
    /// 课程结算失败
    /// </summary>
    [pbr::OriginalName("GetCourseSettlementFail")] GetCourseSettlementFail = 65,
    /// <summary>
    /// 修改课程进度失败
    /// </summary>
    [pbr::OriginalName("ChangeCourseProgressFail")] ChangeCourseProgressFail = 66,
    /// <summary>
    /// 创建课程会话记录失败
    /// </summary>
    [pbr::OriginalName("CreateCourseSessionRecordFail")] CreateCourseSessionRecordFail = 67,
    /// <summary>
    /// 退出课程会话失败
    /// </summary>
    [pbr::OriginalName("ExitCourseSessionFail")] ExitCourseSessionFail = 68,
  }

  #endregion

}

#endregion Designer generated code

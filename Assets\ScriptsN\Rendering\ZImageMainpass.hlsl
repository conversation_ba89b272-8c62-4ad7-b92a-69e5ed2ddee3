﻿#ifndef ZImageMainpass
#define ZImageMainpass

#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

struct appdata_t
{
    float4 vertex : POSITION;
    float4 color : COLOR;
    float4 texcoord : TEXCOORD0;
};

struct v2f
{
    float4 vertex : SV_POSITION;
    float4 color : COLOR;
    float4 texcoord : TEXCOORD0;
    

    #ifdef CLIPPED
    float2 clipPos : TEXCOORD1;
    
    #endif

    #ifdef SOFT_CLIPPED
    float2 clipPos : TEXCOORD1;
    #endif

//    float4 testNum : TEXCOORD2;
    float2 screenUV : TEXCOORD3;
};

Texture2D _MainTex;
SAMPLER(sampler_MainTex);

int _BLUR;

#ifdef COMBINED
Texture2D _AlphaTex;
SAMPLER(sampler_AlphaTex);
#endif

#ifdef BLUR
Texture2D _BlurTex;
SAMPLER(sampler_BlurTex);
#endif

CBUFFER_START(UnityPerMaterial) 
float4 _ClipBox;
float4 _ClipSoftness;
CBUFFER_END


float4x4 _ColorMatrix;
float4 _ColorOffset;
float _ColorOption;

//------------------(UNITY工具函数)------------------
half3 GammaToLinearSpace (half3 sRGB)
{
    // Approximate version from http://chilliant.blogspot.com.au/2012/08/srgb-approximations-for-hlsl.html?m=1
    return sRGB * (sRGB * (sRGB * 0.305306011h + 0.682171111h) + 0.012522878h);

    // Precise version, useful for debugging.
    //return half3(GammaToLinearSpaceExact(sRGB.r), GammaToLinearSpaceExact(sRGB.g), GammaToLinearSpaceExact(sRGB.b));
}
//--------------------------------------------
v2f vert(appdata_t v)
{
    v2f o;
    VertexPositionInputs positionInput = GetVertexPositionInputs(v.vertex);
    
    o.vertex = positionInput.positionCS;
    o.texcoord = v.texcoord;
    o.color = v.color;
 //   o.testNum = float4(0,0,0,0);
    o.screenUV = float2(0,0);

    #if !defined(UNITY_COLORSPACE_GAMMA) && (UNITY_VERSION >= 550)
    o.color.rgb = GammaToLinearSpace(v.color.rgb);
    o.color.a = v.color.a;
    #else
    o.color = v.color;
    #endif

    #ifdef CLIPPED
    o.clipPos = positionInput.positionWS.xy * _ClipBox.zw + _ClipBox.xy;
  //  o.testNum = float4(positionInput.positionWS.xy ,0,1);
    #endif

    #ifdef SOFT_CLIPPED
    o.clipPos = mul(unity_ObjectToWorld, v.vertex).xy * _ClipBox.zw + _ClipBox.xy;
    #endif

    #ifdef BLUR
    o.screenUV = positionInput.positionNDC.xy / positionInput.positionNDC.w;
    #endif

    return o;
}

float4 frag(v2f i) : SV_Target
{
    float4 col = _MainTex.Sample(sampler_MainTex, i.texcoord.xy / i.texcoord.w) * i.color;
  //  float4 col = _MainTex.Sample(sampler_MainTex, i.texcoord.xy / i.texcoord.w);
    
    
    #ifdef COMBINED
    col.a *= _AlphaTex.Sample(sampler_AlphaTex, i.texcoord.xy / i.texcoord.w).g;
    #endif
   
    #ifdef GRAYED
    float grey = dot(col.rgb, float3(0.299, 0.587, 0.114));
    col.rgb = grey.xxx;
    #endif

    
    
    #ifdef SOFT_CLIPPED
    float2 factor = float2(0, 0);
    if (i.clipPos.x < 0)
        factor.x = (1.0 - abs(i.clipPos.x)) * _ClipSoftness.x;
    else
        factor.x = (1.0 - i.clipPos.x) * _ClipSoftness.z;
    if (i.clipPos.y < 0)
        factor.y = (1.0 - abs(i.clipPos.y)) * _ClipSoftness.w;
    else
        factor.y = (1.0 - i.clipPos.y) * _ClipSoftness.y;
    col.a *= clamp(min(factor.x, factor.y), 0.0, 1.0);
    #endif
   
    #ifdef CLIPPED
    float2 factor = abs(i.clipPos);
    col.a *= step(max(factor.x, factor.y), 1.0);
  //  col.rgb = i.clipPos.x;
    // col.rg = i.clipPos*0.1 + 0.5;
    // col.b = 0;
    //
    // col.rgba = i.testNum.g * 0.08 +0.5;
    // col.a = 1;
    #endif
   
    #ifdef COLOR_FILTER
    if (_ColorOption == 0)
    {
        float4 col2 = col;
        col2.r = dot(col, _ColorMatrix[0]) + _ColorOffset.x;
        col2.g = dot(col, _ColorMatrix[1]) + _ColorOffset.y;
        col2.b = dot(col, _ColorMatrix[2]) + _ColorOffset.z;
        col2.a = dot(col, _ColorMatrix[3]) + _ColorOffset.w;
        col = col2;
    }
    else // premultiply alpha
    {
        col.rgb *= col.a;
    }
    #endif
    
    #ifdef ALPHA_MASK
    clip(col.a - 0.001);
    #endif

    if (_BLUR > 0)
    {
        #ifdef BLUR
        half alpha  = col.a;
        clip(col.a - 0.002);
        half4 blur = SAMPLE_TEXTURE2D(_BlurTex,sampler_BlurTex,i.screenUV);

        //1.
        //Modifier
        // half3 modifierCol = blur.rgb;
        // half4 modifier = half4(0.275f,0.275f,0.275f,1.0f);
        // modifier = clamp(modifier,0.0f,0.999f);
        // modifierCol.rgb = modifierCol.rgb + (modifier.rgb * modifierCol.rgb) / (half3(1.0f,1.0f,1.0f) - modifier.rgb);
        // modifierCol.rgb = clamp(modifierCol.rgb,0.0f,1.0f);
        // modifierCol.rgb = modifierCol.rgb * modifier.a + blur.rgb * (1-modifier.a);
        // col.rgb = modifierCol.rgb * (1-col.a) + col.rgb * col.a;

        //2.
        // //Modifier
        col.rgb = blur.rgb * (1-col.a) + col.rgb * col.a;
        half3 modifierCol = col.rgb;
        half4 modifier = half4(0.275f,0.275f,0.275f,1.0f);
        modifier = clamp(modifier,0.0f,0.999f);
        modifierCol.rgb = modifierCol.rgb + (modifier.rgb * modifierCol.rgb) / (half3(1.0f,1.0f,1.0f) - modifier.rgb);
        modifierCol.rgb = clamp(modifierCol.rgb,0.0f,1.0f);
        modifierCol.rgb = modifierCol.rgb * modifier.a + col.rgb * (1-modifier.a);
        col.rgb = modifierCol.rgb;

        // //3.
        // col.rgb = 1;
        // col.a = 0.6;
        // col.rgb = (col.rgb * col.a + blur.rgb * (1- col.a));
        

        
      //  col.rgb = blur;
        col.a = 1;
        #endif
    }
    

    return col;

}
#endif
﻿using System.Collections.Generic;
using System.Linq;
using Msg.question;
using UIBind.FragmentPractice;
using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.FragmentPractice
{
    public class SelectData : APracticeData
    {
        public PB_ReadWordAndSelectPicQuestion AssistQuestion { get; private set; }

        public SelectData(long grpId, PB_QuickPracticeInfo data) : base(grpId, data)
        {
            if (data.dataCase == PB_QuickPracticeInfo.dataOneofCase.read_word_and_select_pic)
            {
                AssistQuestion = data.read_word_and_select_pic;
                AudioId = AssistQuestion.tts_id;
            }
        }

        public override List<PB_BlankRange> GetBlankRanges()
        {
            return new List<PB_BlankRange>(0);
        }

        public override string GetStem()
        {
            return AssistQuestion.question;
        }

        protected override string GetCorrectAnswer()
        {
            return AssistQuestion.correct_index.ToString();
        }

        protected override string GetAnswerContent()
        {
            return AssistQuestion.choices[AssistQuestion.correct_index].translation;
        }

        public override int[] GetAnswerIndexes()
        {
            return new[] { AssistQuestion.correct_index };
        }

        public override string[] GetAnswerOptions()
        {
            return AssistQuestion.choices.Select(c => c.translation).ToArray();
        }

        public override long[] GetAnswerOptionTts()
        {
            return AssistQuestion.choices.Select(c => c.tts_id).ToArray();
        }

        public override string[] GetAnswerOptionImage()
        {
            return AssistQuestion.choices.Select(c => c.image_url).ToArray();
        }

        public override int GetOptionsCount()
        {
            return AssistQuestion.choices.Count;
        }

        public override string GetQuestionTypeLanguageKey()
        {
            return "ui_fragment_question_type_selectPicture";
        }

        public override string GetQuestionComponentUrl()
        {
            return SimpleTextQuestion.url;
        }

        public override string GetAnswerComponentUrl()
        {
            return ImageAnswer.url;
        }

        protected override object GetQuestionObject()
        {
            return AssistQuestion;
        }

        protected override string GetMeaningContent()
        {
            return AssistQuestion.translation;
        }
    }
}
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Settlement
{
    public partial class SettlementFragPanel : UIBindT
    {
        public override string pkgName => "Settlement";
        public override string comName => "SettlementFragPanel";

        public Controller state;
        public Controller spineType;
        public GGraph imgBG;
        public GImage I;
        public GGraph holder;
        public GLoader3D ldr3dFlow;
        public GTextField tfCongra;
        public GTextField tfText;
        public GGroup grpNoraml;
        public GTextField tfCongraResign;
        public GLoader3D sp_flow_resign;
        public GButton btnResign;
        public GGroup grpResign;
        public GTextField tfCongraStamina;
        public GLoader3D sp_flow_stamina;
        public GButton btnStamina;
        public GRichTextField tfStaminaCnt;
        public GGroup grpStamina;
        public SettlementFragFalsePanel compFalse;
        public CompBottom compBottom;
        public CompSettleList compSettleList;
        public CompSettleMark compSettleMark;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            state = com.GetControllerAt(0);
            spineType = com.GetControllerAt(1);
            imgBG = (GGraph)com.GetChildAt(0);
            I = (GImage)com.GetChildAt(1);
            holder = (GGraph)com.GetChildAt(3);
            ldr3dFlow = (GLoader3D)com.GetChildAt(4);
            tfCongra = (GTextField)com.GetChildAt(8);
            tfText = (GTextField)com.GetChildAt(9);
            grpNoraml = (GGroup)com.GetChildAt(10);
            tfCongraResign = (GTextField)com.GetChildAt(11);
            sp_flow_resign = (GLoader3D)com.GetChildAt(12);
            btnResign = (GButton)com.GetChildAt(14);
            grpResign = (GGroup)com.GetChildAt(15);
            tfCongraStamina = (GTextField)com.GetChildAt(16);
            sp_flow_stamina = (GLoader3D)com.GetChildAt(17);
            btnStamina = (GButton)com.GetChildAt(19);
            tfStaminaCnt = (GRichTextField)com.GetChildAt(22);
            grpStamina = (GGroup)com.GetChildAt(26);
            compFalse = new SettlementFragFalsePanel();
            compFalse.Construct(com.GetChildAt(27).asCom);
            compBottom = new CompBottom();
            compBottom.Construct(com.GetChildAt(28).asCom);
            compSettleList = (CompSettleList)com.GetChildAt(29);
            compSettleMark = (CompSettleMark)com.GetChildAt(30);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            state = null;
            spineType = null;
            imgBG = null;
            I = null;
            holder = null;
            ldr3dFlow = null;
            tfCongra = null;
            tfText = null;
            grpNoraml = null;
            tfCongraResign = null;
            sp_flow_resign = null;
            btnResign = null;
            grpResign = null;
            tfCongraStamina = null;
            sp_flow_stamina = null;
            btnStamina = null;
            tfStaminaCnt = null;
            grpStamina = null;
            compFalse.Dispose();
            compFalse = null;
            compBottom.Dispose();
            compBottom = null;
            compSettleList = null;
            compSettleMark = null;
        }
    }
}
﻿using System;
using FairyGUI;
using Modules.DataDot;
using ScriptsHot.Game.Modules.Settlement;
using UIBind.Shop;

namespace ScriptsHot.Game.Modules.Shop
{
    public class SpeakPlanPromotionStep3UI : BaseUI<SpeakPlanPromotionStep3Panel>
    {
        public SpeakPlanPromotionStep3UI(string name) : base(name)
        {
        }
        public override string uiLayer => UILayerConsts.Top;
        private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);
        private ShopModel _shopModel => GetModel<ShopModel>(ModelConsts.Shop);
        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            AddUIEvent(ui.btnBack.onClick, OnClickBack);
            AddUIEvent(ui.btnBottom.onClick, OnClickBottom);

            ui.btnBottom.SetKeyArgs("ui_plan_promotion_desc23", _shopModel.GetRecommendPlanDays());
            ui.tfDesc.SetKey("ui_plan_promotion_desc24");
            ui.tfTitle.SetKey("ui_plan_promotion_desc16");
            ui.tfNotify.SetKeyArgs("ui_plan_promotion_desc17", DateTime.Now.AddDays(_shopModel.GetRecommendPlanDays() - 5).ToString(I18N.inst.MoStr("ui_plan_promotion_desc20")));
        }

        protected override bool isFullScreen => true;
        
        private void OnClickBack()
        {
            DataDotTrialDetailQuit dot = new DataDotTrialDetailQuit();
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            DataDotMgr.Collect(dot);
            
            bool needHide = true;
            if (args != null && args.Length > 0)
            {
                var source = (int)args[0];
                if (source == 4)
                {
                    needHide = false;
                    GetController<SettlementController>(ModelConsts.Settlement).ShowNextView(()
                        =>
                    {
                        GetController<ShopController>(ModelConsts.Shop).CheckSendOnBoardOverMsg();
                        Hide();
                    });
                }
            }
            if (needHide)
            {
                GetController<ShopController>(ModelConsts.Shop).CheckSendOnBoardOverMsg();
                Hide();
            }
        }

        private void OnClickBottom()
        {
            GetUI<SpeakPlanSelectUI>(UIConsts.SpeakPlanSelectUI).Show(args);
            Hide();
            
            DataDotTrialDetailNext dot = new DataDotTrialDetailNext();
            dot.source_page = nameof(PayWallDotHelper.LastSourcePage);
            DataDotMgr.Collect(dot);
            
            AFDots.Click_Remind_page_get();
        }
    }
}
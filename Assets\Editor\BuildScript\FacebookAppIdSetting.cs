using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using Facebook.Unity.Settings;
using UnityEditor;
using UnityEngine;

public class FacebookAppIdSetting
{
    private const int IOS_SettingIndex = 0;
    private const int Android_SettingIndex = 1;

    private const string appId_android = "2600101563659531";
    private const string appName_android = "Talkit-android";
    private const string appToken_android = "4468e2cdf3c7ede87f3b87fa7023c8f2";

    [MenuItem("Facebook/FaceBookSetting/SetIos")]
    public static void SetIosSetting()
    {
        FacebookSettings.SelectedAppIndex = IOS_SettingIndex;
    }

    [MenuItem("Facebook/FaceBookSetting/SetAndroid")]
    public static void SetAndroidSetting()
    {
        FacebookSettings.SelectedAppIndex = Android_SettingIndex;

        UpdateAndroidManifest();
    }

    public static void UpdateAndroidManifest()
    {
        string manifestPath = Path.Combine(Application.dataPath, "Plugins/Android/AndroidManifest.xml");
        if (!File.Exists(manifestPath))
        {
            Debug.LogWarning("AndroidManifest.xml not found at: " + manifestPath);
            return;
        }

        string appId = appId_android;
        string clientToken = appToken_android;
        string fullAppId = $"fb{appId}";
        string providerAuthority = $"com.facebook.app.FacebookContentProvider{appId}";

        XmlDocument doc = new XmlDocument();
        doc.Load(manifestPath);

        XmlNode manifestNode = doc.SelectSingleNode("/manifest");
        XmlNode appNode = manifestNode.SelectSingleNode("application");

        void AddOrUpdateMeta(string name, string value)
        {
            XmlElement meta = null;
            foreach (XmlNode node in appNode.SelectNodes("meta-data"))
            {
                var attr = node.Attributes?["android:name"];
                if (attr != null && attr.Value == name)
                {
                    meta = (XmlElement) node;
                    break;
                }
            }

            if (meta == null)
            {
                meta = doc.CreateElement("meta-data");
                meta.SetAttribute("android:name", name);
                meta.SetAttribute("android:value", value);
                appNode.AppendChild(meta);
            }
            else
            {
                meta.SetAttribute("android:value", value);
            }
        }

        void AddOrUpdateProvider(string authority)
        {
            XmlElement provider = null;
            foreach (XmlNode node in appNode.SelectNodes("provider"))
            {
                var attr = node.Attributes?["android:name"];
                if (attr != null && attr.Value == "com.facebook.FacebookContentProvider")
                {
                    provider = (XmlElement) node;
                    break;
                }
            }

            if (provider == null)
            {
                provider = doc.CreateElement("provider");
                provider.SetAttribute("android:name", "com.facebook.FacebookContentProvider");
                provider.SetAttribute("android:authorities", authority);
                provider.SetAttribute("android:exported", "true");
                appNode.AppendChild(provider);
            }
            else
            {
                provider.SetAttribute("android:authorities", authority);
            }
        }

        AddOrUpdateMeta("com.facebook.sdk.ApplicationId", fullAppId);
        AddOrUpdateMeta("com.facebook.sdk.ClientToken", clientToken);
        AddOrUpdateProvider(providerAuthority);

        doc.Save(manifestPath);
        Debug.Log("✅ Facebook AndroidManifest.xml updated successfully.");
    }
}

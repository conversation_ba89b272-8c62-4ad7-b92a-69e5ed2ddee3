﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Assets.ScriptsHot.Game.Modules.Common;
using FairyGUI;
using Spine.Unity;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UIElements;
using static AvatarActionDirector;
using TextField = FairyGUI.TextField;

namespace ScriptsHot.Game.Modules.Common
{
    //绘制 Box的样式参数类别
    public enum TfBoxType
    {
        Normal,
        HighLight,
        NewWord,
        Mask,
        Liaison,
        Plosion,
        LanguagePoint,//高亮
        color
    }

    //指导一段文本，如果BoxType = LanguagePoint时，绘制Box时，更加细化的颜色倾向
    public enum LanguagePoint
    {
        green,
        yellow,
        pink,
        purple,
        grey,
        deepGreen,
        grey4speak //speak(warmup)题初始方框的配色
    }

    

    public enum RichContentType
    {
        /// <summary>
        /// 文字
        /// </summary>
        Text,
        /// <summary>
        /// 语调 升调
        /// </summary>
        IntonationUp,
        /// <summary>
        /// 语调 降调
        /// </summary>
        IntonationDown,
        /// <summary>
        /// 
        /// </summary>
        SplitChar,
        Mask,
        MaskTranslate,//有mask支持点词翻译的..上面那个被改成warmup专属了..
        Wrap,
        /// <summary>
        /// 突出显示
        /// </summary>
        Prominent,
        /// <summary>
        /// 暂停符号
        /// </summary>
        Pause,
        /// <summary>
        /// 加粗
        /// </summary>
        Bold,
    }

    public enum AnswerType
    {
        Normal,
        Correct,
        Wrong,
    }
    
    public enum HitPointFlagType
    {
        Normal,
    }

    public enum ShowMode
    {
        Normal, // 正常，一般全黑
        QuestionNormal, // 题型正常，和Normal的差别只有音调显示,且会绘制 mask部分
        Typing, // 打字机，左往右填充文本，全黑  || 废弃中需要二次测试
        Follow, // 跟读，左往右变紫
        Intonation, // 语调，展示升降调及暂停键，字体全黑, 不播动画
        QuestionFollow, // 题型，跟跟读基本一样，音调不放动画
        Evaluate, // 评分，一般五颜六色
        PlayerFollow, // 跟读,用户录音时,只有黑字和音调,不播放任何动画
        
        SpeakInit,// speak首轮的展示
        SpeakEval, //speak后续轮次，逐词的展示
        FragmentHideShowQuestion//显示 隐藏 组件 支持划线
    }

    public struct RichContent
    {
        public RichContentType Type;
        public string Content;
        public int StartIdx;
        public int EndIdx;
        public int Length;
        public bool IsCoreWord; // 是否是重点语块
        public bool IsLiasionWord; // 是否是爆破音语块
        public bool IsPlosionWord; // 是否是连读语块
        public bool IsLast;
        public int Id;
        public float PlaySpeed;
        public int StartTime;
        public int EndTime;
        public Color Color;
        public bool NoDot;
        public bool DotGreen;//绿点
        public bool IsLanguagePoint; // 知识点-高亮
        public bool LanguagePointLast; // 知识点-高亮 ,最后的一次单词
    }

    public struct AnswerContent
    {
        public int Id;
        public string Answer;
        [FormerlySerializedAs("answerType")] public AnswerType AnswerType;
    }
    
    public partial class TextFieldExtension : TextExtensionBase
    {
        public bool IsTyping => _isTyping;

        public bool IsWholeContent => _isWholeContent;
        
        public bool IsRecordWordEnable
        {
            get
            {
                return _isRecordWordEnable;
            }

            set
            {
                _isRecordWordEnable = value;
            }
        }

        public bool IsFairyBatchingEnable
        {
            get
            {
                return _isFairyBatchingEnable;
            }

            set
            {
                if(value)
                    ((GetChild("text").asRichTextField).displayObject as RichTextField).fairyBatching = true;
                _isFairyBatchingEnable = value;
            }
        }
        
        private bool _isTyping = false;

        private bool _isWholeContent = false;

        private bool _isRecordWordEnable = true;
        
        private bool _isMaxRowEnable = false;

        private bool _isFairyBatchingEnable = false;

        private bool _isBreak = false;
        
        private int _curIdx = 0;

        private int _maxRow = 0;
        
        private int _maxIndex = 0;

        private float _outline = 0;

        private float _faceDilate = 0;

        private Color _outlineColor;
        
        private int _curContentIdx = 0;

        private string _richTextWithoutTags = "";
        
        private string _richText = "";
        
        private Coroutine _typingEffectCrt = null;
        
        private List<NewWordUnderlineComponent> _newWordCmpts = new();
        
        private List<QuestionAnswerComponent> _answerCmpts = new();
        
        List<GGraph> _graphs = new List<GGraph>();

        private List<GComponent> _dotComps = new();
        
        private Dictionary<int, GGraph> _normalBoxes = new();
        
        private Dictionary<int, GGraph> _highlightBoxes = new();
        
        private Dictionary<int, GGraph> _newWordBoxes = new();
        
        private Dictionary<int, GGraph> _splitCharBoxes = new();
        
        private Dictionary<int, GGraph> _coreWordGraphBoxes = new();
        
        private Dictionary<int, GGraph> _plosionWordGraphBoxes = new();
        
        private Dictionary<int, GGraph> _liasionWordGraphBoxes = new();

        private Dictionary<int, GGraph> _LanguagePointBoxes = new();

        private Dictionary<int, GGraph> _colorBoxes = new();


        private Dictionary<(int, int), string> _newWords = new();

        private Dictionary<string, int> _wordList = new();
        
        private Dictionary<int, List<GTextField>> _evaluatedWords = new();
        
        private Queue<GRichTextField> _richTextPool = new();
        
        private Queue<GRichTextField> _richTextObjs = new();
        
        private List<(int, int)> _coreWordBoxes = new();

        private List<(int, int)> _languagePointBoxes = new();
        
        private List<(int, int)> _plosionWordBoxes = new();

        private List<(int, int)> _liasionWordBoxes = new();

        private List<GComponent> _toneItems = new();
        
        private List<GComponent> _pauseItems = new();

        private List<MatchResult> _evaluateResult = new();
        
        private List<RichContent> _richContents = new ();

        private HashSet<string> _cannotTransWords = new ();

        private GGraph _TypeEndChar = null;

        private GTextField _textGft;
        
        private RichTextField _textFt;

        private ShowMode _curShowMode = ShowMode.Normal;

        public static readonly Color BOX_COLOR_NORMAL = new Color(237f / 255f, 237f / 255f, 237f / 255f, 1f);
        // 这是一种灰白色，通常用作普通或默认的背景色。

        public static readonly Color BOX_COLOR_HIGHLIGHT = new Color(102f / 255f, 50f / 255f, 1f, 0.2f);
        // 这是一种带有蓝紫色调的颜色，带有一定透明度（0.2），通常用作高亮显示。

        public static readonly Color BOX_COLOR_NEWWORD = new Color(1f, 1f, 1f, 0f);
        // 这代表完全透明的白色，用于标记新单词（透明）。

        public static readonly Color BOX_COLOR_PLOSION = new Color(255f / 255f, 150f / 255f, 213f / 255f, 1f);
        // 这是一种粉红色或桃色，用来表示“爆炸”或特殊效果。

        public static readonly Color BOX_COLOR_LIAISON = new Color(113 / 255f, 244 / 255f, 252 / 255f, 1f);
        // 这是一种浅蓝色，通常表示联系或关联。

        public static readonly Color BOX_COLOR_YELLOW = new Color(1f, 239 / 255f, 63 / 255f, 1f);
        // 这是一种明亮的黄色，带有轻微的橙色调，用作突出的标记或警告。

        public static readonly Color BOX_COLOR_Green = new Color(93f / 255f, 233f / 255f, 163f / 255f, 1f);
        // 这是一种明亮的绿色，用于表示正常、健康或成功的状态。

        public static readonly Color TEXT_COLOR_NORMAL =  Color.black;
        // 这是标准的黑色，通常用作正常文本的颜色。

        public static readonly Color TEXT_COLOR_DEFAULT = new Color(142f / 255f, 142f / 255f, 147f / 255f, 1f);
        // 这是一种灰色，可能用于默认文本或次要文本内容。

        public static readonly Color TEXT_COLOR_GOOD = new Color(33f / 255f, 208f / 255f, 137f / 255f, 1f);
        // 这是一种绿色，表示好的或正面的状态。

        public static readonly Color TEXT_COLOR_BAD = new Color(1f, 0f, 122f / 255f, 1f);
        // 这是一种鲜艳的红色，表示不好的或负面的状态。

        #region
        public static readonly Color TEXT_SPEAK_UNHIT     = new Color(17f / 255f, 17f / 255f, 17f / 255f, 1f);
        public static readonly Color TEXT_SPEAK_HIT       = new Color(93f / 255f, 58f / 255f, 215f / 255f, 1f);
        public static readonly Color TEXT_SPEAK_FINAL_HIT = new Color(100f / 255f, 200f / 255f, 142f / 255f, 1f);

        public static readonly Color TEXT_SPEAK_UNHIT_BOX       = new Color(242 / 255f, 242 / 255f, 242 / 255f, 1f);
        public static readonly Color TEXT_SPEAK_HIT_BOX         = new Color(175f / 255f, 153f / 255f, 248f / 255f, 1f); 
        public static readonly Color TEXT_SPEAK_FINAL_HIT_BOX   = new Color(219f / 255f, 245f / 255f, 231f / 255f, 1f);
        #endregion

        public static readonly Color TEXT_COLOR_YELLOW = new Color(1f, 0.78f, 0f, 1f);
        
        public static readonly Color TEXT_COLOR_FOLLOW = new Color(102f/255f, 59f/255f, 1f, 1f);

        public static readonly Color TEXT_COLOR_NARRATION = new Color(128f / 255f, 229f / 255f, 245f / 255f, 0.7f);
        
        
        // 这是一种黄色，通常用来表示警告、注意或高亮。
        // 这是一种紫色，可能用来表示“跟随”或某种特殊的文本强调。
        public static readonly Color BOX_COLOR_WHITE = new Color(242f / 255f, 242f / 255f, 247f / 255f, 1f);

        public static readonly Color BOX_COLOR_LIGHT_PURPLE = new Color(178f / 255f, 152f / 255f, 255f / 255f, 1f);

        public static readonly Color BOX_COLOR_PINK = new Color(255f / 255f, 217f / 255f, 235f / 255f, 1f);

        //淡绿
        public static readonly Color BOX_COLOR_LIGHT_GREEN = new Color(211f / 255f, 246f / 255f, 231f / 255f, 1f);
        //深绿
        public static readonly Color BOX_COLOR_DEEP_GREEN = new Color(93f / 255, 233f / 255, 163f / 255,1f);

        public static readonly float BASE_INTERVAL = 0.03f;
        
        public static readonly float DIVIDE_INTERVAL = 0.8f;

        public static readonly int STRONG_OFFSET = 8;
        
        public static readonly int TONE_ITEM_LENGTH = 4;
        
        public static readonly string TONE_ITEM_PLACE_HOLDER = "    ";
        
        public static readonly int PAUSE_ITEM_LENGTH = 4;
        
        public static readonly string PAUSE_ITEM_PLACE_HOLDER = "    ";
        
        public static readonly int SPALIT_CHAR_LENGTH = 2;
        
        public static readonly string SPALIT_CHAR_PLACE_HOLDER = "  ";

        public event Action OnTypingEffectStart;
        
        public event Action OnTypingEffectEnd;
        
        public event Action<NewWordComponent, Vector2> OnClickNewWord;

        public event Action<NewWordComponent, Vector2> OnClickMaskTips;


        public LanguagePoint languagePoint  {  get;  set; } = LanguagePoint.green;

        public string Content => _richTextWithoutTags;

        public bool EnableCache { get; set; } = false;  // 优化DC，不能和打字机一起使用

        private GComponent subtext => GetChild("subtext").asCom;
        public GTextField TextGtf
        {
            get
            {
                 _textGft ??= GetChild("text").asRichTextField;
                return _textGft;
            }
        }
        
        public RichTextField TextTf
        {
            get
            {
                _textFt ??= TextGtf.displayObject as RichTextField;
                return _textFt;
            }
        }

        public int Length
        {
            get
            {
                var l = 0;
                if (_richTextWithoutTags.Length > 0)
                    l = _richTextWithoutTags.Length;
                return l;
            }
        }

        public bool IsBreak => _isBreak;

        public int UnderlineOffset { get; set; } = 5;


        public override void ConstructFromXML(FairyGUI.Utils.XML cxml)
        {
            base.ConstructFromXML(cxml);
            TextTf.textField.EnableCharPositionSupport();
        }
        
        //*********************************************************** public *****************************************************************
        # region public
        /// <summary>
        /// 设置格式
        /// </summary>
        /// <param name="color">颜色</param>
        /// <param name="size">尺寸</param>
        public void SetFormat(Color color, int size)
        {
            TextGtf.color = color;
            var textFormat =  TextGtf.textFormat;
            textFormat.size = size;
            
            TextGtf.textFormat = textFormat;
        }

        public void SetAutoSize(AutoSizeType autoSizeType)
        {
            TextGtf.autoSize = autoSizeType;
        }

        public void SetNewFont(string fontName) {
            var textFormat = TextGtf.textFormat;
            TextGtf.textFormat = new TextFormat() {
                font = fontName,
                color = textFormat.color,
                size = textFormat.size
            };
        }
        public void SetNewFont(string fontName,Color fontCol,int fontSize, AlignType alignType= AlignType.Left)
        {
            TextGtf.textFormat = new TextFormat()
            {
                font = fontName,
                color = fontCol,
                size = fontSize,
                align = alignType
            };
        }

        /// <summary>
        /// 设置对齐方式
        /// </summary>
        public void SetAlignType(AlignType type)
        {
            TextGtf.align = type;
        }

      

        /// <summary>
        /// 设置最大展示行数，超出末尾显示成“...”
        /// </summary>
        /// <param name="row">最大行数</param>
        public void SetMaxRow(int row)
        {
            _isMaxRowEnable = true;
            _maxRow = row;
        }

        public void CloseMaxRow()
        {
            _isMaxRowEnable = false;
        }

        /// <summary>
        /// 设置外发光
        /// </summary>
        /// <param name="outline">描边粗细</param>
        /// <param name="faceDilate">扩张</param>
        /// <param name="color">描边颜色</param>
        public void SetOutline(float outline, float faceDilate, Color color)
        {
            _outline = outline;

            _faceDilate = faceDilate;

            _outlineColor = color;
        }
        
        /// <summary>
        /// 设置答案文本
        /// </summary>
        /// <param name="list">所有答案</param>
        public void ShowAnswer(List<AnswerContent> list, out List<QuestionAnswerComponent> correctCmpts, out List<QuestionAnswerComponent> wrongCmpts)
        {
            ChangeShowMode(ShowMode.Evaluate); 
            RemoveAllMask();
            correctCmpts = new();
            wrongCmpts = new(); 
            foreach (var answerContent in list)
            {
                foreach (var richContent in _richContents)
                {
                    if (answerContent.Id == richContent.Id)
                    {
                        var cmp = DrawMask(richContent.StartIdx, richContent.EndIdx, answerContent.AnswerType);
                        if (answerContent.AnswerType == AnswerType.Wrong)
                            wrongCmpts.Add(cmp);
                        else if (answerContent.AnswerType == AnswerType.Correct)
                            correctCmpts.Add(cmp);
                        var originLength = richContent.EndIdx - richContent.StartIdx;
                        //var startIdx = richContent.StartIdx + (originLength - answerContent.Answer.Length*3)/2;
                        //var endIdx = startIdx + answerContent.Answer.Length;
                        // var text = DrawColor(richContent.StartIdx, richContent.EndIdx,
                        //     answerContent.AnswerType == AnswerType.Correct ? TEXT_COLOR_GOOD :
                        //     answerContent.AnswerType == AnswerType.Wrong ? TEXT_COLOR_BAD : TEXT_COLOR_NORMAL, false, answerContent.Answer,
                        //     answerContent.Answer.Length >= richContent.Length ? _textGft.textFormat.size - 2 : default);
                        // cmp.SetAnswer(answerContent.Answer, answerContent.AnswerType.ToString());
                    }
                }    
            }
        }

        /// <summary>
        /// 单词加点 按索引 不按NoDot
        /// </summary>
        /// <param name="sIndex">开始的索引</param>
        /// <param name="eIndex">结束的索引</param>
        /// <param name="noSpace">默认包含空格</param>
        /// 
        public void AddDot(int sIndex = -1,int eIndex = 99999999,bool noSpace = false)
        {
            ClearUnderDot();
            DrawUnderDot(sIndex, eIndex, noSpace);
        }

        private bool CanBeMerged(RichContent content1, RichContent content2)
        {
            return content1.NoDot == content2.NoDot && content1.DotGreen == content2.DotGreen;
        }


        /// <summary>
        /// 单词加点 可以排除NoDot
        /// </summary>
        /// <param name="contents">整理好的content</param>
        /// <param name="noSpace">默认包含空格</param>
        /// <summary>
        /// 单词加点 可以排除NoDot
        /// </summary>
        /// <param name="contents">整理好的content</param>
        /// <param name="noSpace">默认包含空格</param>
        public void AddDot(List<RichContent> contents, bool noSpace = false)
        {
            ClearUnderDot();
            int sIndex = 0;  // 当前批次的起始点
            int eIndex = 0;  // 当前批次的结束点
            RichContent? curContent = null;
            string debugContent = "";

            for (int i = 0; i < contents.Count; i++)
            {
                var richContent = contents[i];

                // 第一批次，先记下来
                if (curContent == null)
                {
                    curContent = richContent;
                    sIndex = 0;
                    eIndex = richContent.Content.Length;
                    debugContent = richContent.Content;
                    continue;
                }

                // 相同样式，合并，略过
                if (CanBeMerged(curContent.Value, richContent))
                {
                    eIndex += richContent.Content.Length;
                    debugContent += richContent.Content;
                    continue;
                }
                
                // 发现不同样式，画上一批次的点，重新开始计批次
                var startBlock = curContent.Value;
                if (!startBlock.NoDot)
                {
                    DrawUnderDot(sIndex, eIndex, noSpace, 0, startBlock.DotGreen);
                }

                // 重新开始记批次
                curContent = richContent;
                sIndex = eIndex;
                eIndex = sIndex + richContent.Content.Length;
                debugContent = richContent.Content;
            }

            // 最后一批次
            if (curContent != null && !curContent.Value.NoDot)
            {
                var startBlock = curContent.Value;
                DrawUnderDot(sIndex, eIndex, noSpace, 0, startBlock.DotGreen);
            }             
        }

        public void PlayAnswer(List<AnswerContent> list, bool isWrong = false)
        {
            ChangeShowMode(ShowMode.Evaluate); 
            
            foreach (var answerContent in list)
            {
                foreach (var richContent in _richContents)
                {
                    if (answerContent.Id == richContent.Id)
                    {
                        // int delta = (richContent.EndIdx - richContent.StartIdx)/2;
                        // int strLength = richContent.Content.Length;
                        // int rightSpaces = delta - strLength/2;
                        //VFDebug.LogError("delta "+delta+"  strLength "+strLength+" rightSpaces "+rightSpaces);
                        int spacesToAdd = (richContent.EndIdx - richContent.StartIdx ) - answerContent.Answer.Length;
                        // 在字符串前添加空格
                        string word = String.Empty;
                        if(spacesToAdd > 0)
                            word = new string(' ', spacesToAdd) + answerContent.Answer;
                        else if(spacesToAdd == 0)
                            word = answerContent.Answer;
                        else
                        {
                            VFDebug.LogError("spacesToAdd less zero "+spacesToAdd);
                        }
                        //var cmp = DrawMask(richContent.StartIdx, richContent.EndIdx, answerContent.AnswerType);
                        //cmp.PlayCorrectAnswer(world,isWrong);
                        
                        for (var i = 0; i < word.Length; i++)
                        {
                            string strColor = isWrong ? "#FF007A" : "#21D089";
                            Color nowColor;
                            ColorUtility.TryParseHtmlString(strColor, out nowColor);
                            int startIndex = richContent.StartIdx + i;
                            int endIndex = richContent.StartIdx + i + 1;
                            GTextField textField = DrawColor(startIndex, endIndex, nowColor);
                            VFDebug.Log("PlayAnswer    "+startIndex+" endIndex "+endIndex+"  word "+word[i]+" isWrong "+isWrong);
                        }
                    }
                }    
            }
            if(EnableCache)
            {
                subtext.displayObject.cacheAsBitmap = true;
            }
        }

        /// <summary>
        /// 填充Content
        /// </summary>
        /// <param name="contents">所有内容</param>
        /// <param name="showImmediately">是否直接显示</param>
        public TextFieldExtension AppendContent(List<RichContent> contents, bool showImmediately = false)
        {
            foreach (var content in contents)
            {
                if (content.NoDot)
                    _cannotTransWords.Add(content.Content);
                AppendContent(content, showImmediately);
            }
            return this;
        }


        /// <summary>
        /// 填充Content
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="showImmediately">是否直接显示</param>
        public TextFieldExtension AppendContent(RichContent content, bool showImmediately = false)
        {
            if (content.NoDot)
                _cannotTransWords.Add(content.Content);
            var size = TextGtf.textFormat.size;
            content.StartIdx = _richTextWithoutTags.Length;
            var length = 0;
            switch (content.Type)
            {
                case RichContentType.MaskTranslate:
                case RichContentType.Mask:
                    //var placeholder = "mm"+content.Content+"mm";
                    var placeholder = content.Content;
                    _richText += placeholder;
                    _richTextWithoutTags += placeholder;
                    content.Length = placeholder.Length;
                    length = placeholder.Length;
                    break;
                case RichContentType.Prominent:
                    _richText += $"<font size={size + STRONG_OFFSET}>{content.Content}</font>";
                    _richTextWithoutTags += content.Content;
                    // DrawColor(curIdx, curIdx + 1, content.Color);
                    content.Length = content.Content.Length;
                    length = content.Content.Length;
                    break;
                case RichContentType.Bold:
                    _richText += $"<b>{content.Content}</b>";
                    _richTextWithoutTags += content.Content;
                    // DrawColor(curIdx, curIdx + 1, content.Color);
                    content.Length = content.Content.Length;
                    length = content.Content.Length;
                    break;
                case RichContentType.Text:
                    _richText += content.Content;
                    _richTextWithoutTags += content.Content;
                    content.Length =  content.Content.Length;
                    length = content.Content.Length;
                    // DrawColor(curIdx, curIdx + 1, content.Color);
                    break;
                case RichContentType.SplitChar:
                    _richText += SPALIT_CHAR_PLACE_HOLDER;
                    _richTextWithoutTags += SPALIT_CHAR_PLACE_HOLDER;
                    length = SPALIT_CHAR_LENGTH;
                    content.Length = SPALIT_CHAR_LENGTH;
                    break;
                case RichContentType.IntonationDown:
                    _richText += TONE_ITEM_PLACE_HOLDER;
                    _richTextWithoutTags += TONE_ITEM_PLACE_HOLDER;
                    length = TONE_ITEM_LENGTH;
                    content.Length = TONE_ITEM_LENGTH;
                    break;
                case RichContentType.IntonationUp:
                    _richText += TONE_ITEM_PLACE_HOLDER;
                    _richTextWithoutTags += TONE_ITEM_PLACE_HOLDER;
                    length = TONE_ITEM_LENGTH;
                    content.Length = TONE_ITEM_LENGTH;
                    break;
                case RichContentType.Pause://增加暂停
                    _richText += PAUSE_ITEM_PLACE_HOLDER;
                    _richTextWithoutTags += PAUSE_ITEM_PLACE_HOLDER;
                    length = PAUSE_ITEM_LENGTH;
                    content.Length = PAUSE_ITEM_LENGTH;
                    break;
                case RichContentType.Wrap:
                    _richText += "<br>";
                    _richTextWithoutTags += " ";
                    length = 1;
                    content.Length = 1;
                    // VFDebug.LogError("1111111111111");
                    break;
            }
            content.EndIdx = content.StartIdx + length;
            _richContents.Add(content);
            
            if (content.IsCoreWord)
            {
                RegisterCoreWord(content.StartIdx, content.EndIdx);
            }
            if (content.IsLanguagePoint)
            {
                RegisterIsLanguagePointWord(content.StartIdx, content.EndIdx);
            }
            if (content.IsLiasionWord)
            {
                RegisterLiasionWord(content.StartIdx, content.EndIdx);
            }
            if (content.IsPlosionWord)
            {
                RegisterPlosionWord(content.StartIdx, content.EndIdx);
            }
            if (content.IsLast)
            {
                if (_isRecordWordEnable)
                {
                    SplitWords();
                }
                _isWholeContent = true;
            }

            if (showImmediately)
            {
                TextGtf.text = _richText;
                TextGtf.visible = true;
            }
            else {
                TextGtf.visible = false;
            }
                
            return this;
        }
        
        /// <summary>
        /// 显示生词
        /// </summary>
        public override void CreateNewWord()
        {
            //暂时不上
            return;
            ClearNewWordUnderline();
            foreach (var item in _newWords)
            {
                foreach (var word in _wordList)
                {
                    if (String.Equals(item.Value.ToLower(), word.Key.ToLower()))
                    {
                        DrawNewWord(item.Key.Item2, word.Value);
                    }
                }
            }
        }
                
        /// <summary>
        /// 添加生词
        /// </summary>
        /// <param name="word">文本</param>
        /// <param name="count">次数</param>
        public override void AppendNewWord(string word, int count)
        {
            if (_wordList.ContainsKey(word))
                _wordList[word] = count;
            else
                _wordList.Add(word, count);
        }
        
        /// <summary>
        /// 添加生词
        /// </summary>
        /// <param name="wordList">文本，次数</param>
        public override void AppendNewWord(Dictionary<string, int> wordList)
        {
            _wordList = wordList;
        }
        
        /// <summary>
        /// 替文本打分
        /// </summary>
        /// <param name="value">文本，分数</param>
        public void SetEvaluateResult(List<MatchResult> value)
        {
            _evaluateResult = value;
        }


        /// <summary>
        /// 替换某一Content
        /// </summary>
        /// <param name="idx">内容index</param>
        /// <param name="newContent">内容</param>
        public void ReplaceContent(int idx, RichContent newContent)
        {
            _richContents[idx] = newContent;
        }

        /// <summary>
        /// 查询是否有评价特效可播放
        /// </summary>
        public bool IsPlayCollectEffectAble()
        {
            return _evaluatedWords.Count > 0;
        }
        
        public List<Vector2> GetEvaluatedPos()
        {
            if (_evaluatedWords.Count == 0)
                return new List<Vector2>();

            List<Vector2> posList = new();
            foreach (var (k, v) in _evaluatedWords)
            {
                if(TryGetStrPos(k, out var pos))
                    posList.Add(TextTf.parent.LocalToGlobal(pos));
            }

            return posList;
        }
        
        public GGraph GetNewWordByIndex(int index)
        {
            GGraph gGraph = null;
            int i = 0; 
            foreach (GGraph item in _newWordBoxes.Values)
            {
                if (i > index) break;
                gGraph = item;
                i++;
            }            
            return gGraph;
        }
        
        /// <summary>
        /// 播放评价特效
        /// </summary>
        /// <param name="idx">内容index</param>
        /// <param name="newContent">内容</param>
        public void PlayCollectEffect()
        {
            if (_evaluatedWords.Count == 0)
                return;
            _textGft.visible = false;
            //负反馈效果
            var offset = TextGtf.textFormat.size;
            var i = 0;
            foreach (var (k, v) in _evaluatedWords)
            {
                foreach (var com in v)
                {
                    com.TweenMoveY(com.y + offset / 4f, 0.1f).OnComplete(() => com.TweenMoveY(com.y - offset / 2f, 0.1f).OnComplete(() =>
                        com.TweenMoveY(com.y + offset * 3 / 8f, 0.1f).OnComplete(() =>
                            com.TweenMoveY(com.y - offset * 2 / 8f, 0.1f).OnComplete(
                                () => com.TweenMoveY(com.y + +offset / 8f, 0.1f).OnComplete(() =>
                                {
                                    // if (i == 0)
                                    // {
                                        _textGft.visible = true;
                                    // }
                                })
                            ))));
                    i++;
                }
            }

            
            //钢琴打字效果
            // var offset = TextGtf.textFormat.size/2;
            // Timers.inst.Add(0f,1, (_) =>
            //     {
            //         foreach (var (k, v) in _evaluatedWords)
            //         {
            //             var delay = 0.4f / v.Count;
            //             var i = 0;
            //             foreach (var t in v)
            //             {
            //                 Timers.inst.Add(delay + i * delay, 1,
            //                     (o2) => { t.TweenMoveY(t.y - offset, 0.2f).OnComplete(() => { t.TweenMoveY(t.y + offset, 0.2f); }); });
            //                 i++;
            //             }
            //         }
            //     }
            // );

            //弹簧打字效果
            // Timers.inst.Add(3,1, (o) =>
            // {
            //     foreach (var (k,v) in _evaluatedWords)
            //     {
            //         var offset = TextGtf.textFormat.size * 1.5f / v.Count;
            //         var startIdx = -TextGtf.textFormat.size/1.5f;
            //         var i = 0;
            //         foreach (var t in v)
            //         {
            //             var f = startIdx + offset * i;
            //             t.TweenMoveX(t.x + f, 0.4f).OnComplete(() => { t.TweenMoveX(t.x - 2*f, 0.4f);});
            //             i++;
            //         }
            //     }
            // });
        }
        
        /// <summary>
        /// 展示文本（无动效）
        /// </summary>
        /// <param name="showMode">展示模式(主要支持Normal, QuestionNormal， Evaluate)</param>
        public TextFieldExtension Display(ShowMode showMode)
        {
            //Debug.Log("TextExt display showMode="+ showMode+" text="+ _richText);
            ChangeShowMode(showMode);

            #region 处理_textGft相关的

            _textGft.text = _richText;
            // var nextStartIdx = 0;
            if (_isMaxRowEnable)
            {
                _maxIndex = _richText.Length - 1;
                _isBreak = false;
                for (var i = 0; i < TextTf.textField.charPositions.Count; i++)
                {
                    if (TextTf.textField.charPositions[i].lineIndex > _maxRow - 1)
                    {
                        _maxIndex = i;
                        _isBreak = true;
                        _textGft.text = _richText.Substring(0, _maxIndex - 3) + "...";
                        break;
                    }
                }
            }

            #endregion

            //要根据showmode处理 到底使用的是RichTextGroup级别的，还是单个文本RichTextField，还是两者同时混用
            //目前 evalate类是两种都在用，有2层

            switch (showMode)
            {
                //特殊点：只显示group层，逐RichContent生成
                case ShowMode.QuestionNormal:
                    _textGft.visible = false;
                    DrawRichContent(false);//true时逐词（1个词1个textfield），false时不逐字
                    //内部自带mask类的处理
                    DrawMaskAsUnderline();
                    break;

                //注意 这个case 20250515 raybit修订后未测试
                //特殊点：两层都显示，动态层靠_evaluateResult来染色
                case ShowMode.Evaluate:
                    _textGft.visible = true;
                    DrawEvaluateColor();
                    DrawMaskAsUnderline();//不确定是否正确
                    break;

                case ShowMode.SpeakInit:
                case ShowMode.SpeakEval:
                    _textGft.visible = true;

                    //SpeakInit时 _evaluateResult为空，相当于重新写 richtext和文本层
                    //SpeakEval时 _evaluateResult不为空，会增加染色信息 再去写richtext和文本层
                    DrawEvaluateColorForTextField();
                    if (showMode == ShowMode.SpeakInit)
                    {
                        DrawMaskAsBox4SpeakInit();
                    }

                    _evaluateResult.Clear();
                    break;

                default:
                    _textGft.visible = true; //这类的用的 RichTextField
                    DrawMaskAsUnderline();//以前的 DrawNormalMask 
                    break;

            }


            if (showMode == ShowMode.PlayerFollow|| showMode == ShowMode.QuestionNormal)
            {
                DrawQuestionNormalTone();
            }


            
            DrawCoreWord();
            DrawLanguagePointWord();
            DrawPlosionWord();
            DrawLiaisonWord();
            AddDynamicBtn();


            if (EnableCache)
            {
                subtext.displayObject.cacheAsBitmap = true;
            }

            if (_textGft.visible)
            {
                subtext.visible = false;
            }
            else
            {
                subtext.visible = true;
            }

            return this;
        }
        
        /// <summary>
        /// 播放文本（动效）
        /// </summary>
        /// <param name="showMode">展示模式(主要支持Typing, Follow, Intonation, QuestionFollow)</param>
        /// <param name="timeScale">播放速度(默认1倍)(</param>
        public void Play(ShowMode showMode, float timeScale = 1f){
            Debug.Log("TextExt play mode="+ showMode.ToString()+ "_richText="+ _richText);
            ChangeShowMode(showMode);
            if (!_isTyping)
            {
                _isTyping = true;
                TextGtf.text = _richText;
                
                if (_isMaxRowEnable)
                {
                    _maxIndex = _richText.Length - 1;
                    _isBreak = false;
                    for (var i = 0; i < TextTf.textField.charPositions.Count; i++)
                    {
                        if (TextTf.textField.charPositions[i].lineIndex > _maxRow - 1)
                        {
                            _maxIndex = i;
                            _isBreak = true;
                            _textGft.text = _richText.Substring(0, _maxIndex - 3) + "...";
                            break;
                        }
                    }
                }
                
                if (showMode == ShowMode.Follow || showMode == ShowMode.QuestionFollow || showMode == ShowMode.PlayerFollow)
                {
                    TextGtf.visible = true;
                    DrawCoreWord();
                }
                else if (showMode == ShowMode.Typing)
                {
                    TextGtf.visible = false; 
                }
                else if (showMode == ShowMode.Intonation)
                {
                    TextGtf.visible = false;//这个也有打字的
                }
                else
                {
                    TextGtf.visible = false;
                }

                if (_curShowMode == ShowMode.QuestionFollow || showMode == ShowMode.PlayerFollow)
                {
                    DrawQuestionNormalTone();
                    DrawMaskAsUnderline();
                }
                if (showMode == ShowMode.Intonation)
                {
                    _typingEffectCrt = Timers.inst.StartCoroutine(StartPlayTyping(timeScale, false));
                }
                else
                    _typingEffectCrt = Timers.inst.StartCoroutine(StartPlayTyping(timeScale));
            }
        }

        /// <summary>
        /// 重置（初始化）组件
        /// </summary>
        public void Reset()
        {
            _richContents.Clear();
            _evaluatedWords.Clear();
            _coreWordBoxes.Clear();
            _languagePointBoxes.Clear();
            _liasionWordBoxes.Clear();
            _plosionWordBoxes.Clear();
            _newWords.Clear();
            _wordList.Clear();
            _evaluateResult.Clear();
            _cannotTransWords.Clear();
            _richText = "";
            _richTextWithoutTags = "";
            TextGtf.text = "";
            _isTyping = false;
            OnTypingEffectStart = null;
            OnTypingEffectEnd = null;
            OnClickNewWord = null;
            OnClickMaskTips = null;
            ClearTypingEffect();
            ClearNewWordUnderline();
            ClearUnderDot();
            RemoveAllMask();
            RemoveAllBoxes();
      
            RemoveAllToneItems();
            RemoveAllPauseItems();
            RemoveAllRichText();
            foreach (var item in _graphs)
            {
                item.Dispose(); 
            }
            _graphs.Clear();
            DisposeHitPointRes();
        }
        
        /// <summary>
        /// 清空所有展示的内容物
        /// </summary>
        public void ClearContent()
        {
            TextGtf.text = "";
            _isTyping = false;
            ClearTypingEffect();
            ClearNewWordUnderline();
            ClearUnderDot();
            RemoveAllMask();
            RemoveAllBoxes();
         
            RemoveAllToneItems();
            RemoveAllPauseItems(); 
            RemoveAllRichText();
            foreach (var item in _graphs)
            {
                item.Dispose(); 
            }
            _graphs.Clear();
            DisposeHitPointRes();
        }
        
        /// <summary>
        /// 仅仅清理文本
        /// </summary>
        public void Clear()
        {
            _richContents.Clear();
            _richText = "";
            _richTextWithoutTags = "";
            TextGtf.text = "";
            _cannotTransWords.Clear();
            RemoveAllRichText();
            DisposeHitPointRes();
        }

        public void ClearWordBoxs()
        {
            _coreWordBoxes.Clear();
        }
        
        public override void Dispose()
        {
            base.Dispose();
            OnClickNewWord = null;
            OnClickMaskTips = null;
            ClearTypingEffect();
        }
        #endregion
        
        //*********************************************************** private ****************************************************************
        #region private methods
        
        private void ChangeShowMode(ShowMode showMode)
        {
            _curShowMode = showMode;
        }
        
        private void ClearNewWordUnderline()
        {
            foreach (var item in _newWordCmpts)
            {
                item.asCom.Dispose();
            }
            _newWordCmpts.Clear();
        }
        
        private void RemoveAllMask()
        {
            foreach (var item in _answerCmpts)
            {
                item.asCom.Dispose();
            }
            _answerCmpts.Clear();
        }
        
        private void ClearTypingEffect()
        {
            if (_typingEffectCrt != null)
            {
                Timers.inst.StopCoroutine(_typingEffectCrt);
                _typingEffectCrt = null;
            }
        }

        // bWithTypingAni 是否播放打字机效果
        private IEnumerator StartPlayTyping(float timeScale, bool bWithTypingAni = true)
        {
            var startTime = Time.time;
            OnTypingEffectStart?.Invoke();
            _curIdx = 0;
            // var nextStartIdx = 0;
            while (_curIdx < Length || !_isWholeContent)
            {
                var content = GetRichContent(_curIdx);
                
                // 未收完全部文本等待协议下发
                if (_curIdx > _richTextWithoutTags.Length - 1)
                    yield return new WaitUntil( ()=> _curIdx <= _richTextWithoutTags.Length - 1);
                
                if (bWithTypingAni)
                {
                    // 根据时间轴等待，没有时间轴数据按默认时间打字(0.03s/字)
                    if (content.StartIdx == _curIdx)
                        yield return new WaitUntil(() => content.StartTime * timeScale / 1000 <= Time.time - startTime);
                    else
                        yield return new WaitForSeconds(content.PlaySpeed > 0 ? content.PlaySpeed * timeScale : BASE_INTERVAL * timeScale);
                }

                if (_isMaxRowEnable && _isBreak)
                {
                    if (_curIdx <= _maxIndex - 4)
                    {
                        CreateContent(content, _curIdx);
                    }
                }
                else
                {
                    CreateContent(content, _curIdx);
                }
                // 若要打字打到重点语块最后一个字才展示的效果，用下面代码
                // if (WillDrawCoreWord(_curIdx, out var endBoxIdx))
                // {
                //     DrawCoreWord(nextStartIdx, endBoxIdx, out var startBoxIdx);
                //     nextStartIdx = startBoxIdx;
                // }
                
                _curIdx++;
            }

            yield return new WaitForSeconds(0.2f);
            
            if(EnableCache)
            {
                subtext.displayObject.cacheAsBitmap = true;
            }

            _isTyping = false;
            if (_TypeEndChar != null)
                _TypeEndChar.visible = false;
            // DrawCoreWord();
            AddDynamicBtn();
            OnTypingEffectEnd?.Invoke();
            _typingEffectCrt = null;
        }

        //与Evaluate类流程冲突
        private void CreateContent(RichContent content, int curIdx,bool allowDrawByWord=false)
        {
            //逐字逐词渲染日志
            //Debug.Log("init create Content: c=" + content.Content + " t=" + content.Type.ToString() + "_curShowMode="+ _curShowMode.ToString());
            switch (content.Type)
            {
                //特殊content符号，画上去的类别，很多符号已经没有继续用了
                case RichContentType.MaskTranslate:
                case RichContentType.Mask:
                    if (curIdx == content.StartIdx)
                    {
                        DrawMask(content.StartIdx, content.EndIdx, AnswerType.Normal);
                    }
                    break;
                case RichContentType.SplitChar:
                    if (curIdx == content.StartIdx)
                    {
                        DrawSplitChar(curIdx, true);
                    }
                    break;
                case RichContentType.IntonationDown:
                    if (curIdx == content.StartIdx )
                        DrawTone(curIdx, RichContentType.IntonationDown,_curShowMode == ShowMode.Intonation ? TEXT_COLOR_DEFAULT : content.Color);
                    break;
                case RichContentType.IntonationUp:
                    if (curIdx == content.StartIdx ) 
                        DrawTone(curIdx, RichContentType.IntonationUp, _curShowMode == ShowMode.Intonation ? TEXT_COLOR_DEFAULT : content.Color);
                    break;
                case RichContentType.Pause:
                    if (curIdx == content.StartIdx ) 
                        DrawPause(curIdx);
                    break;
                default:
                    var colorVal = _curShowMode == ShowMode.Follow ? TEXT_COLOR_FOLLOW : content.Color;//follow有特殊染色规则，但是现在也不怎么用了
                    var isStrongVal = content.Type == RichContentType.Prominent ? true : false;
                    var isBoldVal = content.Type == RichContentType.Bold ? true : false;

                    //常规类文本
                    if (allowDrawByWord)
                    {
                        if (curIdx == content.StartIdx)
                        {
                            DrawColor(content.StartIdx, content.EndIdx, colorVal, isStrong: isStrongVal, null, 0, isBoldVal);
                        }
                    }
                    else {
       
                        DrawColor(curIdx, curIdx+1, colorVal, isStrong: isStrongVal, null, 0, isBoldVal);
                    }
                  
                    break;
            }
        }

        private void RegisterCoreWord(int startIdx, int endIdx)
        {
            _coreWordBoxes.Add((startIdx, endIdx));
        }

        private void RegisterIsLanguagePointWord(int startIdx, int endIdx)
        {
            _languagePointBoxes.Add((startIdx, endIdx));
        }
        
        private void RegisterLiasionWord(int startIdx, int endIdx)
        {
            _liasionWordBoxes.Add((startIdx, endIdx));
        }
        
        private void RegisterPlosionWord(int startIdx, int endIdx)
        {
            _plosionWordBoxes.Add((startIdx, endIdx));
        }

        private bool WillDrawCoreWord(int idx, out int endBoxIdx)
        {
            endBoxIdx = 9999999;
            for (int i = 0; i < _coreWordBoxes.Count; i++)
            {
                if (i == _coreWordBoxes.Count - 1 && idx == _coreWordBoxes[_coreWordBoxes.Count - 1].Item2 - 1)
                {
                    endBoxIdx = i;
                    return true; 
                }
                
                if ( i + 1 < _coreWordBoxes.Count && idx == _coreWordBoxes[i].Item2)
                {
                    if (_coreWordBoxes[i + 1].Item1 == _coreWordBoxes[i].Item2)
                    {
                        return false;
                    }
                    endBoxIdx = i;
                    return true;
                }
            }
            return false;
        }
        
        private void DrawCoreWord()
        {
            DrawCoreWord(0, _coreWordBoxes.Count - 1, out var startBoxIdx);
        }
        
        private void DrawCoreWord(int boxIdx,int endBoxIdx, out int startBoxIdx)
        {
            startBoxIdx = 0;
            for (var i = endBoxIdx; i >= boxIdx; i--)
            {
                var p = _coreWordBoxes[i];
                var startIdx = p.Item1;
                var endIdx = p.Item2;
                var endLine = GetStrLine(endIdx);
                for (var k = i - 1; k >= boxIdx; k--)
                {
                    if (_coreWordBoxes[k].Item2 == startIdx)
                    {
                        var startLine = GetStrLine(_coreWordBoxes[k].Item1);
                        if (endLine == startLine)
                        {
                            startIdx = _coreWordBoxes[k].Item1;
                            if (k == boxIdx)
                            {
                                i = k;
                                break;
                            }
                        }
                        else
                        {
                            i = k + 1;
                            break;
                        }
                    }
                    else
                    {
                        i = k + 1;
                        break;
                    }
                }

                startBoxIdx = i + 1;
                DrawBox(startIdx, endIdx, TfBoxType.Normal);
            }
        }
        
        private void DrawLanguagePointWord()
        {
            DrawLanguagePointWord(0, _languagePointBoxes.Count - 1, out var startBoxIdx);
        }
        
        private void DrawLanguagePointWord(int boxIdx, int endBoxIdx, out int startBoxIdx)
        {
            startBoxIdx = 0;
            for (var i = endBoxIdx; i >= boxIdx; i--)
            {
                var p = _languagePointBoxes[i];
                var startIdx = p.Item1;
                var endIdx = p.Item2;
                var endLine = GetStrLine(endIdx);
                for (var k = i - 1; k >= boxIdx; k--)
                {
                    if (_languagePointBoxes[k].Item2 == startIdx)
                    {
                        var startLine = GetStrLine(_languagePointBoxes[k].Item1);
                        if (endLine == startLine)
                        {
                            startIdx = _languagePointBoxes[k].Item1;
                            if (k == boxIdx)
                            {
                                i = k;
                                break;
                            }
                        }
                        else
                        {
                            i = k + 1;
                            break;
                        }
                    }
                    else
                    {
                        i = k + 1;
                        break;
                    }
                }

                startBoxIdx = i + 1;
                DrawBox(startIdx, endIdx, TfBoxType.LanguagePoint);

            }
        }

        private void DrawLiaisonWord()
        {
            DrawLiaisonWord(0, _liasionWordBoxes.Count - 1, out var startBoxIdx);
        }
        
        //画连读单词底色
        private void DrawLiaisonWord(int boxIdx,int endBoxIdx, out int startBoxIdx)
        {
            startBoxIdx = 0;
            for (var i = endBoxIdx; i >= boxIdx; i--)
            {
                var p = _liasionWordBoxes[i];
                var startIdx = p.Item1;
                var endIdx = p.Item2;
                var endLine = GetStrLine(endIdx);
                for (var k = i - 1; k >= boxIdx; k--)
                {
                    if (_liasionWordBoxes[k].Item2 == startIdx)
                    {
                        var startLine = GetStrLine(_liasionWordBoxes[k].Item1);
                        if (endLine == startLine)
                        {
                            startIdx = _liasionWordBoxes[k].Item1;
                            if (k == boxIdx)
                            {
                                i = k;
                                break;
                            }
                        }
                        else
                        {
                            i = k + 1;
                            break;
                        }
                    }
                    else
                    {
                        i = k + 1;
                        break;
                    }
                }

                startBoxIdx = i + 1;
                var box = DrawBox(startIdx, endIdx, TfBoxType.Liaison);
                if (box == null) return;
                _liasionWordGraphBoxes.Add(i,box);
            }
        }
        
        //画爆破音单词底色
        private void DrawPlosionWord()
        {
            DrawPlosionWord(0, _plosionWordBoxes.Count - 1, out var startBoxIdx);
        }
        
        private void DrawPlosionWord(int boxIdx,int endBoxIdx, out int startBoxIdx)
        {
            startBoxIdx = 0;
            for (var i = endBoxIdx; i >= boxIdx; i--)
            {
                var p = _plosionWordBoxes[i];
                var startIdx = p.Item1;
                var endIdx = p.Item2;
                var endLine = GetStrLine(endIdx);
                for (var k = i - 1; k >= boxIdx; k--)
                {
                    if (_plosionWordBoxes[k].Item2 == startIdx)
                    {
                        var startLine = GetStrLine(_plosionWordBoxes[k].Item1);
                        if (endLine == startLine)
                        {
                            startIdx = _plosionWordBoxes[k].Item1;
                            if (k == boxIdx)
                            {
                                i = k;
                                break;
                            }
                        }
                        else
                        {
                            i = k + 1;
                            break;
                        }
                    }
                    else
                    {
                        i = k + 1;
                        break;
                    }
                }

                startBoxIdx = i + 1;
                var box = DrawBox(startIdx, endIdx, TfBoxType.Plosion);
                if (box == null) return;
                _plosionWordGraphBoxes.Add(i,box);
            }
        }

        public void RemoveAllBoxes()
        {
            while (_normalBoxes.Count > 0)
            {
                _normalBoxes.ElementAt(0).Value.Dispose();
                _normalBoxes.Remove(_normalBoxes.ElementAt(0).Key);
            }
            
            while (_highlightBoxes.Count > 0)
            {
                _highlightBoxes.ElementAt(0).Value.Dispose();
                _highlightBoxes.Remove(_highlightBoxes.ElementAt(0).Key);
            }
            while (_LanguagePointBoxes.Count > 0)
            {
                _LanguagePointBoxes.ElementAt(0).Value.Dispose();
                _LanguagePointBoxes.Remove(_LanguagePointBoxes.ElementAt(0).Key);
            }
            

            while (_newWordBoxes.Count > 0)
            {
                _newWordBoxes.ElementAt(0).Value.Dispose();
                _newWordBoxes.Remove(_newWordBoxes.ElementAt(0).Key);
            }
            
            while (_splitCharBoxes.Count > 0)
            {
                _splitCharBoxes.ElementAt(0).Value.Dispose();
                _splitCharBoxes.Remove(_splitCharBoxes.ElementAt(0).Key);
            }
            
            while (_coreWordGraphBoxes.Count > 0)
            {
                _coreWordGraphBoxes.ElementAt(0).Value.Dispose();
                _coreWordGraphBoxes.Remove(_coreWordGraphBoxes.ElementAt(0).Key);
            }
            
            while (_plosionWordGraphBoxes.Count > 0)
            {
                _plosionWordGraphBoxes.ElementAt(0).Value.Dispose();
                _plosionWordGraphBoxes.Remove(_plosionWordGraphBoxes.ElementAt(0).Key);
            }
            
            while (_liasionWordGraphBoxes.Count > 0)
            {
                _liasionWordGraphBoxes.ElementAt(0).Value.Dispose();
                _liasionWordGraphBoxes.Remove(_liasionWordGraphBoxes.ElementAt(0).Key);
            }

            while (_colorBoxes.Count > 0)
            {
                _colorBoxes.ElementAt(0).Value.Dispose();
                _colorBoxes.Remove(_colorBoxes.ElementAt(0).Key);
            }


        }

        public void RemoveAllcolorBoxes()
        {
            while (_colorBoxes.Count > 0)
            {
                _colorBoxes.ElementAt(0).Value.Dispose();
                _colorBoxes.Remove(_colorBoxes.ElementAt(0).Key);
            }
        }

        private void RemoveAllToneItems()
        {
            while (_toneItems.Count > 0)
            {
                _toneItems[0].Dispose();
                _toneItems.RemoveAt(0);
            }
        }
        
        private void RemoveAllPauseItems()
        {
            while (_pauseItems.Count > 0)
            {
                _pauseItems[0].Dispose();
                _pauseItems.RemoveAt(0);
            }
        }

        public void RemoveAllRichText()
        {
            while (_richTextObjs.Count > 0)
            {
                RecycleRichTextObj(_richTextObjs.Dequeue());
            }
            Debug.Log("_richTextPool len="+ _richTextPool.Count);
        }


        private GTextField DrawColor(int startIdx, int endIdx, Color color = default, bool isStrong = false,
            string word = default, int overrideSize = default, bool isBold = false)
        {
            var aRichTextField = GetRichTextObj();

            return DrawColor(aRichTextField, startIdx, endIdx, color, isStrong, word, overrideSize, isBold);
        }

        private GTextField DrawColor2(int startIdx, int endIdx, Color color = default, bool isStrong = false,
            string word = default, int overrideSize = default, bool isBold = false)
        {
            var aRichTextField = GetRichTextObj();

            return DrawColor(aRichTextField, startIdx, endIdx, color, isStrong, word, overrideSize, isBold);
        }

        private GTextField DrawColor(GRichTextField richTF, int startIdx, int endIdx, Color color = default, bool isStrong = false,
            string word = default, int overrideSize = default, bool isBold = false)
        {
            var aRichTextField = richTF;
            if (word == default)
            {
                word = _richTextWithoutTags.Substring(startIdx, endIdx - startIdx);
            }

            // if (_curShowMode == ShowMode.Normal || _curShowMode == ShowMode.QuestionNormal)
            // {
            //     color = TEXT_COLOR_NORMAL;
            // }
            if (_curShowMode == ShowMode.Follow || _curShowMode == ShowMode.QuestionFollow)
            {
                color = TEXT_COLOR_FOLLOW;
            }


            var hsRGB = ColorUtility.ToHtmlStringRGB(color);
            var prefix2 = isStrong ? "size=" + (_textGft.textFormat.size + STRONG_OFFSET) : "";
            var boldStr0 = isBold ? "<b>" : "";
            var boldStr1 = isBold ? "</b>" : "";
            aRichTextField.text = $"{boldStr0}<font {prefix2} color=#{hsRGB}>{word}</font>{boldStr1}";

            aRichTextField.color = TextGtf.color;
            aRichTextField.size = TextGtf.size;

            TryGetStrPos(startIdx, out var pos);
            aRichTextField.position = new Vector3(pos.x - 2, isStrong ? pos.y - 2 - STRONG_OFFSET : pos.y - 2, 0);
            if (isDebugDrawColor)
            {
                //VFDebug.Log("DrawColor  "+word+"  "+aRichTextField.position+"  startIdx "+startIdx+"  "+(endIdx - startIdx)+" _richTextWithoutTags "+_richTextWithoutTags);
                VFDebug.Log("DrawColor  " + word + " pos:" + aRichTextField.position);
            }

            aRichTextField.textFormat = TextGtf.textFormat;
            if (overrideSize != default)
            {
                aRichTextField.textFormat = new TextFormat()
                {
                    font = TextGtf.textFormat.font,
                    color = TextGtf.textFormat.color,
                    size = overrideSize,
                };
            }
            if (_curShowMode == ShowMode.SpeakEval)
            {
                //每个text 需要独立控制颜色
                aRichTextField.textFormat = new TextFormat()
                {
                    font = TextGtf.textFormat.font,
                    color = TextGtf.textFormat.color,
                    size = TextGtf.textFormat.size,
                };
            }
            if (_outline > 0 && _curShowMode == ShowMode.Follow)
            {
                aRichTextField.textFormat = new TextFormat()
                {
                    font = TextGtf.textFormat.font,
                    color = TextGtf.textFormat.color,
                    size = TextGtf.textFormat.size,
                };
                aRichTextField.textFormat.outline = 0;
                aRichTextField.textFormat.faceDilate = _faceDilate;
                aRichTextField.textFormat.outlineColor = _outlineColor;
            }

            if (_isFairyBatchingEnable)
                aRichTextField.richTextField.fairyBatching = true;
            subtext.AddChild(aRichTextField);
            return aRichTextField;
        }

        private void DrawSplitChar(int startIdx, bool isTyping)
        {
            var size = TextGtf.textFormat.size;
            var rect = new GGraph();
            TryGetStrPos(startIdx, out var startPos);
            var co = new float[] { 4, 4, 4, 4};

            rect.DrawRoundRect(2f, size + 2, isTyping ? BOX_COLOR_HIGHLIGHT : BOX_COLOR_NORMAL, co);
            AddChildAt(rect, this.numChildren);
            rect.position = new Vector3(startPos.x + size/2f, startPos.y + 2f, 0);
            rect.name = "SplitChar";
        }

        //画暂停键
        private void DrawPause(int startIdx)
        {
            GComponent pause = UIPackage.CreateObject("common","PauseCom") as GComponent;
            TryGetStrPos(startIdx, out var startPos);
            pause.xy = new Vector2(startPos.x, startPos.y + 4f);
            _pauseItems.Add(pause);
            AddChildAt(pause, this.numChildren);
        }
        
        //画升降号
        private void DrawTone(int startIdx, RichContentType type, Color color, bool isOverrideColor = false)
        {
            GComponent tone;
            if (type == RichContentType.IntonationDown)
            {
                tone = UIPackage.CreateObject("common", "ToneDownCom") as GComponent;
            }
            else
            {
                tone = UIPackage.CreateObject("common", "ToneUpCom") as GComponent;
            }


            TryGetStrPos(startIdx, out var startPos);
            tone.xy = new Vector2(startPos.x, startPos.y + 4f);

            if (isOverrideColor == false)
            {
                if (_curShowMode == ShowMode.QuestionNormal || _curShowMode == ShowMode.Evaluate)
                {
                    color = TEXT_COLOR_DEFAULT;
                }
                else if (_curShowMode == ShowMode.Follow)
                {
                    color = TEXT_COLOR_FOLLOW;
                    tone.GetTransition("CutIn").Play();
                }
                else if (_curShowMode == ShowMode.QuestionFollow)
                {
                    color = TEXT_COLOR_FOLLOW;
                }
                else if (_curShowMode == ShowMode.Typing)
                {
                    color = TEXT_COLOR_NORMAL;
                }   
            }

            tone.GetChild("icon").asImage.color = color;
            _toneItems.Add(tone);
            AddChildAt(tone, this.numChildren);
        }
        
        private void DrawNewWord(int endIdx,int times)
        {
            var newWordUnderlineCmp = UIPackage.CreateObject("common", "NewWordUnderlineCom") as NewWordUnderlineComponent;
            var box = _newWordBoxes[endIdx];
            newWordUnderlineCmp.xy = new Vector3(box.x, box.y + box.height);
            newWordUnderlineCmp.width = Mathf.Clamp(box.width,14,box.width);
            newWordUnderlineCmp.DrawUnderline(times);
            _newWordCmpts.Add(newWordUnderlineCmp);
            AddChildAt(newWordUnderlineCmp, numChildren - 1);
        }

        //todo 待未来重构
        private void DrawBox4SpeakInit(int startIdx, int endIdx)
        {
            languagePoint = LanguagePoint.grey4speak; //todo 这与drawbox内部分类相关,算是 样式的特殊预定
            DrawBox(startIdx, endIdx, TfBoxType.LanguagePoint);
        }

        //注意：存在firstChar参数补丁是因为 外部的richcontent 到内部textField.text[startIdx]中间在触发 speakInit的流程时（因为引入了font col 00000000），两侧的数据会不一致
        private GGraph DrawBox(int startIdx, int endIdx, TfBoxType tfBoxType, Color color = default)
        {
            if (TextTf.textField.text.Length <= startIdx)  //当文本启用 。。。模式时候 会超出显示的文本长度
            {
                Debug.LogError("DrawBox failed:"+ startIdx +" tf.text.len="+ TextTf.textField.text.Length);
                return null;
            }
            
            
            if (tfBoxType == TfBoxType.Normal)
            {
                color = BOX_COLOR_NORMAL;
            }
            else if (tfBoxType == TfBoxType.HighLight)
            {
                color = BOX_COLOR_HIGHLIGHT;
            }
            else if (tfBoxType == TfBoxType.NewWord)
            {
                color = BOX_COLOR_NEWWORD;
            }
            else if (tfBoxType == TfBoxType.Liaison)
            {
                color = BOX_COLOR_LIAISON;
            }
            else if (tfBoxType == TfBoxType.Plosion)
            {
                color = BOX_COLOR_PLOSION;
            }
         
            else if (tfBoxType == TfBoxType.LanguagePoint )
            {
                switch (languagePoint)
                {
                    case LanguagePoint.green:
                        color = BOX_COLOR_LIGHT_GREEN;
                        break;
                    case LanguagePoint.yellow:
                        color = BOX_COLOR_YELLOW;
                        break;
                    case LanguagePoint.purple:
                        color = BOX_COLOR_LIGHT_PURPLE;
                        break;
                    case LanguagePoint.pink:
                        color = BOX_COLOR_PINK;
                        break;
                    case LanguagePoint.grey:
                        color = BOX_COLOR_WHITE;
                        break;
                    case LanguagePoint.deepGreen:
                        color = BOX_COLOR_DEEP_GREEN;
                        break;
                    case LanguagePoint.grey4speak:
                        color = TEXT_SPEAK_UNHIT_BOX;
                        break;

                }
            }

            var size = TextGtf.textFormat.size;
            var rect = new GGraph();

            char character;

            //character = TextTf.textField.text[startIdx];//content里的idx和   text里的idx已经不一定100%能对齐
            character = _richTextWithoutTags[startIdx];  //[关键修订项]
            //Debug.Log("draw box fisrtchar:"+ character + " startIdx="+ startIdx + "noTagText="+ _richTextWithoutTags);

            var startPos = new Vector2();
            if (char.IsWhiteSpace(character) || character == '\0')
            {
                TryGetStrPos(startIdx + 1, out startPos);
               
            }
            else
            {
                TryGetStrPos(startIdx, out startPos);
            }
            
            TryGetStrPos(endIdx, out var endPos);

            var  co = new float[] { 6, 6, 6, 6 };
            var offset = 4f;

            //grey4speak间接表示文本框是用于speak题的，走圆角
            if (tfBoxType == TfBoxType.color|| tfBoxType == TfBoxType.LanguagePoint && this.languagePoint != LanguagePoint.grey4speak)
            {
                co = new float[] { 0, 0, 0, 0 };
                offset = size / 4+2;
            }


            rect.DrawRoundRect(endPos.x - startPos.x + offset, size + 8f, color, co);
            if(tfBoxType == TfBoxType.color)
            {
                AddChildAt(rect, color== BOX_COLOR_LIGHT_PURPLE?this.numChildren :0);
            }
            else
            {
                AddChildAt(rect, tfBoxType == TfBoxType.NewWord ? this.numChildren : 0);
            }
           
            rect.position = new Vector3(startPos.x - offset/2, startPos.y, 0);
            rect.name = Enum.GetName(typeof(TfBoxType), tfBoxType);
            if (tfBoxType == TfBoxType.Normal)
            {
                _normalBoxes.Add(endIdx, rect);
            }
            else if (tfBoxType == TfBoxType.HighLight)
            {
                _highlightBoxes.Add(endIdx, rect);
            }
            else if (tfBoxType == TfBoxType.LanguagePoint)
            {
                _LanguagePointBoxes.Add(endIdx, rect);
            }

            return rect;
        }
        
        private QuestionAnswerComponent DrawMask(int startIdx, int endIdx, AnswerType type)
        {
            TryGetStrPos(startIdx, out var startPos);
            TryGetStrPos(endIdx, out var endPos);
            // var image = UIPackage.CreateObject("common",
            //     type == AnswerType.Normal ? "card_word_outline_white_28" :
            //     type == AnswerType.Correct ? "card_word_outline_green_28" : "card_word_outline_red_28").asImage;
            // image.xy = new Vector2(startPos.x - 1f, startPos.y);
            // image.size = new Vector2( endPos.x - startPos.x + 2f,  TextGtf.textFormat.size * 1.4f);
            var cmp = UIPackage.CreateObject("common", "QuestionAnswerCom") as QuestionAnswerComponent;
            cmp.xy = new Vector2(startPos.x - 1f, startPos.y);
            AddChild(cmp);
            _answerCmpts.Add(cmp);
            return cmp;
        }

        private void PrintWords() {
            var testStr = "";
            foreach (var word in this._newWords.Values)
            {
                testStr += word + "_";
            }
            Debug.LogError("SplitWords:" + testStr);
        }


        private static readonly Regex PrecompiledRegex = new Regex(
            @"^[\s\p{P}\p{S}]+$",
            RegexOptions.Compiled // 关键选项：预编译为IL代码
        );
        public static bool IsWordSeparator(string x)
        {
            // 空字符串直接返回false
            if (string.IsNullOrEmpty(x)) return false;

            // 正则表达式说明：
            // ^              匹配字符串开头
            // [              开始字符组
            //   \s           匹配所有空白字符（空格、制表符、换行等）
            //   \p{P}        匹配所有Unicode标点符号（包括各种括号、引号、句号等）
            //   \p{S}        匹配所有Unicode符号（如货币符号、数学符号等）
            // ]              结束字符组
            // +              匹配一个或多个前述字符
            // $              匹配字符串结尾
            return PrecompiledRegex.IsMatch(x);
        }


        public string evalText = string.Empty;


        private bool isDebugDrawColor = false;

        public void DrawRichContent(bool allowDrawByWord) {
            var curIdx = 0;

            //逐字的create步骤，如果是eval的流程会跳过
            //目前渲染流程较为混乱， 这里的逐字之后 eval也“可能”逐字渲染
            while (curIdx < Length)
            {
                if (_isMaxRowEnable && _isBreak)
                {
                    if (curIdx > _maxIndex - 4)
                    {
                        // -3是因为修正省略号长度
                        break;
                    }
                }
                var content = GetRichContent(curIdx);

                if (allowDrawByWord)
                {
                    //逐词
                    CreateContent(content, curIdx,true);//

                    curIdx++;
                }
                else {
                    //逐字
                    CreateContent(content, curIdx,false);//

                    curIdx++;
                }
                
            }
        }
        //逐字or逐词做 叠加型染色
        public void DrawEvaluateColor()
        {
            var lastIdx = 0;
            _evaluatedWords.Clear();
            evalText = string.Empty;

            var richTextNoTag = _richTextWithoutTags.ToLower();

            string testChar1,testChar2;
            bool isValid1,isValid2;

            Debug.Log("====DrawEvaluateColor _richTextPool len=" + _richTextPool.Count +"====") ;

            //_evaluateResult的结果，只是源自原文，如果不考虑外部clear效果，
            //实际的drawcol效果会是以叠加绘制 新的文本框

            var len = _evaluateResult.Count;
            MatchResult matchResult;
            for (int i=0;i<len;i++)
            {
                matchResult = _evaluateResult[i];
                var str = matchResult.DisplayWord;//含有标点的
                var color = matchResult.Color;

                var singleWord = matchResult.MatchedWord.ToLower();

                //注意1：计算idx drawColor 将只对特定位置片段的词汇渲染
                var idx = richTextNoTag.IndexOf(singleWord, lastIdx);

                //校验singleWord的左右两个char 是否为分隔符号；idx的IndexOf判定法过于不严格
                isValid1 = true;
                isValid2 = true;
                if (idx-1 >= 0 )
                {
                    testChar1 = richTextNoTag.Substring(idx-1, 1);
                    isValid1 = IsWordSeparator(testChar1);
                }

                if (idx + str.Length < richTextNoTag.Length - 1) {
                    testChar2 = richTextNoTag.Substring(idx + str.Length, 1);
                    isValid2 = IsWordSeparator(testChar2);
                }


                //bool hasWord = wordsSet.Count > 0 ? wordsSet.Contains<string>(singleWord) : true; /

                ///此前缺少 整词比较，直接陷入了逐字比较，当词汇中有 "to" 等部分时
                if (idx > -1 && isValid1 && isValid2)
                {
                    var textList = new List<GTextField>();

                    //for (var i = 0; i < str.Length; i++)
                    //{
                    //    //DrawColor 从_richTextWithoutTags提取
                    //    var textField = DrawColor(idx + i, idx + i + 1, color, IsStrongFont(idx));
                    //    textList.Add(textField);
                    //    evalText += textField.text +"&";
                    //}

                    isDebugDrawColor = true;
                    var textField = DrawColor2(idx + 0, idx + str.Length, color, IsStrongFont(idx));
                    isDebugDrawColor = false;

                    textList.Add(textField);
                    evalText += $"{textField.text}[{matchResult.DisplayWord}]&";
                    Debug.Log($"{textField.text}[{matchResult.DisplayWord}]&");

                    lastIdx = idx + str.Length;
                    if (color == TEXT_COLOR_BAD)
                        _evaluatedWords.Add((idx + str.Length / 2), textList);
                }
                
            }
            if(EnableCache)
            {
                subtext.displayObject.cacheAsBitmap = true;
            }
        }


        //为了speak题和 多灵国里的录音染色题
        public void DrawEvaluateColorForTextField()
        {
            var lastIdx = 0;
            var origLastIdx = 0;


            var richText_NoTag_Lowered = _richTextWithoutTags.ToLower();
            string richTextOrig = _richTextWithoutTags.Substring(0);//强制赋值,有正确的大小写
            
            string testChar1, testChar2;
            bool isValid1, isValid2;

            

            //_evaluateResult的结果，只是源自原文，如果不考虑外部clear效果，
            //实际的drawcol效果会是以叠加绘制 新的文本框

            var len = _evaluateResult.Count;
            string replaceStr;
            MatchResult matchResult;
            
            for (int i = 0; i < len; i++)
            {
                matchResult = _evaluateResult[i];
                var singleWordDisplay = matchResult.DisplayWord;//含有标点的, 被隐藏时为空串
                var singleWord = matchResult.MatchedWord;//已经是全小写的状态了
                var singleWordOrig = matchResult.OrigWord;

                var color = matchResult.Color;

                
                

                //注意1：计算idx drawColor 将只对特定位置片段的词汇渲染
                var idx = richText_NoTag_Lowered.IndexOf(singleWord, lastIdx);
                var origIdx = richTextOrig.IndexOf(singleWordOrig, origLastIdx);
                //Debug.Log($"DrawCol2=> isPunc:{matchResult.IsPunctuation } singleWord={singleWord} idx={idx} || singleWordOrig={singleWordOrig} origIdx={origIdx} ");

                //校验singleWord的左右两个char 是否为分隔符号；idx的IndexOf判定法过于不严格
                isValid1 = true;
                isValid2 = true;

                if (!matchResult.IsPunctuation)
                {
                    //不是标点时，特征word片段 需要是整个word全匹配：比如word为to，那么togther里的to不属于全匹配
                    if (idx - 1 >= 0)
                    {
                        testChar1 = richText_NoTag_Lowered.Substring(idx - 1, 1);
                        isValid1 = IsWordSeparator(testChar1);
                    }

                    if (idx + singleWordOrig.Length < richText_NoTag_Lowered.Length - 1)
                    {
                        testChar2 = richText_NoTag_Lowered.Substring(idx + singleWordOrig.Length, 1);
                        isValid2 = IsWordSeparator(testChar2);
                    }
                }
               


                //bool hasWord = wordsSet.Count > 0 ? wordsSet.Contains<string>(singleWord) : true
                //此前缺少 整词比较，直接陷入了逐字比较，当词汇中有 "to" 等部分时

                if (idx > -1 && origIdx > -1 && isValid1 && isValid2)
                {

                    //var textList = new List<GTextField>();

                    //var textField = DrawColor2(idx + 0, idx + str.Length, color, IsStrongFont(idx));
                    var hsRGB = ColorUtility.ToHtmlStringRGB(color);
                    if (singleWordDisplay == string.Empty) {

                        // 当前词汇被hideDisplay过,透明色跳过
                        hsRGB = "00000000";
                    } 

                    replaceStr = $"<font color=#{hsRGB}>{singleWordOrig}</font>";

                    richTextOrig = richTextOrig.Substring(0, origIdx) + replaceStr + richTextOrig.Substring(origIdx + singleWord.Length);

                    //richTextOrig = richTextOrig.Replace(singleWordOrig, replaceStr) ;不安全，singleWordOrig可能是短词 包括 a  the 等

                    Debug.Log($"===DrawEvaluateColorForTextField single={singleWordOrig} full={richTextOrig}");

                    lastIdx = idx + singleWord.Length;
                    origLastIdx = origIdx + replaceStr.Length;

                    //if (color == TEXT_COLOR_BAD)
                    //    _evaluatedWords.Add((idx + str.Length / 2), textList);
                }
                else
                {
                    //Debug.Log($"===else single={singleWordOrig} isValid1:{isValid1}  isValid2:{isValid2} idx+str.Len:{idx + str.Length } rLen-1:{richText_NoTag_Lowered.Length - 1}");
                }
            }
            if (EnableCache)
            {
                subtext.displayObject.cacheAsBitmap = true;
            }
            
            _richText = richTextOrig;
            _textGft.text = _richText;//最后把textfield
            
            Debug.Log("===DrawEvaluateColorForTextField a4=" + richTextOrig);
        }


        private bool IsStrongFont(int idx)
        {
            var curIdx = 0;
            foreach (var content in _richContents)
            {
                if (idx >= curIdx && idx < curIdx + content.Length && content.Type == RichContentType.Prominent)
                {
                    return true;
                }
                curIdx += content.Length;
            }
            return false;
        }

        private void DrawMaskAsUnderline()
        {
            foreach (var content in _richContents)
            {
                if (content.Type == RichContentType.Mask || content.Type == RichContentType.MaskTranslate)
                {
                    DrawUnderline(content.StartIdx, content.EndIdx);
                }
            }
        }

        public void DrawMaskAsBox4SpeakInit()
        {
            foreach (var content in _richContents)
            {
                if (content.Type == RichContentType.Mask)
                {
                    DrawBox4SpeakInit(content.StartIdx, content.EndIdx);
                }
            }
        }

        //绘制box区域： 新建or改色
        //这里的text需要用orig的文本，_richContents内是原始的
        public void DrawMaskAsBoxBytext(string text,Color color)
        {
            foreach (var content in _richContents)
            {
                if (content.Type == RichContentType.Mask && content.Content.Contains(text))//todo 这里的条件应该是==更准确
                {
                    if (_LanguagePointBoxes.ContainsKey(content.EndIdx))
                    {
                        _LanguagePointBoxes[content.EndIdx].color = color;
                        return;
                    }
                    else
                    {

                        var box = DrawBox(content.StartIdx, content.EndIdx, TfBoxType.LanguagePoint, color);//此步骤已经可能存在_LanguagePointBoxes add行为

                        if (box != null)
                        {
                            box.color = color;
                            //防御写法
                            if (!_LanguagePointBoxes.ContainsKey(content.EndIdx))
                            {
                                this._LanguagePointBoxes.Add(content.EndIdx, box);
                            }
                        }
                    }


                    //this._colorBoxes.Add(content.EndIdx, box);
                    //Debug.Log("DrawBox succ text=" + text);
                }
                else {
                    //Debug.Log("DrawBox fail text="+ text);
                }
                
            }
            

        }



        private void DrawQuestionNormalTone()
        {
            foreach (var content in _richContents)
            {
                if (content.Type == RichContentType.IntonationDown || content.Type == RichContentType.IntonationUp)
                {
                    DrawTone(content.StartIdx, content.Type, TEXT_COLOR_DEFAULT, true);
                }
            }
        }

        private string SplitWordsPattern = @"\b(([A-Za-z]+(['-][A-Za-z]+)*)|([A-Z]\.){2,})|\$?\d+(?:\.\d+)?%?|\w+(-\w+)*";
        private void SplitWords()
        {
            if (_newWords.Count > 0)
                return;
            

            var matches = Regex.Matches(_richTextWithoutTags, SplitWordsPattern, 
                RegexOptions.IgnoreCase | RegexOptions.ExplicitCapture);

            foreach (Match match in matches)
            {
                if (match.Success)
                {
                    int start = match.Index;
                    int end = start + match.Length - 1;
                    string word = match.Value;
                    
                    // 处理特殊情况：带点的缩写（如Mr.、U.S.A.）
                    if (Regex.IsMatch(word, @"^([A-Za-z]\.)+$"))
                    {
                        // 确保不分割缩写中的点
                        _newWords[(start, end)] = word;
                        continue;
                    }

                    // 处理带连字符的复合词（如state-of-the-art）
                    if (word.Contains('-') && word.Count(c => c == '-') == 1)
                    {
                        _newWords[(start, end)] = word;
                        continue;
                    }

                    // 排除纯数字的情况
                    if (!Regex.IsMatch(word, @"^\d+$"))
                    {
                        _newWords[(start, end)] = word;
                    }
                }
            }
        }

        void BindClickEventRecursively(int index, EventCallback0 clickAction, List<(int, int)> wordKeys, HashSet<int> processedBoxes)
        {
            if (index < 0 || index >= wordKeys.Count) return; // 越界检查

            var key = wordKeys[index];

            if (GetRichContent(key.Item1).Type != RichContentType.Mask) return;
            // 避免重复绑定
            if (!processedBoxes.Add(key.Item2)) return;

            if (_newWordBoxes.TryGetValue(key.Item2, out var box))
            {
                box.onClick.Add(clickAction);
            }

            // 绑定相邻的前后 Box（确保邻居的邻居也触发）
            if (index > 0) BindClickEventRecursively(index - 1, clickAction,  wordKeys, processedBoxes);
            if (index < wordKeys.Count - 1) BindClickEventRecursively(index + 1, clickAction, wordKeys, processedBoxes);
        }

        
        private void AddDynamicBtn()
        {
            
            
            if (_newWordBoxes.Count > 0)
                return;
            // 1. 先创建所有 Box，并存入 _newWordBoxes，确保后续事件绑定不会丢失
            List<(int, int)> wordKeys = _newWords.Keys.ToList(); // 取出 key 顺序
            // 1. 先创建所有 Box 并存入 _newWordBoxes
            foreach (var item in _newWords)
            {
                var box = DrawBox(item.Key.Item1, item.Key.Item2, TfBoxType.NewWord);
                if (box != null)
                {
                    _newWordBoxes[item.Key.Item2] = box;
                }
            }
            // 2. 绑定点击事件，并确保邻居的邻居也绑定
            
                                                 // 3. 遍历所有元素，为每个 box 绑定点击事件
            for (int i = 0; i < wordKeys.Count; i++)
            {
                var key = wordKeys[i];

                if (!_newWordBoxes.TryGetValue(key.Item2, out var box)) continue;

                if (GetRichContent(key.Item1).Type == RichContentType.Mask)
                {
                    // 定义点击事件
                    void OnClickAction()
                    {
                        if (IsTyping) return;
                        var MaskTipsCmpt = UIPackage.CreateObject("common", "NewWordCom") as NewWordComponent;
                        MaskTipsCmpt.parentTfExt = this;
                        MaskTipsCmpt.word = _newWords[key];
                        var wordPos = box.parent.LocalToGlobal(new Vector3(box.x + box.width / 2f, box.y + box.height));
                        OnClickMaskTips?.Invoke(MaskTipsCmpt, wordPos);
                    }

                    // 绑定当前 Box 的点击事件
                    box.onClick.Add(OnClickAction);
                    HashSet<int> processedBoxes = new(); // 记录已处理 Box，防止重复绑定
                    // 绑定邻居及其邻居
                    BindClickEventRecursively(i, OnClickAction, wordKeys,  processedBoxes);
                }
                else
                {
                    box.onClick.Add(() =>
                    {
                        if (IsTyping)
                            return;
                        var newWordCmpt = UIPackage.CreateObject("common", "NewWordCom") as NewWordComponent;
                        newWordCmpt.parentTfExt = this;
                        newWordCmpt.word = _newWords[key];
                        var wordPos = box.parent.LocalToGlobal(new Vector3(box.x + box.width / 2f, box.y));
                        OnClickNewWord?.Invoke(newWordCmpt, wordPos);
                    });
                }
                    
                
                
            }
        }

        private void RemoveNewWords()
        {
            _newWords.Clear();
            foreach (var item in _newWordBoxes)
            { 
                item.Value.Dispose();
            }
            _newWordBoxes.Clear();
            OnClickNewWord = null;
        }
        
        public void RemoveNewWordsOutClick()
        {
            _newWords.Clear();
            foreach (var item in _newWordBoxes)
            {
                item.Value.Dispose();
            }
            _newWordBoxes.Clear();
        }
        
        private void DrawUnderline(int startIdx = -1, int endIdx = 99999999)
        {
            var idx = 0;
            for (var i = 0; i < TextTf.textField.charPositions.Count; i++)
            {
                if (!(i >= startIdx && i < endIdx))
                    continue;

                var c = TextTf.textField.charPositions[i];
                var curChar = GetStr(idx, out var isLast);
                var l = TextTf.textField.lines[c.lineIndex];
                GGraph holder = new GGraph();
                holder.SetSize(100, 100);
                holder.SetPosition(c.offsetX + 1, l.y + l.height + UnderlineOffset, 0);
                Color nowColor;
                ColorUtility.TryParseHtmlString("#6F6F73", out nowColor);
                if (!string.IsNullOrEmpty(curChar))
                    holder.DrawRect(c.width+1, l.height / 28, 1, nowColor, nowColor);
                _graphs.Add(holder);
                AddChild(holder);
                holder.name = "Underline";
                idx++;
            }
            
            InvalidateBatchingState();            
        }
        
        public Rect[] GetMaskBounds()
        {
            var rects = new List<Rect>();
            foreach (var content in _richContents)
            {
                if (content.Type == RichContentType.Mask)
                {
                    rects.Add(GetCharBounds(content.StartIdx, content.EndIdx));
                }
            }
            return rects.ToArray();
        }

        /// <param name="startIdx">included</param>
        /// <param name="endIdx">excluded</param>
        private Rect GetCharBounds(int startIdx, int endIdx)
        {
            Rect rect = Rect.zero;
            for (var i = startIdx; i < endIdx; i++)
            {
                var c = TextTf.textField.charPositions[i];
                var curChar = GetStr(i, out var isLast);
                var l = TextTf.textField.lines[c.lineIndex];
                if (!string.IsNullOrEmpty(curChar))
                {
                    if (rect.width == 0)
                    {
                        // 建立一个新的
                        rect = new Rect(c.offsetX, l.y, c.width, l.height);
                    }else
                    {
                        // 扩宽 
                        rect.width += c.width;
                    }
                }
            }
            return rect;            
        }
      

        private Vector2 GetEndStrPos()
        {
            var c = TextTf.textField.charPositions[TextGtf.text.Length - 1];
            var l = TextTf.textField.lines[c.lineIndex];
            return new Vector2(c.offsetX + c.width + 1, l.y + 2);
        }
        
        private bool TryGetStrPos(int idx, out Vector2 pos)
        {
            pos = Vector2.zero;
            
            if (TextTf.textField.charPositions.Count > idx)
            {
                var c = TextTf.textField.charPositions[idx];
                var l = TextTf.textField.lines[c.lineIndex];
                var yFixed = l.y + (l.baseline - TextTf.textField.textFormat.size);
                pos = new Vector2(c.offsetX, yFixed);
                return true;
            }
            
            return false;
        }
        
        private int GetStrLine(int idx)
        {
            if (TextTf.textField.charPositions.Count > idx)
            {
                var c = TextTf.textField.charPositions[idx];
                return c.lineIndex;
            }

            return 0;
        }

        private string GetStr(int idx, out bool isLast)
        {
            var curIdx = 0;
            var curContentIdx = 0;
            isLast = false;
            
            foreach (var str in _richContents)
            {
                if (idx < curIdx + str.Content.Length)
                {
                    _curContentIdx = curContentIdx;
                    if (idx - curIdx == str.Content.Length - 1)
                        isLast = true;
                    // VFDebug.LogError("idx "+idx+" curIdx "+curIdx+"  str.Content "+str.Content);
                    return str.Content[idx - curIdx].ToString();
                }
                curIdx += str.Content.Length;
                curContentIdx++;
            }

            return null;
        }

        private RichContent GetRichContent(int idx)
        {
            var curIdx = 0;
            var curContentIdx = 0;

            foreach (var content in _richContents)
            {
                if (idx < curIdx + content.Length)
                {
                    _curContentIdx = curContentIdx;
                    return content;
                }
                curIdx += content.Length;
                curContentIdx++;
            }
            return new RichContent();
        }

        public List<RichContent> GetAllContents()
        {
            return _richContents;
        }

        private GRichTextField GetRichTextObj()
        {
            GRichTextField t;
            if (_richTextPool.Count > 0)
            {
                t = _richTextPool.Dequeue();
                if (_outline > 0)
                {
                    t.textFormat.outline = 0f;
                    t.textFormat.faceDilate = 0f;
                }
            }
            else
            {
                t = new GRichTextField();
            }
            
            _richTextObjs.Enqueue(t);
            t.visible = true;
            return t;
        }

        private GRichTextField GetRichTextObj2()
        {
            GRichTextField t;
         
            t = new GRichTextField();
         

            _richTextObjs.Enqueue(t);
            t.visible = true;
            return t;
        }

        private void RecycleRichTextObj(GRichTextField t)
        {
            t.visible = false;
            _richTextPool.Enqueue(t);
        }

        protected override void OnUpdate()
        {
            base.OnUpdate();
            if (_isTyping && _curShowMode == ShowMode.Typing)
            {
                if (_TypeEndChar == null)
                {
                    _TypeEndChar = new GGraph();
                    _TypeEndChar.DrawRect(3, TextGtf.textFormat.size + 4, 1, Color.black, Color.black);
                    AddChild(_TypeEndChar);
                    _TypeEndChar.name = "TypeEndChar";
                }
                
                if (TryGetStrPos(_isMaxRowEnable && _isBreak && _curIdx > _maxIndex ? _maxIndex - 3 : _curIdx + 1, out var pos))
                {
                    _TypeEndChar.SetPosition(pos.x, pos.y, 0);
                    _TypeEndChar.visible = Mathf.Abs(Time.time - Mathf.Floor(Time.time) - 0.5f) > 0.25f;
                }
            }
            else if(!_isTyping && _TypeEndChar != null && _TypeEndChar.visible)
            {
                _TypeEndChar.visible = false;
            }
        }

        private void ClearUnderDot()
        {
            foreach (var comp in _dotComps)
            {
                comp.Dispose();
            }
            _dotComps.Clear();
        }
        
        private void DrawUnderDot(int startIdx, int endIdx,bool noSpace,int initIdx = 0,bool needGreen = false)
        {
            int idx = initIdx;
            int lintIdx = -1;//行数
            bool changeLine = false;//是否换行
            int sIndex = 0;

            endIdx = math.min(endIdx, TextTf.textField.charPositions.Count - 1);
            
            for (int i = 0; i < TextTf.textField.charPositions.Count; i++)
            {
                if (i < startIdx)
                {
                    idx++;
                    continue;
                }
                if (i > endIdx)
                    break;

                var c = TextTf.textField.charPositions[i];
                var curChar = GetStr(idx, out var isLast);//理论上可简化
                var l = TextTf.textField.lines[c.lineIndex];
                
                if (i + 1 < endIdx)
                {
                    var nextC = TextTf.textField.charPositions[i + 1];
                    changeLine = c.lineIndex != nextC.lineIndex;
                }

                if (lintIdx == -1)
                {
                    if (!noSpace || curChar != " ")
                    {
                        lintIdx = c.lineIndex;
                        sIndex = i;
                    }
                }

                // 重置了  且   不要空格 当前是空格 需要挖空    或      换行了
                if (lintIdx >= 0 && (c.lineIndex != lintIdx || (noSpace && curChar == " ") || changeLine ||
                                     i == endIdx))
                {
                    if (i > 0 && sIndex != i)
                    {
                        float sPos = TextTf.textField.charPositions[sIndex].offsetX + 1;

                        //20250527 noSpace == false表示包含空格
                        //case1 不画空格时，但遇到空格时，以空格为 eIndex 
                        //case2 遇到最后一个符号时，以空格为 eIndex
                        //case3 换行类case，lineIndex != lintIdx || changeLine  未实际多轮测试

                        int eIndex = noSpace && curChar == " " ||  i == endIdx ? i : i - 1;// 20250528 修订最后一位char计算取值总是偏移的问题

                        float ePos = TextTf.textField.charPositions[eIndex].offsetX +
                                     (changeLine ? TextTf.textField.charPositions[eIndex].width : 1);

                        //Debug.Log($"underline: curChar={curChar} raw={this._richTextWithoutTags} sIndex ={sIndex} eIndex={eIndex} i={i } endIdx={endIdx} noSpace={noSpace}");

                        var lastLineInfo = TextTf.textField.lines[lintIdx];
                        var dotComp = UIPackage.CreateObject("common", "NewWordUnderDotCom") as GComponent;
                        if (dotComp == null) return;
                        dotComp.xy = new Vector3(sPos, lastLineInfo.y + l.height + 5, 0);
                        dotComp.GetController("state").selectedIndex = needGreen ? 1 : 0;

                        //var imgWidth = Mathf.Floor((ePos - sPos)/8) * 8f; // 点的32px x0.125 =8px，间距32px0.125=8px宽，64px=1点+1间距
                        var imgWidth = ePos - sPos; //先不做上下取整看看

                        //todo:from raybit 20250501发现 获取每句话最后的eIndex值 低于预期，到时dot添加时最后一个词经常描的不完整

                        dotComp.GetChild("imgDot").width = imgWidth*8;//因为imgDot 有0.125的scale
                        _dotComps.Add(dotComp);
                        AddChild(dotComp);

                        lintIdx = -1;
                    }
                }

                idx++;
            }
            
            InvalidateBatchingState();
            
        }

        public void ResizeToFit()
        {
            width = TextTf.width;
            height = TextTf.height;
        }

        #endregion

        #region 词语命中标记

        private List<GComponent> _hitPointCom = new List<GComponent>();
        public void ShowHitPointFlag()
        {
            foreach (var content in _richContents)
            {
                if (content.IsLanguagePoint && content.LanguagePointLast)
                {
                    AddHitPointFlag(content.StartIdx,content.EndIdx);
                }
            }
        }
        
        private void AddHitPointFlag(int startIdx, int endIdx, HitPointFlagType type = HitPointFlagType.Normal)
        {
            TryGetStrPos(endIdx, out var endPos);
       
            GComponent com = UIPackage.CreateObject("Chat", "HitPointSpint") as GComponent;
            
            if (com != null)
            {
                com.xy = new Vector2(endPos.x - 1f, endPos.y);
                AddChild(com);
                GImage imgEnd = com.GetChild("imgEnd") as GImage;
                imgEnd.visible = false;
                GLoader3D spineLoader = com.GetChild("spineEffect") as GLoader3D;
                spineLoader.visible = true;
                spineLoader.animationName = "2";
                spineLoader.loop = false;
                spineLoader.playing = true;
                _hitPointCom.Add(com); 
                
                // 获取 Spine 组件
                SkeletonAnimation skeletonAnimation = spineLoader.displayObject.gameObject.GetComponentInChildren<SkeletonAnimation>();
    
                if (skeletonAnimation != null)
                {
                    skeletonAnimation.AnimationState.Complete += (trackEntry) =>
                    {
                        // Debug.LogError("spine播放完毕");
                        imgEnd.visible = true;
                        spineLoader.visible = false;
                    };
                }
            }
        }
        private void DisposeHitPointRes()
        {
            for (int i = 0; i < _hitPointCom.Count; i++)
            {
                var com = _hitPointCom[i];
                if (com != null)
                {
                    RemoveChild(com);
                    com.Dispose();  
                    _hitPointCom[i] = null; 
                }
            }
            _hitPointCom.Clear(); 
        }

        #endregion
    }
}
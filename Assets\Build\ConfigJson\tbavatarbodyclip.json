[{"clipKey": "Idle", "clipName": "Idle", "canBlend": true, "blendIn": 1, "blendOut": 1, "canLoop": true, "loopMax": 1666666600.0, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "State1-1", "clipName": "State1-1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State1-2", "clipName": "State1-2", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State1-3", "clipName": "State1-3", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State1-4", "clipName": "State1-4", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State1-5", "clipName": "State1-5", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State1-6", "clipName": "State1-6", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State2-1", "clipName": "State2-1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State2-2", "clipName": "State2-2", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State2-3", "clipName": "State2-3", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State2-4", "clipName": "State2-4", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State2-5", "clipName": "State2-5", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State2-6", "clipName": "State2-6", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State3-1", "clipName": "State3-1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "State3-2", "clipName": "State3-2", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.75, "speedRangeMax": 1.5, "footIK": false, "ModifierI": false}, {"clipKey": "PointSelf1", "clipName": "PointSelf1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.66, "speedRangeMax": 1.66, "footIK": false, "ModifierI": false}, {"clipKey": "PointSelf2", "clipName": "PointSelf2", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.66, "speedRangeMax": 1.66, "footIK": false, "ModifierI": false}, {"clipKey": "PointPlayer1", "clipName": "PointPlayer1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.66, "speedRangeMax": 1.66, "footIK": false, "ModifierI": false}, {"clipKey": "PointPlayer2", "clipName": "PointPlayer2", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.66, "speedRangeMax": 1.66, "footIK": false, "ModifierI": false}, {"clipKey": "TalkingIdle", "clipName": "TalkingIdle", "canBlend": true, "blendIn": 1, "blendOut": 1, "canLoop": true, "loopMax": 1666666600.0, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "DeepIdle1-1", "clipName": "DeepIdle1-1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "DeepIdle1-2", "clipName": "DeepIdle1-2", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 88, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "DeepIdle1-3", "clipName": "DeepIdle1-3", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "DeepIdle2-1", "clipName": "DeepIdle2-1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "DeepIdle2-2", "clipName": "DeepIdle2-2", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 88, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "DeepIdle2-3", "clipName": "DeepIdle2-3", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "ShallowIdle1", "clipName": "ShallowIdle1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "ModifierI": false}, {"clipKey": "ShallowIdle2", "clipName": "ShallowIdle2", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 7, "loopBlendTime": 0.2, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "ModifierI": false}, {"clipKey": "ShallowIdle3", "clipName": "ShallowIdle3", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "ModifierI": false}, {"clipKey": "PointPlayer1-1", "clipName": "PointPlayer1-1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "ModifierI": false}, {"clipKey": "PointPlayer1-2", "clipName": "PointPlayer1-2", "canBlend": true, "blendIn": 0, "blendOut": 0.8, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "ModifierI": false}, {"clipKey": "Correct1", "clipName": "Correct1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "Correct2", "clipName": "Correct2", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "Correct3", "clipName": "Correct3", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "Recording1", "clipName": "Recording1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "Recording2", "clipName": "Recording2", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 2147483600.0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "Recording3", "clipName": "Recording3", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "StageBegin", "clipName": "StageBegin", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "StageEnd", "clipName": "StageEnd", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "Win5", "clipName": "Win5", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "Win10", "clipName": "Win10", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}, {"clipKey": "Wrong1", "clipName": "Wrong1", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "ModifierI": false}]
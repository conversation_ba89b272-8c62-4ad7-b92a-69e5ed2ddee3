/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Settlement
{
    public partial class SettlementCommonPanel : UIBindT
    {
        public override string pkgName => "Settlement";
        public override string comName => "SettlementCommonPanel";

        public GImage imgBG;
        public CompSettleCommon CompSettle;
        public CompBottom CompBottom;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            imgBG = (GImage)com.GetChildAt(0);
            CompSettle = new CompSettleCommon();
            CompSettle.Construct(com.GetChildAt(1).asCom);
            CompBottom = new CompBottom();
            CompBottom.Construct(com.GetChildAt(2).asCom);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            imgBG = null;
            CompSettle.Dispose();
            CompSettle = null;
            CompBottom.Dispose();
            CompBottom = null;
        }
    }
}
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Settlement
{
    public partial class CompAUASettle : UIBindT
    {
        public override string pkgName => "Settlement";
        public override string comName => "CompAUASettle";

        public GLoader3D ldr3dFlow;
        public GGraph effectPosFlag;
        public GTextField tfCongra;
        public CompSettleList compSettleList;
        public AUAContentComp compContent;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ldr3dFlow = (GLoader3D)com.GetChildAt(0);
            effectPosFlag = (GGraph)com.GetChildAt(1);
            tfCongra = (GTextField)com.GetChildAt(2);
            compSettleList = (CompSettleList)com.GetChildAt(3);
            compContent = new AUAContentComp();
            compContent.Construct(com.GetChildAt(4).asCom);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ldr3dFlow = null;
            effectPosFlag = null;
            tfCongra = null;
            compSettleList = null;
            compContent.Dispose();
            compContent = null;
        }
    }
}
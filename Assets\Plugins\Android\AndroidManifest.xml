﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.unity3d.player" android:installLocation="preferExternal" xmlns:tools="http://schemas.android.com/tools">
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <!-- <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" tools:node="remove"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" tools:node="remove"/> -->
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="com.android.vending.BILLING" />
  <!-- Optional permissions. Will pass Lat/Lon values when available. Choose either Coarse or Fine -->
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" tools:replace="android:name" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" tools:replace="android:name" />
  <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <!-- 这个权限允许应用程序访问消息推送 -->
  <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
  <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <!-- 这个权限允许应用程序注册和取消注册消息推送 -->
  <uses-permission android:name="android.permission.VIBRATE" />
  <!-- 这个权限允许应用程序接收来自FCM的消息 -->
  <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
  <permission android:name="visionflow.ai.permission.C2D_MESSAGE" android:protectionLevel="signature" />
  <uses-permission android:name="visionflow.ai.permission.C2D_MESSAGE" />
  <uses-permission android:name="visionflow.ai.permission.JPUSH_MESSAGE" />
  <uses-permission android:name="visionflow.ai.permission.JOPERATE_MESSAGE" />
  <queries>
    <package android:name="com.google.android.googlequicksearchbox" />
  </queries>
  <uses-permission android:name="android.permission.RECORD_AUDIO" />
  <uses-feature android:name="android.hardware.type.pc" android:required="false" />
  <supports-screens android:smallScreens="true" android:normalScreens="true" android:largeScreens="true" android:xlargeScreens="true" android:anyDensity="true" />
  <application android:theme="@android:style/Theme.NoTitleBar.Fullscreen" android:icon="@drawable/app_icon" android:label="@string/app_name" android:debuggable="false" android:usesCleartextTraffic="true">
    <activity android:name="com.unity3d.player.UnityPlayerActivity" android:label="@string/app_name">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
      </intent-filter>
      <meta-data android:name="unityplayer.UnityActivity" android:value="true" />
    </activity>
    <uses-library android:name="org.apache.http.legacy" android:required="false" />
    <activity android:name="com.facebook.unity.FBUnityLoginActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen" android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
    <activity android:name="com.facebook.unity.FBUnityDialogsActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen" android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
    <activity android:name="com.facebook.unity.FBUnityGamingServicesFriendFinderActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen" android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
    <activity android:name="com.facebook.unity.FBUnityAppLinkActivity" android:exported="true" />
    <activity android:name="com.facebook.unity.FBUnityDeepLinkingActivity" android:exported="true" />
    <activity android:name="com.facebook.unity.FBUnityGameRequestActivity" />
    <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="fb740531045139149" />
    <meta-data android:name="com.facebook.sdk.ClientToken" android:value="8d600d2dd68658467d970fbd6d32d243" />
    <meta-data android:name="com.facebook.sdk.AutoLogAppEventsEnabled" android:value="true" />
    <meta-data android:name="com.facebook.sdk.AdvertiserIDCollectionEnabled" android:value="true" />
    <provider android:name="com.facebook.FacebookContentProvider" android:authorities="com.facebook.app.FacebookContentProvider740531045139149" android:exported="true" />
  </application>
</manifest>
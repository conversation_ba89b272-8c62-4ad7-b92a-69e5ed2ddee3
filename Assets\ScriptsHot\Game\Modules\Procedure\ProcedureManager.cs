﻿using ScriptsHot.Game.Modules.Procedure;
using System;
using System.Collections.Generic;
using ScriptsHot.Game.Modules.Consts;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Procedure
{
    public partial class ProcedureManager
    {
        public List<BaseDrama> Dramas { get; private set; } = new List<BaseDrama>();
        public int ProcessingIndex { get; private set; }
        public bool IsRunning { get; private set; }
        public bool IsPaused { get; private set; }
        public EProcedureType Type { get; set; }
        public bool IsBreak { get; private set; }
        /// <summary>
        /// maker 中根据个人需求可以透传数据  程序结束 会传出
        /// </summary>
        private ProcedureParams _params = null;

        private object breakCaller;
        private Action<object> breakCallback;
        private int uuid;

        private static ProcedureManager _instance = null;

        public static ProcedureManager instance
        {
            get
            {
                if (_instance == null)
                    _instance = new ProcedureManager();
                return _instance;
            }
            
        }
        public ProcedureManager()
        {
            Clear();
        }
        private int GenerateId()
        {
            uuid++;
            return uuid;
        }

        public void Update(float delta)
        {
            if (IsRunning && Dramas[ProcessingIndex].IfNeedUpdate())
            {
                Dramas[ProcessingIndex].Update(delta);
            }
        }

        public void OnInit()
        {
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_main_pause, Pause);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_main_break, Break);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_main_start, OnStart);
            AddEvent();
        }

        public void OnExit()
        {
            Break(string.Empty,null);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_main_break, Break);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_main_pause, Pause);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_main_start, OnStart);
            RemoveEvent();
        }

        private void OnStart(string name, object body)
        {
            Start();
        }

        public void Start()
        {
            if (IsRunning) return;
            if (Dramas.Count <= ProcessingIndex) return;
                
            //VFDebug.Log("dram  start---------------IsRunning--true--");
            IsRunning = true;
            IsBreak = false;
            ProcessingIndex = 0;
            Roll();
        }

        public void Skip()
        {
            if (IsRunning)
            {
                Dramas[ProcessingIndex].Break(false);
                if (ProcessingIndex == Dramas.Count - 1)
                {
                    Over();
                }
                else
                {
                    ProcessingIndex++;
                    Roll();
                }
            }
        }

        private void Pause(string name, object body)
        {
            Debug.Log("Pause");
            if (IsRunning)
            {
                Dramas[ProcessingIndex].Pause();
            }

            IsPaused = true;
        }

        public void Resume()
        {
            if (IsRunning)
            {
                Dramas[ProcessingIndex].Resume();
            }

            IsPaused = false;
        }

        public void Break(string name, object body)
        {
            
            if (IsRunning)
            {
                Dramas[ProcessingIndex].Break(true);
            }

            breakCallback?.Invoke(breakCaller);
            breakCallback = null;
            breakCaller = null;
            IsBreak = true;
            IsPaused = false;
            Over();
        }

        public void RegisterBreakCallback(object caller, Action<object> callback)
        {
            breakCaller = caller;
            breakCallback = callback;
        }

        public void UnRegisterBreakCallback()
        {
            breakCallback = null;
            breakCaller = null;
        }

        private void Over()
        {
            NotifyInfo info = new NotifyInfo();
            info.param = new object[]{ Type,IsBreak,_params};
            
            Notifier.instance.SendNotification(NotifyConsts.procedure_all_over, info);
            Clear();
            VFDebug.LogWarning("procedure over!!");
        }

        public void Insert<T>(object param = null) where T : BaseDrama, new()
        {
            // if (!IsRunning)
            // {
            //     throw new InvalidOperationException("Cannot insert a drama into a non-started procedure!");
            // }

            // Debug.LogError("dram insert--------------------------------------------Dramas:" + Dramas.Count);
            var drama = new T();
            drama.Initialize(this, GenerateId(), param, DramaFinish);
            Dramas.Add(drama);
        }
        
        public void InsertFirst<T>(object param = null) where T : BaseDrama, new()
        {
            var drama = new T();
            drama.Initialize(this, GenerateId(), param, DramaFinish);
            Dramas.Insert(0, drama);
        }

        private void Roll()
        {
            if (Dramas[ProcessingIndex].IsPaused)
            {
                VFDebug.Log("执行一个被暂停的drama 请检查 ProcessingIndex ：：" + ProcessingIndex);
                return;
            }

            Dramas[ProcessingIndex].OnEvent();
            Dramas[ProcessingIndex].Do();
        }

        private void DramaFinish(int dramaId)
        {
            if (IsRunning)
            {
                ProcessingIndex++;
                if (ProcessingIndex >= Dramas.Count)
                {
                    Over();
                }
                else
                {
                    Roll();
                }
            }
        }
        
        public void Clear()
        {
            //VFDebug.Log("dram clear-----------------------------------------");
            foreach (var drama in Dramas)
            {
                drama?.Dispose();
            }

            Dramas = new List<BaseDrama>();
            _params = null;
            ProcessingIndex = 0;
            IsRunning = false;
            IsPaused = false;
            IsBreak = false;
        }

    }

    public class ProcedureParams
    {
        public EProcedureType type = EProcedureType.None;
        public object param = null;
    }
}
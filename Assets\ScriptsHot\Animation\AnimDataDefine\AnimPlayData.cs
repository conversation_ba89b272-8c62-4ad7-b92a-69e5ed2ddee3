﻿using UnityEngine;

namespace AnimationSystem
{
    // 动作播放数据
    public class AnimPlayData
    {
        public AnimationClip clip;
        public int nextAnimIndex;                 // 下一个动画索引
        public float speed = 1.0f;
        public bool willBlendToNext = false;
        public float blendToNext = 1.0f;          // 在动画播放到百分之多少时融合
        public float blendTime = 0.0f;            // 融合时间（秒）
        public float loopCount = 0;               // 循环次数（支持小数）
        public bool enableFootIK = false;          // 是否启用FootIK
    }
}
﻿using System.Collections.Generic;
using FairyGUI;
using Msg.basic;
using UIBind.Settlement;

namespace ScriptsHot.Game.Modules.Settlement.SettlementUI
{
    public class SettlementAUAUI : BaseUI<SettlementAUAPanel>
    {
        public SettlementAUAUI(string name) : base(name)
        {
        }

        private SettlementModel SettlementMod => GetModel<SettlementModel>(ModelConsts.Settlement);
        public override string uiLayer => UILayerConsts.Top;
        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            AddUIEvent(ui.compBottom.btnStart.onClick, OnClickNext);

            ui.compAUA.compContent.list.itemRenderer = OnRendererSections;
            ui.compAUA.compSettleList.DoInit();
            ui.compAUA.tfCongra.SetKey("freeTalk_Reward");
            ui.compBottom.tfReward.SetKey("common_continue");
        }

        protected override void OnShow()
        {
            base.OnShow();
            ui.compBottom.state.selectedIndex = 1;
            SoundManger.instance.PlayUI("settlement");
            DataDotAppear_Dialogue_Result dot = new DataDotAppear_Dialogue_Result();
            dot.Dialogue_id = GetModel<ChatModel>(ModelConsts.Chat).dialogId;
            dot.Dialogue_type = SettlementMod.DialogMode;
            DataDotMgr.Collect(dot);
            RefreshCompAUA();
        }

        protected override bool isFullScreen => true;
        private List<CompSettleList.SettleListStruct> _settleStrList = new();

        private void RefreshCompAUA()
        {
            PB_DialogSettlementData settlementData = SettlementMod.SettlementData;
            
            _settleStrList.Clear();
            _settleStrList.Add(new CompSettleList.SettleListStruct()
            {
                IconType = CompSettleList.SettleIconType.Time,
                Number = int.Parse(settlementData.task_cost_time),
            });
            _settleStrList.Add(new CompSettleList.SettleListStruct()
            {
                IconType = CompSettleList.SettleIconType.Exp,
                Number = settlementData.experience,
            });
            _settleStrList.Add(new CompSettleList.SettleListStruct()
            {
                IconType = CompSettleList.SettleIconType.Words,
                Number = int.Parse(settlementData.word_num),
            });
            ui.compAUA.compSettleList.DoShowSettleList(_settleStrList);
            
            ui.compAUA.compContent.list.numItems = settlementData.section_list.Count;
            ui.compAUA.compContent.list.ResizeToFit();
        }
        
        private void OnClickNext()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            GetController<SettlementController>(ModelConsts.Settlement).ShowNextView( ()=> { Hide(); });
           
        }

        private void OnRendererSections(int index, GObject obj)
        {
            AUAContentItem item = new AUAContentItem();
            item.Construct(obj as GComponent);
            PB_DialogSettlementData settlementData = SettlementMod.SettlementData;
            PB_FlashSectionInfo sectionInfo = settlementData.section_list[index];
            item.tfTitle.text = sectionInfo.section_title;
            item.tfContent.text = sectionInfo.description;
            item.showLine.selectedIndex = index == settlementData.section_list.Count - 1 ? 0 : 1;
        }
    }
}
﻿using ScriptsHot.Game.Modules.Explore.ExploreType.Base;

namespace ScriptsHot.Game.Modules.Explore.State.Base
{
    public class ExploreStateName
    {
        public static string Start = "Start";
        public static string Exit = "Exit";
        
        
        public static string ExploreChatActiveStart = "ExploreChatActiveStart";
    }

    public class ExploreStateBase : BaseMachineState<ExploreEntityBase>
    {
        protected ExploreController _manager;

        public ExploreStateBase(string name,ExploreEntityBase chat) : base(name)
        {
        }


        public override void OnEnter(params object[] args)
        {
            AddEvent();
   
        }

        public override void OnExit()
        {
            base.OnExit();
            RemoveEvent();
        }

        public virtual void AddEvent()
        {
        }
        
        public virtual void RemoveEvent()
        {
        }
    }
}
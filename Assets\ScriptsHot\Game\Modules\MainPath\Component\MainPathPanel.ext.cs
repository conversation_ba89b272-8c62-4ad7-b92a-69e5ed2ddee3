﻿using System;
using System.Collections.Generic;
using FairyGUI;
using Modules.DataDot;
using Msg.basic;
using Msg.course;
using ScriptsHot.Game.Modules.MainPath;
using Unity.Mathematics;
using UnityEngine;

namespace UIBind.MainPath
{
    public partial class MainPathPanel
    {
        private CenterHomeUI _homePanel;
        private MainPathController _pathController;
        private int _focusIndex;

        private const string BtnPathNodeUrl = "ui://lno6dlsnlkfp2d";
        private const string BtnPathNodeRewardUrl = "ui://lno6dlsnlkfp2n";
        private const string BtnPathNodeCupUrl = "ui://lno6dlsnlkfp2u";

        private const string CompDot2Url = "ui://lno6dlsnslcu3e";
        private const string CompDot3Url = "ui://lno6dlsnslcu3k";
        private const string CompDot4Url = "ui://lno6dlsnslcu3l";
        private const string CompDot5Url = "ui://lno6dlsnslcu3m";
        private const string CompDot6Url = "ui://lno6dlsnslcu3n";

        private int _selectNodeIndex; //主要是用来看打开谁的卡片窗
        public GButton btnToTop;

        public void OnInit(CenterHomeUI panel)
        {
            _homePanel = panel;
            _pathController = panel.GetController<MainPathController>(ModelConsts.MainPath);
            panel.AddUIEvent(chapterDetails.com.onClick, OnClickOpenBookshelf);
            panel.AddUIEvent(pathList.scrollPane.onScroll, OnScroll);
            panel.AddUIEvent(pathList.onTouchMove, OnTouchMove);
            panel.AddUIEvent(pathList.onClickItem, ClearSelect);
            panel.AddUIEvent(closeLdr.onClick, ClearSelect);
            pathList.itemRenderer = OnRendererMainPath;
        }


        public void OnShow(GButton topBtn)
        {
            RemoveEvent();
            AddEvent();
            RefreshMainPath();

            AdjustToTop(topBtn);

            DotAppearMainPath dot = new DotAppearMainPath();
            DataDotMgr.Collect(dot);
        }

        private void AdjustToTop(GButton topBtn)
        {
            btnToTop = topBtn;
            _homePanel.AddUIEvent(btnToTop.onClick, OnClickToTop);
        }


        private void RefreshMainPath()
        {
            _focusIndex = 0;
            _titleObjs.Clear();
            _dicNodeStructByObj.Clear();
            ClearSelect();
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            if (sectionData == null) return;
            pathList.numItems = sectionData.NodeList.Count; //有限个 不用虚拟列表也可以
            TimerManager.instance.RegisterNextFrame((a) =>
            {
                FocusMainPathNode();
                UpdateBtnToTopOnScroll();
            });
        }

        //case 1:最开始进入路径的时候 focus running
        //case 2:切换书本 focus 0
        //case 3:点击节点 focus 节点 弹窗

        private float FocusMainPathNode()
        {
            if (_selectNodeIndex != -1) //打开tips 要算一下弹窗位置
            {
                GObject focusObj = pathList.GetChildAt(_selectNodeIndex);
                Vector2 focusPos = com.GlobalToLocal(focusObj.LocalToGlobal(Vector2.zero));
                RefreshChapterDetail(_pathController.Model.FocusData.SectionData.NodeList[_selectNodeIndex]);
                if (focusPos.y > targetY.position.y)
                {
                    float needLength = focusPos.y - targetY.position.y;//需要移动的距离
                    float remainLength = pathList.scrollPane.contentHeight - pathList.scrollPane.posY - pathList.height;//可移动距离

                    if (needLength > remainLength)
                    {
                        pathList.scrollPane.SetPosY(pathList.scrollPane.contentHeight - pathList.height, true);//直接到头
                        return targetY.position.y + focusObj.height + (needLength - remainLength);
                    }
                    pathList.scrollPane.SetPosY(pathList.scrollPane.posY - (targetY.position.y - focusPos.y), true);
                    if (Math.Abs(pathList.scrollPane.contentHeight - pathList.scrollPane.posY - pathList.height) < 1f)
                        return focusPos.y + focusObj.height;
                    return math.min(targetY.position.y + focusObj.height, pathList.scrollPane.contentHeight - pathList.height);
                }

                if (focusPos.y < pathList.y)
                {
                    pathList.scrollPane.SetPosY(pathList.scrollPane.posY - (pathList.y - focusPos.y), true);
                    return pathList.y + focusObj.height;
                }
            }
            if (_pathController.Model.FocusData.NeedChange)//不打开tips的话只显示当前节点就ok
            {
                MainPathModel.FocusNodeData data = _pathController.Model.FocusData;
                _pathController.Model.SetFocusData(data.SectionData, data.UnitData, data.LevelData);//needChange变false
                pathList.ScrollToView(_focusIndex);
                RefreshChapterDetail(_pathController.Model.FocusData.SectionData.NodeList[_focusIndex]);
            }

            return 0;
        }

        private void RefreshChapterDetail(MainPathSectionData.MainPathNodesStruct nodeData)
        {
            PB_SectionData sectionData = _pathController.Model.FocusData.SectionData.ServerSectionData;
            chapterDetails.head.icon = nodeData.UnitData.ServerUnitData.avatar_head_url;
            chapterDetails.title1.SetKeyArgs("ui_main_path_primary", sectionData.section_title,
                (nodeData.UnitData.ServerUnitData.unit_index + 1).ToString());
            chapterDetails.title2.text = nodeData.UnitData.ServerUnitData.unit_title;
            chapterDetails.grpContent.EnsureBoundsCorrect();
            chapterDetails.color.selectedIndex = (int)nodeData.Color;
        }

        private Dictionary<GObject, MainPathSectionData.MainPathNodesStruct> _dicNodeStructByObj = new();
        private List<GObject> _titleObjs = new();

        private void OnRendererMainPath(int index, GObject obj)
        {
            GComponent comp = obj as GComponent;
            if (comp == null) return;
            BtnPathNodePositioning item = new BtnPathNodePositioning();
            item.Construct(comp);
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            List<MainPathSectionData.MainPathNodesStruct> nodeList = sectionData.NodeList;
            MainPathSectionData.MainPathNodesStruct nodeData = nodeList[index];
            int dotCount = nodeData.NodeData?.ServerLevelData.session_data_list.Count - 1 ?? 0;
            dotCount = nodeData.NodeData?.ServerLevelData.status == PB_ProgressStatusEnum.PSLock ? 0 : dotCount;
            item.positioning.selectedIndex = nodeData.Position;
            item.node.selectedIndex = (int)nodeData.NodeStyle;
            item.dotNum.selectedIndex = dotCount;
            if (nodeData.NodeStyle == MainPathSectionData.NodeType.TitleNode)
                item.title0.text = nodeData.UnitData.ServerUnitData.unit_title;
            RefreshStartTips(item, nodeData);
            RefreshSkipTips(item.skipToThisTips, nodeData);
            _dicNodeStructByObj[obj] = nodeData;
            _homePanel.AddUIEvent(item.nodeState.onClick, OnClickNode);
            if (nodeData.NodeStyle == MainPathSectionData.NodeType.TitleNode && !_titleObjs.Contains(obj))
                _titleObjs.Add(obj);

            if (nodeData.NodeData == null) return;
            MainPathModel.FocusNodeData focusData = _pathController.Model.FocusData;
            try
            {
                if (focusData.UnitData != null && focusData.LevelData != null
                    && focusData.LevelData.NodeId == nodeData.NodeData.NodeId
                    && focusData.UnitData.ServerUnitData.unit_index == nodeData.UnitData.ServerUnitData.unit_index)

                    _focusIndex = index;
                LoadNodeState(item.nodeState, nodeData);
                LoadDotComp(item.dotLdr, nodeData);
            }
            catch
            {

                if (nodeData.UnitData == null)
                {
                    Debug.LogError("nd1");
                }
                else if (nodeData.UnitData.ServerUnitData == null)
                {
                    Debug.LogError("nd2");
                }
                throw new Exception("路径null");
            }

        }

        private void OnClickToTop()
        {
            // 找到最后一个开放的节点并聚焦
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            if (sectionData != null && sectionData.NodeList != null && sectionData.NodeList.Count > 0)
            {
                int lastOpenIndex = -1;
                for (int i = sectionData.NodeList.Count - 1; i >= 0; i--)
                {
                    var node = sectionData.NodeList[i];
                    if (node.NodeData != null && node.NodeData.ServerLevelData.status != PB_ProgressStatusEnum.PSLock)
                    {
                        lastOpenIndex = i;
                        break;
                    }
                }
                if (lastOpenIndex != -1)
                {
                    _focusIndex = lastOpenIndex;
                    _selectNodeIndex = -1;
                    pathList.ScrollToView(Mathf.Max(0, _focusIndex - 2), true, true);  // 往下移动两个图标，以让出气泡的位置
                    RefreshChapterDetail(sectionData.NodeList[_focusIndex]);
                    return;
                }
            }
            FocusMainPathNode();
        }

        private void RefreshStartTips(BtnPathNodePositioning item, MainPathSectionData.MainPathNodesStruct nodeData)
        {
            bool jumpState = nodeData.JumpState;
            bool runAndShowState = nodeData.NodeData != null && (nodeData.UnitData.ServerUnitData.unit_index > 0 ||
                                                                 (nodeData.UnitData.ServerUnitData.unit_index == 0 &&
                                                                  nodeData.NodeData.ServerLevelData.level_index > 0)) &&
                                   nodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSRunning;

            item.skipTips.selectedIndex = jumpState || runAndShowState ? 1 : 0;
            if (runAndShowState) item.node_breathe_1.Play(-1, 0, null);
            else item.node_breathe_1.Stop();
            item.compSkipTips.color.selectedIndex = (int)nodeData.Color;
            item.compSkipTips.title0.SetKey(runAndShowState ? "ui_main_path_start" : "ui_main_path_skip");
        }

        private void RefreshSkipTips(SkipToThisTips item, MainPathSectionData.MainPathNodesStruct nodeData)
        {
            if (nodeData.NodeStyle != MainPathSectionData.NodeType.SkipNode) return;

            if (_pathController.Model.BookShelfCourseData.section_data_list.Count > _pathController.Model.FocusData.SectionData.ID + 1)
            {
                PB_SectionData section = _pathController.Model.BookShelfCourseData.section_data_list[
                        _pathController.Model.FocusData.SectionData.ID + 1];
                item.title1.text = section.section_title;
                item.title2.text = section.section_subtitle;
                item.BtnSkipToThis.SetKey(section.status == PB_ProgressStatusEnum.PSLock ? "ui_main_path_skip"
                    : "ui_main_path_continue");
                _homePanel.AddUIEvent(item.BtnSkipToThis.onClick, OnClickSkipSection);
            }
            else
            {
                item.com.visible = false;//顶个位置而已 不然弹窗会被卡在下面
            }
        }

        private void LoadNodeState(GLoader container, MainPathSectionData.MainPathNodesStruct nodeData)
        {
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            switch (container.url)
            {
                case BtnPathNodeUrl:
                    BtnPathNode normalNode = new BtnPathNode();
                    normalNode.Construct(container.component);
                    MainPathSectionData.ColorType color =
                        nodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSLock && !nodeData.JumpState
                            ? MainPathSectionData.ColorType.Gray
                            : nodeData.Color;
                    normalNode.color.selectedIndex = (int)color;
                    normalNode.blockType.url = sectionData.GetNormalNodeUrlByType(
                            nodeData.NodeData.ServerLevelData.level_type, nodeData);
                    float plusY = nodeData.NodeStyle == MainPathSectionData.NodeType.NormalNode ? 0 : 32;
                    container.SetPosition(container.x, container.height / 2 + plusY, 0);
                    break;
                case BtnPathNodeRewardUrl:
                    BtnPathNodeReward rewardNode = new BtnPathNodeReward();
                    rewardNode.Construct(container.component);
                    if (nodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSLock)
                        rewardNode.state.selectedPage = "gray";
                    else if (nodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSRunning)
                        rewardNode.state.selectedPage = "unClaim";
                    else
                        rewardNode.state.selectedPage = "claimed";
                    break;
                case BtnPathNodeCupUrl:
                    BtnPathNodeCup cupNode = new BtnPathNodeCup();
                    cupNode.Construct(container.component);
                    container.SetPosition(container.x, 87, container.z);//魔法数字87 = 77+10 
                    cupNode.color.selectedIndex =
                        nodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSLock
                            ? 5
                            : (int)nodeData.Color;
                    break;
            }
        }

        private void LoadDotComp(GLoader container, MainPathSectionData.MainPathNodesStruct nodeData)
        {
            if (string.IsNullOrEmpty(container.url)) return;
            switch (container.url)
            {
                case CompDot2Url:
                    CompDot2 dot2 = new CompDot2();
                    dot2.Construct(container.component);
                    break;
                case CompDot3Url:
                    CompDot3 dot3 = new CompDot3();
                    dot3.Construct(container.component);
                    break;
                case CompDot4Url:
                    CompDot4 dot4 = new CompDot4();
                    dot4.Construct(container.component);
                    break;
                case CompDot5Url:
                    CompDot5 dot5 = new CompDot5();
                    dot5.Construct(container.component);
                    break;
                case CompDot6Url:
                    CompDot6 dot6 = new CompDot6();
                    dot6.Construct(container.component);
                    break;
            }
            for (int i = 0; i < nodeData.NodeData.ServerLevelData.session_data_list.Count; i++)
            {
                bool finishState = nodeData.NodeData.ServerLevelData.session_completed > i;
                container.component.GetChild($"dot{i}").asCom.GetController("color").selectedIndex =
                    finishState ? (int)nodeData.Color : (int)MainPathSectionData.ColorType.Gray;
            }
        }

        private void OnClickOpenBookshelf()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            
            DotClickMainPathLevel dot = new DotClickMainPathLevel();
            dot.level_id = _pathController.Model.CurSectionData.ID;
            DataDotMgr.Collect(dot);

            ClearSelect();

            _pathController.RequestBookShelf();
        }

        private void OnClickNode(EventContext ext)
        {
            ext.StopPropagation();
            
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);
            
            GObject obj = ext.sender as GObject;
            if (obj == null) return;
            int index = pathList.GetChildIndex(obj.parent);
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            MainPathSectionData.MainPathNodesStruct nodeData = sectionData.NodeList[index];
            if (nodeData.NodeStyle == MainPathSectionData.NodeType.TitleNode ||
                nodeData.NodeStyle == MainPathSectionData.NodeType.SkipNode)
                return;

            if (nodeData.NodeStyle == MainPathSectionData.NodeType.RewardNode &&
                nodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSRunning)
            {
                _pathController.ReqOpenRewardBox();
                return;
            }

            if (index == _selectNodeIndex)
            {
                ClearSelect();
                return;
            }

            if (nodeData.JumpState)
            {
                _pathController.EnterJump(nodeData.UnitData.ServerUnitData);
                return;
            }

            _selectNodeIndex = index;
            GObject selectObj = pathList.GetChildAt(_selectNodeIndex);
            Vector2 targetPos = com.GlobalToLocal(selectObj.LocalToGlobal(Vector2.zero));
            float focusY = FocusMainPathNode();
            focusY = focusY == 0 ? targetPos.y + selectObj.height : focusY;
            compNodeStart.SetPosition(compNodeStart.x,
                focusY + ((nodeData.NodeData.ServerLevelData.session_data_list.Count > 1 &&
                           nodeData.NodeData.ServerLevelData.status != PB_ProgressStatusEnum.PSLock) ||
                          nodeData.NodeStyle == MainPathSectionData.NodeType.TrophyNode
                    ? 30 : 0), 0);
            compNodeStart.ShowStartTips(nodeData, ClearSelect, ClearSelect);
            HideSkipTips();
        }

        private void ShowSkipTip()
        {
            for (int i = 0; i < pathList.numItems; i++)
            {
                GObject obj = pathList.GetChildAt(i);
                if (obj == null) continue;
                BtnPathNodePositioning item = new BtnPathNodePositioning();
                item.Construct(obj.asCom);
                
                MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
                List<MainPathSectionData.MainPathNodesStruct> nodeList = sectionData.NodeList;
                MainPathSectionData.MainPathNodesStruct nodeData = nodeList[i];

                RefreshStartTips(item, nodeData);
            }
        }

        private void HideSkipTips()
        {
            for (int i = 0; i < pathList.numItems; i++)
            {
                GObject obj = pathList.GetChildAt(i);
                if (obj == null) continue;
                BtnPathNodePositioning item = new BtnPathNodePositioning();
                item.Construct(obj.asCom);
                item.skipTips.selectedIndex = 0;
            }
        }

        //跳Section
        private void OnClickSkipSection()
        {
            if (_pathController.Model.BookShelfCourseData.section_data_list.Count >
                _pathController.Model.FocusData.SectionData.ID + 1)
            {
                PB_SectionData section = _pathController.Model.BookShelfCourseData.section_data_list[
                    _pathController.Model.FocusData.SectionData.ID + 1];
                DotClickSkipItemSkip dot = new DotClickSkipItemSkip();
                dot.unit_id = _pathController.Model.FocusData.SectionData.ID;
                if (section.status == PB_ProgressStatusEnum.PSLock)
                {
                    dot.is_skip = 1;
                    _pathController.EnterJump(section);
                }
                else
                {
                    dot.is_skip = 0;
                    _pathController.Change2FinishOrRunningSection(section.section_id);
                }
                DataDotMgr.Collect(dot);
            }
        }

        private void ClearSelect()
        {
            if (_selectNodeIndex == -1) return;
            compNodeStart.HideStartTips();
            ShowSkipTip();
            _selectNodeIndex = -1;
        }

        private void OnTouchMove()
        {
            ClearSelect();
        }

        private void OnScroll()
        {
            if (pathList.numItems <= 0)
                return;

            UpdateChapterDetailOnScroll();
            UpdateBtnToTopOnScroll();
        }

        // 根据滚动位置刷新章节详情
        private void UpdateChapterDetailOnScroll()
        {
            var posY = pathList.scrollPane.posY;
            GObject firstObj = pathList.GetChildAt(0);
            if (_titleObjs.Count > 0 && _titleObjs[0].y + firstObj.height / 2 > posY)
            {
                _dicNodeStructByObj.TryGetValue(firstObj, out MainPathSectionData.MainPathNodesStruct nodeData);
                RefreshChapterDetail(nodeData); //第一个没有title obj
            }
            else
            {
                for (int i = 0; i < _titleObjs.Count; i++)
                {
                    _dicNodeStructByObj.TryGetValue(_titleObjs[i], out MainPathSectionData.MainPathNodesStruct nodeData);
                    if (i == _titleObjs.Count - 1 ||
                    (_titleObjs[i].y + firstObj.height / 2 < posY && _titleObjs[i + 1].y + firstObj.height / 2 > posY))
                    {
                        RefreshChapterDetail(nodeData);
                        break;
                    }
                }
            }
        }

        // 根据滚动位置刷新“回到最新解锁节点”按钮
        private void UpdateBtnToTopOnScroll()
        {
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            int latestUnlockedIndex = -1;
            for (int i = sectionData.NodeList.Count - 1; i >= 0; i--)
            {
                var node = sectionData.NodeList[i];
                if (node.NodeData != null && node.NodeData.ServerLevelData.status != PB_ProgressStatusEnum.PSLock)
                {
                    latestUnlockedIndex = i;
                    break;
                }
            }

            if (latestUnlockedIndex != -1)
            {
                GObject unlockedObj = pathList.GetChildAt(latestUnlockedIndex);
                float unlockedY = unlockedObj.y;
                float viewTop = pathList.scrollPane.posY;
                float viewBottom = viewTop + pathList.height;

                if (unlockedY + unlockedObj.height < viewTop)
                {
                    btnToTop.visible = true;
                    btnToTop.scaleY = 1f;
                }
                else if (unlockedY > viewBottom)
                {
                    btnToTop.visible = true;
                    btnToTop.scaleY = -1f;
                }
                else
                {
                    btnToTop.visible = false;
                }
            }
        }

        private void OnUpdateMainPath(string str, object obj)
        {
            RefreshMainPath();
        }

        private void AddEvent()
        {
            Notifier.instance.RegisterNotification(NotifyConsts.UpdateMainPathEvent, OnUpdateMainPath);
        }

        private void RemoveEvent()
        {
            Notifier.instance.UnRegisterNotification(NotifyConsts.UpdateMainPathEvent, OnUpdateMainPath);
        }
    }
}
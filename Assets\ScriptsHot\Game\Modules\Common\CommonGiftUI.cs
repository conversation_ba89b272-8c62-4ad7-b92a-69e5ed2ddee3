﻿using FairyGUI;
using Msg.basic;
using Spine;

public class CommonGiftUI : BaseUI<UIBind.CommonUI.CommonGiftPanel>
{
    public CommonGiftUI(string name) : base(name)
    {
        
    }

    public override string uiLayer => UILayerConsts.Top;

    protected override void OnInit(GComponent uiCom)
    {
        AddUIEvent(ui.btnClose.onClick, OnClickClose);
    }

    private long _diamondNum;

    protected override void OnShow()
    {
        ui.tfDiamondNum.SetVar("num",_diamondNum.ToString()).FlushVars();
        ui.spGift.spineAnimation.AnimationState.SetAnimation(0, "2", false);
        ui.spGift.spineAnimation.AnimationState.Complete += delegate(TrackEntry entry) { Hide(); };
    }

    protected override void OnHide()
    {
        ui.spGift.spineAnimation.AnimationState.ClearListenerNotifications();
    }

    protected override bool isFullScreen => true;

    /// <summary>
    /// 显示Gift弹窗
    /// </summary>
    /// <param name="diamondNum"></param>
    public void ShowGiftDiamondUI(PB_DiamondPermanentInfo diamondNum)
    {
        SoundManger.instance.PlayUI("learnpath_start");
        _diamondNum = diamondNum.cur_balance - diamondNum.pre_balance;
        Show();
    }

    private void OnClickClose()
    {
        Hide();
    }
}
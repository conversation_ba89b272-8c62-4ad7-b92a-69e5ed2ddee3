﻿using System;
using System.Collections.Generic;

namespace AnimationSystem
{
    // 单句信息类
    public enum SentenceSemanticType
    {
        Neutral,
        Happy,
        
        State, //陈述
        Contrast, //对比
        PointSelf, //指向自己
        PointPlayer, //指向对方
    }

    public enum SentenceSemanticSideType
    {
        Strong,
        Weak,
    }

    [Serializable]
    public class SingleSentenceInfo
    {
        public float duration; //0->2.5=2.5
        public float willStartAt; //0.5
        public SentenceSemanticType semanticType;
        public SentenceSemanticSideType semanticSideType;
    }
    
    [Serializable]
    public class SentenceData
    {
        public string text;
        public string function_tag;
        public string intensity_tag;
        public float start_time_second;
        public float end_time_second;
    }

    [Serializable]
    public class DialogData
    {
        public List<SentenceData> sentences;
    }
}
using Cysharp.Threading.Tasks;
using System;
using System.Diagnostics;
using System.Reflection;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using UnityEngine;
using Debug = UnityEngine.Debug;
using UnityEngine.Analytics;

public class Main : MonoBehaviour
{
    private IGame _game;

    public bool Initialized { get; set; }
    private HotUpdateCore core;

    public static string BiGuid;
    
    private void GenerateGuid()
    {
        BiGuid = Guid.NewGuid().ToString("N");
    }
    
    void Awake()
    {
        RefMethods();
        //TimerLogHelper.Ins.AddLog("Main - Awake"); //Main - Awake
        SlowStartLogHelper.Ins.Start();
        
        DontDestroyOnLoad(this.gameObject);
        //

        if (AppConst.IsDebug && !SRDebug.IsInitialized)
        {
            SRDebug.Init();
            SRDebug.Instance.PanelVisibilityChanged += visible =>
            {
                FairyGUI.GRoot.inst.touchable = !visible;
            };
        }
        Screen.orientation = ScreenOrientation.Portrait;
        AppConst.screenWidth = Screen.width;
        AppConst.screenHeight = Screen.height;
        AppConst.screenDpi = Screen.dpi;
        CultureInfo.DefaultThreadCurrentCulture = new CultureInfo("en-US");//系统语言适配
        
        //App设置
        Screen.sleepTimeout = SleepTimeout.NeverSleep;
        Application.targetFrameRate = AppConst.FrameRate;//这个初值是在scriptN空间的
        
        //日志开关
        VFDebug.EnableLog = true;
#if UNITY_EDITOR
        VFDebug.EnableLogTime = false;
#else
        VFDebug.EnableLogTime = true;
#endif

#if UNITY_EDITOR
        // 用于在编辑器中集中查看状态机
        StateMachineViewerBridge.Instance = new GameObject("StateMachineViewerBridge").AddComponent<StateMachineViewerBridge>();
#endif

        this.InitCrashsight();
    }

    public static void Log(string log)
    {
        Debug.Log($"============={log}");
    }
    // Start is called before the first frame update
    async void Start()
    {
#if MACRO_GPAD
        StartCoroutine(AssetDeliveryHelper.CopyAllAssetPacks(_Start));
#else
        for (int i = 0; i < 2; i++)
        {
            await Task.Yield();
        }
        StartAsync();
#endif



    }
    void _Start()
    {
        StartAsync();
    }
    
    async void StartAsync()
    {
        //TimerLogHelper.Ins.AddLog("Main - Start");

#if UNITY_ANDROID// && !UNITY_EDITOR
        PerformanceOptimizer.instance.Startup();
#endif

        SlowStartLogHelper.Ins.Pause();
        int checkTimes = 10;
        while (Application.internetReachability == NetworkReachability.NotReachable)//有时不稳定
        {
            await Task.Delay(1000);
            checkTimes--;
            if (checkTimes <= 0)
            {
                string content = HotUpdateLangCfg.GetStr(HUKey.networkNotAvailableTip);
                string confirmBtnLabel =  HotUpdateLangCfg.GetStr(HUKey.NetRetryBtn);
                MainLoadingUI.Ins.OnShowNetErrorConfirmUIOpen(content,Start , confirmLabel:confirmBtnLabel);
                return;
            }
        }
        SlowStartLogHelper.Ins.ReStart();
        
        GenerateGuid();
        FacebookHelper.Ins.Init();
        AFHelper.Ins.Init();
        AFHelper.Cut_Start_up();
        AFHelper.af_first_open();
        StartUpAmazonBI.cut_start_up();
  

        MainLoadingUI.Ins.SetProgressDesc(MainLoadingUI.ProgressMode.CheckVersion);
        MainLoadingUI.Ins.SetProgressValue(0f);

        await GHttpManager.instance.Startup();
        
        TimerLogHelper.Ins.AddLog("Hot[Ver]0");
        core = new HotUpdateCore();
        AFHelper.hotupdate_check();
        bool result = await core.ReadyHotUpdate();
        if (!result)
        {
            Debug.LogError("Main Start Error.Hot update failed.");
            return;
        }

#if !UNITY_EDITOR
        LoginPreprocessor.Ins.TryLogin();
#endif

        TimerLogHelper.Ins.AddLog("Hot[Ver]1");//"HotUpdate[VerCompare] - End"

        StartHotUpdate();
    }

    async void StartHotUpdate()
    {
        AFHelper.CheckATT();
        AFHelper.hotupdate_version_check();
        HotUpdateCore.ReadyHotUpdateResult readyHotUpdateResult = await core.StartHotUpdate();
        if (readyHotUpdateResult == HotUpdateCore.ReadyHotUpdateResult.Fail)
        {
            Debug.LogError("Main Start Error.Hot update failed.");
            var errorMsg = HotUpdateLangCfg.GetStr(HUKey.retryUpdate);  
            core.OpenConfirmUI(errorMsg, null, null, 0);
            return;
        }
        else if (readyHotUpdateResult == HotUpdateCore.ReadyHotUpdateResult.CanRetry)
        {
            Debug.LogError("Main Start Error.Hot update failed.");
            var errorMsg = HotUpdateLangCfg.GetStr(HUKey.retryUpdate);  
            var confirmTxt = HotUpdateLangCfg.GetStr(HUKey.NetRetryBtn);  
            core.OpenConfirmUI(errorMsg, StartHotUpdate, null, 0 , confirmLabel:confirmTxt);
            return;
        }
        
        TimerLogHelper.Ins.AddLog("Hot[Down]0");
        SlowStartLogHelper.Ins.Pause();//跳过 下载时间
        Stopwatch sw = Stopwatch.StartNew();
        bool result = await core.StartDownload();
        if (!result)
        {
            Debug.LogError("Main Start Error.Hot update failed.");
            return;
        }

        SlowStartLogHelper.Ins.ReStart();
        TimerLogHelper.Ins.AddLog("Hot[Down]1");//HotUpdate[Download] - End

        AFHelper.hotupdate_complate(sw.ElapsedMilliseconds,core.TotalDownloadBytes_together);
        StartUpAmazonBI.cut_hotupdate_complete();
        
        await this.InitAssetDependentModule();
        Initialized = true;
        core.Dispose();
    }

    /// <summary>
    /// 初始化依赖资源的模块，在此之前不应使用资源相关模块
    /// </summary>
    /// <returns></returns>
    private async UniTask InitAssetDependentModule()
    {
        GameObject hotfixUI = GameObject.Find("HotfixPanel");
        if (hotfixUI != null)
            DontDestroyOnLoad(hotfixUI);

        try
        {
#if !UNITY_EDITOR
        Assembly gameAss = Assembly.Load(YooassetsLoadDll.LoadDll("Game.dll"));
#else
        Assembly gameAss = System.AppDomain.CurrentDomain.GetAssemblies().First(a => a.GetName().Name == "Game");
#endif
            Debug.Log($"==============================TestHotFix===========================  gameAss={gameAss}");
            Type gameType = gameAss.GetType("GameStart");//GameStart 实现了IGame
            IGame game = (IGame)Activator.CreateInstance(gameType);
            game.InitGame();
            _game = game;

            if (DeviceAdapter.OnModelReady != null)
            {
                DeviceAdapter.OnModelReady(""); //静音模式下能播放语音的逻辑。勿删。
            }

        }
        catch (Exception e)
        {
            Debug.Log($"====================Load Game.dll Error Message = {e.Message}   StackTrace = {e.StackTrace}");
        }
    }

    private void Update()
    {
        if(_game != null)
            _game.Update((int)(Time.deltaTime * 1000));
    }

    private void OnDestroy()
    {
        if (_game != null)
            this._game.StopGame();
    }

    void OnApplicationPause(bool pause)
    {
        if (this._game != null)
            this._game.OnApplicationPause(pause);
        SlowStartLogHelper.Ins.OnApplicationPause(pause);
    }

    void OnApplicationQuit()
    {
        if (this._game != null)
            this._game.OnApplicationQuit();
        SlowStartLogHelper.Ins.TrySave();
    }

    private void InitCrashsight()
    {
#if !UNITY_EDITOR

// Debug开关，Debug模式下会打印更多便于问题定位的Log.
// #if DEBUG
//         CrashSightAgent.ConfigDebugMode (true);
// #endif

        CrashSightAgent.SetAppVersion(Application.version);
        CrashSightAgent.SetDeviceModel(SystemInfo.deviceModel);
        Debug.Log("CrashSight-DeviceID:"+ CrashSightAgent.GetSDKDefinedDeviceID());
        CrashSightAgent.SetDeviceId(SystemInfo.deviceUniqueIdentifier) ;
        Debug.Log("SetCrashSightDeviceID:"+SystemInfo.deviceUniqueIdentifier);
        
#if UNITY_IOS
        
        //CrashSightAgent.SetDeviceId(SystemInfo.deviceUniqueIdentifier);//要求InitWithAppId前调用
        //CrashSightAgent.SetCustomizedDeviceID(SystemInfo.deviceUniqueIdentifier);//虽然不是最好的选择，可以先用着以后再迭代
        CrashSightAgent.ConfigCrashServerUrl("https://ios.crashsight.wetest.net/pb/sync");
        CrashSightAgent.InitWithAppId(AppConst.CrashsightAppID);

        
#elif UNITY_ANDROID
        // 设置上报的目标域名，请根据项目需求进行填写。（必填）
        CrashSightAgent.ConfigCrashServerUrl("https://android.crashsight.wetest.net/pb/async");
        // 设置上报所指向的APP ID, 并进行初始化。APPID可以在管理端更多->产品设置->产品信息中找到。
        CrashSightAgent.InitWithAppId("f4e1d0bdfc");
#endif
        CrashSightAgent.EnableExceptionHandler();
#endif
    }
    
    /// <summary>
    /// 解决泛型 AOT问题
    /// </summary>
    public void RefMethods()
    {
#if UNITY_ANDROID && !UNITY_EDITOR
            new AndroidJavaObject("android.app.ActivityManager$MemoryInfo").Get<long>("availMem");
#endif
    }
}

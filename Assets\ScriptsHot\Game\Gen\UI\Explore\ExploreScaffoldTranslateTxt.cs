/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreScaffoldTranslateTxt : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreScaffoldTranslateTxt";

        public GTextField txtTran;
        public GRichTextField txtName;
        public GGroup grpTitle;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            txtTran = (GTextField)com.GetChildAt(1);
            txtName = (GRichTextField)com.GetChildAt(3);
            grpTitle = (GGroup)com.GetChildAt(4);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            txtTran = null;
            txtName = null;
            grpTitle = null;
        }
    }
}
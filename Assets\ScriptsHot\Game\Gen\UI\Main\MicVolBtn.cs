/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Main
{
    public partial class MicVolBtn : UIBindT
    {
        public override string pkgName => "Main";
        public override string comName => "MicVolBtn";

        public Controller micState;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            micState = com.GetControllerAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            micState = null;
        }
    }
}
// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/course/service.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.course {

  /// <summary>Holder for reflection information generated from protobuf/course/service.proto</summary>
  public static partial class ServiceReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/course/service.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ServiceReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Ch1wcm90b2J1Zi9jb3Vyc2Uvc2VydmljZS5wcm90bxogcHJvdG9idWYvY291",
            "cnNlL2xlYXJuX3BhdGgucHJvdG8aGnByb3RvYnVmL2NvdXJzZS9ib29rLnBy",
            "b3RvGhtwcm90b2J1Zi9jb3Vyc2UvcmFkaW8ucHJvdG8aIHByb3RvYnVmL2Nv",
            "dXJzZS9zZXR0bGVtZW50LnByb3RvGhxwcm90b2J1Zi9jb3Vyc2UvdGlja2V0",
            "LnByb3RvMsUGCg1Db3Vyc2VTZXJ2aWNlEj0KDUdldFVzZXJDb3Vyc2USFC5D",
            "U19HZXRVc2VyQ291cnNlUmVxGhQuU0NfR2V0VXNlckNvdXJzZUFjayIAEjQK",
            "ClNraXBDb3Vyc2USES5DU19Ta2lwQ291cnNlUmVxGhEuU0NfU2tpcENvdXJz",
            "ZUFjayIAEjEKCVJld2FyZEJveBIQLkNTX1Jld2FyZEJveFJlcRoQLlNDX1Jl",
            "d2FyZEJveEFjayIAEjcKC0dldEJvb2tEYXRhEhIuQ1NfR2V0Qm9va0RhdGFS",
            "ZXEaEi5TQ19HZXRCb29rRGF0YUFjayIAEjoKDEdldFJhZGlvRGF0YRITLkNT",
            "X0dldFJhZGlvRGF0YVJlcRoTLlNDX0dldFJhZGlvRGF0YUFjayIAEk8KE0dl",
            "dENvdXJzZVNldHRsZW1lbnQSGi5DU19HZXRDb3Vyc2VTZXR0bGVtZW50UmVx",
            "GhouU0NfR2V0Q291cnNlU2V0dGxlbWVudEFjayIAEkMKD0J1eUNvdXJzZVRp",
            "Y2tldBIWLkNTX0J1eUNvdXJzZVRpY2tldFJlcRoWLlNDX0J1eUNvdXJzZVRp",
            "Y2tldEFjayIAElIKFENoYW5nZUNvdXJzZVByb2dyZXNzEhsuU1NfQ2hhbmdl",
            "Q291cnNlUHJvZ3Jlc3NSZXEaGy5TU19DaGFuZ2VDb3Vyc2VQcm9ncmVzc0Fj",
            "ayIAEmEKGUNyZWF0ZUNvdXJzZVNlc3Npb25SZWNvcmQSIC5TU19DcmVhdGVD",
            "b3Vyc2VTZXNzaW9uUmVjb3JkUmVxGiAuU1NfQ3JlYXRlQ291cnNlU2Vzc2lv",
            "blJlY29yZEFjayIAEkkKEUV4aXRDb3Vyc2VTZXNzaW9uEhguU1NfRXhpdENv",
            "dXJzZVNlc3Npb25SZXEaGC5TU19FeGl0Q291cnNlU2Vzc2lvbkFjayIAEkMK",
            "D1NldFVzZXJQcm9ncmVzcxIWLlNTX1NldFVzZXJQcm9ncmVzc1JlcRoWLlNT",
            "X1NldFVzZXJQcm9ncmVzc0FjayIAEjoKDEdldFVzZXJTaGVsZhITLkNTX0dl",
            "dFVzZXJTaGVsZlJlcRoTLlNDX0dldFVzZXJTaGVsZkFjayIAQihaGXZmX3By",
            "b3RvYnVmL3NlcnZlci9jb3Vyc2WqAgpNc2cuY291cnNlYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.course.LearnPathReflection.Descriptor, global::Msg.course.BookReflection.Descriptor, global::Msg.course.RadioReflection.Descriptor, global::Msg.course.SettlementReflection.Descriptor, global::Msg.course.TicketReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, null));
    }
    #endregion

  }
}

#endregion Designer generated code

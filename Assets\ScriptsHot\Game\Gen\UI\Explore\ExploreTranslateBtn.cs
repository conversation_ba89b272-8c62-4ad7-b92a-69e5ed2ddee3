/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreTranslateBtn : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreTranslateBtn";

        public Controller ctrl;
        public GImage btnDown;
        public GImage btnUp;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrl = com.GetControllerAt(0);
            btnDown = (GImage)com.GetChildAt(0);
            btnUp = (GImage)com.GetChildAt(1);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrl = null;
            btnDown = null;
            btnUp = null;
        }
    }
}
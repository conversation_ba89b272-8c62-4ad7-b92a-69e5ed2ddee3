using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using LitJson;
using UnityEngine;
using YooAsset;

public class HotUpdateResult
{
    public HotUpdateMode Mode = HotUpdateMode.None;
    public string CdnUrl;
    public ResourceDownloaderOperation downloader;
    public string PkgVer;

    // 非强更相关字段
    public string OptionalCdnUrl;  // 非强更的CDN地址
    public string OptionalPkgVer;  // 非强更的包版本
    public ResourceDownloaderOperation optionalDownloader; // 非强更下载器
    public bool HasOptionalUpdate = false; // 是否有非强更内容
}

public enum HotUpdateMode
{
    None,
    Offline,//读包内
    Simul,//编辑器本地
    Hotupdate //热更取cdn
}

public class HotUpdateCore
{
    public enum ErrorCode
    {
        // Error:Nacos resp is null
        Nacos = 8001,
        // Error: Nacos not find version
        Nacos_NoVersion = 8002,
        // Init YooAssets Error
        YooAssets_Init = 8003,
        // YooAssets Update Package Version Error   
        YooAssets_Update_Package_Version = 8004,
        // YooAssets Update Package Manifest Error
        YooAssets_Update_Package_Manifest = 8005,
        // YooAssets Update Package Manifest Error
        YooAssets_Download_Error = 8006,
    }
    
    public enum ReadyHotUpdateResult
    {
        //失败，没有重试的必要
        Fail,
        //失败，可重试
        CanRetry,
        //成功
        Success
    }
    
    
    /// <summary>
    /// 远端资源地址查询服务类
    /// </summary>
    public class RemoteServices : IRemoteServices
    {
        private string _defaultHostServer;
        private string _fallbackHostServer;

        public RemoteServices(string defaultHostServer, string fallbackHostServer)
        {
            _defaultHostServer = defaultHostServer;
            _fallbackHostServer = fallbackHostServer;
        }

        /// <summary>
        /// 动态更新服务器地址（用于非强更阶段）
        /// </summary>
        public void UpdateServerUrls(string defaultHostServer, string fallbackHostServer)
        {
            _defaultHostServer = defaultHostServer;
            _fallbackHostServer = fallbackHostServer;
        }

        string IRemoteServices.GetRemoteMainURL(string fileName)
        {
            return $"{_defaultHostServer}/{fileName}";
        }
        string IRemoteServices.GetRemoteFallbackURL(string fileName)
        {
            return $"{_fallbackHostServer}/{fileName}";
        }
    }

    
    private Dictionary<string, HotUpdateResult> result = new Dictionary<string, HotUpdateResult>();

    private long totalDownloadBytes_together;
    public long TotalDownloadBytes_together => totalDownloadBytes_together;

    // 存储RemoteServices实例，用于动态更新URL
    private RemoteServices mainRemoteServices;
    private RemoteServices avatarRemoteServices;

    private HotUpdateResult MainResult => result[HotUpdateFlow.YooAssetPackage];
    private HotUpdateResult AvatarResult => result[GAvatarResManager.YooAssetPackage];

    private void SetOfflineResult()
    {
        foreach (var kv in result)
        {
            kv.Value.Mode = HotUpdateMode.Offline;
        }
    }

    
    private async UniTask MultiGetHotVersionData()
    {
        TimerLogHelper.Ins.SaveTimeStart(TimerLogHelper.TimeType.HotUpdate);
        var task_server = GetHotVersionData();
        var task_cdn = GetHotVersionDataFromCdn();
        var (index, result_task_server,result_task_cdn) = await UniTask.WhenAny(task_server, task_cdn);
        string result = index == 0 ? result_task_server : result_task_cdn;
        bool isServerResult = index == 0;
        if (string.IsNullOrEmpty(result)) // 确保返回值有效
        {
            if (index == 0)
            {
                isServerResult = false;
                result = await GetHotVersionDataFromCdn();                
            }
            else
            {
                isServerResult = true;
                result = await GetHotVersionData();    
            }
        }

        if (string.IsNullOrEmpty(result))
        {
            return;
        }
        
        if (isServerResult)
        {
            Debug.Log($"====cdn_version winner = server , result = {result}");
            var gethotfixV2Resp = JsonMapper.ToObject<gethotfixV2Resp>(result);
            VersionData = JsonMapper.ToObject(gethotfixV2Resp.data);
            AFHelper.req_v_winner_server();
        }
        else
        {
            Debug.Log($"====cdn_version winner = cdn , result = {result}");
            VersionData = JsonMapper.ToObject(result_task_cdn);
            AFHelper.req_v_winner_cdn();
            killMyServerReq = true;
        }
        TimerLogHelper.Ins.SaveTimeEnd(TimerLogHelper.TimeType.HotUpdate);
    }
    
    
    /// <summary>
    /// 更新预处理，和nacos通信确认ver
    /// </summary>
    /// <returns></returns>
    public async UniTask<bool> ReadyHotUpdate()
    {
        result.Clear();
        result.Add(HotUpdateFlow.YooAssetPackage , new HotUpdateResult());
        result.Add(GAvatarResManager.YooAssetPackage , new HotUpdateResult());
        
        Debug.Log($"AppConst.IsHotUpdateMode = {AppConst.IsHotUpdateMode}");
        //非热更新模式
//         if (!AppConst.IsHotUpdateMode)
//         {
// #if UNITY_EDITOR
//             MainResult.Mode = HotUpdateMode.Simul;
//             AvatarResult.Mode = HotUpdateMode.Offline;
//             return true;
// #else
//             SetOfflineResult();
//             return true;
// #endif
//         }
        
        Debug.Log("当前热更模式 = hotupdate");
        await MultiGetHotVersionData();
        Debug.Log($"loginSrv = {AppConst.LoginSrv}"); 
        if (VersionData == null)
        {
            var errorMsg = HotUpdateLangCfg.GetStr(HUKey.networkNotAvailableTip);
            OpenConfirmUI(errorMsg, null, null, 0);
            return false;
        }
        
        try
        {
            string appVersion = Application.version; //AppConst.AotVersion;//检查底包是否有热更新版本
            if (!VersionData.ContainsKey(appVersion))
            {
                var errorMsg = HotUpdateLangCfg.GetStr(HUKey.checkVersionErrTip, appVersion);
                OpenConfirmUI(errorMsg, null, null, 0);
                Debug.LogError(errorMsg);
                return false;
            }


            string platformTag = "";
            switch (Application.platform)
            {
                case RuntimePlatform.Android:
                    platformTag = "android";
                    break;
                case RuntimePlatform.IPhonePlayer:
                    platformTag = "ios";//固定写为 全大写
                    break;
                default:
                    break;
            }
            
            #if UNITY_EDITOR
            platformTag = "ios";//固定写为 全大写
            #endif

            if (string.IsNullOrEmpty(platformTag))
            {
                var errorMsg = HotUpdateLangCfg.GetStr(HUKey.checkVersionErrTip, appVersion + " ErrInGetPlatformTag");
                OpenConfirmUI(errorMsg, null, null, 0);
                Debug.LogError(errorMsg);
                return false;
            }


            //EOL=end if life
            if (VersionData[appVersion].ContainsKey("isEOL")) {
                bool isEOL = (bool)VersionData[appVersion]["isEOL"];
                if (isEOL)
                {
                    var mainTip = HotUpdateLangCfg.GetStr(HUKey.forceJumpDownloadPageTip);
                    var comfirmBtnTip = HotUpdateLangCfg.GetStr(HUKey.forceJumpBtn);

                    string shopURL = "";
                    if (VersionData.ContainsKey(platformTag+"_shopURL"))
                    {
                        shopURL = (string)VersionData[platformTag + "_shopURL"];
                    }
                    else {
                        var errorMsg = HotUpdateLangCfg.GetStr(HUKey.checkVersionErrTip, appVersion + " ErrInGet("+ platformTag + "_shopURL)");
                        OpenConfirmUI(errorMsg, null, null, 0);
                        Debug.LogError(errorMsg);
                        return false;
                    }
                    
                    var tcs = new UniTaskCompletionSource<bool>();
                    OpenConfirmUI(mainTip,
                        () =>
                        {
                            Application.OpenURL(shopURL);
                            tcs.TrySetResult(false);
                        },
                        null,
                        2,
                        comfirmBtnTip
                    );
                    return false;
                }
                
            }

            #region process cdn
            //解析defaultCDN, fallbackCDN尚未启用
            string cdnUrl_prefix = "";//此cdn不携带平台标记（IOS ANDROID）
            if (VersionData.ContainsKey("defaultCDN"))
            {
                cdnUrl_prefix = (string)VersionData["defaultCDN"];
            }

            if (VersionData[appVersion].ContainsKey("cdn"))
            {
                cdnUrl_prefix = (string)VersionData[appVersion]["cdn"];
            }

            if (string.IsNullOrEmpty(cdnUrl_prefix))
            {
                var errorMsg = HotUpdateLangCfg.GetStr(HUKey.checkVersionErrTip, appVersion + " CDNErrInPrefix");
                OpenConfirmUI(errorMsg, null, null, 0);
                Debug.LogError(errorMsg);
                return false;
            }

     

            string cdnUrl = cdnUrl_prefix + platformTag.ToUpper()+"/";  //特殊约定此处全大写 && 需要补文件链接符号
            #endregion

            // 解析主资源版本信息
            string mainVersionHash = GetVersionHashSafely(appVersion, platformTag.ToLower() + "_version_hash", "offline");

            // 解析Avatar资源版本信息
            string avatarVersionHash = GetVersionHashSafely(appVersion, "avatar_" + platformTag.ToLower() + "_version_hash", "offline");

            // 解析非强更版本信息
            string optionalMainVersionHash = GetVersionHashSafely(appVersion, "optional_" + platformTag.ToLower() + "_version_hash", "offline");
            string optionalAvatarVersionHash = GetVersionHashSafely(appVersion, "optional_avatar_" + platformTag.ToLower() + "_version_hash", "offline");
            
            Debug.Log("======================================");
            Debug.Log($"======Hotupdate appVer:{appVersion}");
            Debug.Log("======================================");
            Debug.Log($"======mainhash:{mainVersionHash } ");
            Debug.Log($"======buildin-mainhash:{AutoAppConst.Main_Version_Hash } ");
            Debug.Log("======================================");
            Debug.Log($"======avatarhash:{avatarVersionHash } ");
            Debug.Log($"======buildin-avatarhash:{AutoAppConst.Avatar_Version_Hash } ");
            Debug.Log("======================================");
            Debug.Log($"======optional-mainhash:{optionalMainVersionHash } ");
            Debug.Log($"======optional-avatarhash:{optionalAvatarVersionHash } ");
            Debug.Log("======================================");
            Debug.Log($"======appSrv:{AppConst.LoginSrv} ");
            Debug.Log($"======cdnBase:{cdnUrl} ");

            #region PromoCode UI控制
            AppConst.DebugPromoCodeInfo = "";
            string promo_code_key = "promo_code";
            if (VersionData[appVersion].ContainsKey(promo_code_key))
            {
                string debugPromoCodeInfo = (string)VersionData[appVersion][promo_code_key];//是否开放 兑换码UI的开关
                if (!string.IsNullOrEmpty(debugPromoCodeInfo))
                {
                    AppConst.DebugPromoCodeInfo = debugPromoCodeInfo;
                }
            }
            #endregion


#if MACRO_GPAD
            AppConst.VersionHash = mainVersionHash;
            MainResult.CdnUrl = cdnUrl + mainVersionHash;
            MainResult.Mode = HotUpdateMode.Hotupdate;
            MainResult.PkgVer = mainVersionHash;

            AvatarResult.CdnUrl = cdnUrl + "avatar_res/" +  avatarVersionHash;
            AvatarResult.Mode = HotUpdateMode.Hotupdate;
            AvatarResult.PkgVer = avatarVersionHash;
#else
            #region MainRes
            if (IsVersionOffline(mainVersionHash)) {
                Debug.Log("Main资源的hotfix标记=offline模式");
                MainResult.Mode = HotUpdateMode.Offline;
                MainResult.PkgVer = AutoAppConst.Main_Version_Hash;
            }
            else
            {
                // 检查本地是否已有更新的版本（非强更下载的版本）
                string localLatestVersion = GetLocalLatestVersion(HotUpdateFlow.YooAssetPackage);
                string actualVersionToUse = mainVersionHash;

                // 如果本地有更新的版本，且该版本大于等于服务器的非强更版本，则使用本地版本
                if (!string.IsNullOrEmpty(localLatestVersion))
                {
                    if (CompareVersionNumbers(localLatestVersion, mainVersionHash) > 0)
                    {
                        // 检查非强更版本，如果本地版本已经是最新的非强更版本，则直接使用
                        if (!IsVersionOffline(optionalMainVersionHash) &&
                            CompareVersionNumbers(localLatestVersion, optionalMainVersionHash) >= 0)
                        {
                            actualVersionToUse = localLatestVersion;
                            Debug.Log($"Main资源使用本地已下载的版本: {actualVersionToUse} (服务器强更版本: {mainVersionHash})");
                        }
                    }
                }

                AppConst.VersionHash = actualVersionToUse;
                MainResult.CdnUrl = cdnUrl + actualVersionToUse;
                MainResult.PkgVer = actualVersionToUse;

                if (actualVersionToUse.Equals(AutoAppConst.Main_Version_Hash))
                {
                    MainResult.Mode = HotUpdateMode.Offline;
                }
                else
                {
                    MainResult.Mode = HotUpdateMode.Hotupdate;
                }
            }

            // 处理非强更信息
            if (!IsVersionOffline(optionalMainVersionHash) && !optionalMainVersionHash.Equals(MainResult.PkgVer))
            {
                // 版本号比较：只有当目标版本大于当前使用版本时才更新
                if (CompareVersionNumbers(optionalMainVersionHash, MainResult.PkgVer) > 0)
                {
                    MainResult.OptionalCdnUrl = cdnUrl + optionalMainVersionHash;
                    MainResult.OptionalPkgVer = optionalMainVersionHash;
                    MainResult.HasOptionalUpdate = true;
                    Debug.Log($"Main资源有非强更版本: {optionalMainVersionHash} (当前使用: {MainResult.PkgVer})");
                    Debug.Log($"MainResult.HasOptionalUpdate = {MainResult.HasOptionalUpdate}");
                    Debug.Log($"MainResult.OptionalCdnUrl = {MainResult.OptionalCdnUrl}");
                    Debug.Log($"mainRemoteServices = {mainRemoteServices}");
                }
                else
                {
                    Debug.Log($"Main非强更版本 {optionalMainVersionHash} 不大于当前使用版本 {MainResult.PkgVer}，跳过更新");
                }
            }
            else
            {
                Debug.Log($"Main非强更版本为offline或与当前版本相同，跳过非强更");
            }
            #endregion

            #region AvatarRes
            if (IsVersionOffline(avatarVersionHash)) {
                Debug.Log("Avatar资源的hotfix标记=offline模式");
                AvatarResult.Mode = HotUpdateMode.Offline;
                AvatarResult.PkgVer = AutoAppConst.Avatar_Version_Hash;
            }
            else
            {
                // 检查本地是否已有更新的版本（非强更下载的版本）
                string localLatestVersion = GetLocalLatestVersion(GAvatarResManager.YooAssetPackage);
                string actualVersionToUse = avatarVersionHash;

                // 如果本地有更新的版本，且该版本大于等于服务器的非强更版本，则使用本地版本
                if (!string.IsNullOrEmpty(localLatestVersion))
                {
                    if (CompareVersionNumbers(localLatestVersion, avatarVersionHash) > 0)
                    {
                        // 检查非强更版本，如果本地版本已经是最新的非强更版本，则直接使用
                        if (!IsVersionOffline(optionalAvatarVersionHash) &&
                            CompareVersionNumbers(localLatestVersion, optionalAvatarVersionHash) >= 0)
                        {
                            actualVersionToUse = localLatestVersion;
                            Debug.Log($"Avatar资源使用本地已下载的版本: {actualVersionToUse} (服务器强更版本: {avatarVersionHash})");
                        }
                    }
                }

                AvatarResult.CdnUrl = cdnUrl + "avatar_res/" + actualVersionToUse;
                AvatarResult.PkgVer = actualVersionToUse;

                if (actualVersionToUse.Equals(AutoAppConst.Avatar_Version_Hash))
                {
                    AvatarResult.Mode = HotUpdateMode.Offline;
                }
                else
                {
                    AvatarResult.Mode = HotUpdateMode.Hotupdate;
                }
            }

            // 处理非强更信息
            if (!IsVersionOffline(optionalAvatarVersionHash) && !optionalAvatarVersionHash.Equals(AvatarResult.PkgVer))
            {
                // 版本号比较：只有当目标版本大于当前使用版本时才更新
                if (CompareVersionNumbers(optionalAvatarVersionHash, AvatarResult.PkgVer) > 0)
                {
                    AvatarResult.OptionalCdnUrl = cdnUrl + "avatar_res/" + optionalAvatarVersionHash;
                    AvatarResult.OptionalPkgVer = optionalAvatarVersionHash;
                    AvatarResult.HasOptionalUpdate = true;
                    Debug.Log($"Avatar资源有非强更版本: {optionalAvatarVersionHash} (当前使用: {AvatarResult.PkgVer})");
                }
                else
                {
                    Debug.Log($"Avatar非强更版本 {optionalAvatarVersionHash} 不大于当前使用版本 {AvatarResult.PkgVer}，跳过更新");
                }
            }
            else
            {
                Debug.Log($"Avatar非强更版本为offline或与当前版本相同，跳过非强更");
            }
            #endregion
#endif


            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"HotUpdateFlow Exception.{e.Message}");
            return false;
        }
    }

    private HotUpdateFlow hotUpdateFlow = new HotUpdateFlow();
    private GAvatarResManager avatarResManager = GAvatarResManager.instance;

    //ReadyHotUpdate包含了yooasset内部的初始化时间
    public async UniTask<ReadyHotUpdateResult> StartHotUpdate()
    {
        #region MainRes
        ReadyHotUpdateResult isReady = await hotUpdateFlow.ReadyHotUpdate(this,MainResult);
        if (isReady != ReadyHotUpdateResult.Success)
        {
            return isReady;
        }
        // 保存RemoteServices实例的引用
        mainRemoteServices = hotUpdateFlow.RemoteServices;
        #endregion

        #region AvatarRes
        isReady = await avatarResManager.ReadyHotUpdate(this,AvatarResult);
        if (isReady != ReadyHotUpdateResult.Success)
        {
            return isReady;
        }
        // 保存RemoteServices实例的引用
        avatarRemoteServices = avatarResManager.RemoteServices;
        #endregion

        return ReadyHotUpdateResult.Success;
    }

    public async UniTask<bool> StartDownload()
    {
        if (MainResult.Mode != HotUpdateMode.Hotupdate && AvatarResult.Mode != HotUpdateMode.Hotupdate)
        {
            return true;
        }
        int totalDownloadCnt = 0;
        
        //需要下载的文件总数和总大小
        totalDownloadBytes_together = 0;
        if (MainResult.Mode == HotUpdateMode.Hotupdate)
        {
            totalDownloadCnt += MainResult.downloader.TotalDownloadCount;
            totalDownloadBytes_together += MainResult.downloader.TotalDownloadBytes;
        }

        if (AvatarResult.Mode == HotUpdateMode.Hotupdate)
        {
            totalDownloadCnt += AvatarResult.downloader.TotalDownloadCount;
            totalDownloadBytes_together += AvatarResult.downloader.TotalDownloadBytes;
        }

        //没有需要下载的资源
        if (totalDownloadCnt == 0)
        {
            return true;
        }

        float totalMB = totalDownloadBytes_together / 1024 / 1024;
        
        bool confirmDownload = await CheckInternet(totalMB);
        await UniTask.WaitUntil(() => confirmDownload);

        AFHelper.hotupdate_download();
        
        #region main
        if (MainResult.Mode == HotUpdateMode.Hotupdate &&  MainResult.downloader.TotalDownloadCount > 0)
        {
            //注册回调方法
            MainResult.downloader.OnDownloadErrorCallback = OnHotDownloadError;
            MainResult.downloader.OnDownloadProgressCallback = OnMainHotDownloadProgressUpdate;
            MainResult.downloader.OnDownloadOverCallback = OnHotDownloadOver;
            //开启下载
            MainResult.downloader.BeginDownload();
            await MainResult.downloader;
            //检测下载结果
            if (MainResult.downloader.Status == EOperationStatus.Succeed)
            {
                //下载成功
                Debug.Log($"main Updated package download Success");
            }
            else
            {
                //下载失败
                Debug.Log($"main Updated package download Error. {MainResult.downloader.Error}");
                return false;
            }
        }
        #endregion
        
        
        #region avatar
        if (AvatarResult.Mode == HotUpdateMode.Hotupdate && AvatarResult.downloader.TotalDownloadCount > 0)
        {
            //注册回调方法
            AvatarResult.downloader.OnDownloadErrorCallback = OnHotDownloadError;
            AvatarResult.downloader.OnDownloadProgressCallback = OnAvatarHotDownloadProgressUpdate;
            AvatarResult.downloader.OnDownloadOverCallback = OnHotDownloadOver;
            //开启下载
            AvatarResult.downloader.BeginDownload();
            await AvatarResult.downloader;
            //检测下载结果
            if (AvatarResult.downloader.Status == EOperationStatus.Succeed)
            {
                //下载成功
                Debug.Log($"main Updated package download Success");
            }
            else
            {
                //下载失败
                Debug.Log($"main Updated package download Error. {AvatarResult.downloader.Error}");
                return false;
            }
        }
        #endregion
        
        return true;
    }

    /// <summary>
    /// 开始非强更后台下载（静默下载，不阻塞用户）
    /// </summary>
    public void StartOptionalDownloadInBackground()
    {
        Debug.Log($"MainResult.HasOptionalUpdate = {MainResult.HasOptionalUpdate}");
        Debug.Log($"MainResult.OptionalCdnUrl = {MainResult.OptionalCdnUrl}");
        Debug.Log($"mainRemoteServices = {mainRemoteServices}");
        
        // 检查是否有非强更内容
        bool hasOptionalContent = MainResult.HasOptionalUpdate || AvatarResult.HasOptionalUpdate;
        if (!hasOptionalContent)
        {
            Debug.Log("没有非强更内容需要下载");
            return;
        }

        Debug.Log("开始非强更后台下载流程");

        // 异步执行，不阻塞主流程
        StartOptionalDownloadAsync().Forget();
    }

    /// <summary>
    /// 异步执行非强更下载
    /// </summary>
    private async UniTaskVoid StartOptionalDownloadAsync()
    {
        try
        {
            // 等待一段时间再开始下载，避免与游戏启动冲突
            await UniTask.Delay(5000); // 延迟5秒开始

            // 检查网络状态，只在WiFi环境下进行非强更下载
            if (!IsNetworkSuitableForOptionalDownload())
            {
                Debug.Log("当前网络环境不适合非强更下载，跳过后台下载");
                return;
            }

            Debug.Log($"MainResult.HasOptionalUpdate = {MainResult.HasOptionalUpdate}");
            Debug.Log($"MainResult.OptionalCdnUrl = {MainResult.OptionalCdnUrl}");
            Debug.Log($"mainRemoteServices = {mainRemoteServices}");
            
            // 更新RemoteServices的URL到非强更地址
            if (MainResult.HasOptionalUpdate && mainRemoteServices != null)
            {
                mainRemoteServices.UpdateServerUrls(MainResult.OptionalCdnUrl, MainResult.OptionalCdnUrl);
                Debug.Log($"后台更新Main RemoteServices URL到: {MainResult.OptionalCdnUrl}");
            }

            if (AvatarResult.HasOptionalUpdate && avatarRemoteServices != null)
            {
                avatarRemoteServices.UpdateServerUrls(AvatarResult.OptionalCdnUrl, AvatarResult.OptionalCdnUrl);
                Debug.Log($"后台更新Avatar RemoteServices URL到: {AvatarResult.OptionalCdnUrl}");
            }

            // 使用PreDownloadContentAsync进行非强更预下载
            bool mainOptionalResult = await StartMainOptionalDownload();
            bool avatarOptionalResult = await StartAvatarOptionalDownload();

            if (mainOptionalResult && avatarOptionalResult)
            {
                Debug.Log("非强更后台下载完成");
            }
            else
            {
                Debug.LogWarning("非强更后台下载部分失败，但不影响游戏运行");
            }
        }
        catch (Exception e)
        {
            Debug.LogWarning($"非强更后台下载异常: {e.Message}，但不影响游戏运行");
        }
    }

    /// <summary>
    /// 检查网络是否适合进行非强更下载
    /// </summary>
    /// <returns></returns>
    private bool IsNetworkSuitableForOptionalDownload()
    {
        switch (Application.internetReachability)
        {
            case NetworkReachability.NotReachable:
                Debug.Log("无网络连接，跳过非强更下载");
                return false;

            case NetworkReachability.ReachableViaCarrierDataNetwork:
                Debug.Log("当前使用移动网络，为节省流量跳过非强更下载");
                return false; // 移动网络下不进行非强更下载，节省用户流量

            case NetworkReachability.ReachableViaLocalAreaNetwork:
                Debug.Log("当前使用WiFi网络，可以进行非强更下载");
                return true; // WiFi环境下可以进行非强更下载

            default:
                return false;
        }
    }

    private async UniTask<bool> StartMainOptionalDownload()
    {
        if (!MainResult.HasOptionalUpdate)
        {
            return true;
        }

        try
        {
            var package = YooAssets.GetPackage(HotUpdateFlow.YooAssetPackage);
            var preDownloadOperation = package.PreDownloadContentAsync(MainResult.OptionalPkgVer);
            await preDownloadOperation;

            if (preDownloadOperation.Status == EOperationStatus.Succeed)
            {
                // 创建下载器进行实际下载，使用较低的并发数和较长的超时时间进行流量控制
                var downloader = preDownloadOperation.CreateResourceDownloader(
                    downloadingMaxNumber: 2,  // 降低并发数，减少流量压力
                    failedTryAgain: 3,
                    timeout: 120  // 增加超时时间，避免网络波动导致的失败
                );

                if (downloader.TotalDownloadCount > 0)
                {
                    MainResult.optionalDownloader = downloader;
                    downloader.OnDownloadProgressCallback = OnMainOptionalDownloadProgressUpdate;
                    downloader.OnDownloadOverCallback = OnOptionalDownloadOver;
                    downloader.OnDownloadErrorCallback = OnOptionalDownloadError;

                    Debug.Log($"Main非强更开始后台下载，文件数: {downloader.TotalDownloadCount}, 大小: {downloader.TotalDownloadBytes / 1024f / 1024f:F2}MB");

                    downloader.BeginDownload();
                    await downloader;

                    bool success = downloader.Status == EOperationStatus.Succeed;
                    if (success)
                    {
                        // 下载成功后保存版本信息
                        SaveLocalLatestVersion(HotUpdateFlow.YooAssetPackage, MainResult.OptionalPkgVer);
                        Debug.Log($"Main非强更下载成功，已保存版本记录: {MainResult.OptionalPkgVer}");
                    }

                    return success;
                }
                else
                {
                    Debug.Log("Main非强更：没有需要下载的内容");
                    return true;
                }
            }
            else
            {
                Debug.LogError($"Main非强更预下载失败: {preDownloadOperation.Error}");
                return false;
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Main非强更下载异常: {e.Message}");
            return false;
        }
    }

    private async UniTask<bool> StartAvatarOptionalDownload()
    {
        if (!AvatarResult.HasOptionalUpdate)
        {
            return true;
        }

        try
        {
            var package = YooAssets.GetPackage(GAvatarResManager.YooAssetPackage);
            var preDownloadOperation = package.PreDownloadContentAsync(AvatarResult.OptionalPkgVer);
            await preDownloadOperation;

            if (preDownloadOperation.Status == EOperationStatus.Succeed)
            {
                // 创建下载器进行实际下载，使用较低的并发数和较长的超时时间进行流量控制
                var downloader = preDownloadOperation.CreateResourceDownloader(
                    downloadingMaxNumber: 2,  // 降低并发数，减少流量压力
                    failedTryAgain: 3,
                    timeout: 120  // 增加超时时间，避免网络波动导致的失败
                );

                if (downloader.TotalDownloadCount > 0)
                {
                    AvatarResult.optionalDownloader = downloader;
                    downloader.OnDownloadProgressCallback = OnAvatarOptionalDownloadProgressUpdate;
                    downloader.OnDownloadOverCallback = OnOptionalDownloadOver;
                    downloader.OnDownloadErrorCallback = OnOptionalDownloadError;

                    Debug.Log($"Avatar非强更开始后台下载，文件数: {downloader.TotalDownloadCount}, 大小: {downloader.TotalDownloadBytes / 1024f / 1024f:F2}MB");

                    downloader.BeginDownload();
                    await downloader;

                    bool success = downloader.Status == EOperationStatus.Succeed;
                    if (success)
                    {
                        // 下载成功后保存版本信息
                        SaveLocalLatestVersion(GAvatarResManager.YooAssetPackage, AvatarResult.OptionalPkgVer);
                        Debug.Log($"Avatar非强更下载成功，已保存版本记录: {AvatarResult.OptionalPkgVer}");
                    }

                    return success;
                }
                else
                {
                    Debug.Log("Avatar非强更：没有需要下载的内容");
                    return true;
                }
            }
            else
            {
                Debug.LogError($"Avatar非强更预下载失败: {preDownloadOperation.Error}");
                return false;
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Avatar非强更下载异常: {e.Message}");
            return false;
        }
    }

    private void OnMainOptionalDownloadProgressUpdate(int totalDownloadCount, int currentDownloadCount,
        long totalDownloadBytes, long currentDownloadBytes)
    {
        float progress = (float)currentDownloadBytes / totalDownloadBytes * 100f;
        float sizeMB = (float)currentDownloadBytes / 1024f / 1024f;
        float totalMB = (float)totalDownloadBytes / 1024f / 1024f;

        // 后台下载，只记录日志，不更新UI（避免干扰用户）
        if (currentDownloadCount % 5 == 0 || progress >= 100f) // 每5个文件或完成时才打印日志
        {
            Debug.Log($"Main非强更后台下载进度: {progress:F1}% ({currentDownloadCount}/{totalDownloadCount}文件, {sizeMB:F2}MB/{totalMB:F2}MB)");
        }
    }

    private void OnAvatarOptionalDownloadProgressUpdate(int totalDownloadCount, int currentDownloadCount,
        long totalDownloadBytes, long currentDownloadBytes)
    {
        float progress = (float)currentDownloadBytes / totalDownloadBytes * 100f;
        float sizeMB = (float)currentDownloadBytes / 1024f / 1024f;
        float totalMB = (float)totalDownloadBytes / 1024f / 1024f;

        // 后台下载，只记录日志，不更新UI（避免干扰用户）
        if (currentDownloadCount % 5 == 0 || progress >= 100f) // 每5个文件或完成时才打印日志
        {
            Debug.Log($"Avatar非强更后台下载进度: {progress:F1}% ({currentDownloadCount}/{totalDownloadCount}文件, {sizeMB:F2}MB/{totalMB:F2}MB)");
        }
    }

    private void OnOptionalDownloadOver(bool isSucceed)
    {
        Debug.Log($"非强更后台下载完成. 结果:{isSucceed}");
    }

    private void OnOptionalDownloadError(string fileName, string error)
    {
        Debug.LogWarning($"非强更后台下载文件错误. 文件:{fileName}, 错误:{error} (后台下载错误不影响游戏运行)");
    }

    private void OnMainHotDownloadProgressUpdate(int totalDownloadCount, int currentDownloadCount,
        long totalDownloadBytes, long currentDownloadBytes)
    {
        float totalMB = totalDownloadBytes_together / 1024f / 1024f;
        float lastMb = (float)currentDownloadBytes / 1024f / 1024f;
        SetProgress(((float)currentDownloadBytes / (float)totalDownloadBytes_together * 100f) , $"{lastMb.ToString("0.00")}M/{totalMB.ToString("0.00")}M");
    }
    
    private void OnAvatarHotDownloadProgressUpdate(int totalDownloadCount, int currentDownloadCount, 
        long totalDownloadBytes, long currentDownloadBytes)
    {
        float totalMB = totalDownloadBytes_together / 1024f / 1024f;

        long mainDownloaderBytes = 0;
        if (MainResult != null && MainResult.downloader != null)
        {
            mainDownloaderBytes = MainResult.downloader.TotalDownloadBytes;
        }
        
        float lastMb = (float)(currentDownloadBytes + mainDownloaderBytes) / 1024f / 1024f;
        SetProgress((((float)currentDownloadBytes + mainDownloaderBytes) / (float)totalDownloadBytes_together * 100f) , $"{lastMb.ToString("0.00")}M/{totalMB.ToString("0.00")}M");
    }
    
    private void OnHotDownloadOver(bool isSucceed)
    {
        Debug.Log($"HotUpdateFlow Download Over. Result:{isSucceed}");
    }

    private void OnHotDownloadError(string fileName, string error)
    {
        Debug.Log($"HotUpdateFlow Download File Error. file:{fileName}, error:{error}");

        var errorMsg = HotUpdateLangCfg.GetStr(HUKey.updateSysException, (int) HotUpdateCore.ErrorCode.YooAssets_Download_Error);                
        OpenConfirmUI(errorMsg, () =>
        {
            Application.Quit(0);
        }, null, 0);
    }

    public void SetProgress(float downloadProgress,string param)
    {
        MainLoadingUI.Ins.SetProgressDesc(MainLoadingUI.ProgressMode.Download,param);
        MainLoadingUI.Ins.SetProgressValue(downloadProgress);
    }

    public void Dispose()
    {
        result = null;
    }

    /// <summary>
    /// 判断版本是否为offline状态
    /// </summary>
    /// <param name="version">版本号</param>
    /// <returns>是否为offline</returns>
    private bool IsVersionOffline(string version)
    {
        return string.IsNullOrEmpty(version) || version.ToLower() == "offline";
    }

    /// <summary>
    /// 安全获取版本号，处理字段不存在、为空或为offline的情况
    /// </summary>
    /// <param name="appVersion">应用版本</param>
    /// <param name="versionKey">版本字段key</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>版本号</returns>
    private string GetVersionHashSafely(string appVersion, string versionKey, string defaultValue)
    {
        try
        {
            // 检查应用版本是否存在
            if (!VersionData.ContainsKey(appVersion))
            {
                Debug.LogWarning($"应用版本 {appVersion} 不存在，使用默认值: {defaultValue}");
                return defaultValue;
            }

            var versionInfo = VersionData[appVersion];

            // 检查版本字段是否存在
            if (!versionInfo.ContainsKey(versionKey))
            {
                Debug.Log($"版本字段 {versionKey} 不存在，使用默认值: {defaultValue}");
                return defaultValue;
            }

            // 获取版本值
            var versionValue = versionInfo[versionKey];
            if (versionValue == null)
            {
                Debug.Log($"版本字段 {versionKey} 为null，使用默认值: {defaultValue}");
                return defaultValue;
            }

            string versionStr = versionValue.ToString().Trim();

            // 检查是否为空或offline
            if (string.IsNullOrEmpty(versionStr) || versionStr.ToLower() == "offline")
            {
                Debug.Log($"版本字段 {versionKey} 为空或offline，使用默认值: {defaultValue}");
                return defaultValue;
            }

            Debug.Log($"获取版本字段 {versionKey}: {versionStr}");
            return versionStr;
        }
        catch (Exception e)
        {
            Debug.LogError($"获取版本字段 {versionKey} 时发生异常: {e.Message}，使用默认值: {defaultValue}");
            return defaultValue;
        }
    }

    /// <summary>
    /// 获取本地已下载的最新版本号
    /// 通过检查本地缓存文件来确定最新版本
    /// </summary>
    /// <param name="packageName">包名</param>
    /// <returns>本地最新版本号，如果没有则返回null</returns>
    private string GetLocalLatestVersion(string packageName)
    {
        try
        {
            // 使用PlayerPrefs存储已下载的版本信息
            string key = $"LocalLatestVersion_{packageName}";
            string localVersion = PlayerPrefs.GetString(key, "");

            if (!string.IsNullOrEmpty(localVersion))
            {
                Debug.Log($"从本地记录获取到{packageName}的最新版本: {localVersion}");
                return localVersion;
            }

            Debug.Log($"{packageName}没有本地版本记录");
            return null;
        }
        catch (Exception e)
        {
            Debug.LogWarning($"获取{packageName}本地版本失败: {e.Message}");
            return null;
        }
    }

    /// <summary>
    /// 保存本地已下载的版本号
    /// </summary>
    /// <param name="packageName">包名</param>
    /// <param name="version">版本号</param>
    private void SaveLocalLatestVersion(string packageName, string version)
    {
        try
        {
            string key = $"LocalLatestVersion_{packageName}";
            PlayerPrefs.SetString(key, version);
            PlayerPrefs.Save();
            Debug.Log($"保存{packageName}的本地版本记录: {version}");
        }
        catch (Exception e)
        {
            Debug.LogWarning($"保存{packageName}本地版本失败: {e.Message}");
        }
    }

    /// <summary>
    /// 比较版本号（全数字格式，例如：250101022720）
    /// </summary>
    /// <param name="version1">版本1</param>
    /// <param name="version2">版本2</param>
    /// <returns>1: version1 > version2, 0: 相等, -1: version1 < version2</returns>
    private int CompareVersionNumbers(string version1, string version2)
    {
        if (string.IsNullOrEmpty(version1) || string.IsNullOrEmpty(version2))
        {
            Debug.LogWarning($"版本号比较失败，存在空值: v1={version1}, v2={version2}");
            return 0;
        }

        // 尝试解析为数字进行比较
        if (long.TryParse(version1, out long v1) && long.TryParse(version2, out long v2))
        {
            if (v1 > v2) return 1;
            if (v1 < v2) return -1;
            return 0;
        }
        else
        {
            // 如果不是纯数字，则使用字符串比较
            Debug.LogWarning($"版本号不是纯数字格式，使用字符串比较: v1={version1}, v2={version2}");
            return string.Compare(version1, version2, StringComparison.Ordinal);
        }
    }

    #region 请求服务器
    private const int MaxRetries = 5; // 最大重试次数
    private const int retry_time = 1500;
    private bool killMyServerReq = false;
    public JsonData VersionData;
    public async UniTask<string> GetHotVersionData()
    {
        int retryCount = 0;
        killMyServerReq = false;
        while (retryCount < MaxRetries)
        {
            try
            {
                if (killMyServerReq)
                {
                    return string.Empty;
                }
                string data = await RequestHotVersionAsync();
                if (string.IsNullOrEmpty(data))
                {
                    retryCount++;
                    if (retryCount < MaxRetries)
                    {
                        await UniTask.Delay(retry_time);
                    }
                    else
                    {
                        return string.Empty;
                    }
                }
                else
                {

                    return data; // 成功则返回数据
                }
            }
            catch (Exception ex)
            {
                retryCount++;
                if (retryCount < MaxRetries)
                {
                    await UniTask.Delay(retry_time);
                }
                else
                {
                    return string.Empty;
                }
            }
        }
        return string.Empty;
    }

    public async UniTask<string> GetHotVersionDataFromCdn()
    {
        // await Task.Delay(10000);
        return await GHttpManager.instance.GetAsync(AppConst.HotUpdate_version_url , MaxRetries);
    }
    
    
    private async UniTask<string> RequestHotVersionAsync()
    {
        string versionUrl = AppConst.HotfixUrlV2;
        string simulDeviceId = CrashSightAgent.GetSDKDefinedDeviceID();//这个 V2Req内的did以前曾经承担了一层过滤类的职能，防竞对使用

        Debug.Log($" DeviceId: {simulDeviceId}");
        
        
        var reqData = new gethotfixV2Req { device_id = simulDeviceId };
        try {
            var result = await GHttpManager.instance.PostAsync(versionUrl,
                JsonMapper.ToJson(reqData));
            if (result.code != 200)
            {
                return String.Empty;
            }

            return result.data;
        }
        catch (System.Exception e)
        {
            Debug.LogError("Handle HotVersionReq err:"+e.ToString());
            return String.Empty;
        }
       
    }
    
    public class gethotfixV2Req
    {
        public string device_id;
    }

    public class gethotfixV2Resp
    {
        public string data;
        public int error_code;
    }
    #endregion

    #region 弹窗
    /// <summary>
    /// 打开面板
    /// </summary>
    /// <param name="content">内容</param>
    /// <param name="confirmFunc">确认回调</param>
    /// <param name="cancelFunc">取消回调</param>
    /// <param name="type">是否单按钮(默认为2) 0无按钮  1单按钮  2双按钮</param>
    /// <param name="title">标题</param>
    /// <param name="confirmLabel">确认按钮label</param>
    /// <param name="cancelLabel">取消按钮label</param>
    public void OpenConfirmUI(string content, Action confirmFunc, Action cancelFunc = null, int type = 2,
        string confirmLabel = "", string cancelLabel = "")
    {
        if (string.IsNullOrEmpty(confirmLabel))
        {
            confirmLabel = HotUpdateLangCfg.GetStr(HUKey.confirmBtn);
        }
        if (string.IsNullOrEmpty(cancelLabel))
        {
            cancelLabel = HotUpdateLangCfg.GetStr(HUKey.noWifiUpdateNoBtn);
        }
        MainLoadingUI.Ins.OnShowNetErrorConfirmUIOpen(content,confirmFunc,cancelFunc,type,confirmLabel,cancelLabel);
    }
    
    private async UniTask<bool> CheckInternet(float totalMB)
    {
        switch (Application.internetReachability)
        {
            case NetworkReachability.NotReachable:
                return false;
            case NetworkReachability.ReachableViaCarrierDataNetwork:
                if (totalMB > 10)
                {
                    var tcs = new UniTaskCompletionSource<bool>();
                    string msg = HotUpdateLangCfg.GetStr(HUKey.noWifiUpdateTip, $"{totalMB.ToString("0.00")}MB");
                    OpenConfirmUI(msg,
                        () => tcs.TrySetResult(true),
                        () =>
                        {
                            tcs.TrySetResult(false);
                            Application.Quit();
                        },
                        2,
                        HotUpdateLangCfg.GetStr(HUKey.noWifiUpdateYesBtn),
                        HotUpdateLangCfg.GetStr(HUKey.noWifiUpdateNoBtn)
                    );
                    return await tcs.Task;
                }
                else
                {
                    return true;
                }
            default:
                return true;
        }
    }
    #endregion
}

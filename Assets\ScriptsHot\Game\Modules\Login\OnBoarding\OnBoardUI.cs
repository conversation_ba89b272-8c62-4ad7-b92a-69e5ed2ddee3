/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/07/18 17:10:41 星期四
# 功能：Nothing
****************************************************
*/

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using CommonUI;
using DG.Tweening;
using FairyGUI;
using Google.Protobuf.Collections;
using Msg;
using Msg.basic;
using Msg.core;
using Msg.dialog_task;
using Msg.incentive;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Managers;
using ScriptsHot.Game.Modules.Shop;
using UIBind.OnBoard;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Login.OnBoarding
{
    public partial class OnBoardUI : FlowModelUIBase<OnBoardPanel>
    {
        public override void OnBackBtnClick()
        {
        }

        public OnBoardUI(string name) : base(name) { }
        public override string uiLayer => UILayerConsts.Home;
        protected override bool isFullScreen => true;
        private bool _isGoalListInitialized;
        private bool _isLevelListInitialized;

        private PB_LearningGoalEnum _selectedGoalItemIdx;  // OnBoardingGoalCfgs 表 id
        private OnBoardingGoalCfg CurrentGoal => Cfg.T.TBOnBoardingGoals.Get((int)_selectedGoalItemIdx);
        private List<PB_LearningSceneEnum> _selectedHabitItemIdxs = new();  // OnBoardingHabitCfgs 表 id
        private CEFRLevel _selectedLevelItemIdx;   // OnBoardingLevelCfgs 表 id
        private OnBoardingLevelCfg CurrentLevel => Cfg.T.TBOnBoardingLevels.Get((int)_selectedLevelItemIdx);
        private bool _isOnCamaraMoving;
        private GameObject _headBar;

        private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);
        private LoginController _loginController => this.GetController<LoginController>(ModelConsts.Login);
        private LoginOnBoardingModel _boardingModel => GetModel<LoginOnBoardingModel>(ModelConsts.Login);
        private MainController _mainController => GetController<MainController>(ModelConsts.Main);
        
        private static Vector3 CAMERA_START_POS1 = new Vector3(36.79f,8.748f,65.593f);
        private static Vector3 CAMERA_START_ANGEL1 = new Vector3(10f, 0f, 0f);
        private static Vector3 CAMERA_START_POS2 = new Vector3(36.79f,8.748f,79f);
        private static Vector3 CAMERA_START_POS3 = new Vector3(36.79f,2.47f,91.26f);
        private static Vector3 CAMERA_START_ANGEL3 = new Vector3(13.838f, 0f, 0f);
        
        private string _payAbValue = String.Empty;

        private bool _flowReady = false;
        private bool _fsmReady = false;
        private bool _isShow = false;

        //状态机是否已经启动
        private bool _fsmStarted = false;
        
        private enum OnBoardState
        {
            None = 0,
            ZoomIn,
            ShowFlowTips1,
            ShowFlowTips2,
            Recommend,
            ShowPay,
            Dialogue,
            Finish,
            
            
            OnBoardInfo,
        }

        private LinearFSM<OnBoardState> stateFsm;

        protected override void OnInit(GComponent uiCom)
        {
            // AddUIEvent(ui.imgBG.onClick, OnFullScreenClicked);
            AddUIEvent(ui.btnBack.onClick, OnBtnBackClicked);
            AddUIEvent(ui.recommend.jumpBtm.onClick, OnRecommendJumpBtnClick);
            AddUIEvent(ui.recommend.BtnA1.onClick, OnRecommendBtnA1BtnClick);   
            
            Notifier.instance.RegisterNotification(NotifyConsts.OnboardUI_ShowBack, OnShowBack);
            
            InitView();

            InitStates();
        }

        private void OnShowBack(string s, object body)
        {
            bool showBack = (bool) body;
            if (showBack)
            {
                this.ui.ctrl.selectedPage = "ShowBack";
            }
            else
            {
                this.ui.ctrl.selectedPage = "Normal";
            }
        }

        private void RegisterDisabledNextBtns(params GButton[] nextBtns)
        {
            foreach (var btn in nextBtns)
            {
                btn.SetLanguageKeyArgs(LanguageType.MotherTongue, "avatar_talk_button_next");
            }
        }

        private void RegisterNextBtns(params GButton[] nextBtns)
        {
            foreach (var btn in nextBtns)
            {
                AddUIEvent(btn.onClick, OnClickNext);
                btn.SetLanguageKeyArgs(LanguageType.MotherTongue, "avatar_talk_button_next");
            }
        }

        private void InitView()
        {
            VisibleAll();
            // InitFlow();
            BeforeUIPassManager.AddRenderer(ui.comFlow.comAvatar.comTxt.blurBack.displayObject);
            CreateFlowModel(ui.comFlow.loader.asCom, true, () =>
            {
                _flowAnimator.SetTrigger(ANI_TRIGGER_IDLE);
                ui.comFlow.com.visible = false;
                _flowReady = true;
                CheckAndStartFsm();
            });

            SetImgBgEnable(false);
            I18NText.SetAllTextLanguage(ui.com, LanguageType.MotherTongue);
        }

        private void SetImgBgEnable(bool value)
        {
            ui.imgBG.enabled = value;
        }

        private void VisibleAll()
        {
            ui.comSetName.inputName.hideInput = true;
            ui.comSystemTips.com.visible = false;
            ui.btnBack.visible = false;
            ui.btnSwitchRoleLeft.visible = false;
            ui.btnSwitchRoleRight.visible = false;
            ui.comSetName.com.visible = false;
            ui.comFlowDialog.com.visible = false;
            ui.comFlowRightBubble.com.visible = false;
            ui.btnNextDisable.visible = false;
            ui.btnNext.visible = false;
            ui.comFlowTopBubble.com.visible = false;
            ui.recommend.com.visible = false;
            ui.comFlow.com.visible = false;
            ui.comFlow.bubbleSpine.visible = false;
        }

        private async void InitStates()
        {
            _payAbValue = "BEFORE_CALVIN";
            // await AbTestUtil.ReqAbTestValue("onboarding-paywall-location",
            //     msg =>
            //     {
            //         _payAbValue = msg.data.value;
            //         VFDebug.Log("阶段：_payAbValue:" + _payAbValue);
            //     }
            // );
            if (_boardingModel.CurOnBoardingState == TaskRecord.CreateRole)
            {
                InitSelectStepFsm();
            }
            else if (_boardingModel.CurOnBoardingState == TaskRecord.Dialogue)
            {
                // InitDialogueStepFsm();
                InitPayStepFsm();
            }
            else if (_boardingModel.CurOnBoardingState == TaskRecord.ShowPay)
            {
                InitPayStepFsm();
            }

            _fsmReady = true;
            CheckAndStartFsm();
        }

        protected override void OnShow()
        {
            base.OnShow();
            var dic = BI.ApplyBiDic();
            BI.Collect(BIEvent.appear_home_onboarding_page,dic);
            _isShow = true;
            CheckAndStartFsm();
            GetController<ShopController>(ModelConsts.Shop).OnBoardReq();
        }
        
        private void CheckAndStartFsm()
        {
            if (_flowReady && _fsmReady && _isShow)
            {
                FsmStart();
            }
        }
        
        private void FsmStart()
        {
            if (_fsmStarted) return;
            _fsmStarted = true;
            stateFsm.Start();
        }

        private void OnClickNext()
        {
            SoundManger.instance.PlayUI("onboard_next");
            Next();
        }

        private void Next()
        {
            SetImgBgEnable(false);
            
            var oldState = stateFsm.CurrentKey;
            if(!stateFsm.Next()) return;
            _flowAudioSource.Stop();

            PlayAni(oldState, stateFsm.CurrentKey);
            VFDebug.Log($"current onBoard state: {stateFsm.CurrentKey}");
        }

        protected override void OnFlowBtnClicked()
        {
        }

        private void OnBtnBackClicked()
        {
            var oldState = stateFsm.CurrentKey;
            if(!stateFsm.Prev()) return;
            _flowAudioSource.Stop();

            PlayAni(oldState, stateFsm.CurrentKey);
            SoundManger.instance.PlayUI("onboard_next");
            VFDebug.Log($"current onBoard state: {stateFsm.CurrentKey}");
        }

        #region 动画
        private void PlayAni(OnBoardState preState, OnBoardState curState)
        {
            if (preState == OnBoardState.ShowFlowTips1 && curState == OnBoardState.ShowFlowTips2)
            {
                PlayFlowTopBubbleAni();
            }
            else if (curState == OnBoardState.Recommend)
            {
                //ui.switch_recommend.Play();
            }
        }

        private void PlayFlowTopBubbleAni()
        {
            ui.comFlowTopBubble.com.pivot = new Vector2(0.5f, 1f);
            ui.comFlowTopBubble.com.scale = new Vector2(0.5f, 0.5f);
            ui.comFlowTopBubble.com.alpha = 0.5f;
            ui.comFlowTopBubble.com.TweenFade(1, 0.3f);
            ui.comFlowTopBubble.com.TweenScale(Vector2.one, 0.3f).OnComplete(() =>
            {
                ui.comFlowTopBubble.com.pivot = new Vector2(0, 0);
            });
        }
        #endregion

 

        private Action _action;
        
        private void OnShowOnBoardInfoUI()
        {
            _action = Next;
            VisibleAll();
            GetUI<OnBoardInfoUI>(UIConsts.OnBoardInfoUI).Show(null, _action);
            ui.ctrl.selectedPage = "ShowBack";
        }
        
        private void ProSave(TaskRecord pro,Action callback = null)
        {
            _boardingModel.SetCurOnBoardingState(pro, (m) =>
            {
                callback?.Invoke();
            });
        }


        #region Finish
        private void OnFinishEnter()
        {
            this.ui.ctrl.selectedPage = "Normal";
            AFDots.Click_Study_plan_next();
            
            this.GetUI<SceneLoadingUI>(UIConsts.SceneLoading).Show();
            
            ProSave(TaskRecord.ShowDiamond,PlayFlowFadeOut);
        }

        private void PlayFlowFadeOut()
        {
            Hide();
            _loginController.EnterHomepageAfterLogin();
            GameObject.Destroy(_headBar);
            return;
                
            _flowAnimator.transform.DOLocalRotate(new Vector3(0, 90, 0), 0.2f).OnComplete(() =>
            {
                _flowAnimator.SetTrigger(ANI_TRIGGER_MALL_JUMP);
                var curPos = ui.comFlow.loader.xy;
                ui.comFlow.loader.TweenMoveX(curPos.x - 200, 1f).OnComplete(() =>
                {
                    Hide();
                    this.GetUI<SceneLoadingUI>(UIConsts.SceneLoading).Show();
                    // _loginController.DoOnBoardLoginFromGame();
                    _loginController.EnterHomepageAfterLogin();
                    GameObject.Destroy(_headBar);
                    _flowAnimator.transform.localEulerAngles = Vector3.zero;
                });
            });

        }
        #endregion

        #region 播放TTS
        private int ttsId = 0;
        public async void PlayTTS(string rawContent, Action callback = null)
        {
            var content = StripHtmlTags(rawContent);
            ttsId++;
            CS_GetAudioForFlowReq msg = new CS_GetAudioForFlowReq();
            msg.content = content;
            msg.unique_id = ttsId.ToString();
            var resp = await MsgManager.instance.SendAsyncMsg<SC_GetAudioForFlowAck>(msg);
            if (resp == null)
                return;
            var data = resp.data;
            
            if (data.audio_id != 0 && data.unique_id == ttsId.ToString())
            {
                if (_flowAudioSource)
                {
                    _flowAudioSource.Stop();
                }
                TTSManager.instance.PlayTTS(data.audio_id, callback, true, TTSManager.AudioChannel.Flow, default, _flowAudioSource);
            }
        }
        
        public string StripHtmlTags(string input)
        {
            return Regex.Replace(input, "<.*?>", string.Empty);
        }

        public void StopTTS()
        {
            if (_flowAudioSource)
            {
                _flowAudioSource.Stop();
            }
        }
        #endregion

        #region 工具人

        private void OnFullScreenClicked()
        {
            Next();
        }

        private List<OnBoardingHabitCfg> GetHabitsOfCurrentGoal()
        {
            return Cfg.T.TBOnBoardingHabits.DataList.FindAll(s => s.goalId == (int)_selectedGoalItemIdx);
        }

        #endregion

        protected override void HandleNotification(string name, object body)
        {
            base.HandleNotification(name, body);
            switch (name)
            {
                case NotifyConsts.OnBoardShopUIOver:
                    Next();
                    break;
            }
        }

        protected override string[] ListNotificationInterests()
        {
            return new string[] { NotifyConsts.OnBoardShopUIOver };
        }
    }
}
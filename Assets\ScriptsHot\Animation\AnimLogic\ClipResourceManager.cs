using System.Collections.Generic;
using UnityEngine;
using YooAsset;

namespace AnimationSystem
{
    /// <summary>
    /// 动画片段资源管理器
    /// 负责管理对话时用到的动画资源
    /// TODO:现在是统一预加载,后续随着资源增多,预加载肯定不行,要改成动态装载/卸载。
    /// </summary>
    public class ClipResourceManager
    {
        private static ClipResourceManager _instance;
        public static ClipResourceManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ClipResourceManager();
                }
                return _instance;
            }
        }

        private static int currClipID = 0;

        // 用于存储多种类型动画片段的字典映射，键为类型ID，值为动画片段字典
        private Dictionary<int, Dictionary<string, AnimationClip>> _clipTypeMap = new Dictionary<int, Dictionary<string, AnimationClip>>();
        
        // Int可以直接改String,但是要改的太多了,先暂时这样 TODO.
        private Dictionary<string, int> _clipStringToInt = new ();

        // 标记是否已初始化
        private bool _isInitialized = false;
        
        // 私有构造函数
        private ClipResourceManager()
        {
            // 添加基础类型
            AddClipType("Girl"); // 类型0（原女性）
            AddClipType("Boy"); // 类型1（原男性）

            LoadAllClips();
        }

        /// <summary>
        /// 初始化并加载所有动画资源
        /// </summary>
        /// <returns>加载的资源数量</returns>
        public int LoadAllClips()
        {
            if (_isInitialized)
            {
                // 统计所有字典中的条目数
                int totalCount = 0;
                foreach (var dict in _clipTypeMap.Values)
                {
                    totalCount += dict.Count;
                }
                return totalCount;
            }

            // 确保至少有类型0和1
            // if (!_clipTypeMap.ContainsKey(0))
            // {
            //     AddClipType(0);
            // }
            // if (!_clipTypeMap.ContainsKey(1))
            // {
            //     AddClipType(1);
            // }

            // 清理所有资源
            Clear();
            
            int loadedCount = 0;

            // 加载所有类型的动画资源
            foreach (var typeId in _clipTypeMap.Keys)
            {
                string prefix;
                if (typeId == 0)
                    prefix = "Girl";
                else if (typeId == 1)
                    prefix = "Boy";
                else
                    prefix = $"Type{typeId}_";
                
                // 为每个类型加载动画资源
                int typeLoadCount = LoadClipsForType(typeId, prefix);
                loadedCount += typeLoadCount;
            }

            _isInitialized = true;
            VFDebug.Log($"ClipResourceManager初始化完成，成功加载了{loadedCount}个动画资源");
            return loadedCount;
        }
        
        /// <summary>
        /// 添加新的动画类型
        /// </summary>
        /// <param name="typeId">类型ID</param>
        /// <returns>是否添加成功</returns>
        // public bool AddClipType(int typeId)
        // {
        //     // 检查类型ID是否已存在
        //     if (_clipTypeMap.ContainsKey(typeId))
        //     {
        //         VFDebug.LogWarning($"类型ID {typeId} 已存在");
        //         return false;
        //     }
        //     
        //     // 添加新的类型字典
        //     _clipTypeMap[typeId] = new Dictionary<string, AnimationClip>();
        //     return true;
        // }

        public int AddClipType(string name)
        {
            if (_clipStringToInt.ContainsKey(name))
            {
                return _clipStringToInt[name];
            }
            else
            {
                _clipStringToInt.Add(name, currClipID);
                _clipTypeMap[currClipID] = new Dictionary<string, AnimationClip>();
                LoadClipsForType(currClipID, name);
                currClipID++;
                return _clipStringToInt[name];
            }
        }
        
        /// <summary>
        /// 为指定类型加载动画资源
        /// </summary>
        /// <param name="typeId">类型ID</param>
        /// <param name="prefix">资源前缀</param>
        /// <returns>加载的资源数量</returns>
        public int LoadClipsForType(int typeId, string prefix)
        {
            // 检查类型ID是否存在
            if (!_clipTypeMap.ContainsKey(typeId))
            {
                VFDebug.LogWarning($"类型ID {typeId} 不存在，请先添加类型");
                return 0;
            }
            
            var targetDict = _clipTypeMap[typeId];
            int loadedCount = 0;
            
            // 获取配置表中的所有动画项
            var clipDataList = Cfg.T.TBAvatarBodyClip.DataList;
            if (clipDataList == null || clipDataList.Count == 0)
            {
                VFDebug.LogWarning("配置表中未找到动画数据");
                return 0;
            }
            
            // 遍历配置表，加载动画资源
            foreach (var clipData in clipDataList)
            {
                string clipName = clipData.clipName;
                
                // 加载基本动画资源
                AnimationClip clip = LoadAnimationClip(clipName, typeId, prefix);
                if (clip != null)
                {
                    targetDict[clipName] = clip;
                    loadedCount++;
                }
                
                // 加载M变体动画资源
                clip = LoadAnimationClip(clipName + 'M', typeId, prefix);
                if (clip != null)
                {
                    targetDict[clipName + 'M'] = clip;
                    loadedCount++;
                }
            }
            
            VFDebug.Log($"为类型ID {typeId} 加载了 {loadedCount} 个动画资源");
            return loadedCount;
        }

        /// <summary>
        /// 根据动画名称获取动画片段
        /// </summary>
        /// <param name="clipName">动画名称</param>
        /// <param name="animationTypeId">人种编号</param>
        /// <returns>动画片段，如果未找到则返回null</returns>
        public AnimationClip GetClip(string clipName, int animationTypeId)
        {
            if (!_isInitialized)
            {
                this.LoadAllClips();
                this._isInitialized = true;
            }

            if (string.IsNullOrEmpty(clipName))
            {
                VFDebug.LogWarning("clipName为空");
                return null;
            }
            
            // 从类型映射中获取对应的字典
            Dictionary<string, AnimationClip> targetDict;
            if (!_clipTypeMap.TryGetValue(animationTypeId, out targetDict))
            {
                // VFDebug.LogWarning($"未找到人种编号 {animationTypeId} 对应的动画字典，使用默认人种(0)");
                // if (!_clipTypeMap.TryGetValue(0, out targetDict))
                // {
                //     VFDebug.LogError("默认人种(0)也不存在，无法获取动画");
                //     return null;
                // }
                return null;
            }
            
            // 从字典中查找并返回
            AnimationClip clip;
            if (Random.Range(0.0f, 1.0f) < 0.5f && targetDict.TryGetValue(clipName + 'M', out clip))
            {
                return clip;
            }
            else if(targetDict.TryGetValue(clipName, out clip))
            {
                return clip;
            }
            else
            {
                VFDebug.LogWarning($"未找到名为{clipName}的动画资源，人种编号: {animationTypeId}");
                return null;
            }
        }

        /// <summary>
        /// 加载单个动画片段
        /// </summary>
        /// <param name="clipName">动画名称</param>
        /// <param name="type">类型</param>
        /// <returns>加载的动画片段，如果加载失败则返回null</returns>
        private AnimationClip LoadAnimationClip(string clipName, int type)
        {
            if (string.IsNullOrEmpty(clipName))
            {
                VFDebug.LogWarning("尝试加载的clipName为空");
                return null;
            }
            
            string prefix = type == 1 ? "Boy" : "Girl";
            return LoadAnimationClip(clipName, type, prefix);
        }
        
        /// <summary>
        /// 加载单个动画片段（带前缀）
        /// </summary>
        /// <param name="clipName">动画名称</param>
        /// <param name="type">类型</param>
        /// <param name="prefix">资源前缀</param>
        /// <returns>加载的动画片段，如果加载失败则返回null</returns>
        private AnimationClip LoadAnimationClip(string clipName, int type, string prefix)
        {
            if (string.IsNullOrEmpty(clipName))
            {
                VFDebug.LogWarning("尝试加载的clipName为空");
                return null;
            }
            
            string fullName = prefix + clipName;
            return LoadResourceByName(fullName);
        }
        
        /// <summary>
        /// 根据资源名称加载资源
        /// </summary>
        /// <param name="fullName">完整资源名称</param>
        /// <returns>加载的动画片段</returns>
        private AnimationClip LoadResourceByName(string fullName)
        {
            // 检查资源路径是否有效
            if (YooAssets.CheckLocationValid(fullName))
            {
                var handle = YooAssets.LoadAssetSync(fullName);
                if (handle != null && handle.AssetObject != null)
                {
                    return handle.AssetObject as AnimationClip;
                }
            }
            else
            {
                // 尝试从Resources加载
                AnimationClip clip = Resources.Load<AnimationClip>(fullName);
                if (clip != null)
                {
                    return clip;
                }
            }
            
            VFDebug.LogWarning($"动画片段 {fullName} 未找到(YooAsset和Resources下都无)");
            return null;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Clear()
        {
            // 清理所有字典
            foreach (var dict in _clipTypeMap.Values)
            {
                dict.Clear();
            }
            
            _isInitialized = false;
            VFDebug.Log("ClipResourceManager资源已清理");
        }
    }
} 
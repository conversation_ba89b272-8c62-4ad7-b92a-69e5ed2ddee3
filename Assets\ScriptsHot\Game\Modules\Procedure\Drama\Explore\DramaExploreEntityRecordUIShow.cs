using Msg.explore;
using ScriptsHot.Game.Modules.Explore;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Procedure.Drama
{
    /// <summary>
    /// 记录UI显示
    /// </summary>
    public class DramaExploreEntityRecordUIShow : BaseDrama
    {
        public override void OnEvent()
        {
            base.OnEvent();
        }

        public override void Do(bool canFinish = true)
        {
            base.Do();
            Notifier.instance.SendNotification(NotifyConsts.ExploreShowRecordUI);
            Finish();
        }

        public override void OnFinish(bool isBreak)
        {
            base.OnFinish(isBreak);
        }
        
        public override void Dispose()
        {
            base.Dispose();
        }
    }
} 
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class PhoneInstruction : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "PhoneInstruction";
        public static string url => "ui://cmoz5osjn32ouvptcy";

        public Controller stateCtrl;
        public GTextField textCenter;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(PhoneInstruction));
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            stateCtrl = GetControllerAt(0);
            textCenter = GetChildAt(0) as GTextField;
        }
        public override void Dispose()
        {
            stateCtrl = null;
            textCenter = null;

            base.Dispose();
        }
    }
}
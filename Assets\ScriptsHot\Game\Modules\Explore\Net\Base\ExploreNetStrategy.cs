using System;
using System.Threading;
using Grpc.Core;
using Google.Protobuf;

using Msg.basic;
using Msg.explore;
using Msg.speech;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Explore.ExploreType.Base;
using UnityEngine;

public partial class ExploreNetStrategy
{
    protected CancellationTokenSource _tokenSource = null;
    protected ExploreController _controller;

    public ExploreNetStrategy()
    {
        _controller = ControllerManager.instance.GetController<ExploreController>(ModelConsts.Explore) as ExploreController;
    }

    public virtual void AddEvent()
    {
    }

    public virtual void RemoveEvent()
    {
    }

    public virtual void Update(int interval)
    {
        UpdateASR(interval);
    }
    
    public virtual void Stop()
    {
        
    }
    
    public virtual async void SendRecommendReq(long entityId)
    {
       
    }
    
    public virtual async void SendDialogReq(PB_Explore_DialogUpBizEvent type,long taskId,PB_DialogMode dialogMode)
    {
       
    }

    #region 语音1v1部分，"userSettingMsg", "matchingMsg", "userChatMsg"
    public virtual async void SendSetting()
    {

    }

    public virtual async void SendMatchingMsg(PB_MatchingUpMsg.upBizMsgOneofCase msgCase, long matchRecordId)
    {

    }

    public virtual async void SendUserChatExitMsg(long selfUserId,long partnerUserId)//PB_Explore_DialogUpBizEvent type, long taskId, PB_DialogMode dialogMode)
    {

    }

    public virtual async void SendRawAudioUp(ByteString strByte)
    {

    }
    #endregion

    
}

public class ExploreTTSVO
{
    public long recordId;
    public bool isPlaying = false;
    public Action playEndCallback = null;
    public Action<bool> playStartCallback = null;
    public string audioType = ChatCellAudioType.Normal;

    public bool isAvatar;
    public bool isPreload;
    public float playTime;
    public float rate = 1f;
    public AudioClip audioClip;
    public bool isReadyForPlaying;
    public Action preloadCallback = null;
    public byte[] audioClipData = Array.Empty<byte>();
    public ExploreAudioChannel channel = ExploreAudioChannel.Avatar;
}

public enum ExploreAudioChannel
{
    Avatar,
    AvatarStream,
    Player,
    Flow,
    TTS,
}

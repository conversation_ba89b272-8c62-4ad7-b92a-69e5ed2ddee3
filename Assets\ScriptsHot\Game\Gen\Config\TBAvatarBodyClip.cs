
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;



public partial class TBAvatarBodyClip
{
    private readonly System.Collections.Generic.Dictionary<string, AvatarBodyClipCfg> _dataMap;
    private readonly System.Collections.Generic.List<AvatarBodyClipCfg> _dataList;
    
    public TBAvatarBodyClip(ByteBuf _buf)
    {
        _dataMap = new System.Collections.Generic.Dictionary<string, AvatarBodyClipCfg>();
        _dataList = new System.Collections.Generic.List<AvatarBodyClipCfg>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            AvatarBodyClipCfg _v;
            _v = AvatarBodyClipCfg.DeserializeAvatarBodyClipCfg(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.clipKey, _v);
        }
    }

    public System.Collections.Generic.Dictionary<string, AvatarBodyClipCfg> DataMap => _dataMap;
    public System.Collections.Generic.List<AvatarBodyClipCfg> DataList => _dataList;

    public AvatarBodyClipCfg GetOrDefault(string key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public AvatarBodyClipCfg Get(string key) => _dataMap[key];
    public AvatarBodyClipCfg this[string key] => _dataMap[key];

    public void ResolveRef(Tables tables)
    {
        foreach(var _v in _dataList)
        {
            _v.ResolveRef(tables);
        }
    }

}




/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Main
{
    public partial class BottomTabRegion : UIBindT
    {
        public override string pkgName => "Main";
        public override string comName => "BottomTabRegion";

        public GImage BottomTabRegionBg;
        public GImage BottomTabSelected;
        public GGraph BottomTabRegionBg_white;
        public GImage BottomTabRegionBg_black;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            BottomTabRegionBg = (GImage)com.GetChildAt(0);
            BottomTabSelected = (GImage)com.GetChildAt(1);
            BottomTabRegionBg_white = (GGraph)com.GetChildAt(2);
            BottomTabRegionBg_black = (GImage)com.GetChildAt(3);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            BottomTabRegionBg = null;
            BottomTabSelected = null;
            BottomTabRegionBg_white = null;
            BottomTabRegionBg_black = null;
        }
    }
}
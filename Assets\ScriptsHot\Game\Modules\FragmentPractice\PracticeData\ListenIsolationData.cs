﻿using System.Collections.Generic;
using System.Linq;
using Msg.question;
using UIBind.FragmentPractice;
using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.FragmentPractice
{
    public class ListenIsolationData : APracticeData
    {
        public PB_ListenSenAndChooseWordQuestion IsolationQuestion { get; private set; }
        
        public ListenIsolationData(long grpId, PB_QuickPracticeInfo data) : base(grpId, data)
        {
            if (data.dataCase == PB_QuickPracticeInfo.dataOneofCase.listen_sen_and_choose_word)
            {
                IsolationQuestion = data.listen_sen_and_choose_word;
                AudioId = IsolationQuestion.tts_id;
            }
        }

        public override List<PB_BlankRange> GetBlankRanges()
        {
            return IsolationQuestion.blank_range.ToList();
        }

        public override string GetStem()
        {
            return IsolationQuestion.question;
        }

        protected override string GetCorrectAnswer()
        {
            return IsolationQuestion.correct_index.ToString();
        }

        protected override string GetAnswerContent()
        {
            return IsolationQuestion.choices[IsolationQuestion.correct_index].text;
        }

        public override int[] GetAnswerIndexes()
        {
            return new[] { IsolationQuestion.correct_index };
        }

        public override string[] GetAnswerOptions()
        {
            return IsolationQuestion.choices.Select(c => c.text).ToArray();
        }
        
        public override string[] GetAnswerOptionImage()
        {
            return IsolationQuestion.choices.Select(c => c.image_url).ToArray();
        }

        public override int GetOptionsCount()
        {
            return IsolationQuestion.choices.Count;
        }

        public override string GetQuestionTypeLanguageKey()
        {
            return "ui_fragment_question_type_listenIsolation";
        }

        public override string GetQuestionComponentUrl()
        {
            return SimpleTextQuestion.url;
        }

        public override string GetAnswerComponentUrl()
        {
            return AudioSelectAnswer.url;
        }

        protected override object GetQuestionObject()
        {
            return IsolationQuestion;
        }

        public override long[] GetAnswerOptionTts()
        {
            return IsolationQuestion.choices.Select(c => c.tts_id).ToArray();
        }

        protected override string GetMeaningContent()
        {
            return IsolationQuestion.translation;
        }
    }
}
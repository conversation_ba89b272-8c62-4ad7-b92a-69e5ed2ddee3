﻿using Msg;
using System;
using UnityEngine;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using System.IO;
using System.Collections.Concurrent;
using System.Text;
using System.Threading;
using UnityEngine.Networking;
using Msg.tts;
using Grpc.Core;
using ScriptsHot.Game.Modules.MultiMedia;

public class TTSManager
{
    private static TTSManager _instance = null;
    private long CurrPlayingId;
    private long _currPlayingId
    {
        get { return CurrPlayingId;}
        set
        {
            //VFDebug.Log("CurrPlaying TTSId:: " + value);
            CurrPlayingId = value;
        }
    }


    public enum AudioChannel
    {
        Avatar,
        AvatarStream,
        Player,
        Flow,
        TTS,
    }

    private ConcurrentQueue<TTSVO> _cache = new ConcurrentQueue<TTSVO>();

    public static TTSManager instance
    {
        get
        {
            if (_instance == null)
                _instance = new TTSManager();
            return _instance;
        }
    }

    private readonly float[] audioData = new float[64];
    public event Action<float[]> onGetAudioData;  // 发送null代表没有在播放
    // private float[] AudioData => audioData;

    /// <summary>
    /// 这个方法报废了 由于之前 谁加了 _currPlayingId 公共变量，导致会冲突，以后需要预加载，找婧涵 她弄了一个独立的预加载音频
    /// </summary>
    /// <param name="recordId"></param>
    /// <param name="channel"></param>
    /// <param name="preloadCallback"></param>
    /// <param name="stopCallback"></param>
    /// <param name="playStartCallBack"></param>
    public void PreloadTTS(long recordId, AudioChannel channel, Action preloadCallback = null, Action stopCallback = null, Action playStartCallBack = null)
    {
        VFDebug.Log("PreloadTTS " + recordId);
        if (UrlExistedForRecordId(recordId))
        {
            if (preloadCallback != null)
            {
                preloadCallback();
            }
            else
            {
                VFDebug.LogError($"PreloadTTS {recordId} err");
            }

        }
        else
        {

            TTSVO cachedObj = GetItemWithRecordId(recordId);
            if (cachedObj != null)
            {
               
                if (preloadCallback != null)
                    preloadCallback();
                return;
            }

            var ttsVO = new TTSVO
            {
                recordId = recordId
            };
            ttsVO.isAvatar = false;
            ttsVO.isPreload = true;
            ttsVO.preloadCallback = preloadCallback;
            ttsVO.playCallback = stopCallback;
            ttsVO.playStartCallback = playStartCallBack;


            _cache.Enqueue(ttsVO);
            
            this.GetTTS(ttsVO, recordId);
        }
    }

    private float maxCompleteTimeInAdv = 0f;
    public void AllowPlayTTSCallbackInAdvance(float maxTimeDeltaInAdv) {
        maxCompleteTimeInAdv = maxTimeDeltaInAdv;
    }

    //TODO 当前未处理分片及文件缓存
    public async void PlayTTS(long recordId, Action callback = null, bool replacePrePlaying = true, AudioChannel channel = AudioChannel.TTS, float rate = 1f, AudioSource audioSource = null, Action playStartCallBack = null)
    {
        //VFDebug.Log("TTSManger PlayTTS  recordId  " + recordId + " channel " + channel + " replacePrePlaying " + replacePrePlaying);
        if (replacePrePlaying && this._currPlayingId != 0)
        {
            this.StopTTSInternal(this._currPlayingId);
           
            this._currPlayingId = 0;
        }
        //判断缓存
        TTSVO cachedObj = GetItemWithRecordId(recordId);
        if (cachedObj != null)
        {
            if (channel == AudioChannel.Avatar || channel == AudioChannel.AvatarStream)
                cachedObj.isAvatar = true;
            cachedObj.playCallback = callback;
            cachedObj.playStartCallback = playStartCallBack;
            cachedObj.channel = channel;
            cachedObj.rate = rate;
            if (cachedObj.isReadyForPlaying)
            {
                var recordStatus = UIManager.instance.GetUI<RecordUI>(UIConsts.RecordUI).FetchRecordStatus();  //  怎么可以这么调用(´･д･)?
                if (recordStatus == RecordUIStatus.NewSend)
                {
                    if (cachedObj.playCallback != null)
                        cachedObj.playCallback();
                    VFDebug.Log("已经开始录音，不播放  cachedObj.isReadyForPlaying 音频");
                    return;
                }
                
                cachedObj.playTime = TimeExt.time;
                this._currPlayingId = recordId;
                cachedObj.isPlaying = true;
                
                if (cachedObj.isAvatar)
                {
                    GSoundManager.instance.PlayAvatarTTS(cachedObj.audioClip, cachedObj.rate);
                }
                else if (channel == AudioChannel.Flow)
                {
                    GSoundManager.instance.PlayFlowTTS(cachedObj.audioClip, audioSource);
                    cachedObj.isFlow = true;
                }
                else
                    GSoundManager.instance.PlayTTS(cachedObj.audioClip, cachedObj.rate);
                if (cachedObj.playStartCallback != null)
                    cachedObj.playStartCallback();
            }

        }
        else if (UrlExistedForRecordId(recordId))
        {
            var recordStatus = UIManager.instance.GetUI<RecordUI>(UIConsts.RecordUI).FetchRecordStatus();
            if (recordStatus == RecordUIStatus.NewSend)
            {
                if (callback != null)
                    callback.Invoke();
                VFDebug.Log("已经开始录音，不播放  UrlExistedForRecordId 音频");
                return;
            }
            
            var ttsVO = new TTSVO
            {
                recordId = recordId
            };
            ttsVO.channel = channel;
            ttsVO.playCallback = callback;
            ttsVO.playStartCallback = playStartCallBack;
            _cache.Enqueue(ttsVO);

            //VFDebug.Log("TTSGet UrlExistedForRecordId(recordId) TRUE ");
            AudioClip audioClip;
            if (channel == AudioChannel.AvatarStream)
            {
                audioClip = await this.GetAudioFromDataWav(ttsVO.recordId);
            }
            else
            {
                audioClip = await this.GetAudioFromData(ttsVO.recordId);
            }

            ttsVO.isLocal = true;

            if (channel == AudioChannel.Avatar || channel == AudioChannel.AvatarStream)
                ttsVO.isAvatar = true;
            ttsVO.audioClip = audioClip;
            ttsVO.playTime = TimeExt.time;
            ttsVO.isPlaying = true;
            this._currPlayingId = recordId;

            ttsVO.rate = rate;
            //VFDebug.Log("PlayTTS3333 " + recordId + " ttsVO.recordId " + ttsVO.recordId);
            if (ttsVO.isAvatar)
            {
                GSoundManager.instance.PlayAvatarTTS(audioClip, ttsVO.rate);
            }
            else if (channel == AudioChannel.Flow)
            {
                GSoundManager.instance.PlayFlowTTS(audioClip, audioSource);
                ttsVO.isFlow = true;
            }
            else
                GSoundManager.instance.PlayTTS(audioClip, ttsVO.rate);
            if (ttsVO.playStartCallback != null)
                ttsVO.playStartCallback();
        }
        else
        {
            var ttsVO = new TTSVO
            {
                recordId = recordId
            };

            ttsVO.channel = channel;
            if (channel == AudioChannel.Avatar)
                ttsVO.isAvatar = true;

            if (channel == AudioChannel.AvatarStream)
            {
                ttsVO.isAvatar = true;
                ttsVO.isLocal = false;
            }

            ttsVO.playCallback = callback;
            ttsVO.playStartCallback = playStartCallBack;
            ttsVO.rate = rate;
            if (channel == AudioChannel.Flow)
            {
                ttsVO.audioSource = audioSource;
                ttsVO.isFlow = true;
            }

            //这里不要立即赋予 this._currPlayingId 和入栈动作，因为GetTTS是异步的，不是当前这个瞬间就开始play有缓冲过程
            _cache.Enqueue(ttsVO);
            this.GetTTS(ttsVO, recordId);
        }


    }

    private AsyncServerStreamingCall<global::Msg.tts.SC_GetTTSAudioAck> _clientCall;
    private async void GetTTS(TTSVO ttsVO, long recordId)
    {
        // SteamPlayAudioManager.Instance.Begin();
        await UniTask.DelayFrame(33);//raybit fix 20250107 避免 stream结束的处理和 拉取下一个stream tts的处理同时被触发

        //流播放
        if (ttsVO.channel == AudioChannel.AvatarStream)
        {
            AudioStreamPlayer sP = GSoundManager.instance.AudioStreamPlayer;
            if (sP)
            {
                var recordStatus = UIManager.instance.GetUI<RecordUI>(UIConsts.RecordUI).FetchRecordStatus();
                if (recordStatus != RecordUIStatus.NewSend)
                {
                    sP.Begin(recordId, TTSManager.instance.StreamAudioPlayOver);
                }
                else
                {
                    if (ttsVO.playCallback != null)
                        ttsVO.playCallback();
                    VFDebug.Log("已经开始录音，不播放音频流");
                }
            }
        }
        //Debug.Log("client.GetTTSAudio reading tts");
        CommonUtils.Instance.TimeBegin("TTS");
        var reqMsg = new CS_GetTTSAudioReq { tts_record_id = recordId };
        var client = GRPCManager.instance.GetGrpcClient<TtsService.TtsServiceClient>((short)GameMSGID.CS_GetTTSAudioReq_ID);
        _clientCall = client.GetTTSAudio(reqMsg);

        // _cache.Enqueue(ttsVO);//raybit fix 20250107 在实际取tts前插队
        //_currPlayingId = recordId 是一个核心的检索器，要在playing触发 || playing一定不触发时再赋予，不要提前赋予
        try
        {
            await foreach (var result in _clientCall.ResponseStream.ReadAllAsync())
            {
                if (result.code != (int)GameErrID.Ack_Success)
                {
                    _currPlayingId = recordId;
                    VFDebug.LogError($"TTS: get rpc data error. RecordId: {recordId}, error:{result.code}");
                    InvokeCallback(ttsVO);
                }
                else
                {
                    //流播放
                    if (ttsVO.channel == AudioChannel.AvatarStream)
                    {
                        var streamBytes = result.data.audio_clip.audio_clip.ToByteArray();
                        // SteamPlayAudioManager.Instance.OnAudioDataReceived(streamBytes,ttsVO.playCallback);
                        AudioStreamPlayer sP = GSoundManager.instance.AudioStreamPlayer;
                        if (sP)
                        {
                            _currPlayingId = recordId;
                            ttsVO.isPlaying = true;
                            sP.AddAudioData(streamBytes, result.data.audio_clip.is_last_clip);
                            if (sP.IsBufferReady())
                            {
                                sP.StartAudio();
                                //VFDebug.Log("流逝音频 tss play");
                            }
                        }
                    }
                    //gRpc的stream是保证有序的，先不做特殊处理
                    this.DealWithRpcData(ttsVO, result.data);
                    if (result.data.is_last_audio && result.data.audio_clip.is_last_clip)
                    {
                        bool success = SaveToLocal(ttsVO);
                        ttsVO.audioClipData = null;
                        if (success)
                        {
                            _currPlayingId = recordId;
                            ttsVO.isReadyForPlaying = true;

                            if (ttsVO.channel == AudioChannel.AvatarStream)
                            {
                                ttsVO.isReadyForPlaying = false;
                                if (ttsVO.playStartCallback != null)
                                    ttsVO.playStartCallback();
                                //VFDebug.Log("流逝音频获取完毕");

                            }

                            //preload类型的tts资源达到这里后 会在update逻辑中被检测到并播放

                            CommonUtils.Instance.TimeEnd();
                        }
                    }
                    else
                    {
                        //VFDebug.Log("stream tts next read");
                    }
                }
            }
        }
        catch (Exception e)
        {
            VFDebug.LogError("TTS rpc error." + e.Message + " TTS recordId " + recordId);
            InvokeCallback(ttsVO);
        }
    }

    private void DealWithRpcData(TTSVO ttsVO, PB_GetTTSAudioResp audioData)
    {
        var data = audioData.audio_clip.audio_clip.ToByteArray();
        if (data == null) return;
        int nowAudioLength = ttsVO.audioClipData.Length;
        Array.Resize(ref ttsVO.audioClipData, nowAudioLength + data.Length);
        Array.Copy(data, 0, ttsVO.audioClipData, nowAudioLength, data.Length);
    }

    public void TestSaveBytesAsAudio(byte[] data)
    {
        if (data == null || data.Length == 0)
        {
            Debug.LogError("Failed to TestSaveBytesAsAudio data 0");
        }
        else
        {
            try
            {
                string url = UrlForSaveRecordId(552299);
                if (true)
                {
                    SaveWav(url, data, 16000, 1);
                }
                else
                {
                    File.WriteAllBytes(url, data);
                }

            }
            catch (Exception ex)
            {
                VFDebug.LogError("TestSaveBytesAsAudio 写文件失败: " + ex.Message);
            }
        }
    }

    private bool SaveToLocal(TTSVO ttsVO)
    {
        bool success = true;
        var data = ttsVO.audioClipData;
        //VFDebug.Log("TTSManager TTS data length " + data.Length + " isPreload " + ttsVO.isPreload + " id " + ttsVO.recordId);
        if (data.Length == 0)
        {
            if (ttsVO.channel != AudioChannel.AvatarStream)
                InvokeCallback(ttsVO);
            return false;
        }
        try
        {
            string url = UrlForSaveRecordId(ttsVO.recordId);
            if (ttsVO.channel == AudioChannel.AvatarStream)
            {
                SaveWav(url, data, 24000, 1);
            }
            else
            {
                File.WriteAllBytes(url, data);
            }

        }
        catch (Exception ex)
        {
            VFDebug.Log("TTSManager 写文件失败: " + ex.Message);
            success = false;
        }
        return success;
    }

    public static void SaveWav(string filePath, byte[] rawAudioData, int sampleRate, int channels)
    {
        using (FileStream fs = new FileStream(filePath, FileMode.Create))
        {
            using (BinaryWriter writer = new BinaryWriter(fs))
            {
                // 写入 RIFF 头部
                writer.Write(Encoding.ASCII.GetBytes("RIFF"));
                writer.Write(36 + rawAudioData.Length); // 文件大小
                writer.Write(Encoding.ASCII.GetBytes("WAVE"));

                // 写入 fmt 子块
                writer.Write(Encoding.ASCII.GetBytes("fmt "));
                writer.Write(16); // fmt 块大小
                writer.Write((short)1); // 音频格式（1 = PCM）
                writer.Write((short)channels); // 声道数
                writer.Write(sampleRate); // 采样率
                writer.Write(sampleRate * channels * 2); // 每秒字节数
                writer.Write((short)(channels * 2)); // 每帧字节数
                writer.Write((short)16); // 位深度

                // 写入 data 子块
                writer.Write(Encoding.ASCII.GetBytes("data"));
                writer.Write(rawAudioData.Length); // 数据大小
                writer.Write(rawAudioData); // 音频数据
            }
        }
    }


    private void InvokeCallback(TTSVO ttsVO)
    {
        if (ttsVO.playCallback != null)
        {
            var callback = ttsVO.playCallback;
            ttsVO.playCallback = null;
            callback();
        }
        RemoveItemWithRecordId(_currPlayingId);
        _currPlayingId = 0;
    }

    //TODO 先使用内存构建,最终都由文件处理
    private async Task<AudioClip> GetAudioFromData(long record)
    {
        //VFDebug.Log("TTSManager GetAudioFromData Mp3 " + UrlForLoadRecordId(record));
        UnityWebRequest req = UnityWebRequestMultimedia.GetAudioClip(UrlForLoadRecordId(record), AudioType.MPEG);
        await req.SendWebRequest();
        return DownloadHandlerAudioClip.GetContent(req);
    }

    private async Task<AudioClip> GetAudioFromDataWav(long record)
    {
        //VFDebug.Log("TTSManager GetAudioFromData Wav " + UrlForLoadRecordId(record));
        UnityWebRequest req = UnityWebRequestMultimedia.GetAudioClip(UrlForLoadRecordId(record), AudioType.WAV);
        await req.SendWebRequest();
        return DownloadHandlerAudioClip.GetContent(req);
    }

    public async void StopTTS(long recordId = 0,bool callBack = true)
    {
        //VFDebug.Log("TTSManger StopTTS  recordId " + recordId);
        if (recordId == 0)
        {
            TTSVO tTSVO = this.StopTTSInternal(this._currPlayingId,callBack);
            return;
        }
        TTSVO ttsVO = this.StopTTSInternal(recordId,callBack);

        //TODO     
        // Debug.LogError("GRPC 销毁！！！！！！！！！！！！" + _clientCall);
        // _clientCall 停不掉  使用 CancellationTokenSource  也停不掉。 
        // if (_clientCall != null)
        // {
        //     _clientCall.Dispose();
        //     _clientCall = null;
        // }

    }


    public void StreamAudioPlayOver(long recordId)
    {
        TTSVO ttsVO = GetItemWithRecordId(_currPlayingId);
        if (ttsVO != null && ttsVO.isPlaying)
        {
            ttsVO.isLocal = true;
            var playCallback = ttsVO.playCallback;
            ttsVO.playCallback = null;
            RemoveItemWithRecordId(_currPlayingId);
            _currPlayingId = 0;
            if (playCallback != null)
            {
                playCallback();
            }
        }
        else
        {
            Debug.Log("currPlaying recID:" + _currPlayingId + ttsVO == null ? " ttsVO:null" : " ttsVo notNull but it is not playing");
        }
    }

    private TTSVO StopTTSInternal(long recordId,bool callBack = true)
    {

        TTSVO ttsVO = GetItemWithRecordId(recordId);
        if (ttsVO == null)
        {
            GSoundManager.instance.StopAvatarTTS();
            GSoundManager.instance.StopTTS();
            return null;
        }

        var tmpCallback = ttsVO.playCallback;

        if (ttsVO.isPlaying)
        {
            if (ttsVO.isAvatar)
                GSoundManager.instance.StopAvatarTTS();
            else
                GSoundManager.instance.StopTTS();
            RemoveItemWithRecordId(recordId);
            _currPlayingId = 0;
        }
        else
        {
            RemoveItemWithRecordId(recordId);
        }

        if (tmpCallback != null && callBack)
        {
            tmpCallback();
        }
        return ttsVO;
    }

    public async void Update(int interval)
    {
        // SteamPlayAudioManager.Instance.Update(interval);
        TTSVO ttsVO = GetItemWithRecordId(_currPlayingId);
        if (ttsVO != null)
        {
            if (ttsVO.isPlaying)
            {
                if (ttsVO.audioClip == null || (ttsVO.channel == AudioChannel.AvatarStream && !ttsVO.isLocal))
                {
                    return;
                }

                int audioLength = (int)(ttsVO.audioClip.length * 1000);
                //使用TimeExt.time 意味着时间线会受timescale的影响
                //为了统一性 GSoundManager里面PlayTTS PlayAvatarTTS PlayFlowTTS 播放语音时也需要变快，但由于复杂度和时间的问题 暂不处理
                float currTime = TimeExt.time;


                if ((int)((currTime - ttsVO.playTime + maxCompleteTimeInAdv) * 1000) >= audioLength)
                {
                    maxCompleteTimeInAdv = 0;

                    var playCallback = ttsVO.playCallback;
                    ttsVO.playCallback = null;
                    RemoveItemWithRecordId(_currPlayingId);
                    VFDebug.Log("PreloadTTS " + _currPlayingId + " ttsVO " + ttsVO.recordId +" TTSaudioLen(ms)="+ audioLength);
                    _currPlayingId = 0;

                    if (playCallback != null)
                    {
                        playCallback();
                    }

                }

                // 获得频谱
                if (onGetAudioData != null)
                {
                    AudioListener.GetOutputData(audioData, 0);
                    onGetAudioData(audioData);
                }
            }
            else if (ttsVO.isReadyForPlaying && ttsVO.isPreload)
            {

                VFDebug.Log("_currPlayingId ::" + _currPlayingId + "     ttsVO.channel::" + ttsVO.channel);
                var preloadCallback = ttsVO.preloadCallback;

                AudioClip audioClip;
                if (ttsVO.channel == AudioChannel.AvatarStream)
                {
                    audioClip = await this.GetAudioFromDataWav(ttsVO.recordId);
                }
                else
                {
                    audioClip = await this.GetAudioFromData(ttsVO.recordId);
                }

                ttsVO.audioClip = audioClip;
                ttsVO.preloadCallback = null;
                ttsVO.isPreload = false;
                this._currPlayingId = 0;
                VFDebug.Log("PreloadTTS isReadyForPlaying " + _currPlayingId);
                if (preloadCallback != null)
                    preloadCallback();
                
                ClearSpectrum();
            }
            else if (ttsVO.isReadyForPlaying && ttsVO.channel != AudioChannel.AvatarStream)
            {
                ttsVO.isPlaying = true;
                ttsVO.isReadyForPlaying = false;
                AudioClip audioClip;
                audioClip = await this.GetAudioFromData(ttsVO.recordId);
                ttsVO.audioClip = audioClip;
                ttsVO.playTime = TimeExt.time;
                VFDebug.Log("PreloadTTS " + _currPlayingId + " ttsVO.recordId " + ttsVO.recordId);
                if (ttsVO.isAvatar)
                    GSoundManager.instance.PlayAvatarTTS(audioClip, ttsVO.rate);
                else if (ttsVO.isFlow)
                    GSoundManager.instance.PlayFlowTTS(audioClip, ttsVO.audioSource);
                else
                    GSoundManager.instance.PlayTTS(audioClip, ttsVO.rate);
                if (ttsVO.playStartCallback != null)
                    ttsVO.playStartCallback();
                ClearSpectrum();
            }

        }else
        {
            ClearSpectrum();
        }   
    }

    private void ClearSpectrum()
    {
        if (onGetAudioData != null)
        {
            onGetAudioData(null);
        }
    }


    public bool IsPlayingTTS()
    {
        return GSoundManager.instance.IsPlaying("TTS");
    }

    private string UrlForSaveRecordId(long recordId)
    {
        // string ret = Application.persistentDataPath + "/" + recordId;
        // return ret;
        return TTSPreloader.Instance.UrlForSaveRecordId(recordId);
    }

    private string UrlForLoadRecordId(long recordId)
    {
//         string ret = Application.persistentDataPath + "/" + recordId;
//         Debug.Log("before ret url: " + ret);
// #if UNITY_IOS || UNITY_STANDALONE_OSX || UNITY_ANDROID 
//         ret = "file://" + ret;
//         // VFDebug.Log("TTSManager url: " + ret);
// #endif         
//         return ret;
        return TTSPreloader.Instance.UrlForLoadRecordId(recordId);
    }


    private bool UrlExistedForRecordId(long recordId)
    {
        // string url = UrlForSaveRecordId(recordId);
        // return File.Exists(url);
        return TTSPreloader.Instance.UrlExistedForRecordId(recordId);
    }

    private TTSVO GetItemWithRecordId(long recordId)
    {
        if (recordId == 0) return null;
        TTSVO ret = null;
        int cnt = _cache.Count;
        for (int i = 0; i < cnt; i++)
        {
            TTSVO tmpObj = null;
            bool notEmpty = _cache.TryPeek(out tmpObj);
            if (notEmpty)
            {
                if (tmpObj != null)
                {
                    if (tmpObj.recordId == recordId)
                    {
                        ret = tmpObj;
                        break;
                    }
                    else
                    {
                        _cache.TryDequeue(out tmpObj);
                        _cache.Enqueue(tmpObj);
                    }
                }
            }
        }
        return ret;
    }

    private void RemoveItemWithRecordId(long recordId)
    {
        //Debug.Log("rm recordID:" + recordId);
        int cnt = _cache.Count;
        for (int i = 0; i < cnt; i++)
        {
            TTSVO tmpObj = null;
            bool notEmpty = _cache.TryDequeue(out tmpObj);
            if (notEmpty)
            {
                if (tmpObj != null)
                {
                    if (tmpObj.recordId == recordId)
                    {
                        break;
                    }
                    else
                    {
                        _cache.Enqueue(tmpObj);
                    }
                }
            }
        }
    }

    internal class TTSVO
    {
        public long recordId;
        public AudioClip audioClip;
        public AudioSource audioSource;
        public bool isReadyForPlaying = false;
        public bool isPlaying = false;
        public Action playCallback = null;
        public Action playStartCallback = null;
        public Action preloadCallback = null;
        public byte[] audioClipData = new byte[0];
        public float playTime = 0;
        public bool isAvatar;
        public bool isFlow;
        public bool isPreload = false;
        public float rate = 1f;//语音播放速度
        public AudioChannel channel = AudioChannel.Avatar;
        public bool isLocal = true; //是否读取本地缓存数据
    }
}
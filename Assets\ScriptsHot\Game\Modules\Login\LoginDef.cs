﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

public class LoginChannel
{
    public const string apple = "apple";
    public const string google = "google";
    public const string facebook = "facebook";
    public const string phone = "phone";
    public const string email = "email";
    public const string debug = "debug";
    public const string not_debug = "not_debug";
}

public class LoginReq
{
    public string type = ""; //apple, google, facebook, phone, email, debug
    public string identifier = "";
    public string credential = "";
    public string name = "";
    public string email = "";
    public string phone = "";
    public string password = "";
    public string version = "";
    public string device_id = "";
    public long user_id ;
    public string system_lang = "";   
    public string appsflyer_id = "";
    public string firebase_id = "";
    public string country_code = "";    
}

public class ZoneInfo
{
    public long user_id ;
    public string time_zone ;
    public string country;
}

public class LogoutRequest
{
    public long user_id ;
    public string engagelab_device_id;
}

public class UserBaseInfo
{
    public long user_id;
    public string learn_purpose;
    public string lang_level;
    public string favourite_topic;
    public string email;
}

public class LoginVerifytokenReq
{
    public long user_id;
    public string access_token;
}

public class LoginResp
{
    public int error_code = 0;
    public long user_id;
    public string access_token;
    public string cur_first_lang;
    public string cur_foreign_lang;
    public string talkit_gateway;
    public string game_gateway;
    public string task_node;
    public long timestamp;
    
    //old roleinfo
    public long player_id;
    public string player_name;
    public string style;
    public string roleUniqueName;
    public string h5_config;
}

public class LoginConst
{
#if PHONE4CN
    public static string privacyUrl = "https://mudiyongxian.com/tp-fe/privacy";
    public static string usertermsUrl = "https://mudiyongxian.com/tp-fe/terms";
#else
    public static string privacyUrl = "https://talkit.ai/privatepolicyen";
    public static string usertermsUrl = "https://talkit.ai/termsofserviceen";
#endif
    
    
    


    public const string Key_Apple_Token = "VF_talkit_Apple_token";
    public const string Key_Apple_Id = "VF_talkit_Apple_id";
    public const string Key_User_Token = "VF_talkit_user_token";
    public const string Key_User_Token_Timestamp = "VF_talkit_user_token_timestamp";
    public const string Key_User_Email = "VF_talkit_user_email";
    public const string Key_User_Email_Password = "VF_talkit_user_email_password";
    public const string Key_User_Id = "VF_talkit_user_id";
    public const string Key_LastLogin_State = "VF_talkit_user_lastStete";

    public const string Key_Debug_DeviceID = "talkit_debug_device";
    
    // public static string LoginGetStatePath = "/account/gettaskrecord";
    public static string LoginSetStatePath = "/account/settaskrecord";
    
    public static string ClearAccountPath = "/account/clearaccount";
    public static string SendVerificationCode = "/account/sendverificationcode";
    public static string EmailRegister = "/account/email_register";    
    public static string ResetPassword = "/account/reset_password";    
    public static string FrameRate = "talkit_Frame_rate";

    public static string GetLoginError(int code)
    {
        switch(code)
        {
            case NetErrorCode.ERROR_UNKNOWN:
                return I18N.inst.MoStr("ui_error_10000");
            case NetErrorCode.ERROR_PASSWORD_WRONG:
                return I18N.inst.MoStr("ui_error_10089");
            case NetErrorCode.ERROR_EMAIL_NOT_REGISTERED:
                return I18N.inst.MoStr("ui_error_10090");
            case NetErrorCode.ERROR_EMAIL_HAD_REGISTERED:
                return I18N.inst.MoStr("ui_error_10091");  
            case NetErrorCode.ERROR_VERIFICATION_CODE_WRONG:
                return I18N.inst.MoStr("ui_login31");   
            case NetErrorCode.ERROR_VERIFICATION_CODE_EXPIRED:
                return I18N.inst.MoStr("ui_error_10003");  
            case 10026:
                return I18N.inst.MoStr("login_user_create_fail");
            case 10088:
                return I18N.inst.MoStr(GetErrorKey());
            default:
                return I18N.inst.MoStr("ui_error_10000");
        }
        return string.Empty;
    }
    
    public static string GetErrorKey()
    {
#if UNITY_ANDROID
        return "ui_login_tips1_android";
#endif
        return "ui_login_tips1";
    }
}





public enum TaskRecord
{
    None,        // 没传值的状态，服务器没有这个状态
    CreateRole,  // 服务器第一个状态是这个 
    Dialogue,    // 对话阶段
    ShowPay,     // 支付阶段
    ShowDiamond, // 钻石弹窗之前
    Finish
}

public class LoginSetState
{
    public long id;
    public string task_node;
}

public class LoginUpdateStateReq
{
    public long id;
    public string task_node;
    public int error_code = 0 ;
}

public class SetUserLangReq
{
    public long user_id;
    public string first_lang;
    public string foreign_lang;
}

public class VerifyVersionReq
{
    public string type;
    public string version;
}

public class VerifyVersionResp
{
    public string need_update;
    public string server;
    public int error_code;
}

public class LoginAuthorizationReq
{
    public string auth_id;
}

public class DeleteAccountReq
{
    public long user_id;
}

public class SendVerificationCodeReq
{
    public string phone_number;
    public string email;
    public string system_lang;
    public string source_type;
}

public class EmailRegisterReq
{
    public string email;
    public string password;
    public string verification_code;
}

public class EmailRegisterResp
{
    public int code;
}

public class SendVerificationCodeResp
{
    public SendVerificationCodeResp2 data;
    public int code;
}
public class SendVerificationCodeResp2
{
    public string verification_code;
}
﻿using System.Collections.Generic;
using FairyGUI;
using Modules.DataDot;
using Msg.economic;
using UIBind.Shop;
using Unity.Mathematics;

namespace ScriptsHot.Game.Modules.Shop
{
    public class OnBoardPaywallUI : BaseUI<OnBoardPaywallPanel>
    {
        public OnBoardPaywallUI(string name) : base(name)
        {
        }
        
        public override string uiLayer => UILayerConsts.Top;

        private LoginOnBoardingModel BoardingModel => GetModel<LoginOnBoardingModel>(ModelConsts.Login);
        private MainModel MainModel => GetModel<MainModel>(ModelConsts.Main);
        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            ui.CompContent.tfTitle.SetKey("ui_paywall_onboard_Title");
            ui.CompContent.tfLessons.SetKey("ui_paywall_onboard_lessons");
            ui.CompContent.tfConversations.SetKey("ui_paywall_onboard_conversations");
            ui.tfGo.SetKey("ui_paywall_onboard_try");
            ui.tfSubscriptions.SetKey("ui_plan_promotion_desc6");

            ui.CompContent.list.itemRenderer = OnRendererOnBoard;
            AddUIEvent(ui.btnBack.onClick, OnClickClose);
            AddUIEvent(ui.btnGo.onClick, OnClickNext);
        }
        
        protected override void OnShow()
        {
            base.OnShow();

            DataDotMgr.Collect(new DotAppearMonetizationOnboardingCustomizePage());
            
            _cacheItem.Clear();
            
            BoardingModel.DicOnBoardSelect.TryGetValue(LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal, out int goalVal);
            if (goalVal == 0) return;
            int index = GetSelectedIndexes(BoardingModel.CfgOnBoardPrimaryGoal.Count, goalVal)[0];
            ui.CompContent.tfDesc.SetKey($"ui_paywall_onboard_type_{index + 1}");
            ui.CompContent.list.numItems = _titleLanguageList.Count;
            ui.CompContent.list.ResizeToFit();

            PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.onboarding_customize;
        }
        
        protected override bool isFullScreen => true;

        private readonly List<string> _titleLanguageList = new()
        {
            "ui_paywall_onboard_goal", "ui_paywall_onboard_priorities", "ui_paywall_onboard_duration",
            "ui_paywall_onboard_topics"
        };
        
        private readonly List<string> _ldrUrlList = new()
        {
            "ui://Shop/icon_Goal_white", "ui://Shop/icon_Priorities_white", "ui://Shop/icon_Duration_white",
            "ui://Shop/icon_topics_white"
        };

        private readonly List<LoginOnBoardingModel.OnBoardInfoEnum> _enumsList = new()
        {
            LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal,
            LoginOnBoardingModel.OnBoardInfoEnum.UseEnglish,
            LoginOnBoardingModel.OnBoardInfoEnum.StudyTime,
            LoginOnBoardingModel.OnBoardInfoEnum.Hobby,
        };

        private Dictionary<int, CompOnboardItem> _cacheItem = new();
        
        private void OnRendererOnBoard(int index, GObject obj)
        {
            if (!_cacheItem.ContainsKey(index))
            {
                CompOnboardItem item = new CompOnboardItem();
                item.Construct(obj as GComponent);
                item.list.itemRenderer = (tIndex, tObj) =>
                {
                    CompSelectItem tItem = new CompSelectItem();
                    tItem.Construct(tObj as GComponent);
                    BoardingModel.DicOnBoardSelect.TryGetValue(_enumsList[index], out int value);
                    switch (_enumsList[index])
                    {
                        case LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal:
                            int sIndex0 = GetSelectedIndexes(BoardingModel.CfgOnBoardPrimaryGoal.Count, value)[tIndex];
                            tItem.ldr.icon = BoardingModel.CfgOnBoardPrimaryGoal[sIndex0].icon;
                            tItem.tfTitle.SetKey(BoardingModel.CfgOnBoardPrimaryGoal[sIndex0].titleKey);
                            break;
                        case LoginOnBoardingModel.OnBoardInfoEnum.UseEnglish:
                            BoardingModel.DicOnBoardSelect.TryGetValue(LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal,
                                out int goalVal);
                            int goalIndex = GetSelectedIndexes(BoardingModel.CfgOnBoardPrimaryGoal.Count, goalVal)[0];
                            int goalId = BoardingModel.CfgOnBoardPrimaryGoal[goalIndex].ID;
                            int sIndex1 = GetSelectedIndexes(BoardingModel.DicOnBoardUseEnglish[goalId].Count, value)[tIndex];
                            tItem.ldr.icon = BoardingModel.DicOnBoardUseEnglish[goalId][sIndex1].icon;
                            tItem.tfTitle.SetKey(BoardingModel.DicOnBoardUseEnglish[goalId][sIndex1].habitKey);
                            break;
                        case LoginOnBoardingModel.OnBoardInfoEnum.StudyTime:
                            int sIndex2 = GetSelectedIndexes(BoardingModel.CfgOnBoardTime.Count, value)[tIndex];
                            tItem.ldr.icon = BoardingModel.CfgOnBoardTime[sIndex2].icon;
                            tItem.tfTitle.SetKey(BoardingModel.CfgOnBoardTime[sIndex2].titleKey);
                            break;
                        case LoginOnBoardingModel.OnBoardInfoEnum.Hobby:
                            int sIndex3 = GetSelectedIndexes(BoardingModel.CfgOnBoardHobbies.Count, value)[tIndex];
                            tItem.ldr.icon = BoardingModel.CfgOnBoardHobbies[sIndex3].icon;
                            tItem.tfTitle.SetKey(BoardingModel.CfgOnBoardHobbies[sIndex3].titleKey);
                            break;
                    }
                };
                _cacheItem[index] = item;   
            }
            _cacheItem[index].tfTitle.SetKey(_titleLanguageList[index]);
            _cacheItem[index].ldr.icon = _ldrUrlList[index];
            BoardingModel.DicOnBoardSelect.TryGetValue(_enumsList[index], out int value);
            switch (_enumsList[index])
            {
                case LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal:
                    _cacheItem[index].list.numItems =
                        GetSelectedIndexes(BoardingModel.CfgOnBoardPrimaryGoal.Count, value).Count;
                    _cacheItem[index].list.ResizeToFit();
                    break;
                case LoginOnBoardingModel.OnBoardInfoEnum.UseEnglish:
                    BoardingModel.DicOnBoardSelect.TryGetValue(LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal,
                        out int goalVal);
                    int goalIndex = GetSelectedIndexes(BoardingModel.CfgOnBoardPrimaryGoal.Count, goalVal)[0];
                    int goalId = BoardingModel.CfgOnBoardPrimaryGoal[goalIndex].ID;
                    _cacheItem[index].list.numItems = math.min(3,
                        GetSelectedIndexes(BoardingModel.DicOnBoardUseEnglish[goalId].Count, value).Count);
                    _cacheItem[index].list.ResizeToFit();
                    break;
                case LoginOnBoardingModel.OnBoardInfoEnum.StudyTime:
                    _cacheItem[index].list.numItems =
                        GetSelectedIndexes(BoardingModel.CfgOnBoardTime.Count, value).Count;
                    _cacheItem[index].list.ResizeToFit();
                    break;
                case LoginOnBoardingModel.OnBoardInfoEnum.Hobby:
                    _cacheItem[index].list.numItems = math.min(3,
                        GetSelectedIndexes(BoardingModel.CfgOnBoardHobbies.Count, value).Count);
                    _cacheItem[index].list.ResizeToFit();
                    break;
            }
        }

        private void OnClickClose()
        {
            GetController<ShopController>(ModelConsts.Shop).CheckSendOnBoardOverMsg();
            Hide();
        }

        private void OnClickNext()
        {
            DataDotMgr.Collect(new DotClickMonetizationOnboardingCustomizePageNextButton());
            GetUI<SpeakPlanPromotionStep3UI>(UIConsts.SpeakPlanPromotionStep3UI).Show(args);
            Hide();
        }

        /// <summary>
        /// 获取所有被选中的选项索引
        /// </summary>
        private List<int> GetSelectedIndexes(int maxOptionCount, int value)
        {
            var list = new List<int>();
            for (int i = 0; i < maxOptionCount; i++)
            {
                if ((value & (1 << i)) != 0)
                    list.Add(i);
            }
            return list;
        }
    }
}
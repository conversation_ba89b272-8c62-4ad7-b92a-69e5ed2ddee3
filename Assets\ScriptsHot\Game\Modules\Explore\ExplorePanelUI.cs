﻿using System;using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using FairyGUI;
using Modules.DataDot;
using Msg.economic;
using Msg.explore;
using Msg.incentive;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Explore.UI;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using ScriptsHot.Game.Modules.Shop;
using UIBind.Explore;
using UIBind.Explore.Item;
using UnityEngine;

public enum ExploreExperienceState
{
     Normal,
     Vip,
     TimeDown,
     WaitOver,
     Over
}

public class ExplorePanelUI: BaseUI<ExplorePanel>
{
    public override void OnBackBtnClick()
    {
    }

    public override string uiLayer => UILayerConsts.Top;
    protected override bool isFullScreen => true;

    private GList _list;
    
    private int currentIndex = 0;
    private int _currentDisplayIndex = -1;  // 记录当前显示的Item索引

    private ExploreController _controller;
    
    private SceneController _sceneController;
    private Level _gameScene;

    private GameObject _rtContainer;

    private Dictionary<int, ExploreItemUI> _items = new Dictionary<int, ExploreItemUI>();

    /// <summary>
    /// 向上滑动
    /// </summary>
    private bool _UpDrag = true;

    private ExploreCellBase _curEnterItem;

    private float _lastOnShowTime = -1f;
    private const float OnShowInterval = 0.5f; // 0.5秒

    public bool IsCreated = false;

    private int _secondLastTime = 0;
    /// <summary>
    /// 体验模式剩余时间
    /// </summary>
    private int _experienceLastTime = 0;

    /// <summary>
    /// 是否是会员
    /// </summary>
    private bool _isVip = false;
    
    /// <summary>
    /// 体验状态
    /// </summary>
    private ExploreExperienceState _experienceState = ExploreExperienceState.Normal;

    // 上一次Update的时间
    private float _lastUpdateTime = -1f;  
    private int _realIntervalMs = 0;

    private string _payNoticekey = "payNoticeOpen";
    public ExplorePanelUI(string name) : base(name)
    {
    }

    private void AddEvent()
    {
        Notifier.instance.RegisterNotification(NotifyConsts.RefreshExploreData,OnRefreshExploreList);
        Notifier.instance.RegisterNotification(NotifyConsts.RefresUINet,OnRefreshExploreNet);
        Notifier.instance.RegisterNotification(NotifyConsts.MainTabChange,OnMainTabChange);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreChatFristAvatarShow,OnShowFirstAvatarCell);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreBGMopen,OnOpenBGM);
        Notifier.instance.RegisterNotification(NotifyConsts.MainTabIndexBack,OnMainTabSetIndex);
        Notifier.instance.RegisterNotification(NotifyConsts.ShopInfoUpdate,OnShopInfoUpdate);
        
        
        //推荐下行 - 切换推荐实体
        MsgManager.instance.RegisterCallBack<SC_RecommendSwitchEntity>(this.OnRecommendSwitchEntity);
        
        MsgManager.instance.RegisterCallBack<SC_GetIncentiveDataForExploreAck>(this.OnGetIncentiveDataForExploreAck);

    }
    
    //这部分rm调用先不适用，只保留一次add
    private void RemoveEvent()
    {
        Notifier.instance.UnRegisterNotification(NotifyConsts.RefreshExploreData,OnRefreshExploreList);
        Notifier.instance.UnRegisterNotification(NotifyConsts.RefresUINet,OnRefreshExploreNet);
        Notifier.instance.UnRegisterNotification(NotifyConsts.MainTabChange,OnMainTabChange);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreChatFristAvatarShow,OnShowFirstAvatarCell);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreBGMopen,OnOpenBGM);
        Notifier.instance.UnRegisterNotification(NotifyConsts.MainTabIndexBack,OnMainTabSetIndex);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ShopInfoUpdate,OnShopInfoUpdate);
        
        //推荐下行 - 切换推荐实体
        MsgManager.instance.UnRegisterCallBack<SC_RecommendSwitchEntity>(this.OnRecommendSwitchEntity);
        
        MsgManager.instance.UnRegisterCallBack<SC_GetIncentiveDataForExploreAck>(this.OnGetIncentiveDataForExploreAck);
    }
    
    /// <summary>
    /// 支付完成 商店状态变化
    /// </summary>
    /// <param name="s"></param>
    /// <param name="body"></param>
    private void OnShopInfoUpdate(string s, object body)
    {
        Debug.Log("商店状态变化,支付成功");
        AskLastTime();
    }

    private void OnGetIncentiveDataForExploreAck(SC_GetIncentiveDataForExploreAck msg)
    {
        VFDebug.Log("收到服务器时间回复: second:" + msg.data.economic_info.remaining_seconds +  "     member_type:" + msg.data.economic_info.member_info.member_type + "  begin:" +msg.data.economic_info.is_consume_time);
        _experienceLastTime = msg.data.economic_info.remaining_seconds;
        _controller.IsConsumeTime = msg.data.economic_info.is_consume_time;
        _isVip = msg.data.economic_info.member_info.is_member;
        
        _controller.CanExperienceTimeDown = _controller.IsConsumeTime;

        ShowTime();
        if (_isVip)
        {
            this.ui.ctrlPay.selectedPage = "vip"; 
            _experienceState = ExploreExperienceState.Vip;
            
            _controller.RecordLock = false;
            
            HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
            if (hCon.IsExploreTab() && this.GetUI<RecordUI>(UIConsts.RecordUI).isShow)
            {
                _controller.OnCancelRecored();
            }
        }
        else
        {
            if (_experienceLastTime > 0)
            {
                _controller.SaveFlag(_payNoticekey,false);
                _experienceState = ExploreExperienceState.TimeDown;
                this.ui.ctrlPay.selectedPage = "timeDown"; 
            }
            else
            {
                _experienceState = ExploreExperienceState.Over;
                TimeOver();
                
                ExperienceTimeOver();
            }
        }
        
        UpdateBySecond();
    }

    private void TimeOver()
    {
        this.ui.ctrlPay.selectedPage = "timeOver";
        this.ui.txtTime.textFormat.size = 28;
        this.ui.txtTime.SetKey("ui_profile_unlock");
    }
    private void OnMainTabSetIndex(string s, object body)
    {
        HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
        if (!hCon.IsExploreTab())
        {
            OutExploreTab();
        }
    }

    protected override void OnInit(GComponent uiCom)
    {
        _controller = ControllerManager.instance.GetController<ExploreController>(ModelConsts.Explore) as ExploreController;
        
        //惯性滚动最短时间（原 0.3f）
        ScrollPane.TWEEN_TIME_DEFAULT = 0.25f;
        //翻页动画时间（原 0.3f）
        ScrollPane.TWEEN_TIME_GO = 0.25f;
        //缓慢拖动超过一半 → 翻页
        UIConfig.defaultScrollPagingThreshold = 0.5f;
        _list = ui.listContainer;
        _list.SetVirtual();
        _list.itemRenderer = RenderItem;
        _list.scrollPane.snapToItem = true; 
        _list.scrollPane.pageMode = true;   
        //降低惯性，让短距离滑动也能翻页
        _list.scrollPane.decelerationRate = 0.5f;  
        //设置滚动步长，避免滑动太大翻多页
        _list.scrollPane.scrollStep = _list.scrollPane.viewHeight / 7; 
        _list.scrollPane.onScrollEnd.Add(OnScrollEnd);

        //这个延迟是 给ui.imgBG 留时间
        TimerManager.instance.RegisterNextFrame((a) =>
        {
            //按注释 有些分辨率顶部会有白边
            _list.y = ui.imgBG.y;//-Screen.safeArea.y - ExploreConst.ScreenOffsetHeight;
            _list.height = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;//高度不能等于imgBG的高 不知道为啥
        });
        _rtContainer = GameObject.Find("RenderTextureContainer");
        
        this._list.scrollPane.onScroll.Add(scrolled);
        
        ui.btnSetting.onClick.Set(OnBtnSettingClicked);
        ui.btnPay.onClick.Set(OnBtnPay);
        ui.btnVip.onClick.Set(OnBtnVip);

        ui.btnClose.visible = false;
        SetHandVisible(false);
        this.AddEvent();
        // ui.com.height = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;
    }

    private void OnBtnSettingClicked(EventContext context)
    {
        Notifier.instance.SendNotification(NotifyConsts.OpenUI,UIConsts.ExploreSettingPanelUI);
    }
    
    private void OnBtnPay()
    {
        if (_experienceLastTime > 0)
        {
            CLICK_EXPLORE_MAIN_PAGE_COUNTDOWN_ICON dt1 = new CLICK_EXPLORE_MAIN_PAGE_COUNTDOWN_ICON();
            dt1.countdown_time = _experienceLastTime;
            DataDotMgr.Collect(dt1);
        }
        else
        {
            CLICK_EXPLORE_MAIN_PAGE_UNLOCK_ICON dt1 = new CLICK_EXPLORE_MAIN_PAGE_UNLOCK_ICON();
            DataDotMgr.Collect(dt1);
        }

        Notifier.instance.SendNotification(NotifyConsts.OpenUI, new UIManager.UIParams() {viewName = UIConsts.SpeakPlanBottomSubscribeUI, param = 1});
    }
    
    private void OnBtnVip()
    {
        var dot = new DotClickMembershipIcon();
        // dot.source_page = "explore";
        // dot.member_status = DotMemberStatusEnum.member.ToString();
        DataDotMgr.Collect(dot);
        Notifier.instance.SendNotification(NotifyConsts.OpenUI, new UIManager.UIParams() {viewName = UIConsts.SpeakPlanPromotionStep1UI, param = SpeakShowType.Explore});
    }

    #region 思明处理相机
    
    void scrolled(EventContext context)
    {
        var y = _list.scrollPane.scrollingPosY;
        UpdateScrollDirection(_list);
    }

    //注意：现在一屏只有一个元素，后续入果一屏多个元素，需要基于元素个数甚至单个元素大小来维护此list。
    void UpdateItemToRender()
    {
        int startIndex = _list.GetFirstChildInView();
        _itemToRender.Clear();
        
        if (_isDragging)
        {
            if (_isScrollingDown)
            {
                _itemToRender.Add(startIndex);
                _itemToRender.Add(startIndex + 1);
                //show 2 cams
            }
            else
            {
                _itemToRender.Add(startIndex);
                _itemToRender.Add(startIndex + 1);
                //show 2 cams
            }
        }
        else
        {
            _itemToRender.Add(startIndex);
            //show 1 cam
        }
    }

    void Update3DRendering()
    {
        HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
        if (hCon.IsExploreTab())
        {
            UpdateItemToRender();
            foreach (var kvp  in _exploreItemDic)
            {
                if (kvp.Key != null)
                {
                    if (_itemToRender.Contains(kvp.Value))
                    {
                        kvp.Key.SetCamera(true);
                        kvp.Key.SetAvatar(true);
                    }
                    else
                    {
                        kvp.Key.SetCamera(false);
                        kvp.Key.SetAvatar(false);
                    }
                }
            }
        }
        else
        {
            foreach (var kvp  in _exploreItemDic)
            {
                if (kvp.Key != null)
                {
                    kvp.Key.SetCamera(false,999);
                    kvp.Key.SetAvatar(false,999);
                }
            }
        }
        
    }
    
    Dictionary<ExploreItemUI,int> _exploreItemDic = new();
    List<int> _itemToRender = new List<int>(); //要渲染的index
    float _lastScrollPosition = 0;
    bool _isScrollingDown = false;
    bool _isDragging = false;
    

    void UpdateScrollDirection(GList list)
    {
        float currentPos = list.scrollPane.scrollingPosY;
        _isScrollingDown = currentPos > _lastScrollPosition;
        _lastScrollPosition = currentPos;
    
    //    Debug.Log($"滚动方向: {(_isScrollingDown ? "向下" : "向上")}");
    }
    
    #endregion

    private async void RenderItem(int index, GObject obj)
    {
        // 如果是当前显示的Item，跳过刷新
        if (index == _currentDisplayIndex)
        {
            return;
        }
        GComponent modelItem = obj.asCom;
        List<ExploreItemData> datas = _controller.Model.AllData; 
         // Debug.LogError("Index:::" + index + "     entityId ::" + datas[index].Data.entityId + "  avatarId:::" + datas[index].Data.dialogTaskPreloadData.avatar.avatarId);

        if (modelItem.data == null)
        {
            ExploreItemUI uiItem = new ExploreItemUI(modelItem, _controller);
            uiItem.SetUI(this.ui.com);
            modelItem.data = uiItem;
        }
        datas[index].HasLook = true;
        ExploreItemUI modelItemEx = modelItem.data as ExploreItemUI;
        _items[index] = modelItemEx;
        modelItemEx.SetData(datas[index].Data.dialogTaskPreloadData);
        modelItemEx.UpdateTitle(datas[index].Data.dialogTaskPreloadData.avatar);
        modelItemEx.UpdateDescribe(datas[index].Data.dialogTaskPreloadData.detail,datas[index].Data.entityId);

        if (_exploreItemDic.ContainsKey(modelItemEx))
        {
            //循环列表重用，重设index
            _exploreItemDic[modelItemEx] = index;
        }
        else
        {
            //新增
            _exploreItemDic.Add(modelItemEx,index);
        }
        
        bool is3d = datas[index].Data.dialogTaskPreloadData.avatar.is3D;
        modelItemEx.SetIs3d(is3d);
        if (is3d)
        {
           // Debug.LogError("+渲染了物体"+ index);
            modelItemEx.SetModel(_gameScene,datas[index].Data.entityId,datas[index].Data.dialogTaskPreloadData.avatar.avatarId,datas[index].Data.dialogTaskPreloadData.scene.bgPicTag);  // 绑定新模型
            await modelItemEx.LoadBackgroundImageAsync2(datas[index].Data.dialogTaskPreloadData.scene.bgPicTag);
        }
        else
        {
            modelItemEx.ShowImg2d(datas[index].Data.dialogTaskPreloadData.scene.bgPicUrl);
        }
    }
    
    private void OnRefreshExploreNet(string s, object body)
    {
        //点击weak network提示的retry
        CLICK_EXPLORE_WEAK_NETWORK_RETRY data = new CLICK_EXPLORE_WEAK_NETWORK_RETRY();
        DataDotMgr.Collect(data);
        
        //数据会在 ExploreController。OnNetReConn 中获取 ，这里只负责刷新，刷新时候还是没有数据 就继续保持提示界面
        UpdateData();
    }

    private bool _hasShow = false;
    private string _updateTimeFlag = String.Empty;
    protected override void OnShow()
    {
        float now = Time.realtimeSinceStartup;
        if (_lastOnShowTime > 0 && now - _lastOnShowTime < OnShowInterval)
        {
            // Debug.LogError("OnShow 调用过于频繁，已拦截。");
            return;
        }
        _lastOnShowTime = now;

        // Debug.LogError("ExplorePanelUI OnShow");
        _inExploreTab = true;
        PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.explore;//panel 入口埋点
        if (_hasShow)
        {
            //-------------应该不会走到这里了  tab 多次触发界面打开问题 唐雷已经处理
            //处理 没有数据时候 tab切换的情况 ，Net重连界面显示
            List<ExploreItemData> datas = _controller.Model.AllData;
            if (datas.Count <= 0)
            {
                UpdateData();
            }
            if (_items.TryGetValue(lastFirstIndex, out ExploreItemUI modelItemEx))
            {
                modelItemEx.DoDescribe();
            } 
            return;
        }

        Notifier.instance.SendNotification(NotifyConsts.ExploreEnter);
        
        _hasShow = true;
        
        _sceneController = this.GetController<SceneController>(ModelConsts.Scene);
        _gameScene = _sceneController.scene;
        UpdateData(true);

        _updateTimeFlag = TimerManager.instance.RegisterTimer((a) => { Update(a);}, 10,0);
        this.ui.ctrlPay.selectedPage = "none";
        AskLastTime();
    }

    private void AskLastTime()
    {
        _experienceState = ExploreExperienceState.Normal;
        _controller.AskExperienceLastTime();
    }

    private void Update(int interval)
    {
        float now = Time.realtimeSinceStartup;
        if (_lastUpdateTime < 0)
        {
            _realIntervalMs = 0;
        }
        else
        {
            _realIntervalMs = (int)((now - _lastUpdateTime) * 1000f);
        }
        _lastUpdateTime = now;

        for (int i = 0; i < _items.Count; i++)
        {
            _items[i].Update(_realIntervalMs);
        }
        
        _isDragging =_list.scrollPane.isDragged;
        Update3DRendering();

        if (_secondLastTime <= 0)
        {
            _secondLastTime = 1000;
            UpdateBySecond();
        }
        else
        {
            _secondLastTime -= _realIntervalMs;
        }

        if (_experienceState == ExploreExperienceState.WaitOver)
        {
            if (_controller.RecordState != RecordState.Recording)
            {
                _experienceState = ExploreExperienceState.Over;
                ExperienceTimeOver();
            }
        }
    }

    private void UpdateBySecond()
    {
        if (this.ui.ctrlPay.selectedPage != "timeDown") return;

        if (!_controller.CanExperienceTimeDown) return;
        
        if (_experienceLastTime > 0)
        {
            _experienceLastTime--;
            ShowTime();
        }
        else
        {
            TimeOver();
            _experienceState = ExploreExperienceState.WaitOver;
        }
    }

    private void ShowTime()
    {
        int minutes = _experienceLastTime / 60;
        int seconds = _experienceLastTime % 60;
        if (this.ui.txtTime.textFormat.size == 28)
        {
            this.ui.txtTime.textFormat.size = 32;
        }
        ui.txtTime.text = $"{minutes:D2}:{seconds:D2}";
    }

    private void ExperienceTimeOver()
    {
        HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
        if (!hCon.IsExploreTab()) return;

        if (!_controller.GetFlag(_payNoticekey))
        {
            _controller.SaveFlag(_payNoticekey,true);
            //显示充值界面
            OnBtnPay();
        }
        
        //屏蔽麦克风
        _controller.RecordLock = true;
        bool showBar = UIManager.instance.GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage).CurrTabState ==
                       BottomTabState.showing;
        if (!showBar)
        {
            _controller.OnCancelRecored();
        }

    }

    private void UpdateData(bool isOnshow  = false)
    {
        // 记录当前显示的Item索引
        _currentDisplayIndex = _list.GetFirstChildInView();
        List<ExploreItemData> datas = _controller.Model.AllData;
        _list.numItems = datas.Count;
        
        VFDebug.Log($"Explore刷新数据  初始化数据个数  datas.Count：{datas.Count}");
        if (datas.Count > 0)
        {
            lastFirstIndex = _list.GetFirstChildInView();
            if (isOnshow) return;
            EnterEntity();
            PlayBGM(lastFirstIndex);
        }
        else
        {
            //出现weak network提示
            APPEAR_EXPLORE_WEAK_NETWORK data = new APPEAR_EXPLORE_WEAK_NETWORK();
            DataDotMgr.Collect(data);

            GComponent uiContainer = GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage).SetUIContainerVisible(true);
            Notifier.instance.SendNotification(NotifyConsts.OpenUI,
                new UIManager.UIParams()
                {
                    viewName = UIConsts.NoNetworkPanelUI, 
                    param = new ContainerUI()
                    {
                        Container = uiContainer
                    }
                });
        }
    }

    private bool _inExploreTab = true;
    private void OnMainTabChange(string s, object body)
    {
        // VFDebug.LogError("OnMainTabChange::_inExploreTab::" + _inExploreTab);
        GameObject cameraContainer = GameObject.Find("CameraPool");
        //Light light = GameObject.Find("RTLight").GetComponent<Light>();
        
        TabData tabData = (TabData) body;

        Camera[] cameras = null;
        if (cameraContainer != null)
        {
            cameras = cameraContainer.GetComponentsInChildren<Camera>();
        }
        
        if (tabData.TargetIndex != TabIndex.Explore)
        {
            SetHandVisible(false);
            // if(light != null)
            //      light.enabled = true;
            OutExploreTab();
            Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.NoNetworkTipUI);
        }
        else
        {
            _inExploreTab = true;
            _hasShow = false;
            // if(light != null)
            //     light.enabled = true;
            if (cameras != null)
            {
                foreach (var ca in cameras)
                {
                    ca.enabled = true;
                }
            }

            PlayBGM(_list.GetFirstChildInView());
            //TODO  这里 临时这么处理吧 ，解决 新号第一次进入 _list.height 赋值的时候  和执行这里 先后顺序问题
            //之后 解决android适配时候 list刷新问题要彻底解决  这里timer也要删除
            TimerManager.instance.RegisterNextFrame((a) =>
            { 
                EnterEntity();
            });
        }
    }

    private void OutExploreTab()
    {
        if (!_inExploreTab) return;
        //RemoveEvent();
        _inExploreTab = false;
        GameObject cameraContainer = GameObject.Find("CameraPool");
     //   Light light = GameObject.Find("RTLight").GetComponent<Light>();
        if (cameraContainer != null)
        {
            Camera[] cameras = cameraContainer.GetComponentsInChildren<Camera>();
            
            // if(light != null)
            //     light.enabled = true;
            if (cameras != null)
            {
                foreach (var ca in cameras)
                {
                    ca.enabled = false;
                }
            }

        }

        _controller.ClearSaffold();
        _controller.CloseRecordUI();
        Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
        Notifier.instance.SendNotification(NotifyConsts.ExploreSoundStop);
        GSoundManager.instance.Pause(SoundManger.SoundChannel.Scene,true);
        Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.NoNetworkTipUI);
        ClearAllEntity();
        ExitAllEntity();
            
        RenderTexturePool.ClearPool();
    }

    /// <summary>
    /// 推荐下行 - 切换推荐实体
    /// </summary>
    /// <param name="msg"></param>
    private void OnRecommendSwitchEntity(SC_RecommendSwitchEntity msg)
    {
        // VFDebug.LogError($"推荐下行 - 切换推荐实体：{msg.timestamp}");
    }

    private void OnShowFirstAvatarCell(string s, object body)
    {
        //TEST
        //_controller.ClearFlag("NewhandDrag");
        SetHandVisible(true);
    }

    private void SetHandVisible(bool value)
    {
        HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
        if (!hCon.IsExploreTab()) return;
        if (value)
        {
            if (_controller.GetFlag("NewhandDrag"))
                return;
            _controller.SaveFlag("NewhandDrag", true);
            //--改为滑动结束消失
            TimerManager.instance.RegisterTimer(a =>
            {
                SetHandVisible(false);
            }, ExploreConst.NewhandDragTime);
            
            Notifier.instance.SendNotification(NotifyConsts.OpenUI, UIConsts.ExploreNewhandPanelUI);
        }
        else
        {
            Notifier.instance.SendNotification(NotifyConsts.CloseUI, UIConsts.ExploreNewhandPanelUI);
        }
    }

    private void OnRefreshExploreList(string s, object body)
    {
        VFDebug.Log("刷新Explore 最新数据----------------------------------------------------------------");
        // 记录当前滚动位置
        float oldPosY = _list.scrollPane.posY;
        _list.numItems = _controller.Model.AllData.Count;
        
        //刷新列表，但保持原有位置
        // _list.RefreshVirtualList();
        _list.scrollPane.posY = oldPosY;
    }

    private int lastFirstIndex = -1;
    private void OnScrollEnd()
    {
        // **确保翻页才触发**
        int curIndex = _list.GetFirstChildInView();
        _currentDisplayIndex = curIndex;  // 更新当前显示的Item索引
        _controller.UpdateLookAtIndex(curIndex);
        if (curIndex == lastFirstIndex) return;
        SetUpMove(curIndex < lastFirstIndex);
        lastFirstIndex = curIndex;
    
        
        List<ExploreItemData> datas = _controller.Model.AllData;
        ModelPreloader.Instance.PreloadModels(datas, curIndex);
    
        PlayBGM(_list.GetFirstChildInView());
        long curTaskId = _controller.CurTaskId;
        EnterEntity();
        long nextTaskId = _controller.CurTaskId;
        DotDrag(curTaskId,nextTaskId);
        
        SetHandVisible(false);
    }

    private void SetUpMove(bool value)
    {
        _UpDrag = value;
    }

    private void DotDrag(long curTaskId, long nextTaskId)
    {
        bool showBar = _controller.GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage).CurrTabState ==
                       BottomTabState.showing;
        
        SWIPE_EXPLORE_FEED data = new SWIPE_EXPLORE_FEED();
        data.swipe_side = _UpDrag ? "up" : "down";
        data.previous_task_id = curTaskId;
        data.target_task_id = nextTaskId;
        data.is_tab_bar = showBar?"appear":"hide";
        DataDotMgr.Collect(data);
    }

    private void OnOpenBGM(string s, object body)
    {
        bool open = (bool) body;
        if (open)
        {
            PlayBGM(_list.GetFirstChildInView());
        }
        else
        {
            GSoundManager.instance.Pause(SoundManger.SoundChannel.Scene,true);
        }
    }
    private void PlayBGM(int index)
    {
        if (_controller.Model.BackgroundMusic == PB_Explore_UserSetting_BackgroundMusic.EO_US_BM_OFF ) return;
        List<ExploreItemData> datas = _controller.Model.AllData;
        string bgm = datas[index].Data.dialogTaskPreloadData.scene.bgVoiceTag;
        // VFDebug.Log($"bgm + {bgm}   _list.index::{_list.GetFirstChildInView()}");
        SoundManger.instance.PlayBGM(bgm);
    }

    private void EnterEntity()
    {
        Debug.Log("=== EnterEntity ===");
        //外层的延迟 是为了对应 OnInit 方法中 imgBg 刷新的延迟 
        //内层的times 是为了对应 OnInit 中 _list.height变化后 触发的GList RefreshVirtualList 的处理
        //这两个延迟 必不可少！
        TimerManager.instance.RegisterNextFrame((a) =>
        {
            Timers.inst.CallLater(a =>
            {
                ConectNet();
                _controller.ClearSaffold();
                Debug.Log("=== EnterEntity222222222 ===");
                ExitAllEntity();
    
                if (_items.TryGetValue(lastFirstIndex, out ExploreItemUI modelItemEx))
                {
                    modelItemEx.Enter();
                } 
            });
         
        });
    }

    private void ExitAllEntity()
    {
        foreach (var kv in _items)
        {
            kv.Value.Exit();
        }
    }
    
    private void ClearAllEntity()
    {
        foreach (var kv in _items)
        {
            kv.Value.LogicEntity.Clear();
        }
    }

    private void ConectNet()
    {
        if (_items.TryGetValue(lastFirstIndex, out ExploreItemUI modelItemEx))
        {
            long taskId = modelItemEx.Data.taskId;
            _controller.CurNetStrategy.SendRecommendReq(taskId);
            VFDebug.Log($"Explore ConectNet taskId::{taskId}  SendRecommendReq");
        }
    }

    protected override void OnHide()
    {
        base.OnHide();
        Debug.LogError("ExplorePanelUI OnHide");
        _hasShow = false;
        //this.RemoveEvent();
        _items.Clear();
        if (_updateTimeFlag != String.Empty)
        {
            TimerManager.instance.UnRegisterTimer(_updateTimeFlag);
            _updateTimeFlag = String.Empty;
        }
    }
}



/*
****************************************************
# 作者：Huangshiwen
# 创建时间：   2025/03/17 16:33:03 星期一
# 功能：Nothing
****************************************************
*/

namespace Modules.DataDot
{
    public enum DotHeartStatusEnum
    {
        unlimited_heart,
        unlimited_hearts_tool,
        full_hearts,
        next_heart,
    }
    
    public enum DotRefillBtnEnum
    {
        //在full hearts悬浮窗中按钮失效
        button_unavailable,
        //在next heart悬浮窗中钻石够
        diamond_enough,
        //在next heart悬浮窗中钻石不够
        diamond_not_enough,
    }
    
    public enum DotPracticeBtnEnum
    {
        //按钮的状态
        practice_to_earn_hearts,
        //在full hearts悬浮窗中按钮失效
        button_unavailable,
        //在next heart悬浮窗中有0次practice机会
        pracitce_chance_0,
        //在next heart悬浮窗中有1次practice机会
        pracitce_chance_1,
    }
    
    public enum DotRootPageEnum
    {
        //按钮的状态
        full_hearts_page,
        //next heart悬浮窗
        next_heart_page,
        //在扉页中点击start后出现的红心不足悬浮窗
        unsufficient_hearts_page,
    }
    
// 用户点击红心icon
    public class DotClickHeartIcon : DataDotBase
    {
        public override string Event_name => "Click_heart_icon";
        public string heart_status;
    }

// 在unlimited hearts状态下点击红心icon出现unlimited hearts悬浮窗
    public class DotAppearUnlimitedHeartPage : DataDotBase
    {
        public override string Event_name => "Appear_unlimited_heart_page";
        public string heart_status;
    }

// 在unlimited hearts tool状态下点击红心icon出现unlimited hearts tool悬浮窗
    public class DotAppearUnlimitedHeartToolPage : DataDotBase
    {
        public override string Event_name => "Appear_unlimited_heart_tool_page";
        public double time_left;
    }

// 在full hearts状态下点击红心icon出现full hearts悬浮窗
    public class DotAppearFullHeartsPage : DataDotBase
    {
        public override string Event_name => "Appear_full_hearts_page";
    }

// 在next heart状态下点击红心icon出现next heart悬浮窗
    public class DotAppearNextHeartPage : DataDotBase
    {
        public override string Event_name => "Appear_next_heart_page";
        public int heart_left;
        public double next_heart_time_left;
        public string refill_hearts_button_status;
        public string practice_to_earn_hearts_button_status;
    }
    
    //Appear_heart_page
    public class DotAppearHeartPage : DataDotBase
    {
        public override string Event_name => "Appear_heart_page";
        public int heart_count;
    }

// 用户点击unlimited hearts按钮
    public class DotClickUnlimitedHearts : DataDotBase
    {
        public override string Event_name => "Click_unlimited_hearts";
        public string subscribe_type;//特殊
        public string refill_hearts_button_status;
        public string practice_to_earn_hearts_button_status;
        
    }

// 用户点击refill hearts按钮
    public class DotClickRefillHearts : DataDotBase
    {
        public override string Event_name => "Click_refill_hearts";
        public string source_page;
        public string practice_to_earn_hearts_button_status;
    }

// 用户点击practice to earn hearts按钮
    public class DotClickPracticeToEarnHearts : DataDotBase
    {
        public override string Event_name => "Click_practice_to_earn_hearts";
        public string source_page;
        public string refill_hearts_button_status;
    }

// 完成无限红心任务后在结算页出现无限红心恭喜页面
    public class DotAppearUnlimitedHeartsCongraPage : DataDotBase
    {
        public override string Event_name => "Appear_unlimited_hearts_congra_page";
        public int heart_left;
    }

// 在无限红心恭喜页面点击continue按钮
    public class DotClickUnlimitedHeartsCongraPageContinue : DataDotBase
    {
        public override string Event_name => "Click_unlimited_hearts_congra_page_continue";
    }

// 有无限红心的daily quest进入完成状态
    public class DotCutDailyQuestsFinished : DataDotBase
    {
        public override string Event_name => "Cut_daily_quests_finished";
    }

// 用户在钻石小店物品中点击无限红心
    public class DotClickDiamondShopUnlimitedHearts : DataDotBase
    {
        public override string Event_name => "Click_diamond_shop_unlimited_hearts";
    }

// 用户在钻石小店物品中点击无限红心后出现完成每日任务获得无限红心提示悬浮窗
    public class DotAppearEarnUnlimitedHeartsReminderPage : DataDotBase
    {
        public override string Event_name => "Appear_earn_unlimited_hearts_reminder_page";
    }

// 用户在完成每日任务获得无限红心提示悬浮窗中点击got it
    public class DotClickEarnUnlimitedHeartsReminderPageGotIt : DataDotBase
    {
        public override string Event_name => "Click_earn_unlimited_hearts_reminder_page_got_it";
    }

// （覆盖相同名称旧点）在扉页中点击start
    public class DotClickMainTitlePageStart : DataDotBase
    {
        public override string Event_name => "Click_Main_title_page_start";
        public long task_id;
        public long dialogue_id;
    }

// 在扉页中点击start后出现红心不足悬浮窗
    public class DotAppearInsufficientHeartsPage : DataDotBase
    {
        public override string Event_name => "Appear_insufficient_hearts_page";
        public string refill_hearts_button_status;
        public string practice_to_earn_hearts_button_status;
    }

// 在红心不足悬浮窗中点击关闭
    public class DotClickInsufficientHeartsPageQuit : DataDotBase
    {
        public override string Event_name => "Click_insufficient_hearts_page_quit";
    }
}
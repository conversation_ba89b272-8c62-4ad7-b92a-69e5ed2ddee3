﻿
using System;
using System.Collections.Generic;
using AnimationSystem;
using FairyGUI;
using Msg.basic;
using Msg.dialog_task;
using Msg.explore;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.Explore.ExploreType;
using ScriptsHot.Game.Modules.Explore.ExploreType.Base;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using UnityEngine;
using NotImplementedException = System.NotImplementedException;
using Vector2 = System.Numerics.Vector2;

namespace UIBind.Explore.Item
{
    public class ExploreCellBase:ComponentObject,IComponetOwner
    {
        private List<BaseUIEventVO> _events = new List<BaseUIEventVO>();
        
        public GComponent GCom { get; set; } = null;

        
        private bool _isNew = true;
        
        protected ExploreModel _model;
        protected ExploreController _controller;

        protected GComponent _ui;
        
        private PB_DialogTaskPreloadData _data;
        public PB_DialogTaskPreloadData Data => _data;

        private ExploreEntityBase _logicEntity;
        public ExploreEntityBase LogicEntity => _logicEntity;

        private bool _IsPlayAvatarAudio = false;

        /// <summary>
        /// 正在播放avatar音频 (模型口型)
        /// </summary>
        public bool IsPlayAvatarAudio
        {
            get
            {
                return _IsPlayAvatarAudio;
            }
            set
            {
                //VFDebug.Log($"IsPlayAvatarAudio ::::::::::::::{IsPlayAvatarAudio}");
                _IsPlayAvatarAudio = value;
            }
        }

        /// <summary>
        /// 正在播放脚手架音频 (没有口型)
        /// </summary>
        public bool IsPlayScaffoldAudio = false;
        
        /// <summary>
        /// 是否可以播放音频
        /// </summary>
        public bool IfCanPlayAudio = false;

        public virtual void OnInit(GComponent gCom)
        {
            this.GCom = gCom;
            _isNew = true;
            
            _model = ModelManager.instance.GetModel(ModelConsts.Explore) as ExploreModel;
            _controller = ControllerManager.instance.GetController<ExploreController>(ModelConsts.Explore) as ExploreController;
        }

        public virtual void Enter()
        {
            AddEvent();
            _controller.CurEnterEntity = this;
            _logicEntity.Enter();
        }

        public virtual void Exit()
        {
            RemoveEvent();
            _logicEntity.Exit();
        }

        public virtual void SetUI(GComponent ui)
        {
            _ui = ui;
        }
        public virtual void SetData(PB_DialogTaskPreloadData data)
        {
            _data = data;

            if (_logicEntity != null)
            {
                _logicEntity.UnEvent();
            }

            ExploreParam p = new ExploreParam();
            switch (_data.dialogMode)
            {
                case PB_DialogMode.Challenge:
                    _logicEntity = new ExploreRolePlayEntity();
                    p.ChatMode = PB_DialogMode.Challenge;
                    p.Entity = _logicEntity;
                    p.UI = this;
                    _logicEntity.Init(p,_data,this);
                    break;
                case PB_DialogMode.Tutor:
                case PB_DialogMode.Career:
                    _logicEntity = new ExploreNormalEntity();
                    p.ChatMode = PB_DialogMode.Career;
                    p.Entity = _logicEntity;
                    p.UI = this;
                    _logicEntity.Init(p,_data,this);
                    break;
                default:
                    _logicEntity = new ExploreNormalEntity();
                    p.ChatMode = PB_DialogMode.Career;
                    p.Entity = _logicEntity;
                    p.UI = this;
                    _logicEntity.Init(p,_data,this);
                    break;
            }
        }

        public virtual bool GetModelLoaded()
        {
            return false;
        }

        protected void AddUIEvent(FairyGUI.EventListener eventListener, FairyGUI.EventCallback0 callback, int buttonClickInterval = 300)
        {
            this.AddUIEvent(eventListener, callback, null, buttonClickInterval);
        }

        protected void AddUIEvent(FairyGUI.EventListener eventListener, FairyGUI.EventCallback1 callback, int buttonClickInterval = 300)
        {
            this.AddUIEvent(eventListener, null, callback, buttonClickInterval);
        }

        private void AddUIEvent(FairyGUI.EventListener eventListener,
            FairyGUI.EventCallback0 callback0, FairyGUI.EventCallback1 callback1, int buttonClickInterval)
        {
            for (int i = 0; i < this._events.Count; i++)
            {
                BaseUIEventVO vo = this._events[i];
                if (vo.eventListener == eventListener && (vo.callback0 == callback0 || vo.callback1 == callback1))
                {
                    return;
                }
            }

            BaseUIEventVO eventVO = new BaseUIEventVO();
            eventVO.eventListener = eventListener;
            eventVO.callback0 = callback0;
            eventVO.callback1 = callback1;
            eventVO.eventTriggerInterval = eventListener.type == "onClick" ? buttonClickInterval : -1;
            eventVO.listenerCallback = eventContext =>
            {
                //按钮点击间隔判定
                if (eventVO.eventTriggerInterval > -1)
                {
                    var currTime = TimeExt.currTime;
                    if (currTime - eventVO.eventTriggerTime < eventVO.eventTriggerInterval)
                        return;
                    eventVO.eventTriggerTime = currTime;
                }

                if (callback0 != null)
                    callback0();
                if (callback1 != null)
                    callback1(eventContext);
            };
            this._events.Add(eventVO);
            eventListener.Add(eventVO.listenerCallback);
        }

        public void RemoveUIEvent()
        {
            for (int i = 0; i < this._events.Count; i++)
            {
                BaseUIEventVO vo = this._events[i];
                vo.eventListener.Remove(vo.listenerCallback);
                vo.eventListener = null;
                vo.callback0 = null;
                vo.callback1 = null;
                vo.listenerCallback = null;
            }

            this._events.Clear();
        }

        public virtual void Dispose()
        {
            RemoveEvent();
        }

        public void RemoveEvent()
        {
            RemoveUIEvent();
            UnEvent();
        }

        protected void ShowGoldNum(long score, GComponent gObj)
        {
            if (score > 80 && _isNew)//同一轮次之会出现一次
            {
                SoundManger.instance.PlayUI("GetGold");
                var obj = new
                {
                    gold = 3,
                    pos = new Vector2(40,40)
                };
                
                Notifier.instance.SendNotification(NotifyConsts.OpenUI,new UIManager.UIParams{viewName = UIConsts.CommonGetGold,param = obj});
                //超过80 金币加3
                // _chatController.GetUI<CommonGetGoldTipsUI>(UIConsts.CommonGetGold).Open(3,gObj,new Vector2(40,40));
                _isNew = false;
            }
        }

        public virtual void Update(int interval)
        {
            _logicEntity?.Update(interval);
        }

        public virtual void AddEvent()
        {
            _logicEntity?.AddEvent();
        }

        public virtual void UnEvent()
        {
            _logicEntity?.UnEvent();
        }
        
        public virtual void TriggerBtnPlay()
        {
        }
        
        public string GetUnitByAvatarTid(long id)
        {
            SceneController sC = ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene) as SceneController;
            var unit = sC.scene.GetComponent<UnitComponent>().GetUnitByAvatarTid(id);
            if(unit != null)
                return unit.name;
            else
                return String.Empty;
        }

        public virtual void ResetCell()
        {
        }
    }
}
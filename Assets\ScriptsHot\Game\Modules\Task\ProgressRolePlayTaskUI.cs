﻿
/* 
****************************************************
* 作者：CuiMengLin
* 创建时间：2024/09/12 14:24:52 星期四
* 功能：Nothing
****************************************************
*/

using CommonUI;
using DG.Tweening;
using FairyGUI;
using Msg.basic;
using Msg.dialog_task;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.ChatLogicNew.ChatType;
using ScriptsHot.Game.Modules.ChatStart;
using ScriptsHot.Game.Modules.Common;
using UnityEngine;
using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.Task
{
 

    public class ProgressRolePlayTaskUI: BaseUI<UIBind.Progress.ProgressRolePlayTaskPanel>
    {
        public ProgressRolePlayTaskUI(string name) : base(name){ }
        public override string uiLayer => UILayerConsts.Top;
        protected override bool isFullScreen => true;
        
        private float _allWidth = 486;
        private int _progressStartNum = 7;//最开始要有起始值

        private float _progressCurValue = 0;
        private float _progressMinValue = 7;
        private ProgressFreeTalkTipsState _curState = ProgressFreeTalkTipsState.None;
        private bool _isMax = false;
        private float _filledAmount = 0;
        private int _titleMinHigth = 83;
        private ChatLogicController _chatController;
        private TextFieldExtension tfOrigin =>  ui.comGoal.tfOrigin as TextFieldExtension;

        /// <summary>
        /// 进度条是否播放过动画 ，策划改为 只播放一次动画
        /// </summary>
        private bool _ifBarEffectPlayed = false;
        protected override void OnInit(GComponent uiCom)
        {
            _chatController =
                ControllerManager.instance.GetController<ChatLogicController>(ModelConsts.ChatLogic) as ChatLogicController;

            tfOrigin.SetNewFont(FontCfg.DinNextRegular, Color.black, 32);
            tfOrigin.TextGtf.textFormat.lineSpacing = 0;
            tfOrigin.touchable = true;
            tfOrigin.OnClickNewWord += RequestNewWordTrans;
        }
        
        protected override void OnShow()
        {
            ui.Reset.Play();
            PB_GetStartPageInfoResp pageInfo =
                GetModel<ChatStartModel>(ModelConsts.ChatStartModel).GetStartPageInfoResp();
            ui.comGoal.txtGoal.text = pageInfo.objective;
            ui.comGoal.txtGoal.alpha = 0;
            ui.comGoal.txtGoalFlag.text = pageInfo.objective;
            ui.comGoal.txtGoalFlag.alpha = 0;
            this.AddUIEvent(this.ui.comGoal.btnTitle.onClick, this.OnClickTitle);

            this.ui.comGoal.ctrShowDetail.selectedIndex = 0;
            this.ui.bar.comBar.bar.value = _progressMinValue;
            ui.bar.spineStar.visible = false;
            ui.bar.comBarTip.com.x = 0;
            ui.bar.comBarTip.com.visible = false;
            _progressCurValue = 0;
            UpdateTitleHeight();

            this.ui.bar.txtNewFlag.SetKey("ui_roleplay_progress_bar_beginner_guidance");
            this.ui.bar.txtNewFlag.visible = false;//_chatController.CurChat != null && (_chatController.CurChat as ChatRolePlay2).RolePlayEnterCount <= 1;

            ui.comGoal.txtGoalTitle.SetKey("learnPath_goal_title");
            
            DoTfOrigin();
        }

        private void DoTfOrigin()
        {
            if (this.ui.comGoal.ctrShowDetail.selectedIndex == 0)
            {
                tfOrigin.SetMaxRow(2);
            }
            else
            {
                tfOrigin.CloseMaxRow();
            }
            PB_GetStartPageInfoResp pageInfo =
                GetModel<ChatStartModel>(ModelConsts.ChatStartModel).GetStartPageInfoResp();
            
            RichContent richContent = new RichContent();
            richContent.Content =  pageInfo.objective;
            richContent.Type = RichContentType.Text;
            richContent.IsLast = true;
            tfOrigin.Reset();
            tfOrigin.OnClickNewWord += RequestNewWordTrans;
            // tfOrigin.Clear();
            tfOrigin.AppendContent(richContent).Display(ShowMode.Normal);
        }

        private void OnClickTitle()
        {
            if (this.ui.comGoal.ctrShowDetail.selectedIndex == 0)
            {
                this.ui.comGoal.ctrShowDetail.selectedIndex = 1;
            }
            else
            {
                this.ui.comGoal.ctrShowDetail.selectedIndex = 0;
            }

            UpdateTitleHeight();
        }

        private void UpdateTitleHeight()
        {
            if (this.ui.comGoal.ctrShowDetail.selectedIndex == 0)
            {
                ui.comGoal.txtGoal.height = _titleMinHigth;
            }
            else
            {
                if (ui.comGoal.txtGoalFlag.height <= _titleMinHigth)
                {
                    ui.comGoal.txtGoal.height = _titleMinHigth;
                }
                else
                {
                    ui.comGoal.txtGoal.height = ui.comGoal.txtGoalFlag.height;
                }
            }
            if (ui.comGoal.txtGoalFlag.height <= _titleMinHigth)
            {
                this.ui.comGoal.ctrShowDetail.selectedIndex = 2;
            }
            
            DoTfOrigin();
        }

        public void ChangeValue(PB_DialogProgressInfo progressInfo)
        {
            if (progressInfo.total_progress == 0)
            {
                VFDebug.LogError("roleplay进度条分母为0了！");
                progressInfo.total_progress = 1;
            }
            float progress = (float)(progressInfo.cur_progress) / (float)progressInfo.total_progress; // 计算完成的比例
            _filledAmount = Mathf.Lerp(_progressStartNum/100f, 1, progress); 
            bool isMax = progressInfo.cur_progress >= progressInfo.total_progress;
            if (isMax)
                _filledAmount = 1;

            _barTipEffectPlaying = false;
            float addValue = progressInfo.cur_progress - _progressCurValue;
            
            if(progressInfo.cur_progress != 0 && addValue > 0)
                SoundManger.instance.PlayUI("free_progress_add");
            
            ui.bar.comBarTip.tfScore.text = "+" + addValue.ToString();
            _progressCurValue = progressInfo.cur_progress;

            float endValue = _filledAmount * 100 > _progressMinValue ? _filledAmount * 100 : _progressMinValue;
            VFDebug.Log($"_progressStartNum :{progressInfo.cur_progress} + progressInfo.total_progress:{progressInfo.total_progress} + progress: {progress } + _filledAmount :{_filledAmount}+ endValue:{ endValue} ");
            DOTween.To(() => ui.bar.comBar.bar.value, x => ui.bar.comBar.bar.value = x, endValue, 0.5f)
            .OnUpdate(() =>
            {
                ui.bar.spineStar.visible = false;
                double width = ui.bar.comBar.bar.actualWidth * (ui.bar.comBar.bar.value / ui.bar.comBar.bar.max);
                float x = ui.bar.comBar.com.x + (float) width;
                ui.bar.comBarTip.com.x = x - 35.5f;
                PlayBarTipEffect(true);
                
            }).OnComplete(() =>
            {
                PlayBarTipEffect(false);
                double width = ui.bar.comBar.bar.actualWidth * (ui.bar.comBar.bar.value / ui.bar.comBar.bar.max);
                float x = ui.bar.comBar.com.x + (float) width;
                ui.bar.spineStar.x = x;
                
                ui.bar.spineStar.visible = true;
                ui.bar.spineStar.spineAnimation.AnimationState.ClearListenerNotifications();
                ui.bar.spineStar.spineAnimation.AnimationState.SetAnimation(0,"animation",false).Complete +=
                    (t) =>
                    {
                        ui.bar.spineStar.visible = false;
                    };;
            
                if (isMax)
                {
                    _isMax = true;
                    SoundManger.instance.PlayUI("free_level_up");
                    // ChangeTipsState(ProgressFreeTalkTipsState.Finish);
                }

                if (_chatController.CurChat != null && (_chatController.CurChat as ChatRolePlay2).RolePlayEnterCount <= 1 && !_ifBarEffectPlayed)
                {
                    _ifBarEffectPlayed = true;
                    _barEffectCountCur = 0;
                    TimerManager.instance.RegisterTimer(cc =>
                    {
                        PlayBarEffect();
                    }, 300, 1);
                }
                
            });
        }

        private bool _barTipEffectPlaying = false;
        private void PlayBarTipEffect(bool show)
        {
            Transition showTransition = ui.bar.com.GetTransition("show");
            Transition hideTransition  = ui.bar.com.GetTransition("hide");
            if (show)
            {
                if (_barTipEffectPlaying) return;
                _barTipEffectPlaying = true;
                ui.bar.comBarTip.com.visible = true;
                showTransition.Play();
            }
            else
            {
                hideTransition.Play(() =>
                {
                    ui.bar.comBarTip.com.visible = false;
                });   
            }
        }

        private int _barEffectCountMax = 2;
        private int _barEffectCountCur = 0;
        
        private void PlayBarEffect()
        {
            if (_barEffectCountCur >= _barEffectCountMax) return;
            _barEffectCountCur++;
            // ShowPerSpineVisible();
            this.ui.bar.txtNewFlag.visible = true;
            DOTween.To(() => ui.bar.comBar.bar.value, x => ui.bar.comBar.bar.value = x,  100, 0.5f)
                .OnUpdate(() =>
                {
                    // double width = ui.bar.comBar.bar.actualWidth * (ui.bar.comBar.bar.value / ui.bar.comBar.bar.max);
                    // float x = ui.bar.comBar.com.x + (float) width;
                    // ui.bar.spineStar.x = x;
                }).OnComplete(() =>
            {
                this.ui.bar.txtNewFlag.visible = false;
                float endValue = _filledAmount * 100 > _progressMinValue ? _filledAmount * 100 : _progressMinValue;
                this.ui.bar.comBar.bar.value = endValue;
                PlayBarEffect();
            });
        }

        public void ChangeTipsState(ProgressFreeTalkTipsState tipsState)
        {
            switch (tipsState)
            {
                case ProgressFreeTalkTipsState.None:
                    break;
                case ProgressFreeTalkTipsState.Setting:
                    CS_ChangeMarkInfoReq msg = new CS_ChangeMarkInfoReq();
                    msg.option = PB_MarkOperationTypeEnum.OpAssistLevelPop;
                    MsgManager.instance.SendMsg(msg);
                    break;
           
            }

            _curState = tipsState;
        }

        public ProgressFreeTalkTipsState GetFreeTalkTipsState()
        {
            return _curState;
        }
        
        public void DismissWithAnimation()
        {
            ui.Out.Play(() => {this.Hide(); });
        }

        protected override void OnHide()
        {
            
        }


        #region 点词
        private const string ClientIdPrefix = "ChatRolePlay2.0Title";
        private GComponent _showNewWordComp;
        private NewWordComponent _curNewWordComp;
        private ChatStartController ChatStartCtrl =>
            GetController<ChatStartController>(ModelConsts.ChatStartController);
       private void RequestNewWordTrans(NewWordComponent comp,Vector2 wordPos)
        {
            _curNewWordComp = comp;
            ChatStartCtrl.NewWordResp += RespNewWordTrans;
         
            var msg = new CS_ClickWordReq
            {
                text = comp.word,
                client_id = ClientIdPrefix,
            };
            AddChildCom(comp);
            comp.xy = comp.GlobalToLocal(wordPos);
            MsgManager.instance.SendMsg(msg, NewWordTransFail);
            Notifier.instance.SendNotification(NotifyConsts.NewWorldAudioStart);
        }
        
        private void RespNewWordTrans(SC_ClickWordAck msg)
        {
            if (msg.data.client_id != ClientIdPrefix)
                return;
            if (msg.code != 0)
            {
                Debug.Log("SC_ClickWordAck is error");
                NewWordTransFail(GRPCManager.ErrorType.None,null);
                return;
            }
            
            if (_curNewWordComp != null && !_curNewWordComp.isDisposed)
            {
                if (_curNewWordComp.word.ToLower() == msg.data.word.ToLower())
                {
                    TTSManager.instance.PlayTTS(msg.data.audio_id);
                }
                _curNewWordComp.SetWord(msg.data.translation,3F,true);
                _curNewWordComp.parentTfExt.AppendNewWord(msg.data.word, msg.data.count);
                _curNewWordComp.parentTfExt.CreateNewWord();
            }
        }
        
        //生词翻译失败
        private void NewWordTransFail(GRPCManager.ErrorType et,Google.Protobuf.IMessage msg)
        {
            if (_curNewWordComp != null && !_curNewWordComp.isDisposed)
                _curNewWordComp.Dispose();
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("translate-error-toast");
        }

        private void AddChildCom(GComponent cmp)
        {
            if (_showNewWordComp != null)
            {
                _showNewWordComp.Dispose();
                _showNewWordComp = null;
            }
            _showNewWordComp = cmp;
            ui.com.AddChild(cmp);
        }

        #endregion
    }
}
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreAdviceItem : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreAdviceItem";

        public GComponent tfOrigin;
        public GRichTextField txtName;
        public GGroup grpTitle;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            tfOrigin = (GComponent)com.GetChildAt(1);
            txtName = (GRichTextField)com.GetChildAt(3);
            grpTitle = (GGroup)com.GetChildAt(4);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            tfOrigin = null;
            txtName = null;
            grpTitle = null;
        }
    }
}
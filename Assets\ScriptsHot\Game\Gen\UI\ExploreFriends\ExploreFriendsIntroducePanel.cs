/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsIntroducePanel : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ExploreFriendsIntroducePanel";

        public Controller IsSelect;
        public GGraph imgBG;
        public GGraph bgImg;
        public GTextField TitleTxt;
        public GGroup Introduce;
        public GLoader3D AvatarLoader;
        public FriendCommonBtn NextBtn;
        public GTextField SelectItemName;
        public GGroup NameNode;
        public GImage bg;
        public GTextField SelectItemName_2;
        public GGroup chatNode;
        public GList selectList;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            IsSelect = com.GetControllerAt(0);
            imgBG = (GGraph)com.GetChildAt(0);
            bgImg = (GGraph)com.GetChildAt(1);
            TitleTxt = (GTextField)com.GetChildAt(2);
            Introduce = (GGroup)com.GetChildAt(7);
            AvatarLoader = (GLoader3D)com.GetChildAt(8);
            NextBtn = new FriendCommonBtn();
            NextBtn.Construct(com.GetChildAt(9).asCom);
            SelectItemName = (GTextField)com.GetChildAt(10);
            NameNode = (GGroup)com.GetChildAt(12);
            bg = (GImage)com.GetChildAt(13);
            SelectItemName_2 = (GTextField)com.GetChildAt(14);
            chatNode = (GGroup)com.GetChildAt(15);
            selectList = (GList)com.GetChildAt(16);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            IsSelect = null;
            imgBG = null;
            bgImg = null;
            TitleTxt = null;
            Introduce = null;
            AvatarLoader = null;
            NextBtn.Dispose();
            NextBtn = null;
            SelectItemName = null;
            NameNode = null;
            bg = null;
            SelectItemName_2 = null;
            chatNode = null;
            selectList = null;
        }
    }
}
// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/incentive/ranking.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.incentive {

  /// <summary>Holder for reflection information generated from protobuf/incentive/ranking.proto</summary>
  public static partial class RankingReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/incentive/ranking.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static RankingReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiBwcm90b2J1Zi9pbmNlbnRpdmUvcmFua2luZy5wcm90bxobcHJvdG9idWYv",
            "YmFzaWMvZGlhbG9nLnByb3RvGh5wcm90b2J1Zi9iYXNpYy9pbmNlbnRpdmUu",
            "cHJvdG8aG3Byb3RvYnVmL2Jhc2ljL2NvdXJzZS5wcm90byIuChtTU19HZXRV",
            "c2VyUmFua2luZ1N1bW1hcnlSZXESDwoHdXNlcl9pZBgBIAEoAyJNChtTU19H",
            "ZXRVc2VyUmFua2luZ1N1bW1hcnlBY2sSDAoEY29kZRgBIAEoBRIgCgRkYXRh",
            "GAIgASgLMhIuUEJfUmFua2luZ1N1bW1hcnkiiAIKEVBCX1JhbmtpbmdTdW1t",
            "YXJ5EikKDnJhbmtpbmdfc3RhdHVzGAEgASgOMhEuUEJfUmFua2luZ1N0YXR1",
            "cxIqCgtjaGFuZ2VfdHlwZRgCIAEoDjIVLlBCX1JhbmtpbmdDaGFuZ2VUeXBl",
            "EiQKDWN1cnJlbnRfbGV2ZWwYAyABKAsyDS5QQl9MZXZlbEluZm8SIAoJcHJl",
            "X2xldmVsGAQgASgLMg0uUEJfTGV2ZWxJbmZvEhUKDWN1cnJlbnRfb3JkZXIY",
            "BSABKAUSEQoJcHJlX29yZGVyGAYgASgFEhAKCHVzZXJfZXhwGAcgASgDEhgK",
            "EHVzZXJfcmFua2luZ19leHAYCCABKAMiWwoeQ1NfR2V0VXNlclJhbmtpbmdQ",
            "b3J0YWxEYXRhUmVxEg8KB3VzZXJfaWQYASABKAMSKAoKbW9ja19zY2VuZRgC",
            "IAEoDjIULlBCX1JhbmtpbmdNb2NrU2NlbmUiUwoeU0NfR2V0VXNlclJhbmtp",
            "bmdQb3J0YWxEYXRhQWNrEgwKBGNvZGUYASABKAUSIwoEZGF0YRgCIAEoCzIV",
            "LlBCX1JhbmtpbmdQb3J0YWxEYXRhIrMDChRQQl9SYW5raW5nUG9ydGFsRGF0",
            "YRIwChJyYW5raW5nX2xldmVsX2xpc3QYASADKAsyFC5QQl9SYW5raW5nTGV2",
            "ZWxJbmZvEikKDnJhbmtpbmdfc3RhdHVzGAIgASgOMhEuUEJfUmFua2luZ1N0",
            "YXR1cxIqCgtjaGFuZ2VfdHlwZRgDIAEoDjIVLlBCX1JhbmtpbmdDaGFuZ2VU",
            "eXBlEiQKDWN1cnJlbnRfbGV2ZWwYBCABKAsyDS5QQl9MZXZlbEluZm8SIAoJ",
            "cHJlX2xldmVsGAUgASgLMg0uUEJfTGV2ZWxJbmZvEhYKDmxlZnRfdGltZXN0",
            "YW1wGAYgASgDEhkKEWxhc3RfdW5sb2NrX2NvdW50GAcgASgFEiQKDnVzZXJf",
            "aXRlbV9saXN0GAggAygLMgwuUEJfVXNlckl0ZW0SFQoNY3VycmVudF9vcmRl",
            "chgJIAEoBRIRCglwcmVfb3JkZXIYCiABKAUSFwoPY2FuX2RyYXdfcmV3YXJk",
            "GAsgASgIEi4KEXJhbmtpbmdfcGFnZV9zaG93GAwgASgLMhMuUEJfUmFua2lu",
            "Z1BhZ2VTaG93IjsKE1BCX1JhbmtpbmdMZXZlbEluZm8SEAoIbGV2ZWxfaWQY",
            "ASABKAUSEgoKbGV2ZWxfbmFtZRgCIAEoCSI/ChNQQl9SYW5raW5nT3JkZXJJ",
            "bmZvEhEKCXByZV9vcmRlchgBIAEoAxIVCg1jdXJyZW50X29yZGVyGAIgASgD",
            "IqUBCiBTU19UcmlnZ2VyUmFua2luZ1VwZGF0ZUJ5VGFza1JlcRIWCg50YXNr",
            "X3JlY29yZF9pZBgBIAEoAxIhCgl0YXNrX21vZGUYAiABKA4yDi5QQl9EaWFs",
            "b2dNb2RlEhAKCHN0YXJfY250GAMgASgFEhEKCXRhc2tfY29zdBgEIAEoAxIN",
            "CgVyb3VuZBgFIAEoAxISCgpzdGFydF90aW1lGAYgASgDIk8KIFNTX1RyaWdn",
            "ZXJSYW5raW5nVXBkYXRlQnlUYXNrQWNrEgwKBGNvZGUYASABKAUSHQoEZGF0",
            "YRgCIAEoCzIPLlBCX1JhbmtpbmdJbmZvIk4KHVNTX0dldFRhc2tFc3RpbWF0",
            "ZUV4cEJhdGNoUmVxEi0KCXRhc2tfaW5mbxgBIAMoCzIaLlBCX1Rhc2tFc3Rp",
            "bWF0ZUV4cFJlcURhdGEi9AEKGVBCX1Rhc2tFc3RpbWF0ZUV4cFJlcURhdGES",
            "EAoIdGFza19pZHgYASABKAMSIQoJdGFza19tb2RlGAIgASgOMg4uUEJfRGlh",
            "bG9nTW9kZRIVCg10YXNrX2R1cmF0aW9uGAMgASgFEisKDWRpYWxvZ19zb3Vy",
            "Y2UYBCABKA4yFC5QQl9EaWFsb2dTb3VyY2VFbnVtEhQKDGNvcnJlY3RfcmF0",
            "ZRgHIAEoAxIlCgpsZXZlbF90eXBlGAggASgOMhEuUEJfTGV2ZWxUeXBlRW51",
            "bRIhCghleHBfdHlwZRgJIAEoDjIPLlBCX0V4cFR5cGVFbnVtIrABCh1TU19H",
            "ZXRUYXNrRXN0aW1hdGVFeHBCYXRjaEFjaxJGCg10YXNrX2V4cF9pbmZvGAEg",
            "AygLMi8uU1NfR2V0VGFza0VzdGltYXRlRXhwQmF0Y2hBY2suVGFza0V4cElu",
            "Zm9FbnRyeRpHChBUYXNrRXhwSW5mb0VudHJ5EgsKA2tleRgBIAEoAxIiCgV2",
            "YWx1ZRgCIAEoCzITLlBCX0VzdGltYXRlRXhwSW5mbzoCOAEiNwoSUEJfRXN0",
            "aW1hdGVFeHBJbmZvEgsKA2V4cBgBIAEoBRIUCgxzdGFtaW5hX2Nvc3QYAiAB",
            "KAUiEwoRQ1NfSm9pblJhbmtpbmdSZXEiIQoRU0NfSm9pblJhbmtpbmdBY2sS",
            "DAoEY29kZRgBIAEoBSIqChdTU19EcmF3UmFua2luZ1Jld2FyZFJlcRIPCgd1",
            "c2VyX2lkGAEgASgDIkkKF1NTX0RyYXdSYW5raW5nUmV3YXJkQWNrEgwKBGNv",
            "ZGUYASABKAUSIAoEZGF0YRgCIAEoCzISLlBCX0RyYXdSZXdhcmREYXRhIi0K",
            "GlNTX0dldFJhbmtpbmdTdGF0aXN0aWNzUmVxEg8KB3VzZXJfaWQYASABKAMi",
            "TwoaU1NfR2V0UmFua2luZ1N0YXRpc3RpY3NBY2sSDAoEY29kZRgBIAEoBRIj",
            "CgRkYXRhGAIgASgLMhUuUEJfUmFua2luZ1N0YXRpc3RpY3MiYAoUUEJfUmFu",
            "a2luZ1N0YXRpc3RpY3MSGAoQdG90YWxfZXhwZXJpZW5jZRgBIAEoBRIVCg1j",
            "dXJyZW50X2xldmVsGAIgASgFEhcKD2lzX2xldmVsX3VubG9jaxgDIAEoCCKg",
            "AQoWU1NfUmFua2luZ0NoYW5nZU1RRGF0YRIPCgd1c2VyX2lkGAEgASgDEioK",
            "C2NoYW5nZV90eXBlGAIgASgOMhUuUEJfUmFua2luZ0NoYW5nZVR5cGUSEQoJ",
            "dG90YWxfZXhwGAMgASgDEhMKC3JhbmtpbmdfZXhwGAQgASgDEg8KB3Jhbmtp",
            "bmcYBSABKAUSEAoIZ3JvdXBfaWQYBiABKAMihAIKF1NTX1JhbmtpbmdTZXR0",
            "bGVtZW50UmVxEhYKDnRhc2tfcmVjb3JkX2lkGAEgASgDEiEKCXRhc2tfbW9k",
            "ZRgCIAEoDjIOLlBCX0RpYWxvZ01vZGUSEAoIc3Rhcl9jbnQYAyABKAUSDQoF",
            "cm91bmQYBCABKAMSEgoKc3RhcnRfdGltZRgFIAEoAxIZChFoaXRfa25vd2xl",
            "ZGdlX2NudBgGIAEoBRIUCgxjb3JyZWN0X3JhdGUYByABKAMSJQoKbGV2ZWxf",
            "dHlwZRgIIAEoDjIRLlBCX0xldmVsVHlwZUVudW0SIQoIZXhwX3R5cGUYCSAB",
            "KA4yDy5QQl9FeHBUeXBlRW51bSJGChdTU19SYW5raW5nU2V0dGxlbWVudEFj",
            "axIMCgRjb2RlGAEgASgFEh0KBGRhdGEYAiABKAsyDy5QQl9SYW5raW5nSW5m",
            "byKCAQoNU1NfUmFua01RRGF0YRIPCgd1c2VyX2lkGAEgASgDEhQKDGRhdGVf",
            "dmVyc2lvbhgCIAEoCRIRCglyZWNvcmRfaWQYAyABKAkSCwoDZXhwGAQgASgF",
            "EioKD3NldHRsZV9leHBfdHlwZRgFIAEoDjIRLlBCX1NldHRsZUV4cFR5cGUi",
            "LgobQ1NfU2V0UmFua2luZ0NoYW5nZUNsaWNrUmVxEg8KB3VzZXJfaWQYASAB",
            "KAMiKwobU0NfU2V0UmFua2luZ0NoYW5nZUNsaWNrQWNrEgwKBGNvZGUYASAB",
            "KAUiVQoSUEJfUmFua2luZ1BhZ2VTaG93Ej8KFWlzX3Nob3dfd2Vla2x5X2No",
            "YW5nZRgBIAEoDjIgLlBCX1JhbmtpbmdQYWdlU2hvd19XZWVrbHlDaGFuZ2Ui",
            "eAoTU1NfU2V0dGxlRXhwSW5mb1JlcRIPCgd1c2VyX2lkGAEgASgJEioKD3Nl",
            "dHRsZV9leHBfdHlwZRgCIAEoDjIRLlBCX1NldHRsZUV4cFR5cGUSEQoJdGlt",
            "ZV9jb3N0GAMgASgFEhEKCWRpYWxvZ19pZBgEIAEoCSJIChNTU19TZXR0bGVF",
            "eHBJbmZvQWNrEgwKBGNvZGUYASABKAUSIwoEZGF0YRgCIAEoCzIVLlNTX1Nl",
            "dHRsZUV4cEluZm9EYXRhIrIBChRTU19TZXR0bGVFeHBJbmZvRGF0YRIUCgxw",
            "cmV2aW91c19leHAYASABKAUSEQoJYWRkZWRfZXhwGAIgASgFEhAKCGJhc2Vf",
            "ZXhwGAMgASgFEhEKCXRvdGFsX2V4cBgEIAEoBRIRCgl0b2RheV9leHAYBSAB",
            "KAUSIgoadG9kYXlfdGltZV9jb3N0X2luX3NlY29uZHMYBiABKAMSFQoNcmVh",
            "Y2hfZXhwX2NhcBgHIAEoCCqwAQoQUEJfUmFua2luZ1N0YXR1cxIaChZQQl9S",
            "QU5LSU5HX1NUQVRVU19OT05FEAASHAoYUEJfUkFOS0lOR19TVEFUVVNfTk9S",
            "TUFMEAESHAoYUEJfUkFOS0lOR19TVEFUVVNfTE9DS0VEEAISIwofUEJfUkFO",
            "S0lOR19TVEFUVVNfTk9UX0FWQUlMQUJMRRADEh8KG1BCX1JBTktJTkdfU1RB",
            "VFVTX0FWQUlMQUJMRRAEKuMBChNQQl9SYW5raW5nTW9ja1NjZW5lEh4KGlBC",
            "X1JBTktJTkdfTU9DS19TQ0VORV9OT05FEAASGwoXUEJfUkFOS0lOR19NT0NL",
            "X1NDRU5FXzEQARIbChdQQl9SQU5LSU5HX01PQ0tfU0NFTkVfMhACEhsKF1BC",
            "X1JBTktJTkdfTU9DS19TQ0VORV8zEAMSGwoXUEJfUkFOS0lOR19NT0NLX1ND",
            "RU5FXzQQBBIbChdQQl9SQU5LSU5HX01PQ0tfU0NFTkVfNRAFEhsKF1BCX1JB",
            "TktJTkdfTU9DS19TQ0VORV82EAYqaQofUEJfUmFua2luZ1BhZ2VTaG93X1dl",
            "ZWtseUNoYW5nZRIWChJXRUVLTFlfQ0hBTkdFX05PTkUQABIWChJXRUVLTFlf",
            "Q0hBTkdFX1NIT1cQARIWChJXRUVLTFlfQ0hBTkdFX0hJREUQAkIuWhx2Zl9w",
            "cm90b2J1Zi9zZXJ2ZXIvaW5jZW50aXZlqgINTXNnLmluY2VudGl2ZWIGcHJv",
            "dG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.basic.DialogReflection.Descriptor, global::IncentiveReflection.Descriptor, global::Msg.basic.CourseReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.incentive.PB_RankingStatus), typeof(global::Msg.incentive.PB_RankingMockScene), typeof(global::Msg.incentive.PB_RankingPageShow_WeeklyChange), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_GetUserRankingSummaryReq), global::Msg.incentive.SS_GetUserRankingSummaryReq.Parser, new[]{ "user_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_GetUserRankingSummaryAck), global::Msg.incentive.SS_GetUserRankingSummaryAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_RankingSummary), global::Msg.incentive.PB_RankingSummary.Parser, new[]{ "ranking_status", "change_type", "current_level", "pre_level", "current_order", "pre_order", "user_exp", "user_ranking_exp" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_GetUserRankingPortalDataReq), global::Msg.incentive.CS_GetUserRankingPortalDataReq.Parser, new[]{ "user_id", "mock_scene" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_GetUserRankingPortalDataAck), global::Msg.incentive.SC_GetUserRankingPortalDataAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_RankingPortalData), global::Msg.incentive.PB_RankingPortalData.Parser, new[]{ "ranking_level_list", "ranking_status", "change_type", "current_level", "pre_level", "left_timestamp", "last_unlock_count", "user_item_list", "current_order", "pre_order", "can_draw_reward", "ranking_page_show" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_RankingLevelInfo), global::Msg.incentive.PB_RankingLevelInfo.Parser, new[]{ "level_id", "level_name" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_RankingOrderInfo), global::Msg.incentive.PB_RankingOrderInfo.Parser, new[]{ "pre_order", "current_order" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_TriggerRankingUpdateByTaskReq), global::Msg.incentive.SS_TriggerRankingUpdateByTaskReq.Parser, new[]{ "task_record_id", "task_mode", "star_cnt", "task_cost", "round", "start_time" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_TriggerRankingUpdateByTaskAck), global::Msg.incentive.SS_TriggerRankingUpdateByTaskAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_GetTaskEstimateExpBatchReq), global::Msg.incentive.SS_GetTaskEstimateExpBatchReq.Parser, new[]{ "task_info" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_TaskEstimateExpReqData), global::Msg.incentive.PB_TaskEstimateExpReqData.Parser, new[]{ "task_idx", "task_mode", "task_duration", "dialog_source", "correct_rate", "level_type", "exp_type" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_GetTaskEstimateExpBatchAck), global::Msg.incentive.SS_GetTaskEstimateExpBatchAck.Parser, new[]{ "task_exp_info" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_EstimateExpInfo), global::Msg.incentive.PB_EstimateExpInfo.Parser, new[]{ "exp", "stamina_cost" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_JoinRankingReq), global::Msg.incentive.CS_JoinRankingReq.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_JoinRankingAck), global::Msg.incentive.SC_JoinRankingAck.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_DrawRankingRewardReq), global::Msg.incentive.SS_DrawRankingRewardReq.Parser, new[]{ "user_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_DrawRankingRewardAck), global::Msg.incentive.SS_DrawRankingRewardAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_GetRankingStatisticsReq), global::Msg.incentive.SS_GetRankingStatisticsReq.Parser, new[]{ "user_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_GetRankingStatisticsAck), global::Msg.incentive.SS_GetRankingStatisticsAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_RankingStatistics), global::Msg.incentive.PB_RankingStatistics.Parser, new[]{ "total_experience", "current_level", "is_level_unlock" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_RankingChangeMQData), global::Msg.incentive.SS_RankingChangeMQData.Parser, new[]{ "user_id", "change_type", "total_exp", "ranking_exp", "ranking", "group_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_RankingSettlementReq), global::Msg.incentive.SS_RankingSettlementReq.Parser, new[]{ "task_record_id", "task_mode", "star_cnt", "round", "start_time", "hit_knowledge_cnt", "correct_rate", "level_type", "exp_type" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_RankingSettlementAck), global::Msg.incentive.SS_RankingSettlementAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_RankMQData), global::Msg.incentive.SS_RankMQData.Parser, new[]{ "user_id", "date_version", "record_id", "exp", "settle_exp_type" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_SetRankingChangeClickReq), global::Msg.incentive.CS_SetRankingChangeClickReq.Parser, new[]{ "user_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_SetRankingChangeClickAck), global::Msg.incentive.SC_SetRankingChangeClickAck.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_RankingPageShow), global::Msg.incentive.PB_RankingPageShow.Parser, new[]{ "is_show_weekly_change" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_SettleExpInfoReq), global::Msg.incentive.SS_SettleExpInfoReq.Parser, new[]{ "user_id", "settle_exp_type", "time_cost", "dialog_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_SettleExpInfoAck), global::Msg.incentive.SS_SettleExpInfoAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_SettleExpInfoData), global::Msg.incentive.SS_SettleExpInfoData.Parser, new[]{ "previous_exp", "added_exp", "base_exp", "total_exp", "today_exp", "today_time_cost_in_seconds", "reach_exp_cap" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum PB_RankingStatus {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("PB_RANKING_STATUS_NONE")] PB_RANKING_STATUS_NONE = 0,
    /// <summary>
    /// 已加入
    /// </summary>
    [pbr::OriginalName("PB_RANKING_STATUS_NORMAL")] PB_RANKING_STATUS_NORMAL = 1,
    /// <summary>
    /// 锁定
    /// </summary>
    [pbr::OriginalName("PB_RANKING_STATUS_LOCKED")] PB_RANKING_STATUS_LOCKED = 2,
    /// <summary>
    /// 已解锁，任务未完成，不可加入
    /// </summary>
    [pbr::OriginalName("PB_RANKING_STATUS_NOT_AVAILABLE")] PB_RANKING_STATUS_NOT_AVAILABLE = 3,
    /// <summary>
    /// 可加入
    /// </summary>
    [pbr::OriginalName("PB_RANKING_STATUS_AVAILABLE")] PB_RANKING_STATUS_AVAILABLE = 4,
  }

  public enum PB_RankingMockScene {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("PB_RANKING_MOCK_SCENE_NONE")] PB_RANKING_MOCK_SCENE_NONE = 0,
    /// <summary>
    /// 常规用户排名，等级没变动
    /// </summary>
    [pbr::OriginalName("PB_RANKING_MOCK_SCENE_1")] PB_RANKING_MOCK_SCENE_1 = 1,
    /// <summary>
    /// 常规用户排名，等级升级
    /// </summary>
    [pbr::OriginalName("PB_RANKING_MOCK_SCENE_2")] PB_RANKING_MOCK_SCENE_2 = 2,
    /// <summary>
    /// 常规用户排名，等级降级
    /// </summary>
    [pbr::OriginalName("PB_RANKING_MOCK_SCENE_3")] PB_RANKING_MOCK_SCENE_3 = 3,
    /// <summary>
    /// 未解锁
    /// </summary>
    [pbr::OriginalName("PB_RANKING_MOCK_SCENE_4")] PB_RANKING_MOCK_SCENE_4 = 4,
    /// <summary>
    /// 已解锁，未完成任务，不可加入
    /// </summary>
    [pbr::OriginalName("PB_RANKING_MOCK_SCENE_5")] PB_RANKING_MOCK_SCENE_5 = 5,
    /// <summary>
    /// 已完成任务，可加入
    /// </summary>
    [pbr::OriginalName("PB_RANKING_MOCK_SCENE_6")] PB_RANKING_MOCK_SCENE_6 = 6,
  }

  public enum PB_RankingPageShow_WeeklyChange {
    [pbr::OriginalName("WEEKLY_CHANGE_NONE")] WEEKLY_CHANGE_NONE = 0,
    [pbr::OriginalName("WEEKLY_CHANGE_SHOW")] WEEKLY_CHANGE_SHOW = 1,
    [pbr::OriginalName("WEEKLY_CHANGE_HIDE")] WEEKLY_CHANGE_HIDE = 2,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserRankingSummaryReq : pb::IMessage<SS_GetUserRankingSummaryReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserRankingSummaryReq> _parser = new pb::MessageParser<SS_GetUserRankingSummaryReq>(() => new SS_GetUserRankingSummaryReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserRankingSummaryReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserRankingSummaryReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserRankingSummaryReq(SS_GetUserRankingSummaryReq other) : this() {
      user_id_ = other.user_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserRankingSummaryReq Clone() {
      return new SS_GetUserRankingSummaryReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserRankingSummaryReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserRankingSummaryReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserRankingSummaryReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserRankingSummaryAck : pb::IMessage<SS_GetUserRankingSummaryAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserRankingSummaryAck> _parser = new pb::MessageParser<SS_GetUserRankingSummaryAck>(() => new SS_GetUserRankingSummaryAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserRankingSummaryAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserRankingSummaryAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserRankingSummaryAck(SS_GetUserRankingSummaryAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserRankingSummaryAck Clone() {
      return new SS_GetUserRankingSummaryAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_RankingSummary data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_RankingSummary data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserRankingSummaryAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserRankingSummaryAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserRankingSummaryAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_RankingSummary();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_RankingSummary();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_RankingSummary();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_RankingSummary : pb::IMessage<PB_RankingSummary>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_RankingSummary> _parser = new pb::MessageParser<PB_RankingSummary>(() => new PB_RankingSummary());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_RankingSummary> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingSummary() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingSummary(PB_RankingSummary other) : this() {
      ranking_status_ = other.ranking_status_;
      change_type_ = other.change_type_;
      current_level_ = other.current_level_ != null ? other.current_level_.Clone() : null;
      pre_level_ = other.pre_level_ != null ? other.pre_level_.Clone() : null;
      current_order_ = other.current_order_;
      pre_order_ = other.pre_order_;
      user_exp_ = other.user_exp_;
      user_ranking_exp_ = other.user_ranking_exp_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingSummary Clone() {
      return new PB_RankingSummary(this);
    }

    /// <summary>Field number for the "ranking_status" field.</summary>
    public const int ranking_statusFieldNumber = 1;
    private global::Msg.incentive.PB_RankingStatus ranking_status_ = global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE;
    /// <summary>
    /// 排行榜状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_RankingStatus ranking_status {
      get { return ranking_status_; }
      set {
        ranking_status_ = value;
      }
    }

    /// <summary>Field number for the "change_type" field.</summary>
    public const int change_typeFieldNumber = 2;
    private global::PB_RankingChangeType change_type_ = global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE;
    /// <summary>
    /// 变化类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_RankingChangeType change_type {
      get { return change_type_; }
      set {
        change_type_ = value;
      }
    }

    /// <summary>Field number for the "current_level" field.</summary>
    public const int current_levelFieldNumber = 3;
    private global::PB_LevelInfo current_level_;
    /// <summary>
    /// 当前等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_LevelInfo current_level {
      get { return current_level_; }
      set {
        current_level_ = value;
      }
    }

    /// <summary>Field number for the "pre_level" field.</summary>
    public const int pre_levelFieldNumber = 4;
    private global::PB_LevelInfo pre_level_;
    /// <summary>
    /// 之前的等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_LevelInfo pre_level {
      get { return pre_level_; }
      set {
        pre_level_ = value;
      }
    }

    /// <summary>Field number for the "current_order" field.</summary>
    public const int current_orderFieldNumber = 5;
    private int current_order_;
    /// <summary>
    /// 当前排名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int current_order {
      get { return current_order_; }
      set {
        current_order_ = value;
      }
    }

    /// <summary>Field number for the "pre_order" field.</summary>
    public const int pre_orderFieldNumber = 6;
    private int pre_order_;
    /// <summary>
    /// 上一次排名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int pre_order {
      get { return pre_order_; }
      set {
        pre_order_ = value;
      }
    }

    /// <summary>Field number for the "user_exp" field.</summary>
    public const int user_expFieldNumber = 7;
    private long user_exp_;
    /// <summary>
    /// 用户总经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_exp {
      get { return user_exp_; }
      set {
        user_exp_ = value;
      }
    }

    /// <summary>Field number for the "user_ranking_exp" field.</summary>
    public const int user_ranking_expFieldNumber = 8;
    private long user_ranking_exp_;
    /// <summary>
    /// 用户排行榜经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_ranking_exp {
      get { return user_ranking_exp_; }
      set {
        user_ranking_exp_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_RankingSummary);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_RankingSummary other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ranking_status != other.ranking_status) return false;
      if (change_type != other.change_type) return false;
      if (!object.Equals(current_level, other.current_level)) return false;
      if (!object.Equals(pre_level, other.pre_level)) return false;
      if (current_order != other.current_order) return false;
      if (pre_order != other.pre_order) return false;
      if (user_exp != other.user_exp) return false;
      if (user_ranking_exp != other.user_ranking_exp) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ranking_status != global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE) hash ^= ranking_status.GetHashCode();
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) hash ^= change_type.GetHashCode();
      if (current_level_ != null) hash ^= current_level.GetHashCode();
      if (pre_level_ != null) hash ^= pre_level.GetHashCode();
      if (current_order != 0) hash ^= current_order.GetHashCode();
      if (pre_order != 0) hash ^= pre_order.GetHashCode();
      if (user_exp != 0L) hash ^= user_exp.GetHashCode();
      if (user_ranking_exp != 0L) hash ^= user_ranking_exp.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ranking_status != global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) ranking_status);
      }
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        output.WriteRawTag(16);
        output.WriteEnum((int) change_type);
      }
      if (current_level_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(current_level);
      }
      if (pre_level_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(pre_level);
      }
      if (current_order != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(current_order);
      }
      if (pre_order != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(pre_order);
      }
      if (user_exp != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(user_exp);
      }
      if (user_ranking_exp != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(user_ranking_exp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ranking_status != global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) ranking_status);
      }
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        output.WriteRawTag(16);
        output.WriteEnum((int) change_type);
      }
      if (current_level_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(current_level);
      }
      if (pre_level_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(pre_level);
      }
      if (current_order != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(current_order);
      }
      if (pre_order != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(pre_order);
      }
      if (user_exp != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(user_exp);
      }
      if (user_ranking_exp != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(user_ranking_exp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ranking_status != global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) ranking_status);
      }
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) change_type);
      }
      if (current_level_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(current_level);
      }
      if (pre_level_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(pre_level);
      }
      if (current_order != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(current_order);
      }
      if (pre_order != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(pre_order);
      }
      if (user_exp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_exp);
      }
      if (user_ranking_exp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_ranking_exp);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_RankingSummary other) {
      if (other == null) {
        return;
      }
      if (other.ranking_status != global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE) {
        ranking_status = other.ranking_status;
      }
      if (other.change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        change_type = other.change_type;
      }
      if (other.current_level_ != null) {
        if (current_level_ == null) {
          current_level = new global::PB_LevelInfo();
        }
        current_level.MergeFrom(other.current_level);
      }
      if (other.pre_level_ != null) {
        if (pre_level_ == null) {
          pre_level = new global::PB_LevelInfo();
        }
        pre_level.MergeFrom(other.pre_level);
      }
      if (other.current_order != 0) {
        current_order = other.current_order;
      }
      if (other.pre_order != 0) {
        pre_order = other.pre_order;
      }
      if (other.user_exp != 0L) {
        user_exp = other.user_exp;
      }
      if (other.user_ranking_exp != 0L) {
        user_ranking_exp = other.user_ranking_exp;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ranking_status = (global::Msg.incentive.PB_RankingStatus) input.ReadEnum();
            break;
          }
          case 16: {
            change_type = (global::PB_RankingChangeType) input.ReadEnum();
            break;
          }
          case 26: {
            if (current_level_ == null) {
              current_level = new global::PB_LevelInfo();
            }
            input.ReadMessage(current_level);
            break;
          }
          case 34: {
            if (pre_level_ == null) {
              pre_level = new global::PB_LevelInfo();
            }
            input.ReadMessage(pre_level);
            break;
          }
          case 40: {
            current_order = input.ReadInt32();
            break;
          }
          case 48: {
            pre_order = input.ReadInt32();
            break;
          }
          case 56: {
            user_exp = input.ReadInt64();
            break;
          }
          case 64: {
            user_ranking_exp = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            ranking_status = (global::Msg.incentive.PB_RankingStatus) input.ReadEnum();
            break;
          }
          case 16: {
            change_type = (global::PB_RankingChangeType) input.ReadEnum();
            break;
          }
          case 26: {
            if (current_level_ == null) {
              current_level = new global::PB_LevelInfo();
            }
            input.ReadMessage(current_level);
            break;
          }
          case 34: {
            if (pre_level_ == null) {
              pre_level = new global::PB_LevelInfo();
            }
            input.ReadMessage(pre_level);
            break;
          }
          case 40: {
            current_order = input.ReadInt32();
            break;
          }
          case 48: {
            pre_order = input.ReadInt32();
            break;
          }
          case 56: {
            user_exp = input.ReadInt64();
            break;
          }
          case 64: {
            user_ranking_exp = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetUserRankingPortalDataReq : pb::IMessage<CS_GetUserRankingPortalDataReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetUserRankingPortalDataReq> _parser = new pb::MessageParser<CS_GetUserRankingPortalDataReq>(() => new CS_GetUserRankingPortalDataReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetUserRankingPortalDataReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserRankingPortalDataReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserRankingPortalDataReq(CS_GetUserRankingPortalDataReq other) : this() {
      user_id_ = other.user_id_;
      mock_scene_ = other.mock_scene_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserRankingPortalDataReq Clone() {
      return new CS_GetUserRankingPortalDataReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "mock_scene" field.</summary>
    public const int mock_sceneFieldNumber = 2;
    private global::Msg.incentive.PB_RankingMockScene mock_scene_ = global::Msg.incentive.PB_RankingMockScene.PB_RANKING_MOCK_SCENE_NONE;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_RankingMockScene mock_scene {
      get { return mock_scene_; }
      set {
        mock_scene_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetUserRankingPortalDataReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetUserRankingPortalDataReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (mock_scene != other.mock_scene) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (mock_scene != global::Msg.incentive.PB_RankingMockScene.PB_RANKING_MOCK_SCENE_NONE) hash ^= mock_scene.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (mock_scene != global::Msg.incentive.PB_RankingMockScene.PB_RANKING_MOCK_SCENE_NONE) {
        output.WriteRawTag(16);
        output.WriteEnum((int) mock_scene);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (mock_scene != global::Msg.incentive.PB_RankingMockScene.PB_RANKING_MOCK_SCENE_NONE) {
        output.WriteRawTag(16);
        output.WriteEnum((int) mock_scene);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (mock_scene != global::Msg.incentive.PB_RankingMockScene.PB_RANKING_MOCK_SCENE_NONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) mock_scene);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetUserRankingPortalDataReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.mock_scene != global::Msg.incentive.PB_RankingMockScene.PB_RANKING_MOCK_SCENE_NONE) {
        mock_scene = other.mock_scene;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            mock_scene = (global::Msg.incentive.PB_RankingMockScene) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            mock_scene = (global::Msg.incentive.PB_RankingMockScene) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetUserRankingPortalDataAck : pb::IMessage<SC_GetUserRankingPortalDataAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetUserRankingPortalDataAck> _parser = new pb::MessageParser<SC_GetUserRankingPortalDataAck>(() => new SC_GetUserRankingPortalDataAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetUserRankingPortalDataAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserRankingPortalDataAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserRankingPortalDataAck(SC_GetUserRankingPortalDataAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserRankingPortalDataAck Clone() {
      return new SC_GetUserRankingPortalDataAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_RankingPortalData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_RankingPortalData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetUserRankingPortalDataAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetUserRankingPortalDataAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetUserRankingPortalDataAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_RankingPortalData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_RankingPortalData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_RankingPortalData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_RankingPortalData : pb::IMessage<PB_RankingPortalData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_RankingPortalData> _parser = new pb::MessageParser<PB_RankingPortalData>(() => new PB_RankingPortalData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_RankingPortalData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingPortalData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingPortalData(PB_RankingPortalData other) : this() {
      ranking_level_list_ = other.ranking_level_list_.Clone();
      ranking_status_ = other.ranking_status_;
      change_type_ = other.change_type_;
      current_level_ = other.current_level_ != null ? other.current_level_.Clone() : null;
      pre_level_ = other.pre_level_ != null ? other.pre_level_.Clone() : null;
      left_timestamp_ = other.left_timestamp_;
      last_unlock_count_ = other.last_unlock_count_;
      user_item_list_ = other.user_item_list_.Clone();
      current_order_ = other.current_order_;
      pre_order_ = other.pre_order_;
      can_draw_reward_ = other.can_draw_reward_;
      ranking_page_show_ = other.ranking_page_show_ != null ? other.ranking_page_show_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingPortalData Clone() {
      return new PB_RankingPortalData(this);
    }

    /// <summary>Field number for the "ranking_level_list" field.</summary>
    public const int ranking_level_listFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.incentive.PB_RankingLevelInfo> _repeated_ranking_level_list_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.incentive.PB_RankingLevelInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.incentive.PB_RankingLevelInfo> ranking_level_list_ = new pbc::RepeatedField<global::Msg.incentive.PB_RankingLevelInfo>();
    /// <summary>
    /// 排行榜等级列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.incentive.PB_RankingLevelInfo> ranking_level_list {
      get { return ranking_level_list_; }
    }

    /// <summary>Field number for the "ranking_status" field.</summary>
    public const int ranking_statusFieldNumber = 2;
    private global::Msg.incentive.PB_RankingStatus ranking_status_ = global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE;
    /// <summary>
    /// 排行榜状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_RankingStatus ranking_status {
      get { return ranking_status_; }
      set {
        ranking_status_ = value;
      }
    }

    /// <summary>Field number for the "change_type" field.</summary>
    public const int change_typeFieldNumber = 3;
    private global::PB_RankingChangeType change_type_ = global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE;
    /// <summary>
    /// 变化类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_RankingChangeType change_type {
      get { return change_type_; }
      set {
        change_type_ = value;
      }
    }

    /// <summary>Field number for the "current_level" field.</summary>
    public const int current_levelFieldNumber = 4;
    private global::PB_LevelInfo current_level_;
    /// <summary>
    /// 当前等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_LevelInfo current_level {
      get { return current_level_; }
      set {
        current_level_ = value;
      }
    }

    /// <summary>Field number for the "pre_level" field.</summary>
    public const int pre_levelFieldNumber = 5;
    private global::PB_LevelInfo pre_level_;
    /// <summary>
    /// 之前的等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_LevelInfo pre_level {
      get { return pre_level_; }
      set {
        pre_level_ = value;
      }
    }

    /// <summary>Field number for the "left_timestamp" field.</summary>
    public const int left_timestampFieldNumber = 6;
    private long left_timestamp_;
    /// <summary>
    ///剩余时间，单位秒
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long left_timestamp {
      get { return left_timestamp_; }
      set {
        left_timestamp_ = value;
      }
    }

    /// <summary>Field number for the "last_unlock_count" field.</summary>
    public const int last_unlock_countFieldNumber = 7;
    private int last_unlock_count_;
    /// <summary>
    ///剩余解锁次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int last_unlock_count {
      get { return last_unlock_count_; }
      set {
        last_unlock_count_ = value;
      }
    }

    /// <summary>Field number for the "user_item_list" field.</summary>
    public const int user_item_listFieldNumber = 8;
    private static readonly pb::FieldCodec<global::PB_UserItem> _repeated_user_item_list_codec
        = pb::FieldCodec.ForMessage(66, global::PB_UserItem.Parser);
    private readonly pbc::RepeatedField<global::PB_UserItem> user_item_list_ = new pbc::RepeatedField<global::PB_UserItem>();
    /// <summary>
    /// 用户列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PB_UserItem> user_item_list {
      get { return user_item_list_; }
    }

    /// <summary>Field number for the "current_order" field.</summary>
    public const int current_orderFieldNumber = 9;
    private int current_order_;
    /// <summary>
    /// 当前排名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int current_order {
      get { return current_order_; }
      set {
        current_order_ = value;
      }
    }

    /// <summary>Field number for the "pre_order" field.</summary>
    public const int pre_orderFieldNumber = 10;
    private int pre_order_;
    /// <summary>
    /// 上一次排名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int pre_order {
      get { return pre_order_; }
      set {
        pre_order_ = value;
      }
    }

    /// <summary>Field number for the "can_draw_reward" field.</summary>
    public const int can_draw_rewardFieldNumber = 11;
    private bool can_draw_reward_;
    /// <summary>
    /// 是否可领取奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool can_draw_reward {
      get { return can_draw_reward_; }
      set {
        can_draw_reward_ = value;
      }
    }

    /// <summary>Field number for the "ranking_page_show" field.</summary>
    public const int ranking_page_showFieldNumber = 12;
    private global::Msg.incentive.PB_RankingPageShow ranking_page_show_;
    /// <summary>
    /// 排行榜页面是否需要展示
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_RankingPageShow ranking_page_show {
      get { return ranking_page_show_; }
      set {
        ranking_page_show_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_RankingPortalData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_RankingPortalData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!ranking_level_list_.Equals(other.ranking_level_list_)) return false;
      if (ranking_status != other.ranking_status) return false;
      if (change_type != other.change_type) return false;
      if (!object.Equals(current_level, other.current_level)) return false;
      if (!object.Equals(pre_level, other.pre_level)) return false;
      if (left_timestamp != other.left_timestamp) return false;
      if (last_unlock_count != other.last_unlock_count) return false;
      if(!user_item_list_.Equals(other.user_item_list_)) return false;
      if (current_order != other.current_order) return false;
      if (pre_order != other.pre_order) return false;
      if (can_draw_reward != other.can_draw_reward) return false;
      if (!object.Equals(ranking_page_show, other.ranking_page_show)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= ranking_level_list_.GetHashCode();
      if (ranking_status != global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE) hash ^= ranking_status.GetHashCode();
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) hash ^= change_type.GetHashCode();
      if (current_level_ != null) hash ^= current_level.GetHashCode();
      if (pre_level_ != null) hash ^= pre_level.GetHashCode();
      if (left_timestamp != 0L) hash ^= left_timestamp.GetHashCode();
      if (last_unlock_count != 0) hash ^= last_unlock_count.GetHashCode();
      hash ^= user_item_list_.GetHashCode();
      if (current_order != 0) hash ^= current_order.GetHashCode();
      if (pre_order != 0) hash ^= pre_order.GetHashCode();
      if (can_draw_reward != false) hash ^= can_draw_reward.GetHashCode();
      if (ranking_page_show_ != null) hash ^= ranking_page_show.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      ranking_level_list_.WriteTo(output, _repeated_ranking_level_list_codec);
      if (ranking_status != global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE) {
        output.WriteRawTag(16);
        output.WriteEnum((int) ranking_status);
      }
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) change_type);
      }
      if (current_level_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(current_level);
      }
      if (pre_level_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(pre_level);
      }
      if (left_timestamp != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(left_timestamp);
      }
      if (last_unlock_count != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(last_unlock_count);
      }
      user_item_list_.WriteTo(output, _repeated_user_item_list_codec);
      if (current_order != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(current_order);
      }
      if (pre_order != 0) {
        output.WriteRawTag(80);
        output.WriteInt32(pre_order);
      }
      if (can_draw_reward != false) {
        output.WriteRawTag(88);
        output.WriteBool(can_draw_reward);
      }
      if (ranking_page_show_ != null) {
        output.WriteRawTag(98);
        output.WriteMessage(ranking_page_show);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      ranking_level_list_.WriteTo(ref output, _repeated_ranking_level_list_codec);
      if (ranking_status != global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE) {
        output.WriteRawTag(16);
        output.WriteEnum((int) ranking_status);
      }
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) change_type);
      }
      if (current_level_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(current_level);
      }
      if (pre_level_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(pre_level);
      }
      if (left_timestamp != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(left_timestamp);
      }
      if (last_unlock_count != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(last_unlock_count);
      }
      user_item_list_.WriteTo(ref output, _repeated_user_item_list_codec);
      if (current_order != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(current_order);
      }
      if (pre_order != 0) {
        output.WriteRawTag(80);
        output.WriteInt32(pre_order);
      }
      if (can_draw_reward != false) {
        output.WriteRawTag(88);
        output.WriteBool(can_draw_reward);
      }
      if (ranking_page_show_ != null) {
        output.WriteRawTag(98);
        output.WriteMessage(ranking_page_show);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += ranking_level_list_.CalculateSize(_repeated_ranking_level_list_codec);
      if (ranking_status != global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) ranking_status);
      }
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) change_type);
      }
      if (current_level_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(current_level);
      }
      if (pre_level_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(pre_level);
      }
      if (left_timestamp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(left_timestamp);
      }
      if (last_unlock_count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(last_unlock_count);
      }
      size += user_item_list_.CalculateSize(_repeated_user_item_list_codec);
      if (current_order != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(current_order);
      }
      if (pre_order != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(pre_order);
      }
      if (can_draw_reward != false) {
        size += 1 + 1;
      }
      if (ranking_page_show_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ranking_page_show);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_RankingPortalData other) {
      if (other == null) {
        return;
      }
      ranking_level_list_.Add(other.ranking_level_list_);
      if (other.ranking_status != global::Msg.incentive.PB_RankingStatus.PB_RANKING_STATUS_NONE) {
        ranking_status = other.ranking_status;
      }
      if (other.change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        change_type = other.change_type;
      }
      if (other.current_level_ != null) {
        if (current_level_ == null) {
          current_level = new global::PB_LevelInfo();
        }
        current_level.MergeFrom(other.current_level);
      }
      if (other.pre_level_ != null) {
        if (pre_level_ == null) {
          pre_level = new global::PB_LevelInfo();
        }
        pre_level.MergeFrom(other.pre_level);
      }
      if (other.left_timestamp != 0L) {
        left_timestamp = other.left_timestamp;
      }
      if (other.last_unlock_count != 0) {
        last_unlock_count = other.last_unlock_count;
      }
      user_item_list_.Add(other.user_item_list_);
      if (other.current_order != 0) {
        current_order = other.current_order;
      }
      if (other.pre_order != 0) {
        pre_order = other.pre_order;
      }
      if (other.can_draw_reward != false) {
        can_draw_reward = other.can_draw_reward;
      }
      if (other.ranking_page_show_ != null) {
        if (ranking_page_show_ == null) {
          ranking_page_show = new global::Msg.incentive.PB_RankingPageShow();
        }
        ranking_page_show.MergeFrom(other.ranking_page_show);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ranking_level_list_.AddEntriesFrom(input, _repeated_ranking_level_list_codec);
            break;
          }
          case 16: {
            ranking_status = (global::Msg.incentive.PB_RankingStatus) input.ReadEnum();
            break;
          }
          case 24: {
            change_type = (global::PB_RankingChangeType) input.ReadEnum();
            break;
          }
          case 34: {
            if (current_level_ == null) {
              current_level = new global::PB_LevelInfo();
            }
            input.ReadMessage(current_level);
            break;
          }
          case 42: {
            if (pre_level_ == null) {
              pre_level = new global::PB_LevelInfo();
            }
            input.ReadMessage(pre_level);
            break;
          }
          case 48: {
            left_timestamp = input.ReadInt64();
            break;
          }
          case 56: {
            last_unlock_count = input.ReadInt32();
            break;
          }
          case 66: {
            user_item_list_.AddEntriesFrom(input, _repeated_user_item_list_codec);
            break;
          }
          case 72: {
            current_order = input.ReadInt32();
            break;
          }
          case 80: {
            pre_order = input.ReadInt32();
            break;
          }
          case 88: {
            can_draw_reward = input.ReadBool();
            break;
          }
          case 98: {
            if (ranking_page_show_ == null) {
              ranking_page_show = new global::Msg.incentive.PB_RankingPageShow();
            }
            input.ReadMessage(ranking_page_show);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ranking_level_list_.AddEntriesFrom(ref input, _repeated_ranking_level_list_codec);
            break;
          }
          case 16: {
            ranking_status = (global::Msg.incentive.PB_RankingStatus) input.ReadEnum();
            break;
          }
          case 24: {
            change_type = (global::PB_RankingChangeType) input.ReadEnum();
            break;
          }
          case 34: {
            if (current_level_ == null) {
              current_level = new global::PB_LevelInfo();
            }
            input.ReadMessage(current_level);
            break;
          }
          case 42: {
            if (pre_level_ == null) {
              pre_level = new global::PB_LevelInfo();
            }
            input.ReadMessage(pre_level);
            break;
          }
          case 48: {
            left_timestamp = input.ReadInt64();
            break;
          }
          case 56: {
            last_unlock_count = input.ReadInt32();
            break;
          }
          case 66: {
            user_item_list_.AddEntriesFrom(ref input, _repeated_user_item_list_codec);
            break;
          }
          case 72: {
            current_order = input.ReadInt32();
            break;
          }
          case 80: {
            pre_order = input.ReadInt32();
            break;
          }
          case 88: {
            can_draw_reward = input.ReadBool();
            break;
          }
          case 98: {
            if (ranking_page_show_ == null) {
              ranking_page_show = new global::Msg.incentive.PB_RankingPageShow();
            }
            input.ReadMessage(ranking_page_show);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_RankingLevelInfo : pb::IMessage<PB_RankingLevelInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_RankingLevelInfo> _parser = new pb::MessageParser<PB_RankingLevelInfo>(() => new PB_RankingLevelInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_RankingLevelInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingLevelInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingLevelInfo(PB_RankingLevelInfo other) : this() {
      level_id_ = other.level_id_;
      level_name_ = other.level_name_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingLevelInfo Clone() {
      return new PB_RankingLevelInfo(this);
    }

    /// <summary>Field number for the "level_id" field.</summary>
    public const int level_idFieldNumber = 1;
    private int level_id_;
    /// <summary>
    /// 等级ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int level_id {
      get { return level_id_; }
      set {
        level_id_ = value;
      }
    }

    /// <summary>Field number for the "level_name" field.</summary>
    public const int level_nameFieldNumber = 2;
    private string level_name_ = "";
    /// <summary>
    /// 等级名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string level_name {
      get { return level_name_; }
      set {
        level_name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_RankingLevelInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_RankingLevelInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (level_id != other.level_id) return false;
      if (level_name != other.level_name) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (level_id != 0) hash ^= level_id.GetHashCode();
      if (level_name.Length != 0) hash ^= level_name.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (level_id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(level_id);
      }
      if (level_name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(level_name);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (level_id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(level_id);
      }
      if (level_name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(level_name);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (level_id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(level_id);
      }
      if (level_name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(level_name);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_RankingLevelInfo other) {
      if (other == null) {
        return;
      }
      if (other.level_id != 0) {
        level_id = other.level_id;
      }
      if (other.level_name.Length != 0) {
        level_name = other.level_name;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            level_id = input.ReadInt32();
            break;
          }
          case 18: {
            level_name = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            level_id = input.ReadInt32();
            break;
          }
          case 18: {
            level_name = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_RankingOrderInfo : pb::IMessage<PB_RankingOrderInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_RankingOrderInfo> _parser = new pb::MessageParser<PB_RankingOrderInfo>(() => new PB_RankingOrderInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_RankingOrderInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingOrderInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingOrderInfo(PB_RankingOrderInfo other) : this() {
      pre_order_ = other.pre_order_;
      current_order_ = other.current_order_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingOrderInfo Clone() {
      return new PB_RankingOrderInfo(this);
    }

    /// <summary>Field number for the "pre_order" field.</summary>
    public const int pre_orderFieldNumber = 1;
    private long pre_order_;
    /// <summary>
    /// 前一个排名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long pre_order {
      get { return pre_order_; }
      set {
        pre_order_ = value;
      }
    }

    /// <summary>Field number for the "current_order" field.</summary>
    public const int current_orderFieldNumber = 2;
    private long current_order_;
    /// <summary>
    /// 当前排名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long current_order {
      get { return current_order_; }
      set {
        current_order_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_RankingOrderInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_RankingOrderInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (pre_order != other.pre_order) return false;
      if (current_order != other.current_order) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (pre_order != 0L) hash ^= pre_order.GetHashCode();
      if (current_order != 0L) hash ^= current_order.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (pre_order != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(pre_order);
      }
      if (current_order != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(current_order);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (pre_order != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(pre_order);
      }
      if (current_order != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(current_order);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (pre_order != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(pre_order);
      }
      if (current_order != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(current_order);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_RankingOrderInfo other) {
      if (other == null) {
        return;
      }
      if (other.pre_order != 0L) {
        pre_order = other.pre_order;
      }
      if (other.current_order != 0L) {
        current_order = other.current_order;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            pre_order = input.ReadInt64();
            break;
          }
          case 16: {
            current_order = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            pre_order = input.ReadInt64();
            break;
          }
          case 16: {
            current_order = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_TriggerRankingUpdateByTaskReq : pb::IMessage<SS_TriggerRankingUpdateByTaskReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_TriggerRankingUpdateByTaskReq> _parser = new pb::MessageParser<SS_TriggerRankingUpdateByTaskReq>(() => new SS_TriggerRankingUpdateByTaskReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_TriggerRankingUpdateByTaskReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_TriggerRankingUpdateByTaskReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_TriggerRankingUpdateByTaskReq(SS_TriggerRankingUpdateByTaskReq other) : this() {
      task_record_id_ = other.task_record_id_;
      task_mode_ = other.task_mode_;
      star_cnt_ = other.star_cnt_;
      task_cost_ = other.task_cost_;
      round_ = other.round_;
      start_time_ = other.start_time_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_TriggerRankingUpdateByTaskReq Clone() {
      return new SS_TriggerRankingUpdateByTaskReq(this);
    }

    /// <summary>Field number for the "task_record_id" field.</summary>
    public const int task_record_idFieldNumber = 1;
    private long task_record_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long task_record_id {
      get { return task_record_id_; }
      set {
        task_record_id_ = value;
      }
    }

    /// <summary>Field number for the "task_mode" field.</summary>
    public const int task_modeFieldNumber = 2;
    private global::Msg.basic.PB_DialogMode task_mode_ = global::Msg.basic.PB_DialogMode.MNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogMode task_mode {
      get { return task_mode_; }
      set {
        task_mode_ = value;
      }
    }

    /// <summary>Field number for the "star_cnt" field.</summary>
    public const int star_cntFieldNumber = 3;
    private int star_cnt_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int star_cnt {
      get { return star_cnt_; }
      set {
        star_cnt_ = value;
      }
    }

    /// <summary>Field number for the "task_cost" field.</summary>
    public const int task_costFieldNumber = 4;
    private long task_cost_;
    /// <summary>
    /// 任务耗时（单位：秒）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long task_cost {
      get { return task_cost_; }
      set {
        task_cost_ = value;
      }
    }

    /// <summary>Field number for the "round" field.</summary>
    public const int roundFieldNumber = 5;
    private long round_;
    /// <summary>
    /// 自由对话轮数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long round {
      get { return round_; }
      set {
        round_ = value;
      }
    }

    /// <summary>Field number for the "start_time" field.</summary>
    public const int start_timeFieldNumber = 6;
    private long start_time_;
    /// <summary>
    ///对话开始时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long start_time {
      get { return start_time_; }
      set {
        start_time_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_TriggerRankingUpdateByTaskReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_TriggerRankingUpdateByTaskReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (task_record_id != other.task_record_id) return false;
      if (task_mode != other.task_mode) return false;
      if (star_cnt != other.star_cnt) return false;
      if (task_cost != other.task_cost) return false;
      if (round != other.round) return false;
      if (start_time != other.start_time) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (task_record_id != 0L) hash ^= task_record_id.GetHashCode();
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) hash ^= task_mode.GetHashCode();
      if (star_cnt != 0) hash ^= star_cnt.GetHashCode();
      if (task_cost != 0L) hash ^= task_cost.GetHashCode();
      if (round != 0L) hash ^= round.GetHashCode();
      if (start_time != 0L) hash ^= start_time.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (task_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(task_record_id);
      }
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) task_mode);
      }
      if (star_cnt != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(star_cnt);
      }
      if (task_cost != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(task_cost);
      }
      if (round != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(round);
      }
      if (start_time != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(start_time);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (task_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(task_record_id);
      }
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) task_mode);
      }
      if (star_cnt != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(star_cnt);
      }
      if (task_cost != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(task_cost);
      }
      if (round != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(round);
      }
      if (start_time != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(start_time);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (task_record_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(task_record_id);
      }
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) task_mode);
      }
      if (star_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(star_cnt);
      }
      if (task_cost != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(task_cost);
      }
      if (round != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(round);
      }
      if (start_time != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(start_time);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_TriggerRankingUpdateByTaskReq other) {
      if (other == null) {
        return;
      }
      if (other.task_record_id != 0L) {
        task_record_id = other.task_record_id;
      }
      if (other.task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        task_mode = other.task_mode;
      }
      if (other.star_cnt != 0) {
        star_cnt = other.star_cnt;
      }
      if (other.task_cost != 0L) {
        task_cost = other.task_cost;
      }
      if (other.round != 0L) {
        round = other.round;
      }
      if (other.start_time != 0L) {
        start_time = other.start_time;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            task_record_id = input.ReadInt64();
            break;
          }
          case 16: {
            task_mode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 24: {
            star_cnt = input.ReadInt32();
            break;
          }
          case 32: {
            task_cost = input.ReadInt64();
            break;
          }
          case 40: {
            round = input.ReadInt64();
            break;
          }
          case 48: {
            start_time = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            task_record_id = input.ReadInt64();
            break;
          }
          case 16: {
            task_mode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 24: {
            star_cnt = input.ReadInt32();
            break;
          }
          case 32: {
            task_cost = input.ReadInt64();
            break;
          }
          case 40: {
            round = input.ReadInt64();
            break;
          }
          case 48: {
            start_time = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_TriggerRankingUpdateByTaskAck : pb::IMessage<SS_TriggerRankingUpdateByTaskAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_TriggerRankingUpdateByTaskAck> _parser = new pb::MessageParser<SS_TriggerRankingUpdateByTaskAck>(() => new SS_TriggerRankingUpdateByTaskAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_TriggerRankingUpdateByTaskAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_TriggerRankingUpdateByTaskAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_TriggerRankingUpdateByTaskAck(SS_TriggerRankingUpdateByTaskAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_TriggerRankingUpdateByTaskAck Clone() {
      return new SS_TriggerRankingUpdateByTaskAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::PB_RankingInfo data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_RankingInfo data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_TriggerRankingUpdateByTaskAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_TriggerRankingUpdateByTaskAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_TriggerRankingUpdateByTaskAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::PB_RankingInfo();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::PB_RankingInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::PB_RankingInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetTaskEstimateExpBatchReq : pb::IMessage<SS_GetTaskEstimateExpBatchReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetTaskEstimateExpBatchReq> _parser = new pb::MessageParser<SS_GetTaskEstimateExpBatchReq>(() => new SS_GetTaskEstimateExpBatchReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetTaskEstimateExpBatchReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetTaskEstimateExpBatchReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetTaskEstimateExpBatchReq(SS_GetTaskEstimateExpBatchReq other) : this() {
      task_info_ = other.task_info_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetTaskEstimateExpBatchReq Clone() {
      return new SS_GetTaskEstimateExpBatchReq(this);
    }

    /// <summary>Field number for the "task_info" field.</summary>
    public const int task_infoFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.incentive.PB_TaskEstimateExpReqData> _repeated_task_info_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.incentive.PB_TaskEstimateExpReqData.Parser);
    private readonly pbc::RepeatedField<global::Msg.incentive.PB_TaskEstimateExpReqData> task_info_ = new pbc::RepeatedField<global::Msg.incentive.PB_TaskEstimateExpReqData>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.incentive.PB_TaskEstimateExpReqData> task_info {
      get { return task_info_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetTaskEstimateExpBatchReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetTaskEstimateExpBatchReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!task_info_.Equals(other.task_info_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= task_info_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      task_info_.WriteTo(output, _repeated_task_info_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      task_info_.WriteTo(ref output, _repeated_task_info_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += task_info_.CalculateSize(_repeated_task_info_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetTaskEstimateExpBatchReq other) {
      if (other == null) {
        return;
      }
      task_info_.Add(other.task_info_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            task_info_.AddEntriesFrom(input, _repeated_task_info_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            task_info_.AddEntriesFrom(ref input, _repeated_task_info_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_TaskEstimateExpReqData : pb::IMessage<PB_TaskEstimateExpReqData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_TaskEstimateExpReqData> _parser = new pb::MessageParser<PB_TaskEstimateExpReqData>(() => new PB_TaskEstimateExpReqData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_TaskEstimateExpReqData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_TaskEstimateExpReqData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_TaskEstimateExpReqData(PB_TaskEstimateExpReqData other) : this() {
      task_idx_ = other.task_idx_;
      task_mode_ = other.task_mode_;
      task_duration_ = other.task_duration_;
      dialog_source_ = other.dialog_source_;
      correct_rate_ = other.correct_rate_;
      level_type_ = other.level_type_;
      exp_type_ = other.exp_type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_TaskEstimateExpReqData Clone() {
      return new PB_TaskEstimateExpReqData(this);
    }

    /// <summary>Field number for the "task_idx" field.</summary>
    public const int task_idxFieldNumber = 1;
    private long task_idx_;
    /// <summary>
    /// 任务下标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long task_idx {
      get { return task_idx_; }
      set {
        task_idx_ = value;
      }
    }

    /// <summary>Field number for the "task_mode" field.</summary>
    public const int task_modeFieldNumber = 2;
    private global::Msg.basic.PB_DialogMode task_mode_ = global::Msg.basic.PB_DialogMode.MNone;
    /// <summary>
    /// 任务模式
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogMode task_mode {
      get { return task_mode_; }
      set {
        task_mode_ = value;
      }
    }

    /// <summary>Field number for the "task_duration" field.</summary>
    public const int task_durationFieldNumber = 3;
    private int task_duration_;
    /// <summary>
    /// 任务平均时长
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int task_duration {
      get { return task_duration_; }
      set {
        task_duration_ = value;
      }
    }

    /// <summary>Field number for the "dialog_source" field.</summary>
    public const int dialog_sourceFieldNumber = 4;
    private global::Msg.basic.PB_DialogSourceEnum dialog_source_ = global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone;
    /// <summary>
    /// 来源入口（比如碎片化练习有补充体力入口和常规入口）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogSourceEnum dialog_source {
      get { return dialog_source_; }
      set {
        dialog_source_ = value;
      }
    }

    /// <summary>Field number for the "correct_rate" field.</summary>
    public const int correct_rateFieldNumber = 7;
    private long correct_rate_;
    /// <summary>
    /// 正确率
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long correct_rate {
      get { return correct_rate_; }
      set {
        correct_rate_ = value;
      }
    }

    /// <summary>Field number for the "level_type" field.</summary>
    public const int level_typeFieldNumber = 8;
    private global::Msg.basic.PB_LevelTypeEnum level_type_ = global::Msg.basic.PB_LevelTypeEnum.LTNone;
    /// <summary>
    /// 等级类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_LevelTypeEnum level_type {
      get { return level_type_; }
      set {
        level_type_ = value;
      }
    }

    /// <summary>Field number for the "exp_type" field.</summary>
    public const int exp_typeFieldNumber = 9;
    private global::Msg.basic.PB_ExpTypeEnum exp_type_ = global::Msg.basic.PB_ExpTypeEnum.ETNone;
    /// <summary>
    /// 经验类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_ExpTypeEnum exp_type {
      get { return exp_type_; }
      set {
        exp_type_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_TaskEstimateExpReqData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_TaskEstimateExpReqData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (task_idx != other.task_idx) return false;
      if (task_mode != other.task_mode) return false;
      if (task_duration != other.task_duration) return false;
      if (dialog_source != other.dialog_source) return false;
      if (correct_rate != other.correct_rate) return false;
      if (level_type != other.level_type) return false;
      if (exp_type != other.exp_type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (task_idx != 0L) hash ^= task_idx.GetHashCode();
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) hash ^= task_mode.GetHashCode();
      if (task_duration != 0) hash ^= task_duration.GetHashCode();
      if (dialog_source != global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone) hash ^= dialog_source.GetHashCode();
      if (correct_rate != 0L) hash ^= correct_rate.GetHashCode();
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) hash ^= level_type.GetHashCode();
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) hash ^= exp_type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (task_idx != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(task_idx);
      }
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) task_mode);
      }
      if (task_duration != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(task_duration);
      }
      if (dialog_source != global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone) {
        output.WriteRawTag(32);
        output.WriteEnum((int) dialog_source);
      }
      if (correct_rate != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(correct_rate);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(64);
        output.WriteEnum((int) level_type);
      }
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        output.WriteRawTag(72);
        output.WriteEnum((int) exp_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (task_idx != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(task_idx);
      }
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) task_mode);
      }
      if (task_duration != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(task_duration);
      }
      if (dialog_source != global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone) {
        output.WriteRawTag(32);
        output.WriteEnum((int) dialog_source);
      }
      if (correct_rate != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(correct_rate);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(64);
        output.WriteEnum((int) level_type);
      }
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        output.WriteRawTag(72);
        output.WriteEnum((int) exp_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (task_idx != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(task_idx);
      }
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) task_mode);
      }
      if (task_duration != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(task_duration);
      }
      if (dialog_source != global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) dialog_source);
      }
      if (correct_rate != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(correct_rate);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) level_type);
      }
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) exp_type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_TaskEstimateExpReqData other) {
      if (other == null) {
        return;
      }
      if (other.task_idx != 0L) {
        task_idx = other.task_idx;
      }
      if (other.task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        task_mode = other.task_mode;
      }
      if (other.task_duration != 0) {
        task_duration = other.task_duration;
      }
      if (other.dialog_source != global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone) {
        dialog_source = other.dialog_source;
      }
      if (other.correct_rate != 0L) {
        correct_rate = other.correct_rate;
      }
      if (other.level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        level_type = other.level_type;
      }
      if (other.exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        exp_type = other.exp_type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            task_idx = input.ReadInt64();
            break;
          }
          case 16: {
            task_mode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 24: {
            task_duration = input.ReadInt32();
            break;
          }
          case 32: {
            dialog_source = (global::Msg.basic.PB_DialogSourceEnum) input.ReadEnum();
            break;
          }
          case 56: {
            correct_rate = input.ReadInt64();
            break;
          }
          case 64: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 72: {
            exp_type = (global::Msg.basic.PB_ExpTypeEnum) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            task_idx = input.ReadInt64();
            break;
          }
          case 16: {
            task_mode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 24: {
            task_duration = input.ReadInt32();
            break;
          }
          case 32: {
            dialog_source = (global::Msg.basic.PB_DialogSourceEnum) input.ReadEnum();
            break;
          }
          case 56: {
            correct_rate = input.ReadInt64();
            break;
          }
          case 64: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 72: {
            exp_type = (global::Msg.basic.PB_ExpTypeEnum) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetTaskEstimateExpBatchAck : pb::IMessage<SS_GetTaskEstimateExpBatchAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetTaskEstimateExpBatchAck> _parser = new pb::MessageParser<SS_GetTaskEstimateExpBatchAck>(() => new SS_GetTaskEstimateExpBatchAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetTaskEstimateExpBatchAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetTaskEstimateExpBatchAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetTaskEstimateExpBatchAck(SS_GetTaskEstimateExpBatchAck other) : this() {
      task_exp_info_ = other.task_exp_info_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetTaskEstimateExpBatchAck Clone() {
      return new SS_GetTaskEstimateExpBatchAck(this);
    }

    /// <summary>Field number for the "task_exp_info" field.</summary>
    public const int task_exp_infoFieldNumber = 1;
    private static readonly pbc::MapField<long, global::Msg.incentive.PB_EstimateExpInfo>.Codec _map_task_exp_info_codec
        = new pbc::MapField<long, global::Msg.incentive.PB_EstimateExpInfo>.Codec(pb::FieldCodec.ForInt64(8, 0L), pb::FieldCodec.ForMessage(18, global::Msg.incentive.PB_EstimateExpInfo.Parser), 10);
    private readonly pbc::MapField<long, global::Msg.incentive.PB_EstimateExpInfo> task_exp_info_ = new pbc::MapField<long, global::Msg.incentive.PB_EstimateExpInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<long, global::Msg.incentive.PB_EstimateExpInfo> task_exp_info {
      get { return task_exp_info_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetTaskEstimateExpBatchAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetTaskEstimateExpBatchAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!task_exp_info.Equals(other.task_exp_info)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= task_exp_info.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      task_exp_info_.WriteTo(output, _map_task_exp_info_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      task_exp_info_.WriteTo(ref output, _map_task_exp_info_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += task_exp_info_.CalculateSize(_map_task_exp_info_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetTaskEstimateExpBatchAck other) {
      if (other == null) {
        return;
      }
      task_exp_info_.MergeFrom(other.task_exp_info_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            task_exp_info_.AddEntriesFrom(input, _map_task_exp_info_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            task_exp_info_.AddEntriesFrom(ref input, _map_task_exp_info_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_EstimateExpInfo : pb::IMessage<PB_EstimateExpInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_EstimateExpInfo> _parser = new pb::MessageParser<PB_EstimateExpInfo>(() => new PB_EstimateExpInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_EstimateExpInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EstimateExpInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EstimateExpInfo(PB_EstimateExpInfo other) : this() {
      exp_ = other.exp_;
      stamina_cost_ = other.stamina_cost_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EstimateExpInfo Clone() {
      return new PB_EstimateExpInfo(this);
    }

    /// <summary>Field number for the "exp" field.</summary>
    public const int expFieldNumber = 1;
    private int exp_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int exp {
      get { return exp_; }
      set {
        exp_ = value;
      }
    }

    /// <summary>Field number for the "stamina_cost" field.</summary>
    public const int stamina_costFieldNumber = 2;
    private int stamina_cost_;
    /// <summary>
    /// 体力消耗值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int stamina_cost {
      get { return stamina_cost_; }
      set {
        stamina_cost_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_EstimateExpInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_EstimateExpInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (exp != other.exp) return false;
      if (stamina_cost != other.stamina_cost) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (exp != 0) hash ^= exp.GetHashCode();
      if (stamina_cost != 0) hash ^= stamina_cost.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (exp != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(exp);
      }
      if (stamina_cost != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(stamina_cost);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (exp != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(exp);
      }
      if (stamina_cost != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(stamina_cost);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(exp);
      }
      if (stamina_cost != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(stamina_cost);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_EstimateExpInfo other) {
      if (other == null) {
        return;
      }
      if (other.exp != 0) {
        exp = other.exp;
      }
      if (other.stamina_cost != 0) {
        stamina_cost = other.stamina_cost;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            exp = input.ReadInt32();
            break;
          }
          case 16: {
            stamina_cost = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            exp = input.ReadInt32();
            break;
          }
          case 16: {
            stamina_cost = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_JoinRankingReq : pb::IMessage<CS_JoinRankingReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_JoinRankingReq> _parser = new pb::MessageParser<CS_JoinRankingReq>(() => new CS_JoinRankingReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_JoinRankingReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_JoinRankingReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_JoinRankingReq(CS_JoinRankingReq other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_JoinRankingReq Clone() {
      return new CS_JoinRankingReq(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_JoinRankingReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_JoinRankingReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_JoinRankingReq other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_JoinRankingAck : pb::IMessage<SC_JoinRankingAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_JoinRankingAck> _parser = new pb::MessageParser<SC_JoinRankingAck>(() => new SC_JoinRankingAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_JoinRankingAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_JoinRankingAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_JoinRankingAck(SC_JoinRankingAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_JoinRankingAck Clone() {
      return new SC_JoinRankingAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_JoinRankingAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_JoinRankingAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_JoinRankingAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_DrawRankingRewardReq : pb::IMessage<SS_DrawRankingRewardReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_DrawRankingRewardReq> _parser = new pb::MessageParser<SS_DrawRankingRewardReq>(() => new SS_DrawRankingRewardReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_DrawRankingRewardReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_DrawRankingRewardReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_DrawRankingRewardReq(SS_DrawRankingRewardReq other) : this() {
      user_id_ = other.user_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_DrawRankingRewardReq Clone() {
      return new SS_DrawRankingRewardReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_DrawRankingRewardReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_DrawRankingRewardReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_DrawRankingRewardReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_DrawRankingRewardAck : pb::IMessage<SS_DrawRankingRewardAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_DrawRankingRewardAck> _parser = new pb::MessageParser<SS_DrawRankingRewardAck>(() => new SS_DrawRankingRewardAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_DrawRankingRewardAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_DrawRankingRewardAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_DrawRankingRewardAck(SS_DrawRankingRewardAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_DrawRankingRewardAck Clone() {
      return new SS_DrawRankingRewardAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::PB_DrawRewardData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_DrawRewardData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_DrawRankingRewardAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_DrawRankingRewardAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_DrawRankingRewardAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::PB_DrawRewardData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::PB_DrawRewardData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::PB_DrawRewardData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 排行榜统计数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetRankingStatisticsReq : pb::IMessage<SS_GetRankingStatisticsReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetRankingStatisticsReq> _parser = new pb::MessageParser<SS_GetRankingStatisticsReq>(() => new SS_GetRankingStatisticsReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetRankingStatisticsReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetRankingStatisticsReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetRankingStatisticsReq(SS_GetRankingStatisticsReq other) : this() {
      user_id_ = other.user_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetRankingStatisticsReq Clone() {
      return new SS_GetRankingStatisticsReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetRankingStatisticsReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetRankingStatisticsReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetRankingStatisticsReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetRankingStatisticsAck : pb::IMessage<SS_GetRankingStatisticsAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetRankingStatisticsAck> _parser = new pb::MessageParser<SS_GetRankingStatisticsAck>(() => new SS_GetRankingStatisticsAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetRankingStatisticsAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetRankingStatisticsAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetRankingStatisticsAck(SS_GetRankingStatisticsAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetRankingStatisticsAck Clone() {
      return new SS_GetRankingStatisticsAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_RankingStatistics data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_RankingStatistics data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetRankingStatisticsAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetRankingStatisticsAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetRankingStatisticsAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_RankingStatistics();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_RankingStatistics();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_RankingStatistics();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_RankingStatistics : pb::IMessage<PB_RankingStatistics>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_RankingStatistics> _parser = new pb::MessageParser<PB_RankingStatistics>(() => new PB_RankingStatistics());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_RankingStatistics> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingStatistics() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingStatistics(PB_RankingStatistics other) : this() {
      total_experience_ = other.total_experience_;
      current_level_ = other.current_level_;
      is_level_unlock_ = other.is_level_unlock_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingStatistics Clone() {
      return new PB_RankingStatistics(this);
    }

    /// <summary>Field number for the "total_experience" field.</summary>
    public const int total_experienceFieldNumber = 1;
    private int total_experience_;
    /// <summary>
    /// 总经验
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int total_experience {
      get { return total_experience_; }
      set {
        total_experience_ = value;
      }
    }

    /// <summary>Field number for the "current_level" field.</summary>
    public const int current_levelFieldNumber = 2;
    private int current_level_;
    /// <summary>
    /// 当前段位
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int current_level {
      get { return current_level_; }
      set {
        current_level_ = value;
      }
    }

    /// <summary>Field number for the "is_level_unlock" field.</summary>
    public const int is_level_unlockFieldNumber = 3;
    private bool is_level_unlock_;
    /// <summary>
    /// 段位是否解锁
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_level_unlock {
      get { return is_level_unlock_; }
      set {
        is_level_unlock_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_RankingStatistics);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_RankingStatistics other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (total_experience != other.total_experience) return false;
      if (current_level != other.current_level) return false;
      if (is_level_unlock != other.is_level_unlock) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (total_experience != 0) hash ^= total_experience.GetHashCode();
      if (current_level != 0) hash ^= current_level.GetHashCode();
      if (is_level_unlock != false) hash ^= is_level_unlock.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (total_experience != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(total_experience);
      }
      if (current_level != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(current_level);
      }
      if (is_level_unlock != false) {
        output.WriteRawTag(24);
        output.WriteBool(is_level_unlock);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (total_experience != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(total_experience);
      }
      if (current_level != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(current_level);
      }
      if (is_level_unlock != false) {
        output.WriteRawTag(24);
        output.WriteBool(is_level_unlock);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (total_experience != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(total_experience);
      }
      if (current_level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(current_level);
      }
      if (is_level_unlock != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_RankingStatistics other) {
      if (other == null) {
        return;
      }
      if (other.total_experience != 0) {
        total_experience = other.total_experience;
      }
      if (other.current_level != 0) {
        current_level = other.current_level;
      }
      if (other.is_level_unlock != false) {
        is_level_unlock = other.is_level_unlock;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            total_experience = input.ReadInt32();
            break;
          }
          case 16: {
            current_level = input.ReadInt32();
            break;
          }
          case 24: {
            is_level_unlock = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            total_experience = input.ReadInt32();
            break;
          }
          case 16: {
            current_level = input.ReadInt32();
            break;
          }
          case 24: {
            is_level_unlock = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_RankingChangeMQData : pb::IMessage<SS_RankingChangeMQData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_RankingChangeMQData> _parser = new pb::MessageParser<SS_RankingChangeMQData>(() => new SS_RankingChangeMQData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_RankingChangeMQData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankingChangeMQData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankingChangeMQData(SS_RankingChangeMQData other) : this() {
      user_id_ = other.user_id_;
      change_type_ = other.change_type_;
      total_exp_ = other.total_exp_;
      ranking_exp_ = other.ranking_exp_;
      ranking_ = other.ranking_;
      group_id_ = other.group_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankingChangeMQData Clone() {
      return new SS_RankingChangeMQData(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    /// <summary>
    ///用户 ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "change_type" field.</summary>
    public const int change_typeFieldNumber = 2;
    private global::PB_RankingChangeType change_type_ = global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE;
    /// <summary>
    ///所在区域
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_RankingChangeType change_type {
      get { return change_type_; }
      set {
        change_type_ = value;
      }
    }

    /// <summary>Field number for the "total_exp" field.</summary>
    public const int total_expFieldNumber = 3;
    private long total_exp_;
    /// <summary>
    ///总经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long total_exp {
      get { return total_exp_; }
      set {
        total_exp_ = value;
      }
    }

    /// <summary>Field number for the "ranking_exp" field.</summary>
    public const int ranking_expFieldNumber = 4;
    private long ranking_exp_;
    /// <summary>
    /// 周排行经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long ranking_exp {
      get { return ranking_exp_; }
      set {
        ranking_exp_ = value;
      }
    }

    /// <summary>Field number for the "ranking" field.</summary>
    public const int rankingFieldNumber = 5;
    private int ranking_;
    /// <summary>
    ///排名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ranking {
      get { return ranking_; }
      set {
        ranking_ = value;
      }
    }

    /// <summary>Field number for the "group_id" field.</summary>
    public const int group_idFieldNumber = 6;
    private long group_id_;
    /// <summary>
    ///组 ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long group_id {
      get { return group_id_; }
      set {
        group_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_RankingChangeMQData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_RankingChangeMQData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (change_type != other.change_type) return false;
      if (total_exp != other.total_exp) return false;
      if (ranking_exp != other.ranking_exp) return false;
      if (ranking != other.ranking) return false;
      if (group_id != other.group_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) hash ^= change_type.GetHashCode();
      if (total_exp != 0L) hash ^= total_exp.GetHashCode();
      if (ranking_exp != 0L) hash ^= ranking_exp.GetHashCode();
      if (ranking != 0) hash ^= ranking.GetHashCode();
      if (group_id != 0L) hash ^= group_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        output.WriteRawTag(16);
        output.WriteEnum((int) change_type);
      }
      if (total_exp != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(total_exp);
      }
      if (ranking_exp != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(ranking_exp);
      }
      if (ranking != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(ranking);
      }
      if (group_id != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(group_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        output.WriteRawTag(16);
        output.WriteEnum((int) change_type);
      }
      if (total_exp != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(total_exp);
      }
      if (ranking_exp != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(ranking_exp);
      }
      if (ranking != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(ranking);
      }
      if (group_id != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(group_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) change_type);
      }
      if (total_exp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(total_exp);
      }
      if (ranking_exp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(ranking_exp);
      }
      if (ranking != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ranking);
      }
      if (group_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(group_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_RankingChangeMQData other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.change_type != global::PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_NONE) {
        change_type = other.change_type;
      }
      if (other.total_exp != 0L) {
        total_exp = other.total_exp;
      }
      if (other.ranking_exp != 0L) {
        ranking_exp = other.ranking_exp;
      }
      if (other.ranking != 0) {
        ranking = other.ranking;
      }
      if (other.group_id != 0L) {
        group_id = other.group_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            change_type = (global::PB_RankingChangeType) input.ReadEnum();
            break;
          }
          case 24: {
            total_exp = input.ReadInt64();
            break;
          }
          case 32: {
            ranking_exp = input.ReadInt64();
            break;
          }
          case 40: {
            ranking = input.ReadInt32();
            break;
          }
          case 48: {
            group_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            change_type = (global::PB_RankingChangeType) input.ReadEnum();
            break;
          }
          case 24: {
            total_exp = input.ReadInt64();
            break;
          }
          case 32: {
            ranking_exp = input.ReadInt64();
            break;
          }
          case 40: {
            ranking = input.ReadInt32();
            break;
          }
          case 48: {
            group_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 排行榜模块结算页
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_RankingSettlementReq : pb::IMessage<SS_RankingSettlementReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_RankingSettlementReq> _parser = new pb::MessageParser<SS_RankingSettlementReq>(() => new SS_RankingSettlementReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_RankingSettlementReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankingSettlementReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankingSettlementReq(SS_RankingSettlementReq other) : this() {
      task_record_id_ = other.task_record_id_;
      task_mode_ = other.task_mode_;
      star_cnt_ = other.star_cnt_;
      round_ = other.round_;
      start_time_ = other.start_time_;
      hit_knowledge_cnt_ = other.hit_knowledge_cnt_;
      correct_rate_ = other.correct_rate_;
      level_type_ = other.level_type_;
      exp_type_ = other.exp_type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankingSettlementReq Clone() {
      return new SS_RankingSettlementReq(this);
    }

    /// <summary>Field number for the "task_record_id" field.</summary>
    public const int task_record_idFieldNumber = 1;
    private long task_record_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long task_record_id {
      get { return task_record_id_; }
      set {
        task_record_id_ = value;
      }
    }

    /// <summary>Field number for the "task_mode" field.</summary>
    public const int task_modeFieldNumber = 2;
    private global::Msg.basic.PB_DialogMode task_mode_ = global::Msg.basic.PB_DialogMode.MNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogMode task_mode {
      get { return task_mode_; }
      set {
        task_mode_ = value;
      }
    }

    /// <summary>Field number for the "star_cnt" field.</summary>
    public const int star_cntFieldNumber = 3;
    private int star_cnt_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int star_cnt {
      get { return star_cnt_; }
      set {
        star_cnt_ = value;
      }
    }

    /// <summary>Field number for the "round" field.</summary>
    public const int roundFieldNumber = 4;
    private long round_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long round {
      get { return round_; }
      set {
        round_ = value;
      }
    }

    /// <summary>Field number for the "start_time" field.</summary>
    public const int start_timeFieldNumber = 5;
    private long start_time_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long start_time {
      get { return start_time_; }
      set {
        start_time_ = value;
      }
    }

    /// <summary>Field number for the "hit_knowledge_cnt" field.</summary>
    public const int hit_knowledge_cntFieldNumber = 6;
    private int hit_knowledge_cnt_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int hit_knowledge_cnt {
      get { return hit_knowledge_cnt_; }
      set {
        hit_knowledge_cnt_ = value;
      }
    }

    /// <summary>Field number for the "correct_rate" field.</summary>
    public const int correct_rateFieldNumber = 7;
    private long correct_rate_;
    /// <summary>
    /// 正确率
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long correct_rate {
      get { return correct_rate_; }
      set {
        correct_rate_ = value;
      }
    }

    /// <summary>Field number for the "level_type" field.</summary>
    public const int level_typeFieldNumber = 8;
    private global::Msg.basic.PB_LevelTypeEnum level_type_ = global::Msg.basic.PB_LevelTypeEnum.LTNone;
    /// <summary>
    /// 等级类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_LevelTypeEnum level_type {
      get { return level_type_; }
      set {
        level_type_ = value;
      }
    }

    /// <summary>Field number for the "exp_type" field.</summary>
    public const int exp_typeFieldNumber = 9;
    private global::Msg.basic.PB_ExpTypeEnum exp_type_ = global::Msg.basic.PB_ExpTypeEnum.ETNone;
    /// <summary>
    /// 经验类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_ExpTypeEnum exp_type {
      get { return exp_type_; }
      set {
        exp_type_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_RankingSettlementReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_RankingSettlementReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (task_record_id != other.task_record_id) return false;
      if (task_mode != other.task_mode) return false;
      if (star_cnt != other.star_cnt) return false;
      if (round != other.round) return false;
      if (start_time != other.start_time) return false;
      if (hit_knowledge_cnt != other.hit_knowledge_cnt) return false;
      if (correct_rate != other.correct_rate) return false;
      if (level_type != other.level_type) return false;
      if (exp_type != other.exp_type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (task_record_id != 0L) hash ^= task_record_id.GetHashCode();
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) hash ^= task_mode.GetHashCode();
      if (star_cnt != 0) hash ^= star_cnt.GetHashCode();
      if (round != 0L) hash ^= round.GetHashCode();
      if (start_time != 0L) hash ^= start_time.GetHashCode();
      if (hit_knowledge_cnt != 0) hash ^= hit_knowledge_cnt.GetHashCode();
      if (correct_rate != 0L) hash ^= correct_rate.GetHashCode();
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) hash ^= level_type.GetHashCode();
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) hash ^= exp_type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (task_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(task_record_id);
      }
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) task_mode);
      }
      if (star_cnt != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(star_cnt);
      }
      if (round != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(round);
      }
      if (start_time != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(start_time);
      }
      if (hit_knowledge_cnt != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(hit_knowledge_cnt);
      }
      if (correct_rate != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(correct_rate);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(64);
        output.WriteEnum((int) level_type);
      }
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        output.WriteRawTag(72);
        output.WriteEnum((int) exp_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (task_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(task_record_id);
      }
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) task_mode);
      }
      if (star_cnt != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(star_cnt);
      }
      if (round != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(round);
      }
      if (start_time != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(start_time);
      }
      if (hit_knowledge_cnt != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(hit_knowledge_cnt);
      }
      if (correct_rate != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(correct_rate);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(64);
        output.WriteEnum((int) level_type);
      }
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        output.WriteRawTag(72);
        output.WriteEnum((int) exp_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (task_record_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(task_record_id);
      }
      if (task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) task_mode);
      }
      if (star_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(star_cnt);
      }
      if (round != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(round);
      }
      if (start_time != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(start_time);
      }
      if (hit_knowledge_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(hit_knowledge_cnt);
      }
      if (correct_rate != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(correct_rate);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) level_type);
      }
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) exp_type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_RankingSettlementReq other) {
      if (other == null) {
        return;
      }
      if (other.task_record_id != 0L) {
        task_record_id = other.task_record_id;
      }
      if (other.task_mode != global::Msg.basic.PB_DialogMode.MNone) {
        task_mode = other.task_mode;
      }
      if (other.star_cnt != 0) {
        star_cnt = other.star_cnt;
      }
      if (other.round != 0L) {
        round = other.round;
      }
      if (other.start_time != 0L) {
        start_time = other.start_time;
      }
      if (other.hit_knowledge_cnt != 0) {
        hit_knowledge_cnt = other.hit_knowledge_cnt;
      }
      if (other.correct_rate != 0L) {
        correct_rate = other.correct_rate;
      }
      if (other.level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        level_type = other.level_type;
      }
      if (other.exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        exp_type = other.exp_type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            task_record_id = input.ReadInt64();
            break;
          }
          case 16: {
            task_mode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 24: {
            star_cnt = input.ReadInt32();
            break;
          }
          case 32: {
            round = input.ReadInt64();
            break;
          }
          case 40: {
            start_time = input.ReadInt64();
            break;
          }
          case 48: {
            hit_knowledge_cnt = input.ReadInt32();
            break;
          }
          case 56: {
            correct_rate = input.ReadInt64();
            break;
          }
          case 64: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 72: {
            exp_type = (global::Msg.basic.PB_ExpTypeEnum) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            task_record_id = input.ReadInt64();
            break;
          }
          case 16: {
            task_mode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 24: {
            star_cnt = input.ReadInt32();
            break;
          }
          case 32: {
            round = input.ReadInt64();
            break;
          }
          case 40: {
            start_time = input.ReadInt64();
            break;
          }
          case 48: {
            hit_knowledge_cnt = input.ReadInt32();
            break;
          }
          case 56: {
            correct_rate = input.ReadInt64();
            break;
          }
          case 64: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 72: {
            exp_type = (global::Msg.basic.PB_ExpTypeEnum) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_RankingSettlementAck : pb::IMessage<SS_RankingSettlementAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_RankingSettlementAck> _parser = new pb::MessageParser<SS_RankingSettlementAck>(() => new SS_RankingSettlementAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_RankingSettlementAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[23]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankingSettlementAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankingSettlementAck(SS_RankingSettlementAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankingSettlementAck Clone() {
      return new SS_RankingSettlementAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::PB_RankingInfo data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_RankingInfo data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_RankingSettlementAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_RankingSettlementAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_RankingSettlementAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::PB_RankingInfo();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::PB_RankingInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::PB_RankingInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_RankMQData : pb::IMessage<SS_RankMQData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_RankMQData> _parser = new pb::MessageParser<SS_RankMQData>(() => new SS_RankMQData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_RankMQData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[24]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankMQData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankMQData(SS_RankMQData other) : this() {
      user_id_ = other.user_id_;
      date_version_ = other.date_version_;
      record_id_ = other.record_id_;
      exp_ = other.exp_;
      settle_exp_type_ = other.settle_exp_type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_RankMQData Clone() {
      return new SS_RankMQData(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "date_version" field.</summary>
    public const int date_versionFieldNumber = 2;
    private string date_version_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string date_version {
      get { return date_version_; }
      set {
        date_version_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "record_id" field.</summary>
    public const int record_idFieldNumber = 3;
    private string record_id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string record_id {
      get { return record_id_; }
      set {
        record_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "exp" field.</summary>
    public const int expFieldNumber = 4;
    private int exp_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int exp {
      get { return exp_; }
      set {
        exp_ = value;
      }
    }

    /// <summary>Field number for the "settle_exp_type" field.</summary>
    public const int settle_exp_typeFieldNumber = 5;
    private global::PB_SettleExpType settle_exp_type_ = global::PB_SettleExpType.TNONE;
    /// <summary>
    /// 结算类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_SettleExpType settle_exp_type {
      get { return settle_exp_type_; }
      set {
        settle_exp_type_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_RankMQData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_RankMQData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (date_version != other.date_version) return false;
      if (record_id != other.record_id) return false;
      if (exp != other.exp) return false;
      if (settle_exp_type != other.settle_exp_type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (date_version.Length != 0) hash ^= date_version.GetHashCode();
      if (record_id.Length != 0) hash ^= record_id.GetHashCode();
      if (exp != 0) hash ^= exp.GetHashCode();
      if (settle_exp_type != global::PB_SettleExpType.TNONE) hash ^= settle_exp_type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (date_version.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(date_version);
      }
      if (record_id.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(record_id);
      }
      if (exp != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(exp);
      }
      if (settle_exp_type != global::PB_SettleExpType.TNONE) {
        output.WriteRawTag(40);
        output.WriteEnum((int) settle_exp_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (date_version.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(date_version);
      }
      if (record_id.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(record_id);
      }
      if (exp != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(exp);
      }
      if (settle_exp_type != global::PB_SettleExpType.TNONE) {
        output.WriteRawTag(40);
        output.WriteEnum((int) settle_exp_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (date_version.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(date_version);
      }
      if (record_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(record_id);
      }
      if (exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(exp);
      }
      if (settle_exp_type != global::PB_SettleExpType.TNONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) settle_exp_type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_RankMQData other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.date_version.Length != 0) {
        date_version = other.date_version;
      }
      if (other.record_id.Length != 0) {
        record_id = other.record_id;
      }
      if (other.exp != 0) {
        exp = other.exp;
      }
      if (other.settle_exp_type != global::PB_SettleExpType.TNONE) {
        settle_exp_type = other.settle_exp_type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            date_version = input.ReadString();
            break;
          }
          case 26: {
            record_id = input.ReadString();
            break;
          }
          case 32: {
            exp = input.ReadInt32();
            break;
          }
          case 40: {
            settle_exp_type = (global::PB_SettleExpType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            date_version = input.ReadString();
            break;
          }
          case 26: {
            record_id = input.ReadString();
            break;
          }
          case 32: {
            exp = input.ReadInt32();
            break;
          }
          case 40: {
            settle_exp_type = (global::PB_SettleExpType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_SetRankingChangeClickReq : pb::IMessage<CS_SetRankingChangeClickReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_SetRankingChangeClickReq> _parser = new pb::MessageParser<CS_SetRankingChangeClickReq>(() => new CS_SetRankingChangeClickReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_SetRankingChangeClickReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[25]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetRankingChangeClickReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetRankingChangeClickReq(CS_SetRankingChangeClickReq other) : this() {
      user_id_ = other.user_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetRankingChangeClickReq Clone() {
      return new CS_SetRankingChangeClickReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_SetRankingChangeClickReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_SetRankingChangeClickReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_SetRankingChangeClickReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_SetRankingChangeClickAck : pb::IMessage<SC_SetRankingChangeClickAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_SetRankingChangeClickAck> _parser = new pb::MessageParser<SC_SetRankingChangeClickAck>(() => new SC_SetRankingChangeClickAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_SetRankingChangeClickAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[26]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetRankingChangeClickAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetRankingChangeClickAck(SC_SetRankingChangeClickAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetRankingChangeClickAck Clone() {
      return new SC_SetRankingChangeClickAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_SetRankingChangeClickAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_SetRankingChangeClickAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_SetRankingChangeClickAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_RankingPageShow : pb::IMessage<PB_RankingPageShow>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_RankingPageShow> _parser = new pb::MessageParser<PB_RankingPageShow>(() => new PB_RankingPageShow());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_RankingPageShow> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[27]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingPageShow() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingPageShow(PB_RankingPageShow other) : this() {
      is_show_weekly_change_ = other.is_show_weekly_change_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankingPageShow Clone() {
      return new PB_RankingPageShow(this);
    }

    /// <summary>Field number for the "is_show_weekly_change" field.</summary>
    public const int is_show_weekly_changeFieldNumber = 1;
    private global::Msg.incentive.PB_RankingPageShow_WeeklyChange is_show_weekly_change_ = global::Msg.incentive.PB_RankingPageShow_WeeklyChange.WEEKLY_CHANGE_NONE;
    /// <summary>
    /// 是否需要展示每周升降级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_RankingPageShow_WeeklyChange is_show_weekly_change {
      get { return is_show_weekly_change_; }
      set {
        is_show_weekly_change_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_RankingPageShow);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_RankingPageShow other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (is_show_weekly_change != other.is_show_weekly_change) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (is_show_weekly_change != global::Msg.incentive.PB_RankingPageShow_WeeklyChange.WEEKLY_CHANGE_NONE) hash ^= is_show_weekly_change.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (is_show_weekly_change != global::Msg.incentive.PB_RankingPageShow_WeeklyChange.WEEKLY_CHANGE_NONE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) is_show_weekly_change);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (is_show_weekly_change != global::Msg.incentive.PB_RankingPageShow_WeeklyChange.WEEKLY_CHANGE_NONE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) is_show_weekly_change);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (is_show_weekly_change != global::Msg.incentive.PB_RankingPageShow_WeeklyChange.WEEKLY_CHANGE_NONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) is_show_weekly_change);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_RankingPageShow other) {
      if (other == null) {
        return;
      }
      if (other.is_show_weekly_change != global::Msg.incentive.PB_RankingPageShow_WeeklyChange.WEEKLY_CHANGE_NONE) {
        is_show_weekly_change = other.is_show_weekly_change;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            is_show_weekly_change = (global::Msg.incentive.PB_RankingPageShow_WeeklyChange) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            is_show_weekly_change = (global::Msg.incentive.PB_RankingPageShow_WeeklyChange) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SettleExpInfoReq : pb::IMessage<SS_SettleExpInfoReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SettleExpInfoReq> _parser = new pb::MessageParser<SS_SettleExpInfoReq>(() => new SS_SettleExpInfoReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SettleExpInfoReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[28]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SettleExpInfoReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SettleExpInfoReq(SS_SettleExpInfoReq other) : this() {
      user_id_ = other.user_id_;
      settle_exp_type_ = other.settle_exp_type_;
      time_cost_ = other.time_cost_;
      dialog_id_ = other.dialog_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SettleExpInfoReq Clone() {
      return new SS_SettleExpInfoReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private string user_id_ = "";
    /// <summary>
    /// 用户 ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string user_id {
      get { return user_id_; }
      set {
        user_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "settle_exp_type" field.</summary>
    public const int settle_exp_typeFieldNumber = 2;
    private global::PB_SettleExpType settle_exp_type_ = global::PB_SettleExpType.TNONE;
    /// <summary>
    /// 结算类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_SettleExpType settle_exp_type {
      get { return settle_exp_type_; }
      set {
        settle_exp_type_ = value;
      }
    }

    /// <summary>Field number for the "time_cost" field.</summary>
    public const int time_costFieldNumber = 3;
    private int time_cost_;
    /// <summary>
    /// 耗时
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int time_cost {
      get { return time_cost_; }
      set {
        time_cost_ = value;
      }
    }

    /// <summary>Field number for the "dialog_id" field.</summary>
    public const int dialog_idFieldNumber = 4;
    private string dialog_id_ = "";
    /// <summary>
    /// 会话ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string dialog_id {
      get { return dialog_id_; }
      set {
        dialog_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SettleExpInfoReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SettleExpInfoReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (settle_exp_type != other.settle_exp_type) return false;
      if (time_cost != other.time_cost) return false;
      if (dialog_id != other.dialog_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id.Length != 0) hash ^= user_id.GetHashCode();
      if (settle_exp_type != global::PB_SettleExpType.TNONE) hash ^= settle_exp_type.GetHashCode();
      if (time_cost != 0) hash ^= time_cost.GetHashCode();
      if (dialog_id.Length != 0) hash ^= dialog_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(user_id);
      }
      if (settle_exp_type != global::PB_SettleExpType.TNONE) {
        output.WriteRawTag(16);
        output.WriteEnum((int) settle_exp_type);
      }
      if (time_cost != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(time_cost);
      }
      if (dialog_id.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(dialog_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(user_id);
      }
      if (settle_exp_type != global::PB_SettleExpType.TNONE) {
        output.WriteRawTag(16);
        output.WriteEnum((int) settle_exp_type);
      }
      if (time_cost != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(time_cost);
      }
      if (dialog_id.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(dialog_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(user_id);
      }
      if (settle_exp_type != global::PB_SettleExpType.TNONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) settle_exp_type);
      }
      if (time_cost != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(time_cost);
      }
      if (dialog_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(dialog_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SettleExpInfoReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id.Length != 0) {
        user_id = other.user_id;
      }
      if (other.settle_exp_type != global::PB_SettleExpType.TNONE) {
        settle_exp_type = other.settle_exp_type;
      }
      if (other.time_cost != 0) {
        time_cost = other.time_cost;
      }
      if (other.dialog_id.Length != 0) {
        dialog_id = other.dialog_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            user_id = input.ReadString();
            break;
          }
          case 16: {
            settle_exp_type = (global::PB_SettleExpType) input.ReadEnum();
            break;
          }
          case 24: {
            time_cost = input.ReadInt32();
            break;
          }
          case 34: {
            dialog_id = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            user_id = input.ReadString();
            break;
          }
          case 16: {
            settle_exp_type = (global::PB_SettleExpType) input.ReadEnum();
            break;
          }
          case 24: {
            time_cost = input.ReadInt32();
            break;
          }
          case 34: {
            dialog_id = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SettleExpInfoAck : pb::IMessage<SS_SettleExpInfoAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SettleExpInfoAck> _parser = new pb::MessageParser<SS_SettleExpInfoAck>(() => new SS_SettleExpInfoAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SettleExpInfoAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[29]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SettleExpInfoAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SettleExpInfoAck(SS_SettleExpInfoAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SettleExpInfoAck Clone() {
      return new SS_SettleExpInfoAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.SS_SettleExpInfoData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.SS_SettleExpInfoData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SettleExpInfoAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SettleExpInfoAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SettleExpInfoAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.SS_SettleExpInfoData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.SS_SettleExpInfoData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.SS_SettleExpInfoData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SettleExpInfoData : pb::IMessage<SS_SettleExpInfoData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SettleExpInfoData> _parser = new pb::MessageParser<SS_SettleExpInfoData>(() => new SS_SettleExpInfoData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SettleExpInfoData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.RankingReflection.Descriptor.MessageTypes[30]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SettleExpInfoData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SettleExpInfoData(SS_SettleExpInfoData other) : this() {
      previous_exp_ = other.previous_exp_;
      added_exp_ = other.added_exp_;
      base_exp_ = other.base_exp_;
      total_exp_ = other.total_exp_;
      today_exp_ = other.today_exp_;
      today_time_cost_in_seconds_ = other.today_time_cost_in_seconds_;
      reach_exp_cap_ = other.reach_exp_cap_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SettleExpInfoData Clone() {
      return new SS_SettleExpInfoData(this);
    }

    /// <summary>Field number for the "previous_exp" field.</summary>
    public const int previous_expFieldNumber = 1;
    private int previous_exp_;
    /// <summary>
    /// 之前经验
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int previous_exp {
      get { return previous_exp_; }
      set {
        previous_exp_ = value;
      }
    }

    /// <summary>Field number for the "added_exp" field.</summary>
    public const int added_expFieldNumber = 2;
    private int added_exp_;
    /// <summary>
    /// 增加经验
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int added_exp {
      get { return added_exp_; }
      set {
        added_exp_ = value;
      }
    }

    /// <summary>Field number for the "base_exp" field.</summary>
    public const int base_expFieldNumber = 3;
    private int base_exp_;
    /// <summary>
    /// 基础经验
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int base_exp {
      get { return base_exp_; }
      set {
        base_exp_ = value;
      }
    }

    /// <summary>Field number for the "total_exp" field.</summary>
    public const int total_expFieldNumber = 4;
    private int total_exp_;
    /// <summary>
    /// 总经验
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int total_exp {
      get { return total_exp_; }
      set {
        total_exp_ = value;
      }
    }

    /// <summary>Field number for the "today_exp" field.</summary>
    public const int today_expFieldNumber = 5;
    private int today_exp_;
    /// <summary>
    /// 今天累计经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int today_exp {
      get { return today_exp_; }
      set {
        today_exp_ = value;
      }
    }

    /// <summary>Field number for the "today_time_cost_in_seconds" field.</summary>
    public const int today_time_cost_in_secondsFieldNumber = 6;
    private long today_time_cost_in_seconds_;
    /// <summary>
    /// 今天累计时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long today_time_cost_in_seconds {
      get { return today_time_cost_in_seconds_; }
      set {
        today_time_cost_in_seconds_ = value;
      }
    }

    /// <summary>Field number for the "reach_exp_cap" field.</summary>
    public const int reach_exp_capFieldNumber = 7;
    private bool reach_exp_cap_;
    /// <summary>
    /// 是否达到经验上限
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool reach_exp_cap {
      get { return reach_exp_cap_; }
      set {
        reach_exp_cap_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SettleExpInfoData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SettleExpInfoData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (previous_exp != other.previous_exp) return false;
      if (added_exp != other.added_exp) return false;
      if (base_exp != other.base_exp) return false;
      if (total_exp != other.total_exp) return false;
      if (today_exp != other.today_exp) return false;
      if (today_time_cost_in_seconds != other.today_time_cost_in_seconds) return false;
      if (reach_exp_cap != other.reach_exp_cap) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (previous_exp != 0) hash ^= previous_exp.GetHashCode();
      if (added_exp != 0) hash ^= added_exp.GetHashCode();
      if (base_exp != 0) hash ^= base_exp.GetHashCode();
      if (total_exp != 0) hash ^= total_exp.GetHashCode();
      if (today_exp != 0) hash ^= today_exp.GetHashCode();
      if (today_time_cost_in_seconds != 0L) hash ^= today_time_cost_in_seconds.GetHashCode();
      if (reach_exp_cap != false) hash ^= reach_exp_cap.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (previous_exp != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(previous_exp);
      }
      if (added_exp != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(added_exp);
      }
      if (base_exp != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(base_exp);
      }
      if (total_exp != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(total_exp);
      }
      if (today_exp != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(today_exp);
      }
      if (today_time_cost_in_seconds != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(today_time_cost_in_seconds);
      }
      if (reach_exp_cap != false) {
        output.WriteRawTag(56);
        output.WriteBool(reach_exp_cap);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (previous_exp != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(previous_exp);
      }
      if (added_exp != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(added_exp);
      }
      if (base_exp != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(base_exp);
      }
      if (total_exp != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(total_exp);
      }
      if (today_exp != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(today_exp);
      }
      if (today_time_cost_in_seconds != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(today_time_cost_in_seconds);
      }
      if (reach_exp_cap != false) {
        output.WriteRawTag(56);
        output.WriteBool(reach_exp_cap);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (previous_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(previous_exp);
      }
      if (added_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(added_exp);
      }
      if (base_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(base_exp);
      }
      if (total_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(total_exp);
      }
      if (today_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(today_exp);
      }
      if (today_time_cost_in_seconds != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(today_time_cost_in_seconds);
      }
      if (reach_exp_cap != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SettleExpInfoData other) {
      if (other == null) {
        return;
      }
      if (other.previous_exp != 0) {
        previous_exp = other.previous_exp;
      }
      if (other.added_exp != 0) {
        added_exp = other.added_exp;
      }
      if (other.base_exp != 0) {
        base_exp = other.base_exp;
      }
      if (other.total_exp != 0) {
        total_exp = other.total_exp;
      }
      if (other.today_exp != 0) {
        today_exp = other.today_exp;
      }
      if (other.today_time_cost_in_seconds != 0L) {
        today_time_cost_in_seconds = other.today_time_cost_in_seconds;
      }
      if (other.reach_exp_cap != false) {
        reach_exp_cap = other.reach_exp_cap;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            previous_exp = input.ReadInt32();
            break;
          }
          case 16: {
            added_exp = input.ReadInt32();
            break;
          }
          case 24: {
            base_exp = input.ReadInt32();
            break;
          }
          case 32: {
            total_exp = input.ReadInt32();
            break;
          }
          case 40: {
            today_exp = input.ReadInt32();
            break;
          }
          case 48: {
            today_time_cost_in_seconds = input.ReadInt64();
            break;
          }
          case 56: {
            reach_exp_cap = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            previous_exp = input.ReadInt32();
            break;
          }
          case 16: {
            added_exp = input.ReadInt32();
            break;
          }
          case 24: {
            base_exp = input.ReadInt32();
            break;
          }
          case 32: {
            total_exp = input.ReadInt32();
            break;
          }
          case 40: {
            today_exp = input.ReadInt32();
            break;
          }
          case 48: {
            today_time_cost_in_seconds = input.ReadInt64();
            break;
          }
          case 56: {
            reach_exp_cap = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Shop
{
    public partial class ShopContent : UIBindT
    {
        public override string pkgName => "Shop";
        public override string comName => "ShopContent";

        public Controller State;
        public Controller FreeIsLifetime;
        public GImage shop_flow_free_plan;
        public GImage shop_flow_subscribe_plan;
        public GRichTextField lifetimeTxt2;
        public GGroup lifetimeNode;
        public GTextField tfSubscribeDesc1;
        public GTextField tfSubscribeDesc1_lifetime;
        public GRichTextField tfSubscribeDesc5;
        public GRichTextField tfSubscribeDesc4;
        public GRichTextField tfSubscribeDesc2;
        public GGraph yearLineImg;
        public GButton btnSubscribe;
        public GTextField tfBtnSubscribe;
        public GRichTextField tfMore;
        public GGraph btnMore;
        public GGroup grpBtnMore;
        public GRichTextField tfSubscribeDesc3;
        public GGroup grpSubscribe;
        public GRichTextField lifetimeTxt1;
        public GGroup lifetimeNode2;
        public GTextField tfSubscribeDesc10;
        public GRichTextField tfSubscribeDesc9;
        public GRichTextField tfSubscribeDesc8;
        public GButton btnFreePlan;
        public GTextField tfBtnFreePlan;
        public GTextField tfSubscribeDesc10_lifetime;
        public GGroup grpFreePlan;
        public GTextField tfCurPlan;
        public GRichTextField tfSubscribeDesc6;
        public GTextField tfSubscribeEndtime;
        public GGroup grpPremium;
        public GTextField tfMyItemsTitle;
        public GList listMyItems;
        public GGroup grpItemList;
        public ShopItemListCom comShopList;
        public GTextField tfDiamondDesc;
        public GTextField tfDiamond;
        public GGroup grpShopList;
        public GTextField tfCurPlan2;
        public GRichTextField tfSubscribeDesc13;
        public GTextField tfSubscribeEndtime2;
        public GRichTextField tfSubscribeDesc12;
        public GLoader3D spBtnUpgrade;
        public GButton btnUpgrade;
        public GTextField tfBtnUpgrade;
        public GGroup grpUpgrade;
        public GTextField tfSubscribeDesc14;
        public GRichTextField tfSubscribeDesc15;
        public GButton btnUpdate;
        public GTextField tfBtnUpdate;
        public GGroup grpUpdate;
        public GRichTextField tfSubscribeDesc6_lifetime;
        public GRichTextField lifetimeTxt3;
        public GGroup lifetimeNode1;
        public GGroup grpLifetimePremium;
        public GGroup grpContent;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            State = com.GetControllerAt(0);
            FreeIsLifetime = com.GetControllerAt(1);
            shop_flow_free_plan = (GImage)com.GetChildAt(1);
            shop_flow_subscribe_plan = (GImage)com.GetChildAt(2);
            lifetimeTxt2 = (GRichTextField)com.GetChildAt(5);
            lifetimeNode = (GGroup)com.GetChildAt(6);
            tfSubscribeDesc1 = (GTextField)com.GetChildAt(7);
            tfSubscribeDesc1_lifetime = (GTextField)com.GetChildAt(8);
            tfSubscribeDesc5 = (GRichTextField)com.GetChildAt(9);
            tfSubscribeDesc4 = (GRichTextField)com.GetChildAt(10);
            tfSubscribeDesc2 = (GRichTextField)com.GetChildAt(11);
            yearLineImg = (GGraph)com.GetChildAt(12);
            btnSubscribe = (GButton)com.GetChildAt(13);
            tfBtnSubscribe = (GTextField)com.GetChildAt(15);
            tfMore = (GRichTextField)com.GetChildAt(17);
            btnMore = (GGraph)com.GetChildAt(19);
            grpBtnMore = (GGroup)com.GetChildAt(20);
            tfSubscribeDesc3 = (GRichTextField)com.GetChildAt(21);
            grpSubscribe = (GGroup)com.GetChildAt(22);
            lifetimeTxt1 = (GRichTextField)com.GetChildAt(24);
            lifetimeNode2 = (GGroup)com.GetChildAt(25);
            tfSubscribeDesc10 = (GTextField)com.GetChildAt(26);
            tfSubscribeDesc9 = (GRichTextField)com.GetChildAt(27);
            tfSubscribeDesc8 = (GRichTextField)com.GetChildAt(28);
            btnFreePlan = (GButton)com.GetChildAt(29);
            tfBtnFreePlan = (GTextField)com.GetChildAt(30);
            tfSubscribeDesc10_lifetime = (GTextField)com.GetChildAt(31);
            grpFreePlan = (GGroup)com.GetChildAt(32);
            tfCurPlan = (GTextField)com.GetChildAt(33);
            tfSubscribeDesc6 = (GRichTextField)com.GetChildAt(34);
            tfSubscribeEndtime = (GTextField)com.GetChildAt(35);
            grpPremium = (GGroup)com.GetChildAt(36);
            tfMyItemsTitle = (GTextField)com.GetChildAt(37);
            listMyItems = (GList)com.GetChildAt(38);
            grpItemList = (GGroup)com.GetChildAt(39);
            comShopList = new ShopItemListCom();
            comShopList.Construct(com.GetChildAt(40).asCom);
            tfDiamondDesc = (GTextField)com.GetChildAt(41);
            tfDiamond = (GTextField)com.GetChildAt(42);
            grpShopList = (GGroup)com.GetChildAt(43);
            tfCurPlan2 = (GTextField)com.GetChildAt(44);
            tfSubscribeDesc13 = (GRichTextField)com.GetChildAt(45);
            tfSubscribeEndtime2 = (GTextField)com.GetChildAt(46);
            tfSubscribeDesc12 = (GRichTextField)com.GetChildAt(47);
            spBtnUpgrade = (GLoader3D)com.GetChildAt(48);
            btnUpgrade = (GButton)com.GetChildAt(49);
            tfBtnUpgrade = (GTextField)com.GetChildAt(50);
            grpUpgrade = (GGroup)com.GetChildAt(51);
            tfSubscribeDesc14 = (GTextField)com.GetChildAt(52);
            tfSubscribeDesc15 = (GRichTextField)com.GetChildAt(53);
            btnUpdate = (GButton)com.GetChildAt(54);
            tfBtnUpdate = (GTextField)com.GetChildAt(55);
            grpUpdate = (GGroup)com.GetChildAt(56);
            tfSubscribeDesc6_lifetime = (GRichTextField)com.GetChildAt(57);
            lifetimeTxt3 = (GRichTextField)com.GetChildAt(59);
            lifetimeNode1 = (GGroup)com.GetChildAt(60);
            grpLifetimePremium = (GGroup)com.GetChildAt(61);
            grpContent = (GGroup)com.GetChildAt(62);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            State = null;
            FreeIsLifetime = null;
            shop_flow_free_plan = null;
            shop_flow_subscribe_plan = null;
            lifetimeTxt2 = null;
            lifetimeNode = null;
            tfSubscribeDesc1 = null;
            tfSubscribeDesc1_lifetime = null;
            tfSubscribeDesc5 = null;
            tfSubscribeDesc4 = null;
            tfSubscribeDesc2 = null;
            yearLineImg = null;
            btnSubscribe = null;
            tfBtnSubscribe = null;
            tfMore = null;
            btnMore = null;
            grpBtnMore = null;
            tfSubscribeDesc3 = null;
            grpSubscribe = null;
            lifetimeTxt1 = null;
            lifetimeNode2 = null;
            tfSubscribeDesc10 = null;
            tfSubscribeDesc9 = null;
            tfSubscribeDesc8 = null;
            btnFreePlan = null;
            tfBtnFreePlan = null;
            tfSubscribeDesc10_lifetime = null;
            grpFreePlan = null;
            tfCurPlan = null;
            tfSubscribeDesc6 = null;
            tfSubscribeEndtime = null;
            grpPremium = null;
            tfMyItemsTitle = null;
            listMyItems = null;
            grpItemList = null;
            comShopList.Dispose();
            comShopList = null;
            tfDiamondDesc = null;
            tfDiamond = null;
            grpShopList = null;
            tfCurPlan2 = null;
            tfSubscribeDesc13 = null;
            tfSubscribeEndtime2 = null;
            tfSubscribeDesc12 = null;
            spBtnUpgrade = null;
            btnUpgrade = null;
            tfBtnUpgrade = null;
            grpUpgrade = null;
            tfSubscribeDesc14 = null;
            tfSubscribeDesc15 = null;
            btnUpdate = null;
            tfBtnUpdate = null;
            grpUpdate = null;
            tfSubscribeDesc6_lifetime = null;
            lifetimeTxt3 = null;
            lifetimeNode1 = null;
            grpLifetimePremium = null;
            grpContent = null;
        }
    }
}
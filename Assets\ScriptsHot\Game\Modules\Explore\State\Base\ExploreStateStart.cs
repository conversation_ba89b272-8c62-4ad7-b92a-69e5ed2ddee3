﻿using Msg.basic;
using ScriptsHot.Game.Modules.ChatLogicNew.ChatState;
using ScriptsHot.Game.Modules.Explore.ExploreType.Base;

namespace ScriptsHot.Game.Modules.Explore.State.Base
{
    
    public class ExploreStateStart : ExploreStateBase
    {
        private ExploreParam _param;
        public ExploreStateStart(ExploreEntityBase chat) : base(ExploreStateName.Start,chat)
        {
        }

        public override void OnEnter(params object[] args)
        {
            base.OnEnter(args);
            _param = (ExploreParam)args[0];
            //清空队列
            Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
            //停止声音
            // Notifier.instance.SendNotification(NotifyConsts.ExploreSoundStop);
            switch (_param.ChatMode)
            {
                 case PB_DialogMode.Career:
                     _param.Entity.ChangeState(ExploreStateName.ExploreChatActiveStart,_param);
                     break;
                 case PB_DialogMode.Challenge:
                     _param.Entity.ChangeState(ExploreStateName.ExploreChatActiveStart,_param);
                     break;
                 default:
                     break;
            }
        }
        
        public override void OnReEnter(params object[] args)
        {
            base.OnReEnter(args);
        }

        public override void OnExit()
        {
            base.OnExit();
        }
    }
}



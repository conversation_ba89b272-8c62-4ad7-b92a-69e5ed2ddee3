/*
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/08/15 16:35:35 星期四
# 功能：Nothing
****************************************************
*/

using System;
using System.Collections.Generic;
using DG.Tweening;
using FairyGUI;
using Msg.incentive;
using UIBind.Sign;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Sign
{
    public class SignTerminalUIBase<T> : BaseUI<T> where T : UIBindT
    {
        protected List<SignStreakTerminalComponent> _streakTerminalCmps = new();
        protected List<TerminalLineComponent> _terminalLineCmps = new();
        protected List<PB_UserCheckinMilestoneItem> _days = new();
        protected List<PB_UserCheckinMilestoneItem> _options = new();
        
        protected bool _isRestart;
        protected bool _isSetting;

        private readonly static int CELL_LENGTH = 86;
        private readonly static int CELL_SPACE = 4;
        private readonly static int LINE_HEIGHT = 24;
        private readonly static int OPTIONS_SIZE = 4;
        protected SignTerminalUIBase(string name) : base(name)
        {
        }

        private void Clear()
        {
            foreach (var cmp in _streakTerminalCmps)
            {
                cmp.com.Dispose();
            }

            foreach (var cmp in _terminalLineCmps)
            {
                cmp.com.Dispose();
            }

            _streakTerminalCmps.Clear();
            _terminalLineCmps.Clear();
        }

        protected void DrawSignTerminal(SignTerminalComponent cmp, int curDay, bool playAni = false)
        {
            Clear();
            cmp.spStar.visible = false;
            var comlength = (int)cmp.com.size.x;
            var comHeight = (int)cmp.com.size.y;
            var lineY = (comHeight - LINE_HEIGHT) / 2;
            // var cellLengthAll = days.Count * CELL_LENGTH;
            var lineCount = _isRestart ? _days.Count : _days.Count - 1;
            var elementCount = _isRestart ? _days.Count * 2 : _days.Count * 2 - 1;
            if (elementCount == 1)
                elementCount = 2;
            if (lineCount == 0)
                lineCount = 1;
            var lineLength = (comlength - CELL_SPACE * (elementCount - 1) - _days.Count * CELL_LENGTH) / lineCount;
            var curX = 0;
            var day = 0;
            var curDayIdx = 0;
            var isDrawLine = false;
            if (lineLength < 8)
            {
                VFDebug.LogError("SignStreakTerminalComponent was initialized by invalid parameters!");
                return;
            }

            for (int i = 0; i < elementCount; i++)
            {
                if (i == 0)
                {
                    if (_isRestart)
                    {
                        var nextDay = curDayIdx >= _days.Count
                            ? _days[curDayIdx - 1].target_checkin_days
                            : _days[curDayIdx].target_checkin_days;
                        if (curDay <= nextDay)
                        {
                            CreateLineCmp(cmp, lineLength, curDay, day, nextDay, curX, lineY, playAni);
                            playAni = false;
                        }
                        else
                        {
                            CreateLineCmp(cmp, lineLength, curDay, day, nextDay, curX, lineY, false);
                        }
                        curX += lineLength + CELL_SPACE;
                        isDrawLine = false;
                    }
                    else
                    {
                        day = _days[curDayIdx].target_checkin_days;
                        curDayIdx++;
                        CreateTerminalCmp(cmp, day, _isSetting || curDay >= day, curX);
                        curX += CELL_LENGTH + CELL_SPACE;
                        isDrawLine = true;
                    }
                }
                else
                {
                    if (isDrawLine)
                    {
                        var nextDay = curDayIdx >= _days.Count
                            ? _days[curDayIdx - 1].target_checkin_days
                            : _days[curDayIdx].target_checkin_days;
                        if (curDay <= nextDay)
                        {
                            CreateLineCmp(cmp, lineLength, curDay, day, nextDay, curX, lineY, playAni);
                            playAni = false;
                        }
                        else
                        {
                            CreateLineCmp(cmp, lineLength, curDay, day, nextDay, curX, lineY, false);
                        }
                        curX += lineLength + CELL_SPACE;
                        isDrawLine = false;
                    }
                    else
                    {
                        day = _days[curDayIdx].target_checkin_days;
                        curDayIdx++;
                        CreateTerminalCmp(cmp, day, curDay >= day, curX);
                        curX += CELL_LENGTH + CELL_SPACE;
                        isDrawLine = true;
                    }
                }
            }
        }

        private void CreateTerminalCmp(SignTerminalComponent father, int day, bool active, int curX)
        {
            SignStreakTerminalComponent cmp = new();
            cmp.Construct(UIPackage.GetByName("Sign").CreateObject("SignStreakTerminalComponent").asCom);
            cmp.com.scale = new Vector2(0.27f, 0.27f);
            cmp.tfDay.text = day.ToString();
            cmp.state.selectedPage = active ? "active" : "inactive";
            cmp.spCalendar.animationName = active ? _isSetting? "b1" : "b3" : "a1";
            cmp.com.xy = new Vector2(curX, 0);
            if (_isSetting)
            {
                cmp.com.alpha = 0f;
                cmp.com.visible = false;
            }
            father.com.AddChild(cmp.com);
            _streakTerminalCmps.Add(cmp);
        }

        private void CreateLineCmp(SignTerminalComponent father, int length, int curDay, int day, int nextDay, int curX, int lineY, bool playAni = false)
        {
            TerminalLineComponent cmp = new();
            cmp.Construct(UIPackage.GetByName("Sign").CreateObject("TerminalLineComponent").asCom);
            cmp.com.size = new Vector2(length, cmp.com.size.y);
            if (!_isSetting)
            {
                var size = cmp.com.size;
                cmp.state.selectedPage = curDay > day ? "active" : "inactive";
                if (playAni)
                {
                    var rateStart = ((float)curDay - 1) / nextDay;
                    var rateEnd = (float)curDay / nextDay;
                    if (rateEnd > 1)
                        rateEnd = 1;
                    cmp.imgProgress.size = new Vector2(rateStart * size.x , size.y);
                    VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);
                    cmp.imgProgress.TweenResize(new Vector2(rateEnd * size.x, size.y),0.5f).OnComplete(() =>
                    {
                        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Success);
                        father.spStar.visible = true;
                        father.spStar.sortingOrder = father.com.parent.numChildren;
                        SoundManger.instance.PlayUI("sign_progress_bar");
                        father.spStar.spineAnimation.AnimationState.ClearListenerNotifications();
                        father.spStar.xy = father.spStar.parent.GlobalToLocal(cmp.com.LocalToGlobal(new Vector2(rateEnd * size.x - 100, size.y / 2 - 100)));
                        father.spStar.spineAnimation.AnimationState.SetAnimation(0, "animation", false).Complete += (t) =>
                        {
                            father.spStar.visible = false;
                        };
                    });
                }
                else
                {
                    var rate = (float)curDay / nextDay;
                    if (rate > 1)
                        rate = 1;
                    cmp.imgProgress.size = new Vector2(rate * size.x , size.y);     
                }
            }
            else
            {
                cmp.state.selectedPage = curDay > day ? "active" : "inactive";
                cmp.com.alpha = 0f;
            }
            cmp.com.xy = new Vector2(curX, lineY);
            father.com.AddChild(cmp.com);
            _terminalLineCmps.Add(cmp);
        }
        
        
        protected void InitTerminals(PB_UserCheckinMilestoneItem target_milestone, PB_UserCheckinMilestoneItem preMileStone, List<PB_UserCheckinMilestoneItem> milestone_list, bool isSetting)
        {
            _days = new();
            _options = new();
            _isRestart = preMileStone.target_checkin_days == 0;
            _isSetting = isSetting;
            
            var startCount = false;
            var counter = 0;
            var maxSize = _isRestart ? OPTIONS_SIZE : OPTIONS_SIZE + 1;
            foreach (var mileStone in milestone_list)
            {
                if (counter < maxSize)
                {
                    if (_isRestart)
                        startCount = true;

                    if (startCount && ((!isSetting && mileStone.target_checkin_days <= target_milestone.target_checkin_days) || isSetting))
                    {
                        if ((_isRestart && (mileStone.target_checkin_days > 1  || target_milestone.target_checkin_days == 1)) || !_isRestart)
                        {
                            _options.Add(mileStone);
                            _days.Add(mileStone);
                            counter++;   
                        }
                    }


                    if (!startCount && target_milestone != null && (mileStone.target_checkin_days == preMileStone.target_checkin_days))
                    {  
                        _days.Add(mileStone);
                        startCount = true;
                        counter++;
                    }
                    else if (!startCount && target_milestone != null && target_milestone.target_checkin_days == 1)
                    {
                        // onboard
                        _days.Add(new PB_UserCheckinMilestoneItem()
                        {
                            target_checkin_days = 1,
                        });
                        _days.Add(mileStone);
                        _options.Add(mileStone);
                        startCount = true;
                        counter++; 
                    }
                    else if (!startCount && target_milestone != null && preMileStone.target_checkin_days == 1)
                    {
                        // onboard
                        _days.Add(new PB_UserCheckinMilestoneItem()
                        {
                            target_checkin_days = 1,
                        });
                        _days.Add(mileStone);
                        startCount = true;
                        counter++; 
                    }
                }
            }
        }

        protected void PlayFadeIn(Action cb)
        {
            var elementCount = _streakTerminalCmps.Count + _terminalLineCmps.Count;
            var lineIdx = 0;
            var terminalIdx = 0;
            var isDrawLine = false;
            var sequence = DOTween.Sequence();
            for (int i = 0; i < elementCount; i++)
            {
                if (i == 0)
                {
                    if (_isRestart)
                    {
                        var com = _terminalLineCmps[lineIdx].com;
                        com.visible = true;
                        com.alpha = 0;
                        sequence.AppendCallback(()=>
                        {
                            com.TweenFade(1, 0.1f);
                        });
                        sequence.AppendInterval(0.05f);
                        isDrawLine = false;
                        lineIdx++;
                    }
                    else
                    {
                        var com = _streakTerminalCmps[terminalIdx].com;
                        com.visible = true;
                        com.alpha = 0;;
                        sequence.AppendCallback(()=>
                        {
                            com.TweenFade(1, 0.1f);
                        });
                        sequence.AppendInterval(0.05f);
                        isDrawLine = true;
                        terminalIdx++;
                    }
                }
                else
                {
                    if (isDrawLine)
                    {
                        var com = _terminalLineCmps[lineIdx].com;
                        com.visible = true;
                        com.alpha = 0;
                        sequence.AppendCallback(()=>
                        {
                            com.TweenFade(1, 0.1f);
                        });
                        sequence.AppendInterval(0.05f);
                        isDrawLine = false;
                        lineIdx++;
                    }
                    else
                    {
                        var com = _streakTerminalCmps[terminalIdx].com;
                        com.visible = true;
                        com.alpha = 0;
                        sequence.AppendCallback(()=>
                        {
                            com.TweenFade(1, 0.1f);
                        });
                        sequence.AppendInterval(0.05f);
                        isDrawLine = true;
                        terminalIdx++;
                    }
                }
            }
            sequence.AppendCallback(() => cb?.Invoke());
        }

        protected void RefreshSignTerminal()
        {
        }
    }
}
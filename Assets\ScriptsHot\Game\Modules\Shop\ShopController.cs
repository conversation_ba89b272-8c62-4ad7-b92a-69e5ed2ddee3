/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/07 21:48:29 星期二
# 功能：Nothing
****************************************************
*/

using CommonUI;
using Game;
using Modules.DataDot;
using Msg.basic;
using Msg.dialog_task;
using Msg.economic;
using UIBind.Shop;
using UnityEngine;
using System;
using System.Collections.Generic;
using ScriptsHot.Game.Modules.IncentiveTask;
using LitJson;
using Msg.incentive;
using ScriptsHot.Game.Modules.Managers;
using ScriptsHot.Game.Modules.Sign;

namespace ScriptsHot.Game.Modules.Shop
{
    public enum OpenShopSource
    {
        None,
        Quest,
    }
    public class ShopController: BaseController
    {
        public ShopController() : base(ModelConsts.Shop)
        {
            
        }
        private ShopModel _shopModel => GetModel<ShopModel>(ModelConsts.Shop);
        public ShopModel ShopModel => _shopModel;
        
        private CurrencyModel _currencyModel => GetModel<CurrencyModel>(ModelConsts.CurrencyController);

        private bool _willOpenShop = false;

        private bool _purchasingMgrInitialed;

        public bool pay_wall_abtest_initialized { get; private set; }

        public static readonly string MEMBER_TYPE_MONTH = "premium_monthly";
        public static readonly string MEMBER_TYPE_QUARTER = "premium_quarterly";
        public static readonly string MEMBER_TYPE_YEAR = "premium_yearly";
        private static readonly string SUBSCRIPTION_UPDATE_URL = "itms-apps://apps.apple.com/account/subscriptions";
        
        // private GuidModel _guidModel => GetModel<GuidModel>(ModelConsts.Guid);
        public override void OnInit()
        {
            this.RegisterModel(new ShopModel());
            // 查询用户的会员状态返回
            //MsgManager.instance.RegisterCallBack<SC_QueryMemberInfoAck>(OnSCQueryMemberInfoAck);
            // 查询某个用户的会员状态（地图外化用）
            // MsgManager.instance.RegisterCallBack<SC_MemberTypePushReq>(OnSCMemberTypePushReq);
            // 创建订单
            MsgManager.instance.RegisterCallBack<SC_CreateOrderAck>(OnSCCreateOrderAck);
            // 查询进行中的订单返回
            MsgManager.instance.RegisterCallBack<SC_GetUnpaidOrderAck>(OnSCGetUnpaidOrderAck);
            // 查询被取消订单的订单返回
            MsgManager.instance.RegisterCallBack<SC_CancelOrderAck>(OnSCCancelOrderAck);
            // 查询被取消订单的订单返回
            MsgManager.instance.RegisterCallBack<SC_UserRewardItem>(OnSCUserRewardItem);
            //商品信息
            MsgManager.instance.RegisterCallBack<SC_GetShopInfoResp>(OnSCGetShopInfoResp);
            
            Notifier.instance.RegisterNotification(NotifyConsts.OpenShop,OpenShop);
            
            //20250620 做折扣ab，只做了duo一套界面
            // Notifier.instance.RegisterNotification(NotifyConsts.OnGetIncentiveData, OnGetIncentiveData);
        }

        public override void OnEnterGame()
        {
            base.OnEnterGame();
            ReqShopInfo();
            AbTestUtil.ReqAbTestValue("recommend_plan", msg =>
            {
                _shopModel.SetTestRecommendPlan(msg.data.value switch
                {
                    "month" => ShopModel.RecommendPlanType.Month,
                    "quarter" => ShopModel.RecommendPlanType.Quarter,
                    "year" => ShopModel.RecommendPlanType.Year,
                    _ => ShopModel.RecommendPlanType.Month,
                });
            }, new Dictionary<string, string>(){ { "country", GetCountryCode() } });
        }

        public void ReqShopInfo()
        {
            CS_GetShopInfoReq msg = GetRequest();

            MsgManager.instance.SendMsg(msg, (type, message) => GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("webview_notice_timeout" , callBack:ReqShopInfo));
        }

        public async void ReqShopInfoAsync()
        {
            CS_GetShopInfoReq msg = GetRequest();

            
            var result = await MsgManager.instance.SendAsyncMsg<SC_GetShopInfoResp>(msg, null, true);
            OnSCGetShopInfoResp(result);
        }

        private CS_GetShopInfoReq GetRequest()
        {
            var rkStr = AppRegionInfo.GetCurrRegionKeyAsStr();

            var msg = new CS_GetShopInfoReq()
            {
                region_key = rkStr,
                country_code = GetCountryCode(),
#if UNITY_IOS
                economic_source = PB_EconomicSourceEnum.APPLE
#elif UNITY_ANDROID
                economic_source = PB_EconomicSourceEnum.GOOGLE
#endif
      
            };
            
            // var msg = new CS_GetShopInfoReq()
            // {
            //     // region_key = "jpTest",
            //     region_key = "jpTest",
            //     country_code = "BRA",
            //     economic_source = PB_EconomicSourceEnum.APPLE
            // };
            
            return msg;
        }

        private string GetCountryCode()
        {
            string countryCode = string.Empty;
            try
            {
                countryCode = OsFuncAdapter.Ins.GetCountryCode();
            }
            catch (Exception e)
            {
                Debug.LogError("SendGetShopInfo err:" + e.ToString());
                countryCode = DefaultCountryCodeProvider.GetCountryCode();
            }

            if (string.IsNullOrEmpty(countryCode))
            {
                //Debug.Log("countrycode = null or empty");
                countryCode = DefaultCountryCodeProvider.GetCountryCode();
            }
            return countryCode;
        }


        public void EnterShop()
        {
            // 250327 中国版过审修改
            if (AppConst.IsCN)
                return;
            _willOpenShop = true;
            ReqShopInfo();
        }

        public override void OnUIInit()
        {
            this.RegisterUI(new ShopUI(UIConsts.ShopUI));
            this.RegisterUI(new PlanInstructionUI(UIConsts.PlanInstructionUI));
            this.RegisterUI(new PlanPromotionStep1UI(UIConsts.PlanPromotionStep1UI));
            this.RegisterUI(new PlanPromotionStep2UI(UIConsts.PlanPromotionStep2UI));
            this.RegisterUI(new PlanPromotionStep3UI(UIConsts.PlanPromotionStep3UI));
            this.RegisterUI(new PlanSelectUI(UIConsts.PlanSelectUI));
            this.RegisterUI(new SubscribeSuccessUI(UIConsts.SubscribeSuccessUI));
            this.RegisterUI(new SubscribePremiumUI(UIConsts.SubscribePremiumUI));
            this.RegisterUI(new PlanDetainmentUI(UIConsts.PlanDetainmentUI));
            this.RegisterUI(new ShopMyItemUI(UIConsts.ShopMyItemUI));
            this.RegisterUI(new PlanBottomSubscribeUI(UIConsts.PlanBottomSubscribeUI));
            this.RegisterUI(new SettlementPlanSubscribeUI(UIConsts.SettlementPlanSubscribeUI));
            this.RegisterUI(new PlanCanceledUI(UIConsts.PlanCanceledUI));
            this.RegisterUI(new PlanPendingUI(UIConsts.PlanPendingUI));
            this.RegisterUI(new PlanRetryUI(UIConsts.PlanRetryUI));
            
            //style-speak
            this.RegisterUI(new Speak_ShopUI(UIConsts.Speak_ShopUI));
            this.RegisterUI(new SpeakPlanPromotionStep1UI(UIConsts.SpeakPlanPromotionStep1UI));
            this.RegisterUI(new SpeakPlanSelectUI(UIConsts.SpeakPlanSelectUI));
            this.RegisterUI(new SpeakPlanBottomSubscribeUI(UIConsts.SpeakPlanBottomSubscribeUI));
            this.RegisterUI(new SpeakSettlementPlanSubscribeUI(UIConsts.SpeakSettlementPlanSubscribeUI));
            
            this.RegisterUI(new PayWallHalfUI(UIConsts.PayWallHalfUI));
            this.RegisterUI(new OnBoardPaywallUI(UIConsts.OnBoardPaywallUI));
            this.RegisterUI(new SpeakPlanPromotionStep3UI(UIConsts.SpeakPlanPromotionStep3UI));
        }
        
        private void OnSCCreateOrderAck(SC_CreateOrderAck msg)
        {
            if (msg.code == PB_BizCode.PB_BizCode_Success)
                _shopModel.SetOrderInfo(msg.data.order_id, msg.data.req_id);
            //todo 显示错误toast
        }

        private void OnSCGetUnpaidOrderAck(SC_GetUnpaidOrderAck msg)
        {
            // if (msg.code == PB_BizCode.PB_BizCode_Success)
                // _shopModel.SetOrderInfo(msg.data.order_id, msg.data.req_id);
        }


        private void OnSCCancelOrderAck(SC_CancelOrderAck msg)
        {
            if (msg.code == PB_BizCode.PB_BizCode_Success)
            {
                var rkStr = AppRegionInfo.GetCurrRegionKeyAsStr();
                var cfgTop = "";
                if (_shopModel.TryGetMemberTypeByName(ShopController.MEMBER_TYPE_QUARTER, out var cfg_quarterly))
                {
                    cfgTop = ShopController.MEMBER_TYPE_QUARTER;
                }
                else if (_shopModel.TryGetMemberTypeByName(ShopController.MEMBER_TYPE_YEAR, out var cfg_yearly))
                {
                    cfgTop = ShopController.MEMBER_TYPE_YEAR;
                }

                var cfg1 = _shopModel.GetMemberTypeByName(cfgTop);
                var cfg2 = _shopModel.GetMemberTypeByName(MEMBER_TYPE_MONTH);
                if ((msg.data.product_id == cfg1.product_id || msg.data.product_id == cfg2.product_id) && _shopModel.memberInfo.member_type == MemberType.NoneMember)
                {
                   // GetUI<PlanDetainmentUI>(UIConsts.PlanDetainmentUI).Show();
                    // 新埋点：用户进入钻石商店
                    DataDotFailedHold dot = new DataDotFailedHold();
                    DataDotMgr.Collect(dot);
                }
            }
        }

        private void OnSCUserRewardItem(SC_UserRewardItem msg)
        {
            GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReq(GameEventName.GameEnter);
            GetUI<CommonGetDiamondUI>(UIConsts.CommonGetDiamond).Open(CommonGetDiamondType.More, (int)msg.product_cnt, null);
        }

        private void OnSCGetShopInfoResp(SC_GetShopInfoResp msg)
        {
            if (msg.code == PB_BizCode.PB_BizCode_Success)
            {
                _shopModel.SetLifetimeData(msg.data);
                _shopModel.SetPromotionType(msg.data.promotion_type);
                _shopModel.SetMemberInfo(msg.data.member_info);
                _shopModel.SetShopInfo(msg.data);
                Notifier.instance.SendNotification(NotifyConsts.ShopInfoUpdate);
                if (_willOpenShop)
                {
                    if (_shopModel.shopUIStyle == ShopModel.ShopUIStyle.Speak)
                    {
                        GetUI<Speak_ShopUI>(UIConsts.Speak_ShopUI).Show();
                    }
                    else
                    {
                        GetUI<ShopUI>(UIConsts.ShopUI).Show();
                    }
                    _willOpenShop = false;
                    //新埋点：用户进入钻石商店
                    DataDotDiamondShop dot = new DataDotDiamondShop();
                    
                    
                    dot.subscribe_status = _shopModel.memberInfo.SubscribeStatus.ToString();
                    dot.member_type = _shopModel.memberInfo.member_type.ToString();
                    DataDotMgr.Collect(dot); 
                }
                else if (GetUI<ShopUI>(UIConsts.ShopUI).isShow)
                {
                    GetUI<ShopUI>(UIConsts.ShopUI).Refresh();
                }
                // 250327 中国版过审修改
                if (!_purchasingMgrInitialed && !AppConst.IsCN)
                {
                    PB_SubscriptionInfo specialProduct = null;
                    Dictionary<string, PB_SubscriptionInfo> tempDic = new();
                    foreach (var value in _shopModel.SubscriptionInfoDic)
                        tempDic[value.Value.product_id] = value.Value;

                    if (msg.data.limited_subscription_info != null)
                        foreach (var info in msg.data.limited_subscription_info.subscription_infos)
                            tempDic[info.product_id] = info;

                    if (msg.data.lifetime_subscription_info != null)
                    {
                        var lifetimeShopInfoData = msg.data.lifetime_subscription_info.subscription_info;
                        specialProduct = lifetimeShopInfoData;
                    }

                    PurchasingManager.instance.InitializePurchasing(tempDic,_shopModel.InAppPurchaseInfoDic , specialProduct);
                    _purchasingMgrInitialed = true;
                }
                
                _shopModel.SetPayWallData(msg.data.limited_subscription_info);
            }
        }

        public void OpenSubscriptionUpdate()
        {
            Application.OpenURL(SUBSCRIPTION_UPDATE_URL);
        }
        
        public void ShowHomepagePlanUI(SC_GetIncentiveDataForPortalAck msg)
        {
            var status = msg.data.homepage_economic_info.member_info.SubscribeStatus;
            var isMember = msg.data.homepage_economic_info.member_info.is_member;
            var lastShowPlanCanceledUIDate = PlayerPrefs.GetString("show_plan_canceled_ui_date");
            var lastShowPlanRetryingUIDate = PlayerPrefs.GetString("show_plan_retrying_ui_date");
            var today = TimeExt.serverTime.ToString("yyyy-MM-dd");
            if (status == SubscribeStatus.FreeTrialCanceled && isMember && !string.Equals(lastShowPlanCanceledUIDate, today))
            {
                GetUI(UIConsts.PlanCanceledUI).PopShow(PopUIManager.Priority.Business);
                PlayerPrefs.SetString("show_plan_canceled_ui_date", TimeExt.serverTime.ToString("yyyy-MM-dd"));
            }
            else if (status == SubscribeStatus.Retrying && !isMember && !string.Equals(lastShowPlanRetryingUIDate, today))
            {
                GetUI(UIConsts.PlanRetryUI).PopShow(PopUIManager.Priority.Business);
                PlayerPrefs.SetString("show_plan_retrying_ui_date", TimeExt.serverTime.ToString("yyyy-MM-dd"));
            }
        }

        public override void OnApplicationPause(bool pause)
        {
            base.OnApplicationPause(pause);
            SendNotification(NotifyConsts.OnApplicationPaused, pause);
        }

        public void OpenShop(string s, object body)
        {
            if(body != null)
                _shopModel.soure = (OpenShopSource)body;
            GetUI<ShopUI>(UIConsts.ShopUI).Show();
        }

        private void OnGetIncentiveData(string s, object body)
        {
            if (pay_wall_abtest_initialized)
                return;
            AbTestUtil.ReqAbTestValue("pay_wall_abtest", msg =>
                {
                    pay_wall_abtest_initialized = true;
                    _shopModel.SetShopUIStyle(msg.data.value switch
                    {
                        "duo" => ShopModel.ShopUIStyle.Duo,
                        "speak" => ShopModel.ShopUIStyle.Speak,
                        _ => ShopModel.ShopUIStyle.Duo,
                    });
                    GetController<HomepageController>(ModelConsts.Homepage).ShowCommercialUI();
                },
                new Dictionary<string, string>()
                {
                    { "is_commercial_new_user", GetModel<MainModel>(ModelConsts.Main).incentiveData.homepage_economic_info.is_commercial_new_user.ToString().ToLower() }
                });
        }

        public void JumpToOpenShopSoure()
        {
            switch ( _shopModel.soure)
            {
                case OpenShopSource.None:
                    break;
                case OpenShopSource.Quest:
                    GetController<IncentiveTaskController>(ModelConsts.IncentiveTask).EnterIncentiveTask();
                    break;
            }
        }

        #region PlanPromotionStep1Panel界面，当从onBoarding界面打开的时候，换个文本

        private string planPromotionStep1PanelTitle;

        public string PlanPromotionStep1PanelTitle
        {
            set
            {
                planPromotionStep1PanelTitle = value;
            }
            get
            {
                if (!string.IsNullOrEmpty(planPromotionStep1PanelTitle))
                {
                    var tmp = planPromotionStep1PanelTitle;
                    planPromotionStep1PanelTitle = string.Empty;
                    return tmp;
                }
                return string.Empty;
            }
        }
#endregion





        #region OnBoard需要打开界面和提前加载数据

        private bool isOpeningShopUIFromOnBoard = false;
        public void OnBoardReq()
        { 
            ReqShopInfo();
            MsgManager.instance.SendMsg(new CS_GetIncentiveDataForPortalReq()
            {
                user_id = GetModel<MainModel>(ModelConsts.Main).userID,
            });
        }
        public void OnBoardOpenShopUI()
        {
            isOpeningShopUIFromOnBoard = true;
            if (GetModel<MainModel>(ModelConsts.Main).incentiveData == null)
            {
                CheckSendOnBoardOverMsg();
                return;
            }
            
            if (_shopModel.OnBoardPaywallAB == "PERSONALIZED")
                GetUI<OnBoardPaywallUI>(UIConsts.OnBoardPaywallUI).Show();
            else
            {
                PlanPromotionStep1PanelTitle = I18N.inst.MoStr("ui_onboard_pay");
                GetUI<SpeakPlanPromotionStep1UI>(UIConsts.SpeakPlanPromotionStep1UI).Show(5);
            }
        }
        public void CheckSendOnBoardOverMsg()
        {
            if (!isOpeningShopUIFromOnBoard)
            {
                return;
            }
            SendNotification(NotifyConsts.OnBoardShopUIOver);
            isOpeningShopUIFromOnBoard = false;
        }
        #endregion
    }
}
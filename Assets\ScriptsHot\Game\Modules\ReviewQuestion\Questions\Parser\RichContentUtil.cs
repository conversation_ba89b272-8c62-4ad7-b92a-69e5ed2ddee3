﻿/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/03/15 19:33:37 星期五
# 功能：Nothing
****************************************************
*/

using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Google.Protobuf.Collections;
using Msg.question;
using Msg.speech;
using Msg.tts;
using ScriptsHot.Game.Modules.Common;
using UnityEngine;

namespace ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser
{
    public static class RichContentUtil
    {
        
        public static List<RichContent> SplitWords(string text, Color overrideColor = default)
        {
            Regex reg = new Regex(@"^[A-Za-z']+$");
            var curIdx = 0;
            var startIdx = 0;
            var isWordStart = false;
            var output = new List<RichContent>();
        
            foreach (var str in text)
            {
                var isChanged = false;
                if (reg.IsMatch(str.ToString()))
                {
                    if (!isWordStart)
                        isChanged = true;
                    isWordStart = true;
                }
                else
                {
                    if (isWordStart)
                        isChanged = true;
                    isWordStart = false;
                }
            
                if ((isChanged || curIdx == text.Length) && curIdx - startIdx != 0)
                {
                    var content = new RichContent()
                    {
                        Type = RichContentType.Text,
                        Color = overrideColor,
                        Content = text.Substring(startIdx, curIdx - startIdx)
                    };
                    
                    output.Add(content);
                    startIdx = curIdx;
                }
                
                if (curIdx == text.Length - 1)
                {
                    var content2 = new RichContent()
                    {
                        Type = RichContentType.Text,
                        Color =  overrideColor,
                        Content = text.Substring(startIdx, text.Length - startIdx),
                        IsLast = true
                    };
                    
                    output.Add(content2);  
                }
                
                curIdx++;
            }

            if (output.Count == 0)
            {
                output.Add(
                     new RichContent()
                    {
                        Type = RichContentType.Text,
                        Color =  overrideColor,
                        Content = text,
                        IsLast = true
                    }
                    );
            }
            
            return output;
        }
        
        //字母变色
        public static List<MatchResult> ConvertPronunciationResult(List<PB_PronunciationAssessmentUnit> units)
        {
            List<MatchResult> result = new();
            foreach (var unit in units)
            {
                if (unit.unit_type == PB_PronunciationAssessmentUnitType.PronunciationAssessmentUnitType_Word)
                {
                    if (unit.word.phonemes.Count == 0)
                    {
                        if(unit.word.decision == PB_AssessmentDecision.AssessmentDecision_Correct)
                            result.Add(new (unit.word.word.ToLower(), TextFieldExtension.TEXT_COLOR_GOOD));
                        else if(unit.word.decision == PB_AssessmentDecision.AssessmentDecision_Warning)
                            result.Add(new (unit.word.word.ToLower(), TextFieldExtension.TEXT_COLOR_YELLOW));
                        else if(unit.word.decision == PB_AssessmentDecision.AssessmentDecision_Incorrect)
                            result.Add(new (unit.word.word.ToLower(), TextFieldExtension.TEXT_COLOR_BAD));
                    }
                    else
                    {
                        foreach (var phoneme in unit.word.phonemes)
                        {
                            if(phoneme.decision == PB_AssessmentDecision.AssessmentDecision_Correct)
                                result.Add(new (phoneme.phoneme.ToLower(), TextFieldExtension.TEXT_COLOR_GOOD));
                            else if(phoneme.decision == PB_AssessmentDecision.AssessmentDecision_Warning)
                                result.Add(new (phoneme.phoneme.ToLower(), TextFieldExtension.TEXT_COLOR_YELLOW));
                            else if(phoneme.decision == PB_AssessmentDecision.AssessmentDecision_Incorrect)
                                result.Add(new (phoneme.phoneme.ToLower(), TextFieldExtension.TEXT_COLOR_BAD));
                        }
                    }
                    
                }
            }
            
            // foreach (var unit in units)
            // {
            //     if (unit.unit_type == PB_PronunciationAssessmentUnitType.PronunciationAssessmentUnitType_Word)
            //     {
            //         if(unit.word.decision == PB_AssessmentDecision.AssessmentDecision_Correct || unit.word.decision == PB_AssessmentDecision.AssessmentDecision_Warning)
            //             result.Add((unit.word.word.ToLower(), TextFieldExtension.TEXT_COLOR_GOOD));
            //         else if(unit.word.decision == PB_AssessmentDecision.AssessmentDecision_Incorrect)
            //             result.Add((unit.word.word.ToLower(), TextFieldExtension.TEXT_COLOR_BAD));
            //     }
            // }
            
            return result;
        }
        
        
        public static List<RichContent> ConvertPronunciationToText(List<PB_PronunciationAssessmentUnit> units){
            List<RichContent> result = new();
            
            foreach (var unit in units)
            {
                var content = new RichContent();
                if (unit.unit_type == PB_PronunciationAssessmentUnitType.PronunciationAssessmentUnitType_Word)
                {
                    content.Type = RichContentType.Text;
                    content.Content = unit.word.word;
                    content.IsPlosionWord = unit.highlight_type == PB_PronunciationHighlightType.PronunciationHighlightType_Plosion;
                    content.IsLiasionWord = unit.highlight_type == PB_PronunciationHighlightType.PronunciationHighlightType_Liaison;
                    result.Add(content);
                }
                else if (unit.unit_type == PB_PronunciationAssessmentUnitType.PronunciationAssessmentUnitType_Blank)
                {
                    content.Type = RichContentType.Text;
                    content.Content = unit.blank;
                    content.IsPlosionWord = unit.highlight_type == PB_PronunciationHighlightType.PronunciationHighlightType_Plosion;
                    content.IsLiasionWord = unit.highlight_type == PB_PronunciationHighlightType.PronunciationHighlightType_Liaison;
                    result.Add(content);
                }
                else if (unit.unit_type == PB_PronunciationAssessmentUnitType.PronunciationAssessmentUnitType_Punctuation)
                {
                    content.Type = RichContentType.Text;
                    content.Content = unit.punctuation;
                    content.IsPlosionWord = unit.highlight_type == PB_PronunciationHighlightType.PronunciationHighlightType_Plosion;
                    content.IsLiasionWord = unit.highlight_type == PB_PronunciationHighlightType.PronunciationHighlightType_Liaison;
                    result.Add(content);
                }
                else if (unit.unit_type == PB_PronunciationAssessmentUnitType.PronunciationAssessmentUnitType_Pause)
                {
                    content.Type = RichContentType.Pause;
                    result.Add(content);
                }
                else if (unit.intonation.intonation_type == PB_PronunciationIntonationType.PronunciationIntonationType_Falling)
                {
                    content.Type = RichContentType.IntonationDown;
                    result.Add(content);
                }
                else if (unit.intonation.intonation_type == PB_PronunciationIntonationType.PronunciationIntonationType_Rising)
                {
                    content.Type = RichContentType.IntonationUp;
                    result.Add(content);
                }
            }

            var last = result[result.Count - 1];
            last.IsLast = true;
            result[result.Count - 1] = last;
            return result;
        }

         
        /// <summary>
        /// 添加时间轴
        /// </summary>
        /// <param name="tfExt"></param>
        /// <param name="transcripts"></param>
        /// <param name="speedMultiplier"></param>
        /// <returns></returns>
        public static List<RichContent> AddFollowAvatarTimeline(TextFieldExtension tfExt, List<PB_AudioTranscript> transcripts){
            
            List<RichContent> contentItems = tfExt.GetAllContents();

            List<RichContent> retList = new List<RichContent>();
            
            foreach(var transcriptItem in transcripts)
            {
                List<PB_AudioTranscriptUnit> units = transcriptItem.units.ToList();
                var lastIdx = 0;
                foreach(var unit in units)
                {
                    for (var i = lastIdx; i < contentItems.Count; i++)
                    {
                        if (contentItems[i].Content == unit.text)
                        {
                            var newContent = contentItems[i];
                            // newContent.Type = RichContentType.Text;
                            newContent.StartTime = unit.start_time;
                            newContent.EndTime = unit.end_time;
                            newContent.PlaySpeed = (unit.end_time - unit.start_time) * 0.5f / 1000f / (float)unit.text.Length; //0.9缩短总长度用来平衡空格带来的多余时长
                            tfExt.ReplaceContent(i, newContent);
                            lastIdx = i + 1;
                            break;
                        }
                    }
                }
                
            }

            return retList;
        }

        /// <summary>
        /// 碎片化练习的单词切分 切单词+挖空
        /// </summary>
        /// <param name="text"> 内容 </param>
        /// <param name="type"> 题目类型 </param>
        /// <param name="blankRanges"></param>
        /// <param name="state"> QuestStateEnum </param>
        /// <param name="overrideColor"> 颜色 默认default </param>
        /// <param name="filledInBlank"> 空格处的填充物 </param>
        /// <returns></returns>
        public static List<RichContent> SplitPracticeWords(string text, PB_QuickPracticeType type, List<PB_BlankRange> blankRanges = null,
            bool isSubmited = false, Color overrideColor = default, string filledInBlank="mmmmm")
        {
            const string ST_NULL = "null";
            const string ST_WORD = "word";
            const string ST_NOT_WORD = "not-word";
            const string ST_BLANK = "blank";

            Regex WordReg = new Regex(@"^[A-Za-z'\-]+$");
            var output = new List<RichContent>(50);
            var crntState = ST_NULL;  // ST_NULL | ST_WORD | ST_NOT_WORD | ST_BLANK
            var blankIndex = 0;

            // 块开始位置
            var blockStart = 0;

            var fillInStyle = type == PB_QuickPracticeType.FillIn || type == PB_QuickPracticeType.Cloze;

            //// 非填空题前面的空格，给播放按钮留空
            //if (!fillInStyle)
            //{
            //    output.Add(
            //        new RichContent()
            //        {
            //            Type = RichContentType.Text,
            //            Color = overrideColor,
            //            Content = "        ",  /*八个别数了*/
            //        }
            //    );
            //}
   

            for (var crntCharIndex = 0; crntCharIndex < text.Length; crntCharIndex++)
            {
                var crntChar = text[crntCharIndex].ToString();
                var isWordChar = WordReg.IsMatch(crntChar);
                var isInBlank = blankRanges != null && blankIndex < blankRanges.Count &&
                                crntCharIndex >= blankRanges[blankIndex].start_index && crntCharIndex < blankRanges[blankIndex].end_index;  // end_index 是开区间

                var oldState = crntState;
                if (isInBlank)
                {
                    crntState = ST_BLANK;
                }else if (isWordChar)
                {
                    crntState = ST_WORD;
                }else
                {
                    crntState = ST_NOT_WORD;
                }

                // 状态变化
                if (oldState == ST_NULL && crntState == ST_WORD)
                {
                    // 开始一个单词
                    blockStart = crntCharIndex;
                }else if (oldState == ST_NULL && crntState == ST_BLANK)
                {
                    // 开始一个空格
                    blockStart = crntCharIndex;
                }else if (oldState == ST_NULL && crntState == ST_NOT_WORD)
                {
                    // 开始一个非单词
                    blockStart = crntCharIndex;
                }else if (oldState == ST_NOT_WORD && crntState == ST_WORD)
                {
                    // 结束一个非单词，开始一个单词
                    AddWord(text, overrideColor, output, blockStart, crntCharIndex, isWord:false);
                    blockStart = crntCharIndex;
                }else if (oldState == ST_NOT_WORD && crntState == ST_BLANK)
                {
                    // 结束一个非单词，开始一个空格
                    AddWord(text, overrideColor, output, blockStart, crntCharIndex, isWord:false);
                    blockStart = crntCharIndex;
                }else if (oldState == ST_WORD && crntState == ST_NOT_WORD)
                {
                    // 结束一个单词，开始一个非单词
                    AddWord(text, overrideColor, output, blockStart, crntCharIndex);
                    blockStart = crntCharIndex;
                }else if (oldState == ST_WORD && crntState == ST_BLANK)
                {
                    // 结束一个单词，开始一个空格
                    AddWord(text, overrideColor, output, blockStart, crntCharIndex);
                    blockStart = crntCharIndex;
                }else if (oldState == ST_BLANK && crntState == ST_NOT_WORD)
                {
                    // 结束一个空格，开始一个非单词
                    AddBlank(text, type, isSubmited, overrideColor, output, blockStart, crntCharIndex, filledInBlank);
                    blockStart = crntCharIndex;
                    blankIndex++;
                }else if (oldState == ST_BLANK && crntState == ST_WORD)
                {
                    // 结束一个空格，开始一个单词
                    AddBlank(text, type, isSubmited, overrideColor, output, blockStart, crntCharIndex, filledInBlank);
                    blockStart = crntCharIndex;
                    blankIndex++;
                }
            }

            // 最后字符收尾，结束空格，或者结束单词
            switch (crntState)
            {
                case ST_BLANK:
                    AddBlank(text, type, isSubmited, overrideColor, output, blockStart, text.Length, filledInBlank, true);
                    break;
                case ST_WORD:
                    AddWord(text, overrideColor, output, blockStart, text.Length, true);
                    break;
                case ST_NOT_WORD:
                    AddWord(text, overrideColor, output, blockStart, text.Length, true, isWord:false);
                    break;
            }        

            return output;
        }

        private static void AddWord(string text, Color overrideColor, List<RichContent> output, int startIndex, int endIndex, bool isLast = false, bool isWord = true)
        {
            output.Add(new RichContent()
            {
                Type = RichContentType.Text,
                Color = overrideColor,
                Content = text.Substring(startIndex, endIndex - startIndex),
                IsLast = isLast,
                NoDot = !isWord,
            });
        }

        private static void AddBlank(string text, PB_QuickPracticeType type, bool isSubmited, Color overrideColor, List<RichContent> output, int startIndex, int endIndex, string filledInBlank, bool isLast = false)
        {
            if (type == PB_QuickPracticeType.FillIn || type == PB_QuickPracticeType.listenSenAndChooseWord)
            {
                if (!isSubmited)
                {
                    output.Add(new RichContent()
                    {
                        Type = RichContentType.Mask,
                        Color = overrideColor,
                        Content = filledInBlank,
                        NoDot = true,
                        IsLast = isLast,
                    });
                }
                else
                {
                    // 揭晓答案，把文字补进填空并染色
                    output.Add(new RichContent()
                    {
                        Type = RichContentType.Text,
                        Color = new Color(33f / 255f, 208f / 255f, 82f / 255f, 1),
                        Content = text.Substring(startIndex, endIndex - startIndex),
                        DotGreen = true,
                        IsLast = isLast,
                    });
                }
            }
            else if (type == PB_QuickPracticeType.Explanation)
            {
                output.Add(new RichContent()
                {
                    Type = RichContentType.Bold,
                    Color = new Color(102f / 255f, 50f / 255f, 1f),
                    Content = text.Substring(startIndex, endIndex - startIndex),
                    NoDot = true,
                    IsLast = isLast,
                });
            }else if (type == PB_QuickPracticeType.Cloze)
            {
                output.Add(new RichContent()
                {
                    Type = RichContentType.Mask,
                    Color = overrideColor,
                    Content = filledInBlank,
                    NoDot = true,
                    IsLast = isLast,
                });
            }else {
                Debug.LogError($"挖空：输出空白错误类型 {type}");
            }
        }


        public static List<RichContent> SplitKnowledgeWords(string text, string knowledge, Color overrideColor = default)
        {
            // Debug.LogError("knowledge::" + knowledge);
            List<PB_BlankRange> hitList = new List<PB_BlankRange>();
            if (!string.IsNullOrEmpty(knowledge))
            {
                int sIndex = 0;  
                while ((sIndex = text.IndexOf(knowledge, sIndex)) != -1)  
                {  
                    int eIndex = sIndex + knowledge.Length - 1;
                    hitList.Add(new PB_BlankRange() { start_index = sIndex, end_index = eIndex });  
                    sIndex = eIndex + 1;  
                }
            }

            
            var output = new List<RichContent>();
            Regex reg = new Regex(@"^[A-Za-z']+$");
            var curIdx = 0;
            var startIdx = 0;
            var isWordStart = false;
            
            int rangeIdx = 0;
            foreach (var str in text)
            {
                var isChanged = false;
                if (reg.IsMatch(str.ToString()))
                {
                    if (!isWordStart)
                        isChanged = true;
                    isWordStart = true;
                }
                else
                {
                    if (isWordStart)
                        isChanged = true;
                    isWordStart = false;
                }

                if ((isChanged || curIdx == text.Length) && curIdx - startIdx != 0)
                {
                    if (hitList.Count > rangeIdx &&
                        curIdx > hitList[rangeIdx].start_index && curIdx <= hitList[rangeIdx].end_index + 1)
                    {
                        var content = new RichContent()
                        {
                            Type = RichContentType.Text,
                            Color = overrideColor,
                            Content = text.Substring(startIdx, curIdx - startIdx),
                            IsLanguagePoint = true,
                        };
                        output.Add(content);
                        if (curIdx >= hitList[rangeIdx].end_index)
                            rangeIdx++;
                    }
                    else
                    {
                        var content = new RichContent()
                        {
                            Type = RichContentType.Text,
                            Color = overrideColor,
                            Content = text.Substring(startIdx, curIdx - startIdx)
                        };
                        output.Add(content);
                    }
                    startIdx = curIdx;
                }

                if (curIdx == text.Length - 1)
                {
                    string endStr = text.Substring(startIdx, text.Length - startIdx);
                    string pattern = @"[a-zA-Z]";
                    bool containsLetter = Regex.IsMatch(endStr, pattern);
                    bool lastChat = hitList.Count > 0 ? hitList[hitList.Count - 1].end_index == curIdx: false;
                    var content2 = new RichContent()
                    {
                        Type = RichContentType.Text,
                        Color = overrideColor,
                        Content = endStr,
                        IsLanguagePoint = containsLetter && lastChat,
                        IsLast = true
                    };
                    // Debug.LogError("最后一个词：" + content2.Content);

                    output.Add(content2);
                }
                curIdx++;
            }
            return output;
        }
        public static List<RichContent> SplitKnowledgeWords(string text, RepeatedField<string> knowledgeList, Color overrideColor = default)
        {
            List<PB_BlankRange> hitList = new List<PB_BlankRange>();

            // 找到所有关键词的范围
            if (knowledgeList != null && knowledgeList.Count > 0)
            {
                foreach (var knowledge in knowledgeList)
                {
                    VFDebug.Log("命中：" + knowledge);
                    if (string.IsNullOrEmpty(knowledge)) continue;

                    int sIndex = 0;
                    while ((sIndex = text.IndexOf(knowledge, sIndex)) != -1)
                    {
                        int eIndex = sIndex + knowledge.Length - 1;
                        hitList.Add(new PB_BlankRange() { start_index = sIndex, end_index = eIndex });
                        sIndex = eIndex + 1;
                    }
                }
            }

            // 合并重叠或连续的范围
            hitList = hitList.OrderBy(r => r.start_index).ToList();
            for (int i = 0; i < hitList.Count - 1; i++)
            {
                if (hitList[i].end_index >= hitList[i + 1].start_index - 1)
                {
                    hitList[i].end_index = Mathf.Max(hitList[i].end_index, hitList[i + 1].end_index);
                    hitList.RemoveAt(i + 1);
                    i--;
                }
            }

            var output = new List<RichContent>();
            // avatar 的词语命中 会出现  命中词语为  tall   ,但是语句是 hello 'tall'  导致识别出错 
            // 所以先去掉 ‘ ，要求命中的词语 正常也不应该出现 类似 'tall' 这种格式 ，发音无法确定是否有 ’
            // Regex reg = new Regex(@"^[A-Za-z']+$");
            Regex reg = new Regex(@"^[A-Za-z]+$");
            var curIdx = 0;
            var startIdx = 0;
            var isWordStart = false;

            int rangeIdx = 0;
            foreach (var str in text)
            {
                var isChanged = false;
                if (reg.IsMatch(str.ToString()))
                {
                    if (!isWordStart)
                        isChanged = true;
                    isWordStart = true;
                }
                else
                {
                    if (isWordStart)
                        isChanged = true;
                    isWordStart = false;
                }

                if ((isChanged || curIdx == text.Length) && curIdx - startIdx != 0)
                {
                    if (hitList.Count > rangeIdx &&
                        curIdx > hitList[rangeIdx].start_index && curIdx <= hitList[rangeIdx].end_index + 1)
                    {
                        var content = new RichContent()
                        {
                            Type = RichContentType.Text,
                            Color = overrideColor,
                            Content = text.Substring(startIdx, curIdx - startIdx),
                            IsLanguagePoint = true,
                            LanguagePointLast = curIdx == hitList[rangeIdx].end_index + 1
                        };
                        output.Add(content);
                        if (curIdx >= hitList[rangeIdx].end_index)
                            rangeIdx++;
                    }
                    else
                    {
                        var content = new RichContent()
                        {
                            Type = RichContentType.Text,
                            Color = overrideColor,
                            Content = text.Substring(startIdx, curIdx - startIdx)
                        };
                        output.Add(content);
                    }
                    startIdx = curIdx;
                }

                if (curIdx == text.Length - 1)
                {
                    string endStr = text.Substring(startIdx, text.Length - startIdx);
                    string pattern = @"[a-zA-Z]";
                    bool containsLetter = Regex.IsMatch(endStr, pattern);
                    //保证命中的单词最后的索引 和 curIdx是一个
                    bool lastChat = hitList.Count > 0 ? hitList[hitList.Count - 1].end_index == curIdx: false;
                    
                    var content2 = new RichContent()
                    {
                        Type = RichContentType.Text,
                        Color = overrideColor,
                        Content = endStr,
                        IsLanguagePoint = containsLetter && lastChat,
                        LanguagePointLast = containsLetter && lastChat,
                        IsLast = true
                        
                    };
                    output.Add(content2);
                }
                curIdx++;
            }
            return output;
        }

        public static List<RichContent> SplitFragmentHideShowStem(string text, Color overrideColor = default)
        {
            var curIdx = 0;
            var startIdx = 0;
            var isWordStart = false;
            Regex wordReg = new Regex(@"^[A-Za-z'\-]+$");
            List<RichContent> output = new List<RichContent>(50);
            output.Add(
                new RichContent()
                {
                    Type = RichContentType.Text,
                    Color = overrideColor,
                    Content = "        ",  /*八个别数了*/
                }
            );

            foreach (var str in text)
            {
                var isChanged = false;
                if (wordReg.IsMatch(str.ToString()))
                {
                    if (!isWordStart)
                        isChanged = true;
                    isWordStart = true;
                }
                else
                {
                    if (isWordStart)
                        isChanged = true;
                    isWordStart = false;
                }
            
                if ((isChanged || curIdx == text.Length) && curIdx - startIdx != 0)
                {
                    string words = text.Substring(startIdx, curIdx - startIdx);
                    var content = new RichContent()
                    {
                        Type = !wordReg.IsMatch(words) ? RichContentType.Text : RichContentType.MaskTranslate,
                        Color = overrideColor,
                        Content = words
                    };
                    
                    output.Add(content);
                    startIdx = curIdx;
                }
                
                if (curIdx == text.Length - 1)
                {
                    string words = text.Substring(startIdx, text.Length - startIdx);
                    var content2 = new RichContent()
                    {
                        Type = !wordReg.IsMatch(words) ? RichContentType.Text : RichContentType.MaskTranslate,
                        Color = overrideColor,
                        Content = words,
                        IsLast = true
                    };
                    
                    output.Add(content2);  
                }
                
                curIdx++;
            }

            if (output.Count == 0)
            {
                output.Add(
                    new RichContent()
                    {
                        Type = RichContentType.Text,
                        Color =  overrideColor,
                        Content = text,
                        IsLast = true
                    }
                );
            }
            
            return output;
        }
    }
}
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class TextQuestion : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "TextQuestion";
        public static string url => "ui://cmoz5osjpa962g";

        public Controller audio;
        public Controller ctrlImage;
        public GLoader imgQuestion;
        public GComponent tfQuestion;
        public GGroup grpOutside;
        public BtnAudio btnPlayAudio;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(TextQuestion));
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            audio = GetControllerAt(0);
            ctrlImage = GetControllerAt(1);
            imgQuestion = GetChildAt(1) as GLoader;
            tfQuestion = GetChildAt(2) as GComponent;
            grpOutside = GetChildAt(3) as GGroup;
            btnPlayAudio = GetChildAt(4) as BtnAudio;
        }
        public override void Dispose()
        {
            audio = null;
            ctrlImage = null;
            imgQuestion = null;
            tfQuestion = null;
            grpOutside = null;
            btnPlayAudio = null;

            base.Dispose();
        }
    }
}
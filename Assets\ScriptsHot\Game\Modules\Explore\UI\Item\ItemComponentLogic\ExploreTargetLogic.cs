﻿
using System.Collections.Generic;
using FairyGUI;
using Google.Protobuf.Collections;
using Msg.explore;
using ScriptsHot.Game.Modules.Explore;
using UnityEngine;

namespace UIBind.Explore.Item.ItemComponentLogic
{
    /// <summary>
    /// 描述
    /// </summary>
    public class ExploreTargetLogic:ItemComponentLogicBase
    {
        public ExploreTarget Com;
        private const int DescribeMaxWidth = 641;

        private List<ExploreTargetStepLogic> stepList = new List<ExploreTargetStepLogic>();

        private PB_Task_Detail _detail;
        
        private Dictionary<long,ExploreTargetStepInfo> _stepState = new Dictionary<long, ExploreTargetStepInfo>();

        /// <summary>
        /// 当前显示的子目标id
        /// </summary>
        private long _curStepGoalId = 0;
        
        private Color[] _backColors = new Color[5];
        private Color _effectColor = new Color(33/255f, 208/255f, 137/255f, 1f);  // #BCF1DC
        private Color _curBackColor = Color.white;

        public bool IsOpen = false;

        /// <summary>
        /// 目标数量
        /// </summary>
        private int _goalsCount;
        
        public override void Init()
        {
            base.Init();
            Com.btnOpen.onClick.Add(()=>
            {
                Com.ctrlDesc.selectedPage = "open";
                IsOpen = true;
                
                CLICK_PULL_DOWN_BUTTON dot = new CLICK_PULL_DOWN_BUTTON();
                dot.task_id = _controller.CurTaskId;
                dot.dialogue_id = _controller.CurEnterEntity.LogicEntity.DialogId;
                dot.dialogue_round = _controller.CurEnterEntity.LogicEntity.Round;
                DataDotMgr.Collect(dot);
                
                APPEAR_GOAL_BOARD dot2 = new APPEAR_GOAL_BOARD();
                dot2.goal_count = _goalsCount;
                dot2.task_id = _controller.CurTaskId;
                dot2.dialogue_id = _controller.CurEnterEntity.LogicEntity.DialogId;
                dot2.dialogue_round = _controller.CurEnterEntity.LogicEntity.Round;
                DataDotMgr.Collect(dot2);
                
            });
            Com.groupOpen.btnClose.onClick.Add(()=>
            {
                DoCloseState();
                IsOpen = false;
                
                CLICK_PULL_UP_BUTTON dot3 = new CLICK_PULL_UP_BUTTON();
                dot3.goal_count = _goalsCount;
                dot3.task_id = _controller.CurTaskId;
                dot3.dialogue_id = _controller.CurEnterEntity.LogicEntity.DialogId;
                dot3.dialogue_round = _controller.CurEnterEntity.LogicEntity.Round;
                DataDotMgr.Collect(dot3);
            });
            ExploreTargetStepLogic step1 = new ExploreTargetStepLogic();
            step1.Com = Com.groupOpen.step1;
            ExploreTargetStepLogic step2 = new ExploreTargetStepLogic();
            step2.Com = Com.groupOpen.step2;
            ExploreTargetStepLogic step3 = new ExploreTargetStepLogic();
            step3.Com = Com.groupOpen.step3;
            ExploreTargetStepLogic step4 = new ExploreTargetStepLogic();
            step4.Com = Com.groupOpen.step4;
            ExploreTargetStepLogic step5 = new ExploreTargetStepLogic();
            step5.Com = Com.groupOpen.step5;
            
            stepList.Add(step1);
            stepList.Add(step2);
            stepList.Add(step3);
            stepList.Add(step4);
            stepList.Add(step5);

            // 常规状态颜色
            _backColors[0] = new Color(0.976f, 0.769f, 0.976f, 1f);  // #F9C4F9
            _backColors[1] = new Color(0.725f, 0.867f, 0.996f, 1f);  // #B9DDFF
            _backColors[2] = new Color(0.843f, 0.792f, 1f, 1f);      // #D7CAFF
            _backColors[3] = new Color(1f, 0.941f, 0.651f, 1f);      // #FFF0A6
            _backColors[4] = new Color(0.698f, 0.937f, 0.976f, 1f);  // #B2EFF9

            Com.imgTitle.color = Color.black;
        }
        public void InitDescribe(PB_Task_Detail info,long entityId)
        {
            _detail = info;
            int targetIndex = (_controller.Model.GetDescribeBack(entityId) - 1) >= 5 ? 4 :_controller.Model.GetDescribeBack(entityId) - 1;
            _curBackColor =  _backColors[targetIndex];
            Com.imgBack.color = _curBackColor;
            Com.ctrl.selectedIndex = 0;
            Com.ctrl.selectedPage = "type1";

            Com.txtDescMain.text = info.desc;
            Com.groupOpen.txtDescMain.text = info.desc;

            Com.groupOpen.imgBack.color = _curBackColor;
            Com.groupOpen.step4.com.visible = false;
            Com.groupOpen.step5.com.visible = false;
            
            //处理子目标
            for (int i = 0; i < stepList.Count; i++)
            {
                stepList[i].Com.com.visible = false;
            }

            _goalsCount = info.rolePlayDetail.goals.Count;
            int completeCount = 0;
            for (int i = 0; i < _goalsCount; i++)
            {
                PB_Task_GoalItem goalItem = info.rolePlayDetail.goals[i];
                
                ExploreTargetStepInfo goalInfo = new ExploreTargetStepInfo();
                goalInfo.GoalId = goalItem.goalId;
                goalInfo.Index = i;
                goalInfo.Num = goalItem.no;
                goalInfo.IsComplete = goalItem.isCompleted;
                if (goalItem.isCompleted)
                {
                    completeCount++;
                }

                _stepState[goalItem.goalId] = goalInfo;
                if (stepList.Count > i)
                {
                    stepList[i].Info = goalItem;
                    stepList[i].Com.com.visible = true;
                    stepList[i].Com.ctrl.selectedPage = goalItem.isCompleted ? "ok" : "no";
                    stepList[i].Com.txtDesc.text = goalItem.goalDesc;
                }
            }

            string title = I18N.inst.MoStr("ui_explore_title_goal");
            Com.txtTypeName.text = $"{title}  {completeCount}/{_goalsCount}";
            ShowCloseMain();
        }

        /// <summary>
        /// 显示关闭状态时候的 main
        /// </summary>
        public void ShowCloseMain()
        {
            this.Com.ctrlDesc.selectedPage = "closeMain";
        }
        
        /// <summary>
        /// 显示关闭状态时候的 step
        /// </summary>
        /// <param name="index"></param>
        public void ShowCloseStep(int index)
        {
            IsOpen = false;
            _curStepGoalId = _detail.rolePlayDetail.goals[index].goalId;
            Com.ctrlDesc.selectedPage = "closeStep";
            Com.txtDesc.text = _detail.rolePlayDetail.goals[index].goalDesc;
            Com.ctrlCloseStep.selectedPage = _detail.rolePlayDetail.goals[index].isCompleted ? "ok" : "no";
        }

        public void CompleteStep(RepeatedField<global::Msg.explore.PB_Explore_TaskGoalStatusChange> changeInfos)
        {
            bool updateCur = false;
            foreach (var stepInfo  in changeInfos)
            {
                if (stepInfo.goalId == _curStepGoalId)
                {
                    updateCur = true;
                    //播放 对号动画
                    Com.ctrlCloseStep.selectedPage = "ok";
                    Com.spineOk.spineAnimation.AnimationState.SetAnimation(0, "1", false);
                }
                _stepState[stepInfo.goalId].IsComplete = stepInfo.afterStatus == PB_Explore_TaskGoalStatus.EO_TASK_GOAL_STATUS_FINISHED;

                if (_stepState[stepInfo.goalId].IsComplete)
                {
                    CUT_SUBGOAL_FINISHED dot = new CUT_SUBGOAL_FINISHED();
                    dot.task_id = _controller.CurTaskId;
                    dot.dialogue_id = _controller.CurEnterEntity.LogicEntity.DialogId;
                    dot.dialogue_round = _controller.CurEnterEntity.LogicEntity.Round;
                    dot.subgoal_id = stepInfo.goalId;
                    dot.subgoal_index = _stepState[stepInfo.goalId].Num;
                    DataDotMgr.Collect(dot);
                }

            }

            int completeCount = 0;
            foreach (var kv in _stepState)
            {
                if (kv.Value.IsComplete)
                {
                    completeCount++;
                }
            }
            string title = I18N.inst.MoStr("ui_explore_title_goal");
            Com.txtTypeName.text = $"{title}  {completeCount}/{_goalsCount}";
            
            //更新打开状态的子目标
            UpdateOpenSteps();
            if (IsOpen)
            {
                //直接修改 close的数据
                DoCloseState();
                
                //title背景动画
                TweenToGreen(Com.imgTitle,Color.black,_effectColor);
            }
            else
            {
                //播放背景动画
                PlayBackEffect();
                //改变当前显示 
                if (updateCur)
                {
                    TimerManager.instance.RegisterTimer((a) =>
                    {
                        DoCloseState();
                    }, ExploreConst.RolePlayStepCompleteTime);
                }
            }
        }

        /// <summary>
        /// 处理 close状态显示
        /// </summary>
        private void DoCloseState()
        {
            bool mask = false;
            foreach (var kv in _stepState)
            {
                if (kv.Value.IsComplete == false)
                {
                    mask = true;
                    ShowCloseStep(kv.Value.Index);
                    break;
                }
            }

            if (!mask)
            {
                ShowCloseMain();
            }
        }

        /// <summary>
        /// 更新打开状态的子目标
        /// </summary>
        private void UpdateOpenSteps()
        {
            for (int i = 0; i < stepList.Count; i++)
            {
                ExploreTargetStepLogic logic = stepList[i];
                if (logic.Com != null && logic.Info != null)
                {
                    if(_stepState.TryGetValue(logic.Info.goalId, out var stepInfo))
                    {
                        logic.Com.ctrl.selectedPage = stepInfo.IsComplete ? "ok" : "no";
                    }
                }
            }
        }

        private void PlayBackEffect()
        {
            SoundManger.instance.PlayUI("combo");
            TweenToGreen(Com.imgBack,_curBackColor,_effectColor);
            TweenToGreen(Com.imgTitle,Color.black,_effectColor);
        }
        private void TweenToGreen(GGraph imgBack,Color fromColor,Color toColor)
        {
            GTween.To(fromColor, toColor, 1f) 
                .SetTarget(imgBack) // 设置目标对象
                .OnUpdate((GTweener tweener) =>
                {
                    // 在缓动过程中更新颜色
                    imgBack.color = tweener.value.color;
                })
                .OnComplete(() =>
                {
                    Debug.Log("颜色缓动完成 绿");
                    TweenToBack(imgBack,toColor,fromColor);
                });
        }
        private void TweenToBack(GGraph imgBack,Color fromColor,Color toColor)
        {
            GTween.To(fromColor, toColor, 1f) 
                .SetTarget(imgBack) // 设置目标对象
                .OnUpdate((GTweener tweener) =>
                {
                    // 在缓动过程中更新颜色
                    imgBack.color = tweener.value.color;
                })
                .OnComplete(() =>
                {
                    Debug.Log("颜色恢复完毕");
                });
        }
    }
    
    public class ExploreTargetStepInfo
    {
        public int Index;
        public int Num;
        public bool IsComplete;
        public long GoalId;
    }
    
}
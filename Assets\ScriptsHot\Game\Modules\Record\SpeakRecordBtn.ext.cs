using System;
using FairyGUI;
using ScriptsHot.Game.Modules.Setting;
using Game.Modules.Record;

namespace UIBind.Record
{
    public partial class SpeakRecordBtn : RecordBtnBase
    {
        public enum SpeakRecordState
        {
            None = 3,
            Recording = 1,    // speak收音
            Repeat = 0,       // speak重说
            Cancelled = 2,           // speak 取消录音
            Continue = 4           // 说得好，可以继续
        }

        private SettingModel _settingModel => ModelManager.instance.GetModel<SettingModel>(ModelConsts.Setting);

        private bool _isFollow = false;
        private string referenceText = "";    
        public bool IsReceivingEvents { get ; private set ; } = true;

        public event Action onContinue;

        public SpeakRecordBtn()
        {
            onAddedToStage.Add(OnAddedToStage);
            onRemovedFromStage.Add(OnRemovedFromStage);
        }

        private void OnAddedToStage(EventContext context)
        {
            ChangeState(SpeakRecordState.None);
            RegisterEvents();
        }

        private void OnRemovedFromStage(EventContext context)
        {
            UnregisterEvents();
        }

        /// <summary>
        /// Register UI events related to speak recording
        /// </summary>
        public void RegisterEvents()
        {
            btnSpeakEndVad.onClick.Add(OnClickSpeakEnd);
            btnSpeakCancel.onClick.Add(ClickSpeakCancel);
            btnSpeakReapeatBtn.onClick.Add(ClickSpeakRepeat);
            btnSmallRepeat.onClick.Add(ClickSpeakRepeat);
            btnSpeakMicBtn.onClick.Add(ClickSpeakRepeat);
            btnContinue.onClick.Add(ClickContinue);
        }

        public void UnregisterEvents()
        {
            btnSpeakEndVad.onClick.Remove(OnClickSpeakEnd);
            btnSpeakCancel.onClick.Remove(ClickSpeakCancel);
            btnSpeakReapeatBtn.onClick.Remove(ClickSpeakRepeat);
            btnSmallRepeat.onClick.Remove(ClickSpeakRepeat);
            btnSpeakMicBtn.onClick.Remove(ClickSpeakRepeat);
            btnContinue.onClick.Remove(ClickContinue);
        }

        private void ChangeState(SpeakRecordState state)
        {
            ctlStatus.selectedIndex = (int)state;
        }

        public void ShowWaitingRecord()
        {             
            ChangeState(SpeakRecordState.Cancelled);
        }

        #region 按钮响应事件
        private void OnClickSpeakEnd()
        {
            RecordEventManager.Instance.DispatchRecordStop();
        }

        private void ClickSpeakCancel()
        {
            RecordEventManager.Instance.DispatchRecordCancel();
        }

        private void ClickSpeakRepeat()
        {
            RecordEventManager.Instance.DispatchRecordStart(referenceText);
        }

        private void ClickContinue()
        {
            onContinue?.Invoke();
        }

        public void SetReferenceText(string referenceText)
        {
            RecordEventManager.Instance.ShowLog($"SetReferenceText: {referenceText}");
            this.referenceText = referenceText;
        }
        #endregion

        #region 处理UI变化
        public void StartRecording()
        {
            ChangeState(SpeakRecordState.Recording);
            if (_isFollow)
            {
                grpTips2.visible = false;
            }
        }

        public void StopRecording(bool canContinue, bool canRetry = true)
        {
            if (canContinue)
            {
                ChangeState(SpeakRecordState.Continue);
                btnSmallRepeat.visible = canRetry;
            }
            else
            {
                ChangeState(SpeakRecordState.Repeat);
            }
        }

        public void CancelRecording()
        {
            ChangeState(SpeakRecordState.Cancelled);
        }
        #endregion
    }
}
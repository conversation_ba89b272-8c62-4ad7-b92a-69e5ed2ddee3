/*
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/07 21:36:33 星期二
# 功能：Nothing
****************************************************
*/

using System;
using System.Collections.Generic;
using CommonUI;
using FairyGUI;
using Game;
using Modules.DataDot;
using Msg.basic;
using Msg.economic;
using Msg.incentive;
using UIBind.LearnPath;
using UIBind.Shop;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Shop
{
    [TransitionUI(CutInEffect.Popup)]
    public class ShopUI : ShopUIBase<UIBind.Shop.ShopPanel>, IBaseUIUpdate
    {
        public ShopUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top; //主UI层

        private ShopModel _shopModel => GetModel<ShopModel>(ModelConsts.Shop);
        
        private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);

        private CurrencyModel _currencyModel => GetModel<CurrencyModel>(ModelConsts.CurrencyController);

        private ShopController _controller => GetController<ShopController>(ModelConsts.Shop);
        
        private List<ShopItemCom> _list = new List<ShopItemCom>();
        
        private Dictionary<long, MyItemsCom> _myItems = new();

        protected override bool isFullScreen { get; } = true;

        private float _lastUpdateTime;

        public bool showPromotionStep3 = true;
        
        //250410 根据业务调整下面按钮固定为月，上面按钮根据地区设置为年或者季，需要动态替换
        private string _topMemberTypeName;
        private PB_SubscriptionInfo _topSubscriptionInfo;

        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(this.ui.btnExit.onClick, OnBtnExitClicked);
            AddUIEvent(this.ui.comShopContent.btnMore.onClick, OnBtnMoreClicked);
            //AddUIEvent(ui.btnServiceDetail.onClick, OnBtnServiceDetailClicked);
            //AddUIEvent(ui.btnPrivate.onClick, OnBtnPrivacyClicked);
            AddUIEvent(ui.comShopContent.btnFreePlan.onClick, OnBtnFreePlanClicked);
            //AddUIEvent(ui.tfRevertPurchase.onClick, OnBtnRestorePurchaseClicked);
            AddUIEvent(ui.comShopContent.btnUpgrade.onClick, OnBtnUpgradeClicked);
            AddUIEvent(ui.comShopContent.btnSubscribe.onClick, OnBtnSubscribeClicked);
            AddUIEvent(ui.comShopContent.btnUpdate.onClick, OnBtnUpdateClicked);
            // AddUIEvent(ui.comShopContent.btnUpgrade.onClick, (I) => GetUI<PlanPromotionStep1UI>(UIConsts.PlanPromotionStep1UI).Show());
            InitUI();
        }

        private void InitI18N()
        {
            ui.tfTitle.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_7");
            ui.comShopContent.tfSubscribeDesc1.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_9");
            ui.comShopContent.tfSubscribeDesc1_lifetime.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_9");            
            
            ui.comShopContent.tfSubscribeDesc10.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_9");
            ui.comShopContent.tfSubscribeDesc10_lifetime.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_9");        
            
            ui.comShopContent.lifetimeTxt1.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_lifetime_1");
            ui.comShopContent.lifetimeTxt2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_lifetime_1");
            ui.comShopContent.lifetimeTxt3.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_lifetime_1");
            
            ui.comShopContent.tfSubscribeDesc5.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_2");
            ui.comShopContent.tfSubscribeDesc9.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_2");
            ui.comShopContent.tfSubscribeDesc6.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_2");
            ui.comShopContent.tfSubscribeDesc6_lifetime.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_2");
            ui.comShopContent.tfSubscribeDesc13.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_2");
            // ui.comShopContent.tfSubscribeDesc8.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_shop_desc_14", _shopModel.GetRecommendPlanDays());
            ui.comShopContent.tfDiamond.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_5");
            ui.comShopContent.tfDiamondDesc.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_6");
            ui.comShopContent.tfMyItemsTitle.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_title_my_items");

            ui.comShopContent.tfMore.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_12");
            // ui.comShopContent.tfBtnFreePlan.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_13");
            ui.comShopContent.tfBtnUpgrade.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_btn_upgrade");
            //ui.tfServiceDetail.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_service");
            //ui.tfPrivate.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_privacy");
            //ui.tfRevertPurchase.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_restore");
            // ui.comShopContent.tfCurPlan.SetKey("ui_shop_desc_12");
        }

        private void InitUI()
        {
            CreateItems();
        }

        protected override void OnShow()
        {
            // 250327 中国版过审修改
            if (AppConst.IsCN)
            {
                Hide();
                return;
            }
            Refresh();
            InitI18N();

            DotAppearDiamondShopPage dot = new DotAppearDiamondShopPage();
            DataDotMgr.Collect(dot);
        }

        private void CreateItems()
        {

            //var rkStr = AppRegionInfo.GetCurrRegionKeyAsStr();
            //var currencyType = OsFuncAdapter.Ins.GetCountryCode();

            //if (Cfg.T.TBShopItems.DataList.Find(s => s.isOnlineItem && s.regionKey == rkStr &&s.currencyType == currencyType) == null)
            //    currencyType = "DEFAULT";
            _list.Clear();
            foreach (var item in _shopModel.InAppPurchaseInfoDic)
            {
                //if (item.isOnlineItem && item.regionKey == rkStr && item.currencyType == currencyType)
                //{
                    CreateShopItem(item.Value);
                //}
            }
        }

        private void CreateShopItem(PB_InAppPurchaseInfo info)
        {
            ShopItemCom shopItem = new();
            shopItem.Construct(UIPackage.GetByName("Shop").CreateObject("ShopItemCom").asCom);
            _list.Add(shopItem);
            //PB_InAppPurchaseInfo info = _shopModel.InAppPurchaseInfoDic[item.ID.ToString()];
            shopItem.tfPrice.text = info.price_in_display;
            shopItem.tfNum.text = info.count.ToString();
            shopItem.imgLoader.url = info.icon_path;
            shopItem.tfOldPrice.text = info.origin_price_in_display;
            shopItem.tfTime.text = info.promoted_purchase_remains_days.ToString();
            ui.comShopContent.comShopList.listShopItems.AddChildAt(shopItem.com, ui.comShopContent.comShopList.listShopItems.numChildren);
            AddUIEvent(shopItem.btnItem.onClick, (I) =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
                PurchasingManager.instance.StartPurchasing(info.product_id, PB_ProductType.PB_ProductType_Diamond, OnBuySuccess, OnBuyFail);
                AFHelper.Click_Diamond_purchase(info.product_id);                
                //新埋点：用户在钻石商店第一页，点击下方购买
                DataDotDiamondPurchase dot = new DataDotDiamondPurchase();
                
                
                dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
                dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
                dot.Diamond_amount = info.count;
                if (float.TryParse(info.price_in_display, out var tmp))
                {
                    dot.Diamond_price = tmp;
                }
                dot.price_currency = OsFuncAdapter.Ins.GetCountryCode();
                DataDotMgr.Collect(dot);
                
               
                
                product_id = info.product_id;
                product_type = PB_ProductType.PB_ProductType_Diamond.ToString();
            });
        }

        public void Refresh()
        {
            var state = "Subscribe";
            var memberInfo = _mainModel.incentiveData.homepage_economic_info.member_info;
            if(memberInfo.member_type == MemberType.LifetimePremium)
                state = "LifitimePremium";
            else if (memberInfo.SubscribeStatus == SubscribeStatus.Subscribing && memberInfo.member_type == MemberType.MonthPremium && memberInfo.is_member)
                state = "Upgrade";
            else if (memberInfo.SubscribeStatus == SubscribeStatus.Subscribing &&
                     (memberInfo.member_type == MemberType.YearPremium ||
                      memberInfo.member_type == MemberType.QuarterPremium)&& 
                     memberInfo.is_member)
                state = "Premium";
            else if (memberInfo.SubscribeStatus == SubscribeStatus.FreeTrial && memberInfo.is_member)
                state = "Premium";
            else if(memberInfo.SubscribeStatus == SubscribeStatus.UnSubscribe)
                state = "Free";
            else if(memberInfo.SubscribeStatus == SubscribeStatus.Retrying)
                state = "Update";

            if (_shopModel.TryGetMemberTypeByName(ShopController.MEMBER_TYPE_QUARTER, out var cfg_quarterly))
            {
                _topMemberTypeName = ShopController.MEMBER_TYPE_QUARTER;
                _topSubscriptionInfo = cfg_quarterly;
            }
            else if (_shopModel.TryGetMemberTypeByName(ShopController.MEMBER_TYPE_YEAR, out var cfg_yearly))
            {
                _topMemberTypeName = ShopController.MEMBER_TYPE_YEAR;
                _topSubscriptionInfo = cfg_yearly;
            }
            
            SetSectionMemberCard(state);
            CreateMyItems();
            RefreshMyItems();
            
            ui.comShopContent.FreeIsLifetime.selectedIndex = _shopModel.IsLifeTime ? 1 : 0;
            
			PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.diamond_shop;
        }

        private void SetSectionMemberCard(string state)
        {
            var memberInfo = _mainModel.incentiveData.homepage_economic_info.member_info;
            ui.tfDiamondNum.text = _currencyModel.GetEconomicInfo(EconomicType.Diamond).CurNum.ToString();
            // ui.comShopContent.State.SetSelectedPage(state);
            //             var prefix_cur_plan =
            //                 memberInfo.member_type == MemberType.MonthPremium || memberInfo.member_type == MemberType.YearPremium
            //         ? I18N.inst.MoStr("ui_plan_common_premium_title")
            //         : I18N.inst.MoStr("ui_plan_common_lite_title");
            ui.comShopContent.State.SetSelectedPage(state);
            var prefix_cur_plan = "premium";
                        
            // var prefix_cur_time = memberInfo.member_type == MemberType.YearPremium || memberInfo.member_type == MemberType.YearLite
            //     ? I18N.inst.MoStr("ui_plan_common_btn_yearly")
            //     : I18N.inst.MoStr("ui_plan_common_btn_monthly");
            var prefix_cur_time = memberInfo.member_type switch
            {
                MemberType.MonthPremium => "monthly",
                MemberType.YearPremium => "yearly",
                MemberType.MonthLite => "monthly",
                MemberType.YearLite => "yearly",
                MemberType.QuarterPremium => "quarterly",
                _ => "monthly"
            };
            
            var cfg = _shopModel.GetMemberTypeByName(_topMemberTypeName);
            //var cfg_cur = _shopModel.GetMemberTypeByName("premium_monthly");
            //    //_shopModel.GetMemberTypeByName($"{prefix_cur_plan.ToLower()}_{prefix_cur_time.ToLower()}");
            //if (_shopModel.memberInfo.member_type == MemberType.MonthPremium || _shopModel.memberInfo.member_type == MemberType.YearPremium ||
            //    _shopModel.memberInfo.member_type == MemberType.MonthLite || _shopModel.memberInfo.member_type == MemberType.YearLite)
            var cfg_cur = _shopModel.GetMemberTypeByName($"{prefix_cur_plan.ToLower()}_{prefix_cur_time.ToLower()}");
            if ((memberInfo.member_type == MemberType.MonthPremium || 
                 memberInfo.member_type == MemberType.YearPremium ||
                 memberInfo.member_type == MemberType.QuarterPremium ||
                 memberInfo.member_type == MemberType.MonthLite || memberInfo.member_type == MemberType.YearLite) && memberInfo.is_member)
            {
                ui.comShopContent.tfCurPlan.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_shop_desc_1", prefix_cur_plan, prefix_cur_time);
                ui.comShopContent.tfCurPlan2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_shop_desc_1", prefix_cur_plan, prefix_cur_time);
                ui.comShopContent.tfSubscribeEndtime.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_shop_desc_4", _shopModel.GetMemberExpiredTime().ToString(I18N.inst.MoStr("ui_plan_promotion_desc20")));
                ui.comShopContent.tfSubscribeEndtime2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_shop_desc_4", _shopModel.GetMemberExpiredTime().ToString(I18N.inst.MoStr("ui_plan_promotion_desc20")));
            }


            ui.comShopContent.tfSubscribeDesc4.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_shop_desc_10", cfg.per_day_price_in_display);
            ui.comShopContent.tfSubscribeDesc2.visible = false;
            ui.comShopContent.yearLineImg.visible = false;
            ui.comShopContent.tfSubscribeDesc2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_shop_desc_11", cfg.origin_price_in_display);
            ui.comShopContent.tfSubscribeDesc3.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_shop_desc_11", cfg.price_in_display);
            if (cfg_cur!= null)
            {
                ui.comShopContent.tfSubscribeDesc12.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_shop_desc_3", cfg_cur.per_day_price_in_display,
                cfg.per_day_price_in_display);
            }

            if (state == "Upgrade")
            {
                ui.comShopContent.spBtnUpgrade.spineAnimation.AnimationState.ClearListenerNotifications();
                ui.comShopContent.spBtnUpgrade.spineAnimation.AnimationState.SetAnimation(0, "b1", false).Complete += (t) =>
                {
                    ui.comShopContent.spBtnUpgrade.spineAnimation.AnimationState.SetAnimation(0, "b2", true);
                };

                if (_shopModel.IsLifeTime)
                {
                    ui.comShopContent.tfBtnFreePlan.text = string.Format(I18N.inst.MoStr("ui_shop_lifetime_4"));
                }
                else
                {
                    ui.comShopContent.tfBtnFreePlan.text = string.Format(I18N.inst.MoStr("ui_plan_instruction_desc12"),
                        prefix_cur_plan, prefix_cur_time);
                }
                
                ui.comShopContent.tfSubscribeDesc12.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_shop_desc_3", cfg_cur.per_day_price_in_display,
                cfg.per_day_price_in_display);
                ui.comShopContent.tfSubscribeEndtime2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_plan_instruction_desc13", _shopModel.GetMemberExpiredTime().ToString(I18N.inst.MoStr("ui_plan_promotion_desc20")));

            }
            else if (state == "Update")
            {
                ui.comShopContent.tfSubscribeDesc15.SetLanguage(LanguageType.MotherTongue, refresh: false).SetKey("ui_shop_desc_plan_retry");
                ui.comShopContent.tfBtnUpdate.SetLanguage(LanguageType.MotherTongue, refresh: false).SetKey("ui_shop_btn_plan_retry");
            }
            else if (state == "Subscribe")
            {
                if (_shopModel.IsLifeTime)
                {
                    ui.comShopContent.tfBtnSubscribe.text = I18N.inst.MoStr("ui_shop_lifetime_4");
                }
                else
                {
                    var _key = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.FreeTrialCanceled || _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Canceled
                        ? "ui_plan_common_btn_keep"
                        : "ui_plan_common_btn_subscribe";
                    ui.comShopContent.tfBtnSubscribe.text = I18N.inst.MoStr(_key);
                }
            }
            else if (state == "Free")
            {
                if (_shopModel.IsLifeTime)
                {
                    ui.comShopContent.tfBtnFreePlan.text = I18N.inst.MoStr("ui_shop_lifetime_4");
                }
                else
                {
                    bool isDiscount = GameEntry.ShopC.ShopModel.IsDiscountType;
                    if (isDiscount)
                    {
                        ui.comShopContent.tfSubscribeDesc8.text = string.Format(I18N.inst.MoStr("ui_shop_discount_03")  , GameEntry.ShopC.ShopModel.GetMaxDiscountStr());
                        ui.comShopContent.tfBtnFreePlan.text = string.Format(I18N.inst.MoStr("ui_shop_discount_04") , GameEntry.ShopC.ShopModel.GetMaxDiscountStr()); 
                    }
                    else
                    {
                        // ui.comShopContent.tfSubscribeDesc8.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_shop_desc_14", _shopModel.GetRecommendPlanDays());
                        // ui.comShopContent.tfBtnFreePlan.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_desc_13");
                        ui.comShopContent.tfSubscribeDesc8.text = string.Format(I18N.inst.MoStr("ui_shop_desc_14")  , _shopModel.GetRecommendPlanDays());  
                        ui.comShopContent.tfBtnFreePlan.text = string.Format(I18N.inst.MoStr("ui_shop_desc_13"));  
                    }
                }
            }


            ui.tfDiamondNum.text = _currencyModel.GetEconomicInfo(EconomicType.Diamond).CurNum.ToString();
        }

        private void CreateMyItems()
        {
            foreach (var pair in _myItems)
            {
                pair.Value.com.Dispose();
            }
            _myItems.Clear();

            var item2 = CreateMyItem(*********);
            ui.comShopContent.listMyItems.AddChild(item2.com);
            var item = CreateMyItem(*********);
            ui.comShopContent.listMyItems.AddChild(item.com);
            var item3 = CreateMyItem(*********);
            ui.comShopContent.listMyItems.AddChild(item3.com);
        }

        private MyItemsCom CreateMyItem(long itemId)
        {
            var itemCfg = Cfg.T.TBItemTable.Get(itemId);
            if (itemCfg == null)
                return null;
            MyItemsCom myItemsCom = new();
            myItemsCom.Construct(UIPackage.GetByName("Shop").CreateObject("MyItemsCom").asCom);
            ui.comShopContent.listMyItems.AddChild(myItemsCom.com);
            myItemsCom.tfNum.SetKey(itemCfg.itemName).SetLanguage(LanguageType.MotherTongue, refresh: true);
            myItemsCom.imgLoader.url = itemCfg.iconMPath;
            myItemsCom.tfCnt.visible = itemCfg.holdMaxCnt != -1;
            if (!LocalCfgMgr.instance.GetGlobal("new_item_showed_" + itemCfg.ID).Equals("TRUE") && itemCfg.holdMaxCnt > 0)
            {
                LocalCfgMgr.instance.SetGlobal("new_item_showed_" + itemCfg.ID, "TRUE");
                myItemsCom.tfNew.SetKey("common_new_item");
                myItemsCom.grpNew.visible = true;
            }
            else
            {
                myItemsCom.grpNew.visible = false;
            }
            _myItems.Add(itemCfg.ID, myItemsCom);
            myItemsCom.com.onClick.Add(() =>
            {
                GetUI(UIConsts.ShopMyItemUI).Show(itemCfg);
                ClickDiamondShopItem dot = new ClickDiamondShopItem();
                switch (itemId)
                {
                    case *********:
                        dot.Item_type = "XP_boost";
                        break;
                    case *********:
                        dot.Item_type = "XP_boost";
                        break;
                    case *********:
                        dot.Item_type = "XP_boost";
                        break;
                    case *********:
                        dot.Item_type = "Streak_freeze";
                        StreakFreezePurchaseAppear model = new StreakFreezePurchaseAppear();
                        _shopModel.TryGetItemInfo(itemCfg.ID.ToString(), out var info);
                        model.Inventory = info.amount;
                        DataDotMgr.Collect(model);
                        break;
                    case *********:
                        dot.Item_type = "Unlimited_energy";
                        break;
                    default:
                        break;
                }
                
                DataDotMgr.Collect(dot);
                
            });
            return myItemsCom;

        }

        public void RefreshMyItems()
        {
            foreach (var pair in _myItems)
            {
                if (_shopModel.TryGetItemInfo(pair.Key.ToString(), out var info) && !Cfg.T.TBItemTable.Get(pair.Key).isPeriodItem)
                {
                    pair.Value.tfCnt.text = "x" + info.amount.ToString();
                }
            }
        }
        
        
        public void Update(int interval)
        {
            if (Time.time - _lastUpdateTime > 0.2f)
            {
                _lastUpdateTime = Time.time;

                if (_list.Count > 0)
                {
                    int index = 0;
                    foreach (var item in _shopModel.InAppPurchaseInfoDic)
                    {
                        ShopItemCom ShopItem = _list[index];
                        index++;
                        UpdateSaleTime(ShopItem, item.Value.promoted_purchase_remains_days);
                    }
                }

                foreach (var item in _myItems)
                {
                    if (item.Key == ********* && TryGetXpItemInfo(out var info))
                    {
                        SetPeriodItem(info, item.Value);
                    }
                    else if (item.Key == ********* && _shopModel.TryGetItemInfo(*********.ToString(), out var staminaInfo1) &&
                             _mainModel.incentiveData.homepage_economic_info.member_info.is_member)
                    {
                        SetPeriodItem(staminaInfo1, item.Value, true);
                    }
                    else if (item.Key == ********* && _shopModel.TryGetItemInfo(*********.ToString(), out var staminaInfo2) && staminaInfo2.amount > 0 &&
                             staminaInfo2.amount > TimeExt.serverTimestamp)
                    {
                        SetPeriodItem(staminaInfo2, item.Value);
                    }
                    else
                    {
                        item.Value.grpOnSale.visible = false;
                        item.Value.tfCnt.visible = true;
                        ;
                    }
                }
                
            }
        }

        private bool TryGetXpItemInfo(out PB_UserMerchandiseInfo info)
        {
            info = null;
            if (_shopModel.TryGetItemInfo(*********.ToString(), out var info1) && info1.amount > 0 && info1.amount > TimeExt.serverTimestamp)
            {
                info = info1;
                return true;
            }
            else if (_shopModel.TryGetItemInfo(*********.ToString(), out var info2) && info2.amount > 0 && info2.amount > TimeExt.serverTimestamp)
            {
                info = info2;
                return true;
            }
            else if (_shopModel.TryGetItemInfo(*********.ToString(), out var info3) && info3.amount > 0 && info3.amount > TimeExt.serverTimestamp)
            {
                info = info3;
                return true;
            }

            return false;
        }

        private string GetItemID(PB_RewardMateriaType type)
        {
            switch (type)
            {
                case PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost:
                    return "i0001";
                case PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_1_5:
                    return "i0001";
                case PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_2:
                    return "i0001";
                case PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_3:
                    return "i0001";
            }

            return null;
        }

        private string GetXPBoostRate(string type)
        {
            if (type.Equals(PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_1_5.ToString()))
                return "1.5";
            if (type.Equals(PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_2.ToString()))
                return "2";
            if (type.Equals(PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_3.ToString()))
                return "3";

            return "";
        }

        private void SetPeriodItem(PB_UserMerchandiseInfo info, MyItemsCom item, bool showAsUnlimited = false)
        {
            if (showAsUnlimited)
            {
                item.tfTime.text = I18N.inst.MoStr("ui_mainHeaderBar_desc_unlimited");
                item.grpOnSale.visible = true;
                item.tfCnt.visible = false;
            }
            else if (info.amount > 0 && info.amount >= TimeExt.serverTimestamp)
            {
                var timeSpan = DateTimeOffset.FromUnixTimeSeconds(info.amount / 1000).DateTime - TimeExt.serverTime;
                if (timeSpan.Hours > 0)
                {
                    item.tfTime.text = timeSpan.ToString();
                }
                else
                {
                    item.tfTime.text = timeSpan.ToString().Substring(3);
                }

                item.grpOnSale.visible = true;
                item.tfCnt.visible = false;
            }
            else
            {
                item.grpOnSale.visible = false;
                item.tfCnt.visible = true;
            }
            item.grpOnSale.SetBoundsChangedFlag();
            item.grpOnSale.EnsureBoundsCorrect();
        }

        private void UpdateSaleTime(ShopItemCom item,long endTimeMillis)
        {
            


            // 计算剩余时间（秒）
            long remainingMillis = endTimeMillis - TimeExt.serverTimestamp / 1000;
            if (remainingMillis < 0)
            {
                item.grpOnSale.visible = false;
                return;
            }
            item.grpOnSale.visible = true;
            // 计算天、小时、分钟
            long totalSeconds = remainingMillis;
            long days = totalSeconds / (24 * 3600);
            long hours = (totalSeconds % (24 * 3600)) / 3600;
            long minutes = (totalSeconds % 3600) / 60;
            if (days > 0)
            {
                item.tfTime.text = days + " Days";
             
            }
            else if (hours > 0)
            {
                item.tfTime.text = hours + " hours";
                
            }
            else
            {
                item.tfTime.text = minutes + " Mins";
            }
            item.grpOnSale.SetBoundsChangedFlag();
            item.grpOnSale.EnsureBoundsCorrect();
        }


        private void OnBtnFreePlanClicked()
        {
            bool isLifetime = GameEntry.ShopC.ShopModel.IsLifeTime;
            bool isDiscount = GameEntry.ShopC.ShopModel.IsDiscountType;
            if (isDiscount || isLifetime)
            {
                GetUI<PlanSelectUI>(UIConsts.PlanSelectUI).Show(3);
            }
            else
            {
                GetUI<SpeakPlanPromotionStep3UI>(UIConsts.SpeakPlanPromotionStep3UI).Show(3);
            }

            var dot = new DotClickNonMemberDiamondShopPageTrial();
            DataDotMgr.Collect(dot);
        }

        private void OnBtnMoreClicked()
        {
            GetUI<PlanInstructionUI>(UIConsts.PlanInstructionUI).Show();
            // Hide();
            //新埋点：用户在钻石商店第一页，点击更多套餐
            DataDotDiamondMorePackage dot = new DataDotDiamondMorePackage();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            DataDotMgr.Collect(dot);
        }

        private void OnBtnUpgradeClicked()
        {
            var cfg = _shopModel.GetMemberTypeByName(_topMemberTypeName);
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            PurchasingManager.instance.StartPurchasing(cfg.product_id, PB_ProductType.PB_ProductType_Subscribe, OnUpgradeSuccess, OnBuyFail);
            DataDotSubscriptionPurchase dot = new DataDotSubscriptionPurchase();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            dot.clicked_member_type = "YearPremium";
            dot.subscribe_price = cfg.price_in_cents;
            dot.price_currency = AppStoreInfoProvider.GetCountryCode();
            DataDotMgr.Collect(dot);
            
           
            
            product_id = cfg.product_id;
            product_type = PB_ProductType.PB_ProductType_Subscribe.ToString();
        }
        
        private void OnUpgradeSuccess()
        {
            Hide();
            OnBuySuccess();
        }

        private void OnBtnExitClicked()
        {
            //新埋点：用户点击钻石商店退出
            DataDotDiamondQuit dot = new DataDotDiamondQuit();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            DataDotMgr.Collect(dot);
            Hide();
            _controller.JumpToOpenShopSoure();
        }

        private void OnBtnSubscribeClicked()
        {
            var cfg = _shopModel.GetMemberTypeByName(_topMemberTypeName);
            //新埋点：用户在钻石商店第一页，点击订阅
            DataDotDiamondSubscribe dot = new DataDotDiamondSubscribe();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            dot.subscribe_price = cfg.price_in_cents;
            dot.price_currency = OsFuncAdapter.Ins.GetCountryCode();
            DataDotMgr.Collect(dot);
            
            AFHelper.Click_Trial_choose_next(cfg.product_id);

            
           
            
            product_id = cfg.product_id;
            product_type = PB_ProductType.PB_ProductType_Subscribe.ToString();
            
            if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.FreeTrialCanceled)
            {
                GetUI<SpeakPlanSelectUI>(UIConsts.SpeakPlanSelectUI).Show(args);
            }
            else
            {
                GetUI<SpeakPlanPromotionStep3UI>(UIConsts.SpeakPlanPromotionStep3UI).Show(args);
            }
            // 250424 业务调整不再能直接在此页直接订阅
            return;
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            PurchasingManager.instance.StartPurchasing(cfg.product_id, PB_ProductType.PB_ProductType_Subscribe, OnBuySuccess, OnBuyFail);
        }

        private void OnBtnUpdateClicked()
        {
            GetController<ShopController>(ModelConsts.Shop).OpenSubscriptionUpdate();
            Hide();
        }

        private void OnBtnPrivacyClicked()
        {
            OnPrivacyClicked();
            //新埋点：用户点击服务隐私协议（钻石小店）
            DataDotShopPrivate dot = new DataDotShopPrivate();
            DataDotMgr.Collect(dot);
        }

        private void OnBtnServiceDetailClicked()
        {
            OnServiceDetailClicked();
            //新埋点：用户点击服务协议（钻石小店）
            DataDotShopService dot = new DataDotShopService();
            DataDotMgr.Collect(dot);
        }

        private void OnBtnRestorePurchaseClicked()
        {
            OnRestorePurchaseClicked();
            //新埋点：用户点击恢复购买（钻石小店）
            DataDotShopRestorePurchase dot = new DataDotShopRestorePurchase();
            DataDotMgr.Collect(dot);
        }

        protected override void HandleNotification(string name, object body)
        {
            switch (name)
            {
                case EconomicCallEvent.OnRefreshEconomicInfo:
                    ui.tfDiamondNum.text = _currencyModel.GetEconomicInfo(EconomicType.Diamond).CurNum.ToString();
                    break;
            }
        }
        
        protected override string[] ListNotificationInterests()
        {
            return new string[]
            {
                EconomicCallEvent.OnRefreshEconomicInfo
            };
        }
    }
}
/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2025/02/11 16:03:34 星期二
# 功能：Nothing
****************************************************
*/

using FairyGUI;
using Modules.DataDot;
using Msg.incentive;

namespace ScriptsHot.Game.Modules.Shop
{
    [TransitionUI(CutInEffect.BottomToTop)]
    public class SpeakPlanBottomSubscribeUI: BaseUI<UIBind.Shop.SpeakPlanBottomSubscribePanel>
    {
        private int _type = 0;
        public SpeakPlanBottomSubscribeUI(string name) : base(name)
        {
        }
        
        public override string uiLayer => UILayerConsts.Top; //主UI层

        protected override bool isFullScreen => true;

        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(ui.btnSubscribe.onClick, OnBtnSubscribeClicked);
            AddUIEvent(ui.btnClose.onClick, OnBgClicked);
        }
        
        private void OnBgClicked()
        {
            if (_type == 1) //探索
            {
                // Notifier.instance.SendNotification(NotifyConsts.ExploreBGMopen, true);
            }
            var dot = new PopupClose();
            DataDotMgr.Collect(dot); 
            Hide();
        }

        private void OnBtnSubscribeClicked()
        {
            //ToDo:dot-recheck  是否 这里是否应该 细分入口？而不是都用popup？
            PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.popup;
            if (_type == 1)
            {
                GetUI(UIConsts.SpeakPlanPromotionStep1UI).Show(SpeakShowType.Explore);
            }
            else
            {
                GetUI(UIConsts.SpeakPlanPromotionStep1UI).Show(SpeakShowType.Speak);
            }
      
            var dot = new PopupMainIcon();
            DataDotMgr.Collect(dot);
            AFDots.Click_popup_main_icon();
            Hide();
        }

        protected override void OnShow()
        {
            base.OnShow();
            if (this.args.Length > 0)
            {
                _type = (int)this.args[0];
            }
            this.ui.ctrlType.selectedIndex = _type;
            if (_type == 1) //探索
            {
                // Notifier.instance.SendNotification(NotifyConsts.ExploreBGMopen, false);
            }

            ui.tfDesc.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_planBottomSubscribe_desc");
            ui.tfTitle.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_planBottomSubscribe_title");
            ui.tfBtnPractice.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_planBottomSubscribe_btn_confirm");
            
            ui.tfTitle2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_paywall_title");
            ui.tfDesc2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_paywall_desc");
            MsgManager.instance.SendMsg(new CS_SetShowStateReq()
            {
                show_item_type = PB_ShowItemType.CommercialPopup,
            });
            var dot = new SubscribePopup();
            dot.popup_title = ui.tfTitle.text;
            dot.popup_text = ui.tfDesc.text;
            DataDotMgr.Collect(dot);
            AFDots.Appear_subscribe_popup();
            
            CheckDiscountAndLifeTime();
        }
        
        private void CheckDiscountAndLifeTime()
        {
            bool isLifeTime = GameEntry.ShopC.ShopModel.IsLifeTime;
            if (!isLifeTime)
            {
                bool isDiscount = GameEntry.ShopC.ShopModel.IsDiscountType;
                ui.discountTxt.text = string.Format(I18N.inst.MoStr("ui_shop_discount_02"),
                    $"{GameEntry.ShopC.ShopModel.GetMaxDiscountStr()}");
  
                if (isDiscount)
                {
                    ui.tfBtnPractice.text = string.Format(I18N.inst.MoStr("ui_shop_discount_01")  , GameEntry.ShopC.ShopModel.GetMaxDiscountStr());
                }
                else
                {
                    ui.tfBtnPractice.text = I18N.inst.MoStr("ui_planBottomSubscribe_btn_confirm");
                }
            }
            else
            {
                ui.discount.selectedIndex = 0;
            }
            ui.lifetimeNode.visible = isLifeTime;
            if (isLifeTime)
            {
                // ui.lifetimeTxt.text = I18N.inst.MoStr("ui_shop_lifetime_1");
                // ui.tfBtnPractice.text = I18N.inst.MoStr("ui_shop_lifetime_4");
                //
                // ui.tfDesc.text = I18N.inst.MoStr("ui_shop_lifetime_2") + "\n" + I18N.inst.MoStr("ui_shop_lifetime_3");
            }
        }
    }
}
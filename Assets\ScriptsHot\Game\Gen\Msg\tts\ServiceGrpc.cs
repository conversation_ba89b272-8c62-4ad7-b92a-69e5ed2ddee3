// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/tts/service.proto
// </auto-generated>
#pragma warning disable 0414, 1591, 8981, 0612
#region Designer generated code

using grpc = global::Grpc.Core;

namespace Msg.tts {
  /// <summary>
  /// To Client（iOS）
  /// </summary>
  public static partial class TtsService
  {
    static readonly string __ServiceName = "TtsService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.CS_GetTTSAudioReq> __Marshaller_CS_GetTTSAudioReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.CS_GetTTSAudioReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SC_GetTTSAudioAck> __Marshaller_SC_GetTTSAudioAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SC_GetTTSAudioAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.CS_GetTTSAudioTranscriptReq> __Marshaller_CS_GetTTSAudioTranscriptReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.CS_GetTTSAudioTranscriptReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SC_GetTTSAudioTranscriptAck> __Marshaller_SC_GetTTSAudioTranscriptAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SC_GetTTSAudioTranscriptAck.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.tts.CS_GetTTSAudioReq, global::Msg.tts.SC_GetTTSAudioAck> __Method_GetTTSAudio = new grpc::Method<global::Msg.tts.CS_GetTTSAudioReq, global::Msg.tts.SC_GetTTSAudioAck>(
        grpc::MethodType.ServerStreaming,
        __ServiceName,
        "GetTTSAudio",
        __Marshaller_CS_GetTTSAudioReq,
        __Marshaller_SC_GetTTSAudioAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.tts.CS_GetTTSAudioTranscriptReq, global::Msg.tts.SC_GetTTSAudioTranscriptAck> __Method_GetTTSAudioTranscript = new grpc::Method<global::Msg.tts.CS_GetTTSAudioTranscriptReq, global::Msg.tts.SC_GetTTSAudioTranscriptAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetTTSAudioTranscript",
        __Marshaller_CS_GetTTSAudioTranscriptReq,
        __Marshaller_SC_GetTTSAudioTranscriptAck);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.tts.ServiceReflection.Descriptor.Services[0]; }
    }

    /// <summary>Base class for server-side implementations of TtsService</summary>
    [grpc::BindServiceMethod(typeof(TtsService), "BindService")]
    public abstract partial class TtsServiceBase
    {
      /// <summary>
      /// 获取音频
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="responseStream">Used for sending responses back to the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>A task indicating completion of the handler.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task GetTTSAudio(global::Msg.tts.CS_GetTTSAudioReq request, grpc::IServerStreamWriter<global::Msg.tts.SC_GetTTSAudioAck> responseStream, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 获取音频时间轴
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.tts.SC_GetTTSAudioTranscriptAck> GetTTSAudioTranscript(global::Msg.tts.CS_GetTTSAudioTranscriptReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for TtsService</summary>
    public partial class TtsServiceClient : grpc::ClientBase<TtsServiceClient>
    {
      /// <summary>Creates a new client for TtsService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public TtsServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for TtsService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public TtsServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected TtsServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected TtsServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// 获取音频
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncServerStreamingCall<global::Msg.tts.SC_GetTTSAudioAck> GetTTSAudio(global::Msg.tts.CS_GetTTSAudioReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetTTSAudio(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取音频
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncServerStreamingCall<global::Msg.tts.SC_GetTTSAudioAck> GetTTSAudio(global::Msg.tts.CS_GetTTSAudioReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncServerStreamingCall(__Method_GetTTSAudio, null, options, request);
      }
      /// <summary>
      /// 获取音频时间轴
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SC_GetTTSAudioTranscriptAck GetTTSAudioTranscript(global::Msg.tts.CS_GetTTSAudioTranscriptReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetTTSAudioTranscript(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取音频时间轴
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SC_GetTTSAudioTranscriptAck GetTTSAudioTranscript(global::Msg.tts.CS_GetTTSAudioTranscriptReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetTTSAudioTranscript, null, options, request);
      }
      /// <summary>
      /// 获取音频时间轴
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SC_GetTTSAudioTranscriptAck> GetTTSAudioTranscriptAsync(global::Msg.tts.CS_GetTTSAudioTranscriptReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetTTSAudioTranscriptAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取音频时间轴
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SC_GetTTSAudioTranscriptAck> GetTTSAudioTranscriptAsync(global::Msg.tts.CS_GetTTSAudioTranscriptReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetTTSAudioTranscript, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override TtsServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new TtsServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(TtsServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GetTTSAudio, serviceImpl.GetTTSAudio)
          .AddMethod(__Method_GetTTSAudioTranscript, serviceImpl.GetTTSAudioTranscript).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, TtsServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GetTTSAudio, serviceImpl == null ? null : new grpc::ServerStreamingServerMethod<global::Msg.tts.CS_GetTTSAudioReq, global::Msg.tts.SC_GetTTSAudioAck>(serviceImpl.GetTTSAudio));
      serviceBinder.AddMethod(__Method_GetTTSAudioTranscript, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.tts.CS_GetTTSAudioTranscriptReq, global::Msg.tts.SC_GetTTSAudioTranscriptAck>(serviceImpl.GetTTSAudioTranscript));
    }

  }
  public static partial class TtsWebService
  {
    static readonly string __ServiceName = "TtsWebService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.Web_GetTTSAudioFullReq> __Marshaller_Web_GetTTSAudioFullReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.Web_GetTTSAudioFullReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.Web_GetTTSAudioFullAck> __Marshaller_Web_GetTTSAudioFullAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.Web_GetTTSAudioFullAck.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.tts.Web_GetTTSAudioFullReq, global::Msg.tts.Web_GetTTSAudioFullAck> __Method_GetTTSAudioFull = new grpc::Method<global::Msg.tts.Web_GetTTSAudioFullReq, global::Msg.tts.Web_GetTTSAudioFullAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetTTSAudioFull",
        __Marshaller_Web_GetTTSAudioFullReq,
        __Marshaller_Web_GetTTSAudioFullAck);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.tts.ServiceReflection.Descriptor.Services[1]; }
    }

    /// <summary>Base class for server-side implementations of TtsWebService</summary>
    [grpc::BindServiceMethod(typeof(TtsWebService), "BindService")]
    public abstract partial class TtsWebServiceBase
    {
      /// <summary>
      /// add get audio full
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.tts.Web_GetTTSAudioFullAck> GetTTSAudioFull(global::Msg.tts.Web_GetTTSAudioFullReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for TtsWebService</summary>
    public partial class TtsWebServiceClient : grpc::ClientBase<TtsWebServiceClient>
    {
      /// <summary>Creates a new client for TtsWebService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public TtsWebServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for TtsWebService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public TtsWebServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected TtsWebServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected TtsWebServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// add get audio full
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.Web_GetTTSAudioFullAck GetTTSAudioFull(global::Msg.tts.Web_GetTTSAudioFullReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetTTSAudioFull(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// add get audio full
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.Web_GetTTSAudioFullAck GetTTSAudioFull(global::Msg.tts.Web_GetTTSAudioFullReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetTTSAudioFull, null, options, request);
      }
      /// <summary>
      /// add get audio full
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.Web_GetTTSAudioFullAck> GetTTSAudioFullAsync(global::Msg.tts.Web_GetTTSAudioFullReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetTTSAudioFullAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// add get audio full
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.Web_GetTTSAudioFullAck> GetTTSAudioFullAsync(global::Msg.tts.Web_GetTTSAudioFullReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetTTSAudioFull, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override TtsWebServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new TtsWebServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(TtsWebServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GetTTSAudioFull, serviceImpl.GetTTSAudioFull).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, TtsWebServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GetTTSAudioFull, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.tts.Web_GetTTSAudioFullReq, global::Msg.tts.Web_GetTTSAudioFullAck>(serviceImpl.GetTTSAudioFull));
    }

  }
  /// <summary>
  /// To Server（内部接口）
  /// </summary>
  public static partial class TtsInnerService
  {
    static readonly string __ServiceName = "TtsInnerService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_OfflineGetTTSAudioReq> __Marshaller_SS_OfflineGetTTSAudioReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_OfflineGetTTSAudioReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_OfflineGetTTSAudioAck> __Marshaller_SS_OfflineGetTTSAudioAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_OfflineGetTTSAudioAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_TextToAudioReq> __Marshaller_SS_TextToAudioReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_TextToAudioReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_TextToAudioAck> __Marshaller_SS_TextToAudioAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_TextToAudioAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_OnlineTextToAudioReq> __Marshaller_SS_OnlineTextToAudioReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_OnlineTextToAudioReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_OnlineTextToAudioAck> __Marshaller_SS_OnlineTextToAudioAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_OnlineTextToAudioAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_TextToAudioImportReq> __Marshaller_SS_TextToAudioImportReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_TextToAudioImportReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_TextToAudioImportAck> __Marshaller_SS_TextToAudioImportAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_TextToAudioImportAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_AddBaseVoiceModelReq> __Marshaller_SS_AddBaseVoiceModelReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_AddBaseVoiceModelReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_AddBaseVoiceModelAck> __Marshaller_SS_AddBaseVoiceModelAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_AddBaseVoiceModelAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_GetBaseVoiceModelsReq> __Marshaller_SS_GetBaseVoiceModelsReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_GetBaseVoiceModelsReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_GetBaseVoiceModelsAck> __Marshaller_SS_GetBaseVoiceModelsAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_GetBaseVoiceModelsAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_AddCustomVoiceModelReq> __Marshaller_SS_AddCustomVoiceModelReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_AddCustomVoiceModelReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_AddCustomVoiceModelAck> __Marshaller_SS_AddCustomVoiceModelAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_AddCustomVoiceModelAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_GetCustomVoiceModelsReq> __Marshaller_SS_GetCustomVoiceModelsReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_GetCustomVoiceModelsReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.tts.SS_GetCustomVoiceModelsAck> __Marshaller_SS_GetCustomVoiceModelsAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.tts.SS_GetCustomVoiceModelsAck.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.tts.SS_OfflineGetTTSAudioReq, global::Msg.tts.SS_OfflineGetTTSAudioAck> __Method_OfflineGetTTSAudio = new grpc::Method<global::Msg.tts.SS_OfflineGetTTSAudioReq, global::Msg.tts.SS_OfflineGetTTSAudioAck>(
        grpc::MethodType.ServerStreaming,
        __ServiceName,
        "OfflineGetTTSAudio",
        __Marshaller_SS_OfflineGetTTSAudioReq,
        __Marshaller_SS_OfflineGetTTSAudioAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.tts.SS_TextToAudioReq, global::Msg.tts.SS_TextToAudioAck> __Method_TextToAudio = new grpc::Method<global::Msg.tts.SS_TextToAudioReq, global::Msg.tts.SS_TextToAudioAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "TextToAudio",
        __Marshaller_SS_TextToAudioReq,
        __Marshaller_SS_TextToAudioAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.tts.SS_OnlineTextToAudioReq, global::Msg.tts.SS_OnlineTextToAudioAck> __Method_OnlineTextToAudio = new grpc::Method<global::Msg.tts.SS_OnlineTextToAudioReq, global::Msg.tts.SS_OnlineTextToAudioAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "OnlineTextToAudio",
        __Marshaller_SS_OnlineTextToAudioReq,
        __Marshaller_SS_OnlineTextToAudioAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.tts.SS_TextToAudioImportReq, global::Msg.tts.SS_TextToAudioImportAck> __Method_TextToAudioImport = new grpc::Method<global::Msg.tts.SS_TextToAudioImportReq, global::Msg.tts.SS_TextToAudioImportAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "TextToAudioImport",
        __Marshaller_SS_TextToAudioImportReq,
        __Marshaller_SS_TextToAudioImportAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.tts.SS_AddBaseVoiceModelReq, global::Msg.tts.SS_AddBaseVoiceModelAck> __Method_AddBaseVoiceModel = new grpc::Method<global::Msg.tts.SS_AddBaseVoiceModelReq, global::Msg.tts.SS_AddBaseVoiceModelAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "AddBaseVoiceModel",
        __Marshaller_SS_AddBaseVoiceModelReq,
        __Marshaller_SS_AddBaseVoiceModelAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.tts.SS_GetBaseVoiceModelsReq, global::Msg.tts.SS_GetBaseVoiceModelsAck> __Method_GetBaseVoiceModels = new grpc::Method<global::Msg.tts.SS_GetBaseVoiceModelsReq, global::Msg.tts.SS_GetBaseVoiceModelsAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetBaseVoiceModels",
        __Marshaller_SS_GetBaseVoiceModelsReq,
        __Marshaller_SS_GetBaseVoiceModelsAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.tts.SS_AddCustomVoiceModelReq, global::Msg.tts.SS_AddCustomVoiceModelAck> __Method_AddCustomVoiceModel = new grpc::Method<global::Msg.tts.SS_AddCustomVoiceModelReq, global::Msg.tts.SS_AddCustomVoiceModelAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "AddCustomVoiceModel",
        __Marshaller_SS_AddCustomVoiceModelReq,
        __Marshaller_SS_AddCustomVoiceModelAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.tts.SS_GetCustomVoiceModelsReq, global::Msg.tts.SS_GetCustomVoiceModelsAck> __Method_GetCustomVoiceModels = new grpc::Method<global::Msg.tts.SS_GetCustomVoiceModelsReq, global::Msg.tts.SS_GetCustomVoiceModelsAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetCustomVoiceModels",
        __Marshaller_SS_GetCustomVoiceModelsReq,
        __Marshaller_SS_GetCustomVoiceModelsAck);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.tts.ServiceReflection.Descriptor.Services[2]; }
    }

    /// <summary>Base class for server-side implementations of TtsInnerService</summary>
    [grpc::BindServiceMethod(typeof(TtsInnerService), "BindService")]
    public abstract partial class TtsInnerServiceBase
    {
      /// <summary>
      /// 获取音频（离线，不会校验用户token，内部系统使用）
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="responseStream">Used for sending responses back to the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>A task indicating completion of the handler.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task OfflineGetTTSAudio(global::Msg.tts.SS_OfflineGetTTSAudioReq request, grpc::IServerStreamWriter<global::Msg.tts.SS_OfflineGetTTSAudioAck> responseStream, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 文本转音频(离线)
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.tts.SS_TextToAudioAck> TextToAudio(global::Msg.tts.SS_TextToAudioReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 文本转音频(在线)
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.tts.SS_OnlineTextToAudioAck> OnlineTextToAudio(global::Msg.tts.SS_OnlineTextToAudioReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 文本转音频批量导入(离线)
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.tts.SS_TextToAudioImportAck> TextToAudioImport(global::Msg.tts.SS_TextToAudioImportReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 添加基础语音模型
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.tts.SS_AddBaseVoiceModelAck> AddBaseVoiceModel(global::Msg.tts.SS_AddBaseVoiceModelReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 基础语音模型列表
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.tts.SS_GetBaseVoiceModelsAck> GetBaseVoiceModels(global::Msg.tts.SS_GetBaseVoiceModelsReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 定制化语音模型
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.tts.SS_AddCustomVoiceModelAck> AddCustomVoiceModel(global::Msg.tts.SS_AddCustomVoiceModelReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 定制化语音模型列表
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.tts.SS_GetCustomVoiceModelsAck> GetCustomVoiceModels(global::Msg.tts.SS_GetCustomVoiceModelsReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for TtsInnerService</summary>
    public partial class TtsInnerServiceClient : grpc::ClientBase<TtsInnerServiceClient>
    {
      /// <summary>Creates a new client for TtsInnerService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public TtsInnerServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for TtsInnerService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public TtsInnerServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected TtsInnerServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected TtsInnerServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// 获取音频（离线，不会校验用户token，内部系统使用）
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncServerStreamingCall<global::Msg.tts.SS_OfflineGetTTSAudioAck> OfflineGetTTSAudio(global::Msg.tts.SS_OfflineGetTTSAudioReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return OfflineGetTTSAudio(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取音频（离线，不会校验用户token，内部系统使用）
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncServerStreamingCall<global::Msg.tts.SS_OfflineGetTTSAudioAck> OfflineGetTTSAudio(global::Msg.tts.SS_OfflineGetTTSAudioReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncServerStreamingCall(__Method_OfflineGetTTSAudio, null, options, request);
      }
      /// <summary>
      /// 文本转音频(离线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_TextToAudioAck TextToAudio(global::Msg.tts.SS_TextToAudioReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return TextToAudio(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 文本转音频(离线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_TextToAudioAck TextToAudio(global::Msg.tts.SS_TextToAudioReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_TextToAudio, null, options, request);
      }
      /// <summary>
      /// 文本转音频(离线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_TextToAudioAck> TextToAudioAsync(global::Msg.tts.SS_TextToAudioReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return TextToAudioAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 文本转音频(离线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_TextToAudioAck> TextToAudioAsync(global::Msg.tts.SS_TextToAudioReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_TextToAudio, null, options, request);
      }
      /// <summary>
      /// 文本转音频(在线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_OnlineTextToAudioAck OnlineTextToAudio(global::Msg.tts.SS_OnlineTextToAudioReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return OnlineTextToAudio(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 文本转音频(在线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_OnlineTextToAudioAck OnlineTextToAudio(global::Msg.tts.SS_OnlineTextToAudioReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_OnlineTextToAudio, null, options, request);
      }
      /// <summary>
      /// 文本转音频(在线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_OnlineTextToAudioAck> OnlineTextToAudioAsync(global::Msg.tts.SS_OnlineTextToAudioReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return OnlineTextToAudioAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 文本转音频(在线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_OnlineTextToAudioAck> OnlineTextToAudioAsync(global::Msg.tts.SS_OnlineTextToAudioReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_OnlineTextToAudio, null, options, request);
      }
      /// <summary>
      /// 文本转音频批量导入(离线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_TextToAudioImportAck TextToAudioImport(global::Msg.tts.SS_TextToAudioImportReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return TextToAudioImport(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 文本转音频批量导入(离线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_TextToAudioImportAck TextToAudioImport(global::Msg.tts.SS_TextToAudioImportReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_TextToAudioImport, null, options, request);
      }
      /// <summary>
      /// 文本转音频批量导入(离线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_TextToAudioImportAck> TextToAudioImportAsync(global::Msg.tts.SS_TextToAudioImportReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return TextToAudioImportAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 文本转音频批量导入(离线)
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_TextToAudioImportAck> TextToAudioImportAsync(global::Msg.tts.SS_TextToAudioImportReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_TextToAudioImport, null, options, request);
      }
      /// <summary>
      /// 添加基础语音模型
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_AddBaseVoiceModelAck AddBaseVoiceModel(global::Msg.tts.SS_AddBaseVoiceModelReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return AddBaseVoiceModel(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 添加基础语音模型
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_AddBaseVoiceModelAck AddBaseVoiceModel(global::Msg.tts.SS_AddBaseVoiceModelReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_AddBaseVoiceModel, null, options, request);
      }
      /// <summary>
      /// 添加基础语音模型
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_AddBaseVoiceModelAck> AddBaseVoiceModelAsync(global::Msg.tts.SS_AddBaseVoiceModelReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return AddBaseVoiceModelAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 添加基础语音模型
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_AddBaseVoiceModelAck> AddBaseVoiceModelAsync(global::Msg.tts.SS_AddBaseVoiceModelReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_AddBaseVoiceModel, null, options, request);
      }
      /// <summary>
      /// 基础语音模型列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_GetBaseVoiceModelsAck GetBaseVoiceModels(global::Msg.tts.SS_GetBaseVoiceModelsReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetBaseVoiceModels(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 基础语音模型列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_GetBaseVoiceModelsAck GetBaseVoiceModels(global::Msg.tts.SS_GetBaseVoiceModelsReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetBaseVoiceModels, null, options, request);
      }
      /// <summary>
      /// 基础语音模型列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_GetBaseVoiceModelsAck> GetBaseVoiceModelsAsync(global::Msg.tts.SS_GetBaseVoiceModelsReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetBaseVoiceModelsAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 基础语音模型列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_GetBaseVoiceModelsAck> GetBaseVoiceModelsAsync(global::Msg.tts.SS_GetBaseVoiceModelsReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetBaseVoiceModels, null, options, request);
      }
      /// <summary>
      /// 定制化语音模型
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_AddCustomVoiceModelAck AddCustomVoiceModel(global::Msg.tts.SS_AddCustomVoiceModelReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return AddCustomVoiceModel(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 定制化语音模型
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_AddCustomVoiceModelAck AddCustomVoiceModel(global::Msg.tts.SS_AddCustomVoiceModelReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_AddCustomVoiceModel, null, options, request);
      }
      /// <summary>
      /// 定制化语音模型
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_AddCustomVoiceModelAck> AddCustomVoiceModelAsync(global::Msg.tts.SS_AddCustomVoiceModelReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return AddCustomVoiceModelAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 定制化语音模型
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_AddCustomVoiceModelAck> AddCustomVoiceModelAsync(global::Msg.tts.SS_AddCustomVoiceModelReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_AddCustomVoiceModel, null, options, request);
      }
      /// <summary>
      /// 定制化语音模型列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_GetCustomVoiceModelsAck GetCustomVoiceModels(global::Msg.tts.SS_GetCustomVoiceModelsReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetCustomVoiceModels(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 定制化语音模型列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.tts.SS_GetCustomVoiceModelsAck GetCustomVoiceModels(global::Msg.tts.SS_GetCustomVoiceModelsReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetCustomVoiceModels, null, options, request);
      }
      /// <summary>
      /// 定制化语音模型列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_GetCustomVoiceModelsAck> GetCustomVoiceModelsAsync(global::Msg.tts.SS_GetCustomVoiceModelsReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetCustomVoiceModelsAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 定制化语音模型列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.tts.SS_GetCustomVoiceModelsAck> GetCustomVoiceModelsAsync(global::Msg.tts.SS_GetCustomVoiceModelsReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetCustomVoiceModels, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override TtsInnerServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new TtsInnerServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(TtsInnerServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_OfflineGetTTSAudio, serviceImpl.OfflineGetTTSAudio)
          .AddMethod(__Method_TextToAudio, serviceImpl.TextToAudio)
          .AddMethod(__Method_OnlineTextToAudio, serviceImpl.OnlineTextToAudio)
          .AddMethod(__Method_TextToAudioImport, serviceImpl.TextToAudioImport)
          .AddMethod(__Method_AddBaseVoiceModel, serviceImpl.AddBaseVoiceModel)
          .AddMethod(__Method_GetBaseVoiceModels, serviceImpl.GetBaseVoiceModels)
          .AddMethod(__Method_AddCustomVoiceModel, serviceImpl.AddCustomVoiceModel)
          .AddMethod(__Method_GetCustomVoiceModels, serviceImpl.GetCustomVoiceModels).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, TtsInnerServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_OfflineGetTTSAudio, serviceImpl == null ? null : new grpc::ServerStreamingServerMethod<global::Msg.tts.SS_OfflineGetTTSAudioReq, global::Msg.tts.SS_OfflineGetTTSAudioAck>(serviceImpl.OfflineGetTTSAudio));
      serviceBinder.AddMethod(__Method_TextToAudio, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.tts.SS_TextToAudioReq, global::Msg.tts.SS_TextToAudioAck>(serviceImpl.TextToAudio));
      serviceBinder.AddMethod(__Method_OnlineTextToAudio, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.tts.SS_OnlineTextToAudioReq, global::Msg.tts.SS_OnlineTextToAudioAck>(serviceImpl.OnlineTextToAudio));
      serviceBinder.AddMethod(__Method_TextToAudioImport, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.tts.SS_TextToAudioImportReq, global::Msg.tts.SS_TextToAudioImportAck>(serviceImpl.TextToAudioImport));
      serviceBinder.AddMethod(__Method_AddBaseVoiceModel, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.tts.SS_AddBaseVoiceModelReq, global::Msg.tts.SS_AddBaseVoiceModelAck>(serviceImpl.AddBaseVoiceModel));
      serviceBinder.AddMethod(__Method_GetBaseVoiceModels, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.tts.SS_GetBaseVoiceModelsReq, global::Msg.tts.SS_GetBaseVoiceModelsAck>(serviceImpl.GetBaseVoiceModels));
      serviceBinder.AddMethod(__Method_AddCustomVoiceModel, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.tts.SS_AddCustomVoiceModelReq, global::Msg.tts.SS_AddCustomVoiceModelAck>(serviceImpl.AddCustomVoiceModel));
      serviceBinder.AddMethod(__Method_GetCustomVoiceModels, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.tts.SS_GetCustomVoiceModelsReq, global::Msg.tts.SS_GetCustomVoiceModelsAck>(serviceImpl.GetCustomVoiceModels));
    }

  }
}
#endregion

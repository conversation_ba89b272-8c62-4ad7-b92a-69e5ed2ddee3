﻿using FairyGUI;
using Msg.explore;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.ChatLogicNew.UI.Item.Component;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Explore;
using UnityEngine;

namespace UIBind.Explore.Item.ItemComponentLogic
{
    /// <summary>
    /// avatar cell
    /// </summary>
    public class ExploreAvatarItemLogic:ItemComponentLogicBase
    {
        public ExploreAvatarItem Com;

        public TextFieldExtension TfOrigin =>  Com.comTxt.tfOrigin as TextFieldExtension;

        public ExploreAvatarTranslateItemLogic TranslateCom;

        private string _bubbleId = string.Empty;

        private string _str;

        private ExploreItemUI _item;

        private long _dialogId;
        private long _round;

        private bool _isMain = true;
        public override void Init()
        {
            base.Init();
            Com.effectSound.com.onClick.Add(OnEffectSound);
            Com.btnTranslate.com.onClick.Add(OnTranslate);
            this.Com.comTxt.sp.visible = false;
            TfOrigin.SetFormat(Color.white, 32);
            
        }
        
        public void SetParentItem(ExploreItemUI item)
        {
            _item = item;
        }

        public void SetInfo(long dialogId,long round)
        {
            _dialogId = dialogId;
            _round = round;
        }

        public void Reset()
        {
            Com.btnTranslate.ctrl.selectedPage = "normal";
            this.Com.ctrl.selectedPage = "normal";
            this.Com.comTxt.com.alpha = 1;
            this.Com.com.visible = false;
            TranslateCom.Reset();
        }

        public void ShowAvatarTxt(string bubbleId,string str,string strTranslate)
        {
            Com.comTxt.txtTran.text = string.Empty;
            SetTransformVisible(false);
            Com.effectSound.com.onClick.Clear();
            Com.btnTranslate.com.onClick.Clear();
            Com.effectSound.com.onClick.Add(OnEffectSound);
            Com.btnTranslate.com.onClick.Add(OnTranslate);
            
            _str = str;
            ExploreNewWordData newWordData = new ExploreNewWordData();
            newWordData.ui = _ui;
            newWordData.avatarId = 1;
            newWordData.dialogId = _controller.CurTaskId;
            newWordData.bubbleId = bubbleId;
            _translateWordComponent.SetData(newWordData);
            
            this._bubbleId = bubbleId;
            var textContent = new RichContent()
            {
                Content = str,
                IsLast = true,
            };
            TfOrigin.Reset();
            TfOrigin.OnClickNewWord += _translateWordComponent.ReqNewWordTrans;
            TfOrigin.AppendContent(textContent).Display(ShowMode.Normal);

            Com.comTxt.txtTran.text = strTranslate;

            if (_controller.Model.AutoTranslateDisplay == PB_Explore_UserSetting_AutoTranslateDisplay.EO_US_ATD_ON)
            {
                //自动显示翻译
                SetTransformVisible(true);
            }

            if (_isMain)
            {
                this.Com.comTxt.com.alpha = 1;
                this.Com.comTxt.com.visible = true;
                this.Com.com.visible = true;
            }
            else
            {
                Com.btnTranslate.ctrl.selectedPage = "normal";
                Com.ctrl.selectedPage = "normal";
                TranslateCom.ToZore();
                ToOne();
            }

            DotShow();
        }
        
        public void AlphaToZore()
        {
            return;
            _isMain = false;
            Com.btnTranslate.ctrl.selectedPage = "translate";
            this.Com.ctrl.selectedPage = "translate";
            
            this.Com.comTxt.com.alpha = 0;
            Com.comTxt.com.TweenFade(0, ExploreConst.CellAphla).OnComplete(() =>
            {
                Com.comTxt.com.visible = false;
                this.Com.com.visible = false;
                TranslateCom.AlphaToOne();
            });
        }
        
        public void AlphaToOne()
        {
            return;
            _isMain = true;
            this.Com.com.visible = true;
            Com.comTxt.com.visible = true;
            Com.comTxt.com.TweenFade(1, ExploreConst.CellAphla);
        }

        public void SetTransformVisible(bool value)
        {
            Com.comTxt.txtTran.visible = value;
            if (value)
            {
                Com.btnTranslate.ctrl.selectedPage = "translate";
            }
            else
            {
                Com.btnTranslate.ctrl.selectedPage = "normal";
            }
        }

        public void ToOne()
        {
            _isMain = true;
            this.Com.com.visible = true;
            Com.comTxt.com.alpha = 1;
            Com.comTxt.com.visible = true;
        }
        
        private void OnEffectSound()
        {
            if (this._controller.IfCanMicrophone) return;
            _item.AudioOver();
            Com.effectSound.ctrlState.selectedIndex = 1;
            ulong audioId = this._controller.Model.GetAudioIdByBubbleId(_bubbleId);
            ExploreAudioPlayEffect playEffect = new ExploreAudioPlayEffect()
            {
                AudioId = audioId,
                Effect = false
            };
            Notifier.instance.SendNotification(NotifyConsts.ExploreAvatarAudioPlay,playEffect);
            DotAudioBtn();
            
            //执行队列中 recordUI的控制
            Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_do_recordui_show_out_other);
            //清空
            Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);

            if (!_isMain)
            {
                OnTranslateBack();
            }
        }
        
        private void OnTranslate()
        {
            //执行队列中 recordUI的控制
            Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_do_recordui_show_out_other);
            //清空
            Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
            
            if (Com.btnTranslate.ctrl.selectedPage == "normal")
            {
                // AlphaToZore();
                SetTransformVisible(true);
                Dot();
            }
            else
            {
                SetTransformVisible(false);
                // OnTranslateBack();
            }
        }
        
        private void OnTranslateBack()
        {
            Com.btnTranslate.ctrl.selectedPage = "normal";
            this.Com.ctrl.selectedPage = "normal";
            TranslateCom.AlphaToZore();
        }

        public void AudioOver()
        {
            Com.effectSound.ctrlState.selectedIndex = 0;
        }

        private void Dot()
        {
            CLICK_EXPLORE_TRANSLATION_BUTTON data = new CLICK_EXPLORE_TRANSLATION_BUTTON();
            data.previuos_langauge = I18N.inst.MotherLanguageStr;
            data.target_language = I18N.inst.ForeignLanguageStr;
            data.root_bubble = "avatar";
            data.previuos_text = _str;
            data.target_text = Com.comTranslate.txtTran.text; 
            
            data.task_id = _controller.CurTaskId;
            data.dialogue_id = _dialogId;   
            data.dialogue_round = _round;
            DataDotMgr.Collect(data);
            
        }
        
        private void DotAudioBtn()
        {
            CLICK_EXPLORE_PLAY_BUTTON data = new CLICK_EXPLORE_PLAY_BUTTON();
            data.root_bubble = "avatar";
            data.bubble_text = _str;
            
            data.task_id = _controller.CurTaskId;
            data.dialogue_id = _dialogId;   
            data.dialogue_round = _round;
            DataDotMgr.Collect(data);
            
        }

        public void DotShow()
        {
            APPEAR_EXPLORE_AVATAR_BUBBLE data = new APPEAR_EXPLORE_AVATAR_BUBBLE();
            data.avatar_text = _str;
            data.task_id = _controller.CurTaskId;
            data.dialogue_id = _dialogId;   
            data.dialogue_round = _round;
            DataDotMgr.Collect(data);
        }
    }
}
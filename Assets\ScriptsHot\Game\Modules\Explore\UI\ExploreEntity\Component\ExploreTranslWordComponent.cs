﻿

using System;
using FairyGUI;
using ScriptsHot.Game.Modules.ChatLogicNew.UI.Item.Component;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Procedure;
using ScriptsHot.Game.Modules.Scene.Level;
using UIBind.Explore.Item.ItemComponentLogic;
using UnityEngine;
using NotImplementedException = System.NotImplementedException;

    /// <summary>
    /// 翻译单词组件
    /// </summary>
    public class ExploreTranslWordComponent:ExploreComponentBase
    {
        private static string CLIENT_ID_PREFIX = "ExploreCell_";
        private NewWordComponent _curNewWordComponent;
        private string _curNewWord;
        private ExploreNewWordData _data;
        public ExploreTranslWordComponent(IComponetOwner owner) : base(owner)
        {
        }

        public void SetData(ExploreNewWordData data)
        {
            _data = data;
        }

        public void ReqNewWordTrans(NewWordComponent cmpt, Vector2 wordPos)
        {
            if (this._controller.IfCanMicrophone) return;
            //暂停 任务链条
            Notifier.instance.SendNotification(NotifyConsts.procedure_main_pause);
            
            _curNewWordComponent = cmpt;
            _curNewWord = cmpt.word;
            (ControllerManager.instance.GetController(ModelConsts.Chat) as ChatController).NewWordResp = RespNewWordTrans;
            
            var msg = new CS_ClickWordReq();
            msg.text = cmpt.word;
            msg.dialog_id = _data.dialogId;
            msg.avatar_id = _data.avatarId;
            msg.client_id = CLIENT_ID_PREFIX + _data.bubbleId;
          
            _data.ui.AddChild(cmpt);
            cmpt.xy = cmpt.GlobalToLocal(wordPos);
            MsgManager.instance.SendMsg(msg,NewWordTransFail);
            
        }

        //生词翻译失败
        private void NewWordTransFail(GRPCManager.ErrorType et,Google.Protobuf.IMessage msg)
        {
            if (_curNewWordComponent != null && !_curNewWordComponent.isDisposed)
            {
                _curNewWordComponent.Dispose();
            }
            DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
            dataDotCutApiCase.Dialogue_id = _data.dialogId;
            dataDotCutApiCase.Api_address = "CS_ClickWordReq";
            dataDotCutApiCase.Cut_mode = 3;
            DataDotMgr.Collect(dataDotCutApiCase);
            VFDebug.LogError("TranslateWordComponent NewWordTransFail errorType : " + et);
        }

        public void RespNewWordTrans(SC_ClickWordAck msg)
        {
            if (msg.data.client_id != CLIENT_ID_PREFIX + _data.bubbleId)
                return;
            //服务器给了errorcode
            if (msg.code != 0)
            {
                VFDebug.LogError("SC_ClickWordAck is error");
                NewWordTransFail(GRPCManager.ErrorType.None,null);
                return;
            }
            if (_curNewWordComponent != null && !_curNewWordComponent.isDisposed)
            {
                if (_curNewWordComponent.word.ToLower() == msg.data.word.ToLower())
                {
           
                    Notifier.instance.SendNotification(NotifyConsts.NewWorldAudioStart);
                    TTSManager.instance.PlayTTS(msg.data.audio_id,PlayWordOver);
                    
                    //执行队列中 recordUI的控制
                    Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_do_recordui_show_out_other);
                    //清空
                    Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
                    
                    _curNewWordComponent.SetWord(msg.data.translation);
                    _curNewWordComponent.parentTfExt.AppendNewWord(msg.data.word, msg.data.count);
                    _curNewWordComponent.parentTfExt.CreateNewWord();
                    
                    
                    CLICK_EXPLORE_VOCAB_TRANSLATION data = new CLICK_EXPLORE_VOCAB_TRANSLATION();
                    data.target_word = _curNewWord;
                    data.target_translation = msg.data.translation;
                    // Debug.LogError("新埋点：生词点击---------------------------");
                    //新埋点：生词点击
                    if (this.GetOwner() is ExploreAvatarItemLogic)
                    {
                        data.root_bubble = "avatar";
                    }
                    else if (this.GetOwner() is ExploreScaffoldLogic)
                    {
                        data.root_bubble = "example";
                    }
                    DataDotMgr.Collect(data);
                }
            }
        }

        /// <summary>
        /// 生词播放完毕
        /// </summary>
        private void PlayWordOver()
        {
            VFDebug.Log("单词播放完毕");
            
            Notifier.instance.SendNotification(NotifyConsts.NewWorldAudioStop);
        }

        public override void OnDispose()
        {
        }

        public override void Refresh()
        {
        }

        public override void Update(int interval)
        {
        }

        public override void Clear()
        {
        }
    }
    
public class ExploreNewWordData
{
    public long dialogId; // 对话id
    public long avatarId; // avatar id 
    public string bubbleId; // 气泡 id
    public GComponent ui;

}
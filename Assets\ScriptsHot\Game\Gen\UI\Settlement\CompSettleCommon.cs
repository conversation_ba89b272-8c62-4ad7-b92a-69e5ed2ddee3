/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Settlement
{
    public partial class CompSettleCommon : UIBindT
    {
        public override string pkgName => "Settlement";
        public override string comName => "CompSettleCommon";

        public GGraph effectPosFlag;
        public GLoader3D ldr3dFlow;
        public GTextField tfCongra;
        public GTextField tfLanguage;
        public GList list;
        public GGroup grpPoint;
        public CompSettleList compSettleList;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            effectPosFlag = (GGraph)com.GetChildAt(0);
            ldr3dFlow = (GLoader3D)com.GetChildAt(1);
            tfCongra = (GTextField)com.GetChildAt(2);
            tfLanguage = (GTextField)com.GetChildAt(4);
            list = (GList)com.GetChildAt(5);
            grpPoint = (GGroup)com.GetChildAt(6);
            compSettleList = (CompSettleList)com.GetChildAt(8);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            effectPosFlag = null;
            ldr3dFlow = null;
            tfCongra = null;
            tfLanguage = null;
            list = null;
            grpPoint = null;
            compSettleList = null;
        }
    }
}
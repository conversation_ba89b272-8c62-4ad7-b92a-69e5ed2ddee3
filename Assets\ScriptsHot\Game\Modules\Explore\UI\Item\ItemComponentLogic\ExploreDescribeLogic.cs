using FairyGUI;
using Msg.explore;

namespace UIBind.Explore.Item.ItemComponentLogic
{
    /// <summary>
    /// 描述
    /// </summary>
    public class ExploreDescribeLogic:ItemComponentLogicBase
    {
        public ExploreDescribe Com;
        private const int DescribeMaxWidth = 641;
        private const int DescribeMaxHeight = 86; // 两行文本的最大高度
        
        public override void Init()
        {
            base.Init();
        }
        public void UpdateDescribe(PB_Task_Detail info,long entityId)
        {
            Com.ctrlDescribeBack.SetSelectedPage("back" + _controller.Model.GetDescribeBack(entityId));
            Com.ctrl.selectedIndex = 1;
            Com.txtTypeName.SetKey("ui_explore_title_topic");
            
            Com.txtDesc.autoSize = AutoSizeType.Both;
            Com.txtDescHeightFlag.text = info.desc;

            Com.txtDesc.visible = false;
       
            float width = Com.txtDesc.width;
            float height = Com.txtDescHeightFlag.height;

            if (height > DescribeMaxHeight)
            {
                Com.txtDesc.width = DescribeMaxWidth;
                Com.txtDesc.height = DescribeMaxHeight;
                Com.txtDesc.autoSize = AutoSizeType.Shrink;
            }
            else
            {
                Com.txtDesc.text = info.desc;
                // 首先处理宽度
                if (width >= DescribeMaxWidth)
                {
                    Com.txtDesc.width = DescribeMaxWidth;
                    Com.txtDesc.autoSize = AutoSizeType.Height;
                }
            }

            Com.txtDesc.text = info.desc;

            Com.txtDesc.visible = true;
        }
    }
}
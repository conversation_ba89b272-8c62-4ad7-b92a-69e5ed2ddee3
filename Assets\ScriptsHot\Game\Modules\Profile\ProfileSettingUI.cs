using System;
using System.Collections;
using System.Collections.Generic;
using CommonUI;
using Cysharp.Threading.Tasks;
using FairyGUI;
using Game;
using Google.Protobuf.Collections;
using JPush;
using LitJson;
using Modules.DataDot;
using Msg.incentive;
using MTPush;
using Newtonsoft.Json;
using ScriptsHot.Game.Modules.MainPath;
using ScriptsHot.Game.Modules.Sign;
using ScriptsHot.Game.UGUI.iOSWidget;
using ScriptsHot.Game.UGUI.iOSWidget.Objs;
using ScriptsHot.Game.UGUI.WebView;
using UnityEditor;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Profile
{
    [TransitionUI(CutInEffect.RightToLeft)]
    public class ProfileSettingUI : BaseUI<UIBind.Profile.ProfileSettingPanel>
    {
        public ProfileSettingUI(string name) : base(name)
        {

        }
        private ProfileModel model => GetModel<ProfileModel>(ModelConsts.Profile);
        private MainModel mainModel => GetModel<MainModel>(ModelConsts.Main);


        public override string uiLayer => UILayerConsts.Module;
        protected override bool isFullScreen => true;
        
        public enum ChangeLangMode
        {
            None,
            FirstLang
        }

        private ChangeLangMode _changeLangMode = ChangeLangMode.FirstLang;
        
        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(this.ui.buttonBack.onClick, (I) => Hide());
            AddUIEvent(this.ui.comProfileSettingContent.buttonFeedback.onClick,OnClickFeedback);
            AddUIEvent(this.ui.comProfileSettingContent.b1.com.asButton.onClick, OnClickLogOut);
            AddUIEvent(this.ui.comProfileSettingContent.b2.com.asButton.onClick, OnClickLDeleteAccount);
            AddUIEvent(this.ui.comProfileSettingContent.buttonSwitchPrivacy.onClick, OnSwitchPrivacy);
            AddUIEvent(this.ui.comProfileSettingContent.btnServiceDetail.onClick, OnBtnServiceDetailClicked);
            AddUIEvent(this.ui.comProfileSettingContent.tfRevertPurchase.onClick, OnBtnRestorePurchaseClicked);
            AddUIEvent(this.ui.comProfileSettingContent.btnPrivate.onClick, OnBtnPrivacyClicked);
            
            AddUIEvent(ui.comProfileSettingContent.buttonTongueSwitch.onClick, OnSwitchTongue);
            
            AddUIEvent(ui.comProfileSettingContent.buttonlv.onClick, OnSwitchlanguageLevel);
            AddUIEvent(ui.comProfileSettingContent.btnMail.onClick, OnBtnMailClicked);
            AddUIEvent(ui.comProfileSettingContent.btnJoin.onClick, OnBtnJoinClicked);   

            uiCom.SetAllTextLanguage(LanguageType.MotherTongue);       
        }

        protected override void OnHide()
        {
            RemoveNotifier();
            base.OnHide();
        }

        private void AddNotifier()
        {
            Notifier.instance.RegisterNotification(NotifyConsts.ChangeLanguage, OnChangeLanguage);
        }

        private void RemoveNotifier()
        {
            Notifier.instance.UnRegisterNotification(NotifyConsts.ChangeLanguage, OnChangeLanguage);
        }

        private void OnChangeLanguage(string name, object body)
        {
            ui.comProfileSettingContent.motherTongue.SetLanguage(I18N.inst.MotherLanguage);
            MainPathController mainPathCtrl = GetController<MainPathController>(ModelConsts.MainPath);
            mainPathCtrl.RequestGetUserCourse(MainPathController.MainPathOperateType.ChangeLanguage);//换语言
        }

        private void OnBtnPrivacyClicked()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
#if UNITY_EDITOR
            OpenUrl(LoginConst.privacyUrl);
#else
            OpenUrl(LoginConst.privacyUrl);
#endif

            //新埋点：用户点击服务隐私协议（钻石小店）
            DataDotShopPrivate dot = new DataDotShopPrivate();
            DataDotMgr.Collect(dot);
        }

        private void OnBtnServiceDetailClicked()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
#if UNITY_EDITOR
            OpenUrl(LoginConst.usertermsUrl);
#else
            OpenUrl(LoginConst.usertermsUrl);
#endif
            //新埋点：用户点击服务协议（钻石小店）
            DataDotShopService dot = new DataDotShopService();
            DataDotMgr.Collect(dot);
        }

        private void OnBtnRestorePurchaseClicked()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            PurchasingManager.instance.Restore(OnRestore);
            //新埋点：用户点击恢复购买（钻石小店）
            DataDotShopRestorePurchase dot = new DataDotShopRestorePurchase();
            DataDotMgr.Collect(dot);
        }

        private void OnRestore(bool success, string msg)
        {
            GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            if (!success)
            {
                GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToast(msg, true);
            }
        }


        private void OnSwitchTongue()
        {  
            _changeLangMode = ChangeLangMode.FirstLang;
            ShowTongueUI();
        }

        private void OnSwitchlanguageLevel()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            GetUI(UIConsts.ProfileLevelSettingUI).Show();
        }

        private void OnBtnMailClicked()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
            GetUI(UIConsts.SetEmailUI).Show();
        }

        private void OnBtnJoinClicked()
        {
            // Application.OpenURL("https://discord.com/invite/ANQ4munPzS");
            Application.OpenURL("https://wa.me/message/CZPO4NZ6264ID1");
        }

        private void ShowTongueUI()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            if (!GetUI<ProfileChooseLanguageUI>(UIConsts.ProfileChooseLanguageUI).isShow)
            {
                GetUI<ProfileChooseLanguageUI>(UIConsts.ProfileChooseLanguageUI).Show(_changeLangMode);
            }
        }

        private void OnRealChangeLang(LanguageCode chooseType)
        {
            if (_changeLangMode == ChangeLangMode.FirstLang)
            {
                ChangeFistLang(chooseType);
            }
        }

        async void ChangeFistLang(LanguageCode chooseType)
        {
            await GetController<LoginController>(ModelConsts.Login).SetSrvFirstLanguage(chooseType, mainModel.userID);
            
            I18NText.RefreshAllText();
            // QuitGame();
        }

        void QuitGame()
        {
#if UNITY_EDITOR
            EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }
        
        
        private void OnSwitchPrivacy()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.SelectionChanged);
            GetController<ProfileController>(ModelConsts.Profile).OnSwitchPrivacy();
        }

        protected override void OnShow()
        {
            //ui.motherState.selectedIndex = 1;

            UpdatePrivace();
   
            RefreshLangauage();
            RefreshVison();
            RefreshUI();

            AddNotifier();
            //GetController<PermissMsgController>(ModelConsts.PermissMsg).TryShowHalfDiscardUI();

            RefreshLanguageLevel();
        }

        private async void RefreshLanguageLevel()
        {
            await GetController<ProfileController>(ModelConsts.Profile).GetUserLanguagelevel();
            RefreshLanguage(model.language_level);
        }

        private void RefreshVison()
        {
            if (string.IsNullOrEmpty(AppConst.VersionHash))
            {
                ui.comProfileSettingContent.tfVersion.text = $"{Application.version}-{AppConstExt.LogicVersion}";
            }
            else {
                var shortHash = AppConst.VersionHash;
                if (shortHash.StartsWith("20")) {
                    shortHash = shortHash.Substring(2);//统一202x 和2x的开头为2x
                }
                //总字符传长度偏长
                if (AppConst.VersionHash.Length > 10)
                {
                    shortHash = AppConst.VersionHash.Substring(4,6);//目前hash为时间放弃年月
                }
                
                ui.comProfileSettingContent.tfVersion.text = $"{Application.version}-{AppConstExt.LogicVersion}-{shortHash }";
            }

        }
        public void RefreshLanguage(int levelID)
        {
            var levelCfg = Cfg.T.TBOnBoardingLevels.Get(levelID);
            ui.comProfileSettingContent.tfLv.SetKey(levelCfg.levelKey);
        }
        public void RefreshUI()
        {
            ui.tfScorTitle.SetKey("ui_profile_setting_tittle");
            ui.comProfileSettingContent.tfLanguageSetting.SetKey("ui_profile_setting_language_setings");
            ui.comProfileSettingContent.tfPrivacySetting.SetKey("ui_profile_setting_privacy_setting");
            ui.comProfileSettingContent.tfFeedback.SetKey("ui_profile_setting_feedback");
            ui.comProfileSettingContent.tfPrivacySwitch.SetKey("ui_profile_setting_privacy_switch");
            ui.comProfileSettingContent.tfPrivacyDesc.SetKey("ui_profile_setting_privacy_desc");
            ui.comProfileSettingContent.tfMotherTongue.SetKey("ui_profile_setting_mother_new");
            ui.comProfileSettingContent.lable.SetKey("profile_setting_version");
            ui.comProfileSettingContent.b1.tfLogout.SetKey("ui_profile_setting_logout");
            ui.comProfileSettingContent.b2.tfDel.SetKey("ui_profile_setting_del_account");
            ui.comProfileSettingContent.tfEmailTitle.SetKey("common_email");
            ui.comProfileSettingContent.tfJoin.SetKey("ui_notice_bottom1");
            ui.comProfileSettingContent.lableEnglishLevel.SetKey("ui_profile_setting_level");
            ui.comProfileSettingContent.tfServiceDetail.SetKey("ui_plan_common_service");
            ui.comProfileSettingContent.tfPrivate.SetKey("ui_plan_common_privacy");
            ui.comProfileSettingContent.tfRevertPurchase.SetKey("ui_plan_common_restore");
            ui.comProfileSettingContent.tfLegalTitle.SetKey("ui_profile_setting_legal");
            ui.comProfileSettingContent.tfMail.text = model.email;
        }

        public void RefreshEmail()
        {
            ui.comProfileSettingContent.tfMail.text = model.email;
        }
        
        private void RefreshLangauage()
        {
            ui.comProfileSettingContent.motherTongue.SetLanguage(I18N.inst.MotherLanguage);
            
            I18NText.RefreshAllText();
        }

        private void OnClickLDeleteAccount()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Nope);
            this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_profile_tips1", () =>
            {
                this.GetController<LoginController>(ModelConsts.Login).BackToLogin();
                this.Hide();
                //
                this.GetUI<ProfileLogoffUI>(UIConsts.ProfileLogoff).Open(() =>
                {
                    this.GetController<LoginController>(ModelConsts.Login).BackToLogin();
#if UNITY_EDITOR
                    EditorApplication.isPlaying = false;
#else
                Application.Quit();
#endif
                });
            }, null, 2, "ui_profile_confirm", "ui_profile_cancel", languageType: LanguageType.MotherTongue);
        }
        private void GetSignDataForIOSWidget(bool isLogin)
        {
            WidgetUtils.UpdateAndRefreshWidget();
        }
        private void OnClickLogOut()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
            this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_profile_setting_logout_tip", () =>
            {
                GetSignDataForIOSWidget(false);
                Hide();
                
                // todo 临时方案,初始化游戏世界 防止进入onboard出错
                             
                
                GetUI<ProfileSelfUI>(UIConsts.ProfileSelfUI).Hide();
                this.GetController<LoginController>(ModelConsts.Login).BackToLogin();
                var rkStr = AppRegionInfo.GetCurrRegionKeyAsStr();
                string deviceId = "";
                if (rkStr == AppRegionKey.eu.ToString())
                {
#if UNITY_ANDROID && !UNITY_EDITOR
                    deviceId = MTPushBinding.GetRegistrationId();
#endif
                }
                else if (rkStr == AppRegionKey.jpTest.ToString())
                {
#if UNITY_IOS && !UNITY_EDITOR
                    deviceId = JPushBinding.GetRegistrationId();
#endif
                }
                var logoutRequest = new LogoutRequest
                {
                    user_id = GetModel<MainModel>(ModelConsts.Main).playerID,
                    engagelab_device_id = deviceId,
                };
                UniTask.Void(async () =>
                {
                    var result = await GHttpManager.instance.PostAsync(
                        AppConst.LoginSrv + "/account/setuserregionalinfo", JsonMapper.ToJson(logoutRequest));
                    if (result.code == 200)
                    {
                        Debug.Log($"Passed logoutRequest");
                        Application.Quit();
                    }
                });
            }, null, 2, "ui_confirm_btn_1", "chat_btn_cancel", false, 1, languageType: LanguageType.MotherTongue);
        }

        private void OnClickFeedback()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
            GetUI<ProfileFeedbackUI>(UIConsts.ProfileFeedbackUI).Show();
        }

        public void UpdatePrivace()
        {
            ui.comProfileSettingContent.buttonPrivacy.ctrlOnOff.selectedIndex = model.isPrivace ? 1 : 0;

        }
        
        private void OpenUrl(string url)
        {
            MainModel mainModel = GetModel<MainModel>(ModelConsts.Main);

            // GameObject ctlPrefab = Resources.Load<GameObject>("Prefabs/WebViewCtl");
            GameObject ctlPrefab = GResManager.instance.LoadPrefab("WebViewCtl");     
            GameObject newCtl = GameObject.Instantiate(ctlPrefab);
            
            WebViewCtl ctl = newCtl.GetComponent<WebViewCtl>();
            if (ctl == null)
            {
                ctl = newCtl.AddComponent<WebViewCtl>();
            }
            ctl.Init(10f, I18N.inst.MotherLanguageStr, I18N.inst.ForeignLanguageStr, mainModel.toKen, I18N.inst.TempUILanguageStr,
                true,
                true,
                () =>
                { 
                    GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
                    {
                        GetUI<MainHeaderUI>(UIConsts.MainHeader).Refresh();
                    });
                },() =>
                {
                    GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
                },
                () =>
                {
                    GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                }
            );
            ctl.LoadUrl(url);
        }
    }
}
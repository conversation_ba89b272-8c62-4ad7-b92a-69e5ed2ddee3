﻿


using System;
using System.Collections.Generic;
using CommonUI;
using Msg.basic;
using Msg.core;
using Msg.dialog_task;
using Msg.explore;
using Msg.social;
using Msg.tts;
using ScriptsHot.Game.Modules.Chat.ChatCell;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.ChatLogicNew.ChatState;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Explore.ExploreType.Base;
using ScriptsHot.Game.Modules.Explore.State.Base;
using ScriptsHot.Game.Modules.Explore.State.ExploreChat;
using ScriptsHot.Game.Modules.Procedure;
using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using ScriptsHot.Game.Modules.Task;
using UIBind.Explore.Item;
using UnityEngine;
using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.Explore.ExploreType
{

    /// <summary>
    /// Normal 对话
    /// </summary>
    public class ExploreNormalEntity:ExploreEntityBase
    {


        protected ExploreComponentBase _curCom = null;

        public ExploreNormalEntity()
        {
   
        }

        public override void Init(ExploreParam param,PB_DialogTaskPreloadData data,ExploreCellBase ui)
        {
            _param = param;
            base.Init(param,data,ui);
            
            if (_controller.CurExplore.Type == ExploreOperationType.Active)
            {
                _curCom = this.AddComponent<ExploreNormalEntityActiveCom>() as ExploreNormalEntityActiveCom;
                _curCom.SetCell(_ui);
            }
            else  if (_controller.CurExplore.Type == ExploreOperationType.Auto)
            {
                _curCom = this.AddComponent<ExploreNormalEntityAutoCom>() as ExploreNormalEntityAutoCom;
                _curCom.SetCell(_ui);
            }
            
            this.InitComponents();
        }


        protected override void InitStateMachine()
        {
            base.InitStateMachine();
            _stateMachine.AddState(new ExploreStateStart(this));
            _stateMachine.AddState(new ExploreChatActiveStart(this));
            _stateMachine.AddState(new ExploreStateExit(this));
        }

        public override void Enter()
        {
            base.Enter();
            _controller.UpdateCurEntityInfo(this.EntityId,this.DialogMode);
            ChangeState(ExploreStateName.Start,_param);
        }
        
        public override void Exit()
        {
            base.Exit();
            _curCom.Clear();
        }

        public override void AddEvent()
        {
            UnEvent();
            _curCom.AddEvent();
            
            //用户自己说话数据
            MsgManager.instance.RegisterCallBack<SC_DialogDownMsgForASR>(this.OnUserDialogContentNtf);
            //用户回复示例（user_reply_example: 也叫事前脚手架）
            MsgManager.instance.RegisterCallBack<SC_DialogDownMsgForUserReplyExample>(this.OnScaffoldNtf);
            //advice
            MsgManager.instance.RegisterCallBack<SC_DialogDownMsgForAdvice>(this.OnAdviceNtf);
            //用户回复示例翻译
            MsgManager.instance.RegisterCallBack<SC_DialogDownMsgForUserReplyExampleTranslate>(this.OnScaffoldTranslateNtf);
            /*
             * 用户回复示例TTS
             * 1. 流式下发：一个音频会出现多个结果
             * 2. 非流式下发：一个音频只有一个结果
             */
            MsgManager.instance.RegisterCallBack<SC_DialogDownMsgForUserReplyExampleTTS>(this.OnScaffoldTTsNtf);

            
            
            //avatar说话文本数据
            MsgManager.instance.RegisterCallBack<SC_DialogDownMsgForAvatarReply>(this.OnAvatarDialogContentNtf);
            //avatar说话翻译
            MsgManager.instance.RegisterCallBack<SC_DialogDownMsgForAvatarReplyTranslate>(this.OnAvatarDialogTranslateNtf);
            //avatar说话语音数据
            MsgManager.instance.RegisterCallBack<SC_DialogDownMsgForAvatarReplyTTS>(this.OnAvatarAudioNtf);
            
            //对话下行反馈结果
            MsgManager.instance.RegisterCallBack<SC_DialogDownMsgForFeedback>(this.OnDialogDownMsg);
            
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreStepStateChange,OnExploreStepStateChange);

        }

      

        public override void UnEvent()
        {
            _curCom.RemoveEvent();
            
            //用户自己说话数据
            MsgManager.instance.UnRegisterCallBack<SC_DialogDownMsgForASR>(this.OnUserDialogContentNtf);
            //用户回复示例（user_reply_example: 也叫事前脚手架）
            MsgManager.instance.UnRegisterCallBack<SC_DialogDownMsgForUserReplyExample>(this.OnScaffoldNtf);
            //用户回复示例翻译
            MsgManager.instance.UnRegisterCallBack<SC_DialogDownMsgForUserReplyExampleTranslate>(this.OnScaffoldTranslateNtf);
            /*
             * 用户回复示例TTS
             * 1. 流式下发：一个音频会出现多个结果
             * 2. 非流式下发：一个音频只有一个结果
             */
            MsgManager.instance.UnRegisterCallBack<SC_DialogDownMsgForUserReplyExampleTTS>(this.OnScaffoldTTsNtf);
            
            //avatar说话文本
            MsgManager.instance.UnRegisterCallBack<SC_DialogDownMsgForAvatarReply>(this.OnAvatarDialogContentNtf);
            //avatar说话翻译
            MsgManager.instance.UnRegisterCallBack<SC_DialogDownMsgForAvatarReplyTranslate>(this.OnAvatarDialogTranslateNtf);
            //avatar说话语音流数据
            MsgManager.instance.UnRegisterCallBack<SC_DialogDownMsgForAvatarReplyTTS>(this.OnAvatarAudioNtf);
            
            //对话下行反馈结果
            MsgManager.instance.UnRegisterCallBack<SC_DialogDownMsgForFeedback>(this.OnDialogDownMsg);

            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreStepStateChange,OnExploreStepStateChange);

        }

        protected void OnExploreStepStateChange(string name, object body)
        {
            Step = (ExploreStep) body;
            VFDebug.Log("Explore Step： " + Step);
            if (Step == ExploreStep.Begin)
            {
                _controller.CloseRecordUI();
            }
            else if (Step == ExploreStep.Player)
            {
                _controller.HideRecordUI();
            }
        }

        public override void OnNewWorldAudioStart()
        {
            Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
            Notifier.instance.SendNotification(NotifyConsts.ExploreSoundStop);
        }


        /// <summary>
        /// 用户自己说话数据--流返回
        /// </summary>
        /// <param name="msg"></param>
        protected void OnUserDialogContentNtf(SC_DialogDownMsgForASR msg)
        {
            // Debug.LogError("/用户自己说话数据--返回:taskId::" + msg.commonData.taskId  + "       cur:" + this.EntityId);
            if (msg.commonData.taskId != EntityId) return;

            if (msg.code != PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
            {
                _controller.GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("common_toast",true);
                _controller.OnCancelRecored();
                //状态修改
                Notifier.instance.SendNotification(NotifyConsts.ExploreStepStateChange,ExploreStep.Normal);
                //重新显示avatar cell
                if (this.Round == 0)
                {
                    Notifier.instance.SendNotification(NotifyConsts.ExploreChatFristAvatarShow,true);
                }
                else
                {
                    Notifier.instance.SendNotification(NotifyConsts.ExploreChatAvatarShow);
                }
                return;
            }

            _controller.Model.SetStreamUserDialogInfo(msg);
        }
        
        /// <summary>
        /// avatar说话数据--流返回
        /// </summary>
        /// <param name="msg"></param>
        protected void OnAvatarDialogContentNtf(SC_DialogDownMsgForAvatarReply msg)
        {
            Debug.Log("/avatar说话数据--流返回:step::"  + msg.commonData.taskId  + "       cur:" + this.EntityId + "   msg.commonData.round " + msg.commonData.round  +  "       round:" + Round);
            if (msg.commonData.taskId != EntityId) return;
            if (msg.commonData.round != Round) return;
            _controller.Model.SetStreamAvatarDialogInfo(msg);
        }
        
        /// <summary>
        /// avatar说话翻译--流返回
        /// </summary>
        /// <param name="msg"></param>
        protected void OnAvatarDialogTranslateNtf(SC_DialogDownMsgForAvatarReplyTranslate msg)
        {
            Debug.Log("/avatar说话翻译--返回:taskId::" + msg.commonData.taskId  + "       cur:" + this.EntityId + "   msg.commonData.round " + msg.commonData.round  +  "       round:" + Round);
            if (msg.commonData.taskId != EntityId) return;
            if (msg.commonData.round != Round) return;
            _controller.Model.SetStreamAvatarDialogTranslate(msg);
        }
        
        /// <summary>
        ///avatar说话语音数据--流返回
        /// </summary>
        /// <param name="msg"></param>
        protected void OnAvatarAudioNtf(SC_DialogDownMsgForAvatarReplyTTS msg)
        {
            Debug.Log("/avatar说话语音数据--返回:step::"  + msg.commonData.taskId  + "       cur:" + this.EntityId + "   msg.commonData.round " + msg.commonData.round  +  "       round:" + Round);
            if (msg.commonData.taskId != EntityId) return;
            if (msg.commonData.round != Round) return;
            _controller.Model.SetStreamAvatarAudio(msg);
        }
        
        /// <summary>
        /// advice
        /// </summary>
        /// <param name="msg"></param>
        private void OnAdviceNtf(SC_DialogDownMsgForAdvice msg)
        {
            if (msg.commonData.taskId != EntityId) return;
            _controller.Model.SetAdvice(msg);
        }
        
        /// <summary>
        /// 用户回复示例（user_reply_example: 也叫事前脚手架）
        /// </summary>
        /// <param name="msg"></param>
        protected void OnScaffoldNtf(SC_DialogDownMsgForUserReplyExample msg)
        {
            if (msg.commonData.taskId != EntityId) return;
            _controller.Model.SetScaffold(msg);
        }
        /// <summary>
        /// 用户回复示例翻译
        /// </summary>
        /// <param name="msg"></param>
        protected void OnScaffoldTranslateNtf(SC_DialogDownMsgForUserReplyExampleTranslate msg)
        {
            if (msg.commonData.taskId != EntityId) return;
            _controller.Model.SetScaffoldTranslate(msg);
        }
        /*
          * 用户回复示例TTS
          * 1. 流式下发：一个音频会出现多个结果
          * 2. 非流式下发：一个音频只有一个结果
          */
        protected void OnScaffoldTTsNtf(SC_DialogDownMsgForUserReplyExampleTTS msg)
        {
            if (msg.commonData.taskId != EntityId) return;
            _controller.Model.SetScaffoldTTs(msg);
        }
        /// <summary>
        /// 对话下行反馈结果
        /// </summary>
        /// <param name="msg"></param>
        protected void OnDialogDownMsg(SC_DialogDownMsgForFeedback msg)
        {
            if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
            {
                _controller.Model.SetAwardMsg(msg);
            }
            else
            {
                VFDebug.LogError($"Explore奖励结算失败：{msg.code}");
            }

        }
    }
}
using System.Collections;
using System.Collections.Generic;
using Msg.economic;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Shop
{
    public partial class ShopModel
    {
        public enum SubscribeType
        {
            None,
            Month,
            Quarter,
            Year,
            Lifetime
        }
        
        public enum SubscribeTypeFullName
        {
            NoneMember,
            MonthPremium,
            QuarterPremium,
            YearPremium
        }
        
        public enum DiscountType
        {
            None,
            DISCOUNT,
            FREE_TRIAL,
            ORIGIN_PRICE
        }
        
        private DiscountType DiscountTp;

        public void SetPromotionType(PB_PromotionType type)
        {
  
            switch (type)
            {
                case PB_PromotionType.DISCOUNT:
                    DiscountTp = DiscountType.DISCOUNT;
                    break;
                case PB_PromotionType.ORIGIN_PRICE:
                    //这是因为没有对应流程，临时 用
                    DiscountTp = DiscountType.FREE_TRIAL;
                    break;
                default:
                    DiscountTp = DiscountType.FREE_TRIAL;
                    break;
            }
            VFDebug.Log($"#########################收到服务器分组数据 = {type} , 实际使用 = {DiscountTp}");
        }

        public string GetMaxDiscountStr()
        {
            var monthValue = GetDiscount(SubscribeType.Month);
            var quarterValue = GetDiscount(SubscribeType.Quarter);
            var yearValue = GetDiscount(SubscribeType.Year);
            
            var maxValue1 = Mathf.Max(monthValue, quarterValue);
            var maxValue2 = Mathf.Max(monthValue, yearValue);
            var maxValue = Mathf.Max(maxValue1, maxValue2);
            
            if (maxValue <= 0)
            {
                return string.Empty;
            }

            return  $"{maxValue}%"  ;
        }
        
        public string GetDiscountStr(SubscribeType tp)
        {
            var value = GetDiscount(tp);
            if (value <= 0)
            {
                return string.Empty;
            }
            return $"{value}%";
        }
        
        public int GetDiscount(SubscribeType tp)
        {
            if (tp == SubscribeType.Month)
            {
                if (TryGetMemberTypeByName(ShopController.MEMBER_TYPE_MONTH, out var cfg_momnthly))
                {
                    return ShopUIUtil.GetSaleOffValue(cfg_momnthly);
                }
            }
            else
            {
                if (TryGetMemberTypeByName(ShopController.MEMBER_TYPE_YEAR, out var cfg_yearly))
                {
                    return ShopUIUtil.GetSaleOffValue(cfg_yearly);
                }
                else if (TryGetMemberTypeByName(ShopController.MEMBER_TYPE_QUARTER, out var cfg_quarterly))
                {
                    return ShopUIUtil.GetSaleOffValue(cfg_quarterly);
                }
            }
            return 0;
        }
        
        public DiscountType GetDiscountType()
        {
            // return DiscountType.DISCOUNT;
            return DiscountTp;
        }
        
        public bool IsDiscountType=> GetDiscountType() == DiscountType.DISCOUNT;

        public bool IsShowDiscountNode()
        {
            if (GameEntry.MainC.MainModel.IsUnlimitedStamina())
                return false;
            return IsDiscountType;
        }





    }
}

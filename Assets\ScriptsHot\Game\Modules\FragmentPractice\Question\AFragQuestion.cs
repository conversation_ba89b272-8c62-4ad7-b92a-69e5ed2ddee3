using System;
using FairyGUI;
using ScriptsHot.Game.Modules.FragmentPractice;

using CommonUI;
using ScriptsHot.Game.Modules.Common;
using UnityEngine;

namespace UIBind.FragmentPractice
{
    public abstract class AFragQuestion : GComponent
    {
        protected bool IsCurrent { get; private set; }
        public APracticeData Practice { get; private set; }

        protected Color ClearColor = new(17f / 255f, 17f / 255f, 17f / 255f, 0);
        protected Color BlackColor = new(17f / 255f, 17f / 255f, 17f / 255f, 1);
        protected Color PurpleColor = new Color(102f / 255f, 50f / 255f, 1f, 1);
        
        public AFragQuestion()
        {
            onAddedToStage.Add(OnAddedToStage);
            onRemovedFromStage.Add(OnRemovedFromStage);
        }
        override public void Dispose()
        {
            OnRemovedFromStage();
            base.Dispose();
        }
        
        protected virtual void OnAddedToStage(){}

        protected virtual void OnRemovedFromStage(){}

        virtual public void Init(bool isCurrent, APracticeData practice) 
        {
            IsCurrent = isCurrent;
            Practice = practice;
        }
        
        abstract public void ShowPractice(AFragAnswer answer);
    }
}
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using FairyGUI;
using Game.Modules.FragmentPractice;
using Msg.question;
using ScriptsHot.Game.Modules.FragmentPractice;

namespace UIBind.FragmentPractice
{
    public interface ICompleteAnswer
    {
        PB_OptionalAnswer[] GetAnswerOptions();
    }

    public partial class CompleteAnswer : AFragAnswer, IQuestionEventListener
    {
        private List<CompleteInput> inputs = new List<CompleteInput>();
        private PB_OptionalAnswer[] options;
        override protected void OnRemovedFromStage()
        {
            QuestionEventManager.Instance.RemoveListener(this);
        }

        public override void Init(bool isCurrent, APracticeData practice)
        {
            base.Init(isCurrent, practice);
            if (isCurrent)
            {
                QuestionEventManager.Instance.AddListener(this);
            }
            HasInput = true;
        }

        override public void ShowPractice(AFragQuestion questionComp)
        {
            List<PB_BlankRange> blankRange = Practice.GetBlankRanges();
            string[] words = RebuildWords(blankRange);

            ICompleteAnswer completeAnswer = Practice as ICompleteAnswer;

            options = completeAnswer.GetAnswerOptions();

            inputs.Clear();

            listTexts.RemoveChildren(0, -1, true);
            var stemLength = 0;
            var blankIndex = 0;

            for (int wordIndex = 0; wordIndex < words.Length; wordIndex++)
            {
                string word = words[wordIndex];

                if (blankIndex <= blankRange.Count - 1 && stemLength == blankRange[blankIndex].start_index)
                {
                    // 如果是空格
                    CompleteInput tf = UIPackage.CreateObject(CompleteInput.pkgName, CompleteInput.comName).asCom as CompleteInput;
                    tf.input.onFocusIn.Add(OnTextFocusIn);
                    // tf.input.onFocusOut.Add(OnTextFocusOut);
                    tf.input.onChanged.Add(OnTextInput);
                    tf.input.singleLine = true;
                    tf.input.restrict = @"[a-zA-Z0-9\- ']*";
                    tf.input.disableIME = true;
                    tf.input.keyboardType = (int)TouchScreenKeyboardType.ASCIICapable;
                    inputs.Add(tf);
                    listTexts.AddChild(tf);

                    UnityEngine.Debug.Log("CompleteAnswer-ShowPractice For:  wIdx=" + wordIndex + " blankIndex=" + blankIndex);
                    tfSample.text = options[blankIndex].answer[0];  // 用他算一个长度
                    tf.SetSize(tfSample.width + 60, tfSample.height);
                    stemLength += word.Length + 1;  // 加上空格的长度

                    blankIndex++;
                }
                else
                {
                    // 如果不是空格
                    GTextField tf = new GTextField();
                    tf.textFormat = tfSample.textFormat;
                    tf.autoSize = AutoSizeType.Both;
                    stemLength += word.Length + 1;  // 加上空格的长度
                    tf.text = word;
                    listTexts.AddChild(tf);
                }
            }

            listTexts.ResizeToFit();
        }

        // 重新构建单词，空格用 _ 代替，目的是把连续的空格拼成一个单词
        private string[] RebuildWords(List<PB_BlankRange> blankRange)
        {
            var stem = Practice.GetStem();
            char[] stemWithBlank = stem.ToCharArray();
            foreach (var blank in blankRange)
            {
                for (int i = blank.start_index; i < blank.end_index; i++)
                {
                    stemWithBlank[i] = '_';
                }
            }
            stem = new string(stemWithBlank);
            return stem.Split(new char[] { ' ' }, System.StringSplitOptions.RemoveEmptyEntries);
        }

        private void OnTextFocusIn(EventContext context)
        {
            DotPracticeManager.Instance.Collect(new DataDot_FocusComplete());             
        }

        private void OnTextFocusOut(EventContext context)
        {
            BubbleEvent("onInputFocusOut", context);
        }

        private void OnTextInput(EventContext context)
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            
            var allInputed = inputs.All(input => input.input.text.Length > 0);
            var rightCount = 0;
            for (int i = 0; i < inputs.Count; i++)
            {

                if (options[i].answer.Contains(inputs[i].input.text))
                {
                    rightCount++;
                }
            }

            string fullText = GetJoinedText();

            CommitAnswer(Practice.GetScore(fullText), allInputed);
        }

        private string GetJoinedText()
        {
            string fullText = "";
            for (int i = 0; i < listTexts.numChildren; i++)
            {
                GObject child = listTexts.GetChildAt(i);
                if (child is GTextField textField)
                {
                    fullText += textField.text;
                    if (i < listTexts.numChildren - 1)
                    {
                        fullText += " ";
                    }
                }
                else if (child is CompleteInput inputComp)
                {
                    fullText += inputComp.input.text;
                    if (i < listTexts.numChildren - 1)
                    {
                        fullText += " ";
                    }
                }
            }

            VFDebug.Log($"complete answer 完整句子: {fullText}");
            return fullText;
        }

        public void OnAnswered(){}

        public void OnSubmit()
        {
            touchable = false;
            DotPracticeManager.Instance.Collect(new DataDot_TypeComplete(GetJoinedText())); 
        }

        public void OnRetry(){}

        public void AutoCheck(){}

        public void OnReset(){}

        public void OnJumpListenTask(){}

        public void OnJumpSpeakTask(){}
    }
}
using UnityEngine;
using FairyGUI;
using Game.Modules.FragmentPractice;
using Msg.question;
using System.Collections.Generic;
using UnityEngine.UIElements;
using System;
using DG.Tweening;
using MatchOptionData = UIBind.FragmentPractice.MatchAnswer.MatchOptionData;

namespace UIBind.FragmentPractice
{
    public partial class OldMatchAnswer : AFragAnswer
    {

        private IMatchAnswer MatchAnswerData => Practice as IMatchAnswer;

        private BtnBaseAnswer selectedButton;
        private int remainMatchCount = 0;
        private const int TIME_MATCHING = 300;

        private readonly List<TimerManager.Handler> errorHandlers = new();

        private AFragQuestion _questionComp;
        protected override void OnRemovedFromStage()
        {
            errorHandlers.Clear();
            base.OnRemovedFromStage();
        }

        public override void ShowPractice(AFragQuestion questionComp)
        {
            _questionComp = questionComp;
            var choices = MatchAnswerData.GetChoices();
            ShowOptions(choices, leftList, true);
            ShowOptions(choices, rightList, false);
            remainMatchCount = choices.Length;
            grpMatch.EnsureBoundsCorrect();
        }

        public override void FitToPreferedHeight()
        {
            leftList.ResizeToFit();
            rightList.ResizeToFit();

            height = leftList.height;
        }

        private void ShowOptions(PB_Choice[] choices, GList list, bool isEnglish)
        {
            list.RemoveChildren(0, -1, true);
            list.numItems = choices.Length;

            Shuffle(choices);
            for (int i = 0; i < choices.Length; i++)
            {
                var item = list.GetChildAt(i) as OldBtnBaseAnswer;
                var data = choices[i];
                item.data = new MatchOptionData() { data = data, isEnglish = isEnglish };
                if (Practice.QuestionType != PB_QuickPracticeType.ListenWordAndMatchWord) //耳机关卡 的 Match
                {
                    item.height = 144;
                }

                if (isEnglish)
                {
                    if (Practice.QuestionType == PB_QuickPracticeType.ReadWordAndMatchWord)
                    {
                        item.SetMode(data.text, data.tts_id, true, false);
                    }
                    else if (Practice.QuestionType == PB_QuickPracticeType.ListenWordAndMatchWord)
                    {
                        item.SetMode(data.text, data.tts_id, false, true);
                    }
                }
                else
                {
                    item.SetMode(data.translation, 0, true, false);
                }
                item.SetState(BtnBaseAnswer.State.Normal);

                item.onClick.Add(OnItemClick);
            }

            list.ResizeToFit();
        }

        private void OnItemClick(EventContext context)
        {
            BtnBaseAnswer crntItem = context.sender as BtnBaseAnswer;
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);

            var hasMatched = false;

            if (selectedButton == null)
            {
                // 没有选中按钮，选中当前按钮
                selectedButton = crntItem;
                selectedButton.SetState(BtnBaseAnswer.State.Select);
            }
            else
            {
                if (selectedButton == crntItem)
                {
                    // 同一个按钮，取消选择
                    selectedButton.SetState(BtnBaseAnswer.State.Normal);
                    selectedButton = null;
                }
                else
                {
                    // 做比对，如果选中按钮和当前按钮是同一边，则取消之前的选择
                    if (selectedButton.data is MatchOptionData selectedData && crntItem.data is MatchOptionData crntData)
                    {
                        if (selectedData.isEnglish == crntData.isEnglish)
                        {
                            // 同一边，取消之前的选择并选中当前的
                            selectedButton.SetState(BtnBaseAnswer.State.Normal);

                            selectedButton = crntItem;
                            selectedButton.SetState(BtnBaseAnswer.State.Select);
                        }
                        else
                        {
                            // 不同边，比较匹配
                            var isMatch = selectedData.data.text.Equals(crntData.data.text);

                            if (isMatch)
                            {
                                // 匹配成功
                                selectedButton.touchable = false;
                                crntItem.touchable = false;
                                hasMatched = true;

                                selectedButton.SetState(BtnBaseAnswer.State.Right);
                                crntItem.SetState(BtnBaseAnswer.State.Right);

                                Scale(selectedButton);
                                Scale(crntItem);

                                var selectedButton_ = selectedButton;
                                var crntItem_ = crntItem;
                                selectedButton = null;

                                if (remainMatchCount > 1)
                                {
                                    // 最后全对的声音留给外面统一的叮铃
                                    SoundManger.instance.PlayUI("question_right");
                                }

                                TimerManager.Handler errorHandler = new((c) =>
                                {
                                    selectedButton_.SetGrpState(BtnBaseAnswer.AnswerGrpState.RegularText);
                                    crntItem_.SetGrpState(BtnBaseAnswer.AnswerGrpState.RegularText);

                                    selectedButton_.SetState(BtnBaseAnswer.State.Gray);
                                    crntItem_.SetState(BtnBaseAnswer.State.Gray);

                                    //Debug.Log($"MatchAnswer succ,remainMatchCount from:{remainMatchCount} to:{remainMatchCount-1}");
                                    remainMatchCount--;

                                    if (remainMatchCount <= 0)
                                    {
                                        Debug.Log($"MatchAnswer all succ,remainMatchCount:{remainMatchCount}");
                                        CommitAnswer(1, true, true);
                                    }
                                }, TIME_MATCHING);

                                errorHandlers.Add(errorHandler);
                            }
                            else
                            {
                                // 匹配失败
                                selectedButton.touchable = false;
                                crntItem.touchable = false;

                                selectedButton.SetState(BtnBaseAnswer.State.Error);
                                crntItem.SetState(BtnBaseAnswer.State.Error);

                                var selectedButton_ = selectedButton;
                                var crntItem_ = crntItem;
                                selectedButton = null;

                                OnErrorAnswer();

                                TimerManager.Handler errorHandler = new((c) =>
                                {
                                    selectedButton_.touchable = true;
                                    crntItem_.touchable = true;

                                    selectedButton_.SetState(BtnBaseAnswer.State.Normal);
                                    crntItem_.SetState(BtnBaseAnswer.State.Normal);
                                }, TIME_MATCHING);
                                errorHandlers.Add(errorHandler);

                                if (display == BtnBaseAnswer.BtnDisPlay.unlock) CommitAnswer(0);
                            }
                        }
                    }
                }
            }

            if (crntItem?.data is MatchOptionData optionData)
            {
                DotPracticeManager.Instance.Collect(new DataDot_MatchButton(optionData.data.tts_id > 0, optionData.data.text, optionData.isEnglish, hasMatched));
            }
        }

        private void Scale(GObject item)
        {
            if (item != null)
            {
                // 先放大
                DOTween.To(() => item.scale, x => item.scale = x, new Vector2(1.1f, 1.1f), 0.2f)
                    .SetEase(Ease.OutCubic)
                    .OnComplete(() =>
                    {
                        if (item != null)
                        {
                            // 再缩小
                            DOTween.To(() => item.scale, x => item.scale = x, new Vector2(1f, 1f), 0.1f)
                                .SetEase(Ease.OutBounce);
                        }
                    });
            }
        }

        private void Shuffle(PB_Choice[] choices)
        {
            int n = choices.Length;
            while (n > 1)
            {
                n--;
                int k = UnityEngine.Random.Range(0, n + 1);
                var temp = choices[k];
                choices[k] = choices[n];
                choices[n] = temp;
            }
        }
    }
}
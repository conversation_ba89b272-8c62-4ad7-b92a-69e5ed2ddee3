// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/matching.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/matching.proto</summary>
  public static partial class MatchingReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/matching.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static MatchingReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Ch9wcm90b2J1Zi9leHBsb3JlL21hdGNoaW5nLnByb3RvIpsCChBQQl9NYXRj",
            "aGluZ1VwTXNnEjUKEHVwRmluZFBhcnRuZXJSZXEYASABKAsyGS5QQl9NYXRj",
            "aGluZ1VwX01hdGNoU3RhcnRIABI+ChR1cFByZU1hdGNoQ29uZmlybVJlcRgC",
            "IAEoCzIeLlBCX01hdGNoaW5nVXBfUHJlTWF0Y2hDb25maXJtSAASNgoQdXBN",
            "YXRjaENhbmNlbFJlcRgDIAEoCzIaLlBCX01hdGNoaW5nVXBfTWF0Y2hDYW5j",
            "ZWxIABI8ChN1cEdldE1hdGNoU3RhdHVzUmVxGAQgASgLMh0uUEJfTWF0Y2hp",
            "bmdVcF9HZXRNYXRjaFN0YXR1c0gAEg4KBnVudXNlZBgFIAEoBUIKCgh1cEJp",
            "ek1zZyIqChhQQl9NYXRjaGluZ1VwX01hdGNoU3RhcnQSDgoGdW51c2VkGAEg",
            "ASgDIjgKHVBCX01hdGNoaW5nVXBfUHJlTWF0Y2hDb25maXJtEhcKD21hdGNo",
            "X3JlY29yZF9pZBgBIAEoAyI0ChlQQl9NYXRjaGluZ1VwX01hdGNoQ2FuY2Vs",
            "EhcKD21hdGNoX3JlY29yZF9pZBgBIAEoAyI3ChxQQl9NYXRjaGluZ1VwX0dl",
            "dE1hdGNoU3RhdHVzEhcKD21hdGNoX3JlY29yZF9pZBgBIAEoAyJlChtTQ19N",
            "YXRjaGluZ0Rvd25fTWF0Y2hTdGF0dXMSFwoPbWF0Y2hfcmVjb3JkX2lkGAEg",
            "ASgDEi0KD2Rvd25NYXRjaFN0YXR1cxgCIAEoDjIULkVOVU1fTWF0Y2hpbmdT",
            "dGF0dXMi/AEKHVNDX01hdGNoaW5nRG93bl9NYXRjaGVkUmVzdWx0EhMKC3Vz",
            "ZXJjaGF0X2lkGAEgASgDEhcKD21hdGNoX3JlY29yZF9pZBgCIAEoAxIWCg5w",
            "YXJ0bmVyX3VzZXJpZBgDIAEoAxIQCghhdmF0YXJJZBgEIAEoAxISCgpoZWFk",
            "UGljVXJsGAUgASgJEhAKCG5pY2tuYW1lGAYgASgJEhYKDkFnb3JhX3VpZF9z",
            "ZWxmGAcgASgNEhkKEUFnb3JhX3VpZF9wYXJ0bmVyGAggASgNEhMKC0Fnb3Jh",
            "X3Rva2VuGAkgASgJEhUKDUFnb3JhX2NoYW5uZWwYCiABKAkiLQobU0NfTWF0",
            "Y2hpbmdEb3duX01hdGNoRmFpbGVkEg4KBnJlYXNvbhgBIAEoCSrlAQoTRU5V",
            "TV9NYXRjaGluZ1N0YXR1cxIcChhFT19NYXRjaGluZ1N0YXR1c19VbnVzZWQQ",
            "ABIdChlFT19NYXRjaGluZ1N0YXR1c19XYWl0aW5nEAESJQohRU9fTWF0Y2hp",
            "bmdTdGF0dXNfUHJlTWF0Y2hTdWNjZXNzEAISIgoeRU9fTWF0Y2hpbmdTdGF0",
            "dXNfTWF0Y2hTdWNjZXNzEAMSIwofRU9fTWF0Y2hpbmdTdGF0dXNfTWF0Y2hD",
            "YW5jZWxlZBAEEiEKHUVPX01hdGNoaW5nU3RhdHVzX01hdGNoRmFpbGVkEAVC",
            "KloadmZfcHJvdG9idWYvc2VydmVyL2V4cGxvcmWqAgtNc2cuZXhwbG9yZWIG",
            "cHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.explore.ENUM_MatchingStatus), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_MatchingUpMsg), global::Msg.explore.PB_MatchingUpMsg.Parser, new[]{ "upFindPartnerReq", "upPreMatchConfirmReq", "upMatchCancelReq", "upGetMatchStatusReq", "unused" }, new[]{ "upBizMsg" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_MatchingUp_MatchStart), global::Msg.explore.PB_MatchingUp_MatchStart.Parser, new[]{ "unused" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_MatchingUp_PreMatchConfirm), global::Msg.explore.PB_MatchingUp_PreMatchConfirm.Parser, new[]{ "match_record_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_MatchingUp_MatchCancel), global::Msg.explore.PB_MatchingUp_MatchCancel.Parser, new[]{ "match_record_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_MatchingUp_GetMatchStatus), global::Msg.explore.PB_MatchingUp_GetMatchStatus.Parser, new[]{ "match_record_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MatchingDown_MatchStatus), global::Msg.explore.SC_MatchingDown_MatchStatus.Parser, new[]{ "match_record_id", "downMatchStatus" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MatchingDown_MatchedResult), global::Msg.explore.SC_MatchingDown_MatchedResult.Parser, new[]{ "userchat_id", "match_record_id", "partner_userid", "avatarId", "headPicUrl", "nickname", "Agora_uid_self", "Agora_uid_partner", "Agora_token", "Agora_channel" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MatchingDown_MatchFailed), global::Msg.explore.SC_MatchingDown_MatchFailed.Parser, new[]{ "reason" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  ///
  /// 撮合状态枚举
  /// </summary>
  public enum ENUM_MatchingStatus {
    [pbr::OriginalName("EO_MatchingStatus_Unused")] EO_MatchingStatus_Unused = 0,
    [pbr::OriginalName("EO_MatchingStatus_Waiting")] EO_MatchingStatus_Waiting = 1,
    [pbr::OriginalName("EO_MatchingStatus_PreMatchSuccess")] EO_MatchingStatus_PreMatchSuccess = 2,
    [pbr::OriginalName("EO_MatchingStatus_MatchSuccess")] EO_MatchingStatus_MatchSuccess = 3,
    [pbr::OriginalName("EO_MatchingStatus_MatchCanceled")] EO_MatchingStatus_MatchCanceled = 4,
    [pbr::OriginalName("EO_MatchingStatus_MatchFailed")] EO_MatchingStatus_MatchFailed = 5,
  }

  #endregion

  #region Messages
  /// <summary>
  ///
  /// 撮合功能上行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MatchingUpMsg : pb::IMessage<PB_MatchingUpMsg>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MatchingUpMsg> _parser = new pb::MessageParser<PB_MatchingUpMsg>(() => new PB_MatchingUpMsg());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MatchingUpMsg> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MatchingReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUpMsg() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUpMsg(PB_MatchingUpMsg other) : this() {
      unused_ = other.unused_;
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.upFindPartnerReq:
          upFindPartnerReq = other.upFindPartnerReq.Clone();
          break;
        case upBizMsgOneofCase.upPreMatchConfirmReq:
          upPreMatchConfirmReq = other.upPreMatchConfirmReq.Clone();
          break;
        case upBizMsgOneofCase.upMatchCancelReq:
          upMatchCancelReq = other.upMatchCancelReq.Clone();
          break;
        case upBizMsgOneofCase.upGetMatchStatusReq:
          upGetMatchStatusReq = other.upGetMatchStatusReq.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUpMsg Clone() {
      return new PB_MatchingUpMsg(this);
    }

    /// <summary>Field number for the "upFindPartnerReq" field.</summary>
    public const int upFindPartnerReqFieldNumber = 1;
    /// <summary>
    /// 寻找聊伴请求
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MatchingUp_MatchStart upFindPartnerReq {
      get { return upBizMsgCase_ == upBizMsgOneofCase.upFindPartnerReq ? (global::Msg.explore.PB_MatchingUp_MatchStart) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.upFindPartnerReq;
      }
    }

    /// <summary>Field number for the "upPreMatchConfirmReq" field.</summary>
    public const int upPreMatchConfirmReqFieldNumber = 2;
    /// <summary>
    /// 预撮合确认请求
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MatchingUp_PreMatchConfirm upPreMatchConfirmReq {
      get { return upBizMsgCase_ == upBizMsgOneofCase.upPreMatchConfirmReq ? (global::Msg.explore.PB_MatchingUp_PreMatchConfirm) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.upPreMatchConfirmReq;
      }
    }

    /// <summary>Field number for the "upMatchCancelReq" field.</summary>
    public const int upMatchCancelReqFieldNumber = 3;
    /// <summary>
    /// 撮合取消请求
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MatchingUp_MatchCancel upMatchCancelReq {
      get { return upBizMsgCase_ == upBizMsgOneofCase.upMatchCancelReq ? (global::Msg.explore.PB_MatchingUp_MatchCancel) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.upMatchCancelReq;
      }
    }

    /// <summary>Field number for the "upGetMatchStatusReq" field.</summary>
    public const int upGetMatchStatusReqFieldNumber = 4;
    /// <summary>
    /// 获取撮合状态请求
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MatchingUp_GetMatchStatus upGetMatchStatusReq {
      get { return upBizMsgCase_ == upBizMsgOneofCase.upGetMatchStatusReq ? (global::Msg.explore.PB_MatchingUp_GetMatchStatus) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.upGetMatchStatusReq;
      }
    }

    /// <summary>Field number for the "unused" field.</summary>
    public const int unusedFieldNumber = 5;
    private int unused_;
    /// <summary>
    /// 任务id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int unused {
      get { return unused_; }
      set {
        unused_ = value;
      }
    }

    private object upBizMsg_;
    /// <summary>Enum of possible cases for the "upBizMsg" oneof.</summary>
    public enum upBizMsgOneofCase {
      None = 0,
      upFindPartnerReq = 1,
      upPreMatchConfirmReq = 2,
      upMatchCancelReq = 3,
      upGetMatchStatusReq = 4,
    }
    private upBizMsgOneofCase upBizMsgCase_ = upBizMsgOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public upBizMsgOneofCase upBizMsgCase {
      get { return upBizMsgCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearupBizMsg() {
      upBizMsgCase_ = upBizMsgOneofCase.None;
      upBizMsg_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MatchingUpMsg);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MatchingUpMsg other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(upFindPartnerReq, other.upFindPartnerReq)) return false;
      if (!object.Equals(upPreMatchConfirmReq, other.upPreMatchConfirmReq)) return false;
      if (!object.Equals(upMatchCancelReq, other.upMatchCancelReq)) return false;
      if (!object.Equals(upGetMatchStatusReq, other.upGetMatchStatusReq)) return false;
      if (unused != other.unused) return false;
      if (upBizMsgCase != other.upBizMsgCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (upBizMsgCase_ == upBizMsgOneofCase.upFindPartnerReq) hash ^= upFindPartnerReq.GetHashCode();
      if (upBizMsgCase_ == upBizMsgOneofCase.upPreMatchConfirmReq) hash ^= upPreMatchConfirmReq.GetHashCode();
      if (upBizMsgCase_ == upBizMsgOneofCase.upMatchCancelReq) hash ^= upMatchCancelReq.GetHashCode();
      if (upBizMsgCase_ == upBizMsgOneofCase.upGetMatchStatusReq) hash ^= upGetMatchStatusReq.GetHashCode();
      if (unused != 0) hash ^= unused.GetHashCode();
      hash ^= (int) upBizMsgCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (upBizMsgCase_ == upBizMsgOneofCase.upFindPartnerReq) {
        output.WriteRawTag(10);
        output.WriteMessage(upFindPartnerReq);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.upPreMatchConfirmReq) {
        output.WriteRawTag(18);
        output.WriteMessage(upPreMatchConfirmReq);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.upMatchCancelReq) {
        output.WriteRawTag(26);
        output.WriteMessage(upMatchCancelReq);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.upGetMatchStatusReq) {
        output.WriteRawTag(34);
        output.WriteMessage(upGetMatchStatusReq);
      }
      if (unused != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(unused);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (upBizMsgCase_ == upBizMsgOneofCase.upFindPartnerReq) {
        output.WriteRawTag(10);
        output.WriteMessage(upFindPartnerReq);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.upPreMatchConfirmReq) {
        output.WriteRawTag(18);
        output.WriteMessage(upPreMatchConfirmReq);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.upMatchCancelReq) {
        output.WriteRawTag(26);
        output.WriteMessage(upMatchCancelReq);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.upGetMatchStatusReq) {
        output.WriteRawTag(34);
        output.WriteMessage(upGetMatchStatusReq);
      }
      if (unused != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(unused);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (upBizMsgCase_ == upBizMsgOneofCase.upFindPartnerReq) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(upFindPartnerReq);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.upPreMatchConfirmReq) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(upPreMatchConfirmReq);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.upMatchCancelReq) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(upMatchCancelReq);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.upGetMatchStatusReq) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(upGetMatchStatusReq);
      }
      if (unused != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(unused);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MatchingUpMsg other) {
      if (other == null) {
        return;
      }
      if (other.unused != 0) {
        unused = other.unused;
      }
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.upFindPartnerReq:
          if (upFindPartnerReq == null) {
            upFindPartnerReq = new global::Msg.explore.PB_MatchingUp_MatchStart();
          }
          upFindPartnerReq.MergeFrom(other.upFindPartnerReq);
          break;
        case upBizMsgOneofCase.upPreMatchConfirmReq:
          if (upPreMatchConfirmReq == null) {
            upPreMatchConfirmReq = new global::Msg.explore.PB_MatchingUp_PreMatchConfirm();
          }
          upPreMatchConfirmReq.MergeFrom(other.upPreMatchConfirmReq);
          break;
        case upBizMsgOneofCase.upMatchCancelReq:
          if (upMatchCancelReq == null) {
            upMatchCancelReq = new global::Msg.explore.PB_MatchingUp_MatchCancel();
          }
          upMatchCancelReq.MergeFrom(other.upMatchCancelReq);
          break;
        case upBizMsgOneofCase.upGetMatchStatusReq:
          if (upGetMatchStatusReq == null) {
            upGetMatchStatusReq = new global::Msg.explore.PB_MatchingUp_GetMatchStatus();
          }
          upGetMatchStatusReq.MergeFrom(other.upGetMatchStatusReq);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::Msg.explore.PB_MatchingUp_MatchStart subBuilder = new global::Msg.explore.PB_MatchingUp_MatchStart();
            if (upBizMsgCase_ == upBizMsgOneofCase.upFindPartnerReq) {
              subBuilder.MergeFrom(upFindPartnerReq);
            }
            input.ReadMessage(subBuilder);
            upFindPartnerReq = subBuilder;
            break;
          }
          case 18: {
            global::Msg.explore.PB_MatchingUp_PreMatchConfirm subBuilder = new global::Msg.explore.PB_MatchingUp_PreMatchConfirm();
            if (upBizMsgCase_ == upBizMsgOneofCase.upPreMatchConfirmReq) {
              subBuilder.MergeFrom(upPreMatchConfirmReq);
            }
            input.ReadMessage(subBuilder);
            upPreMatchConfirmReq = subBuilder;
            break;
          }
          case 26: {
            global::Msg.explore.PB_MatchingUp_MatchCancel subBuilder = new global::Msg.explore.PB_MatchingUp_MatchCancel();
            if (upBizMsgCase_ == upBizMsgOneofCase.upMatchCancelReq) {
              subBuilder.MergeFrom(upMatchCancelReq);
            }
            input.ReadMessage(subBuilder);
            upMatchCancelReq = subBuilder;
            break;
          }
          case 34: {
            global::Msg.explore.PB_MatchingUp_GetMatchStatus subBuilder = new global::Msg.explore.PB_MatchingUp_GetMatchStatus();
            if (upBizMsgCase_ == upBizMsgOneofCase.upGetMatchStatusReq) {
              subBuilder.MergeFrom(upGetMatchStatusReq);
            }
            input.ReadMessage(subBuilder);
            upGetMatchStatusReq = subBuilder;
            break;
          }
          case 40: {
            unused = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            global::Msg.explore.PB_MatchingUp_MatchStart subBuilder = new global::Msg.explore.PB_MatchingUp_MatchStart();
            if (upBizMsgCase_ == upBizMsgOneofCase.upFindPartnerReq) {
              subBuilder.MergeFrom(upFindPartnerReq);
            }
            input.ReadMessage(subBuilder);
            upFindPartnerReq = subBuilder;
            break;
          }
          case 18: {
            global::Msg.explore.PB_MatchingUp_PreMatchConfirm subBuilder = new global::Msg.explore.PB_MatchingUp_PreMatchConfirm();
            if (upBizMsgCase_ == upBizMsgOneofCase.upPreMatchConfirmReq) {
              subBuilder.MergeFrom(upPreMatchConfirmReq);
            }
            input.ReadMessage(subBuilder);
            upPreMatchConfirmReq = subBuilder;
            break;
          }
          case 26: {
            global::Msg.explore.PB_MatchingUp_MatchCancel subBuilder = new global::Msg.explore.PB_MatchingUp_MatchCancel();
            if (upBizMsgCase_ == upBizMsgOneofCase.upMatchCancelReq) {
              subBuilder.MergeFrom(upMatchCancelReq);
            }
            input.ReadMessage(subBuilder);
            upMatchCancelReq = subBuilder;
            break;
          }
          case 34: {
            global::Msg.explore.PB_MatchingUp_GetMatchStatus subBuilder = new global::Msg.explore.PB_MatchingUp_GetMatchStatus();
            if (upBizMsgCase_ == upBizMsgOneofCase.upGetMatchStatusReq) {
              subBuilder.MergeFrom(upGetMatchStatusReq);
            }
            input.ReadMessage(subBuilder);
            upGetMatchStatusReq = subBuilder;
            break;
          }
          case 40: {
            unused = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 寻找聊伴上行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MatchingUp_MatchStart : pb::IMessage<PB_MatchingUp_MatchStart>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MatchingUp_MatchStart> _parser = new pb::MessageParser<PB_MatchingUp_MatchStart>(() => new PB_MatchingUp_MatchStart());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MatchingUp_MatchStart> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MatchingReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_MatchStart() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_MatchStart(PB_MatchingUp_MatchStart other) : this() {
      unused_ = other.unused_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_MatchStart Clone() {
      return new PB_MatchingUp_MatchStart(this);
    }

    /// <summary>Field number for the "unused" field.</summary>
    public const int unusedFieldNumber = 1;
    private long unused_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long unused {
      get { return unused_; }
      set {
        unused_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MatchingUp_MatchStart);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MatchingUp_MatchStart other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (unused != other.unused) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (unused != 0L) hash ^= unused.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (unused != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(unused);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (unused != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(unused);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (unused != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(unused);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MatchingUp_MatchStart other) {
      if (other == null) {
        return;
      }
      if (other.unused != 0L) {
        unused = other.unused;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            unused = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            unused = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 预撮合确认上行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MatchingUp_PreMatchConfirm : pb::IMessage<PB_MatchingUp_PreMatchConfirm>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MatchingUp_PreMatchConfirm> _parser = new pb::MessageParser<PB_MatchingUp_PreMatchConfirm>(() => new PB_MatchingUp_PreMatchConfirm());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MatchingUp_PreMatchConfirm> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MatchingReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_PreMatchConfirm() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_PreMatchConfirm(PB_MatchingUp_PreMatchConfirm other) : this() {
      match_record_id_ = other.match_record_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_PreMatchConfirm Clone() {
      return new PB_MatchingUp_PreMatchConfirm(this);
    }

    /// <summary>Field number for the "match_record_id" field.</summary>
    public const int match_record_idFieldNumber = 1;
    private long match_record_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long match_record_id {
      get { return match_record_id_; }
      set {
        match_record_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MatchingUp_PreMatchConfirm);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MatchingUp_PreMatchConfirm other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (match_record_id != other.match_record_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (match_record_id != 0L) hash ^= match_record_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (match_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(match_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (match_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(match_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (match_record_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(match_record_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MatchingUp_PreMatchConfirm other) {
      if (other == null) {
        return;
      }
      if (other.match_record_id != 0L) {
        match_record_id = other.match_record_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            match_record_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            match_record_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 取消撮合上行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MatchingUp_MatchCancel : pb::IMessage<PB_MatchingUp_MatchCancel>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MatchingUp_MatchCancel> _parser = new pb::MessageParser<PB_MatchingUp_MatchCancel>(() => new PB_MatchingUp_MatchCancel());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MatchingUp_MatchCancel> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MatchingReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_MatchCancel() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_MatchCancel(PB_MatchingUp_MatchCancel other) : this() {
      match_record_id_ = other.match_record_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_MatchCancel Clone() {
      return new PB_MatchingUp_MatchCancel(this);
    }

    /// <summary>Field number for the "match_record_id" field.</summary>
    public const int match_record_idFieldNumber = 1;
    private long match_record_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long match_record_id {
      get { return match_record_id_; }
      set {
        match_record_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MatchingUp_MatchCancel);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MatchingUp_MatchCancel other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (match_record_id != other.match_record_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (match_record_id != 0L) hash ^= match_record_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (match_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(match_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (match_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(match_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (match_record_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(match_record_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MatchingUp_MatchCancel other) {
      if (other == null) {
        return;
      }
      if (other.match_record_id != 0L) {
        match_record_id = other.match_record_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            match_record_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            match_record_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 寻找聊伴上行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MatchingUp_GetMatchStatus : pb::IMessage<PB_MatchingUp_GetMatchStatus>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MatchingUp_GetMatchStatus> _parser = new pb::MessageParser<PB_MatchingUp_GetMatchStatus>(() => new PB_MatchingUp_GetMatchStatus());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MatchingUp_GetMatchStatus> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MatchingReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_GetMatchStatus() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_GetMatchStatus(PB_MatchingUp_GetMatchStatus other) : this() {
      match_record_id_ = other.match_record_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MatchingUp_GetMatchStatus Clone() {
      return new PB_MatchingUp_GetMatchStatus(this);
    }

    /// <summary>Field number for the "match_record_id" field.</summary>
    public const int match_record_idFieldNumber = 1;
    private long match_record_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long match_record_id {
      get { return match_record_id_; }
      set {
        match_record_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MatchingUp_GetMatchStatus);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MatchingUp_GetMatchStatus other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (match_record_id != other.match_record_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (match_record_id != 0L) hash ^= match_record_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (match_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(match_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (match_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(match_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (match_record_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(match_record_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MatchingUp_GetMatchStatus other) {
      if (other == null) {
        return;
      }
      if (other.match_record_id != 0L) {
        match_record_id = other.match_record_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            match_record_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            match_record_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 撮合状态下行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MatchingDown_MatchStatus : pb::IMessage<SC_MatchingDown_MatchStatus>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MatchingDown_MatchStatus> _parser = new pb::MessageParser<SC_MatchingDown_MatchStatus>(() => new SC_MatchingDown_MatchStatus());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MatchingDown_MatchStatus> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MatchingReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MatchingDown_MatchStatus() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MatchingDown_MatchStatus(SC_MatchingDown_MatchStatus other) : this() {
      match_record_id_ = other.match_record_id_;
      downMatchStatus_ = other.downMatchStatus_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MatchingDown_MatchStatus Clone() {
      return new SC_MatchingDown_MatchStatus(this);
    }

    /// <summary>Field number for the "match_record_id" field.</summary>
    public const int match_record_idFieldNumber = 1;
    private long match_record_id_;
    /// <summary>
    /// 撮合记录id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long match_record_id {
      get { return match_record_id_; }
      set {
        match_record_id_ = value;
      }
    }

    /// <summary>Field number for the "downMatchStatus" field.</summary>
    public const int downMatchStatusFieldNumber = 2;
    private global::Msg.explore.ENUM_MatchingStatus downMatchStatus_ = global::Msg.explore.ENUM_MatchingStatus.EO_MatchingStatus_Unused;
    /// <summary>
    /// 撮合状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.ENUM_MatchingStatus downMatchStatus {
      get { return downMatchStatus_; }
      set {
        downMatchStatus_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MatchingDown_MatchStatus);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MatchingDown_MatchStatus other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (match_record_id != other.match_record_id) return false;
      if (downMatchStatus != other.downMatchStatus) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (match_record_id != 0L) hash ^= match_record_id.GetHashCode();
      if (downMatchStatus != global::Msg.explore.ENUM_MatchingStatus.EO_MatchingStatus_Unused) hash ^= downMatchStatus.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (match_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(match_record_id);
      }
      if (downMatchStatus != global::Msg.explore.ENUM_MatchingStatus.EO_MatchingStatus_Unused) {
        output.WriteRawTag(16);
        output.WriteEnum((int) downMatchStatus);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (match_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(match_record_id);
      }
      if (downMatchStatus != global::Msg.explore.ENUM_MatchingStatus.EO_MatchingStatus_Unused) {
        output.WriteRawTag(16);
        output.WriteEnum((int) downMatchStatus);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (match_record_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(match_record_id);
      }
      if (downMatchStatus != global::Msg.explore.ENUM_MatchingStatus.EO_MatchingStatus_Unused) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) downMatchStatus);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MatchingDown_MatchStatus other) {
      if (other == null) {
        return;
      }
      if (other.match_record_id != 0L) {
        match_record_id = other.match_record_id;
      }
      if (other.downMatchStatus != global::Msg.explore.ENUM_MatchingStatus.EO_MatchingStatus_Unused) {
        downMatchStatus = other.downMatchStatus;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            match_record_id = input.ReadInt64();
            break;
          }
          case 16: {
            downMatchStatus = (global::Msg.explore.ENUM_MatchingStatus) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            match_record_id = input.ReadInt64();
            break;
          }
          case 16: {
            downMatchStatus = (global::Msg.explore.ENUM_MatchingStatus) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 撮合成功下行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MatchingDown_MatchedResult : pb::IMessage<SC_MatchingDown_MatchedResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MatchingDown_MatchedResult> _parser = new pb::MessageParser<SC_MatchingDown_MatchedResult>(() => new SC_MatchingDown_MatchedResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MatchingDown_MatchedResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MatchingReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MatchingDown_MatchedResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MatchingDown_MatchedResult(SC_MatchingDown_MatchedResult other) : this() {
      userchat_id_ = other.userchat_id_;
      match_record_id_ = other.match_record_id_;
      partner_userid_ = other.partner_userid_;
      avatarId_ = other.avatarId_;
      headPicUrl_ = other.headPicUrl_;
      nickname_ = other.nickname_;
      Agora_uid_self_ = other.Agora_uid_self_;
      Agora_uid_partner_ = other.Agora_uid_partner_;
      Agora_token_ = other.Agora_token_;
      Agora_channel_ = other.Agora_channel_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MatchingDown_MatchedResult Clone() {
      return new SC_MatchingDown_MatchedResult(this);
    }

    /// <summary>Field number for the "userchat_id" field.</summary>
    public const int userchat_idFieldNumber = 1;
    private long userchat_id_;
    /// <summary>
    /// 用户聊天id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long userchat_id {
      get { return userchat_id_; }
      set {
        userchat_id_ = value;
      }
    }

    /// <summary>Field number for the "match_record_id" field.</summary>
    public const int match_record_idFieldNumber = 2;
    private long match_record_id_;
    /// <summary>
    /// 撮合记录id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long match_record_id {
      get { return match_record_id_; }
      set {
        match_record_id_ = value;
      }
    }

    /// <summary>Field number for the "partner_userid" field.</summary>
    public const int partner_useridFieldNumber = 3;
    private long partner_userid_;
    /// <summary>
    /// 聊伴用户id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long partner_userid {
      get { return partner_userid_; }
      set {
        partner_userid_ = value;
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 4;
    private long avatarId_;
    /// <summary>
    /// avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "headPicUrl" field.</summary>
    public const int headPicUrlFieldNumber = 5;
    private string headPicUrl_ = "";
    /// <summary>
    /// 头像url
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string headPicUrl {
      get { return headPicUrl_; }
      set {
        headPicUrl_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "nickname" field.</summary>
    public const int nicknameFieldNumber = 6;
    private string nickname_ = "";
    /// <summary>
    /// 昵称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string nickname {
      get { return nickname_; }
      set {
        nickname_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Agora_uid_self" field.</summary>
    public const int Agora_uid_selfFieldNumber = 7;
    private uint Agora_uid_self_;
    /// <summary>
    /// 声网uid self
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Agora_uid_self {
      get { return Agora_uid_self_; }
      set {
        Agora_uid_self_ = value;
      }
    }

    /// <summary>Field number for the "Agora_uid_partner" field.</summary>
    public const int Agora_uid_partnerFieldNumber = 8;
    private uint Agora_uid_partner_;
    /// <summary>
    /// 声网uid partner
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Agora_uid_partner {
      get { return Agora_uid_partner_; }
      set {
        Agora_uid_partner_ = value;
      }
    }

    /// <summary>Field number for the "Agora_token" field.</summary>
    public const int Agora_tokenFieldNumber = 9;
    private string Agora_token_ = "";
    /// <summary>
    /// Agora-token
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Agora_token {
      get { return Agora_token_; }
      set {
        Agora_token_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Agora_channel" field.</summary>
    public const int Agora_channelFieldNumber = 10;
    private string Agora_channel_ = "";
    /// <summary>
    /// Agora-channel
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Agora_channel {
      get { return Agora_channel_; }
      set {
        Agora_channel_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MatchingDown_MatchedResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MatchingDown_MatchedResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (userchat_id != other.userchat_id) return false;
      if (match_record_id != other.match_record_id) return false;
      if (partner_userid != other.partner_userid) return false;
      if (avatarId != other.avatarId) return false;
      if (headPicUrl != other.headPicUrl) return false;
      if (nickname != other.nickname) return false;
      if (Agora_uid_self != other.Agora_uid_self) return false;
      if (Agora_uid_partner != other.Agora_uid_partner) return false;
      if (Agora_token != other.Agora_token) return false;
      if (Agora_channel != other.Agora_channel) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (userchat_id != 0L) hash ^= userchat_id.GetHashCode();
      if (match_record_id != 0L) hash ^= match_record_id.GetHashCode();
      if (partner_userid != 0L) hash ^= partner_userid.GetHashCode();
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (headPicUrl.Length != 0) hash ^= headPicUrl.GetHashCode();
      if (nickname.Length != 0) hash ^= nickname.GetHashCode();
      if (Agora_uid_self != 0) hash ^= Agora_uid_self.GetHashCode();
      if (Agora_uid_partner != 0) hash ^= Agora_uid_partner.GetHashCode();
      if (Agora_token.Length != 0) hash ^= Agora_token.GetHashCode();
      if (Agora_channel.Length != 0) hash ^= Agora_channel.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (userchat_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(userchat_id);
      }
      if (match_record_id != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(match_record_id);
      }
      if (partner_userid != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(partner_userid);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(avatarId);
      }
      if (headPicUrl.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(headPicUrl);
      }
      if (nickname.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(nickname);
      }
      if (Agora_uid_self != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(Agora_uid_self);
      }
      if (Agora_uid_partner != 0) {
        output.WriteRawTag(64);
        output.WriteUInt32(Agora_uid_partner);
      }
      if (Agora_token.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(Agora_token);
      }
      if (Agora_channel.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(Agora_channel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (userchat_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(userchat_id);
      }
      if (match_record_id != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(match_record_id);
      }
      if (partner_userid != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(partner_userid);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(avatarId);
      }
      if (headPicUrl.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(headPicUrl);
      }
      if (nickname.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(nickname);
      }
      if (Agora_uid_self != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(Agora_uid_self);
      }
      if (Agora_uid_partner != 0) {
        output.WriteRawTag(64);
        output.WriteUInt32(Agora_uid_partner);
      }
      if (Agora_token.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(Agora_token);
      }
      if (Agora_channel.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(Agora_channel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (userchat_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(userchat_id);
      }
      if (match_record_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(match_record_id);
      }
      if (partner_userid != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(partner_userid);
      }
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (headPicUrl.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(headPicUrl);
      }
      if (nickname.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(nickname);
      }
      if (Agora_uid_self != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Agora_uid_self);
      }
      if (Agora_uid_partner != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Agora_uid_partner);
      }
      if (Agora_token.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Agora_token);
      }
      if (Agora_channel.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Agora_channel);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MatchingDown_MatchedResult other) {
      if (other == null) {
        return;
      }
      if (other.userchat_id != 0L) {
        userchat_id = other.userchat_id;
      }
      if (other.match_record_id != 0L) {
        match_record_id = other.match_record_id;
      }
      if (other.partner_userid != 0L) {
        partner_userid = other.partner_userid;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.headPicUrl.Length != 0) {
        headPicUrl = other.headPicUrl;
      }
      if (other.nickname.Length != 0) {
        nickname = other.nickname;
      }
      if (other.Agora_uid_self != 0) {
        Agora_uid_self = other.Agora_uid_self;
      }
      if (other.Agora_uid_partner != 0) {
        Agora_uid_partner = other.Agora_uid_partner;
      }
      if (other.Agora_token.Length != 0) {
        Agora_token = other.Agora_token;
      }
      if (other.Agora_channel.Length != 0) {
        Agora_channel = other.Agora_channel;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            userchat_id = input.ReadInt64();
            break;
          }
          case 16: {
            match_record_id = input.ReadInt64();
            break;
          }
          case 24: {
            partner_userid = input.ReadInt64();
            break;
          }
          case 32: {
            avatarId = input.ReadInt64();
            break;
          }
          case 42: {
            headPicUrl = input.ReadString();
            break;
          }
          case 50: {
            nickname = input.ReadString();
            break;
          }
          case 56: {
            Agora_uid_self = input.ReadUInt32();
            break;
          }
          case 64: {
            Agora_uid_partner = input.ReadUInt32();
            break;
          }
          case 74: {
            Agora_token = input.ReadString();
            break;
          }
          case 82: {
            Agora_channel = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            userchat_id = input.ReadInt64();
            break;
          }
          case 16: {
            match_record_id = input.ReadInt64();
            break;
          }
          case 24: {
            partner_userid = input.ReadInt64();
            break;
          }
          case 32: {
            avatarId = input.ReadInt64();
            break;
          }
          case 42: {
            headPicUrl = input.ReadString();
            break;
          }
          case 50: {
            nickname = input.ReadString();
            break;
          }
          case 56: {
            Agora_uid_self = input.ReadUInt32();
            break;
          }
          case 64: {
            Agora_uid_partner = input.ReadUInt32();
            break;
          }
          case 74: {
            Agora_token = input.ReadString();
            break;
          }
          case 82: {
            Agora_channel = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 撮合失败下行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MatchingDown_MatchFailed : pb::IMessage<SC_MatchingDown_MatchFailed>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MatchingDown_MatchFailed> _parser = new pb::MessageParser<SC_MatchingDown_MatchFailed>(() => new SC_MatchingDown_MatchFailed());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MatchingDown_MatchFailed> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MatchingReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MatchingDown_MatchFailed() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MatchingDown_MatchFailed(SC_MatchingDown_MatchFailed other) : this() {
      reason_ = other.reason_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MatchingDown_MatchFailed Clone() {
      return new SC_MatchingDown_MatchFailed(this);
    }

    /// <summary>Field number for the "reason" field.</summary>
    public const int reasonFieldNumber = 1;
    private string reason_ = "";
    /// <summary>
    /// 失败原因
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string reason {
      get { return reason_; }
      set {
        reason_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MatchingDown_MatchFailed);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MatchingDown_MatchFailed other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (reason != other.reason) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (reason.Length != 0) hash ^= reason.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (reason.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(reason);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (reason.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(reason);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (reason.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(reason);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MatchingDown_MatchFailed other) {
      if (other == null) {
        return;
      }
      if (other.reason.Length != 0) {
        reason = other.reason;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            reason = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            reason = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

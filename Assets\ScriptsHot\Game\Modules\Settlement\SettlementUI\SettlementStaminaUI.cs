using DG.Tweening;
using FairyGUI;
using Msg.question_process;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Settlement
{
    public class SettlementStaminaUI : BaseUI<UIBind.Settlement.SettlementStaminaPanel>
    {
        public SettlementStaminaUI(string name) : base(name) { }
        public override string uiLayer => UILayerConsts.Top; //主UI层
        protected override bool isFullScreen => true;
        private SettlementModel model => this.GetModel<SettlementModel>(ModelConsts.Settlement);

        private Tween numberAnimation;
        private Tween viewAnimation;
        private bool isPlayAnimation = false;
        private int endNumber = 0;
        private PB_SubmitQuickPracticeResp _data;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            AddUIEvent(this.ui.btnBgScreen.onClick, Close);
        }

        protected override void OnShow()
        {
            base.OnShow();
            _data = (PB_SubmitQuickPracticeResp)args[0];
            var reward = _data.stamina_award;
            this.ui.comStamina.tfStamina.text = "+0";
            ui.comStamina.tfCongratulationsDes.SetKey("ui_stamina_settlement_congratulation");
            this.endNumber = reward;
            //播放打开界面动画
            PlayViewAnimation();
            
            //顺便刷刷新体力值
            GetController<HomepageController>(ModelConsts.Homepage).ReqGetIncentiveData();
        }

        //打开页面动画
        private void PlayViewAnimation()
        {
            // 从0缩放到1，持续时间为0.2秒
            isPlayAnimation = true;
            //this.ui.com.scale = Vector3.zero;
            //viewAnimation = DOTween.To(() => this.ui.com.scale, x => this.ui.com.scale = x, Vector2.one, 0.2f);
            //viewAnimation.OnComplete(() => PlayNumberAnimation(this.ui.comGold.tfGoldNum, 0,endNumber,0.2f));
            SoundManger.instance.PlayUI("medal");
            PlayNumberAnimation(this.ui.comStamina.tfStamina, 0, endNumber, 0.2f);
        }

        //播放数字动画
        private void PlayNumberAnimation(GTextField tx_num, int startNumber, int endNumber, float duration)
        {
            isPlayAnimation = true;
            numberAnimation = DOTween.To(() => startNumber, x => startNumber = x, endNumber, duration)
                .OnUpdate(() =>
                {
                    // 更新UI显示的数字
                    tx_num.text = "+" + startNumber.ToString();
                }).OnComplete(() => {
                    isPlayAnimation = false;
                });
        }
        //停止动画
        private void StopAnimation()
        {
            // 停止缩放动画和数字滚动动画
            numberAnimation.Kill();
            //viewAnimation.Kill();
            // 直接显示最终的数值
            ui.comStamina.tfStamina.text = "+" + endNumber.ToString();
            this.ui.com.scale = Vector3.one;
            isPlayAnimation = false;
        }

        //如果正在播放数字滚动动画 第一次点击关闭是停止动画 第二次才是关闭界面
        private void Close()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            if (isPlayAnimation)
            {
                StopAnimation();
            }
            else
            {
                GetController<SettlementController>(ModelConsts.Settlement).ShowFragmentPracticeSettlement(_data);
                Hide();
            }
        }

    }

}
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class CompPractice : ExtendedComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "CompPractice";
        public static string url => "ui://cmoz5osjpy44q";

        public Controller previous;
        public GGraph bg;
        public CompTag compTag;
        public GTextField tfQuestType;
        public GComponent loaderQuestion;
        public GComponent loaderAnswer;
        public GTextField tfQuestionId;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompPractice));
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            previous = GetControllerAt(0);
            bg = GetChildAt(0) as GGraph;
            compTag = GetChildAt(1) as CompTag;
            tfQuestType = GetChildAt(2) as GTextField;
            loaderQuestion = GetChildAt(3) as GComponent;
            loaderAnswer = GetChildAt(4) as GComponent;
            tfQuestionId = GetChildAt(5) as GTextField;

            OnConstructed();

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            OnWillDispose();

            previous = null;
            bg = null;
            compTag = null;
            tfQuestType = null;
            loaderQuestion = null;
            loaderAnswer = null;
            tfQuestionId = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.bg.SetKey("BLUR");  // ""
        }
    }
}
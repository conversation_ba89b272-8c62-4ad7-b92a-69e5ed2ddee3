/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Record
{
    public partial class RecordPanel : UIBindT
    {
        public override string pkgName => "Record";
        public override string comName => "RecordPanel";

        public Controller ctlStatus;
        public Controller ctrlAnimation;
        public Controller ctrlCountDown;
        public Controller recordTypeCtrl;
        public GButton btnClose;
        public GImage playerImageBg;
        public GGroup Introduce;
        public GButton btnMicrophone;
        public GImage micMask;
        public GLoader3D spineHand;
        public GTextField tfSpeak;
        public GGroup grpTips;
        public GTextField tfFinish;
        public GGroup grpCompleteTips;
        public GGroup record;
        public RecordTalkCancelButton btnCancel;
        public GButton btnSent;
        public GTextField tfCountDown;
        public GGroup send;
        public GComponent spineLoader;
        public GButton btnRepeat;
        public GButton btnBgNextForStateNR;
        public GTextField tfNextForStateNR;
        public GImage imgNextForStateNR;
        public GGroup nextAndRepeat;
        public GButton btnBgNext;
        public GTextField tfNext;
        public GGroup next;
        public GLoader3D btnBgFin;
        public GTextField tfFin;
        public GGroup fin;
        public GButton btnRepeatForStateFR;
        public GLoader3D btnBgFinForStateFR;
        public GTextField tfFinForStateFR;
        public GGroup finAndRepeat;
        public GButton btnRepeatForStateRepeat;
        public GGroup repeat;
        public GTextField tfDoubleNum;
        public GGroup grpDoubleNum;
        public GTextField tfSingleNum;
        public GGroup grpSingleNum;
        public SingleRedNumCom singleRedNumCom;
        public GGroup waiting;
        public GLoader3D tips;
        public GLoader3D spineBulb;
        public GButton btnBulbLight;
        public GGroup grpBulb;
        public GLoader3D btnNewSend;
        public RecordTalkCancelButton btnNewCancel;
        public GRichTextField tfTime;
        public GTextField tfSpeakNew;
        public GGroup grpSendTips;
        public GTextField tfFinish_2;
        public GGroup grpSendCompleteTips;
        public GGroup newSend;
        public GGraph btnPlayAudio;
        public GMovieClip socialAudioAni;
        public GTextField socialAudioTime;
        public GGroup audioInfo;
        public GGraph btnSocialFinsh;
        public GLoader3D btnSocialBgFinForStateFR;
        public GTextField tfSocialFinForStateFR;
        public GGraph btnRepeatCell;
        public GImage btnicon;
        public GGroup socialFinAndRepeat;
        public GImage textBg;
        public GTextField playerContent;
        public GLoader3D btnSpine;
        public GButton btnSpeakEndVad;
        public GButton btnSpeakCancel;
        public GTextField tfSpeakcansay;
        public GGroup grpTips2;
        public GGroup speakRecord;
        public GButton btnSpeakReapeatBtn;
        public GTextField tfSpeakrepeat;
        public GGroup grpTips1;
        public GGroup speakRepeat;
        public GButton btnSpeakMicBtn;
        public GTextField tfSpeakmic;
        public GGroup grpTips3;
        public GGroup speakMic;
        public GButton btnGuideMicrophone;
        public GImage btnAutoOn;
        public GImage btnAutoOff;
        public GImage socialNewLoading;
        public GLoader3D socialNew;
        public GGroup socialNewCon;
        public GImage lockImg;
        public GGraph lockBtn;
        public Transition Out;
        public Transition Reset;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctlStatus = com.GetControllerAt(0);
            ctrlAnimation = com.GetControllerAt(1);
            ctrlCountDown = com.GetControllerAt(2);
            recordTypeCtrl = com.GetControllerAt(3);
            btnClose = (GButton)com.GetChildAt(2);
            playerImageBg = (GImage)com.GetChildAt(3);
            Introduce = (GGroup)com.GetChildAt(4);
            btnMicrophone = (GButton)com.GetChildAt(5);
            micMask = (GImage)com.GetChildAt(6);
            spineHand = (GLoader3D)com.GetChildAt(7);
            tfSpeak = (GTextField)com.GetChildAt(9);
            grpTips = (GGroup)com.GetChildAt(10);
            tfFinish = (GTextField)com.GetChildAt(12);
            grpCompleteTips = (GGroup)com.GetChildAt(13);
            record = (GGroup)com.GetChildAt(14);
            btnCancel = new RecordTalkCancelButton();
            btnCancel.Construct(com.GetChildAt(16).asCom);
            btnSent = (GButton)com.GetChildAt(17);
            tfCountDown = (GTextField)com.GetChildAt(19);
            send = (GGroup)com.GetChildAt(20);
            spineLoader = (GComponent)com.GetChildAt(21);
            btnRepeat = (GButton)com.GetChildAt(22);
            btnBgNextForStateNR = (GButton)com.GetChildAt(23);
            tfNextForStateNR = (GTextField)com.GetChildAt(24);
            imgNextForStateNR = (GImage)com.GetChildAt(25);
            nextAndRepeat = (GGroup)com.GetChildAt(26);
            btnBgNext = (GButton)com.GetChildAt(27);
            tfNext = (GTextField)com.GetChildAt(28);
            next = (GGroup)com.GetChildAt(30);
            btnBgFin = (GLoader3D)com.GetChildAt(31);
            tfFin = (GTextField)com.GetChildAt(32);
            fin = (GGroup)com.GetChildAt(33);
            btnRepeatForStateFR = (GButton)com.GetChildAt(34);
            btnBgFinForStateFR = (GLoader3D)com.GetChildAt(35);
            tfFinForStateFR = (GTextField)com.GetChildAt(36);
            finAndRepeat = (GGroup)com.GetChildAt(37);
            btnRepeatForStateRepeat = (GButton)com.GetChildAt(38);
            repeat = (GGroup)com.GetChildAt(39);
            tfDoubleNum = (GTextField)com.GetChildAt(41);
            grpDoubleNum = (GGroup)com.GetChildAt(43);
            tfSingleNum = (GTextField)com.GetChildAt(44);
            grpSingleNum = (GGroup)com.GetChildAt(46);
            singleRedNumCom = new SingleRedNumCom();
            singleRedNumCom.Construct(com.GetChildAt(47).asCom);
            waiting = (GGroup)com.GetChildAt(48);
            tips = (GLoader3D)com.GetChildAt(49);
            spineBulb = (GLoader3D)com.GetChildAt(50);
            btnBulbLight = (GButton)com.GetChildAt(51);
            grpBulb = (GGroup)com.GetChildAt(52);
            btnNewSend = (GLoader3D)com.GetChildAt(53);
            btnNewCancel = new RecordTalkCancelButton();
            btnNewCancel.Construct(com.GetChildAt(54).asCom);
            tfTime = (GRichTextField)com.GetChildAt(55);
            tfSpeakNew = (GTextField)com.GetChildAt(57);
            grpSendTips = (GGroup)com.GetChildAt(58);
            tfFinish_2 = (GTextField)com.GetChildAt(60);
            grpSendCompleteTips = (GGroup)com.GetChildAt(61);
            newSend = (GGroup)com.GetChildAt(62);
            btnPlayAudio = (GGraph)com.GetChildAt(63);
            socialAudioAni = (GMovieClip)com.GetChildAt(65);
            socialAudioTime = (GTextField)com.GetChildAt(66);
            audioInfo = (GGroup)com.GetChildAt(67);
            btnSocialFinsh = (GGraph)com.GetChildAt(68);
            btnSocialBgFinForStateFR = (GLoader3D)com.GetChildAt(69);
            tfSocialFinForStateFR = (GTextField)com.GetChildAt(70);
            btnRepeatCell = (GGraph)com.GetChildAt(71);
            btnicon = (GImage)com.GetChildAt(72);
            socialFinAndRepeat = (GGroup)com.GetChildAt(73);
            textBg = (GImage)com.GetChildAt(74);
            playerContent = (GTextField)com.GetChildAt(75);
            btnSpine = (GLoader3D)com.GetChildAt(76);
            btnSpeakEndVad = (GButton)com.GetChildAt(77);
            btnSpeakCancel = (GButton)com.GetChildAt(78);
            tfSpeakcansay = (GTextField)com.GetChildAt(80);
            grpTips2 = (GGroup)com.GetChildAt(81);
            speakRecord = (GGroup)com.GetChildAt(82);
            btnSpeakReapeatBtn = (GButton)com.GetChildAt(83);
            tfSpeakrepeat = (GTextField)com.GetChildAt(85);
            grpTips1 = (GGroup)com.GetChildAt(86);
            speakRepeat = (GGroup)com.GetChildAt(87);
            btnSpeakMicBtn = (GButton)com.GetChildAt(88);
            tfSpeakmic = (GTextField)com.GetChildAt(90);
            grpTips3 = (GGroup)com.GetChildAt(91);
            speakMic = (GGroup)com.GetChildAt(92);
            btnGuideMicrophone = (GButton)com.GetChildAt(93);
            btnAutoOn = (GImage)com.GetChildAt(94);
            btnAutoOff = (GImage)com.GetChildAt(95);
            socialNewLoading = (GImage)com.GetChildAt(96);
            socialNew = (GLoader3D)com.GetChildAt(98);
            socialNewCon = (GGroup)com.GetChildAt(99);
            lockImg = (GImage)com.GetChildAt(100);
            lockBtn = (GGraph)com.GetChildAt(101);
            Out = com.GetTransitionAt(0);
            Reset = com.GetTransitionAt(1);

            SetMultiLanguageInChildren();
            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctlStatus = null;
            ctrlAnimation = null;
            ctrlCountDown = null;
            recordTypeCtrl = null;
            btnClose = null;
            playerImageBg = null;
            Introduce = null;
            btnMicrophone = null;
            micMask = null;
            spineHand = null;
            tfSpeak = null;
            grpTips = null;
            tfFinish = null;
            grpCompleteTips = null;
            record = null;
            btnCancel.Dispose();
            btnCancel = null;
            btnSent = null;
            tfCountDown = null;
            send = null;
            spineLoader = null;
            btnRepeat = null;
            btnBgNextForStateNR = null;
            tfNextForStateNR = null;
            imgNextForStateNR = null;
            nextAndRepeat = null;
            btnBgNext = null;
            tfNext = null;
            next = null;
            btnBgFin = null;
            tfFin = null;
            fin = null;
            btnRepeatForStateFR = null;
            btnBgFinForStateFR = null;
            tfFinForStateFR = null;
            finAndRepeat = null;
            btnRepeatForStateRepeat = null;
            repeat = null;
            tfDoubleNum = null;
            grpDoubleNum = null;
            tfSingleNum = null;
            grpSingleNum = null;
            singleRedNumCom.Dispose();
            singleRedNumCom = null;
            waiting = null;
            tips = null;
            spineBulb = null;
            btnBulbLight = null;
            grpBulb = null;
            btnNewSend = null;
            btnNewCancel.Dispose();
            btnNewCancel = null;
            tfTime = null;
            tfSpeakNew = null;
            grpSendTips = null;
            tfFinish_2 = null;
            grpSendCompleteTips = null;
            newSend = null;
            btnPlayAudio = null;
            socialAudioAni = null;
            socialAudioTime = null;
            audioInfo = null;
            btnSocialFinsh = null;
            btnSocialBgFinForStateFR = null;
            tfSocialFinForStateFR = null;
            btnRepeatCell = null;
            btnicon = null;
            socialFinAndRepeat = null;
            textBg = null;
            playerContent = null;
            btnSpine = null;
            btnSpeakEndVad = null;
            btnSpeakCancel = null;
            tfSpeakcansay = null;
            grpTips2 = null;
            speakRecord = null;
            btnSpeakReapeatBtn = null;
            tfSpeakrepeat = null;
            grpTips1 = null;
            speakRepeat = null;
            btnSpeakMicBtn = null;
            tfSpeakmic = null;
            grpTips3 = null;
            speakMic = null;
            btnGuideMicrophone = null;
            btnAutoOn = null;
            btnAutoOff = null;
            socialNewLoading = null;
            socialNew = null;
            socialNewCon = null;
            lockImg = null;
            lockBtn = null;
            Out = null;
            Reset = null;
        }

        public void SetMultiLanguageInChildren()
        {
            this.tfSpeak.SetKey("ui_record_tap_speak");  // "Tap to Speak"
            this.tfFinish.SetKey("ui_record_finish");  // "Say goodbye to complete"
            this.tfNextForStateNR.SetKey("avatar_talk_button_next");  // "Next"
            this.tfNext.SetKey("avatar_talk_button_next");  // "Next"
            this.tfFin.SetKey("avatar_talk_button_end");  // "End"
            this.tfFinForStateFR.SetKey("avatar_talk_button_end");  // "End"
            this.tfSpeakNew.SetKey("ui_review_cansay");  // "Tap when done"
            this.tfSocialFinForStateFR.SetKey("avatar_talk_button_end");  // "End"
            this.tfSpeakcansay.SetKey("ui_review_cansay");  // "Now you can say..."
            this.tfSpeakrepeat.SetKey("ui_review_tyr_again");  // "Try again!"
            this.tfSpeakmic.SetKey("ui_record_tap_speak");  // "Try again!"
        }
    }
}
[{"actionKey": "greeting01", "desp": "say hi挥手", "animInfo": ["greeting01a", "greeting01b", "greeting01c"]}, {"actionKey": "bodyTestKey2", "desp": "测试集合2", "animInfo": ["applaud1", "applaud2", "applaud3"]}, {"actionKey": "behindTheBack01", "desp": "背手到身后", "animInfo": ["behindTheBack01a", "behindTheBack01b"]}, {"actionKey": "spread01", "desp": "摊手01", "animInfo": ["spread01a", "spread01b", "spread01c"]}, {"actionKey": "toSide01", "desp": "侧向", "animInfo": ["toSide01a", "toSide01b"]}, {"actionKey": "toFront02", "desp": "测试-前向", "animInfo": ["toFront02a", "toFront02b", "toFront02c"]}, {"actionKey": "toSelf01", "desp": "单手指向我01", "animInfo": ["toSelf01a", "toSelf01b", "toSelf01c"]}, {"actionKey": "Test3", "desp": "测试动作组3", "animInfo": ["idleStateThink"]}, {"actionKey": "Test4", "desp": "测试动作组4", "animInfo": ["idleStateWait1"]}, {"actionKey": "Test5", "desp": "测试动作组5", "animInfo": ["idleStateWait2"]}, {"actionKey": "Test6", "desp": "测试动作组6", "animInfo": ["idleStateBeishou"]}, {"actionKey": "Test7", "desp": "测试动作组7", "animInfo": ["idleStateIdle10"]}, {"actionKey": "Test8", "desp": "测试动作组8", "animInfo": ["idleStateIdle11"]}, {"actionKey": "Test9", "desp": "测试动作组9", "animInfo": ["20001", "20002", "20003"]}, {"actionKey": "Test10", "desp": "测试动作组10", "animInfo": ["20004", "20005", "20006"]}, {"actionKey": "Test11", "desp": "测试动作组11", "animInfo": ["20007", "20008", "20009"]}, {"actionKey": "Idle", "desp": "站立", "animInfo": ["Idle"]}, {"actionKey": "State1-1", "desp": "客观陈述1秒弱1", "animInfo": ["State1-1"]}, {"actionKey": "State1-2", "desp": "客观陈述1秒弱2", "animInfo": ["State1-2"]}, {"actionKey": "State1-3", "desp": "客观陈述1秒弱3", "animInfo": ["State1-3"]}, {"actionKey": "State1-4", "desp": "客观陈述1秒弱4", "animInfo": ["State1-4"]}, {"actionKey": "State1-5", "desp": "客观陈述1秒弱5", "animInfo": ["State1-5"]}, {"actionKey": "State1-6", "desp": "客观陈述1秒弱6", "animInfo": ["State1-6"]}, {"actionKey": "State2-1", "desp": "客观陈述2秒弱1", "animInfo": ["State2-1"]}, {"actionKey": "State2-2", "desp": "客观陈述2秒弱2", "animInfo": ["State2-2"]}, {"actionKey": "State2-3", "desp": "客观陈述2秒弱3", "animInfo": ["State2-3"]}, {"actionKey": "State2-4", "desp": "客观陈述2秒弱4", "animInfo": ["State2-4"]}, {"actionKey": "State2-5", "desp": "客观陈述2秒弱5", "animInfo": ["State2-5"]}, {"actionKey": "State2-6", "desp": "客观陈述2秒弱6", "animInfo": ["State2-6"]}, {"actionKey": "State3-1", "desp": "客观陈述3秒弱1", "animInfo": ["State3-1"]}, {"actionKey": "State3-2", "desp": "客观陈述3秒弱2", "animInfo": ["State3-2"]}, {"actionKey": "PointSelf1", "desp": "指向自己1", "animInfo": ["PointSelf1"]}, {"actionKey": "PointSelf2", "desp": "指向自己2", "animInfo": ["PointSelf2"]}, {"actionKey": "PointPlayer1", "desp": "指向对方1", "animInfo": ["PointPlayer1"]}, {"actionKey": "PointPlayer2", "desp": "指向对方2", "animInfo": ["PointPlayer2"]}, {"actionKey": "TalkingIdle", "desp": "站立", "animInfo": ["TalkingIdle"]}, {"actionKey": "DeepIdle1", "desp": "深度待机1", "animInfo": ["DeepIdle1-1", "DeepIdle1-2", "DeepIdle1-3"]}, {"actionKey": "DeepIdle2", "desp": "深度待机2", "animInfo": ["DeepIdle2-1", "DeepIdle2-2", "DeepIdle2-3"]}, {"actionKey": "ShallowIdle1", "desp": "浅度待机", "animInfo": ["ShallowIdle1", "ShallowIdle2", "ShallowIdle3"]}, {"actionKey": "PointPlayer3", "desp": "指向对方1", "animInfo": ["PointPlayer1-1", "PointPlayer1-2"]}, {"actionKey": "Correct1", "desp": "五星回答正确1", "animInfo": ["Correct1"]}, {"actionKey": "Correct2", "desp": "五星回答正确2", "animInfo": ["Correct2"]}, {"actionKey": "Correct3", "desp": "五星回答正确3", "animInfo": ["Correct3"]}, {"actionKey": "RecordingStart", "desp": "五星录音开始", "animInfo": ["Recording1", "Recording2"]}, {"actionKey": "RecordingEnd", "desp": "五星录音结束", "animInfo": ["Recording3"]}, {"actionKey": "StageBegin", "desp": "五星入场", "animInfo": ["StageBegin"]}, {"actionKey": "StageEnd", "desp": "五星结算", "animInfo": ["StageEnd"]}, {"actionKey": "Win5", "desp": "五星五连胜", "animInfo": ["Win5"]}, {"actionKey": "Win10", "desp": "五星十连胜", "animInfo": ["Win10"]}, {"actionKey": "Wrong1", "desp": "五星回答错误1", "animInfo": ["Wrong1"]}]
﻿/*
 ****************************************************
 * 作者：ZhangXiaoWu
 * 创建时间：2024/03/09 16:57:01 星期六
 * 功能：填空题
 ****************************************************
 */
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;
using CommonUI;
using FairyGUI;
using Game.Modules.Record;
using Google.Protobuf;
using Lean.Common;
using Msg.basic;
using Msg.dialog_task;
using Msg.question_process;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Guide;
using ScriptsHot.Game.Modules.ReviewQuestion;
using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using UIBind.Record;
using UnityEngine;

namespace UIBind.ReviewQuestion
{

    public partial class ReviewQuestionGapFilliingCom : AQuestionCard, IReviewQuestionCard, IRecordEventListener
    {
        // 静态字段
        private static int bubbleId;
        private static string NEW_WORD_ID_PREFIX = "Review_Gap_";

        // UI控件引用
        private TextFieldExtension _comTf => comText as TextFieldExtension;
        private SpeakRecordBtn recordBtn => btnRecord as SpeakRecordBtn;

        //id
        private long uuid = 0;
        private int recordId = -1;  // 当前的录音ID
        private int _countMesh = 0;

        // 状态相关字段
        // private bool _isReplaying = false;
        private bool _isReceivingEvents = false;
        

        // 数据相关字段
        private List<RichContent> _datas = new List<RichContent>();
        private QuestionData questionData = null;
        private string referenceText;

        protected AudioClip _curPlayerClip;
        private NewWordComponent _curNewWordComponent;
        private TimerManager.Handler handler;
        private float matchPercent = 0f;


        // Controller引用

        private ChatModel _chatModel => ModelManager.instance.GetModel<ChatModel>(ModelConsts.Chat);
        private ReviewQuestionController _reviewQuestionController =>
            ControllerManager.instance.GetController<ReviewQuestionController>(ModelConsts.ReviewQuestionModel);
        private CurrencyController _currencyController =>
            ControllerManager.instance.GetController<CurrencyController>(ModelConsts.CurrencyController);

        // 公开属性
        public IReviewQuestionUI QuestionUI { get; set; }
        public QuestionData QuestionData => questionData;
        public bool IsReceivingEvents
        {
            get => _isReceivingEvents;
            private set
            {
                _isReceivingEvents = value;

                recordBtn.visible = value;

                if (value == false)
                {
                    ChangeState(Action.RollOut);
                }
            }
        }

        #region Lifecycle Methods
        public ReviewQuestionGapFilliingCom()
        {
            bubbleId++;
            onAddedToStage.Add(OnAddedToStage);
            onRemovedFromStage.Add(OnRemovedFromStage);
        }

        protected void OnAddedToStage()
        {
            _comTf.Reset();
            _comTf.SetNewFont(FontCfg.DinNextMedium, Color.black, 56);//使用title自

            _comTf.SetAlignType(AlignType.Center);
            _comTf.touchable = true;
            _comTf.IsFairyBatchingEnable = true;
            _comTf.TextGtf.textFormat.lineSpacing = 10;

            _comTf.StartNewWordTrans();
            _comTf.OnClickMaskTips += ReqMaskTips;//box部分

            tfTrans.align = AlignType.Center;

            btnPlayerVoic.onClick.Add(OnPlayerVoiceClick);//用户的声音
            btnPlayVoic.onClick.Add(OnPlayTTSClick);  //系统avatar的声音

            RecordEventManager.Instance.AddListener(this);

            recordBtn.onContinue += OnClickRecordBtnContinue;

            //SpeechBulletUI是测录音的调试功能
            var testUI = UIManager.instance.GetUI<SpeechBulletUI>(UIConsts.SpeechBulletUI);
            if (testUI != null)
            {
                tfState.visible = testUI.isShow;
            }
            else {
                tfState.visible = false;
            }
        }

        private void OnClickRecordBtnContinue()
        {
            QuestionUI?.TryNext();
        }


        protected void OnRemovedFromStage()
        {
            RecordEventManager.Instance.DispatchRecordCancel();
            RecordEventManager.Instance.RemoveListener(this);
            recordBtn.onContinue -= OnClickRecordBtnContinue;
            SetCurrentStatus(false);
            Clear();
        }

        public void Clear()
        {
            TTSManager.instance.StopTTS();
            handler?.Clear();
            handler = null;
        }
        #endregion
        #region Question Logic Methods
        public void ShowQuestion(QuestionData data)
        {
            // Debug.Log("RQ-showQuestion qid="+data.question.question_id+" first-tts-id="+ data.question.first_tts_record_id);
            questionData = data;
            referenceText = QuestionParser.GetReferenceContent(questionData.question);
            recordBtn.SetReferenceText(referenceText);

            //多语言
            if (questionData.question.question_type == PB_QuestionType.QuestionType_Shadowing)
            {
                tfTitle.SetKey("knowladge_title_follow");
            }else if (questionData.question.question_type == PB_QuestionType.QuestionType_FillIn)
            {
                tfTitle.SetKey("knowladge_title_gapfill");
            }
            else if (questionData.question.question_type == PB_QuestionType.QuestionType_Answer)
            {
                tfTitle.SetKey("knowladge_title_answer");
            }

            tfTrans.text = questionData.question.stems.FirstOrDefault().translation_text;
            
            ShowVoiceBtns(true, false);

            ShowStem();

            InvalidateBatchingState(true);
        }

        //20250527 raybit 将在此函数中display文本的能力移出
        //等到OnRecordStart时再去展示
        //FGUI _comTf.TextGtf 在展示text时有比较诡异的表现
        //目前 先做隐藏后，再后面的时间（约0.5s后 ）才能一次性对text赋值时正确呈现。 具体原因不明

        private void ShowStem()
        {
            //要解析的数据复杂度 更大一些
            var stemsObj = QuestionParser.ParseQuestion(questionData.question);

            if (stemsObj is GapFillingContent listTexts)
            {
                _datas = listTexts.Arr;
            }
            else
            {
                _datas = stemsObj as List<RichContent>;
            }

            _comTf.TextGtf.visible = false;//【！！】注意这一行非常关键，是目前TextExt 利用font显示透明文字 成功与否的关键，20250527 raybit add

            if (questionData.question.question_type == PB_QuestionType.QuestionType_Shadowing)
            {
                _comTf.Reset();
                _comTf.AppendContent(_datas);// 添加重要的分块数据
                _comTf.Display(ShowMode.SpeakInit);//speak 是初始化的时候用的
                this.ResetTextExt();
            }
        }

        public void ShowResult(PB_DialogTaskQuestionResult data)
        {
        }

        public void OnShowComplete()
        {
            ChangeState(Action.Idle);//会执行OnEnterIdleState  先reset状态
            //ChangeState(Action.PlayTTS);// 展示完卡片就开始 自动播声音
        }
        #endregion

        #region Voice State
        private void OnPlayerVoiceClick()
        {
            ChangeState(Action.PlayVoice);
        }

        //播用户的声音
        private void OnEnterPlayVoiceState()
        {
            SoundManger.instance.PlayUI("listen");
            btnPlayerVoic.GetController("state").SetSelectedPage("on"); 
            QuestionUI?.EnableNextBtn(false, FlagNextButton.Voice);

            RecordEventManager.Instance.DispatchRecordCancel();
            if (_curPlayerClip != null)
            {
                Notifier.instance.SendNotification(NotifyConsts.Start2PlayInteractiveTTS);
                GSoundManager.instance.PlayTTS(_curPlayerClip);
            }
            var length = 0f;
            if (int.TryParse(_curPlayerClip.name, out var time))
            {
                length = time / 1000f;
            }
            Timers.inst.Add(length, 1, (i) =>
            {
                ChangeState(Action.FinishVoice);
                Notifier.instance.SendNotification(NotifyConsts.End2PlayInteractiveTTS);
            });
        }

        private void OnExitPlayVoiceState()
        {
            btnPlayerVoic.GetController("state").SetSelectedPage("off");
            QuestionUI?.EnableNextBtn(true, FlagNextButton.Voice);
        }
        #endregion

        #region TTS Methods

        private void OnPlayTTSClick()
        {
            ChangeState(Action.PlayTTS);
            Notifier.instance.SendNotification(NotifyConsts.Start2PlayInteractiveTTS);
        }
        private void OnEnterPlayQuestionTTSState()
        {
            var tts_id = questionData.question.first_tts_record_id;
            if (questionData.question.question_type == PB_QuestionType.QuestionType_Shadowing)
            {
                tts_id = questionData.question.tts_record_id;
            }
            if (tts_id <= 0)
            {
                ChangeState(Action.FinishTTS);
                return;
            }
            SoundManger.instance.PlayUI("listen");
            TTSManager.instance.PlayTTS(tts_id, ()=>ChangeState(Action.FinishTTS));
            TTSManager.instance.AllowPlayTTSCallbackInAdvance(0.8f);//1f = 1秒, 这类题目自动播放后 mic自动打开的时间点过晚 对冲一点时间

            QuestionUI?.EnableNextBtn(false, FlagNextButton.Voice);
        }

        //播 avatar的voice
        private void OnEnterPlayTTSState()
        {
            var tts_id = questionData.question.tts_record_id;
            SoundManger.instance.PlayUI("listen");
            TTSManager.instance.PlayTTS(tts_id, ()=> {
                ChangeState(Action.FinishTTS);

                Notifier.instance.SendNotification(NotifyConsts.End2PlayInteractiveTTS);
            });
            Notifier.instance.SendNotification(NotifyConsts.Start2PlayInteractiveTTS);
            QuestionUI?.EnableNextBtn(false, FlagNextButton.Voice);
        }

        public void OnExitPlayTTSState()
        {
            TTSManager.instance.StopTTS();
            QuestionUI?.EnableNextBtn(true, FlagNextButton.Voice);
        }        

        private void ShowVoiceBtns(bool enabled, bool hasVoice = false)
        {
            grpVoice.touchable = enabled;
            btnPlayerVoic.alpha = hasVoice ? 1f : 0.5f;
            btnPlayerVoic.touchable = hasVoice;
        }
        #endregion

        #region IRecordEventListener Implementation
        private void OnEnterInitRecordingState()
        {
            RecordEventManager.Instance.DispatchRecordStart(referenceText);
        }
        public void OnRecordStart(string rawString, int recordId)
        {
            this.recordId = recordId;
            ChangeState(Action.StartRecording);
        }

        private void OnEnterRecordingState()
        {            
            grpVoice.touchable = false;

            this.ResetTextExt();

            recordBtn.StartRecording();
            QuestionUI?.EnableNextBtn(false, FlagNextButton.Voice);
        }

        private void DrawBoxByMatchResults(List<MatchResult> matchResults,bool notShowWordInBox) {
            // 将识别结果转换为查找字典，key为识别到的单词
            var matchWordsDict = matchResults
                .GroupBy(r => r.MatchedWord.ToLower().Trim())
                .ToDictionary(
                    g => g.Key,
                    g => g.First()
                );

            //遍历题目中需要填空的内容
            foreach (var questionContent in _datas.Where(d => d.Type == RichContentType.Mask || d.Type == RichContentType.Wrap))
            {
                //每个填空的 RichContent都处理下
                ProcessQuestionContent(questionContent, matchWordsDict, notShowWordInBox);
            }
        }

        private void MarkHideInMatchResults(List<MatchResult> matchResults, bool notShowWordInBox)
        {
            // 将识别结果转换为查找字典，key为识别到的单词
            var matchWordsDict = matchResults
                .GroupBy(r => r.MatchedWord.ToLower().Trim())
                .ToDictionary(
                    g => g.Key,
                    g => g.First()
                );
          
            //遍历题目中需要填空的内容
            foreach (var questionContent in _datas.Where(d => d.Type == RichContentType.Mask || d.Type == RichContentType.Wrap))
            {
                //每个填空的 RichContent都处理下
                ProcessQuestionContent2(questionContent, matchWordsDict, notShowWordInBox);
            }
        }

        //onRecordStart后触发
        private void ResetTextExt()
        {
            //_comTf.SetFormat(TextFieldExtension.TEXT_SPEAK_UNHIT, 56); //已经默认设置过了
            _comTf.Reset();

            //step1 核心richContent数据
            _comTf.AppendContent(_datas);

            //step2 拿匹配的结果，注意SpeechToTextManager的
            //获取语音识别的匹配结果,这时必然没有命中的，借此可以染色为全部Unhit
            SpeechToTextManager.instance.ResetRecognizedWords();//高度依赖此时 内部的refText是已经赋值过的状态
            var matchResults = SpeechToTextManager.instance.MatchWordsInReference(TextFieldExtension.TEXT_SPEAK_HIT, TextFieldExtension.TEXT_SPEAK_UNHIT);

            //step3 遍历matchResults每个子mr，根据_datas内的 mask信息，对mr中需要Hide的 displayword做隐藏
            MarkHideInMatchResults(matchResults, true);//会交叉 datas和 matchResults并且改变results内的部分hide相关的信息，这部分信息对于 一次性Display完成很重要
            _comTf.SetEvaluateResult(matchResults);

            //step4 displayword做隐藏后，DrawEvaluateColorForTextField流程会让这类文本被标记为 font 000000 00的透明色
            _comTf.Display(ShowMode.SpeakInit);
            //SpeakInit 内部包含初始的drawbox步骤
           
        }

        public void OnRecordStop()
        {
            ChangeState(Action.StopRecording);
        }

        public void OnExitRecordingState(Action action)
        {
            grpVoice.touchable = true;
            QuestionUI?.EnableNextBtn(true, FlagNextButton.Voice);
            if (action == Action.CancelRecording)
            {
                recordBtn.CancelRecording();
                return;  // 取消的话下边就不处理了
            }

            recordBtn.StopRecording(matchPercent >= 0.5f);
            RefreshFinalResult();

            _curPlayerClip = GMicrophoneManager.instance.GetAudioClip();
            var audioData = GMicrophoneManager.instance.GetByteString();

            // 使用 ASR 结果类封装所有识别相关信息
            var asrResultData = new ASRResultData
            {
                AudioData = audioData,
                AsrContent = SpeechToTextManager.instance.matchText,
                UUID = SpeechToTextManager.instance.UUIDGenerator()
            };
            
            HandleASRResult(asrResultData);
        }       

        private void HandleASRResult(ASRResultData asrData)
        {
            reviewModel.SetAsrContent(asrData.AsrContent);
            if (_curPlayerClip != null)
            {
                _reviewQuestionController.HandleAsrComplete(asrData);
                ShowVoiceBtns(true, true);
            }
            TTSManager.instance.PlayTTS(questionData.question.tts_record_id);
            

            _reviewQuestionController.ProcessNextQuestion();
        }

        public void SendToServer(PB_Answer_Client answer)
        {
            reviewModel.SetAsrRecordID(uuid);
            reviewModel.SetAssessmentID(uuid);
            reviewModel.SetAnswer(answer);
        }

        public void OnRecordCancel()
        {
            recordId = 0;
            ChangeState(Action.CancelRecording);
            ShowStem();            
            this.ResetTextExt();
        }
        public void OnVad()
        {
            OnRecordStop();
        }
        public void OnCountDown()
        {
            OnRecordStop();
        }
        public void OnMatchAll()
        {
            matchPercent = 1f;
            OnRecordStop();
        }
        public void OnTranscription(string transcribedText, int recordId)
        {
            if (recordId != this.recordId) return;
            RefreshResult();
        }
        // ExitRecording后执行
        public void RefreshFinalResult()
        {
            if (questionData.question.question_type == PB_QuestionType.QuestionType_FillIn || questionData.question.question_type == PB_QuestionType.QuestionType_Answer)
            {
                ProcessAnswerAndSendToServerInFilling();
            }
            else if (questionData.question.question_type == PB_QuestionType.QuestionType_Shadowing)
            {
                ProcessAnswerAndSendToServerInFollow();
            }
            RefreshResults(TextFieldExtension.TEXT_SPEAK_FINAL_HIT, TextFieldExtension.TEXT_SPEAK_UNHIT,true);
        }

        private void RefreshResult()
        {
            TimerManager.instance.RegisterNextFrame((t) =>
            {
                if (GMicrophoneManager.instance.IsRecording)
                {
                    RefreshResults(TextFieldExtension.TEXT_SPEAK_HIT, TextFieldExtension.TEXT_SPEAK_UNHIT, true);// RefreshFollowResult();
                }
                else
                {
                    RefreshResults(TextFieldExtension.TEXT_SPEAK_FINAL_HIT, TextFieldExtension.TEXT_SPEAK_UNHIT, false);//RefreshResult();
                }

            });
        }

        private List<MatchResult> RefreshResults(Color HitColor, Color UnHitColor, bool notShowWordInBox)
        {
            // 获取语音识别的匹配结果
            var matchResults = SpeechToTextManager.instance.MatchWordsInReference(HitColor, UnHitColor);

            var matchCount = matchResults.Count(m => m.Color == HitColor && !m.IsPunctuation);
            var totalCount = matchResults.Count(m => !m.IsPunctuation);
            matchPercent = 1f * matchCount / totalCount;

            //_comTf.RemoveAllRichText();
            //_comTf.RemoveAllBoxes();

            DrawBoxByMatchResults(matchResults, notShowWordInBox);//刷新box,且中间会改写部分result属性
            _comTf?.SetEvaluateResult(matchResults);
            _comTf?.Display(ShowMode.SpeakEval);
            return matchResults;
        }

        private void ProcessQuestionContent(RichContent questionContent, Dictionary<string, MatchResult> matchWordsDict,bool notShowWordInBox)
        {
            // 从题目内容中提取单词
            var questionWords = questionContent.Content
                .Split(' ', StringSplitOptions.RemoveEmptyEntries)
                .Select(w => w.ToLower().Trim());

            foreach (var originalWord in questionWords)
            {
                //Debug.Log("PQC-originalWord =" + originalWord);
                if (matchWordsDict.TryGetValue(originalWord, out var matchResult))
                {
                 
                    Color boxColor = TextFieldExtension.TEXT_SPEAK_UNHIT_BOX;
                 
                    if (matchResult.Color == TextFieldExtension.TEXT_SPEAK_FINAL_HIT)
                    {
                        boxColor =  TextFieldExtension.TEXT_SPEAK_FINAL_HIT_BOX;
                        Debug.Log("matchResult= " + matchResult.OrigWord + " => Final_HIT_BOX  doHide:" + notShowWordInBox);
                    }
                    else if(matchResult.Color == TextFieldExtension.TEXT_SPEAK_HIT)
                    {
                        boxColor = TextFieldExtension.TEXT_SPEAK_HIT_BOX;
                        Debug.Log("matchResult " + matchResult.OrigWord + " => HIT_BOX  doHide:" + notShowWordInBox);
                    }
                    else
                    {
                        Debug.Log("matchResult= " + matchResult.OrigWord + " => UNHIT_BOX doHide:" + notShowWordInBox);
                    }
                    

                    //如果展示box，
                    if (notShowWordInBox)
                    {
                        matchResult.HideDisplay();//这里隐藏后 后续显示eval时文字就没有了
                    }

                    _comTf.DrawMaskAsBoxBytext(matchResult.OrigWord, boxColor);
                }
            }
        }

        private void ProcessQuestionContent2(RichContent questionContent, Dictionary<string, MatchResult> matchWordsDict, bool notShowWordInBox)
        {
            // 从题目内容中提取单词
            var questionWords = questionContent.Content
                .Split(' ', StringSplitOptions.RemoveEmptyEntries)
                .Select(w => w.ToLower().Trim());

            foreach (var originalWord in questionWords)
            {
                //Debug.Log("PQC2-originalWord =" + originalWord);
                if (matchWordsDict.TryGetValue(originalWord, out var matchResult))
                {
                    //if (matchResult.Color == TextFieldExtension.TEXT_SPEAK_FINAL_HIT)
                    //{
                       
                    //    Debug.Log("00matchResult= " + matchResult.OrigWord + " => Final_HIT_BOX  doHide:" + notShowWordInBox);
                    //}
                    //else if (matchResult.Color == TextFieldExtension.TEXT_SPEAK_HIT)
                    //{
                      
                    //    Debug.Log("00matchResult " + matchResult.OrigWord + " => HIT_BOX  doHide:" + notShowWordInBox);
                    //}
                    //else
                    //{
                    //    Debug.Log("00matchResult= " + matchResult.OrigWord + " => UNHIT_BOX doHide:" + notShowWordInBox);
                    //}


                    //如果展示box，
                    if (notShowWordInBox)
                    {
                        matchResult.HideDisplay();//这里隐藏后 后续显示eval时文字就没有了
                    }
                }
            }
        }
        #endregion

        #region State Management
        public void SetCurrentStatus(bool isCurrent)
        {
            if (IsReceivingEvents == isCurrent) return;

            if (!isCurrent)
            {
                ChangeState(Action.Idle);
                RecordEventManager.Instance.DispatchRecordCancel();
            }

            // 启用/禁用录音事件监听
            IsReceivingEvents = isCurrent;
        }
        public void OnClickNextQuestion()
        {
            _curPlayerClip = null;
            ShowVoiceBtns(false);
            GSoundManager.instance.StopTTS();
            if (questionData.question.tts_record_id != -1)
            {
                TTSManager.instance.StopTTS(questionData.question.tts_record_id);
            }
        }
        private void ReqMaskTips(NewWordComponent obj, Vector2 pos)
        {
            if (btnPlayVoic.visible) return;
            AddChild(obj);
            obj.xy = obj.GlobalToLocal(pos);
            obj.SetWord();
        }

        #endregion
    }
}

// 新增 ASR 结果数据类
public class ASRResultData
{
    public ByteString AudioData { get; set; }
    public string AsrContent { get; set; }
    public long UUID { get; set; }
}
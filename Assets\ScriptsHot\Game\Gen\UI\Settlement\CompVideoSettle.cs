/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Settlement
{
    public partial class CompVideoSettle : UIBindT
    {
        public override string pkgName => "Settlement";
        public override string comName => "CompVideoSettle";

        public GLoader3D ldr3dFlow;
        public GGraph effectPosFlag;
        public GTextField tfCongra;
        public VideoLanguageComp compContent;
        public GRichTextField tfTimes;
        public GRichTextField tfMin;
        public GGroup timeGrp;
        public GGroup grp1;
        public GLoader3D spineStar;
        public GTextField tf_xp_num;
        public GTextField nXP;
        public GGroup xpGrp;
        public GGroup grp2;
        public GGroup grpTotal;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ldr3dFlow = (GLoader3D)com.GetChildAt(0);
            effectPosFlag = (GGraph)com.GetChildAt(1);
            tfCongra = (GTextField)com.GetChildAt(2);
            compContent = new VideoLanguageComp();
            compContent.Construct(com.GetChildAt(3).asCom);
            tfTimes = (GRichTextField)com.GetChildAt(7);
            tfMin = (GRichTextField)com.GetChildAt(8);
            timeGrp = (GGroup)com.GetChildAt(9);
            grp1 = (GGroup)com.GetChildAt(10);
            spineStar = (GLoader3D)com.GetChildAt(13);
            tf_xp_num = (GTextField)com.GetChildAt(14);
            nXP = (GTextField)com.GetChildAt(15);
            xpGrp = (GGroup)com.GetChildAt(16);
            grp2 = (GGroup)com.GetChildAt(17);
            grpTotal = (GGroup)com.GetChildAt(18);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ldr3dFlow = null;
            effectPosFlag = null;
            tfCongra = null;
            compContent.Dispose();
            compContent = null;
            tfTimes = null;
            tfMin = null;
            timeGrp = null;
            grp1 = null;
            spineStar = null;
            tf_xp_num = null;
            nXP = null;
            xpGrp = null;
            grp2 = null;
            grpTotal = null;
        }
    }
}
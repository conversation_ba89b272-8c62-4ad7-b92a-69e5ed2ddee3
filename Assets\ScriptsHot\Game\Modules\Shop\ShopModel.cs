/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/17 20:37:14 星期五
# 功能：Nothing
****************************************************
*/

using System;
using System.Collections.Generic;
using System.Linq;
using Msg.basic;
using Msg.dialog_task;
using Msg.economic;
using PB_UserMerchandiseInfo = Msg.basic.PB_UserMerchandiseInfo;

namespace ScriptsHot.Game.Modules.Shop
{
    public class MemberInfo
    {
        public long userId { get; private set; }
        public SubscribeStatus subscribeStatus { get; private set; }
        public MemberType memberType{ get; private set; }
            
        public long expiredTime{ get; private set; }

        public void SetMemberType(MemberType msg)
        {
            memberType = msg;
        }
            
        public void SetSubscribeStatus(SubscribeStatus msg)
        {
            subscribeStatus = msg;
        }
            
        public void SetUserId(long msg)
        {
            userId = msg;
        }
            
        public void SetExpiredTime(long msg)
        {
            expiredTime = msg;
        }
    }
    
    public partial class ShopModel: BaseModel
    {
        public ShopModel() : base(ModelConsts.Shop) { }
        
        public PB_MemberInfoResp memberInfo{ get; private set; } = new ();

        public Dictionary<string,string> orderInfo{ get; private set; } = new ();

        public Dictionary<string, PB_UserMerchandiseInfo> myItemsInfoDic { get; private set; } = new();
        
        public Dictionary<string,  PB_MerchandiseInfo> myItemsCfgInfoDic { get; private set; } = new();

        public Dictionary<string, PB_SubscriptionInfo> SubscriptionInfoDic { get; set; } = new Dictionary<string, PB_SubscriptionInfo>();

        public OpenShopSource soure;

        private string _member_status;
        public string member_status
        {
            get { return _member_status;}
            set
            {
                AmazonBi.shopModel_member_status = value;
                _member_status = value;
            }
        }

        private string _member_type;

        public string member_type
        {
            get { return _member_type;}
            set
            {
                AmazonBi.shopModel_member_type = value;
                _member_type = value;
            }
        }
        

        public enum RecommendPlanType
        {
            Month = 0,
            Quarter,
            Year,
        }
        
        public enum ShopUIStyle
        {
            Duo = 0,
            Speak,
        }
        
        // ABTest用 优先推荐订阅Plan，0=年，1=月
        public RecommendPlanType testRecommendPlan { get; private set; }


        // ABTest用 商店所用的UI模板
        public ShopUIStyle shopUIStyle { get; private set; } = ShopUIStyle.Duo;
        
        public PB_ShopInfoData shopInfoData { get; private set; } = new();

        public Dictionary<string, PB_InAppPurchaseInfo> InAppPurchaseInfoDic { get; set; } = new Dictionary<string, PB_InAppPurchaseInfo>();
        public string OnBoardPaywallAB { get; private set; } = string.Empty;
        public void SetMemberInfo(PB_MemberInfoResp msg)
        {
            memberInfo = msg;
        }
        
        public void SetOrderInfo(string orderId, string reqId)
        {
            orderInfo[orderId] = reqId;
            
        }

        public void SetOnBoardPaywallModel(string str)
        {
            OnBoardPaywallAB = str;
        }

        public void SetShopInfo(PB_ShopInfoData msg)
        {
            shopInfoData = msg;
            myItemsInfoDic.Clear();
            if (msg.user_merchandise_infos != null && msg.user_merchandise_infos.Count > 0)
            {
                foreach (var itemInfo in msg.user_merchandise_infos)
                {
                    if (itemInfo.merchandise_id == "101102100")
                    {
                        itemInfo.merchandise_id = "101102101";
                    }

                    myItemsInfoDic[itemInfo.merchandise_id] = itemInfo;
                } 
            }
            SubscriptionInfoDic.Clear();
            SubscriptionInfoDic = new Dictionary<string, PB_SubscriptionInfo>();
            foreach (var subscription in msg.subscription_infos)
            {
                SubscriptionInfoDic[subscription.member_name] = subscription;
                
                VFDebug.Log($"#########################收到服务器价格数据 {subscription.member_name}");
                VFDebug.Log($"#########################原价 = {subscription.origin_price_in_cents}");
                VFDebug.Log($"#########################折后价 = {subscription.price_in_cents}");
            }

            InAppPurchaseInfoDic.Clear();
            InAppPurchaseInfoDic = new Dictionary<string, PB_InAppPurchaseInfo>();
            foreach (var purchase_info in msg.in_app_purchase_infos)
            {
                InAppPurchaseInfoDic[purchase_info.product_id] = purchase_info;
            }
            
            myItemsCfgInfoDic.Clear();
            if (msg.merchandise_infos != null && msg.merchandise_infos.Count > 0)
            {
                foreach (var itemInfo in msg.merchandise_infos)
                {
                    myItemsCfgInfoDic[itemInfo.merchandise_id] = itemInfo;
                } 
            }

        }

        public bool TryGetItemInfo(string k, out PB_UserMerchandiseInfo info)
        {
            info = null;
            if (myItemsInfoDic != null && myItemsInfoDic.ContainsKey(k))
            {
                info = myItemsInfoDic[k];
                return true;
            }

            return false;
        }
        
        public bool TryGetItemCfgInfo(string k, out PB_MerchandiseInfo info)
        {
            info = null;
            if (myItemsCfgInfoDic != null && myItemsCfgInfoDic.ContainsKey(k))
            {
                info = myItemsCfgInfoDic[k];
                return true;
            }

            return false;
        }
        
        public void TryGetItemInfo(string k, PB_UserMerchandiseInfo info)
        {

            if (myItemsInfoDic != null && myItemsInfoDic.ContainsKey(k))
            {
                myItemsInfoDic[k] = info;
            }
        }

        public DateTime GetMemberExpiredTime()
        {
            return DateTimeOffset.FromUnixTimeSeconds(memberInfo.expire_time).LocalDateTime;
        }

        public PB_SubscriptionInfo GetMemberTypeByName(string memberName)
        {
           if(!SubscriptionInfoDic.ContainsKey(memberName))return null;
            return SubscriptionInfoDic[memberName];
        }
        
        public bool TryGetMemberTypeByName(string memberName, out PB_SubscriptionInfo info)
        {
            info = null;
            var i = GetMemberTypeByName(memberName);
            if (i != null)
            {
                info = i;
                return true;
            }
            return false;
        }
        
        public void SetTestRecommendPlan(RecommendPlanType p)
        {
            testRecommendPlan = p;
        }
        
        public void SetShopUIStyle(ShopUIStyle s)
        {
            shopUIStyle = s;
        }

        public int GetRecommendPlanDays()
        {
            if (SubscriptionInfoDic == null || SubscriptionInfoDic.Count == 0)
            {
                VFDebug.LogError("subscription infos are null. Please check SC_GetShopInfoResp response. type: " + testRecommendPlan);
                return 0;
            }
            if (testRecommendPlan == RecommendPlanType.Month && TryGetMemberTypeByName("premium_monthly", out var info))
            {
                return info.free_days;
            }
            if (testRecommendPlan == RecommendPlanType.Quarter && TryGetMemberTypeByName("premium_quarterly", out var info2))
            {
                return info2.free_days;
            }
            if (testRecommendPlan == RecommendPlanType.Year && TryGetMemberTypeByName("premium_yearly", out var info3))
            {
                return info3.free_days;
            }
            VFDebug.LogError("Can't find the subscription info. Please check SC_GetShopInfoResp response. type: " + testRecommendPlan);
            return 0;
        }
        
    }
}

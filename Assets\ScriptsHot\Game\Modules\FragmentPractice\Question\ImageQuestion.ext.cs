using System;
using FairyGUI;

namespace UIBind.FragmentPractice
{
    public interface IImageQuestion
    {
        string GetImageUrl();
    }

    public partial class ImageQuestion : AFragQuestion
    {
        public override void ShowPractice(AFragAnswer answer)
        {
            if (!IsCurrent) return;
            ldrImage.url = (Practice as IImageQuestion).GetImageUrl();
            tfWord.text = Practice.GetStem();

            ctrlTitle.selectedPage = string.IsNullOrEmpty(tfWord.text) ? "invisible" : "visible";

            EnsureBoundsCorrect();
        }
    }
}
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ClozeOptionBtn : GButton
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "ClozeOptionBtn";
        public static string url => "ui://cmoz5osjt0ae2a";

        public Controller ctrlColor;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ClozeOptionBtn));
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            ctrlColor = GetControllerAt(0);
        }
        public override void Dispose()
        {
            ctrlColor = null;

            base.Dispose();
        }
    }
}
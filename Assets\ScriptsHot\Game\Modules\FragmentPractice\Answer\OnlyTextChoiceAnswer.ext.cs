﻿// using FairyGUI;
// using Game.Modules.FragmentPractice;
// using Msg.question;
// using ScriptsHot.Game.Modules.FragmentPractice;

// //不能跟single choice共用，这个要高度自适应，那个使用的是base里面的状态 直接改有高度冲突
// //仅用来单独显示文字并且高度自适应
// namespace UIBind.FragmentPractice
// {
//     public partial class OnlyTextChoiceAnswer : ExtendedComponent, IQuestionEventListener
//     {
//         private int _selectedAnswerIndex = -1;
//         private (string, long, int)[] optionsData;


//         protected override void OnAddedToStage()
//         {
//             listAnswers.itemRenderer = OnRendererAnswer;
//         }

//         protected override void OnRemovedFromStage()
//         {
//             QuestionEventManager.Instance.RemoveListener(this);
//         }

//         public override void Init(bool isCurrent, APracticeData practice)
//         {
//             base.Init(isCurrent, practice);
//             if (isCurrent)
//             {
//                 listAnswers.onClickItem.Add(OnClickAnswer);
//                 QuestionEventManager.Instance.AddListener(this);
//             }
//         }

//         public override void ShowPractice(AFragQuestion questionComp)
//         {
//             // 新题开始
//             _selectedAnswerIndex = -1;

//             if (Practice.QuestionType == PB_QuickPracticeType.Explanation || Practice.QuestionType == PB_QuickPracticeType.Reading)
//             {
//                 tfQuest.text = Practice.GetQuestion();
//                 title.selectedIndex = 1;
//             }
//             else
//             {
//                 title.selectedIndex = 0;
//             }
//             ShuffleOptions();
//             listAnswers.touchable = true;
//             listAnswers.numItems = Practice.GetAnswerOptions().Length;
//             listAnswers.ResizeToFit();
//             grp.EnsureBoundsCorrect();
//         }
        
//         private void OnRendererAnswer(int index, GObject obj)
//         {
//             GComponent comp = obj as GComponent;
//             BtnChoiceAnswer item = new BtnChoiceAnswer();
//             item.Construct(comp);

//             var content = optionsData[index].Item1;
//             var ttsId_ = optionsData[index].Item2;
//             item.tfAnswer.text = content;
//             //只为set ttsId
//             item.btnAnswer.SetMode(content, ttsId_, true, false);
//             item.btnAnswer.SetGrpState(BtnBaseAnswer.AnswerGrpState.None);
//             item.btnAnswer.UnEnable();
//             item.state.selectedIndex = (int)BtnBaseAnswer.State.Normal;
//         }

//         private void ShuffleOptions()
//         {
//             string[] options = Practice.GetAnswerOptions();
//             long[] ttsId = Practice.GetAnswerOptionTts();

//             optionsData = new (string, long, int)[options.Length];
//             for (int i = 0; i < options.Length; i++)
//             {
//                 optionsData[i] = (options[i], i < ttsId.Length?ttsId[i]:0, i);
//             }

//             // 高德纳洗牌
//             for (int i = optionsData.Length - 1; i > 0; i--)
//             {
//                 int j = UnityEngine.Random.Range(0, i + 1);
//                 (optionsData[i], optionsData[j]) = (optionsData[j], optionsData[i]);
//             }
//         }


//         private void OnClickAnswer(EventContext context)
//         {
//             if (!IsCurrent || Practice == null) return;
            
//             if (context.sender is not GComponent comp) return;

//             BtnChoiceAnswer selectedBtn = new BtnChoiceAnswer();
//             selectedBtn.Construct(context.data as GComponent);
            
//             //BtnBaseAnswer selectedBtn = context.data as BtnBaseAnswer;
//             if (selectedBtn.btnAnswer?.TtsId > 0)
//             {
//                 TTSManager.instance.StopTTS();
//                 TTSManager.instance.PlayTTS(selectedBtn.btnAnswer.TtsId);
//             }
//             int index = comp.GetChildIndex(context.data as GComponent);
//             if (index == _selectedAnswerIndex) return;

//             VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);

//             _selectedAnswerIndex = index;

//             for (int i = 0; i < listAnswers.numItems; i++)
//             {
//                 Controller stateCtrl = listAnswers.GetChildAt(i).asCom.GetController("state");
//                 if (_selectedAnswerIndex != i)
//                     stateCtrl.selectedIndex = (int)BtnBaseAnswer.State.Normal;
//                 else
//                     stateCtrl.selectedIndex = (int)BtnBaseAnswer.State.Select;
//             }
//             CommitAnswer(optionsData[_selectedAnswerIndex].Item3.ToString() == Practice.CorrectAnswer ? 1f : 0f);

//         }
        
//         public void OnAnswered()
//         {
//         }

//         public void OnSubmit()
//         {
//             listAnswers.touchable = false;
//             for (int i = 0; i < listAnswers.numItems; i++)
//             {
//                 Controller stateCtrl = listAnswers.GetChildAt(i).asCom.GetController("state");
//                 bool isCorrect = optionsData[_selectedAnswerIndex].Item3.ToString() == Practice.CorrectAnswer;
//                 if (_selectedAnswerIndex == i)
//                 {
//                     if (isCorrect)
//                     {
//                         BtnBaseAnswer option = listAnswers.GetChildAt(i).asCom.GetChild("btnAnswer") as BtnBaseAnswer;
//                         option?.ShowChoiceEffect();
//                     }
//                     stateCtrl.selectedIndex = isCorrect ? (int)BtnBaseAnswer.State.Right : (int)BtnBaseAnswer.State.Error;
//                 }
//                 else
//                     stateCtrl.selectedIndex = (int)BtnBaseAnswer.State.Normal;
//             }
//         }

//         public void OnRetry()
//         {
//         }

//         public void AutoCheck()
//         {
//         }

//         public void OnReset()
//         {
//         }

//         public void OnJumpListenTask()
//         {
//             listAnswers.touchable = false;
//         }

//         public void OnJumpSpeakTask()
//         {
//             listAnswers.touchable = false;
//         }
//     }
// }
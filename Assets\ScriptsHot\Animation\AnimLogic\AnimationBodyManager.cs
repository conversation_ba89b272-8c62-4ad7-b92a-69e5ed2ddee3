using UnityEngine;
using UnityEngine.Playables;
using System.Collections.Generic;
using UnityEngine.Animations;

namespace  AnimationSystem
{
    public class AnimationBodyManager
    {
        #region avatarManager

        //上一级别传递的数据.
        public AnimationAvatarManager avatarManager;
        private AnimationLayerMixerPlayable avatarLayerMixer;
        public PlayableGraph playableGraph;
        public Animator animator;
        private AvatarMask bodyMask;

        #endregion

        public bool isInitialized = false;
        private static int BODY_LAYER = 0;
        private AnimState currentState;
        
        // 用于记录默认动画的ID，以便后续比较
        //private string currentDefaultGroupId = "";
        private string currentAnimGroupId = ""; //当前正在播放的【主要的】动作组id。
        private GroupPlayableData currentGroupPlayableData;
        
        // 表示混合树中的一个节点
        private class MixerNode : Node
        {
            public AnimationMixerPlayable mixer;       // 本节点的混合器
            public AnimationMixerPlayable input0;                    // 第一个输入（上游节点或动画组）
            public AnimationMixerPlayable input1;                    // 第二个输入（通常是新动画组）
            
            public float transitionStartTime;          // 过渡开始时间
            public float transitionDuration;           // 过渡持续时间
            public bool isTransitioning = true;        // 是否正在过渡
            
            // 树结构
            public List<Node> children = new List<Node>();  // 子节点列表
        }
        
        private class PlayableNode : Node
        {
            public GroupPlayableData group;            // 直接关联的动画组（对叶子节点）
        }

        private class Node
        {
            public MixerNode parent;                   // 父节点
        }
        
        // 当前活跃的根混合器节点
        private MixerNode rootNode;

        // 组内Mixer相关
        private class GroupPlayableData
        {
            public AnimationMixerPlayable groupMixer;
            public List<AnimationClipPlayable> activeClips = new List<AnimationClipPlayable>();
            public AnimGroupPlayData playData;
            public int currentClipIndex;
            public float startTime;
            public bool isBlending;
            public float blendStartTime;
            public float blendDuration;
            public int blendFromIndex;
            public int blendToIndex;
        }

        // 是否正在播放默认动画
        private bool isPlayingDefaultAnim = false;
        private bool isPaused = false;
        private float pauseStartTime = 0f; // 记录开始暂停的时间点

        #region lifecycle

        public bool Initialize(
            AnimationAvatarManager avatarManager, 
            Animator animator, 
            PlayableGraph graph,
            AnimationLayerMixerPlayable avatarLayerMixer,
            AnimationPlayableOutput playableOutput,
            AvatarMask bodyMask)
        {
            if (isInitialized) return true;

            //保存引用
            this.animator = animator;
            this.playableGraph = graph;
            this.avatarManager = avatarManager;
            this.avatarLayerMixer = avatarLayerMixer;
            this.bodyMask = bodyMask;
            
            // 创建根混合器节点
            rootNode = new MixerNode
            {
                mixer = AnimationMixerPlayable.Create(playableGraph, 1),
                isTransitioning = false
            };
            
            // 连接到avatarLayerMixer
            playableGraph.Connect(rootNode.mixer, 0, avatarLayerMixer, BODY_LAYER);
            avatarLayerMixer.SetInputWeight(BODY_LAYER, 1.0f);
            
            // 设置层级遮罩
            if(bodyMask) avatarLayerMixer.SetLayerMaskFromAvatarMask((uint)BODY_LAYER, bodyMask);
            avatarLayerMixer.SetLayerAdditive((uint)BODY_LAYER, false);
            
            isInitialized = true;
            
        //    VFDebug.Log("AnimationBodyManager初始化完成 - 使用树状混合结构");
            return true;
        }

        #endregion

        public void PlayAnimGroupData(AnimGroupPlayData groupData,  float transitionTime,bool isDefault = false)
        {
            if (!isInitialized || groupData.animPlayDatas.Count == 0)
            {
                VFDebug.LogError($"收到命令播放一个动画,但是没有对应资源,该动画不会播放.(动画id:{groupData.animGroup?.bodyGroupID},动画类别:{this.avatarManager.animTypeId},动画数量:{groupData.animGroup?.GetAnimInfos().Count}");
                return;
            }
            
            // 检查是否是相同的默认动画,如果是,那么返回[并且将默认动画的持续时间设为无限]
            if (isDefault && currentAnimGroupId == groupData.animGroup.bodyGroupID)
            {
                currentGroupPlayableData.playData.animPlayDatas[0].loopCount = 1666666.0f;
                
                //debug:如果通过MoveToNextAnimation过来的，在结束的时候本来会已经Pause当前动画。所以这里需要重新Play,确保能够正常播放无限循环。
                currentGroupPlayableData.activeClips[currentGroupPlayableData.currentClipIndex].Play();
                return;
            }
            
            // 创建新动画组数据
            var newGroupData = CreateGroupPlayableData(groupData);
            PlayableNode node = new PlayableNode()
            {
                group = newGroupData,
                parent = rootNode,
            };
            
            // 如果根节点没有输入，直接连接
            if (rootNode.input0.IsNull())
            {
                // 连接新动画组到根节点
                rootNode.children.Add(node);
                playableGraph.Connect(newGroupData.groupMixer, 0, rootNode.mixer, 0);
                rootNode.mixer.SetInputWeight(0, 1.0f);
                rootNode.input0 = node.group.groupMixer;
            }
            else
            {
                // 创建从当前状态到新动画的过渡
                CreateTransition(node, transitionTime);
            }
            
            isPlayingDefaultAnim = isDefault;
            currentGroupPlayableData = newGroupData;
            currentAnimGroupId = newGroupData.playData.animGroup.bodyGroupID;
        }

        // 创建从当前状态到新动画组的过渡
        private void CreateTransition(PlayableNode node, float duration)
        {
            if (duration >= 2.0f)
            {
                VFDebug.LogWarning("过渡时长超过1秒!不会导致问题,但可能导致大量动作组叠加。(如果重复触发过渡)");
            }
            
            // 创建新的混合器节点
            MixerNode newNode = new MixerNode
            {
                mixer = AnimationMixerPlayable.Create(playableGraph, 2),
                input1 = node.group.groupMixer,
                input0 = rootNode.mixer,
                transitionStartTime = Time.time,
                transitionDuration = duration,
                isTransitioning = true,
                parent = null
            };
            
            // 先断开当前根节点与avatarLayerMixer的连接,不然会导致根节点被占用无法继续链接新的节点。
            playableGraph.Disconnect(avatarLayerMixer, BODY_LAYER);
            
            // 连接当前根节点到新节点的第一个输入
            playableGraph.Connect(newNode.input0, 0, newNode.mixer, 0);
            newNode.mixer.SetInputWeight(0, 1.0f);

            // 连接新动画组到新节点的第二个输入
            playableGraph.Connect(newNode.input1, 0, newNode.mixer, 1);
            newNode.mixer.SetInputWeight(1, 0.0f);
            
            // 建立父子关系
            newNode.children.Add(rootNode);
            newNode.children.Add(node);
            node.parent = newNode;
            rootNode.parent = newNode;
            
            // 连接新节点到avatarLayerMixer
            playableGraph.Connect(newNode.mixer, 0, avatarLayerMixer, BODY_LAYER);
            
            // 更新根节点引用
            rootNode = newNode;
            
        //    VFDebug.Log($"创建新过渡，持续时间={duration}秒");
        }

        public void SetAnimState(AnimState state)
        {
            if (state != null)
            {
                this.currentState = state;
            }
        }
        
        #region debugger

        public bool trigger;

        #endregion

        public void Update()
        {
            //debug
            if (trigger)
            {
                Util.PrintPlayableGraph(playableGraph);
                trigger = false;
            }
            
            // 如果暂停，不更新动画
            if (isPaused)
            {
                return;
            }

            // 从根节点递归更新整个混合树
            UpdateNode(rootNode);
            
            // 收集并更新所有动画组
            List<GroupPlayableData> activeGroups = new List<GroupPlayableData>();
            CollectActiveGroups(rootNode, activeGroups);
            
            // 更新所有活跃的动画组
            foreach (var group in activeGroups)
            {
                UpdateGroupPlayback(group);
            }
        }
        
        // 递归更新节点
        private void UpdateNode(Node node)
        {
            if (node == null) return;

            //只更新MixerNode.
            if (node is MixerNode)
            {
                MixerNode mixerNode = node as MixerNode;
                
                // 更新过渡状态
                if (mixerNode.isTransitioning)
                {
                    float t = (Time.time - mixerNode.transitionStartTime) / mixerNode.transitionDuration;
                
                    if (t >= 1.0f)
                    {
                        // 过渡完成
                        FinishNodeTransition(mixerNode);
                    }
                    else
                    {
                        var curve = avatarManager.avatarAnimationDataObject.genericAnimationBlending;
                        var curveVal = curve.Evaluate(t);
                
                        mixerNode.mixer.SetInputWeight(0, 1 - curveVal);
                        mixerNode.mixer.SetInputWeight(1, curveVal);
                    }
                }
            
                // 递归更新所有子节点
                foreach (var child in new List<Node>(mixerNode.children))
                {
                    UpdateNode(child);
                }
            }
        }
        
        // 完成节点过渡
        private void FinishNodeTransition(MixerNode node)
        {
            // 设置最终权重
            node.mixer.SetInputWeight(0, 0.0f);
            node.mixer.SetInputWeight(1, 1.0f);
            node.isTransitioning = false;
            
            // 保存节点1（目标节点）的信息，它将移至节点0
            AnimationMixerPlayable targetPlayable = node.input1;
            
            // 断开两个输入连接
            if (!node.input0.IsNull())
            {
                playableGraph.Disconnect(node.mixer, 0);
            }
            
            if (!node.input1.IsNull())
            {
                playableGraph.Disconnect(node.mixer, 1);
            }
            
            // 清理那个旧的节点
            MixerNode nodeToClean = null;
            foreach (var child in node.children)
            {
                if (child is MixerNode)
                {
                    if ((child as MixerNode).mixer.Equals(node.input0))
                    {
                        nodeToClean = child as MixerNode;
                        break;
                    }
                    nodeToClean = child as MixerNode;
                }
            }

            if (nodeToClean != null)
            {
                node.children.Remove(nodeToClean);
                CleanupNodeTree(nodeToClean);
            }
            else
            {
                VFDebug.LogError("存在一个没有叶子节点的MixerNode!这里需要修改!");
            }
            
            // 重置节点输入引用
            node.input0 = AnimationMixerPlayable.Null;
            node.input1 = AnimationMixerPlayable.Null;
            
            // 如果有目标Playable，现在将其连接到节点0（保持节点0始终被使用）
            if (!targetPlayable.IsNull())
            {
                playableGraph.Connect(targetPlayable, 0, node.mixer, 0);
                node.mixer.SetInputWeight(0, 1.0f);
                node.input0 = targetPlayable;
            }
        }
        
        // 递归清理节点树
        private void CleanupNodeTree(Node node)
        {
            if (node == null) return;
    
            if (node is MixerNode mixerNode)
            {
                // 先递归清理所有子节点
                foreach (var child in new List<Node>(mixerNode.children))
                {
                    CleanupNodeTree(child);
                }
                mixerNode.children.Clear();
        
                // 断开所有连接
                if (!mixerNode.input0.IsNull())
                {
                    playableGraph.Disconnect(mixerNode.mixer, 0);
                    mixerNode.input0 = AnimationMixerPlayable.Null;
                }
        
                if (!mixerNode.input1.IsNull())
                {
                    playableGraph.Disconnect(mixerNode.mixer, 1);
                    mixerNode.input1 = AnimationMixerPlayable.Null;
                }
        
                // 销毁混合器
                if (mixerNode.mixer.IsValid())
                {
                    mixerNode.mixer.Destroy();
                }
            }
            else if (node is PlayableNode playableNode)
            {
                // 如果是PlayableNode，清理关联的动画组
                if (playableNode.group != null)
                {
                    CleanupGroupData(playableNode.group);
                    playableNode.group = null;
                }
            }
    
            // 从父节点移除此节点（如果有父节点）
            if (node.parent != null)
            {
                if (node.parent.children.Contains(node))
                {
                    node.parent.children.Remove(node);
                }
                node.parent = null;
            }
        }
        
        // 递归收集所有活跃的动画组
        private void CollectActiveGroups(Node node, List<GroupPlayableData> groupsList)
        {
            if (node == null) return;

            if (node is PlayableNode)
            {
                if ((node as PlayableNode).group != null)
                {
                    groupsList.Add((node as PlayableNode).group);
                }
            }
            else
            {
                // 递归处理所有子节点
                foreach (var child in (node as MixerNode).children)
                {
                    CollectActiveGroups(child, groupsList);
                }
            }
        }

        private void UpdateGroupPlayback(GroupPlayableData groupData)
        {
            if (groupData == null) return;
            
            float currentTime = Time.time - groupData.startTime;

            if (groupData.isBlending)
            {
                UpdateGroupBlending(groupData);
            }
            else
            {
                CheckForNextAnimation(groupData, currentTime, groupData != currentGroupPlayableData);
            }
        }

        private void UpdateGroupBlending(GroupPlayableData groupData)
        {
            float t = (Time.time - groupData.blendStartTime) / groupData.blendDuration;

            if (t >= 1.0f)
            {
                // 完成组内blend
                groupData.groupMixer.SetInputWeight(groupData.blendFromIndex, 0f);
                groupData.groupMixer.SetInputWeight(groupData.blendToIndex, 1f);

                // 暂停并重置源动画
                var sourceClip = groupData.activeClips[groupData.blendFromIndex];
                sourceClip.Pause();

                groupData.isBlending = false;
                groupData.currentClipIndex = groupData.blendToIndex;
                groupData.startTime = Time.time - (groupData.blendDuration *
                                                   groupData.playData.animPlayDatas[groupData.currentClipIndex].speed);
            }
            else
            {
                var curve = avatarManager.avatarAnimationDataObject.genericAnimationBlending;
                var curveVal = curve.Evaluate(t);
                
                groupData.groupMixer.SetInputWeight(groupData.blendFromIndex, 1 - curveVal);
                groupData.groupMixer.SetInputWeight(groupData.blendToIndex, curveVal);
            }
        }

        private void CheckForNextAnimation(GroupPlayableData groupData, float currentTime, bool ignoreDefaultTransition = false)
        {
            var currentPlayData = groupData.playData.animPlayDatas[groupData.currentClipIndex];
            float clipDuration = currentPlayData.clip.length / currentPlayData.speed;

            // 计算总时长（包括所有循环）
            float totalDuration = clipDuration * (1 + currentPlayData.loopCount);

            if (currentPlayData.willBlendToNext)
            {
                float blendStartPoint = clipDuration * currentPlayData.blendToNext;
                if (groupData.currentClipIndex < groupData.playData.animPlayDatas.Count - 1)
                {
                    if (currentTime >= blendStartPoint)
                    {
                        StartGroupBlending(groupData, currentPlayData.nextAnimIndex, currentPlayData.blendTime);
                    }
                }
                else
                {
                    if (currentTime >= blendStartPoint)
                    {
                        if (!isPlayingDefaultAnim && currentState != null && !ignoreDefaultTransition)
                        {
                            // 切换到默认动画,原因是该动画播放完毕,提前结束。
                            currentState.PlayDefaultAnimation();
                        }
                    }
                }
            }
            // 检查动画是否完全播放完成（包括所有循环）
            else if (currentTime >= totalDuration)
            {
                // 动画完全播放完成，切换到下一个动画
                MoveToNextAnimation(groupData, ignoreDefaultTransition);
            }
        }

        private void StartGroupBlending(GroupPlayableData groupData, int toIndex, float duration)
        {
            if (toIndex < 0 || toIndex >= groupData.activeClips.Count) return;

            // 准备目标动画
            var targetClip = groupData.activeClips[toIndex];
            targetClip.SetTime(0);
            targetClip.Play();

            groupData.isBlending = true;
            groupData.blendFromIndex = groupData.currentClipIndex;
            groupData.blendToIndex = toIndex;
            groupData.blendStartTime = Time.time;
            groupData.blendDuration = duration;
        }

        // 提取切换到下一个动画的逻辑为单独的方法
        private void MoveToNextAnimation(GroupPlayableData groupData, bool ignoreDefaultTransition = false)
        {
            var currentPlayData = groupData.playData.animPlayDatas[groupData.currentClipIndex];
            int nextIndex = currentPlayData.nextAnimIndex;

            // 暂停当前动画
            groupData.activeClips[groupData.currentClipIndex].Pause();

            if (nextIndex >= 0 && nextIndex < groupData.activeClips.Count)
            {
                // 切换到下一个动画
                groupData.groupMixer.SetInputWeight(groupData.currentClipIndex, 0f);
                groupData.groupMixer.SetInputWeight(nextIndex, 1f);

                // 重置并播放下一个动画
                groupData.activeClips[nextIndex].SetTime(0);
                groupData.activeClips[nextIndex].Play();

                groupData.currentClipIndex = nextIndex;
                groupData.startTime = Time.time;
            }
            else if (!isPlayingDefaultAnim && currentState != null && !ignoreDefaultTransition)
            {
                // 切换到默认动画
                currentState.PlayDefaultAnimation();
            }
        }

        // 创建动画组数据
        private GroupPlayableData CreateGroupPlayableData(AnimGroupPlayData groupData)
        {
            var newGroupData = new GroupPlayableData
            {
                groupMixer = AnimationMixerPlayable.Create(playableGraph, groupData.animPlayDatas.Count),
                playData = groupData,
                currentClipIndex = 0,
                startTime = Time.time,
                activeClips = new List<AnimationClipPlayable>()
            };
            
            // 创建并连接组内的clips
            for (int i = 0; i < groupData.animPlayDatas.Count; i++)
            {
                var clipPlayable = AnimationClipPlayable.Create(playableGraph, groupData.animPlayDatas[i].clip);
                clipPlayable.SetSpeed(groupData.animPlayDatas[i].speed);
                clipPlayable.SetApplyFootIK(groupData.animPlayDatas[i].enableFootIK);
                
                clipPlayable.Pause();
                clipPlayable.SetTime(0);
                
                newGroupData.activeClips.Add(clipPlayable);
                newGroupData.groupMixer.ConnectInput(i, clipPlayable, 0);
                newGroupData.groupMixer.SetInputWeight(i, i == 0 ? 1f : 0f);
            }
            
            // 只播放第一个动画
            if (newGroupData.activeClips.Count > 0)
            {
                newGroupData.activeClips[0].Play();
            }
            
            return newGroupData;
        }

        private void CleanupGroupData(GroupPlayableData groupData)
        {
            if (groupData == null) return;

            // 清理所有clip playables
            foreach (var clip in groupData.activeClips)
            {
                if (clip.IsValid())
                {
                    clip.Destroy();
                }
            }

            // 清理group mixer
            if (groupData.groupMixer.IsValid())
            {
                groupData.groupMixer.Destroy();
            }

            groupData.activeClips.Clear();
        }

        public void PauseAnimation(bool pause)
        {
            // 如果状态没有变化，直接返回
            if (isPaused == pause) return;

            // 如果组件处于禁用状态，只更新isPaused状态，不操作PlayableGraph
            if (!avatarManager.isActiveAndEnabled)
            {
                isPaused = pause;
                return;
            }

            // 暂停/恢复Playable图
            if (isInitialized && playableGraph.IsValid())
            {
                if (pause)
                {
                    // 记录开始暂停的时间
                    pauseStartTime = Time.time;
                    playableGraph.Stop();
                    isPaused = true;
            //        VFDebug.Log("动画已暂停");
                }
                else
                {
                    // 计算暂停的时长
                    float pauseDuration = Time.time - pauseStartTime;

                    // 更新所有节点的时间相关变量
                    UpdateNodeTimesAfterPause(rootNode, pauseDuration);

                    playableGraph.Play();
                    isPaused = false;
            //        VFDebug.Log($"动画已恢复，补偿暂停时长: {pauseDuration:F2}秒");
                }
            }
            else
            {
                isPaused = pause;
            }
        }

        // 递归更新节点时间以补偿暂停
        private void UpdateNodeTimesAfterPause(Node node, float pauseDuration)
        {
            if (node == null) return;
    
            if (node is MixerNode mixerNode)
            {
                // 更新混合器节点过渡时间
                if (mixerNode.isTransitioning)
                {
                    mixerNode.transitionStartTime += pauseDuration;
                }
        
                // 递归更新所有子节点
                foreach (var child in mixerNode.children)
                {
                    UpdateNodeTimesAfterPause(child, pauseDuration);
                }
            }
            else if (node is PlayableNode playableNode)
            {
                // 更新PlayableNode关联的动画组
                if (playableNode.group != null)
                {
                    playableNode.group.startTime += pauseDuration;
                    if (playableNode.group.isBlending)
                    {
                        playableNode.group.blendStartTime += pauseDuration;
                    }
                }
            }
        }

        public void OnDestroy()
        {
            // 递归清理整个混合树
            if (rootNode != null)
            {
                CleanupNodeTree(rootNode);
                rootNode = null;
            }
        }

        public void OnEnable()
        {
            if (isInitialized && playableGraph.IsValid() && !isPaused)
            {
                // 计算禁用期间的时长
                float disableDuration = Time.time - pauseStartTime;

                // 更新所有节点的时间相关变量
                UpdateNodeTimesAfterPause(rootNode, disableDuration);

            //    VFDebug.Log($"AnimationManager启用：恢复PlayableGraph播放，补偿禁用时长: {disableDuration:F2}秒");
            }
        }

        public void OnDisable()
        {
            // 如果PlayableGraph已初始化，记录禁用时间
            if (isInitialized && playableGraph.IsValid())
            {
                // 记录禁用时的时间点
                pauseStartTime = Time.time;

             //   VFDebug.Log($"AnimationManager禁用：(用户暂停状态: {(isPaused ? "已暂停" : "播放中")})");
            }
        }

        /// <summary>
        /// 动态设置身体遮罩，运行时生效
        /// </summary>
        /// <param name="newMask">新的身体遮罩</param>
        /// <returns>是否设置成功</returns>
        public bool SetAvatarMask(AvatarMask newMask)
        {
            if (newMask == null)
            {
                VFDebug.LogWarning("尝试设置null AvatarMask到BodyManager");
                return false;
            }
            
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationBodyManager未初始化，无法设置身体遮罩");
                return false;
            }
            
            bodyMask = newMask;
            
            // 如果已初始化，动态更新遮罩
            if (isInitialized && avatarLayerMixer.IsValid())
            {
                // 更新遮罩
                avatarLayerMixer.SetLayerMaskFromAvatarMask((uint)BODY_LAYER, bodyMask);
                VFDebug.Log("运行时动态更新身体遮罩成功");
                return true;
            }
            
            return false;
        }
    }
}

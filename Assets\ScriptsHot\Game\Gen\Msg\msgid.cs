// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/msgid.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg {

  /// <summary>Holder for reflection information generated from protobuf/msgid.proto</summary>
  public static partial class MsgidReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/msgid.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static MsgidReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChRwcm90b2J1Zi9tc2dpZC5wcm90byrxlQEKCUdhbWVNU0dJRBIXChNTU19T",
            "ZXJ2ZXJJbmZvUnB0X0lEEAASFwoTU1NfU2VydmVySW5mb0Fja19JRBABEhIK",
            "DkNTX0xvZ2luUmVxX0lEEAISEgoOU0NfTG9naW5BY2tfSUQQAxIYChRDU19H",
            "ZXRSb2xlSW5mb1JlcV9JRBAEEhgKFFNDX0dldFJvbGVJbmZvQWNrX0lEEAUS",
            "GgoWQ1NfRW50ZXJHYW1lWm9uZVJlcV9JRBAGEhoKFlNDX0VudGVyR2FtZVpv",
            "bmVBY2tfSUQQBxIYChRaV19HZXRSb2xlRGF0YVJlcV9JRBAIEhgKFFdaX0dl",
            "dFJvbGVEYXRhQWNrX0lEEAkSGQoVWldfU2F2ZVJvbGVEYXRhUmVxX0lEEAoS",
            "GQoVV1pfU2F2ZVJvbGVEYXRhQWNrX0lEEAsSFQoRQ1NfRXhpdEdhbWVSZXFf",
            "SUQQDBIVChFTQ19FeGl0R2FtZUFja19JRBANEhsKF0NTX0NoYW5nZVJvbGVO",
            "YW1lUmVxX0lEEA4SGwoXU0NfQ2hhbmdlUm9sZU5hbWVBY2tfSUQQDxIbChdT",
            "Q19DaGFuZ2VSb2xlTmFtZU50Zl9JRBAQEhUKEVdHX0tpY2tSb2xlUmVxX0lE",
            "EBESFQoRR1dfS2lja1JvbGVBY2tfSUQQEhIVChFXR19LaWNrR2F0ZVJlcV9J",
            "RBATEhUKEUdXX0tpY2tHYXRlQWNrX0lEEBQSFQoRV1pfS2lja0dhbWVSZXFf",
            "SUQQFRIVChFaV19LaWNrR2FtZUFja19JRBAWEhYKEkNTX0hlYXJ0QmVhdFJl",
            "cV9JRBAXEhYKElNDX0hlYXJ0QmVhdEFja19JRBAYEhcKE0NTX0NyZWF0ZVJv",
            "bGVSZXFfSUQQGRIXChNTQ19DcmVhdGVSb2xlQWNrX0lEEBoSFwoTQ1NfU2Vs",
            "ZWN0Um9sZVJlcV9JRBAbEhcKE1NDX1NlbGVjdFJvbGVBY2tfSUQQHBIbChdX",
            "R19UYWxraXRQdXNoSW5mb050Zl9JRBAdEhsKF1NDX1JvbGVPbmxpbmVJbmZv",
            "TnRmX0lEEGQSFQoRU0NfU2VsZkluZm9OdGZfSUQQZRIbChdTQ19Sb2xlQXBw",
            "ZWFySW5mb050Zl9JRBBmEh0KGVNDX0F2YXRhckFwcGVhckluZm9OdGZfSUQQ",
            "ZxIUChBTQ19PYmpNb3ZlTnRmX0lEEGgSGQoVU0NfT2JqRGlzYXBwZWFyTnRm",
            "X0lEEGkSFQoRQ1NfUm9sZU1vdmVSZXFfSUQQahIVChFTQ19Sb2xlTW92ZUFj",
            "a19JRBBrEhwKGENTX1JvbGVDaGFuZ2VTY2VuZVJlcV9JRBBsEhwKGFNDX1Jv",
            "bGVDaGFuZ2VTY2VuZUFja19JRBBtEhkKFVNDX0J1YmJsaW5nSW5mb050Zl9J",
            "RBBvEhcKE1NDX0FjdGlvbkluZm9OdGZfSUQQcBIZChVDU19UYWxrVG9BdmF0",
            "YXJSZXFfSUQQcRIZChVTQ19UYWxrVG9BdmF0YXJBY2tfSUQQchIZChVDU19H",
            "b1BsYXllckhvbWVSZXFfSUQQcxIZChVTQ19Hb1BsYXllckhvbWVBY2tfSUQQ",
            "dBIcChhDU19MZWF2ZVBsYXllckhvbWVSZXFfSUQQdRIcChhTQ19MZWF2ZVBs",
            "YXllckhvbWVBY2tfSUQQdhIbChdHQV9HZXRBdmF0YXJHcmVldFJlcV9JRBB3",
            "EhwKF0FHX0dldEF2YXRhckdyZWV0QWNrX0lEENwBEiIKHUFHX0F2YXRhclJl",
            "ZmxlY3Rpb25QdXNoUmVxX0lEEN0BEiIKHUFHX0F2YXRhclJlZmxlY3Rpb25Q",
            "dXNoQWNrX0lEEN4BEiIKHUNTX0dldEN1ckxpbmVBdmF0YXJJbmZvUmVxX0lE",
            "EN8BEiIKHVNDX0dldEN1ckxpbmVBdmF0YXJJbmZvQWNrX0lEEOABEhoKFUNT",
            "X1BsYXllckluVGFza1JlcV9JRBDiARIaChVTQ19QbGF5ZXJJblRhc2tBY2tf",
            "SUQQ4wESHgoZU0NfQXZhdGFyTW92ZVRvU2VsZk50Zl9JRBDkARIdChhDU19B",
            "Z3JlZUF2YXRhck1vdmVSZXFfSUQQ5QESHQoYU0NfQWdyZWVBdmF0YXJNb3Zl",
            "QWNrX0lEEOYBEiMKHkNTX0dldEJ1aWxkaW5nQXZhdGFySW5mb1JlcV9JRBDn",
            "ARIjCh5TQ19HZXRCdWlsZGluZ0F2YXRhckluZm9BY2tfSUQQ6AESIwoeU1Nf",
            "UHVzaFBsYXllckxhZGRlckxldmVsUmVxX0lEEOkBEiMKHlNTX1B1c2hQbGF5",
            "ZXJMYWRkZXJMZXZlbEFja19JRBDqARIiCh1TQ19QbGF5ZXJMYWRkZXJMZXZl",
            "bENoYW5nZV9JRBDrARIbChZaR19Ccm9hZGNhc3RJbmZvTnRmX0lEEOwBEh4K",
            "GVNDX01lbWJlclR5cGVDaGFuZ2VOdGZfSUQQ7QESHgoZQ1NfR2V0QXZhdGFy",
            "Sm9iSW5mb1JlcV9JRBDuARIeChlTQ19HZXRBdmF0YXJKb2JJbmZvQWNrX0lE",
            "EO8BEh0KGFNTX1VzZXJMYW5nSW5mb05vdGlmeV9JRBDwARIZChRDU19Wb2lj",
            "ZURpYWxvZ1JlcV9JRBDxARIZChRTQ19Wb2ljZURpYWxvZ0Fja19JRBDyARIZ",
            "ChRTQ19Wb2ljZURpYWxvZ050Zl9JRBDzARIdChhDU19EZWFsVm9pY2VEaWFs",
            "b2dSZXFfSUQQ9AESHQoYU0NfRGVhbFZvaWNlRGlhbG9nQWNrX0lEEPUBEh0K",
            "GFNDX0RlYWxWb2ljZURpYWxvZ050Zl9JRBD2ARIiCh1DU19HZXRWb2ljZURp",
            "YWxvZ1JlY29yZFJlcV9JRBD3ARIiCh1TQ19HZXRWb2ljZURpYWxvZ1JlY29y",
            "ZEFja19JRBD4ARIdChhTQ19QbGF5ZXJTdGF0ZUluZm9OdGZfSUQQ+QESHgoZ",
            "U0NfU3RhcnRWb2ljZURpYWxvZ050Zl9JRBD6ARIkCh9DU19Wb2ljZURpYWxv",
            "Z1N0YXRlQ2hhbmdlUmVxX0lEEPsBEiQKH1NDX1ZvaWNlRGlhbG9nU3RhdGVD",
            "aGFuZ2VBY2tfSUQQ/AESJAofU0NfVm9pY2VEaWFsb2dTdGF0ZUNoYW5nZU50",
            "Zl9JRBD9ARIdChhDU19TdG9wVm9pY2VEaWFsb2dSZXFfSUQQ/gESHQoYU0Nf",
            "U3RvcFZvaWNlRGlhbG9nQWNrX0lEEP8BEh0KGFNDX1N0b3BWb2ljZURpYWxv",
            "Z050Zl9JRBCAAhIfChpDU19DYW5jZWxWb2ljZURpYWxvZ1JlcV9JRBCBAhIf",
            "ChpTQ19DYW5jZWxWb2ljZURpYWxvZ0Fja19JRBCCAhIfChpTQ19DYW5jZWxW",
            "b2ljZURpYWxvZ050Zl9JRBCDAhIlCiBTQ19Gb3JiaWRWb2ljZURpYWxvZ0No",
            "YW5nZU50Zl9JRBCEAhIdChhDU19Wb2ljZURpYWxvZ0luZm9SZXFfSUQQhQIS",
            "HQoYU0NfVm9pY2VEaWFsb2dJbmZvQWNrX0lEEIYCEh0KGFNDX1ZvaWNlRGlh",
            "bG9nSW5mb050Zl9JRBCHAhIhChxDU19SZWN2Vm9pY2VEaWFsb2dJbmZvUmVx",
            "X0lEEIgCEiEKHFNDX1JlY3ZWb2ljZURpYWxvZ0luZm9BY2tfSUQQiQISHQoY",
            "U0NfVm9pY2VEaWFsb2dUZXh0TnRmX0lEEIoCEiEKHENTX1JlY3ZWb2ljZURp",
            "YWxvZ1RleHRSZXFfSUQQiwISIQocU0NfUmVjdlZvaWNlRGlhbG9nVGV4dEFj",
            "a19JRBCMAhIlCiBDU19HZXRSZWNvbm5lY3RWb2ljZURpYWxvZ1JlcV9JRBCN",
            "AhIlCiBTQ19HZXRSZWNvbm5lY3RWb2ljZURpYWxvZ0Fja19JRBCOAhIlCiBD",
            "U19Gb3JiaWRWb2ljZURpYWxvZ0NoYW5nZVJlcV9JRBCWAhIlCiBTQ19Gb3Ji",
            "aWRWb2ljZURpYWxvZ0NoYW5nZUFja19JRBCXAhIdChhDU19Wb2ljZURpYWxv",
            "Z1RpY2tSZXFfSUQQmAISHQoYU0NfVm9pY2VEaWFsb2dUaWNrQWNrX0lEEJkC",
            "Eh0KGFNDX1ZvaWNlRGlhbG9nVGlja050Zl9JRBCaAhIgChtTQ19Wb2ljZURp",
            "YWxvZ1RpbWVvdXROdGZfSUQQmwISFQoQQ1NfTGVhdmVHU1JlcV9JRBCcAhIV",
            "ChBTQ19MZWF2ZUdTQWNrX0lEEJ0CEiEKHENTX1F1ZXJ5UG9wdWxhcml0eUxp",
            "c3RSZXFfSUQQngISIQocU0NfUXVlcnlQb3B1bGFyaXR5TGlzdEFja19JRBCf",
            "AhIbChZTU19BdmF0YXJDb21tYW5kUmVxX0lEEOkHEhsKFlNTX0F2YXRhckNv",
            "bW1hbmRBY2tfSUQQ6gcSGwoWU0NfUm9sZUluc3RDaGF0UHVzaF9JRBDrBxIb",
            "ChZDU19BdmF0YXJDb21tYW5kUmVxX0lEEOwHEhsKFlNDX0F2YXRhckNvbW1h",
            "bmRBY2tfSUQQ7QcSIAobU0NfUGxheWVyQ29tbWFuZE1vdmVQdXNoX0lEEO4H",
            "EiIKHVNDX0NvbW1hbmRRdWl0Q2hhdERyYW1hTnRmX0lEEO8HEhsKFkNTX0dl",
            "dFRhbGtpdFB1c2hSZXFfSUQQiScSGwoWU0NfR2V0VGFsa2l0UHVzaEFja19J",
            "RBCKJxIYChNDU19KdW1wU2VydmVyUmVxX0lEEPEuEhgKE1NDX0p1bXBTZXJ2",
            "ZXJBY2tfSUQQ8i4SGAoTU1NfSnVtcFNlcnZlclJlcV9JRBDzLhIYChNTU19K",
            "dW1wU2VydmVyQWNrX0lEEPQuEiMKHkNTX0NyZWF0ZURpYWxvZ1Rhc2tDYWNo",
            "ZVJlcV9JRBCQThIjCh5TQ19DcmVhdGVEaWFsb2dUYXNrQ2FjaGVBY2tfSUQQ",
            "kU4SIwoeQ1NfR2V0RGlhbG9nVGFza01vZGVMaXN0UmVxX0lEEJJOEiMKHlND",
            "X0dldERpYWxvZ1Rhc2tNb2RlTGlzdEFja19JRBCTThIZChRDU19HZXRNb2Rl",
            "SW5mb1JlcV9JRBCUThIZChRTQ19HZXRNb2RlSW5mb0Fja19JRBCVThIeChlD",
            "U19DcmVhdGVEaWFsb2dUYXNrUmVxX0lEEJZOEh4KGVNDX0NyZWF0ZURpYWxv",
            "Z1Rhc2tBY2tfSUQQl04SIQocQ1NfRGlhbG9nVGFza01zZ0hhbmRsZVJlcV9J",
            "RBCYThIhChxTQ19EaWFsb2dUYXNrTXNnSGFuZGxlQWNrX0lEEJlOEiEKHFND",
            "X1Rhc2tTdGVwQ29udGVudFB1c2hSZXFfSUQQmk4SIAobU0NfVGFza1N0ZXBT",
            "dGF0dXNQdXNoUmVxX0lEEJtOEhcKEkNTX1RyYW5zbGF0ZVJlcV9JRBCcThIX",
            "ChJTQ19UcmFuc2xhdGVBY2tfSUQQnU4SHgoZQ1NfU2NhZmZvbGRGZWVkYmFj",
            "a1JlcV9JRBCeThIeChlTQ19TY2FmZm9sZEZlZWRiYWNrQWNrX0lEEJ9OEh4K",
            "GUNTX1F1ZXN0aW9uRmVlZGJhY2tSZXFfSUQQoE4SHgoZU0NfUXVlc3Rpb25G",
            "ZWVkYmFja0Fja19JRBChThIaChVDU19UYXNrRmVlZGJhY2tSZXFfSUQQok4S",
            "GgoVU0NfVGFza0ZlZWRiYWNrQWNrX0lEEKNOEh4KGUNTX0ZyZWVUYWxrRmVl",
            "ZGJhY2tSZXFfSUQQpE4SHgoZU0NfRnJlZVRhbGtGZWVkYmFja0Fja19JRBCl",
            "ThIbChZDU19Db250ZW50MkF1ZGlvUmVxX0lEEKZOEhsKFlNDX0NvbnRlbnQy",
            "QXVkaW9BY2tfSUQQp04SHAoXQ1NfRXhpdERpYWxvZ1Rhc2tSZXFfSUQQqE4S",
            "HAoXU0NfRXhpdERpYWxvZ1Rhc2tBY2tfSUQQqU4SIAobQ1NfR2V0TXNnQXNz",
            "ZXNzUmVzdWx0UmVxX0lEEKpOEiAKG1NDX0dldE1zZ0Fzc2Vzc1Jlc3VsdEFj",
            "a19JRBCrThIeChlDU19EaWFsb2dGbG93Q3JlYXRlUmVxX0lEEKxOEh4KGVND",
            "X0RpYWxvZ0Zsb3dDcmVhdGVBY2tfSUQQrU4SIQocQ1NfRGlhbG9nRmxvd01z",
            "Z0hhbmRsZVJlcV9JRBCuThIhChxTQ19EaWFsb2dGbG93TXNnSGFuZGxlQWNr",
            "X0lEEK9OEhcKEkNTX1dvcmRUcmFuc1JlcV9JRBCwThIXChJTQ19Xb3JkVHJh",
            "bnNBY2tfSUQQsU4SJwoiQ1NfR2V0RGlhbG9nVGFza05leHRSb3VuZE1zZ1Jl",
            "cV9JRBCyThInCiJTQ19HZXREaWFsb2dUYXNrTmV4dFJvdW5kTXNnQWNrX0lE",
            "ELNOEisKJkNTX1N0cmVuZ3RoZW5EaWFsb2dUYXNrTXNnSGFuZGxlUmVxX0lE",
            "ELROEisKJlNDX1N0cmVuZ3RoZW5EaWFsb2dUYXNrTXNnSGFuZGxlQWNrX0lE",
            "ELVOEjEKLENTX0dldFN0cmVuZ3RoZW5EaWFsb2dUYXNrTmV4dFJvdW5kTXNn",
            "UmVxX0lEELZOEjEKLFNDX0dldFN0cmVuZ3RoZW5EaWFsb2dUYXNrTmV4dFJv",
            "dW5kTXNnQWNrX0lEELdOEhkKFENTX0dldFRUU0F1ZGlvUmVxX0lEELhOEhkK",
            "FFNDX0dldFRUU0F1ZGlvQWNrX0lEELlOEhwKF0NTX1N0YXJ0QVNSU3RyZWFt",
            "UmVxX0lEELpOEhwKF1NDX1N0YXJ0QVNSU3RyZWFtQWNrX0lEELtOEigKI0NT",
            "X1N0cmVuZ3RoZW5DcmVhdGVEaWFsb2dUYXNrUmVxX0lEELxOEigKI1NDX1N0",
            "cmVuZ3RoZW5DcmVhdGVEaWFsb2dUYXNrQWNrX0lEEL1OEhwKF0NTX0dldFNw",
            "ZWVjaEF1ZGlvUmVxX0lEEL5OEhwKF1NDX0dldFNwZWVjaEF1ZGlvQWNrX0lE",
            "EL9OEhoKFUNTX0dldEZhaWxlZE1zZ1JlcV9JRBDAThIaChVTQ19HZXRGYWls",
            "ZWRNc2dBQ0tfSUQQwU4SFgoRQ1NfRmxvd0hlbHBSZXFfSUQQwk4SFgoRU0Nf",
            "Rmxvd0hlbHBBY2tfSUQQw04SJAofQ1NfU3VibWl0UXVlc3Rpb25GZWVkYmFj",
            "a1JlcV9JRBDEThIkCh9TQ19TdWJtaXRRdWVzdGlvbkZlZWRiYWNrQWNrX0lE",
            "EMVOEhwKF0NTX0RpYWxvZ0Zsb3dTdG9wUmVxX0lEEMZOEhwKF1NDX0RpYWxv",
            "Z0Zsb3dTdG9wQWNrX0lEEMdOEhYKEUNTX0ZlZWRiYWNrUmVxX0lEEMhOEhYK",
            "EVNDX0ZlZWRiYWNrQWNrX0lEEMlOEhoKFUNTX0NhbmNlbEFzc2Vzc1JlcV9J",
            "RBDKThIaChVTQ19DYW5jZWxBc3Nlc3NBY2tfSUQQy04SFwoSQ1NfQ2FuY2Vs",
            "QVNSUmVxX0lEEMxOEhcKElNDX0NhbmNlbEFTUkFja19JRBDNThIgChtDU19T",
            "dWJtaXRUYXNrRmVlZGJhY2tSZXFfSUQQzk4SIAobU0NfU3VibWl0VGFza0Zl",
            "ZWRiYWNrQWNrX0lEEM9OEiMKHkNTX0dldFRUU0F1ZGlvVHJhbnNjcmlwdFJl",
            "cV9JRBDQThIjCh5TQ19HZXRUVFNBdWRpb1RyYW5zY3JpcHRBY2tfSUQQ0U4S",
            "IgodQ1NfU3dpdGNoVGFza0RpZmZpY3VsdHlSZXFfSUQQ0k4SIgodU0NfU3dp",
            "dGNoVGFza0RpZmZpY3VsdHlBY2tfSUQQ004SIwoeQ1NfQXNzZXNzV2l0aEF1",
            "ZGlvU3RyZWFtUmVxX0lEENROEiMKHlNDX0Fzc2Vzc1dpdGhBdWRpb1N0cmVh",
            "bUFja19JRBDVThIbChZDU19SZWNvbW1lbmRUYXNrUmVxX0lEENZOEhsKFlND",
            "X1JlY29tbWVuZFRhc2tBY2tfSUQQ104SIgodQ1NfVXBsb2FkVXNlclRhc2tB",
            "Y3Rpb25SZXFfSUQQ2E4SIgodU0NfVXBsb2FkVXNlclRhc2tBY3Rpb25BY2tf",
            "SUQQ2U4SGQoUQ1NfR2V0VXNlckluZm9SZXFfSUQQ2k4SGQoUU0NfR2V0VXNl",
            "ckluZm9BY2tfSUQQ204SHQoYQ1NfR2V0QXVkaW9Gb3JGbG93UmVxX0lEENxO",
            "Eh0KGFNDX0dldEF1ZGlvRm9yRmxvd0Fja19JRBDdThIZChRDU19HZXRUYXNr",
            "SW5mb1JlcV9JRBDeThIZChRTQ19HZXRUYXNrSW5mb0Fja19JRBDfThIiCh1D",
            "U19HZXRNb2RlSW5mb0ZvckNhcmVlclJlcV9JRBDgThIiCh1TQ19HZXRNb2Rl",
            "SW5mb0ZvckNhcmVlckFja19JRBDhThIcChdDU19DaGFuZ2VNYXJrSW5mb1Jl",
            "cV9JRBDiThIcChdTQ19DaGFuZ2VNYXJrSW5mb0Fja19JRBDjThIbChZDU19T",
            "YW5kVGFibGVMaXN0UmVxX0lEEOROEhsKFlNDX1NhbmRUYWJsZUxpc3RBY2tf",
            "SUQQ5U4SHQoYQ1NfUXVlcnlNZW1iZXJJbmZvUmVxX0lEEOZOEh0KGFNDX1F1",
            "ZXJ5TWVtYmVySW5mb0Fja19JRBDnThIcChdTQ19NZW1iZXJUeXBlUHVzaFJl",
            "cV9JRBDoThIcChdDU19NZW1iZXJUeXBlUHVzaEFja19JRBDpThIdChhDU19H",
            "ZXRFY29ub21pY0luZm9SZXFfSUQQ6k4SHQoYU0NfR2V0RWNvbm9taWNJbmZv",
            "QWNrX0lEEOtOEh0KGENTX0J1eURpYWxvZ1RpY2tldFJlcV9JRBDsThIdChhT",
            "Q19CdXlEaWFsb2dUaWNrZXRBY2tfSUQQ7U4SIQocQ1NfR2V0SW5Qcm9ncmVz",
            "c0RpYWxvZ1JlcV9JRBDuThIhChxTQ19HZXRJblByb2dyZXNzRGlhbG9nQWNr",
            "X0lEEO9OEiEKHFNDX05vdGlmeU1lbWJlckJlbmVmaXRSZXFfSUQQ8E4SIQoc",
            "Q1NfTm90aWZ5TWVtYmVyQmVuZWZpdEFja19JRBDxThIcChdDU19HZXRQb3BM",
            "aXN0SW5mb1JlcV9JRBDyThIcChdTQ19HZXRQb3BMaXN0SW5mb0Fja19JRBDz",
            "ThIfChpDU19TYW5kVGFibGVIb21lcGFnZVJlcV9JRBD0ThIfChpTQ19TYW5k",
            "VGFibGVIb21lcGFnZUFja19JRBD1ThIfChpDU19RdWVyeURpYWxvZ1JlY29y",
            "ZFJlcV9JRBD2ThIfChpTQ19RdWVyeURpYWxvZ1JlY29yZEFja19JRBD3ThIa",
            "ChVTU19DcmVhdGVEaWFsb2dSZXFfSUQQ+E4SGgoVU1NfQ3JlYXRlRGlhbG9n",
            "QWNrX0lEEPlOEh8KGlNTX1NlbmREaWFsb2dDb250ZW50UmVxX0lEEPpOEh8K",
            "GlNTX1NlbmREaWFsb2dDb250ZW50QWNrX0lEEPtOEhgKE0NTX0RpYWxvZ0hl",
            "bHBSZXFfSUQQ/E4SGAoTU0NfRGlhbG9nSGVscEFja19JRBD9ThIfChpDU19H",
            "ZXREaWFsb2dTY2FmZm9sZFJlcV9JRBD+ThIfChpTQ19HZXREaWFsb2dTY2Fm",
            "Zm9sZEFja19JRBD/ThInCiJDU19HZXRBdmF0YXJMaXN0QnlEaWFsb2dNb2Rl",
            "UmVxX0lEEIBPEicKIlNDX0dldEF2YXRhckxpc3RCeURpYWxvZ01vZGVBY2tf",
            "SUQQgU8SLAonQ1NfV2FybXVwUHJhY3RpY2VDcmVhdGVEaWFsb2dUYXNrUmVx",
            "X0lEEIJPEiwKJ1NDX1dhcm11cFByYWN0aWNlQ3JlYXRlRGlhbG9nVGFza0Fj",
            "a19JRBCDTxIvCipDU19XYXJtdXBQcmFjdGljZURpYWxvZ1Rhc2tNc2dIYW5k",
            "bGVSZXFfSUQQhE8SLwoqU0NfV2FybXVwUHJhY3RpY2VEaWFsb2dUYXNrTXNn",
            "SGFuZGxlQWNrX0lEEIVPEjUKMENTX0dldFdhcm11cFByYWN0aWNlRGlhbG9n",
            "VGFza05leHRSb3VuZE1zZ1JlcV9JRBCGTxI1CjBTQ19HZXRXYXJtdXBQcmFj",
            "dGljZURpYWxvZ1Rhc2tOZXh0Um91bmRNc2dBY2tfSUQQh08SGQoUQ1NfR2V0",
            "QVNSQXVkaW9SZXFfSUQQiE8SGQoUU0NfR2V0QVNSQXVkaW9BY2tfSUQQiU8S",
            "HAoXQ1NfVXBsb2FkQXNyQXVkaW9SZXFfSUQQik8SHAoXU0NfVXBsb2FkQXNy",
            "QXVkaW9BY2tfSUQQi08SIwoeQ1NfU3VibWl0U3BlYWtVc2VyQW5zd2VyUmVx",
            "X0lEEIxPEiMKHlNDX1N1Ym1pdFNwZWFrVXNlckFuc3dlckFja19JRBCNTxIg",
            "ChtDU19HZXRVc2VyQ2hhcHRlckluZm9SZXFfSUQQhFISIAobU0NfR2V0VXNl",
            "ckNoYXB0ZXJJbmZvQWNrX0lEEIVSEhkKFENTX0dldEdvYWxJbmZvUmVxX0lE",
            "EIZSEhkKFFNDX0dldEdvYWxJbmZvQWNrX0lEEIdSEiUKIENTX0dldENoYXB0",
            "ZXJSZWNvbW1lbmRMaXN0UmVxX0lEEIhSEiUKIFNDX0dldENoYXB0ZXJSZWNv",
            "bW1lbmRMaXN0QWNrX0lEEIlSEhsKFkNTX1NlbGVjdENoYXB0ZXJSZXFfSUQQ",
            "ilISGwoWU0NfU2VsZWN0Q2hhcHRlckFja19JRBCLUhIkCh9DU19HZXRDaGFw",
            "dGVyUHJvZ3Jlc3NJbmZvUmVxX0lEEIxSEiQKH1NDX0dldENoYXB0ZXJQcm9n",
            "cmVzc0luZm9BY2tfSUQQjVISHgoZQ1NfR2V0Q2hhcHRlclJld2FyZFJlcV9J",
            "RBCOUhIeChlTQ19HZXRDaGFwdGVyUmV3YXJkQWNrX0lEEI9SEiIKHUNTX0dl",
            "dExlYXJuUGF0aFBhZ2VUeXBlUmVxX0lEEJBSEiIKHVNDX0dldExlYXJuUGF0",
            "aFBhZ2VUeXBlQWNrX0lEEJFSEiIKHUNTX05vdGlmeUFuaW1hdGlvblN0YXRl",
            "UmVxX0lEEJJSEiIKHVNDX05vdGlmeUFuaW1hdGlvblN0YXRlQWNrX0lEEJNS",
            "Eh8KGkNTX0dldFVzZXJHb2FsRGV0YWlsUmVxX0lEEJRSEh8KGlNDX0dldFVz",
            "ZXJHb2FsRGV0YWlsQWNrX0lEEJVSEh0KGENTX0xlYXJuUGF0aFJld2FyZFJl",
            "cV9JRBCWUhIdChhTQ19MZWFyblBhdGhSZXdhcmRBY2tfSUQQl1ISHgoZQ1Nf",
            "RGlhbG9nU3VnZ2VzdGlvblJlcV9JRBCYUhIeChlTQ19EaWFsb2dTdWdnZXN0",
            "aW9uQWNrX0lEEJlSEh0KGENTX0RpYWxvZ1RyYW5zbGF0ZVJlcV9JRBCaUhId",
            "ChhTQ19EaWFsb2dUcmFuc2xhdGVBY2tfSUQQm1ISHwoaQ1NfR2V0QXZhdGFy",
            "VGFza0luZm9SZXFfSUQQnFISHwoaU0NfR2V0QXZhdGFyVGFza0luZm9BY2tf",
            "SUQQnVISHgoZQ1NfR2V0U3RhcnRQYWdlSW5mb1JlcV9JRBCeUhIeChlTQ19H",
            "ZXRTdGFydFBhZ2VJbmZvQWNrX0lEEJ9SEiQKH0NTX0dldFVzZXJBc3Npc3RM",
            "ZXZlbExpc3RSZXFfSUQQoFISJAofU0NfR2V0VXNlckFzc2lzdExldmVsTGlz",
            "dEFja19JRBChUhIgChtDU19TZXRVc2VyQXNzaXN0TGV2ZWxSZXFfSUQQolIS",
            "IAobU0NfU2V0VXNlckFzc2lzdExldmVsQWNrX0lEEKNSEh0KGENTX0dldFVz",
            "ZXJHb2FsTm9kZVJlcV9JRBCkUhIdChhTQ19HZXRVc2VyR29hbE5vZGVBY2tf",
            "SUQQpVISHQoYQ1NfR2V0VXNlckNvbnRhY3RzUmVxX0lEEKZSEh0KGFNDX0dl",
            "dFVzZXJDb250YWN0c0Fja19JRBCnUhIjCh5DU19BdmF0YXJGYXZvcml0ZVNl",
            "dHRpbmdSZXFfSUQQqFISIwoeU0NfQXZhdGFyRmF2b3JpdGVTZXR0aW5nQWNr",
            "X0lEEKlSEiYKIUNTX0dldFVzZXJRdWVzdGlvbkV4dHJhTGlzdFJlcV9JRBCq",
            "UhImCiFTQ19HZXRVc2VyUXVlc3Rpb25FeHRyYUxpc3RBY2tfSUQQq1ISIwoe",
            "Q1NfR2V0S25vd2xlZGdlUG9pbnRMaXN0UmVxX0lEEKxSEiMKHlNDX0dldEtu",
            "b3dsZWRnZVBvaW50TGlzdEFja19JRBCtUhIoCiNDU19HZXRLbm93bGVkZ2VQ",
            "b2ludEZyb250UGFnZVJlcV9JRBCuUhIoCiNTQ19HZXRLbm93bGVkZ2VQb2lu",
            "dEZyb250UGFnZUFja19JRBCvUhIrCiZDU19HZXRVc2VyUXVlc3Rpb25FeHRy",
            "YUZyb250UGFnZVJlcV9JRBCwUhIrCiZTQ19HZXRVc2VyUXVlc3Rpb25FeHRy",
            "YUZyb250UGFnZUFja19JRBCxUhIwCitDU19CYXRjaEdldFVzZXJRdWVzdGlv",
            "bkV4dHJhRnJvbnRQYWdlUmVxX0lEELJSEjAKK1NDX0JhdGNoR2V0VXNlclF1",
            "ZXN0aW9uRXh0cmFGcm9udFBhZ2VBY2tfSUQQs1ISJQogQ1NfUmV2aWV3VXNl",
            "clF1ZXN0aW9uRXh0cmFSZXFfSUQQtFISJQogU0NfUmV2aWV3VXNlclF1ZXN0",
            "aW9uRXh0cmFBY2tfSUQQtVISGwoWQ1NfT3BlblJld2FyZEJveFJlcV9JRBC3",
            "UhIbChZTQ19PcGVuUmV3YXJkQm94QWNrX0lEELhSEiEKHENTX0dldERpYWxv",
            "Z1NldHRsZW1lbnRSZXFfSUQQuVISIQocU0NfR2V0RGlhbG9nU2V0dGxlbWVu",
            "dEFja19JRBC6UhImCiFDU19TdGFydEJhdGNoUXVlc3Rpb25EaWFsb2dSZXFf",
            "SUQQwVISJgohU0NfU3RhcnRCYXRjaFF1ZXN0aW9uRGlhbG9nQWNrX0lEEMJS",
            "EicKIkNTX1N1Ym1pdEJhdGNoUXVlc3Rpb25EaWFsb2dSZXFfSUQQw1ISJwoi",
            "U0NfU3VibWl0QmF0Y2hRdWVzdGlvbkRpYWxvZ0Fja19JRBDEUhIaChVDU19H",
            "ZXRCb3hSZXdhcmRSZXFfSUQQu1ISGgoVU0NfR2V0Qm94UmV3YXJkQWNrX0lE",
            "ELxSEiIKHUNTX0dldFVzZXJEaWFsb2dIaXN0b3J5UmVxX0lEEL1SEiIKHVND",
            "X0dldFVzZXJEaWFsb2dIaXN0b3J5QWNrX0lEEL5SEiAKG0NTX09uYm9hcmRp",
            "bmdOZXdTdG9yeVJlcV9JRBC/UhIgChtTQ19PbmJvYXJkaW5nTmV3U3RvcnlB",
            "Y2tfSUQQwFISFwoSQ1NfR2V0UmVkUG9kUmVxX0lEEMVSEhcKElNDX0dldFJl",
            "ZFBvZEFja19JRBDGUhIZChRDU19DbGlja1JlZFBvZFJlcV9JRBDHUhIZChRT",
            "Q19DbGlja1JlZFBvZEFja19JRBDIUhInCiJDU19HZXRVc2VyVG9waWNEaWFs",
            "b2dIaXN0b3J5UmVxX0lEEMlSEicKIlNDX0dldFVzZXJUb3BpY0RpYWxvZ0hp",
            "c3RvcnlBY2tfSUQQylISIgodQ1NfQ3JlYXRlUXVlc3Rpb25EaWFsb2dSZXFf",
            "SUQQy1ISIgodU0NfQ3JlYXRlUXVlc3Rpb25EaWFsb2dBY2tfSUQQzFISHgoZ",
            "Q1NfRGVsTGVhcm5QYXRoRGF0YVJlcV9JRBDVYRIeChlTQ19EZWxMZWFyblBh",
            "dGhEYXRhQWNrX0lEENZhEiIKHUNTX0dldFF1aWNrUHJhY3RpY2VMaXN0UmVx",
            "X0lEEMllEiIKHVNDX0dldFF1aWNrUHJhY3RpY2VMaXN0QWNrX0lEEMplEiEK",
            "HENTX1N1Ym1pdFF1aWNrUHJhY3RpY2VSZXFfSUQQy2USIQocU0NfU3VibWl0",
            "UXVpY2tQcmFjdGljZUFja19JRBDMZRIkCh9DU19TdWJtaXRRdWlja1ByYWN0",
            "aWNlTmV3UmVxX0lEEM1lEiQKH1NDX1N1Ym1pdFF1aWNrUHJhY3RpY2VOZXdB",
            "Y2tfSUQQzmUSHwoaQ1NfRXhpdFF1aWNrUHJhY3RpY2VSZXFfSUQQz2USHwoa",
            "U0NfRXhpdFF1aWNrUHJhY3RpY2VBY2tfSUQQ0GUSGwoWQ1NfR2V0VXNlckNv",
            "dXJzZVJlcV9JRBDRZRIbChZTQ19HZXRVc2VyQ291cnNlQWNrX0lEENJlEhgK",
            "E0NTX1NraXBDb3Vyc2VSZXFfSUQQ02USGAoTU0NfU2tpcENvdXJzZUFja19J",
            "RBDUZRIXChJDU19SZXdhcmRCb3hSZXFfSUQQ1WUSFwoSU0NfUmV3YXJkQm94",
            "QWNrX0lEENZlEhkKFENTX0dldEJvb2tEYXRhUmVxX0lEENdlEhkKFFNDX0dl",
            "dEJvb2tEYXRhQWNrX0lEENhlEhoKFUNTX0dldFJhZGlvRGF0YVJlcV9JRBDZ",
            "ZRIaChVTQ19HZXRSYWRpb0RhdGFBY2tfSUQQ2mUSIQocQ1NfR2V0Q291cnNl",
            "U2V0dGxlbWVudFJlcV9JRBDbZRIhChxTQ19HZXRDb3Vyc2VTZXR0bGVtZW50",
            "QWNrX0lEENxlEh0KGENTX0J1eUNvdXJzZVRpY2tldFJlcV9JRBDdZRIdChhT",
            "Q19CdXlDb3Vyc2VUaWNrZXRBY2tfSUQQ3mUSGgoVQ1NfR2V0VXNlclNoZWxm",
            "UmVxX0lEEN9lEhoKFVNDX0dldFVzZXJTaGVsZkFja19JRBDgZRIeChlDU19H",
            "ZXREeW5hbWljUXBMaXN0UmVxX0lEEOFlEh4KGVNDX0dldER5bmFtaWNRcExp",
            "c3RBY2tfSUQQ4mUSHAoXU0NfVGFza1Jlc3VsdFB1c2hSZXFfSUQQn3USHAoX",
            "Q1NfVGFza1Jlc3VsdFB1c2hBY2tfSUQQoHUSHgoZQ1NfR2V0VGFza01vZGVC",
            "YXRjaFJlcV9JRBChdRIeChlTQ19HZXRUYXNrTW9kZUJhdGNoQWNrX0lEEKJ1",
            "Eh4KGUNTX0dldERhaWx5VGFza0luZm9SZXFfSUQQo3USHgoZU0NfR2V0RGFp",
            "bHlUYXNrSW5mb0Fja19JRBCkdRIYChJDU19EYXRhQmF0Y2hSZXFfSUQQ0YwB",
            "EhgKElNDX0RhdGFCYXRjaEFja19JRBDSjAESFQoPQ1NfR01Ub29sUmVxX0lE",
            "ELmUARIVCg9TQ19HTVRvb2xBY2tfSUQQupQBEiIKHENTX1RyZWVMaXN0Rm9y",
            "SG9tZXBhZ2VSZXFfSUQQpZwBEiIKHFNDX1RyZWVMaXN0Rm9ySG9tZXBhZ2VB",
            "Y2tfSUQQppwBEiQKHkNTX1RyZWVEZXRhaWxGb3JIb21lcGFnZVJlcV9JRBCn",
            "nAESJAoeU0NfVHJlZURldGFpbEZvckhvbWVwYWdlQWNrX0lEEKicARIdChdD",
            "U19HZXRVc2VyUHJvZmlsZVJlcV9JRBDBuwESIgocU0NfR2V0VXNlclByb2Zp",
            "bGVSZXNwb25zZV9JRBDCuwESJgogQ1NfU2V0VXNlclByb2ZpbGVBdXRob3Jp",
            "dHlSZXFfSUQQw7sBEicKIVNDX1NldFVzZXJQcm9maWxlQXV0aG9yaXR5UmVz",
            "cF9JRBDEuwESJgogQ1NfR2V0VXNlclByb2ZpbGVBdXRob3JpdHlSZXFfSUQQ",
            "xbsBEicKIVNDX0dldFVzZXJQcm9maWxlQXV0aG9yaXR5UmVzcF9JRBDGuwES",
            "GwoVQ1NfU2VuZEZlZWRiYWNrUmVxX0lEEMe7ARIcChZTQ19TZW5kRmVlZGJh",
            "Y2tSZXNwX0lEEMi7ARIbChVDU19HZXRNeVByb2ZpbGVSZXFfSUQQybsBEiAK",
            "GlNDX0dldE15UHJvZmlsZVJlc3BvbnNlX0lEEMq7ARIfChlDU19TZXRVc2Vy",
            "UG9ydHJhaXRzUmVxX0lEEMu7ARIfChlTQ19TZXRVc2VyUG9ydHJhaXRzQWNr",
            "X0lEEMy7ARIjCh1DU19TZXRVc2VyTGFuZ3VhZ2VMZXZlbFJlcV9JRBDNuwES",
            "IwodU0NfU2V0VXNlckxhbmd1YWdlTGV2ZWxBY2tfSUQQzrsBEh8KGUNTX0dl",
            "dFVzZXJQb3J0cmFpdHNSZXFfSUQQz7sBEh8KGVNDX0dldFVzZXJQb3J0cmFp",
            "dHNBY2tfSUQQ0LsBEiYKIENTX1NldFVzZXJWb2ljZUluZm9ybWF0aW9uUmVx",
            "X0lEENG7ARImCiBTQ19TZXRVc2VyVm9pY2VJbmZvcm1hdGlvbkFja19JRBDS",
            "uwESHQoXQ1NfU2V0VXNlckRyZXNzVXBSZXFfSUQQ07sBEh0KF1NDX1NldFVz",
            "ZXJEcmVzc1VwQWNrX0lEENS7ARIiChxDU19TZXRDaGVja2luTWlsZXN0b25l",
            "UmVxX0lEENW7ARIiChxTQ19TZXRDaGVja2luTWlsZXN0b25lQWNrX0lEENa7",
            "ARIgChpDU19HZXRDaGVja2luU3VtbWFyeVJlcV9JRBDXuwESIAoaU0NfR2V0",
            "Q2hlY2tpblN1bW1hcnlBY2tfSUQQ2LsBEicKIUNTX0dldFVzZXJDaGVja2lu",
            "UG9ydGFsRGF0YVJlcV9JRBDZuwESJwohU0NfR2V0VXNlckNoZWNraW5Qb3J0",
            "YWxEYXRhQWNrX0lEENq7ARIZChNDU19EcmF3UmV3YXJkUmVxX0lEENu7ARIZ",
            "ChNTQ19EcmF3UmV3YXJkQWNrX0lEENy7ARIYChJDU19SZWNoZWNraW5SZXFf",
            "SUQQ3bsBEhgKElNDX1JlY2hlY2tpbkFja19JRBDeuwESIQobQ1NfUmVjaGVj",
            "a2luQnlEaWFtb25kUmVxX0lEEN+7ARIhChtTQ19SZWNoZWNraW5CeURpYW1v",
            "bmRBY2tfSUQQ4LsBEh4KGENTX1JlY2hlY2tpbkJ5VGFza1JlcV9JRBDhuwES",
            "HgoYU0NfUmVjaGVja2luQnlUYXNrQWNrX0lEEOK7ARIuCihDU19HZXRVc2Vy",
            "Q2hlY2tpbkRhdGFGb3JUYXNrRmluaXNoUmVxX0lEEOO7ARIuCihTQ19HZXRV",
            "c2VyQ2hlY2tpbkRhdGFGb3JUYXNrRmluaXNoQWNrX0lEEOS7ARInCiFDU19H",
            "ZXRVc2VyUmFua2luZ1BvcnRhbERhdGFSZXFfSUQQ5bsBEicKIVNDX0dldFVz",
            "ZXJSYW5raW5nUG9ydGFsRGF0YUFja19JRBDmuwESKAoiQ1NfR2V0SW5jZW50",
            "aXZlRGF0YUZvclBvcnRhbFJlcV9JRBDnuwESKAoiU0NfR2V0SW5jZW50aXZl",
            "RGF0YUZvclBvcnRhbEFja19JRBDouwESGgoUQ1NfSm9pblJhbmtpbmdSZXFf",
            "SUQQ6bsBEhoKFFNDX0pvaW5SYW5raW5nQWNrX0lEEOq7ARIlCh9DU19HZXRV",
            "c2VyQ2hlY2tpbkNhbGVuZGFyUmVxX0lEEOu7ARIlCh9TQ19HZXRVc2VyQ2hl",
            "Y2tpbkNhbGVuZGFyQWNrX0lEEOy7ARIeChhDU19SZWZ1c2VSZWNoZWNraW5S",
            "ZXFfSUQQ7bsBEh4KGFNDX1JlZnVzZVJlY2hlY2tpbkFja19JRBDuuwESLAom",
            "Q1NfR2V0VXNlckRyZXNzVXBNZXJjaGFuZGlzZURhdGFSZXFfSUQQ77sBEiwK",
            "JlNDX0dldFVzZXJEcmVzc1VwTWVyY2hhbmRpc2VEYXRhQWNrX0lEEPC7ARIi",
            "ChxDU19HZXRHcm93dGhXZWVrbHlHaWZ0UmVxX0lEEPG7ARIiChxTQ19HZXRH",
            "cm93dGhXZWVrbHlHaWZ0QWNrX0lEEPK7ARIkCh5DU19TZXRSYW5raW5nQ2hh",
            "bmdlQ2xpY2tSZXFfSUQQ87sBEiQKHlNDX1NldFJhbmtpbmdDaGFuZ2VDbGlj",
            "a0Fja19JRBD0uwESHgoYQ1NfU2V0VXNlckhlYWRJdGVtUmVxX0lEEPW7ARIe",
            "ChhTQ19TZXRVc2VySGVhZEl0ZW1BY2tfSUQQ9rsBEiAKGkNTX0dldFVzZXJG",
            "cmllbmRMaXN0UmVxX0lEEPe7ARIgChpTQ19HZXRVc2VyRnJpZW5kTGlzdEFj",
            "a19JRBD4uwESGAoSQ1NfQWRkRnJpZW5kUmVxX0lEEPm7ARIYChJTQ19BZGRG",
            "cmllbmRBY2tfSUQQ+rsBEhsKFUNTX1JlbW92ZUZyaWVuZFJlcV9JRBD7uwES",
            "GwoVU0NfUmVtb3ZlRnJpZW5kQWNrX0lEEPy7ARInCiFDU19HZXRSZWNvbW1l",
            "bmRlZEZyaWVuZExpc3RSZXFfSUQQ/bsBEicKIVNDX0dldFJlY29tbWVuZGVk",
            "RnJpZW5kTGlzdEFja19JRBD+uwESGQoTQ1NfU2VhcmNoVXNlclJlcV9JRBD/",
            "uwESGQoTU0NfU2VhcmNoVXNlckFja19JRBCAvAESIAoaQ1NfU2VuZEdpZnRG",
            "b3JGcmllbmRSZXFfSUQQgbwBEiAKGlNDX1NlbmRHaWZ0Rm9yRnJpZW5kQWNr",
            "X0lEEIK8ARIkCh5DU19HZXRVc2VyR2lmdFBvcnRhbERhdGFSZXFfSUQQg7wB",
            "EiQKHlNDX0dldFVzZXJHaWZ0UG9ydGFsRGF0YUFja19JRBCEvAESIgocQ1Nf",
            "TWF0Y2hGcmllbmRTaGlwVGFza1JlcV9JRBCFvAESIgocU0NfTWF0Y2hGcmll",
            "bmRTaGlwVGFza0Fja19JRBCGvAESHwoZQ1NfRnJpZW5kU2hpcE5vdGlmeVJl",
            "cV9JRBCHvAESHwoZU0NfRnJpZW5kU2hpcE5vdGlmeUFja19JRBCIvAESGwoV",
            "Q1NfU2V0U2hvd1N0YXRlUmVxX0lEEIm8ARIbChVTQ19TZXRTaG93U3RhdGVB",
            "Y2tfSUQQirwBEisKJUNTX0dldEZyaWVuZFN0cmVha1JlY29tbWVuZExpc3RS",
            "ZXFfSUQQi7wBEisKJVNDX0dldEZyaWVuZFN0cmVha1JlY29tbWVuZExpc3RB",
            "Y2tfSUQQjLwBEikKI0NTX1VwZGF0ZUZyaWVuZFN0cmVha1JlbGF0aW9uUmVx",
            "X0lEEI28ARIpCiNTQ19VcGRhdGVGcmllbmRTdHJlYWtSZWxhdGlvbkFja19J",
            "RBCOvAESJAoeQ1NfR2V0RnJpZW5kU3RyZWFrUG9ydGFsUmVxX0lEEI+8ARIk",
            "Ch5TQ19HZXRGcmllbmRTdHJlYWtQb3J0YWxBY2tfSUQQkLwBEikKI0NTX0dl",
            "dEluY2VudGl2ZURhdGFGb3JFeHBsb3JlUmVxX0lEEJG8ARIpCiNTQ19HZXRJ",
            "bmNlbnRpdmVEYXRhRm9yRXhwbG9yZUFja19JRBCSvAESGwoVQ1NfU3RhcnRD",
            "b25zdW1lUmVxX0lEEJO8ARIbChVTQ19TdGFydENvbnN1bWVBY2tfSUQQlLwB",
            "EiYKIFNDX0Vjb25vbWljQ29pblNldHRsZW1lbnRQdXNoX0lEEKnDARIlCh9D",
            "U19FY29ub21pY0NvaW5HZXRCYWxhbmNlUmVxX0lEEKrDARIlCh9TQ19FY29u",
            "b21pY0NvaW5HZXRCYWxhbmNlQWNrX0lEEKvDARIaChRDU19DcmVhdGVPcmRl",
            "clJlcV9JRBCywwESGgoUU0NfQ3JlYXRlT3JkZXJBY2tfSUQQs8MBEhoKFENT",
            "X0NhbmNlbE9yZGVyUmVxX0lEELTDARIaChRTQ19DYW5jZWxPcmRlckFja19J",
            "RBC1wwESHQoXQ1NfR2V0VW5wYWlkT3JkZXJSZXFfSUQQtsMBEh0KF1NDX0dl",
            "dFVucGFpZE9yZGVyQWNrX0lEELfDARIgChpDU19WZXJpZnlSZWNlaXB0RGF0",
            "YVJlcV9JRBC4wwESIQobU0NfVmVyaWZ5UmVjZWlwdERhdGFSZXNwX0lEELnD",
            "ARIaChRTQ19Vc2VyUmV3YXJkSXRlbV9JRBC6wwESGgoUQ1NfR2V0U2hvcElu",
            "Zm9SZXFfSUQQu8MBEhsKFVNDX0dldFNob3BJbmZvUmVzcF9JRBC8wwESHgoY",
            "Q1NfUHVyY2hhc2VTdGFtaW5hUmVxX0lEEL3DARIeChhTQ19QdXJjaGFzZVN0",
            "YW1pbmFBY2tfSUQQvsMBEh0KF0NTX1BheU1lcmNoYW5kaXNlUmVxX0lEEL/D",
            "ARIeChhTQ19QYXlNZXJjaGFuZGlzZVJlc3BfSUQQwMMBEhgKEkNTX0NsaWNr",
            "V29yZFJlcV9JRBCNxAESGAoSU0NfQ2xpY2tXb3JkQWNrX0lEEI7EARIhChtD",
            "U19HZXRVc2VyQ2hlY2tpbkRhdGFSZXFfSUQQ8cQBEiEKG1NDX0dldFVzZXJD",
            "aGVja2luRGF0YUFja19JRBDyxAESIAoaQ1NfRHJhd0NoZWNraW5SZXdhcmRS",
            "ZXFfSUQQ88QBEiAKGlNDX0RyYXdDaGVja2luUmV3YXJkQWNrX0lEEPTEARIh",
            "ChtDU19SZWdpc3RlclVzZXJEZXZpY2VSZXFfSUQQ1cUBEiEKG1NDX1JlZ2lz",
            "dGVyVXNlckRldmljZUFja19JRBDWxQESHgoYQ1NfR2V0U3lzdGVtTm90aWNl",
            "UmVxX0lEENfFARIeChhTQ19HZXRTeXN0ZW1Ob3RpY2VBY2tfSUQQ2MUBEhsK",
            "FUNTX1JlZGVlbUNvdXBvblJlcV9JRBC5xgESGwoVU0NfUmVkZWVtQ291cG9u",
            "QWNrX0lEELrGARIiChxDU19HZXRTaW5nbGVDaGF0RGlhbG9nUmVxX0lEEJHL",
            "ARIiChxTQ19HZXRTaW5nbGVDaGF0RGlhbG9nQWNrX0lEEJLLARIcChZDU19R",
            "dWVyeUNoYXRMaXN0UmVxX0lEEJPLARIcChZTQ19RdWVyeUNoYXRMaXN0QWNr",
            "X0lEEJTLARIjCh1DU19RdWVyeUNoYXRNZXNzYWdlTGlzdFJlcV9JRBCVywES",
            "IwodU0NfUXVlcnlDaGF0TWVzc2FnZUxpc3RBY2tfSUQQlssBEh4KGENTX1Nl",
            "bmRDaGF0TWVzc2FnZVJlcV9JRBCXywESHgoYU0NfU2VuZENoYXRNZXNzYWdl",
            "QWNrX0lEEJjLARIaChRTQ19DaGF0TWVzc2FnZU50Zl9JRBCZywESHgoYQ1Nf",
            "VXBkYXRlQ2hhdEluZGV4UmVxX0lEEJrLARIeChhTQ19VcGRhdGVDaGF0SW5k",
            "ZXhBY2tfSUQQm8sBEiAKGkNTX1F1ZXJ5Q2hhdFVzZXJMaXN0UmVxX0lEEJzL",
            "ARIgChpTQ19RdWVyeUNoYXRVc2VyTGlzdEFja19JRBCdywESIQobU1NfR2V0",
            "U2luZ2xlQ2hhdEdyb3VwUmVxX0lEEJ7LARIhChtTU19HZXRTaW5nbGVDaGF0",
            "R3JvdXBBY2tfSUQQn8sBEh8KGVNTX0dldENoYXRHcm91cExpc3RSZXFfSUQQ",
            "oMsBEh8KGVNTX0dldENoYXRHcm91cExpc3RBY2tfSUQQocsBEhsKFUNTX0No",
            "YXRTY2FmZm9sZFJlcV9JRBCiywESGwoVU0NfQ2hhdFNjYWZmb2xkQWNrX0lE",
            "EKPLARIgChpDU19HZW5UZXN0U2luZ2xlQ2hhdFJlcV9JRBCkywESIAoaU0Nf",
            "R2VuVGVzdFNpbmdsZUNoYXRBY2tfSUQQpcsBEiQKHkNTX1F1ZXJ5VW5SZWFk",
            "TWVzc2FnZU51bVJlcV9JRBCmywESJAoeU0NfUXVlcnlVblJlYWRNZXNzYWdl",
            "TnVtQWNrX0lEEKfLARIjCh1DU19DaGF0UmVjb3JkU3RhdHVzU3luY1JlcV9J",
            "RBCoywESIwodU0NfQ2hhdFJlY29yZFN0YXR1c1N5bmNBY2tfSUQQqcsBEh8K",
            "GVNDX0NoYXRSZWNvcmRTdGF0dXNOdGZfSUQQqssBEiMKHUNTX0dldEJ1aWxk",
            "aW5nVG9waWNMaXN0UmVxX0lEEKvLARIjCh1TQ19HZXRCdWlsZGluZ1RvcGlj",
            "TGlzdEFja19JRBCsywESJQofQ1NfR2V0QXJlYUFjaGlldmVtZW50TGlzdFJl",
            "cV9JRBCtywESJQofU0NfR2V0QXJlYUFjaGlldmVtZW50TGlzdEFja19JRBCu",
            "ywESIwodQ1NfQ3JlYXRlV29ybGRTdG9yeVRhc2tSZXFfSUQQ+dIBEiMKHVND",
            "X0NyZWF0ZVdvcmxkU3RvcnlUYXNrQWNrX0lEEPrSARIfChlDU19TdGFydFdv",
            "cmxkRGlhbG9nUmVxX0lEEPvSARIfChlTQ19TdGFydFdvcmxkRGlhbG9nQWNr",
            "X0lEEPzSARIjCh1DU19GaW5pc2hXb3JsZFN0b3J5VGFza1JlcV9JRBD90gES",
            "IwodU0NfRmluaXNoV29ybGRTdG9yeVRhc2tBY2tfSUQQ/tIBEiAKGlNDX1Vz",
            "ZXJEaWFsb2dDb250ZW50TnRmX0lEEP/SARIiChxTQ19BdmF0YXJEaWFsb2dD",
            "b250ZW50TnRmX0lEEIDTARIaChRTQ19BdmF0YXJBdWRpb050Zl9JRBCB0wES",
            "JAoeU0NfQXZhdGFyRGlhbG9nVHJhbnNsYXRlTnRmX0lEEILTARIbChVTQ19B",
            "dmF0YXJBZHZpY2VOdGZfSUQQg9MBEhwKFlNDX0F2YXRhckV4YW1wbGVOdGZf",
            "SUQQhNMBEiAKGlNDX1dvcmxkU3RvcnlQcm9jZXNzTnRmX0lEEIXTARIcChZD",
            "U19EaWFsb2dTZXR0aW5nUmVxX0lEEIbTARIfChlDU19HZXRXb3JsZFJvbGVJ",
            "bmZvUmVxX0lEEOHaARIfChlTQ19HZXRXb3JsZFJvbGVJbmZvQWNrX0lEEOLa",
            "ARIeChhDU19DcmVhdGVXb3JsZFJvbGVSZXFfSUQQ49oBEh4KGFNDX0NyZWF0",
            "ZVdvcmxkUm9sZUFja19JRBDk2gESIwodQ1NfU2VuZFZlcmlmaWNhdGlvbkNv",
            "ZGVSZXFfSUQQyeIBEiQKHlNDX1NlbmRWZXJpZmljYXRpb25Db2RlUmVzcF9J",
            "RBDK4gESHgoYQ1NfR2V0QWJUZXN0UmVzdWx0UmVxX0lEELHqARIeChhTQ19H",
            "ZXRBYlRlc3RSZXN1bHRBY2tfSUQQsuoBEh8KGUNTX0dldFJlY29tbWVuZExp",
            "c3RSZXFfSUQQmfIBEiAKGlNDX0dldFJlY29tbWVuZExpc3RSZXNwX0lEEJry",
            "ARIhChtTQ19SZWNvbW1lbmRTd2l0Y2hFbnRpdHlfSUQQm/IBEhgKEkNTX0V4",
            "cGxvcmVVcE1zZ19JRBD98gESGgoUU0NfRXhwbG9yZURvd25Nc2dfSUQQ/vIB",
            "EigKIlNDX0V4cGxvcmVEb3duTXNnRm9yU2VydmVyQmFzaWNfSUQQ//IBEiYK",
            "IFNDX0V4cGxvcmVEb3duTXNnRm9ySGVhcnRiZWF0X0lEEIDzARInCiFTQ19E",
            "aWFsb2dEb3duTXNnRm9yQXZhdGFyUmVwbHlfSUQQ4/MBEjAKKlNDX0RpYWxv",
            "Z0Rvd25Nc2dGb3JBdmF0YXJSZXBseVRyYW5zbGF0ZV9JRBDk8wESKgokU0Nf",
            "RGlhbG9nRG93bk1zZ0ZvckF2YXRhclJlcGx5VFRTX0lEEOXzARIsCiZTQ19E",
            "aWFsb2dEb3duTXNnRm9yVXNlclJlcGx5RXhhbXBsZV9JRBDm8wESNQovU0Nf",
            "RGlhbG9nRG93bk1zZ0ZvclVzZXJSZXBseUV4YW1wbGVUcmFuc2xhdGVfSUQQ",
            "5/MBEi8KKVNDX0RpYWxvZ0Rvd25Nc2dGb3JVc2VyUmVwbHlFeGFtcGxlVFRT",
            "X0lEEOjzARIfChlTQ19EaWFsb2dEb3duTXNnRm9yQVNSX0lEEOnzARIkCh5T",
            "Q19EaWFsb2dEb3duTXNnRm9yQml6RXZlbnRfSUQQ6vMBEiQKHlNDX0RpYWxv",
            "Z0Rvd25Nc2dGb3JGZWVkYmFja19JRBDr8wESMAoqU0NfRGlhbG9nRG93bk1z",
            "Z0ZvclRhc2tHb2FsU3RhdHVzQ2hhbmdlX0lEEOzzARIiChxTQ19EaWFsb2dE",
            "b3duTXNnRm9yQWR2aWNlX0lEEO3zARIxCitTQ19Vc2VyU2V0dGluZ0Rvd25N",
            "c2dGb3JTYXZlVXNlclNldHRpbmdzX0lEEMX0ARIqCiRDU19VcGRhdGVBcHBz",
            "Zmx5ZXJDYWxsYmFja0RhdGFSZXFfSUQQqfUBEisKJVNDX1VwZGF0ZUFwcHNm",
            "bHllckNhbGxiYWNrRGF0YVJlc3BfSUQQqvUBEiMKHUNTX0dldEhvbWVwYWdl",
            "R3VpZGVJdGVtUmVxX0lEEKv1ARIkCh5TQ19HZXRIb21lcGFnZUd1aWRlSXRl",
            "bVJlc3BfSUQQrPUBEi0KJ1NDX1VzZXJDaGF0RG93bk1zZ0ZvclVzZXJSZWNv",
            "Z25pemluZ19JRBCN9gESLAomU0NfVXNlckNoYXREb3duTXNnRm9yVXNlclJl",
            "Y29nbml6ZWRfSUQQjvYBEjIKLFNDX1VzZXJDaGF0RG93bk1zZ0Zvck90aGVy",
            "VXNlclJlY29nbml6aW5nX0lEEI/2ARIxCitTQ19Vc2VyQ2hhdERvd25Nc2dG",
            "b3JPdGhlclVzZXJSZWNvZ25pemVkX0lEEJD2ARI1Ci9TQ19Vc2VyQ2hhdERv",
            "d25Nc2dGb3JPdGhlclVzZXJSZXBseVRyYW5zbGF0ZV9JRBCR9gESLgooU0Nf",
            "VXNlckNoYXREb3duTXNnRm9yVXNlclJlcGx5RXhhbXBsZV9JRBCS9gESNwox",
            "U0NfVXNlckNoYXREb3duTXNnRm9yVXNlclJlcGx5RXhhbXBsZVRyYW5zbGF0",
            "ZV9JRBCT9gESJgogU0NfVXNlckNoYXREb3duTXNnRm9yQml6RXZlbnRfSUQQ",
            "lPYBEiQKHlNDX01hdGNoaW5nRG93bl9NYXRjaFN0YXR1c19JRBDz9gESJgog",
            "U0NfTWF0Y2hpbmdEb3duX01hdGNoZWRSZXN1bHRfSUQQ9PYBEiQKHlNDX01h",
            "dGNoaW5nRG93bl9NYXRjaEZhaWxlZF9JRBD19gESLwopU0NfT25ib2FyZGlu",
            "Z0NoYXREb3duTXNnRm9yQXZhdGFyUmVwbHlfSUQQ1fcBEjgKMlNDX09uYm9h",
            "cmRpbmdDaGF0RG93bk1zZ0ZvckF2YXRhclJlcGx5VHJhbnNsYXRlX0lEENb3",
            "ARIyCixTQ19PbmJvYXJkaW5nQ2hhdERvd25Nc2dGb3JBdmF0YXJSZXBseVRU",
            "U19JRBDX9wESJwohU0NfT25ib2FyZGluZ0NoYXREb3duTXNnRm9yQVNSX0lE",
            "ENj3ARIsCiZTQ19PbmJvYXJkaW5nQ2hhdERvd25Nc2dGb3JCaXpFdmVudF9J",
            "RBDZ9wESLgooU0NfT25ib2FyZGluZ0NoYXREb3duTXNnRm9yU2V0dGxlbWVu",
            "dF9JRBDa9wESKwolQ1NfR2V0T25ib2FyZGluZ0NoYXRQcmVsb2FkRGF0YVJl",
            "cV9JRBDb9wESLAomU0NfR2V0T25ib2FyZGluZ0NoYXRQcmVsb2FkRGF0YVJl",
            "c3BfSUQQ3PcBEh4KGFNDX1NraXBPbmJvYXJkaW5nQ2hhdF9JRBC5+AFCFFoM",
            "Li87Z2FtZXByb3RvqgIDTXNnYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.GameMSGID), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum GameMSGID {
    /// <summary>
    ///服务器间建立连接以后上报当前服务器编号请求
    /// </summary>
    [pbr::OriginalName("SS_ServerInfoRpt_ID")] SS_ServerInfoRpt_ID = 0,
    /// <summary>
    ///rpt对应回包，收到这个回包后双边可以正常通信
    /// </summary>
    [pbr::OriginalName("SS_ServerInfoAck_ID")] SS_ServerInfoAck_ID = 1,
    /// <summary>
    ///前端和服务器间的登陆请求
    /// </summary>
    [pbr::OriginalName("CS_LoginReq_ID")] CS_LoginReq_ID = 2,
    /// <summary>
    ///登陆确认成功或者失败消息返回前端
    /// </summary>
    [pbr::OriginalName("SC_LoginAck_ID")] SC_LoginAck_ID = 3,
    /// <summary>
    ///apisix确认端登陆成功后，向world服拉取对应的角色基础信息
    /// </summary>
    [pbr::OriginalName("CS_GetRoleInfoReq_ID")] CS_GetRoleInfoReq_ID = 4,
    /// <summary>
    ///当前账号如果没有角色，则生成一个角色，返回角色基础信息（包含对应进入game_zone信息）
    /// </summary>
    [pbr::OriginalName("SC_GetRoleInfoAck_ID")] SC_GetRoleInfoAck_ID = 5,
    /// <summary>
    ///apisix获取角色信息后向对应的game_zone发送请求进入
    /// </summary>
    [pbr::OriginalName("CS_EnterGameZoneReq_ID")] CS_EnterGameZoneReq_ID = 6,
    /// <summary>
    ///进入game-zone成功后向端返回的信息
    /// </summary>
    [pbr::OriginalName("SC_EnterGameZoneAck_ID")] SC_EnterGameZoneAck_ID = 7,
    /// <summary>
    ///game-zone向world服务器拉取角色全量数据
    /// </summary>
    [pbr::OriginalName("ZW_GetRoleDataReq_ID")] ZW_GetRoleDataReq_ID = 8,
    /// <summary>
    ///world返回角色全量数据信息
    /// </summary>
    [pbr::OriginalName("WZ_GetRoleDataAck_ID")] WZ_GetRoleDataAck_ID = 9,
    /// <summary>
    ///在线角色定时数据保存或者下线数据保存
    /// </summary>
    [pbr::OriginalName("ZW_SaveRoleDataReq_ID")] ZW_SaveRoleDataReq_ID = 10,
    /// <summary>
    ///保存结果返回
    /// </summary>
    [pbr::OriginalName("WZ_SaveRoleDataAck_ID")] WZ_SaveRoleDataAck_ID = 11,
    /// <summary>
    ///退出游戏强求，前端主动发起或者apisix捕获到前端连接断开替代前端发起
    /// </summary>
    [pbr::OriginalName("CS_ExitGameReq_ID")] CS_ExitGameReq_ID = 12,
    /// <summary>
    ///退出执行结果返回
    /// </summary>
    [pbr::OriginalName("SC_ExitGameAck_ID")] SC_ExitGameAck_ID = 13,
    /// <summary>
    ///改名请求
    /// </summary>
    [pbr::OriginalName("CS_ChangeRoleNameReq_ID")] CS_ChangeRoleNameReq_ID = 14,
    /// <summary>
    ///改名结果返回
    /// </summary>
    [pbr::OriginalName("SC_ChangeRoleNameAck_ID")] SC_ChangeRoleNameAck_ID = 15,
    /// <summary>
    ///改名结果在场景内通知同步
    /// </summary>
    [pbr::OriginalName("SC_ChangeRoleNameNtf_ID")] SC_ChangeRoleNameNtf_ID = 16,
    /// <summary>
    ///踢人请求
    /// </summary>
    [pbr::OriginalName("WG_KickRoleReq_ID")] WG_KickRoleReq_ID = 17,
    /// <summary>
    ///踢人结果返回
    /// </summary>
    [pbr::OriginalName("GW_KickRoleAck_ID")] GW_KickRoleAck_ID = 18,
    /// <summary>
    ///踢gate请求
    /// </summary>
    [pbr::OriginalName("WG_KickGateReq_ID")] WG_KickGateReq_ID = 19,
    /// <summary>
    ///踢gate结果返回
    /// </summary>
    [pbr::OriginalName("GW_KickGateAck_ID")] GW_KickGateAck_ID = 20,
    /// <summary>
    ///踢game请求
    /// </summary>
    [pbr::OriginalName("WZ_KickGameReq_ID")] WZ_KickGameReq_ID = 21,
    /// <summary>
    ///踢game结果返回
    /// </summary>
    [pbr::OriginalName("ZW_KickGameAck_ID")] ZW_KickGameAck_ID = 22,
    /// <summary>
    ///心跳请求
    /// </summary>
    [pbr::OriginalName("CS_HeartBeatReq_ID")] CS_HeartBeatReq_ID = 23,
    /// <summary>
    ///心跳返回
    /// </summary>
    [pbr::OriginalName("SC_HeartBeatAck_ID")] SC_HeartBeatAck_ID = 24,
    /// <summary>
    ///创建角色请求
    /// </summary>
    [pbr::OriginalName("CS_CreateRoleReq_ID")] CS_CreateRoleReq_ID = 25,
    /// <summary>
    ///创建角色返回
    /// </summary>
    [pbr::OriginalName("SC_CreateRoleAck_ID")] SC_CreateRoleAck_ID = 26,
    /// <summary>
    ///选择角色请求
    /// </summary>
    [pbr::OriginalName("CS_SelectRoleReq_ID")] CS_SelectRoleReq_ID = 27,
    /// <summary>
    ///选择角色返回
    /// </summary>
    [pbr::OriginalName("SC_SelectRoleAck_ID")] SC_SelectRoleAck_ID = 28,
    /// <summary>
    ///学练推送消息
    /// </summary>
    [pbr::OriginalName("WG_TalkitPushInfoNtf_ID")] WG_TalkitPushInfoNtf_ID = 29,
    /// <summary>
    ///游戏内逻辑协议
    /// </summary>
    [pbr::OriginalName("SC_RoleOnlineInfoNtf_ID")] SC_RoleOnlineInfoNtf_ID = 100,
    /// <summary>
    ///角色自身场景内信息
    /// </summary>
    [pbr::OriginalName("SC_SelfInfoNtf_ID")] SC_SelfInfoNtf_ID = 101,
    /// <summary>
    ///其它角色场景内信息
    /// </summary>
    [pbr::OriginalName("SC_RoleAppearInfoNtf_ID")] SC_RoleAppearInfoNtf_ID = 102,
    /// <summary>
    ///角色场景内信息
    /// </summary>
    [pbr::OriginalName("SC_AvatarAppearInfoNtf_ID")] SC_AvatarAppearInfoNtf_ID = 103,
    /// <summary>
    ///场景内对象移动信息
    /// </summary>
    [pbr::OriginalName("SC_ObjMoveNtf_ID")] SC_ObjMoveNtf_ID = 104,
    /// <summary>
    ///场景内对象消失信息
    /// </summary>
    [pbr::OriginalName("SC_ObjDisappearNtf_ID")] SC_ObjDisappearNtf_ID = 105,
    /// <summary>
    ///角色移动请求
    /// </summary>
    [pbr::OriginalName("CS_RoleMoveReq_ID")] CS_RoleMoveReq_ID = 106,
    /// <summary>
    ///角色移动请求结果
    /// </summary>
    [pbr::OriginalName("SC_RoleMoveAck_ID")] SC_RoleMoveAck_ID = 107,
    /// <summary>
    ///角色切换地图请求，包含了跳线
    /// </summary>
    [pbr::OriginalName("CS_RoleChangeSceneReq_ID")] CS_RoleChangeSceneReq_ID = 108,
    /// <summary>
    ///角色切换地图请求结果
    /// </summary>
    [pbr::OriginalName("SC_RoleChangeSceneAck_ID")] SC_RoleChangeSceneAck_ID = 109,
    /// <summary>
    ///avatar冒泡通知
    /// </summary>
    [pbr::OriginalName("SC_BubblingInfoNtf_ID")] SC_BubblingInfoNtf_ID = 111,
    /// <summary>
    ///avatar动作通知
    /// </summary>
    [pbr::OriginalName("SC_ActionInfoNtf_ID")] SC_ActionInfoNtf_ID = 112,
    /// <summary>
    ///对avatar说话请求
    /// </summary>
    [pbr::OriginalName("CS_TalkToAvatarReq_ID")] CS_TalkToAvatarReq_ID = 113,
    /// <summary>
    ///对avatar说话请求确认
    /// </summary>
    [pbr::OriginalName("SC_TalkToAvatarAck_ID")] SC_TalkToAvatarAck_ID = 114,
    /// <summary>
    ///进入家请求
    /// </summary>
    [pbr::OriginalName("CS_GoPlayerHomeReq_ID")] CS_GoPlayerHomeReq_ID = 115,
    /// <summary>
    ///进入家请求确认
    /// </summary>
    [pbr::OriginalName("SC_GoPlayerHomeAck_ID")] SC_GoPlayerHomeAck_ID = 116,
    /// <summary>
    ///离开家请求
    /// </summary>
    [pbr::OriginalName("CS_LeavePlayerHomeReq_ID")] CS_LeavePlayerHomeReq_ID = 117,
    /// <summary>
    ///离开家请求确认
    /// </summary>
    [pbr::OriginalName("SC_LeavePlayerHomeAck_ID")] SC_LeavePlayerHomeAck_ID = 118,
    /// <summary>
    /// </summary>
    [pbr::OriginalName("GA_GetAvatarGreetReq_ID")] GA_GetAvatarGreetReq_ID = 119,
    /// <summary>
    ///获取avatar问候语请求
    /// </summary>
    [pbr::OriginalName("AG_GetAvatarGreetAck_ID")] AG_GetAvatarGreetAck_ID = 220,
    /// <summary>
    ///avatar反射推送请求
    /// </summary>
    [pbr::OriginalName("AG_AvatarReflectionPushReq_ID")] AG_AvatarReflectionPushReq_ID = 221,
    /// <summary>
    ///avatar反射推送请求
    /// </summary>
    [pbr::OriginalName("AG_AvatarReflectionPushAck_ID")] AG_AvatarReflectionPushAck_ID = 222,
    /// <summary>
    /// </summary>
    [pbr::OriginalName("CS_GetCurLineAvatarInfoReq_ID")] CS_GetCurLineAvatarInfoReq_ID = 223,
    [pbr::OriginalName("SC_GetCurLineAvatarInfoAck_ID")] SC_GetCurLineAvatarInfoAck_ID = 224,
    /// <summary>
    /// </summary>
    [pbr::OriginalName("CS_PlayerInTaskReq_ID")] CS_PlayerInTaskReq_ID = 226,
    [pbr::OriginalName("SC_PlayerInTaskAck_ID")] SC_PlayerInTaskAck_ID = 227,
    /// <summary>
    /// </summary>
    [pbr::OriginalName("SC_AvatarMoveToSelfNtf_ID")] SC_AvatarMoveToSelfNtf_ID = 228,
    /// <summary>
    /// </summary>
    [pbr::OriginalName("CS_AgreeAvatarMoveReq_ID")] CS_AgreeAvatarMoveReq_ID = 229,
    [pbr::OriginalName("SC_AgreeAvatarMoveAck_ID")] SC_AgreeAvatarMoveAck_ID = 230,
    /// <summary>
    /// </summary>
    [pbr::OriginalName("CS_GetBuildingAvatarInfoReq_ID")] CS_GetBuildingAvatarInfoReq_ID = 231,
    [pbr::OriginalName("SC_GetBuildingAvatarInfoAck_ID")] SC_GetBuildingAvatarInfoAck_ID = 232,
    [pbr::OriginalName("SS_PushPlayerLadderLevelReq_ID")] SS_PushPlayerLadderLevelReq_ID = 233,
    [pbr::OriginalName("SS_PushPlayerLadderLevelAck_ID")] SS_PushPlayerLadderLevelAck_ID = 234,
    [pbr::OriginalName("SC_PlayerLadderLevelChange_ID")] SC_PlayerLadderLevelChange_ID = 235,
    [pbr::OriginalName("ZG_BroadcastInfoNtf_ID")] ZG_BroadcastInfoNtf_ID = 236,
    [pbr::OriginalName("SC_MemberTypeChangeNtf_ID")] SC_MemberTypeChangeNtf_ID = 237,
    [pbr::OriginalName("CS_GetAvatarJobInfoReq_ID")] CS_GetAvatarJobInfoReq_ID = 238,
    [pbr::OriginalName("SC_GetAvatarJobInfoAck_ID")] SC_GetAvatarJobInfoAck_ID = 239,
    /// <summary>
    /// 用户相关配置变更通知
    /// </summary>
    [pbr::OriginalName("SS_UserLangInfoNotify_ID")] SS_UserLangInfoNotify_ID = 240,
    /// <summary>
    ///发起对话请求
    /// </summary>
    [pbr::OriginalName("CS_VoiceDialogReq_ID")] CS_VoiceDialogReq_ID = 241,
    [pbr::OriginalName("SC_VoiceDialogAck_ID")] SC_VoiceDialogAck_ID = 242,
    [pbr::OriginalName("SC_VoiceDialogNtf_ID")] SC_VoiceDialogNtf_ID = 243,
    /// <summary>
    ///请求对话响应
    /// </summary>
    [pbr::OriginalName("CS_DealVoiceDialogReq_ID")] CS_DealVoiceDialogReq_ID = 244,
    [pbr::OriginalName("SC_DealVoiceDialogAck_ID")] SC_DealVoiceDialogAck_ID = 245,
    [pbr::OriginalName("SC_DealVoiceDialogNtf_ID")] SC_DealVoiceDialogNtf_ID = 246,
    /// <summary>
    ///获取聊天信息记录列表
    /// </summary>
    [pbr::OriginalName("CS_GetVoiceDialogRecordReq_ID")] CS_GetVoiceDialogRecordReq_ID = 247,
    [pbr::OriginalName("SC_GetVoiceDialogRecordAck_ID")] SC_GetVoiceDialogRecordAck_ID = 248,
    /// <summary>
    ///头顶角色对话状态信息变更通知
    /// </summary>
    [pbr::OriginalName("SC_PlayerStateInfoNtf_ID")] SC_PlayerStateInfoNtf_ID = 249,
    /// <summary>
    ///已经跳转完场景等，通知双方正式启动对话
    /// </summary>
    [pbr::OriginalName("SC_StartVoiceDialogNtf_ID")] SC_StartVoiceDialogNtf_ID = 250,
    /// <summary>
    ///聊天状态变更请求
    /// </summary>
    [pbr::OriginalName("CS_VoiceDialogStateChangeReq_ID")] CS_VoiceDialogStateChangeReq_ID = 251,
    [pbr::OriginalName("SC_VoiceDialogStateChangeAck_ID")] SC_VoiceDialogStateChangeAck_ID = 252,
    [pbr::OriginalName("SC_VoiceDialogStateChangeNtf_ID")] SC_VoiceDialogStateChangeNtf_ID = 253,
    /// <summary>
    ///断开聊天请求
    /// </summary>
    [pbr::OriginalName("CS_StopVoiceDialogReq_ID")] CS_StopVoiceDialogReq_ID = 254,
    [pbr::OriginalName("SC_StopVoiceDialogAck_ID")] SC_StopVoiceDialogAck_ID = 255,
    [pbr::OriginalName("SC_StopVoiceDialogNtf_ID")] SC_StopVoiceDialogNtf_ID = 256,
    /// <summary>
    ///取消对话
    /// </summary>
    [pbr::OriginalName("CS_CancelVoiceDialogReq_ID")] CS_CancelVoiceDialogReq_ID = 257,
    [pbr::OriginalName("SC_CancelVoiceDialogAck_ID")] SC_CancelVoiceDialogAck_ID = 258,
    [pbr::OriginalName("SC_CancelVoiceDialogNtf_ID")] SC_CancelVoiceDialogNtf_ID = 259,
    /// <summary>
    ///聊天设置状态场景通知
    /// </summary>
    [pbr::OriginalName("SC_ForbidVoiceDialogChangeNtf_ID")] SC_ForbidVoiceDialogChangeNtf_ID = 260,
    /// <summary>
    ///信息上传&amp;通知
    /// </summary>
    [pbr::OriginalName("CS_VoiceDialogInfoReq_ID")] CS_VoiceDialogInfoReq_ID = 261,
    [pbr::OriginalName("SC_VoiceDialogInfoAck_ID")] SC_VoiceDialogInfoAck_ID = 262,
    [pbr::OriginalName("SC_VoiceDialogInfoNtf_ID")] SC_VoiceDialogInfoNtf_ID = 263,
    /// <summary>
    ///确认收到的通知信息列表
    /// </summary>
    [pbr::OriginalName("CS_RecvVoiceDialogInfoReq_ID")] CS_RecvVoiceDialogInfoReq_ID = 264,
    [pbr::OriginalName("SC_RecvVoiceDialogInfoAck_ID")] SC_RecvVoiceDialogInfoAck_ID = 265,
    /// <summary>
    ///通知当前流式文本信息
    /// </summary>
    [pbr::OriginalName("SC_VoiceDialogTextNtf_ID")] SC_VoiceDialogTextNtf_ID = 266,
    /// <summary>
    ///确认收齐当前流式文本信息
    /// </summary>
    [pbr::OriginalName("CS_RecvVoiceDialogTextReq_ID")] CS_RecvVoiceDialogTextReq_ID = 267,
    [pbr::OriginalName("SC_RecvVoiceDialogTextAck_ID")] SC_RecvVoiceDialogTextAck_ID = 268,
    /// <summary>
    ///断线重连后，端上不断请求拉取遗漏的清除数据
    /// </summary>
    [pbr::OriginalName("CS_GetReconnectVoiceDialogReq_ID")] CS_GetReconnectVoiceDialogReq_ID = 269,
    [pbr::OriginalName("SC_GetReconnectVoiceDialogAck_ID")] SC_GetReconnectVoiceDialogAck_ID = 270,
    /// <summary>
    ///设置聊天状态请求
    /// </summary>
    [pbr::OriginalName("CS_ForbidVoiceDialogChangeReq_ID")] CS_ForbidVoiceDialogChangeReq_ID = 278,
    [pbr::OriginalName("SC_ForbidVoiceDialogChangeAck_ID")] SC_ForbidVoiceDialogChangeAck_ID = 279,
    /// <summary>
    ///对话过程中的心跳
    /// </summary>
    [pbr::OriginalName("CS_VoiceDialogTickReq_ID")] CS_VoiceDialogTickReq_ID = 280,
    [pbr::OriginalName("SC_VoiceDialogTickAck_ID")] SC_VoiceDialogTickAck_ID = 281,
    [pbr::OriginalName("SC_VoiceDialogTickNtf_ID")] SC_VoiceDialogTickNtf_ID = 282,
    /// <summary>
    ///超时
    /// </summary>
    [pbr::OriginalName("SC_VoiceDialogTimeoutNtf_ID")] SC_VoiceDialogTimeoutNtf_ID = 283,
    /// <summary>
    ///小退请求
    /// </summary>
    [pbr::OriginalName("CS_LeaveGSReq_ID")] CS_LeaveGSReq_ID = 284,
    /// <summary>
    ///小退回包
    /// </summary>
    [pbr::OriginalName("SC_LeaveGSAck_ID")] SC_LeaveGSAck_ID = 285,
    /// <summary>
    /// 查询场景热度列表
    /// </summary>
    [pbr::OriginalName("CS_QueryPopularityListReq_ID")] CS_QueryPopularityListReq_ID = 286,
    [pbr::OriginalName("SC_QueryPopularityListAck_ID")] SC_QueryPopularityListAck_ID = 287,
    /// <summary>
    /// 剧本对话
    /// </summary>
    [pbr::OriginalName("SS_AvatarCommandReq_ID")] SS_AvatarCommandReq_ID = 1001,
    /// <summary>
    ///回包
    /// </summary>
    [pbr::OriginalName("SS_AvatarCommandAck_ID")] SS_AvatarCommandAck_ID = 1002,
    /// <summary>
    ///玩家或者Avatar的外显聊天信息
    /// </summary>
    [pbr::OriginalName("SC_RoleInstChatPush_ID")] SC_RoleInstChatPush_ID = 1003,
    /// <summary>
    ///发送Avatar指令给game
    /// </summary>
    [pbr::OriginalName("CS_AvatarCommandReq_ID")] CS_AvatarCommandReq_ID = 1004,
    /// <summary>
    ///回包
    /// </summary>
    [pbr::OriginalName("SC_AvatarCommandAck_ID")] SC_AvatarCommandAck_ID = 1005,
    /// <summary>
    ///通知玩家移动到某个点
    /// </summary>
    [pbr::OriginalName("SC_PlayerCommandMovePush_ID")] SC_PlayerCommandMovePush_ID = 1006,
    /// <summary>
    ///通知玩家离开Avatar对话观战模式
    /// </summary>
    [pbr::OriginalName("SC_CommandQuitChatDramaNtf_ID")] SC_CommandQuitChatDramaNtf_ID = 1007,
    /// <summary>
    /// </summary>
    [pbr::OriginalName("CS_GetTalkitPushReq_ID")] CS_GetTalkitPushReq_ID = 5001,
    [pbr::OriginalName("SC_GetTalkitPushAck_ID")] SC_GetTalkitPushAck_ID = 5002,
    /// <summary>
    /// jump server
    /// </summary>
    [pbr::OriginalName("CS_JumpServerReq_ID")] CS_JumpServerReq_ID = 6001,
    [pbr::OriginalName("SC_JumpServerAck_ID")] SC_JumpServerAck_ID = 6002,
    [pbr::OriginalName("SS_JumpServerReq_ID")] SS_JumpServerReq_ID = 6003,
    [pbr::OriginalName("SS_JumpServerAck_ID")] SS_JumpServerAck_ID = 6004,
    /// <summary>
    /// 对话协议号段：10001 ～ 15000
    /// </summary>
    [pbr::OriginalName("CS_CreateDialogTaskCacheReq_ID")] CS_CreateDialogTaskCacheReq_ID = 10000,
    /// <summary>
    ///对话创建缓存回复
    /// </summary>
    [pbr::OriginalName("SC_CreateDialogTaskCacheAck_ID")] SC_CreateDialogTaskCacheAck_ID = 10001,
    /// <summary>
    ///获取任务对话模式列表请求
    /// </summary>
    [pbr::OriginalName("CS_GetDialogTaskModeListReq_ID")] CS_GetDialogTaskModeListReq_ID = 10002,
    /// <summary>
    ///获取任务对话模式列表回复
    /// </summary>
    [pbr::OriginalName("SC_GetDialogTaskModeListAck_ID")] SC_GetDialogTaskModeListAck_ID = 10003,
    /// <summary>
    ///获取单个模式详情请求
    /// </summary>
    [pbr::OriginalName("CS_GetModeInfoReq_ID")] CS_GetModeInfoReq_ID = 10004,
    /// <summary>
    ///获取单个模式详情回复
    /// </summary>
    [pbr::OriginalName("SC_GetModeInfoAck_ID")] SC_GetModeInfoAck_ID = 10005,
    /// <summary>
    ///任务对话创建请求
    /// </summary>
    [pbr::OriginalName("CS_CreateDialogTaskReq_ID")] CS_CreateDialogTaskReq_ID = 10006,
    /// <summary>
    ///任务对话创建回复
    /// </summary>
    [pbr::OriginalName("SC_CreateDialogTaskAck_ID")] SC_CreateDialogTaskAck_ID = 10007,
    /// <summary>
    ///任务对话语音请求
    /// </summary>
    [pbr::OriginalName("CS_DialogTaskMsgHandleReq_ID")] CS_DialogTaskMsgHandleReq_ID = 10008,
    /// <summary>
    ///任务对话回复
    /// </summary>
    [pbr::OriginalName("SC_DialogTaskMsgHandleAck_ID")] SC_DialogTaskMsgHandleAck_ID = 10009,
    /// <summary>
    /// 任务对话目标数据推送
    /// </summary>
    [pbr::OriginalName("SC_TaskStepContentPushReq_ID")] SC_TaskStepContentPushReq_ID = 10010,
    /// <summary>
    /// 任务对话目标进度推送
    /// </summary>
    [pbr::OriginalName("SC_TaskStepStatusPushReq_ID")] SC_TaskStepStatusPushReq_ID = 10011,
    /// <summary>
    ///翻译请求
    /// </summary>
    [pbr::OriginalName("CS_TranslateReq_ID")] CS_TranslateReq_ID = 10012,
    /// <summary>
    ///翻译回复
    /// </summary>
    [pbr::OriginalName("SC_TranslateAck_ID")] SC_TranslateAck_ID = 10013,
    /// <summary>
    ///脚手架反馈请求
    /// </summary>
    [pbr::OriginalName("CS_ScaffoldFeedbackReq_ID")] CS_ScaffoldFeedbackReq_ID = 10014,
    /// <summary>
    ///脚手架反馈回复
    /// </summary>
    [pbr::OriginalName("SC_ScaffoldFeedbackAck_ID")] SC_ScaffoldFeedbackAck_ID = 10015,
    /// <summary>
    ///问题/提示反馈请求
    /// </summary>
    [pbr::OriginalName("CS_QuestionFeedbackReq_ID")] CS_QuestionFeedbackReq_ID = 10016,
    /// <summary>
    ///问题/提示反馈回复
    /// </summary>
    [pbr::OriginalName("SC_QuestionFeedbackAck_ID")] SC_QuestionFeedbackAck_ID = 10017,
    /// <summary>
    ///任务反馈请求
    /// </summary>
    [pbr::OriginalName("CS_TaskFeedbackReq_ID")] CS_TaskFeedbackReq_ID = 10018,
    /// <summary>
    ///任务反馈回复
    /// </summary>
    [pbr::OriginalName("SC_TaskFeedbackAck_ID")] SC_TaskFeedbackAck_ID = 10019,
    /// <summary>
    ///自由对话反馈请求
    /// </summary>
    [pbr::OriginalName("CS_FreeTalkFeedbackReq_ID")] CS_FreeTalkFeedbackReq_ID = 10020,
    /// <summary>
    ///自由对话反馈回复
    /// </summary>
    [pbr::OriginalName("SC_FreeTalkFeedbackAck_ID")] SC_FreeTalkFeedbackAck_ID = 10021,
    /// <summary>
    ///文本转语音请求
    /// </summary>
    [pbr::OriginalName("CS_Content2AudioReq_ID")] CS_Content2AudioReq_ID = 10022,
    /// <summary>
    ///文本转语音回复
    /// </summary>
    [pbr::OriginalName("SC_Content2AudioAck_ID")] SC_Content2AudioAck_ID = 10023,
    /// <summary>
    ///结束对话请求
    /// </summary>
    [pbr::OriginalName("CS_ExitDialogTaskReq_ID")] CS_ExitDialogTaskReq_ID = 10024,
    /// <summary>
    ///结束对话请求回复
    /// </summary>
    [pbr::OriginalName("SC_ExitDialogTaskAck_ID")] SC_ExitDialogTaskAck_ID = 10025,
    /// <summary>
    ///获取单句语音评测结果请求
    /// </summary>
    [pbr::OriginalName("CS_GetMsgAssessResultReq_ID")] CS_GetMsgAssessResultReq_ID = 10026,
    /// <summary>
    ///获取单句语音评测结果回复
    /// </summary>
    [pbr::OriginalName("SC_GetMsgAssessResultAck_ID")] SC_GetMsgAssessResultAck_ID = 10027,
    /// <summary>
    ///flow创建对话请求
    /// </summary>
    [pbr::OriginalName("CS_DialogFlowCreateReq_ID")] CS_DialogFlowCreateReq_ID = 10028,
    /// <summary>
    ///flow创建对话回复
    /// </summary>
    [pbr::OriginalName("SC_DialogFlowCreateAck_ID")] SC_DialogFlowCreateAck_ID = 10029,
    /// <summary>
    ///flow对话请求
    /// </summary>
    [pbr::OriginalName("CS_DialogFlowMsgHandleReq_ID")] CS_DialogFlowMsgHandleReq_ID = 10030,
    /// <summary>
    ///flow对话回复
    /// </summary>
    [pbr::OriginalName("SC_DialogFlowMsgHandleAck_ID")] SC_DialogFlowMsgHandleAck_ID = 10031,
    /// <summary>
    ///点词翻译请求
    /// </summary>
    [pbr::OriginalName("CS_WordTransReq_ID")] CS_WordTransReq_ID = 10032,
    /// <summary>
    ///点词翻译回复
    /// </summary>
    [pbr::OriginalName("SC_WordTransAck_ID")] SC_WordTransAck_ID = 10033,
    /// <summary>
    ///获取下一轮任务对话数据请求
    /// </summary>
    [pbr::OriginalName("CS_GetDialogTaskNextRoundMsgReq_ID")] CS_GetDialogTaskNextRoundMsgReq_ID = 10034,
    /// <summary>
    ///获取下一轮任务对话数据请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetDialogTaskNextRoundMsgAck_ID")] SC_GetDialogTaskNextRoundMsgAck_ID = 10035,
    /// <summary>
    ///强化任务对话语音请求
    /// </summary>
    [pbr::OriginalName("CS_StrengthenDialogTaskMsgHandleReq_ID")] CS_StrengthenDialogTaskMsgHandleReq_ID = 10036,
    /// <summary>
    ///强化任务对话回复
    /// </summary>
    [pbr::OriginalName("SC_StrengthenDialogTaskMsgHandleAck_ID")] SC_StrengthenDialogTaskMsgHandleAck_ID = 10037,
    /// <summary>
    ///获取下一轮强化任务对话数据请求
    /// </summary>
    [pbr::OriginalName("CS_GetStrengthenDialogTaskNextRoundMsgReq_ID")] CS_GetStrengthenDialogTaskNextRoundMsgReq_ID = 10038,
    /// <summary>
    ///获取下一轮强化任务对话数据请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetStrengthenDialogTaskNextRoundMsgAck_ID")] SC_GetStrengthenDialogTaskNextRoundMsgAck_ID = 10039,
    /// <summary>
    ///请求tts
    /// </summary>
    [pbr::OriginalName("CS_GetTTSAudioReq_ID")] CS_GetTTSAudioReq_ID = 10040,
    /// <summary>
    ///返回tts
    /// </summary>
    [pbr::OriginalName("SC_GetTTSAudioAck_ID")] SC_GetTTSAudioAck_ID = 10041,
    /// <summary>
    ///请求asr
    /// </summary>
    [pbr::OriginalName("CS_StartASRStreamReq_ID")] CS_StartASRStreamReq_ID = 10042,
    /// <summary>
    ///返回asr
    /// </summary>
    [pbr::OriginalName("SC_StartASRStreamAck_ID")] SC_StartASRStreamAck_ID = 10043,
    /// <summary>
    ///创建强化对话请求
    /// </summary>
    [pbr::OriginalName("CS_StrengthenCreateDialogTaskReq_ID")] CS_StrengthenCreateDialogTaskReq_ID = 10044,
    /// <summary>
    ///创建强化对话回复
    /// </summary>
    [pbr::OriginalName("SC_StrengthenCreateDialogTaskAck_ID")] SC_StrengthenCreateDialogTaskAck_ID = 10045,
    /// <summary>
    ///speech request
    /// </summary>
    [pbr::OriginalName("CS_GetSpeechAudioReq_ID")] CS_GetSpeechAudioReq_ID = 10046,
    /// <summary>
    ///speech response
    /// </summary>
    [pbr::OriginalName("SC_GetSpeechAudioAck_ID")] SC_GetSpeechAudioAck_ID = 10047,
    [pbr::OriginalName("CS_GetFailedMsgReq_ID")] CS_GetFailedMsgReq_ID = 10048,
    [pbr::OriginalName("SC_GetFailedMsgACK_ID")] SC_GetFailedMsgACK_ID = 10049,
    [pbr::OriginalName("CS_FlowHelpReq_ID")] CS_FlowHelpReq_ID = 10050,
    [pbr::OriginalName("SC_FlowHelpAck_ID")] SC_FlowHelpAck_ID = 10051,
    [pbr::OriginalName("CS_SubmitQuestionFeedbackReq_ID")] CS_SubmitQuestionFeedbackReq_ID = 10052,
    [pbr::OriginalName("SC_SubmitQuestionFeedbackAck_ID")] SC_SubmitQuestionFeedbackAck_ID = 10053,
    [pbr::OriginalName("CS_DialogFlowStopReq_ID")] CS_DialogFlowStopReq_ID = 10054,
    [pbr::OriginalName("SC_DialogFlowStopAck_ID")] SC_DialogFlowStopAck_ID = 10055,
    [pbr::OriginalName("CS_FeedbackReq_ID")] CS_FeedbackReq_ID = 10056,
    [pbr::OriginalName("SC_FeedbackAck_ID")] SC_FeedbackAck_ID = 10057,
    [pbr::OriginalName("CS_CancelAssessReq_ID")] CS_CancelAssessReq_ID = 10058,
    [pbr::OriginalName("SC_CancelAssessAck_ID")] SC_CancelAssessAck_ID = 10059,
    [pbr::OriginalName("CS_CancelASRReq_ID")] CS_CancelASRReq_ID = 10060,
    [pbr::OriginalName("SC_CancelASRAck_ID")] SC_CancelASRAck_ID = 10061,
    [pbr::OriginalName("CS_SubmitTaskFeedbackReq_ID")] CS_SubmitTaskFeedbackReq_ID = 10062,
    [pbr::OriginalName("SC_SubmitTaskFeedbackAck_ID")] SC_SubmitTaskFeedbackAck_ID = 10063,
    [pbr::OriginalName("CS_GetTTSAudioTranscriptReq_ID")] CS_GetTTSAudioTranscriptReq_ID = 10064,
    [pbr::OriginalName("SC_GetTTSAudioTranscriptAck_ID")] SC_GetTTSAudioTranscriptAck_ID = 10065,
    [pbr::OriginalName("CS_SwitchTaskDifficultyReq_ID")] CS_SwitchTaskDifficultyReq_ID = 10066,
    [pbr::OriginalName("SC_SwitchTaskDifficultyAck_ID")] SC_SwitchTaskDifficultyAck_ID = 10067,
    [pbr::OriginalName("CS_AssessWithAudioStreamReq_ID")] CS_AssessWithAudioStreamReq_ID = 10068,
    [pbr::OriginalName("SC_AssessWithAudioStreamAck_ID")] SC_AssessWithAudioStreamAck_ID = 10069,
    [pbr::OriginalName("CS_RecommendTaskReq_ID")] CS_RecommendTaskReq_ID = 10070,
    [pbr::OriginalName("SC_RecommendTaskAck_ID")] SC_RecommendTaskAck_ID = 10071,
    /// <summary>
    ///推荐卡片上报请求
    /// </summary>
    [pbr::OriginalName("CS_UploadUserTaskActionReq_ID")] CS_UploadUserTaskActionReq_ID = 10072,
    /// <summary>
    ///上报行为返回
    /// </summary>
    [pbr::OriginalName("SC_UploadUserTaskActionAck_ID")] SC_UploadUserTaskActionAck_ID = 10073,
    /// <summary>
    /// 笑舞说让我帮忙给往后延下序号  好人不留名~不用感谢 :)
    /// </summary>
    [pbr::OriginalName("CS_GetUserInfoReq_ID")] CS_GetUserInfoReq_ID = 10074,
    /// <summary>
    ///获取用户信息回复
    /// </summary>
    [pbr::OriginalName("SC_GetUserInfoAck_ID")] SC_GetUserInfoAck_ID = 10075,
    /// <summary>
    ///文本转语音请求-flow
    /// </summary>
    [pbr::OriginalName("CS_GetAudioForFlowReq_ID")] CS_GetAudioForFlowReq_ID = 10076,
    /// <summary>
    ///文本转语音回复-flow
    /// </summary>
    [pbr::OriginalName("SC_GetAudioForFlowAck_ID")] SC_GetAudioForFlowAck_ID = 10077,
    /// <summary>
    /// 获取任务信息入参
    /// </summary>
    [pbr::OriginalName("CS_GetTaskInfoReq_ID")] CS_GetTaskInfoReq_ID = 10078,
    /// <summary>
    /// 获取任务信息出参
    /// </summary>
    [pbr::OriginalName("SC_GetTaskInfoAck_ID")] SC_GetTaskInfoAck_ID = 10079,
    [pbr::OriginalName("CS_GetModeInfoForCareerReq_ID")] CS_GetModeInfoForCareerReq_ID = 10080,
    [pbr::OriginalName("SC_GetModeInfoForCareerAck_ID")] SC_GetModeInfoForCareerAck_ID = 10081,
    /// <summary>
    ///用户标识变更请求
    /// </summary>
    [pbr::OriginalName("CS_ChangeMarkInfoReq_ID")] CS_ChangeMarkInfoReq_ID = 10082,
    /// <summary>
    ///用户标识变更回复
    /// </summary>
    [pbr::OriginalName("SC_ChangeMarkInfoAck_ID")] SC_ChangeMarkInfoAck_ID = 10083,
    [pbr::OriginalName("CS_SandTableListReq_ID")] CS_SandTableListReq_ID = 10084,
    [pbr::OriginalName("SC_SandTableListAck_ID")] SC_SandTableListAck_ID = 10085,
    [pbr::OriginalName("CS_QueryMemberInfoReq_ID")] CS_QueryMemberInfoReq_ID = 10086,
    [pbr::OriginalName("SC_QueryMemberInfoAck_ID")] SC_QueryMemberInfoAck_ID = 10087,
    [pbr::OriginalName("SC_MemberTypePushReq_ID")] SC_MemberTypePushReq_ID = 10088,
    [pbr::OriginalName("CS_MemberTypePushAck_ID")] CS_MemberTypePushAck_ID = 10089,
    /// <summary>
    /// 获取用户经济详情请求
    /// </summary>
    [pbr::OriginalName("CS_GetEconomicInfoReq_ID")] CS_GetEconomicInfoReq_ID = 10090,
    /// <summary>
    /// 获取用户经济详情回复
    /// </summary>
    [pbr::OriginalName("SC_GetEconomicInfoAck_ID")] SC_GetEconomicInfoAck_ID = 10091,
    /// <summary>
    /// 买票请求
    /// </summary>
    [pbr::OriginalName("CS_BuyDialogTicketReq_ID")] CS_BuyDialogTicketReq_ID = 10092,
    /// <summary>
    /// 买票回复
    /// </summary>
    [pbr::OriginalName("SC_BuyDialogTicketAck_ID")] SC_BuyDialogTicketAck_ID = 10093,
    /// <summary>
    /// 获取进行中对话数据请求
    /// </summary>
    [pbr::OriginalName("CS_GetInProgressDialogReq_ID")] CS_GetInProgressDialogReq_ID = 10094,
    /// <summary>
    /// 获取进行中对话数据回复
    /// </summary>
    [pbr::OriginalName("SC_GetInProgressDialogAck_ID")] SC_GetInProgressDialogAck_ID = 10095,
    [pbr::OriginalName("SC_NotifyMemberBenefitReq_ID")] SC_NotifyMemberBenefitReq_ID = 10096,
    [pbr::OriginalName("CS_NotifyMemberBenefitAck_ID")] CS_NotifyMemberBenefitAck_ID = 10097,
    [pbr::OriginalName("CS_GetPopListInfoReq_ID")] CS_GetPopListInfoReq_ID = 10098,
    [pbr::OriginalName("SC_GetPopListInfoAck_ID")] SC_GetPopListInfoAck_ID = 10099,
    [pbr::OriginalName("CS_SandTableHomepageReq_ID")] CS_SandTableHomepageReq_ID = 10100,
    [pbr::OriginalName("SC_SandTableHomepageAck_ID")] SC_SandTableHomepageAck_ID = 10101,
    [pbr::OriginalName("CS_QueryDialogRecordReq_ID")] CS_QueryDialogRecordReq_ID = 10102,
    [pbr::OriginalName("SC_QueryDialogRecordAck_ID")] SC_QueryDialogRecordAck_ID = 10103,
    [pbr::OriginalName("SS_CreateDialogReq_ID")] SS_CreateDialogReq_ID = 10104,
    [pbr::OriginalName("SS_CreateDialogAck_ID")] SS_CreateDialogAck_ID = 10105,
    [pbr::OriginalName("SS_SendDialogContentReq_ID")] SS_SendDialogContentReq_ID = 10106,
    [pbr::OriginalName("SS_SendDialogContentAck_ID")] SS_SendDialogContentAck_ID = 10107,
    [pbr::OriginalName("CS_DialogHelpReq_ID")] CS_DialogHelpReq_ID = 10108,
    [pbr::OriginalName("SC_DialogHelpAck_ID")] SC_DialogHelpAck_ID = 10109,
    /// <summary>
    /// 新版脚手架请求
    /// </summary>
    [pbr::OriginalName("CS_GetDialogScaffoldReq_ID")] CS_GetDialogScaffoldReq_ID = 10110,
    /// <summary>
    /// 新版脚手架请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetDialogScaffoldAck_ID")] SC_GetDialogScaffoldAck_ID = 10111,
    /// <summary>
    /// 获取指定对话模式的avatar列表请求
    /// </summary>
    [pbr::OriginalName("CS_GetAvatarListByDialogModeReq_ID")] CS_GetAvatarListByDialogModeReq_ID = 10112,
    /// <summary>
    /// 获取指定对话模式的avatar列表响应
    /// </summary>
    [pbr::OriginalName("SC_GetAvatarListByDialogModeAck_ID")] SC_GetAvatarListByDialogModeAck_ID = 10113,
    /// <summary>
    /// WarmupPractice创建会话请求
    /// </summary>
    [pbr::OriginalName("CS_WarmupPracticeCreateDialogTaskReq_ID")] CS_WarmupPracticeCreateDialogTaskReq_ID = 10114,
    /// <summary>
    /// WarmupPractice创建会话请求
    /// </summary>
    [pbr::OriginalName("SC_WarmupPracticeCreateDialogTaskAck_ID")] SC_WarmupPracticeCreateDialogTaskAck_ID = 10115,
    /// <summary>
    /// WarmupPractice会话消息处理请求
    /// </summary>
    [pbr::OriginalName("CS_WarmupPracticeDialogTaskMsgHandleReq_ID")] CS_WarmupPracticeDialogTaskMsgHandleReq_ID = 10116,
    /// <summary>
    /// WarmupPractice会话消息处理请求
    /// </summary>
    [pbr::OriginalName("SC_WarmupPracticeDialogTaskMsgHandleAck_ID")] SC_WarmupPracticeDialogTaskMsgHandleAck_ID = 10117,
    /// <summary>
    /// WarmupPractice会话下一轮消息请求
    /// </summary>
    [pbr::OriginalName("CS_GetWarmupPracticeDialogTaskNextRoundMsgReq_ID")] CS_GetWarmupPracticeDialogTaskNextRoundMsgReq_ID = 10118,
    /// <summary>
    /// WarmupPractice会话下一轮消息请求
    /// </summary>
    [pbr::OriginalName("SC_GetWarmupPracticeDialogTaskNextRoundMsgAck_ID")] SC_GetWarmupPracticeDialogTaskNextRoundMsgAck_ID = 10119,
    /// <summary>
    /// 获取用户语音
    /// </summary>
    [pbr::OriginalName("CS_GetASRAudioReq_ID")] CS_GetASRAudioReq_ID = 10120,
    /// <summary>
    /// 获取用户语音回复
    /// </summary>
    [pbr::OriginalName("SC_GetASRAudioAck_ID")] SC_GetASRAudioAck_ID = 10121,
    [pbr::OriginalName("CS_UploadAsrAudioReq_ID")] CS_UploadAsrAudioReq_ID = 10122,
    [pbr::OriginalName("SC_UploadAsrAudioAck_ID")] SC_UploadAsrAudioAck_ID = 10123,
    [pbr::OriginalName("CS_SubmitSpeakUserAnswerReq_ID")] CS_SubmitSpeakUserAnswerReq_ID = 10124,
    [pbr::OriginalName("SC_SubmitSpeakUserAnswerAck_ID")] SC_SubmitSpeakUserAnswerAck_ID = 10125,
    /// <summary>
    /// 学习路径 号段10500起
    /// </summary>
    [pbr::OriginalName("CS_GetUserChapterInfoReq_ID")] CS_GetUserChapterInfoReq_ID = 10500,
    /// <summary>
    /// 获取用户的chapter详情请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetUserChapterInfoAck_ID")] SC_GetUserChapterInfoAck_ID = 10501,
    /// <summary>
    /// 获取当前goal详情请求
    /// </summary>
    [pbr::OriginalName("CS_GetGoalInfoReq_ID")] CS_GetGoalInfoReq_ID = 10502,
    /// <summary>
    /// 获取当前goal详情回复
    /// </summary>
    [pbr::OriginalName("SC_GetGoalInfoAck_ID")] SC_GetGoalInfoAck_ID = 10503,
    /// <summary>
    /// 获取chapter推荐列表请求
    /// </summary>
    [pbr::OriginalName("CS_GetChapterRecommendListReq_ID")] CS_GetChapterRecommendListReq_ID = 10504,
    /// <summary>
    /// 获取chapter推荐列表回复
    /// </summary>
    [pbr::OriginalName("SC_GetChapterRecommendListAck_ID")] SC_GetChapterRecommendListAck_ID = 10505,
    /// <summary>
    /// 选择chapter请求
    /// </summary>
    [pbr::OriginalName("CS_SelectChapterReq_ID")] CS_SelectChapterReq_ID = 10506,
    /// <summary>
    /// 选择chapter请求回复
    /// </summary>
    [pbr::OriginalName("SC_SelectChapterAck_ID")] SC_SelectChapterAck_ID = 10507,
    /// <summary>
    /// 获取当前用户chapter进展数据请求
    /// </summary>
    [pbr::OriginalName("CS_GetChapterProgressInfoReq_ID")] CS_GetChapterProgressInfoReq_ID = 10508,
    /// <summary>
    /// 获取当前用户chapter进展数据回复
    /// </summary>
    [pbr::OriginalName("SC_GetChapterProgressInfoAck_ID")] SC_GetChapterProgressInfoAck_ID = 10509,
    /// <summary>
    /// chapter奖励发放请求
    /// </summary>
    [pbr::OriginalName("CS_GetChapterRewardReq_ID")] CS_GetChapterRewardReq_ID = 10510,
    /// <summary>
    /// chapter奖励发放回复
    /// </summary>
    [pbr::OriginalName("SC_GetChapterRewardAck_ID")] SC_GetChapterRewardAck_ID = 10511,
    /// <summary>
    /// 获取学习路径当前页面类型请求
    /// </summary>
    [pbr::OriginalName("CS_GetLearnPathPageTypeReq_ID")] CS_GetLearnPathPageTypeReq_ID = 10512,
    /// <summary>
    /// 获取学习路径当前页面类型回复
    /// </summary>
    [pbr::OriginalName("SC_GetLearnPathPageTypeAck_ID")] SC_GetLearnPathPageTypeAck_ID = 10513,
    /// <summary>
    /// 动效完成态通知请求
    /// </summary>
    [pbr::OriginalName("CS_NotifyAnimationStateReq_ID")] CS_NotifyAnimationStateReq_ID = 10514,
    /// <summary>
    /// 动效完成态通知请求回复
    /// </summary>
    [pbr::OriginalName("SC_NotifyAnimationStateAck_ID")] SC_NotifyAnimationStateAck_ID = 10515,
    /// <summary>
    /// 获取用户goal/story详情页请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserGoalDetailReq_ID")] CS_GetUserGoalDetailReq_ID = 10516,
    /// <summary>
    /// 获取用户goal/story详情页请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetUserGoalDetailAck_ID")] SC_GetUserGoalDetailAck_ID = 10517,
    /// <summary>
    /// 学习路径奖励发放请求
    /// </summary>
    [pbr::OriginalName("CS_LearnPathRewardReq_ID")] CS_LearnPathRewardReq_ID = 10518,
    /// <summary>
    /// 学习路径奖励发放请求回复
    /// </summary>
    [pbr::OriginalName("SC_LearnPathRewardAck_ID")] SC_LearnPathRewardAck_ID = 10519,
    /// <summary>
    /// 对话suggestion请求（综合）
    /// </summary>
    [pbr::OriginalName("CS_DialogSuggestionReq_ID")] CS_DialogSuggestionReq_ID = 10520,
    /// <summary>
    /// 对话suggestion请求回复（综合）
    /// </summary>
    [pbr::OriginalName("SC_DialogSuggestionAck_ID")] SC_DialogSuggestionAck_ID = 10521,
    /// <summary>
    /// 对话翻译请求
    /// </summary>
    [pbr::OriginalName("CS_DialogTranslateReq_ID")] CS_DialogTranslateReq_ID = 10522,
    /// <summary>
    /// 对话翻译请求回复
    /// </summary>
    [pbr::OriginalName("SC_DialogTranslateAck_ID")] SC_DialogTranslateAck_ID = 10523,
    /// <summary>
    /// 获取avatar任务详情请求
    /// </summary>
    [pbr::OriginalName("CS_GetAvatarTaskInfoReq_ID")] CS_GetAvatarTaskInfoReq_ID = 10524,
    /// <summary>
    /// 获取avatar任务详情请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetAvatarTaskInfoAck_ID")] SC_GetAvatarTaskInfoAck_ID = 10525,
    /// <summary>
    /// 获取对话起始页详情请求
    /// </summary>
    [pbr::OriginalName("CS_GetStartPageInfoReq_ID")] CS_GetStartPageInfoReq_ID = 10526,
    /// <summary>
    /// 获取对话起始页详情请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetStartPageInfoAck_ID")] SC_GetStartPageInfoAck_ID = 10527,
    /// <summary>
    /// 获取用户援助等级列表请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserAssistLevelListReq_ID")] CS_GetUserAssistLevelListReq_ID = 10528,
    /// <summary>
    /// 获取用户援助等级列表请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetUserAssistLevelListAck_ID")] SC_GetUserAssistLevelListAck_ID = 10529,
    /// <summary>
    /// 设置用户援助等级请求
    /// </summary>
    [pbr::OriginalName("CS_SetUserAssistLevelReq_ID")] CS_SetUserAssistLevelReq_ID = 10530,
    /// <summary>
    /// 设置用户援助等级请求回复
    /// </summary>
    [pbr::OriginalName("SC_SetUserAssistLevelAck_ID")] SC_SetUserAssistLevelAck_ID = 10531,
    /// <summary>
    /// 获取goal详情 路径平铺请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserGoalNodeReq_ID")] CS_GetUserGoalNodeReq_ID = 10532,
    /// <summary>
    /// 获取goal详情 路径平铺请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetUserGoalNodeAck_ID")] SC_GetUserGoalNodeAck_ID = 10533,
    /// <summary>
    /// 用户通讯录请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserContactsReq_ID")] CS_GetUserContactsReq_ID = 10534,
    /// <summary>
    /// 用户通讯录请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetUserContactsAck_ID")] SC_GetUserContactsAck_ID = 10535,
    /// <summary>
    /// 收藏设置请求
    /// </summary>
    [pbr::OriginalName("CS_AvatarFavoriteSettingReq_ID")] CS_AvatarFavoriteSettingReq_ID = 10536,
    /// <summary>
    /// 收藏设置请求回复
    /// </summary>
    [pbr::OriginalName("SC_AvatarFavoriteSettingAck_ID")] SC_AvatarFavoriteSettingAck_ID = 10537,
    [pbr::OriginalName("CS_GetUserQuestionExtraListReq_ID")] CS_GetUserQuestionExtraListReq_ID = 10538,
    [pbr::OriginalName("SC_GetUserQuestionExtraListAck_ID")] SC_GetUserQuestionExtraListAck_ID = 10539,
    [pbr::OriginalName("CS_GetKnowledgePointListReq_ID")] CS_GetKnowledgePointListReq_ID = 10540,
    [pbr::OriginalName("SC_GetKnowledgePointListAck_ID")] SC_GetKnowledgePointListAck_ID = 10541,
    [pbr::OriginalName("CS_GetKnowledgePointFrontPageReq_ID")] CS_GetKnowledgePointFrontPageReq_ID = 10542,
    [pbr::OriginalName("SC_GetKnowledgePointFrontPageAck_ID")] SC_GetKnowledgePointFrontPageAck_ID = 10543,
    [pbr::OriginalName("CS_GetUserQuestionExtraFrontPageReq_ID")] CS_GetUserQuestionExtraFrontPageReq_ID = 10544,
    [pbr::OriginalName("SC_GetUserQuestionExtraFrontPageAck_ID")] SC_GetUserQuestionExtraFrontPageAck_ID = 10545,
    [pbr::OriginalName("CS_BatchGetUserQuestionExtraFrontPageReq_ID")] CS_BatchGetUserQuestionExtraFrontPageReq_ID = 10546,
    [pbr::OriginalName("SC_BatchGetUserQuestionExtraFrontPageAck_ID")] SC_BatchGetUserQuestionExtraFrontPageAck_ID = 10547,
    [pbr::OriginalName("CS_ReviewUserQuestionExtraReq_ID")] CS_ReviewUserQuestionExtraReq_ID = 10548,
    [pbr::OriginalName("SC_ReviewUserQuestionExtraAck_ID")] SC_ReviewUserQuestionExtraAck_ID = 10549,
    /// <summary>
    /// 打开路径上编排的宝箱节点 新
    /// </summary>
    [pbr::OriginalName("CS_OpenRewardBoxReq_ID")] CS_OpenRewardBoxReq_ID = 10551,
    /// <summary>
    /// 打开路径上编排的宝箱节点 新
    /// </summary>
    [pbr::OriginalName("SC_OpenRewardBoxAck_ID")] SC_OpenRewardBoxAck_ID = 10552,
    /// <summary>
    ///获取对话结算数据请求
    /// </summary>
    [pbr::OriginalName("CS_GetDialogSettlementReq_ID")] CS_GetDialogSettlementReq_ID = 10553,
    /// <summary>
    ///获取对话结算数据请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetDialogSettlementAck_ID")] SC_GetDialogSettlementAck_ID = 10554,
    /// <summary>
    ///开始批量问答对话请求
    /// </summary>
    [pbr::OriginalName("CS_StartBatchQuestionDialogReq_ID")] CS_StartBatchQuestionDialogReq_ID = 10561,
    /// <summary>
    ///开始批量问答对话请求回复
    /// </summary>
    [pbr::OriginalName("SC_StartBatchQuestionDialogAck_ID")] SC_StartBatchQuestionDialogAck_ID = 10562,
    /// <summary>
    ///提交批量问答对话请求
    /// </summary>
    [pbr::OriginalName("CS_SubmitBatchQuestionDialogReq_ID")] CS_SubmitBatchQuestionDialogReq_ID = 10563,
    /// <summary>
    ///提交批量问答对话请求回复
    /// </summary>
    [pbr::OriginalName("SC_SubmitBatchQuestionDialogAck_ID")] SC_SubmitBatchQuestionDialogAck_ID = 10564,
    /// <summary>
    /// 宝箱领取请求
    /// </summary>
    [pbr::OriginalName("CS_GetBoxRewardReq_ID")] CS_GetBoxRewardReq_ID = 10555,
    /// <summary>
    /// 宝箱领取请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetBoxRewardAck_ID")] SC_GetBoxRewardAck_ID = 10556,
    /// <summary>
    /// 获取用户聊天记录请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserDialogHistoryReq_ID")] CS_GetUserDialogHistoryReq_ID = 10557,
    /// <summary>
    /// 获取用户聊天记录请求回复
    /// </summary>
    [pbr::OriginalName("SC_GetUserDialogHistoryAck_ID")] SC_GetUserDialogHistoryAck_ID = 10558,
    /// <summary>
    /// Onboarding新goal
    /// </summary>
    [pbr::OriginalName("CS_OnboardingNewStoryReq_ID")] CS_OnboardingNewStoryReq_ID = 10559,
    /// <summary>
    /// Onboarding新goal 回复
    /// </summary>
    [pbr::OriginalName("SC_OnboardingNewStoryAck_ID")] SC_OnboardingNewStoryAck_ID = 10560,
    [pbr::OriginalName("CS_GetRedPodReq_ID")] CS_GetRedPodReq_ID = 10565,
    [pbr::OriginalName("SC_GetRedPodAck_ID")] SC_GetRedPodAck_ID = 10566,
    [pbr::OriginalName("CS_ClickRedPodReq_ID")] CS_ClickRedPodReq_ID = 10567,
    [pbr::OriginalName("SC_ClickRedPodAck_ID")] SC_ClickRedPodAck_ID = 10568,
    /// <summary>
    /// 获取topic对话历史消息请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserTopicDialogHistoryReq_ID")] CS_GetUserTopicDialogHistoryReq_ID = 10569,
    /// <summary>
    /// 获取topic对话历史消息回复
    /// </summary>
    [pbr::OriginalName("SC_GetUserTopicDialogHistoryAck_ID")] SC_GetUserTopicDialogHistoryAck_ID = 10570,
    /// <summary>
    /// 创建问题对话请求
    /// </summary>
    [pbr::OriginalName("CS_CreateQuestionDialogReq_ID")] CS_CreateQuestionDialogReq_ID = 10571,
    /// <summary>
    /// 创建问题对话回复
    /// </summary>
    [pbr::OriginalName("SC_CreateQuestionDialogAck_ID")] SC_CreateQuestionDialogAck_ID = 10572,
    /// <summary>
    /// GM工具协议号段：12501 ～ 13000
    /// </summary>
    [pbr::OriginalName("CS_DelLearnPathDataReq_ID")] CS_DelLearnPathDataReq_ID = 12501,
    /// <summary>
    /// 删除学习路径数据请求回复
    /// </summary>
    [pbr::OriginalName("SC_DelLearnPathDataAck_ID")] SC_DelLearnPathDataAck_ID = 12502,
    /// <summary>
    /// 快速练习协议号段：13001 ～ 15000
    /// </summary>
    [pbr::OriginalName("CS_GetQuickPracticeListReq_ID")] CS_GetQuickPracticeListReq_ID = 13001,
    /// <summary>
    ///获取快速练习列表回复
    /// </summary>
    [pbr::OriginalName("SC_GetQuickPracticeListAck_ID")] SC_GetQuickPracticeListAck_ID = 13002,
    /// <summary>
    ///提交快速练习请求
    /// </summary>
    [pbr::OriginalName("CS_SubmitQuickPracticeReq_ID")] CS_SubmitQuickPracticeReq_ID = 13003,
    /// <summary>
    ///提交快速练习回复
    /// </summary>
    [pbr::OriginalName("SC_SubmitQuickPracticeAck_ID")] SC_SubmitQuickPracticeAck_ID = 13004,
    /// <summary>
    ///提交快速练习请求(新)
    /// </summary>
    [pbr::OriginalName("CS_SubmitQuickPracticeNewReq_ID")] CS_SubmitQuickPracticeNewReq_ID = 13005,
    /// <summary>
    ///提交快速练习回复(新)
    /// </summary>
    [pbr::OriginalName("SC_SubmitQuickPracticeNewAck_ID")] SC_SubmitQuickPracticeNewAck_ID = 13006,
    [pbr::OriginalName("CS_ExitQuickPracticeReq_ID")] CS_ExitQuickPracticeReq_ID = 13007,
    [pbr::OriginalName("SC_ExitQuickPracticeAck_ID")] SC_ExitQuickPracticeAck_ID = 13008,
    /// <summary>
    /// 获取用户课程请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserCourseReq_ID")] CS_GetUserCourseReq_ID = 13009,
    /// <summary>
    /// 获取用户课程回复
    /// </summary>
    [pbr::OriginalName("SC_GetUserCourseAck_ID")] SC_GetUserCourseAck_ID = 13010,
    /// <summary>
    /// 跳过课程请求
    /// </summary>
    [pbr::OriginalName("CS_SkipCourseReq_ID")] CS_SkipCourseReq_ID = 13011,
    /// <summary>
    /// 跳过课程回复
    /// </summary>
    [pbr::OriginalName("SC_SkipCourseAck_ID")] SC_SkipCourseAck_ID = 13012,
    /// <summary>
    /// 学习路径奖励发放请求
    /// </summary>
    [pbr::OriginalName("CS_RewardBoxReq_ID")] CS_RewardBoxReq_ID = 13013,
    /// <summary>
    /// 学习路径奖励发放回复
    /// </summary>
    [pbr::OriginalName("SC_RewardBoxAck_ID")] SC_RewardBoxAck_ID = 13014,
    /// <summary>
    /// 获取书籍数据请求
    /// </summary>
    [pbr::OriginalName("CS_GetBookDataReq_ID")] CS_GetBookDataReq_ID = 13015,
    /// <summary>
    /// 获取书籍数据回复
    /// </summary>
    [pbr::OriginalName("SC_GetBookDataAck_ID")] SC_GetBookDataAck_ID = 13016,
    /// <summary>
    /// 获取耳机数据请求
    /// </summary>
    [pbr::OriginalName("CS_GetRadioDataReq_ID")] CS_GetRadioDataReq_ID = 13017,
    /// <summary>
    /// 获取耳机数据回复
    /// </summary>
    [pbr::OriginalName("SC_GetRadioDataAck_ID")] SC_GetRadioDataAck_ID = 13018,
    /// <summary>
    /// 获取course结算数据请求
    /// </summary>
    [pbr::OriginalName("CS_GetCourseSettlementReq_ID")] CS_GetCourseSettlementReq_ID = 13019,
    /// <summary>
    /// 获取course结算数据回复
    /// </summary>
    [pbr::OriginalName("SC_GetCourseSettlementAck_ID")] SC_GetCourseSettlementAck_ID = 13020,
    /// <summary>
    /// 买票请求
    /// </summary>
    [pbr::OriginalName("CS_BuyCourseTicketReq_ID")] CS_BuyCourseTicketReq_ID = 13021,
    /// <summary>
    /// 买票回复
    /// </summary>
    [pbr::OriginalName("SC_BuyCourseTicketAck_ID")] SC_BuyCourseTicketAck_ID = 13022,
    /// <summary>
    /// 获取用户书架请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserShelfReq_ID")] CS_GetUserShelfReq_ID = 13023,
    /// <summary>
    /// 获取用户书架回复
    /// </summary>
    [pbr::OriginalName("SC_GetUserShelfAck_ID")] SC_GetUserShelfAck_ID = 13024,
    /// <summary>
    /// 获取动态题目请求
    /// </summary>
    [pbr::OriginalName("CS_GetDynamicQpListReq_ID")] CS_GetDynamicQpListReq_ID = 13025,
    /// <summary>
    /// 获取动态题目回复
    /// </summary>
    [pbr::OriginalName("SC_GetDynamicQpListAck_ID")] SC_GetDynamicQpListAck_ID = 13026,
    /// <summary>
    /// 任务协议号段：15001 ～ 19000
    ///	CS_TaskModeRecordListReq_ID = 15001;//任务模式列表请求
    ///	SC_TaskModeRecordListAck_ID = 15002;//任务模式列表返回
    ///	CS_TaskNextTurnReq_ID = 15003;//任务下一轮请求
    ///	SC_TaskNextTurnAck_ID = 15004;//任务下一轮返回
    ///	CS_TaskCompleteReq_ID = 15005;//任务完成请求
    ///	SC_TaskCompleteAck_ID = 15006;//任务完成返回
    /// </summary>
    [pbr::OriginalName("SC_TaskResultPushReq_ID")] SC_TaskResultPushReq_ID = 15007,
    /// <summary>
    ///任务结果推送
    /// </summary>
    [pbr::OriginalName("CS_TaskResultPushAck_ID")] CS_TaskResultPushAck_ID = 15008,
    /// <summary>
    ///任务模式批量请求
    /// </summary>
    [pbr::OriginalName("CS_GetTaskModeBatchReq_ID")] CS_GetTaskModeBatchReq_ID = 15009,
    /// <summary>
    ///任务模式批量返回
    /// </summary>
    [pbr::OriginalName("SC_GetTaskModeBatchAck_ID")] SC_GetTaskModeBatchAck_ID = 15010,
    /// <summary>
    ///每日任务信息请求
    /// </summary>
    [pbr::OriginalName("CS_GetDailyTaskInfoReq_ID")] CS_GetDailyTaskInfoReq_ID = 15011,
    /// <summary>
    ///每日任务信息返回
    /// </summary>
    [pbr::OriginalName("SC_GetDailyTaskInfoAck_ID")] SC_GetDailyTaskInfoAck_ID = 15012,
    /// <summary>
    /// center模块协议号段：18001～19000
    /// </summary>
    [pbr::OriginalName("CS_DataBatchReq_ID")] CS_DataBatchReq_ID = 18001,
    /// <summary>
    /// 数据批量请求回复
    /// </summary>
    [pbr::OriginalName("SC_DataBatchAck_ID")] SC_DataBatchAck_ID = 18002,
    /// <summary>
    /// GM工具协议号段：19001～20000
    /// </summary>
    [pbr::OriginalName("CS_GMToolReq_ID")] CS_GMToolReq_ID = 19001,
    /// <summary>
    ///gm测试设置回复
    /// </summary>
    [pbr::OriginalName("SC_GMToolAck_ID")] SC_GMToolAck_ID = 19002,
    /// <summary>
    /// 成就树协议号段：20001 ～ 25000
    /// CS_GetTreeListReq_ID = 20001;//获取成就树列表请求
    /// SC_GetTreeListAck_ID = 20002;//获取成就树列表返回
    /// CS_GetTreeDetailReq_ID = 20003;//获取成就树详情请求
    /// SC_GetTreeDetailAck_ID = 20004;//获取成就树详情返回
    /// 20001~20004 废弃
    /// </summary>
    [pbr::OriginalName("CS_TreeListForHomepageReq_ID")] CS_TreeListForHomepageReq_ID = 20005,
    /// <summary>
    ///成就树首页列表返回
    /// </summary>
    [pbr::OriginalName("SC_TreeListForHomepageAck_ID")] SC_TreeListForHomepageAck_ID = 20006,
    /// <summary>
    ///成就树首页详情请求
    /// </summary>
    [pbr::OriginalName("CS_TreeDetailForHomepageReq_ID")] CS_TreeDetailForHomepageReq_ID = 20007,
    /// <summary>
    ///成就树首页详情返回
    /// </summary>
    [pbr::OriginalName("SC_TreeDetailForHomepageAck_ID")] SC_TreeDetailForHomepageAck_ID = 20008,
    /// <summary>
    /// 24001~24100 激励模块
    /// </summary>
    [pbr::OriginalName("CS_GetUserProfileReq_ID")] CS_GetUserProfileReq_ID = 24001,
    /// <summary>
    ///profile页详情返回
    /// </summary>
    [pbr::OriginalName("SC_GetUserProfileResponse_ID")] SC_GetUserProfileResponse_ID = 24002,
    /// <summary>
    ///设置profile页查看权限请求
    /// </summary>
    [pbr::OriginalName("CS_SetUserProfileAuthorityReq_ID")] CS_SetUserProfileAuthorityReq_ID = 24003,
    /// <summary>
    ///设置profile页查看权限返回
    /// </summary>
    [pbr::OriginalName("SC_SetUserProfileAuthorityResp_ID")] SC_SetUserProfileAuthorityResp_ID = 24004,
    /// <summary>
    ///获取profile页权限请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserProfileAuthorityReq_ID")] CS_GetUserProfileAuthorityReq_ID = 24005,
    /// <summary>
    ///获取profile页权限返回
    /// </summary>
    [pbr::OriginalName("SC_GetUserProfileAuthorityResp_ID")] SC_GetUserProfileAuthorityResp_ID = 24006,
    /// <summary>
    ///发送用户反馈请求
    /// </summary>
    [pbr::OriginalName("CS_SendFeedbackReq_ID")] CS_SendFeedbackReq_ID = 24007,
    /// <summary>
    ///发送用户反馈返回
    /// </summary>
    [pbr::OriginalName("SC_SendFeedbackResp_ID")] SC_SendFeedbackResp_ID = 24008,
    /// <summary>
    ///我的profile页请求
    /// </summary>
    [pbr::OriginalName("CS_GetMyProfileReq_ID")] CS_GetMyProfileReq_ID = 24009,
    /// <summary>
    ///我的profile页详情返回
    /// </summary>
    [pbr::OriginalName("SC_GetMyProfileResponse_ID")] SC_GetMyProfileResponse_ID = 24010,
    /// <summary>
    ///设置用户画像请求
    /// </summary>
    [pbr::OriginalName("CS_SetUserPortraitsReq_ID")] CS_SetUserPortraitsReq_ID = 24011,
    /// <summary>
    ///设置用户画像响应
    /// </summary>
    [pbr::OriginalName("SC_SetUserPortraitsAck_ID")] SC_SetUserPortraitsAck_ID = 24012,
    /// <summary>
    /// 设置用户英语等级请求
    /// </summary>
    [pbr::OriginalName("CS_SetUserLanguageLevelReq_ID")] CS_SetUserLanguageLevelReq_ID = 24013,
    /// <summary>
    /// 设置用户英语等级请求
    /// </summary>
    [pbr::OriginalName("SC_SetUserLanguageLevelAck_ID")] SC_SetUserLanguageLevelAck_ID = 24014,
    /// <summary>
    /// 获取用户画像请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserPortraitsReq_ID")] CS_GetUserPortraitsReq_ID = 24015,
    /// <summary>
    /// 获取用户画像响应
    /// </summary>
    [pbr::OriginalName("SC_GetUserPortraitsAck_ID")] SC_GetUserPortraitsAck_ID = 24016,
    [pbr::OriginalName("CS_SetUserVoiceInformationReq_ID")] CS_SetUserVoiceInformationReq_ID = 24017,
    [pbr::OriginalName("SC_SetUserVoiceInformationAck_ID")] SC_SetUserVoiceInformationAck_ID = 24018,
    [pbr::OriginalName("CS_SetUserDressUpReq_ID")] CS_SetUserDressUpReq_ID = 24019,
    [pbr::OriginalName("SC_SetUserDressUpAck_ID")] SC_SetUserDressUpAck_ID = 24020,
    /// <summary>
    ///打卡
    /// </summary>
    [pbr::OriginalName("CS_SetCheckinMilestoneReq_ID")] CS_SetCheckinMilestoneReq_ID = 24021,
    [pbr::OriginalName("SC_SetCheckinMilestoneAck_ID")] SC_SetCheckinMilestoneAck_ID = 24022,
    [pbr::OriginalName("CS_GetCheckinSummaryReq_ID")] CS_GetCheckinSummaryReq_ID = 24023,
    [pbr::OriginalName("SC_GetCheckinSummaryAck_ID")] SC_GetCheckinSummaryAck_ID = 24024,
    [pbr::OriginalName("CS_GetUserCheckinPortalDataReq_ID")] CS_GetUserCheckinPortalDataReq_ID = 24025,
    [pbr::OriginalName("SC_GetUserCheckinPortalDataAck_ID")] SC_GetUserCheckinPortalDataAck_ID = 24026,
    [pbr::OriginalName("CS_DrawRewardReq_ID")] CS_DrawRewardReq_ID = 24027,
    [pbr::OriginalName("SC_DrawRewardAck_ID")] SC_DrawRewardAck_ID = 24028,
    [pbr::OriginalName("CS_RecheckinReq_ID")] CS_RecheckinReq_ID = 24029,
    [pbr::OriginalName("SC_RecheckinAck_ID")] SC_RecheckinAck_ID = 24030,
    [pbr::OriginalName("CS_RecheckinByDiamondReq_ID")] CS_RecheckinByDiamondReq_ID = 24031,
    [pbr::OriginalName("SC_RecheckinByDiamondAck_ID")] SC_RecheckinByDiamondAck_ID = 24032,
    [pbr::OriginalName("CS_RecheckinByTaskReq_ID")] CS_RecheckinByTaskReq_ID = 24033,
    [pbr::OriginalName("SC_RecheckinByTaskAck_ID")] SC_RecheckinByTaskAck_ID = 24034,
    [pbr::OriginalName("CS_GetUserCheckinDataForTaskFinishReq_ID")] CS_GetUserCheckinDataForTaskFinishReq_ID = 24035,
    [pbr::OriginalName("SC_GetUserCheckinDataForTaskFinishAck_ID")] SC_GetUserCheckinDataForTaskFinishAck_ID = 24036,
    /// <summary>
    /// 获取用户排行榜数据请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserRankingPortalDataReq_ID")] CS_GetUserRankingPortalDataReq_ID = 24037,
    /// <summary>
    /// 获取用户排行榜数据响应
    /// </summary>
    [pbr::OriginalName("SC_GetUserRankingPortalDataAck_ID")] SC_GetUserRankingPortalDataAck_ID = 24038,
    /// <summary>
    /// 获取激励数据请求
    /// </summary>
    [pbr::OriginalName("CS_GetIncentiveDataForPortalReq_ID")] CS_GetIncentiveDataForPortalReq_ID = 24039,
    /// <summary>
    /// 获取激励数据响应
    /// </summary>
    [pbr::OriginalName("SC_GetIncentiveDataForPortalAck_ID")] SC_GetIncentiveDataForPortalAck_ID = 24040,
    /// <summary>
    /// 加入排行榜请求
    /// </summary>
    [pbr::OriginalName("CS_JoinRankingReq_ID")] CS_JoinRankingReq_ID = 24041,
    /// <summary>
    /// 加入排行榜响应
    /// </summary>
    [pbr::OriginalName("SC_JoinRankingAck_ID")] SC_JoinRankingAck_ID = 24042,
    /// <summary>
    /// 获取用户打卡日历请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserCheckinCalendarReq_ID")] CS_GetUserCheckinCalendarReq_ID = 24043,
    /// <summary>
    /// 获取用户打卡日历响应
    /// </summary>
    [pbr::OriginalName("SC_GetUserCheckinCalendarAck_ID")] SC_GetUserCheckinCalendarAck_ID = 24044,
    /// <summary>
    /// 拒绝打卡请求
    /// </summary>
    [pbr::OriginalName("CS_RefuseRecheckinReq_ID")] CS_RefuseRecheckinReq_ID = 24045,
    /// <summary>
    /// 拒绝打卡响应
    /// </summary>
    [pbr::OriginalName("SC_RefuseRecheckinAck_ID")] SC_RefuseRecheckinAck_ID = 24046,
    /// <summary>
    /// 获取用户装扮物品全集
    /// </summary>
    [pbr::OriginalName("CS_GetUserDressUpMerchandiseDataReq_ID")] CS_GetUserDressUpMerchandiseDataReq_ID = 24047,
    /// <summary>
    /// 获取用户装扮物品全集
    /// </summary>
    [pbr::OriginalName("SC_GetUserDressUpMerchandiseDataAck_ID")] SC_GetUserDressUpMerchandiseDataAck_ID = 24048,
    /// <summary>
    /// Growth周奖励
    /// </summary>
    [pbr::OriginalName("CS_GetGrowthWeeklyGiftReq_ID")] CS_GetGrowthWeeklyGiftReq_ID = 24049,
    /// <summary>
    /// Growth周奖励
    /// </summary>
    [pbr::OriginalName("SC_GetGrowthWeeklyGiftAck_ID")] SC_GetGrowthWeeklyGiftAck_ID = 24050,
    /// <summary>
    /// 设置用户排行榜升降级已展示
    /// </summary>
    [pbr::OriginalName("CS_SetRankingChangeClickReq_ID")] CS_SetRankingChangeClickReq_ID = 24051,
    /// <summary>
    /// 设置用户排行榜升降级已展示
    /// </summary>
    [pbr::OriginalName("SC_SetRankingChangeClickAck_ID")] SC_SetRankingChangeClickAck_ID = 24052,
    /// <summary>
    /// 设置用户头像请求
    /// </summary>
    [pbr::OriginalName("CS_SetUserHeadItemReq_ID")] CS_SetUserHeadItemReq_ID = 24053,
    /// <summary>
    /// 设置用户头像响应
    /// </summary>
    [pbr::OriginalName("SC_SetUserHeadItemAck_ID")] SC_SetUserHeadItemAck_ID = 24054,
    /// <summary>
    /// 获取用户好友列表请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserFriendListReq_ID")] CS_GetUserFriendListReq_ID = 24055,
    /// <summary>
    /// 获取用户好友列表响应
    /// </summary>
    [pbr::OriginalName("SC_GetUserFriendListAck_ID")] SC_GetUserFriendListAck_ID = 24056,
    /// <summary>
    /// 添加好友请求
    /// </summary>
    [pbr::OriginalName("CS_AddFriendReq_ID")] CS_AddFriendReq_ID = 24057,
    /// <summary>
    /// 添加好友响应
    /// </summary>
    [pbr::OriginalName("SC_AddFriendAck_ID")] SC_AddFriendAck_ID = 24058,
    /// <summary>
    /// 移除好友请求
    /// </summary>
    [pbr::OriginalName("CS_RemoveFriendReq_ID")] CS_RemoveFriendReq_ID = 24059,
    /// <summary>
    /// 移除好友响应
    /// </summary>
    [pbr::OriginalName("SC_RemoveFriendAck_ID")] SC_RemoveFriendAck_ID = 24060,
    /// <summary>
    /// 获取推荐好友列表请求
    /// </summary>
    [pbr::OriginalName("CS_GetRecommendedFriendListReq_ID")] CS_GetRecommendedFriendListReq_ID = 24061,
    /// <summary>
    /// 获取推荐好友列表响应
    /// </summary>
    [pbr::OriginalName("SC_GetRecommendedFriendListAck_ID")] SC_GetRecommendedFriendListAck_ID = 24062,
    /// <summary>
    /// 查询用户请求
    /// </summary>
    [pbr::OriginalName("CS_SearchUserReq_ID")] CS_SearchUserReq_ID = 24063,
    /// <summary>
    /// 查询用户响应
    /// </summary>
    [pbr::OriginalName("SC_SearchUserAck_ID")] SC_SearchUserAck_ID = 24064,
    /// <summary>
    /// 获取用户礼物请求
    /// </summary>
    [pbr::OriginalName("CS_SendGiftForFriendReq_ID")] CS_SendGiftForFriendReq_ID = 24065,
    /// <summary>
    /// 获取用户礼物响应
    /// </summary>
    [pbr::OriginalName("SC_SendGiftForFriendAck_ID")] SC_SendGiftForFriendAck_ID = 24066,
    /// <summary>
    /// 获取用户礼物请求
    /// </summary>
    [pbr::OriginalName("CS_GetUserGiftPortalDataReq_ID")] CS_GetUserGiftPortalDataReq_ID = 24067,
    /// <summary>
    /// 获取用户礼物响应
    /// </summary>
    [pbr::OriginalName("SC_GetUserGiftPortalDataAck_ID")] SC_GetUserGiftPortalDataAck_ID = 24068,
    /// <summary>
    /// 配对好友请求
    /// </summary>
    [pbr::OriginalName("CS_MatchFriendShipTaskReq_ID")] CS_MatchFriendShipTaskReq_ID = 24069,
    /// <summary>
    /// 配对好友响应
    /// </summary>
    [pbr::OriginalName("SC_MatchFriendShipTaskAck_ID")] SC_MatchFriendShipTaskAck_ID = 24070,
    /// <summary>
    /// 好友通知请求
    /// </summary>
    [pbr::OriginalName("CS_FriendShipNotifyReq_ID")] CS_FriendShipNotifyReq_ID = 24071,
    /// <summary>
    /// 好友通知响应
    /// </summary>
    [pbr::OriginalName("SC_FriendShipNotifyAck_ID")] SC_FriendShipNotifyAck_ID = 24072,
    /// <summary>
    /// 设置展示状态请求
    /// </summary>
    [pbr::OriginalName("CS_SetShowStateReq_ID")] CS_SetShowStateReq_ID = 24073,
    /// <summary>
    /// 设置展示状态
    /// </summary>
    [pbr::OriginalName("SC_SetShowStateAck_ID")] SC_SetShowStateAck_ID = 24074,
    /// <summary>
    /// 获取好友连胜推荐列表请求
    /// </summary>
    [pbr::OriginalName("CS_GetFriendStreakRecommendListReq_ID")] CS_GetFriendStreakRecommendListReq_ID = 24075,
    /// <summary>
    /// 获取好友连胜推荐列表响应
    /// </summary>
    [pbr::OriginalName("SC_GetFriendStreakRecommendListAck_ID")] SC_GetFriendStreakRecommendListAck_ID = 24076,
    /// <summary>
    /// 更新好友连胜关系请求
    /// </summary>
    [pbr::OriginalName("CS_UpdateFriendStreakRelationReq_ID")] CS_UpdateFriendStreakRelationReq_ID = 24077,
    /// <summary>
    /// 更新好友连胜关系响应
    /// </summary>
    [pbr::OriginalName("SC_UpdateFriendStreakRelationAck_ID")] SC_UpdateFriendStreakRelationAck_ID = 24078,
    /// <summary>
    /// 获取好友连胜页面信息请求
    /// </summary>
    [pbr::OriginalName("CS_GetFriendStreakPortalReq_ID")] CS_GetFriendStreakPortalReq_ID = 24079,
    /// <summary>
    /// 获取好友连胜页面信息响应
    /// </summary>
    [pbr::OriginalName("SC_GetFriendStreakPortalAck_ID")] SC_GetFriendStreakPortalAck_ID = 24080,
    /// <summary>
    /// 获取Explore首页激励信息请求
    /// </summary>
    [pbr::OriginalName("CS_GetIncentiveDataForExploreReq_ID")] CS_GetIncentiveDataForExploreReq_ID = 24081,
    /// <summary>
    /// 获取Explore首页激励信息响应
    /// </summary>
    [pbr::OriginalName("SC_GetIncentiveDataForExploreAck_ID")] SC_GetIncentiveDataForExploreAck_ID = 24082,
    /// <summary>
    /// 开始消耗请求
    /// </summary>
    [pbr::OriginalName("CS_StartConsumeReq_ID")] CS_StartConsumeReq_ID = 24083,
    /// <summary>
    /// 开始消耗响应
    /// </summary>
    [pbr::OriginalName("SC_StartConsumeAck_ID")] SC_StartConsumeAck_ID = 24084,
    /// <summary>
    /// 25001~25100 经济模块
    /// </summary>
    [pbr::OriginalName("SC_EconomicCoinSettlementPush_ID")] SC_EconomicCoinSettlementPush_ID = 25001,
    /// <summary>
    ///查询金币余额请求
    /// </summary>
    [pbr::OriginalName("CS_EconomicCoinGetBalanceReq_ID")] CS_EconomicCoinGetBalanceReq_ID = 25002,
    /// <summary>
    ///查询金币余额返回
    /// </summary>
    [pbr::OriginalName("SC_EconomicCoinGetBalanceAck_ID")] SC_EconomicCoinGetBalanceAck_ID = 25003,
    /// <summary>
    /// 订单接口
    /// </summary>
    [pbr::OriginalName("CS_CreateOrderReq_ID")] CS_CreateOrderReq_ID = 25010,
    /// <summary>
    /// 创建订单请求返回
    /// </summary>
    [pbr::OriginalName("SC_CreateOrderAck_ID")] SC_CreateOrderAck_ID = 25011,
    /// <summary>
    /// 取消订单请求
    /// </summary>
    [pbr::OriginalName("CS_CancelOrderReq_ID")] CS_CancelOrderReq_ID = 25012,
    /// <summary>
    /// 取消订单返回
    /// </summary>
    [pbr::OriginalName("SC_CancelOrderAck_ID")] SC_CancelOrderAck_ID = 25013,
    /// <summary>
    /// 取消订单请求
    /// </summary>
    [pbr::OriginalName("CS_GetUnpaidOrderReq_ID")] CS_GetUnpaidOrderReq_ID = 25014,
    /// <summary>
    /// 取消订单返回
    /// </summary>
    [pbr::OriginalName("SC_GetUnpaidOrderAck_ID")] SC_GetUnpaidOrderAck_ID = 25015,
    /// <summary>
    /// 校验回调请求
    /// </summary>
    [pbr::OriginalName("CS_VerifyReceiptDataReq_ID")] CS_VerifyReceiptDataReq_ID = 25016,
    /// <summary>
    /// 校验回调返回
    /// </summary>
    [pbr::OriginalName("SC_VerifyReceiptDataResp_ID")] SC_VerifyReceiptDataResp_ID = 25017,
    /// <summary>
    ///用户订单推送消息
    /// </summary>
    [pbr::OriginalName("SC_UserRewardItem_ID")] SC_UserRewardItem_ID = 25018,
    /// <summary>
    ///获取商品详情请求
    /// </summary>
    [pbr::OriginalName("CS_GetShopInfoReq_ID")] CS_GetShopInfoReq_ID = 25019,
    /// <summary>
    ///获取商品详情返回
    /// </summary>
    [pbr::OriginalName("SC_GetShopInfoResp_ID")] SC_GetShopInfoResp_ID = 25020,
    /// <summary>
    /// 购买体力请求
    /// </summary>
    [pbr::OriginalName("CS_PurchaseStaminaReq_ID")] CS_PurchaseStaminaReq_ID = 25021,
    /// <summary>
    /// 购买体力请求
    /// </summary>
    [pbr::OriginalName("SC_PurchaseStaminaAck_ID")] SC_PurchaseStaminaAck_ID = 25022,
    /// <summary>
    /// 购买物品请求
    /// </summary>
    [pbr::OriginalName("CS_PayMerchandiseReq_ID")] CS_PayMerchandiseReq_ID = 25023,
    /// <summary>
    /// 购买物品返回
    /// </summary>
    [pbr::OriginalName("SC_PayMerchandiseResp_ID")] SC_PayMerchandiseResp_ID = 25024,
    /// <summary>
    /// 25101~25200 生词模块
    /// </summary>
    [pbr::OriginalName("CS_ClickWordReq_ID")] CS_ClickWordReq_ID = 25101,
    /// <summary>
    ///点词返回
    /// </summary>
    [pbr::OriginalName("SC_ClickWordAck_ID")] SC_ClickWordAck_ID = 25102,
    /// <summary>
    /// 打卡
    /// </summary>
    [pbr::OriginalName("CS_GetUserCheckinDataReq_ID")] CS_GetUserCheckinDataReq_ID = 25201,
    [pbr::OriginalName("SC_GetUserCheckinDataAck_ID")] SC_GetUserCheckinDataAck_ID = 25202,
    [pbr::OriginalName("CS_DrawCheckinRewardReq_ID")] CS_DrawCheckinRewardReq_ID = 25203,
    [pbr::OriginalName("SC_DrawCheckinRewardAck_ID")] SC_DrawCheckinRewardAck_ID = 25204,
    /// <summary>
    /// 通知
    /// </summary>
    [pbr::OriginalName("CS_RegisterUserDeviceReq_ID")] CS_RegisterUserDeviceReq_ID = 25301,
    [pbr::OriginalName("SC_RegisterUserDeviceAck_ID")] SC_RegisterUserDeviceAck_ID = 25302,
    [pbr::OriginalName("CS_GetSystemNoticeReq_ID")] CS_GetSystemNoticeReq_ID = 25303,
    [pbr::OriginalName("SC_GetSystemNoticeAck_ID")] SC_GetSystemNoticeAck_ID = 25304,
    /// <summary>
    /// 兑换码
    /// </summary>
    [pbr::OriginalName("CS_RedeemCouponReq_ID")] CS_RedeemCouponReq_ID = 25401,
    [pbr::OriginalName("SC_RedeemCouponAck_ID")] SC_RedeemCouponAck_ID = 25402,
    /// <summary>
    /// 26001~27000 社交
    /// </summary>
    [pbr::OriginalName("CS_GetSingleChatDialogReq_ID")] CS_GetSingleChatDialogReq_ID = 26001,
    [pbr::OriginalName("SC_GetSingleChatDialogAck_ID")] SC_GetSingleChatDialogAck_ID = 26002,
    [pbr::OriginalName("CS_QueryChatListReq_ID")] CS_QueryChatListReq_ID = 26003,
    [pbr::OriginalName("SC_QueryChatListAck_ID")] SC_QueryChatListAck_ID = 26004,
    [pbr::OriginalName("CS_QueryChatMessageListReq_ID")] CS_QueryChatMessageListReq_ID = 26005,
    [pbr::OriginalName("SC_QueryChatMessageListAck_ID")] SC_QueryChatMessageListAck_ID = 26006,
    [pbr::OriginalName("CS_SendChatMessageReq_ID")] CS_SendChatMessageReq_ID = 26007,
    [pbr::OriginalName("SC_SendChatMessageAck_ID")] SC_SendChatMessageAck_ID = 26008,
    [pbr::OriginalName("SC_ChatMessageNtf_ID")] SC_ChatMessageNtf_ID = 26009,
    [pbr::OriginalName("CS_UpdateChatIndexReq_ID")] CS_UpdateChatIndexReq_ID = 26010,
    [pbr::OriginalName("SC_UpdateChatIndexAck_ID")] SC_UpdateChatIndexAck_ID = 26011,
    [pbr::OriginalName("CS_QueryChatUserListReq_ID")] CS_QueryChatUserListReq_ID = 26012,
    [pbr::OriginalName("SC_QueryChatUserListAck_ID")] SC_QueryChatUserListAck_ID = 26013,
    [pbr::OriginalName("SS_GetSingleChatGroupReq_ID")] SS_GetSingleChatGroupReq_ID = 26014,
    [pbr::OriginalName("SS_GetSingleChatGroupAck_ID")] SS_GetSingleChatGroupAck_ID = 26015,
    [pbr::OriginalName("SS_GetChatGroupListReq_ID")] SS_GetChatGroupListReq_ID = 26016,
    [pbr::OriginalName("SS_GetChatGroupListAck_ID")] SS_GetChatGroupListAck_ID = 26017,
    [pbr::OriginalName("CS_ChatScaffoldReq_ID")] CS_ChatScaffoldReq_ID = 26018,
    [pbr::OriginalName("SC_ChatScaffoldAck_ID")] SC_ChatScaffoldAck_ID = 26019,
    [pbr::OriginalName("CS_GenTestSingleChatReq_ID")] CS_GenTestSingleChatReq_ID = 26020,
    [pbr::OriginalName("SC_GenTestSingleChatAck_ID")] SC_GenTestSingleChatAck_ID = 26021,
    [pbr::OriginalName("CS_QueryUnReadMessageNumReq_ID")] CS_QueryUnReadMessageNumReq_ID = 26022,
    [pbr::OriginalName("SC_QueryUnReadMessageNumAck_ID")] SC_QueryUnReadMessageNumAck_ID = 26023,
    [pbr::OriginalName("CS_ChatRecordStatusSyncReq_ID")] CS_ChatRecordStatusSyncReq_ID = 26024,
    [pbr::OriginalName("SC_ChatRecordStatusSyncAck_ID")] SC_ChatRecordStatusSyncAck_ID = 26025,
    [pbr::OriginalName("SC_ChatRecordStatusNtf_ID")] SC_ChatRecordStatusNtf_ID = 26026,
    [pbr::OriginalName("CS_GetBuildingTopicListReq_ID")] CS_GetBuildingTopicListReq_ID = 26027,
    [pbr::OriginalName("SC_GetBuildingTopicListAck_ID")] SC_GetBuildingTopicListAck_ID = 26028,
    [pbr::OriginalName("CS_GetAreaAchievementListReq_ID")] CS_GetAreaAchievementListReq_ID = 26029,
    [pbr::OriginalName("SC_GetAreaAchievementListAck_ID")] SC_GetAreaAchievementListAck_ID = 26030,
    /// <summary>
    /// 27001~28000 全双工对话
    /// </summary>
    [pbr::OriginalName("CS_CreateWorldStoryTaskReq_ID")] CS_CreateWorldStoryTaskReq_ID = 27001,
    [pbr::OriginalName("SC_CreateWorldStoryTaskAck_ID")] SC_CreateWorldStoryTaskAck_ID = 27002,
    [pbr::OriginalName("CS_StartWorldDialogReq_ID")] CS_StartWorldDialogReq_ID = 27003,
    [pbr::OriginalName("SC_StartWorldDialogAck_ID")] SC_StartWorldDialogAck_ID = 27004,
    [pbr::OriginalName("CS_FinishWorldStoryTaskReq_ID")] CS_FinishWorldStoryTaskReq_ID = 27005,
    [pbr::OriginalName("SC_FinishWorldStoryTaskAck_ID")] SC_FinishWorldStoryTaskAck_ID = 27006,
    [pbr::OriginalName("SC_UserDialogContentNtf_ID")] SC_UserDialogContentNtf_ID = 27007,
    [pbr::OriginalName("SC_AvatarDialogContentNtf_ID")] SC_AvatarDialogContentNtf_ID = 27008,
    [pbr::OriginalName("SC_AvatarAudioNtf_ID")] SC_AvatarAudioNtf_ID = 27009,
    [pbr::OriginalName("SC_AvatarDialogTranslateNtf_ID")] SC_AvatarDialogTranslateNtf_ID = 27010,
    [pbr::OriginalName("SC_AvatarAdviceNtf_ID")] SC_AvatarAdviceNtf_ID = 27011,
    [pbr::OriginalName("SC_AvatarExampleNtf_ID")] SC_AvatarExampleNtf_ID = 27012,
    [pbr::OriginalName("SC_WorldStoryProcessNtf_ID")] SC_WorldStoryProcessNtf_ID = 27013,
    [pbr::OriginalName("CS_DialogSettingReq_ID")] CS_DialogSettingReq_ID = 27014,
    /// <summary>
    /// 28001~29000 游戏世界
    /// </summary>
    [pbr::OriginalName("CS_GetWorldRoleInfoReq_ID")] CS_GetWorldRoleInfoReq_ID = 28001,
    [pbr::OriginalName("SC_GetWorldRoleInfoAck_ID")] SC_GetWorldRoleInfoAck_ID = 28002,
    [pbr::OriginalName("CS_CreateWorldRoleReq_ID")] CS_CreateWorldRoleReq_ID = 28003,
    [pbr::OriginalName("SC_CreateWorldRoleAck_ID")] SC_CreateWorldRoleAck_ID = 28004,
    /// <summary>
    /// 29001~30000 登录/用户中心
    /// </summary>
    [pbr::OriginalName("CS_SendVerificationCodeReq_ID")] CS_SendVerificationCodeReq_ID = 29001,
    [pbr::OriginalName("SC_SendVerificationCodeResp_ID")] SC_SendVerificationCodeResp_ID = 29002,
    /// <summary>
    /// 30001~30100 A/B测试
    /// </summary>
    [pbr::OriginalName("CS_GetAbTestResultReq_ID")] CS_GetAbTestResultReq_ID = 30001,
    [pbr::OriginalName("SC_GetAbTestResultAck_ID")] SC_GetAbTestResultAck_ID = 30002,
    /// <summary>
    /// 31001~31000 探索
    /// 31001~31100 推荐
    /// </summary>
    [pbr::OriginalName("CS_GetRecommendListReq_ID")] CS_GetRecommendListReq_ID = 31001,
    /// <summary>
    /// 推荐列表响应
    /// </summary>
    [pbr::OriginalName("SC_GetRecommendListResp_ID")] SC_GetRecommendListResp_ID = 31002,
    /// <summary>
    /// 推荐下行消息 - 切换推荐实体
    /// </summary>
    [pbr::OriginalName("SC_RecommendSwitchEntity_ID")] SC_RecommendSwitchEntity_ID = 31003,
    /// <summary>
    /// 31101~31200 长连接
    /// </summary>
    [pbr::OriginalName("CS_ExploreUpMsg_ID")] CS_ExploreUpMsg_ID = 31101,
    /// <summary>
    /// Explore长连接下行消息
    /// </summary>
    [pbr::OriginalName("SC_ExploreDownMsg_ID")] SC_ExploreDownMsg_ID = 31102,
    /// <summary>
    /// Explore服务基础下行消息
    /// </summary>
    [pbr::OriginalName("SC_ExploreDownMsgForServerBasic_ID")] SC_ExploreDownMsgForServerBasic_ID = 31103,
    /// <summary>
    /// Explore服务心跳下行消息
    /// </summary>
    [pbr::OriginalName("SC_ExploreDownMsgForHeartbeat_ID")] SC_ExploreDownMsgForHeartbeat_ID = 31104,
    /// <summary>
    /// 31201~31300 对话
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAvatarReply_ID")] SC_DialogDownMsgForAvatarReply_ID = 31203,
    /// <summary>
    /// 对话下行消息 - Avatar回复翻译
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAvatarReplyTranslate_ID")] SC_DialogDownMsgForAvatarReplyTranslate_ID = 31204,
    /// <summary>
    /// 对话下行消息 - Avatar回复TTS
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAvatarReplyTTS_ID")] SC_DialogDownMsgForAvatarReplyTTS_ID = 31205,
    /// <summary>
    /// 对话下行消息 - 用户回复示例
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForUserReplyExample_ID")] SC_DialogDownMsgForUserReplyExample_ID = 31206,
    /// <summary>
    /// 对话下行消息 - 用户回复示例翻译
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForUserReplyExampleTranslate_ID")] SC_DialogDownMsgForUserReplyExampleTranslate_ID = 31207,
    /// <summary>
    /// 对话下行消息 - 用户回复示例TTS
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForUserReplyExampleTTS_ID")] SC_DialogDownMsgForUserReplyExampleTTS_ID = 31208,
    /// <summary>
    /// 对话下行消息 - 用户语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForASR_ID")] SC_DialogDownMsgForASR_ID = 31209,
    /// <summary>
    /// 对话下行消息 - 业务事件
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForBizEvent_ID")] SC_DialogDownMsgForBizEvent_ID = 31210,
    /// <summary>
    /// 对话下行消息 - 反馈结果
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForFeedback_ID")] SC_DialogDownMsgForFeedback_ID = 31211,
    /// <summary>
    /// 对话下行消息 - 任务目标状态变化
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForTaskGoalStatusChange_ID")] SC_DialogDownMsgForTaskGoalStatusChange_ID = 31212,
    /// <summary>
    /// 对话下行消息 - Advice
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAdvice_ID")] SC_DialogDownMsgForAdvice_ID = 31213,
    /// <summary>
    /// 31301~31400 用户设置
    /// </summary>
    [pbr::OriginalName("SC_UserSettingDownMsgForSaveUserSettings_ID")] SC_UserSettingDownMsgForSaveUserSettings_ID = 31301,
    /// <summary>
    /// 31401~31500 用户中心
    /// </summary>
    [pbr::OriginalName("CS_UpdateAppsflyerCallbackDataReq_ID")] CS_UpdateAppsflyerCallbackDataReq_ID = 31401,
    [pbr::OriginalName("SC_UpdateAppsflyerCallbackDataResp_ID")] SC_UpdateAppsflyerCallbackDataResp_ID = 31402,
    [pbr::OriginalName("CS_GetHomepageGuideItemReq_ID")] CS_GetHomepageGuideItemReq_ID = 31403,
    [pbr::OriginalName("SC_GetHomepageGuideItemResp_ID")] SC_GetHomepageGuideItemResp_ID = 31404,
    /// <summary>
    /// 31501~31600 用户聊天
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserRecognizing_ID")] SC_UserChatDownMsgForUserRecognizing_ID = 31501,
    /// <summary>
    /// 用户聊天下行消息 - 用户语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserRecognized_ID")] SC_UserChatDownMsgForUserRecognized_ID = 31502,
    /// <summary>
    /// 用户聊天下行消息 - 他人语音识别过程结果
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForOtherUserRecognizing_ID")] SC_UserChatDownMsgForOtherUserRecognizing_ID = 31503,
    /// <summary>
    /// 用户聊天下行消息 - 他人语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForOtherUserRecognized_ID")] SC_UserChatDownMsgForOtherUserRecognized_ID = 31504,
    /// <summary>
    /// 用户聊天下行消息 - 他人回复翻译
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForOtherUserReplyTranslate_ID")] SC_UserChatDownMsgForOtherUserReplyTranslate_ID = 31505,
    /// <summary>
    /// 用户聊天下行消息 - 用户回复示例
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserReplyExample_ID")] SC_UserChatDownMsgForUserReplyExample_ID = 31506,
    /// <summary>
    /// 用户聊天下行消息 - 用户回复示例翻译
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserReplyExampleTranslate_ID")] SC_UserChatDownMsgForUserReplyExampleTranslate_ID = 31507,
    /// <summary>
    /// 用户聊天下行消息 - 业务事件
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForBizEvent_ID")] SC_UserChatDownMsgForBizEvent_ID = 31508,
    /// <summary>
    /// 31601~31700 撮合
    /// </summary>
    [pbr::OriginalName("SC_MatchingDown_MatchStatus_ID")] SC_MatchingDown_MatchStatus_ID = 31603,
    /// <summary>
    /// 撮合下行消息 - 撮合成功结果
    /// </summary>
    [pbr::OriginalName("SC_MatchingDown_MatchedResult_ID")] SC_MatchingDown_MatchedResult_ID = 31604,
    /// <summary>
    /// 撮合下行消息 - 撮合失败结果
    /// </summary>
    [pbr::OriginalName("SC_MatchingDown_MatchFailed_ID")] SC_MatchingDown_MatchFailed_ID = 31605,
    /// <summary>
    /// 31701~31800 onboarding对话
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForAvatarReply_ID")] SC_OnboardingChatDownMsgForAvatarReply_ID = 31701,
    /// <summary>
    /// onboarding对话下行消息 - Avatar回复翻译
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForAvatarReplyTranslate_ID")] SC_OnboardingChatDownMsgForAvatarReplyTranslate_ID = 31702,
    /// <summary>
    /// onboarding对话下行消息 - Avatar回复TTS
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForAvatarReplyTTS_ID")] SC_OnboardingChatDownMsgForAvatarReplyTTS_ID = 31703,
    /// <summary>
    /// onboarding对话下行消息 - 用户语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForASR_ID")] SC_OnboardingChatDownMsgForASR_ID = 31704,
    /// <summary>
    /// onboarding对话下行消息 - 业务事件
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForBizEvent_ID")] SC_OnboardingChatDownMsgForBizEvent_ID = 31705,
    /// <summary>
    /// onboarding对话下行消息 - 对话结算
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForSettlement_ID")] SC_OnboardingChatDownMsgForSettlement_ID = 31706,
    /// <summary>
    /// 获取onboarding英语水平评测对话预加载数据请求
    /// </summary>
    [pbr::OriginalName("CS_GetOnboardingChatPreloadDataReq_ID")] CS_GetOnboardingChatPreloadDataReq_ID = 31707,
    /// <summary>
    /// 获取onboarding英语水平评测对话预加载数据响应
    /// </summary>
    [pbr::OriginalName("SC_GetOnboardingChatPreloadDataResp_ID")] SC_GetOnboardingChatPreloadDataResp_ID = 31708,
    /// <summary>
    /// 31801~31900 onboarding
    /// </summary>
    [pbr::OriginalName("SC_SkipOnboardingChat_ID")] SC_SkipOnboardingChat_ID = 31801,
  }

  #endregion

}

#endregion Designer generated code

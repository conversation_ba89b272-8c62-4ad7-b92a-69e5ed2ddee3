/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.FragmentPractice
{
    public partial class ImageMaskComp : UIBindT
    {
        public override string pkgName => "FragmentPractice";
        public override string comName => "ImageMaskComp";

        public GLoader ldr;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ldr = (GLoader)com.GetChildAt(1);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ldr = null;
        }
    }
}
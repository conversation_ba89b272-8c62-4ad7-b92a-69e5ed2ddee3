# FairyGUI 弧形列表实现说明

## 功能概述

实现了一个横向滚动的弧形列表，具有以下特性：

1. **弧形显示效果**：屏幕上显示5个item，大小依次为 0.4f, 0.6f, 1f, 0.6f, 0.4f
2. **Y方向位置变化**：形成弧形效果，1、5位置最靠下，2、4位置偏上，3号位正常
3. **点击居中**：点击任意item会将其平滑移动到屏幕中央
4. **拖拽吸附**：停止滑动时自动吸附到最近的中心位置并选中

## 核心配置参数

```csharp
// 5个位置的缩放比例
private readonly float[] _itemScales = { 0.4f, 0.6f, 1f, 0.6f, 0.4f };

// Y方向偏移，形成弧形 (负值表示向上偏移)
private readonly float[] _itemYOffsets = { -30f, -15f, 0f, -15f, -30f };

// 中心位置索引
private readonly int _centerIndex = 2;

// 可见item数量
private readonly int _visibleItemCount = 5;
```

## 主要方法说明

### 1. InitializeList()
初始化列表设置，配置为横向布局并设置渲染回调。

### 2. UpdateItemsDisplay()
核心方法，负责更新所有可见item的弧形显示效果。
- 计算每个item相对于中心的位置
- 应用对应的缩放和Y偏移
- 标记中心位置的item为选中状态

### 3. MoveToCenter(int targetIndex)
将指定索引的item移动到中心位置。
- 使用FairyGUI的ScrollToView进行平滑滚动
- 滚动完成后更新显示效果

### 4. SnapToCenter()
拖拽结束后的吸附逻辑。
- 计算最接近中心的item
- 自动滚动到该位置

### 5. ApplyArcEffect(GObject item, int positionIndex)
应用弧形效果的具体实现。
- 使用GTween进行平滑的缩放和位置动画
- 动画时长0.3秒，使用QuadOut缓动

## 事件处理

### 点击事件
```csharp
private void OnSelectListClick(EventContext context)
{
    // 获取被点击的item并移动到中心
    GObject clickedItem = context.sender as GObject;
    if (clickedItem?.data != null)
    {
        int clickedIndex = (int)clickedItem.data;
        MoveToCenter(clickedIndex);
    }
}
```

### 拖拽事件
- **OnSelectDragStartClick**: 标记开始拖拽
- **OnSelectDragMoveClick**: 拖拽过程中实时更新显示
- **OnSelectDragEndClick**: 拖拽结束后执行吸附逻辑

## 使用方法

1. 确保你的GList设置为横向布局
2. 在OnShow()中调用SetupListData()设置数据
3. 系统会自动处理所有的弧形显示和交互逻辑

## 自定义配置

可以通过修改以下参数来调整效果：

- `_itemScales`: 调整不同位置的缩放比例
- `_itemYOffsets`: 调整Y方向偏移量，控制弧形程度
- `animDuration`: 在ApplyArcEffect中调整动画时长
- `EaseType`: 修改动画缓动类型

## 注意事项

1. 确保列表item的data属性正确设置了索引值
2. 列表应该有足够的item数量（建议>8个）
3. 动画过程中会阻止新的交互，避免冲突
4. 需要根据实际的item结构调整RenderListItem方法

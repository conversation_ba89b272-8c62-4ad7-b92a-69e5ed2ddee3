// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/learn_path/course.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.learnPath {

  /// <summary>Holder for reflection information generated from protobuf/learn_path/course.proto</summary>
  public static partial class CourseReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/learn_path/course.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static CourseReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiBwcm90b2J1Zi9sZWFybl9wYXRoL2NvdXJzZS5wcm90bxIGY291cnNlGhtw",
            "cm90b2J1Zi9iYXNpYy9jb3Vyc2UucHJvdG8imQEKE1NTX0dldENvdXJzZUlu",
            "Zm9SZXESEQoJY291cnNlX2lkGAEgASgDEhYKDmxlYXJuX2xhbmd1YWdlGAIg",
            "ASgJEhUKDWZyb21fbGFuZ3VhZ2UYAyABKAkSEgoKc2VjdGlvbl9pZBgEIAEo",
            "AxIVCg1pc19mcm9tX3NoZWxmGAUgASgIEhUKDXNlY3Rpb25faW5kZXgYBiAB",
            "KAUiWQoTU1NfR2V0Q291cnNlSW5mb0FjaxIMCgRjb2RlGAEgASgFEg8KB2Vy",
            "cl9tc2cYAiABKAkSIwoEZGF0YRgDIAEoCzIVLmNvdXJzZS5TU19Db3Vyc2VJ",
            "bmZvIn8KDVNTX0NvdXJzZUluZm8SEQoJY291cnNlX2lkGAEgASgDEhMKC2Nv",
            "dXJzZV9uYW1lGAIgASgJEhgKEGNvdXJzZV9pbWFnZV91cmwYAyABKAkSLAoM",
            "c2VjdGlvbl9saXN0GAQgAygLMhYuY291cnNlLlNTX1NlY3Rpb25JbmZvIpcB",
            "Cg5TU19TZWN0aW9uSW5mbxISCgpzZWN0aW9uX2lkGAEgASgDEhUKDXNlY3Rp",
            "b25faW5kZXgYAiABKAUSDQoFdGl0bGUYAyABKAkSEAoIc3VidGl0bGUYBCAB",
            "KAkSJgoJdW5pdF9saXN0GAUgAygLMhMuY291cnNlLlNTX1VuaXRJbmZvEhEK",
            "CWNvdmVyX3VybBgGIAEoCSKWAQoLU1NfVW5pdEluZm8SDwoHdW5pdF9pZBgB",
            "IAEoAxISCgp1bml0X2luZGV4GAIgASgFEg0KBXRpdGxlGAMgASgJEhAKCHN1",
            "YnRpdGxlGAQgASgJEhcKD2F2YXRhcl9oZWFkX3VybBgFIAEoCRIoCgpsZXZl",
            "bF9saXN0GAYgAygLMhQuY291cnNlLlNTX0xldmVsSW5mbyK/AQoMU1NfTGV2",
            "ZWxJbmZvEhAKCGxldmVsX2lkGAEgASgDEhMKC2xldmVsX2luZGV4GAIgASgF",
            "Eg0KBXRpdGxlGAMgASgJEhAKCHN1YnRpdGxlGAQgASgJEiUKCmxldmVsX3R5",
            "cGUYBSABKA4yES5QQl9MZXZlbFR5cGVFbnVtEhIKCnJlbGF0ZWRfaWQYBiAB",
            "KAMSLAoMc2Vzc2lvbl9saXN0GAcgAygLMhYuY291cnNlLlNTX1Nlc3Npb25J",
            "bmZvIjsKDlNTX1Nlc3Npb25JbmZvEhIKCnNlc3Npb25faWQYASABKAMSFQoN",
            "c2Vzc2lvbl9pbmRleBgCIAEoBUIvWh12Zl9wcm90b2J1Zi9zZXJ2ZXIvbGVh",
            "cm5fcGF0aKoCDU1zZy5sZWFyblBhdGhiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.basic.CourseReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.learnPath.SS_GetCourseInfoReq), global::Msg.learnPath.SS_GetCourseInfoReq.Parser, new[]{ "course_id", "learn_language", "from_language", "section_id", "is_from_shelf", "section_index" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.learnPath.SS_GetCourseInfoAck), global::Msg.learnPath.SS_GetCourseInfoAck.Parser, new[]{ "code", "err_msg", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.learnPath.SS_CourseInfo), global::Msg.learnPath.SS_CourseInfo.Parser, new[]{ "course_id", "course_name", "course_image_url", "section_list" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.learnPath.SS_SectionInfo), global::Msg.learnPath.SS_SectionInfo.Parser, new[]{ "section_id", "section_index", "title", "subtitle", "unit_list", "cover_url" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.learnPath.SS_UnitInfo), global::Msg.learnPath.SS_UnitInfo.Parser, new[]{ "unit_id", "unit_index", "title", "subtitle", "avatar_head_url", "level_list" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.learnPath.SS_LevelInfo), global::Msg.learnPath.SS_LevelInfo.Parser, new[]{ "level_id", "level_index", "title", "subtitle", "level_type", "related_id", "session_list" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.learnPath.SS_SessionInfo), global::Msg.learnPath.SS_SessionInfo.Parser, new[]{ "session_id", "session_index" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  /// <summary>
  /// 获取课程信息请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetCourseInfoReq : pb::IMessage<SS_GetCourseInfoReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetCourseInfoReq> _parser = new pb::MessageParser<SS_GetCourseInfoReq>(() => new SS_GetCourseInfoReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetCourseInfoReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.learnPath.CourseReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetCourseInfoReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetCourseInfoReq(SS_GetCourseInfoReq other) : this() {
      course_id_ = other.course_id_;
      learn_language_ = other.learn_language_;
      from_language_ = other.from_language_;
      section_id_ = other.section_id_;
      is_from_shelf_ = other.is_from_shelf_;
      section_index_ = other.section_index_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetCourseInfoReq Clone() {
      return new SS_GetCourseInfoReq(this);
    }

    /// <summary>Field number for the "course_id" field.</summary>
    public const int course_idFieldNumber = 1;
    private long course_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long course_id {
      get { return course_id_; }
      set {
        course_id_ = value;
      }
    }

    /// <summary>Field number for the "learn_language" field.</summary>
    public const int learn_languageFieldNumber = 2;
    private string learn_language_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string learn_language {
      get { return learn_language_; }
      set {
        learn_language_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "from_language" field.</summary>
    public const int from_languageFieldNumber = 3;
    private string from_language_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string from_language {
      get { return from_language_; }
      set {
        from_language_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "section_id" field.</summary>
    public const int section_idFieldNumber = 4;
    private long section_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long section_id {
      get { return section_id_; }
      set {
        section_id_ = value;
      }
    }

    /// <summary>Field number for the "is_from_shelf" field.</summary>
    public const int is_from_shelfFieldNumber = 5;
    private bool is_from_shelf_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_from_shelf {
      get { return is_from_shelf_; }
      set {
        is_from_shelf_ = value;
      }
    }

    /// <summary>Field number for the "section_index" field.</summary>
    public const int section_indexFieldNumber = 6;
    private int section_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int section_index {
      get { return section_index_; }
      set {
        section_index_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetCourseInfoReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetCourseInfoReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (course_id != other.course_id) return false;
      if (learn_language != other.learn_language) return false;
      if (from_language != other.from_language) return false;
      if (section_id != other.section_id) return false;
      if (is_from_shelf != other.is_from_shelf) return false;
      if (section_index != other.section_index) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (course_id != 0L) hash ^= course_id.GetHashCode();
      if (learn_language.Length != 0) hash ^= learn_language.GetHashCode();
      if (from_language.Length != 0) hash ^= from_language.GetHashCode();
      if (section_id != 0L) hash ^= section_id.GetHashCode();
      if (is_from_shelf != false) hash ^= is_from_shelf.GetHashCode();
      if (section_index != 0) hash ^= section_index.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      if (learn_language.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(learn_language);
      }
      if (from_language.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(from_language);
      }
      if (section_id != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(section_id);
      }
      if (is_from_shelf != false) {
        output.WriteRawTag(40);
        output.WriteBool(is_from_shelf);
      }
      if (section_index != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(section_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      if (learn_language.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(learn_language);
      }
      if (from_language.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(from_language);
      }
      if (section_id != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(section_id);
      }
      if (is_from_shelf != false) {
        output.WriteRawTag(40);
        output.WriteBool(is_from_shelf);
      }
      if (section_index != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(section_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (course_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(course_id);
      }
      if (learn_language.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(learn_language);
      }
      if (from_language.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(from_language);
      }
      if (section_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(section_id);
      }
      if (is_from_shelf != false) {
        size += 1 + 1;
      }
      if (section_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(section_index);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetCourseInfoReq other) {
      if (other == null) {
        return;
      }
      if (other.course_id != 0L) {
        course_id = other.course_id;
      }
      if (other.learn_language.Length != 0) {
        learn_language = other.learn_language;
      }
      if (other.from_language.Length != 0) {
        from_language = other.from_language;
      }
      if (other.section_id != 0L) {
        section_id = other.section_id;
      }
      if (other.is_from_shelf != false) {
        is_from_shelf = other.is_from_shelf;
      }
      if (other.section_index != 0) {
        section_index = other.section_index;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 18: {
            learn_language = input.ReadString();
            break;
          }
          case 26: {
            from_language = input.ReadString();
            break;
          }
          case 32: {
            section_id = input.ReadInt64();
            break;
          }
          case 40: {
            is_from_shelf = input.ReadBool();
            break;
          }
          case 48: {
            section_index = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 18: {
            learn_language = input.ReadString();
            break;
          }
          case 26: {
            from_language = input.ReadString();
            break;
          }
          case 32: {
            section_id = input.ReadInt64();
            break;
          }
          case 40: {
            is_from_shelf = input.ReadBool();
            break;
          }
          case 48: {
            section_index = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetCourseInfoAck : pb::IMessage<SS_GetCourseInfoAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetCourseInfoAck> _parser = new pb::MessageParser<SS_GetCourseInfoAck>(() => new SS_GetCourseInfoAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetCourseInfoAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.learnPath.CourseReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetCourseInfoAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetCourseInfoAck(SS_GetCourseInfoAck other) : this() {
      code_ = other.code_;
      err_msg_ = other.err_msg_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetCourseInfoAck Clone() {
      return new SS_GetCourseInfoAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "err_msg" field.</summary>
    public const int err_msgFieldNumber = 2;
    private string err_msg_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string err_msg {
      get { return err_msg_; }
      set {
        err_msg_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 3;
    private global::Msg.learnPath.SS_CourseInfo data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.learnPath.SS_CourseInfo data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetCourseInfoAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetCourseInfoAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (err_msg != other.err_msg) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (err_msg.Length != 0) hash ^= err_msg.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (err_msg.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(err_msg);
      }
      if (data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (err_msg.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(err_msg);
      }
      if (data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (err_msg.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(err_msg);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetCourseInfoAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.err_msg.Length != 0) {
        err_msg = other.err_msg;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.learnPath.SS_CourseInfo();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            err_msg = input.ReadString();
            break;
          }
          case 26: {
            if (data_ == null) {
              data = new global::Msg.learnPath.SS_CourseInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            err_msg = input.ReadString();
            break;
          }
          case 26: {
            if (data_ == null) {
              data = new global::Msg.learnPath.SS_CourseInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_CourseInfo : pb::IMessage<SS_CourseInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_CourseInfo> _parser = new pb::MessageParser<SS_CourseInfo>(() => new SS_CourseInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_CourseInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.learnPath.CourseReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_CourseInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_CourseInfo(SS_CourseInfo other) : this() {
      course_id_ = other.course_id_;
      course_name_ = other.course_name_;
      course_image_url_ = other.course_image_url_;
      section_list_ = other.section_list_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_CourseInfo Clone() {
      return new SS_CourseInfo(this);
    }

    /// <summary>Field number for the "course_id" field.</summary>
    public const int course_idFieldNumber = 1;
    private long course_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long course_id {
      get { return course_id_; }
      set {
        course_id_ = value;
      }
    }

    /// <summary>Field number for the "course_name" field.</summary>
    public const int course_nameFieldNumber = 2;
    private string course_name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string course_name {
      get { return course_name_; }
      set {
        course_name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "course_image_url" field.</summary>
    public const int course_image_urlFieldNumber = 3;
    private string course_image_url_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string course_image_url {
      get { return course_image_url_; }
      set {
        course_image_url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "section_list" field.</summary>
    public const int section_listFieldNumber = 4;
    private static readonly pb::FieldCodec<global::Msg.learnPath.SS_SectionInfo> _repeated_section_list_codec
        = pb::FieldCodec.ForMessage(34, global::Msg.learnPath.SS_SectionInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.learnPath.SS_SectionInfo> section_list_ = new pbc::RepeatedField<global::Msg.learnPath.SS_SectionInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.learnPath.SS_SectionInfo> section_list {
      get { return section_list_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_CourseInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_CourseInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (course_id != other.course_id) return false;
      if (course_name != other.course_name) return false;
      if (course_image_url != other.course_image_url) return false;
      if(!section_list_.Equals(other.section_list_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (course_id != 0L) hash ^= course_id.GetHashCode();
      if (course_name.Length != 0) hash ^= course_name.GetHashCode();
      if (course_image_url.Length != 0) hash ^= course_image_url.GetHashCode();
      hash ^= section_list_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      if (course_name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(course_name);
      }
      if (course_image_url.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(course_image_url);
      }
      section_list_.WriteTo(output, _repeated_section_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      if (course_name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(course_name);
      }
      if (course_image_url.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(course_image_url);
      }
      section_list_.WriteTo(ref output, _repeated_section_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (course_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(course_id);
      }
      if (course_name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(course_name);
      }
      if (course_image_url.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(course_image_url);
      }
      size += section_list_.CalculateSize(_repeated_section_list_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_CourseInfo other) {
      if (other == null) {
        return;
      }
      if (other.course_id != 0L) {
        course_id = other.course_id;
      }
      if (other.course_name.Length != 0) {
        course_name = other.course_name;
      }
      if (other.course_image_url.Length != 0) {
        course_image_url = other.course_image_url;
      }
      section_list_.Add(other.section_list_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 18: {
            course_name = input.ReadString();
            break;
          }
          case 26: {
            course_image_url = input.ReadString();
            break;
          }
          case 34: {
            section_list_.AddEntriesFrom(input, _repeated_section_list_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 18: {
            course_name = input.ReadString();
            break;
          }
          case 26: {
            course_image_url = input.ReadString();
            break;
          }
          case 34: {
            section_list_.AddEntriesFrom(ref input, _repeated_section_list_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SectionInfo : pb::IMessage<SS_SectionInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SectionInfo> _parser = new pb::MessageParser<SS_SectionInfo>(() => new SS_SectionInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SectionInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.learnPath.CourseReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SectionInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SectionInfo(SS_SectionInfo other) : this() {
      section_id_ = other.section_id_;
      section_index_ = other.section_index_;
      title_ = other.title_;
      subtitle_ = other.subtitle_;
      unit_list_ = other.unit_list_.Clone();
      cover_url_ = other.cover_url_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SectionInfo Clone() {
      return new SS_SectionInfo(this);
    }

    /// <summary>Field number for the "section_id" field.</summary>
    public const int section_idFieldNumber = 1;
    private long section_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long section_id {
      get { return section_id_; }
      set {
        section_id_ = value;
      }
    }

    /// <summary>Field number for the "section_index" field.</summary>
    public const int section_indexFieldNumber = 2;
    private int section_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int section_index {
      get { return section_index_; }
      set {
        section_index_ = value;
      }
    }

    /// <summary>Field number for the "title" field.</summary>
    public const int titleFieldNumber = 3;
    private string title_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string title {
      get { return title_; }
      set {
        title_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "subtitle" field.</summary>
    public const int subtitleFieldNumber = 4;
    private string subtitle_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string subtitle {
      get { return subtitle_; }
      set {
        subtitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "unit_list" field.</summary>
    public const int unit_listFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Msg.learnPath.SS_UnitInfo> _repeated_unit_list_codec
        = pb::FieldCodec.ForMessage(42, global::Msg.learnPath.SS_UnitInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.learnPath.SS_UnitInfo> unit_list_ = new pbc::RepeatedField<global::Msg.learnPath.SS_UnitInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.learnPath.SS_UnitInfo> unit_list {
      get { return unit_list_; }
    }

    /// <summary>Field number for the "cover_url" field.</summary>
    public const int cover_urlFieldNumber = 6;
    private string cover_url_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string cover_url {
      get { return cover_url_; }
      set {
        cover_url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SectionInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SectionInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (section_id != other.section_id) return false;
      if (section_index != other.section_index) return false;
      if (title != other.title) return false;
      if (subtitle != other.subtitle) return false;
      if(!unit_list_.Equals(other.unit_list_)) return false;
      if (cover_url != other.cover_url) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (section_id != 0L) hash ^= section_id.GetHashCode();
      if (section_index != 0) hash ^= section_index.GetHashCode();
      if (title.Length != 0) hash ^= title.GetHashCode();
      if (subtitle.Length != 0) hash ^= subtitle.GetHashCode();
      hash ^= unit_list_.GetHashCode();
      if (cover_url.Length != 0) hash ^= cover_url.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (section_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(section_id);
      }
      if (section_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(section_index);
      }
      if (title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(title);
      }
      if (subtitle.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(subtitle);
      }
      unit_list_.WriteTo(output, _repeated_unit_list_codec);
      if (cover_url.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(cover_url);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (section_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(section_id);
      }
      if (section_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(section_index);
      }
      if (title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(title);
      }
      if (subtitle.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(subtitle);
      }
      unit_list_.WriteTo(ref output, _repeated_unit_list_codec);
      if (cover_url.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(cover_url);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (section_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(section_id);
      }
      if (section_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(section_index);
      }
      if (title.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(title);
      }
      if (subtitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(subtitle);
      }
      size += unit_list_.CalculateSize(_repeated_unit_list_codec);
      if (cover_url.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(cover_url);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SectionInfo other) {
      if (other == null) {
        return;
      }
      if (other.section_id != 0L) {
        section_id = other.section_id;
      }
      if (other.section_index != 0) {
        section_index = other.section_index;
      }
      if (other.title.Length != 0) {
        title = other.title;
      }
      if (other.subtitle.Length != 0) {
        subtitle = other.subtitle;
      }
      unit_list_.Add(other.unit_list_);
      if (other.cover_url.Length != 0) {
        cover_url = other.cover_url;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            section_id = input.ReadInt64();
            break;
          }
          case 16: {
            section_index = input.ReadInt32();
            break;
          }
          case 26: {
            title = input.ReadString();
            break;
          }
          case 34: {
            subtitle = input.ReadString();
            break;
          }
          case 42: {
            unit_list_.AddEntriesFrom(input, _repeated_unit_list_codec);
            break;
          }
          case 50: {
            cover_url = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            section_id = input.ReadInt64();
            break;
          }
          case 16: {
            section_index = input.ReadInt32();
            break;
          }
          case 26: {
            title = input.ReadString();
            break;
          }
          case 34: {
            subtitle = input.ReadString();
            break;
          }
          case 42: {
            unit_list_.AddEntriesFrom(ref input, _repeated_unit_list_codec);
            break;
          }
          case 50: {
            cover_url = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_UnitInfo : pb::IMessage<SS_UnitInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_UnitInfo> _parser = new pb::MessageParser<SS_UnitInfo>(() => new SS_UnitInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_UnitInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.learnPath.CourseReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_UnitInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_UnitInfo(SS_UnitInfo other) : this() {
      unit_id_ = other.unit_id_;
      unit_index_ = other.unit_index_;
      title_ = other.title_;
      subtitle_ = other.subtitle_;
      avatar_head_url_ = other.avatar_head_url_;
      level_list_ = other.level_list_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_UnitInfo Clone() {
      return new SS_UnitInfo(this);
    }

    /// <summary>Field number for the "unit_id" field.</summary>
    public const int unit_idFieldNumber = 1;
    private long unit_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long unit_id {
      get { return unit_id_; }
      set {
        unit_id_ = value;
      }
    }

    /// <summary>Field number for the "unit_index" field.</summary>
    public const int unit_indexFieldNumber = 2;
    private int unit_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int unit_index {
      get { return unit_index_; }
      set {
        unit_index_ = value;
      }
    }

    /// <summary>Field number for the "title" field.</summary>
    public const int titleFieldNumber = 3;
    private string title_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string title {
      get { return title_; }
      set {
        title_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "subtitle" field.</summary>
    public const int subtitleFieldNumber = 4;
    private string subtitle_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string subtitle {
      get { return subtitle_; }
      set {
        subtitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "avatar_head_url" field.</summary>
    public const int avatar_head_urlFieldNumber = 5;
    private string avatar_head_url_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string avatar_head_url {
      get { return avatar_head_url_; }
      set {
        avatar_head_url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "level_list" field.</summary>
    public const int level_listFieldNumber = 6;
    private static readonly pb::FieldCodec<global::Msg.learnPath.SS_LevelInfo> _repeated_level_list_codec
        = pb::FieldCodec.ForMessage(50, global::Msg.learnPath.SS_LevelInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.learnPath.SS_LevelInfo> level_list_ = new pbc::RepeatedField<global::Msg.learnPath.SS_LevelInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.learnPath.SS_LevelInfo> level_list {
      get { return level_list_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_UnitInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_UnitInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (unit_id != other.unit_id) return false;
      if (unit_index != other.unit_index) return false;
      if (title != other.title) return false;
      if (subtitle != other.subtitle) return false;
      if (avatar_head_url != other.avatar_head_url) return false;
      if(!level_list_.Equals(other.level_list_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (unit_id != 0L) hash ^= unit_id.GetHashCode();
      if (unit_index != 0) hash ^= unit_index.GetHashCode();
      if (title.Length != 0) hash ^= title.GetHashCode();
      if (subtitle.Length != 0) hash ^= subtitle.GetHashCode();
      if (avatar_head_url.Length != 0) hash ^= avatar_head_url.GetHashCode();
      hash ^= level_list_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (unit_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(unit_id);
      }
      if (unit_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(unit_index);
      }
      if (title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(title);
      }
      if (subtitle.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(subtitle);
      }
      if (avatar_head_url.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(avatar_head_url);
      }
      level_list_.WriteTo(output, _repeated_level_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (unit_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(unit_id);
      }
      if (unit_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(unit_index);
      }
      if (title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(title);
      }
      if (subtitle.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(subtitle);
      }
      if (avatar_head_url.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(avatar_head_url);
      }
      level_list_.WriteTo(ref output, _repeated_level_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (unit_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(unit_id);
      }
      if (unit_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(unit_index);
      }
      if (title.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(title);
      }
      if (subtitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(subtitle);
      }
      if (avatar_head_url.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(avatar_head_url);
      }
      size += level_list_.CalculateSize(_repeated_level_list_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_UnitInfo other) {
      if (other == null) {
        return;
      }
      if (other.unit_id != 0L) {
        unit_id = other.unit_id;
      }
      if (other.unit_index != 0) {
        unit_index = other.unit_index;
      }
      if (other.title.Length != 0) {
        title = other.title;
      }
      if (other.subtitle.Length != 0) {
        subtitle = other.subtitle;
      }
      if (other.avatar_head_url.Length != 0) {
        avatar_head_url = other.avatar_head_url;
      }
      level_list_.Add(other.level_list_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            unit_id = input.ReadInt64();
            break;
          }
          case 16: {
            unit_index = input.ReadInt32();
            break;
          }
          case 26: {
            title = input.ReadString();
            break;
          }
          case 34: {
            subtitle = input.ReadString();
            break;
          }
          case 42: {
            avatar_head_url = input.ReadString();
            break;
          }
          case 50: {
            level_list_.AddEntriesFrom(input, _repeated_level_list_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            unit_id = input.ReadInt64();
            break;
          }
          case 16: {
            unit_index = input.ReadInt32();
            break;
          }
          case 26: {
            title = input.ReadString();
            break;
          }
          case 34: {
            subtitle = input.ReadString();
            break;
          }
          case 42: {
            avatar_head_url = input.ReadString();
            break;
          }
          case 50: {
            level_list_.AddEntriesFrom(ref input, _repeated_level_list_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_LevelInfo : pb::IMessage<SS_LevelInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_LevelInfo> _parser = new pb::MessageParser<SS_LevelInfo>(() => new SS_LevelInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_LevelInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.learnPath.CourseReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_LevelInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_LevelInfo(SS_LevelInfo other) : this() {
      level_id_ = other.level_id_;
      level_index_ = other.level_index_;
      title_ = other.title_;
      subtitle_ = other.subtitle_;
      level_type_ = other.level_type_;
      related_id_ = other.related_id_;
      session_list_ = other.session_list_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_LevelInfo Clone() {
      return new SS_LevelInfo(this);
    }

    /// <summary>Field number for the "level_id" field.</summary>
    public const int level_idFieldNumber = 1;
    private long level_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long level_id {
      get { return level_id_; }
      set {
        level_id_ = value;
      }
    }

    /// <summary>Field number for the "level_index" field.</summary>
    public const int level_indexFieldNumber = 2;
    private int level_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int level_index {
      get { return level_index_; }
      set {
        level_index_ = value;
      }
    }

    /// <summary>Field number for the "title" field.</summary>
    public const int titleFieldNumber = 3;
    private string title_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string title {
      get { return title_; }
      set {
        title_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "subtitle" field.</summary>
    public const int subtitleFieldNumber = 4;
    private string subtitle_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string subtitle {
      get { return subtitle_; }
      set {
        subtitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "level_type" field.</summary>
    public const int level_typeFieldNumber = 5;
    private global::Msg.basic.PB_LevelTypeEnum level_type_ = global::Msg.basic.PB_LevelTypeEnum.LTNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_LevelTypeEnum level_type {
      get { return level_type_; }
      set {
        level_type_ = value;
      }
    }

    /// <summary>Field number for the "related_id" field.</summary>
    public const int related_idFieldNumber = 6;
    private long related_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long related_id {
      get { return related_id_; }
      set {
        related_id_ = value;
      }
    }

    /// <summary>Field number for the "session_list" field.</summary>
    public const int session_listFieldNumber = 7;
    private static readonly pb::FieldCodec<global::Msg.learnPath.SS_SessionInfo> _repeated_session_list_codec
        = pb::FieldCodec.ForMessage(58, global::Msg.learnPath.SS_SessionInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.learnPath.SS_SessionInfo> session_list_ = new pbc::RepeatedField<global::Msg.learnPath.SS_SessionInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.learnPath.SS_SessionInfo> session_list {
      get { return session_list_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_LevelInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_LevelInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (level_id != other.level_id) return false;
      if (level_index != other.level_index) return false;
      if (title != other.title) return false;
      if (subtitle != other.subtitle) return false;
      if (level_type != other.level_type) return false;
      if (related_id != other.related_id) return false;
      if(!session_list_.Equals(other.session_list_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (level_id != 0L) hash ^= level_id.GetHashCode();
      if (level_index != 0) hash ^= level_index.GetHashCode();
      if (title.Length != 0) hash ^= title.GetHashCode();
      if (subtitle.Length != 0) hash ^= subtitle.GetHashCode();
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) hash ^= level_type.GetHashCode();
      if (related_id != 0L) hash ^= related_id.GetHashCode();
      hash ^= session_list_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (level_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(level_id);
      }
      if (level_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(level_index);
      }
      if (title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(title);
      }
      if (subtitle.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(subtitle);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) level_type);
      }
      if (related_id != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(related_id);
      }
      session_list_.WriteTo(output, _repeated_session_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (level_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(level_id);
      }
      if (level_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(level_index);
      }
      if (title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(title);
      }
      if (subtitle.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(subtitle);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) level_type);
      }
      if (related_id != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(related_id);
      }
      session_list_.WriteTo(ref output, _repeated_session_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (level_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(level_id);
      }
      if (level_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(level_index);
      }
      if (title.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(title);
      }
      if (subtitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(subtitle);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) level_type);
      }
      if (related_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(related_id);
      }
      size += session_list_.CalculateSize(_repeated_session_list_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_LevelInfo other) {
      if (other == null) {
        return;
      }
      if (other.level_id != 0L) {
        level_id = other.level_id;
      }
      if (other.level_index != 0) {
        level_index = other.level_index;
      }
      if (other.title.Length != 0) {
        title = other.title;
      }
      if (other.subtitle.Length != 0) {
        subtitle = other.subtitle;
      }
      if (other.level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        level_type = other.level_type;
      }
      if (other.related_id != 0L) {
        related_id = other.related_id;
      }
      session_list_.Add(other.session_list_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            level_id = input.ReadInt64();
            break;
          }
          case 16: {
            level_index = input.ReadInt32();
            break;
          }
          case 26: {
            title = input.ReadString();
            break;
          }
          case 34: {
            subtitle = input.ReadString();
            break;
          }
          case 40: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 48: {
            related_id = input.ReadInt64();
            break;
          }
          case 58: {
            session_list_.AddEntriesFrom(input, _repeated_session_list_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            level_id = input.ReadInt64();
            break;
          }
          case 16: {
            level_index = input.ReadInt32();
            break;
          }
          case 26: {
            title = input.ReadString();
            break;
          }
          case 34: {
            subtitle = input.ReadString();
            break;
          }
          case 40: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 48: {
            related_id = input.ReadInt64();
            break;
          }
          case 58: {
            session_list_.AddEntriesFrom(ref input, _repeated_session_list_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SessionInfo : pb::IMessage<SS_SessionInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SessionInfo> _parser = new pb::MessageParser<SS_SessionInfo>(() => new SS_SessionInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SessionInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.learnPath.CourseReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SessionInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SessionInfo(SS_SessionInfo other) : this() {
      session_id_ = other.session_id_;
      session_index_ = other.session_index_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SessionInfo Clone() {
      return new SS_SessionInfo(this);
    }

    /// <summary>Field number for the "session_id" field.</summary>
    public const int session_idFieldNumber = 1;
    private long session_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long session_id {
      get { return session_id_; }
      set {
        session_id_ = value;
      }
    }

    /// <summary>Field number for the "session_index" field.</summary>
    public const int session_indexFieldNumber = 2;
    private int session_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int session_index {
      get { return session_index_; }
      set {
        session_index_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SessionInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SessionInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (session_id != other.session_id) return false;
      if (session_index != other.session_index) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (session_id != 0L) hash ^= session_id.GetHashCode();
      if (session_index != 0) hash ^= session_index.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (session_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(session_id);
      }
      if (session_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(session_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (session_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(session_id);
      }
      if (session_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(session_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (session_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(session_id);
      }
      if (session_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(session_index);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SessionInfo other) {
      if (other == null) {
        return;
      }
      if (other.session_id != 0L) {
        session_id = other.session_id;
      }
      if (other.session_index != 0) {
        session_index = other.session_index;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            session_id = input.ReadInt64();
            break;
          }
          case 16: {
            session_index = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            session_id = input.ReadInt64();
            break;
          }
          case 16: {
            session_index = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

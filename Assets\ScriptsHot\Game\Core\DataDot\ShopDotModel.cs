﻿/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/27 15:06:16 星期一
# 功能：Nothing
****************************************************
*/
using  ScriptsHot.Game.Modules.Shop;
    
namespace Modules.DataDot
{
    //辅助基类，不用于直接埋点
    
    public class PayWallDotHelperBase:DataDotBase
    {
        public override string Event_name => "";//
        public string subscribe_status;
        public string member_type;
        public PayWallDotHelperBase()
        {
           
            //Deconstruct 解构赋值
            (this.subscribe_status, this.member_type) = PayWallDotHelper.SubscribeInfo;

            UnityEngine.Debug.Log("base");
        }
    }
    
    public class PayWallDotHelperBase0:PayWallDotHelperBase
    {
        public override string Event_name => "";//
        protected ShopModel _shopModel;  
        
        public string subscribe_type;//每个人被推送的策略（免费，限时折扣）不同
        public string subscribe_status;
        public string member_type;
        public string source_page;
        
        
        public PayWallDotHelperBase0():base()
        {   
            //Deconstruct 解构赋值
            //已继承 (this.subscribe_status, this.member_type) = PayWallDotHelper.SubscribeInfo;

            _shopModel = ModelManager.instance.GetModel<ShopModel>(ModelConsts.Shop);
            subscribe_type = _shopModel.IsDiscountType
                ? nameof(PaywallSubscribePageType.discount)
                : nameof(PaywallSubscribePageType.freetrial);
            
            source_page = PayWallDotHelper.LastSourcePage.ToString();
            UnityEngine.Debug.Log("base0");
        }
    }
    
    public class PayWallDotHelperBase1:PayWallDotHelperBase0
    {
        public override string Event_name => "";//
        public long users_diamond_amount;
        public long users_temp_diamond_amount;

        public PayWallDotHelperBase1():base()
        {
            //Deconstruct 解构赋值
            (this.users_diamond_amount,this.users_temp_diamond_amount) = PayWallDotHelper.PropertyInfo;
            UnityEngine.Debug.Log("base1");
        }
    }

    //商业化相关打点
  
    
    #region 基于base0 + base1 的埋点类 
    public class DataDotDiamondShop : PayWallDotHelperBase1
    {
        public override string Event_name => "Appear_Diamond_shop";
    }
    
    
    #endregion 
    
    #region 基于新table顺序的
    public class MembershipPage : PayWallDotHelperBase0
    {
        public override string Event_name => "Appear_membership_page";
    }
    
    public class MembershipPageTrial : PayWallDotHelperBase0
    {
        public override string Event_name => "Click_membership_page_trial";
       
    }
    
    public class MembershipPageClose : PayWallDotHelperBase0
    {
        public override string Event_name => "Click_membership_page_close";
    }

    #endregion 
    
    public class DataDotDiamondQuit : PayWallDotHelperBase0
    {
        public override string Event_name => "Click_Diamond_quit";

    }
    
    public class DataDotDiamondSubscribe : DataDotBase
    {
        public override string Event_name => "Click_Diamond_subscribe";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
        
        public float subscribe_price;
        public string price_currency;
        public string source_page;
    }
    
    public class DataDotDiamondMorePackage : DataDotBase
    {
        public override string Event_name => "Click_Diamond_more_package";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
    }
    
    public class DataDotDiamondPurchase : DataDotBase
    {
        public override string Event_name => "Click_Diamond_purchase";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
        
        public float subscribe_price;
        public string price_currency;
        
        
        public float Diamond_amount;
        public float Diamond_price;
 
    }
        
    public class DataDotShopService : DataDotBase
    {
        public override string Event_name => "Click_service";
    }
    
    public class DataDotShopPrivate : DataDotBase
    {
        public override string Event_name => "Click_private";
    }
    
    public class DataDotShopRestorePurchase : DataDotBase
    {
        public override string Event_name => "Click_Restore_purchase";
    }
    
    public class DataDotSubscriptionQuit : DataDotBase
    {
        public override string Event_name => "Click_Subscription_quit";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string clicked_member_type;
        public string subscribe_status;
        public string member_type;
    }
    
    public class DataDotSubscriptionYearly : DataDotBase
    {
        public override string Event_name => "Click_Subscription_yearly";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
        
        public string clicked_member_type;
    }
    
    public class DataDotSubscriptionMonthly : DataDotBase
    {
        public override string Event_name => "Click_Subscription_monthly";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
        
        public string clicked_member_type;
    }
    
    public class DataDotSubscriptionPremium : DataDotBase
    {
        public override string Event_name => "Click_Subscription_premium";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
        
        public string clicked_member_type;
    }
    
    public class DataDotSubscriptionLite : DataDotBase
    {
        public override string Event_name => "Click_Subscription_lite";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
        
        public string clicked_member_type;
    }
    
    public class DataDotSubscriptionPurchase : DataDotBase
    {
        public override string Event_name => "Click_Subscription_purchase";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
        
        public float subscribe_price;
        public string price_currency;
        public string source_page;
        
        public string clicked_member_type;
    }
    
    public class DataDotSubscriptionDescription : DataDotBase
    {
        public override string Event_name => "Click_Subscription_description";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
        
        public float subscribe_price;
        public float price_currency;
        
        public string clicked_member_type;
        public string Description;
        public string Text;
    }
    
    public class DataDotSubscriptionGuidanceNext : PayWallDotHelperBase0
    {
        public override string Event_name => "Click_Subscription_guidance_next";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
    }
    
    public class DataDotDiamondFreeQuit : PayWallDotHelperBase0
    {
        public override string Event_name => "Click_Diamond_free_quit";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
    }
    
    public class DataDotDiamondFreeTrial : DataDotBase
    {
        public override string Event_name => "Click_Diamond_free_trial";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
    }
    
    public class DataDotTrialGuidance : DataDotBase
    {
        public override string Event_name => "Appear_Trial_guidance";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
    }
    
    public class DataDotTrialGuidanceDescription : DataDotBase
    {
        public override string Event_name => "Click_Trial_guidance_description";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
        
        
        public float subscribe_price;
        public float price_currency;
        
        public string clicked_member_type;
        public string Description;
        public string Text;
    }
    
    public class DataDotTrialGuidanceQuit : DataDotBase
    {
        public override string Event_name => "Click_Trial_guidance_quit";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
    }
    
    public class DataDotTrialGuidanceNext : DataDotBase
    {
        public override string Event_name => "Click_Trial_guidance_next";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
    }
    
    public class DataDotTrialDetailQuit : DataDotBase
    {
        public override string Event_name => "Click_Trial_detail_quit";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;
    }
    
    public class DataDotTrialDetailNext : DataDotBase
    {
        public override string Event_name => "click_remind_page_get";
        public long users_diamond_amount;
        public long users_temp_diamond_amount;
        public string subscribe_status;
        public string member_type;

        public string source_page;
    }
    
    public class DataDotTrialChooseQuit : PayWallDotHelperBase1
    {
        public override string Event_name => "Click_Trial_choose_quit";
        
        public float subscribe_price;
        public string price_currency;
        public string clicked_member_type;
    }
    
    public class DataDotTrialChooseNext:PayWallDotHelperBase1
    {
        public override string Event_name => "Click_Trial_choose_next";
    
        public float subscribe_price;
        public string price_currency;
        public string clicked_member_type;
    }
    
    public class DataDotTrialChooseYearly : PayWallDotHelperBase0
    {
        public override string Event_name => "Click_Trial_choose_yearly";
    }
    
    public class DataDotTrialChooseQuarterly : PayWallDotHelperBase0
    {
        public override string Event_name => "Click_Trial_choose_quarterly";
    }
    
    public class DataDotTrialChooseMonthly : PayWallDotHelperBase0
    {
        public override string Event_name => "Click_Trial_choose_monthly";
    }
    
    public class DataDotTrialMorePackage : DataDotBase
    {
        public override string Event_name => "Click_Trial_more_package";
    }
    
    public class DataDotTrialService : DataDotBase
    {
        public override string Event_name => "Click_Trial_service";
    }
    
    public class DataDotTrialPrivate : DataDotBase
    {
        public override string Event_name => "Click_Trial_private";
    }
    
    public class DataDotTrialRestorePurchase : DataDotBase
    {
        public override string Event_name => "Click_Trial_restore_purchase";
    }
    
    public class DataDotPurchaseSucceed : PayWallDotHelperBase1
    {
        public override string Event_name => "Cut_Purchase_succeed";
        public string product_id;
    }
    
    public class DataDotPurchaseFailed : PayWallDotHelperBase1
    {
        public override string Event_name => "Cut_Purchase_failed";
        public string product_id;
    }
    
    //appear_Purchase_succeed_page
    public class DataDotAppearPurchaseSucceedPage : PayWallDotHelperBase0
    {
        public override string Event_name => "appear_Purchase_succeed_page";
    }
    
    public class DataDotFailedHold : DataDotBase
    {
        public override string Event_name => "Appear_Failed_hold";
    }
    
    public class DataDotFailedHoldNext : DataDotBase
    {
        public override string Event_name => "Click_Failed_hold_next";
    }
    
    public class DataDotDialogueCallback : DataDotBase
    {
        public override string Event_name => "Appear_Dialogue_callback";
        public long Dialogue_id;
    }
    
    public class DataDotDialogueCallbackComfirm : DataDotBase
    {
        public override string Event_name => "Click_Dialogue_callback_comfirm";
        public long Dialogue_id;
    }
    
    public class DataDotDialogueCallbackCancel : DataDotBase
    {
        public override string Event_name => "Click_Dialogue_callback_cancel";
        public long Dialogue_id;
    }

    public class ClickDiamondShopItem : DataDotBase
    {
        public override string Event_name => "Click_Diamond_shop_item";
        public string Item_type;  // 可选值：XP_boost, Streak_freeze, Unlimited_energy
    }

    public class StreakFreezePurchaseAppear : DataDotBase
    {
        public override string Event_name => "Streak_freeze_purchase";
        public long Inventory;  // 用户存量，弹出时用户有多少个连胜激冻
    }

    public class StreakFreezePurchaseClick : DataDotBase
    {
        public override string Event_name => "Click_Streak_freeze_purchase";
        public int Selected;  // 用户选择的商品位置，1 或 2
    }

    public class StreakFreezeQuitClick : DataDotBase
    {
        public override string Event_name => "Click_Streak_freeze_quit";
        public int Selected;  // 用户选择的商品位置，1 或 2
    }

    public class SubscribePopup : PayWallDotHelperBase0
    {
        public override string Event_name => "Appear_subscribe_popup";
        public string popup_title; //回传浮窗标题文本
        public string popup_text; //回传浮窗正文文本
    }

    public class PopupMainIcon : PayWallDotHelperBase0
    {
        public override string Event_name => "Click_popup_main_icon";
        
    }

    public class PopupClose : PayWallDotHelperBase0
    {
        public override string Event_name => "Click_popup_close";
        
    }

    //appear_monetization_onboarding_customize_page
    public class DotAppearMonetizationOnboardingCustomizePage : DataDotBase
    {
        public override string Event_name => "appear_monetization_onboarding_customize_page";
    }
  
    //click_monetization_onboarding_customize_page_next_button
    public class DotClickMonetizationOnboardingCustomizePageNextButton : DataDotBase
    {
        public override string Event_name => "click_monetization_onboarding_customize_page_next_button";
    }

    public class AppearTrialChoosePage : PayWallDotHelperBase0
    {
        public override string Event_name => "Appear_Trial_choose_page";
    }

    public class RemindPage : DataDotBase
    {
        public override string Event_name => "Appear_remind_page";
        public string subscribe_status;
    }

    public class RemindPageGet : DataDotBase
    {
        public override string Event_name => "Click_remind_page_get";
        //public string subscribe_status;
    }

    public class RemindPageClose : DataDotBase
    {
        public override string Event_name => "Click_remind_page_close";
        //public string subscribe_status;
    }

    public class AppearSubscribeBottomBar : PayWallDotHelperBase
    {
        public override string Event_name => "Appear_subscribe_bottom_bar";
        
    }
    
    public class ClickSubscribeBottomBar : PayWallDotHelperBase
    {
        public override string Event_name => "Click_subscribe_bottom_bar";
    }

    public class SubscribeBottomBarClose : PayWallDotHelperBase
    {
        public override string Event_name => "Click_subscribe_bottom_bar_close";
    }

    public class MembershipPageLater : DataDotBase
    {
        public override string Event_name => "Click_membership_page_later";
        public string subscribe_status;
    }
}
using System;
using System.Collections.Generic;
using UIBind.Chat;
using FairyGUI;
using Msg.basic;
using ScriptsHot.Game.Modules.Common;
using UnityEngine;
using Msg.learn_assist;
using Msg.question_process;

using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using ScriptsHot.Game.Modules.Task;
using ScriptsHot.Game.Modules.ReviewQuestion;
using ScriptsHot.Game.Modules.Guide;

public abstract class BaseChatCell
{
    public abstract UIBindT cell { get; }
    private ChatState owner;
    public int bubbleId;
    public ChatCellType cellType;
    private List<BaseUIEventVO> _events = new List<BaseUIEventVO>();
    private List<string> _timerAfterShow = new List<string>();

    public GComponent gCom;

    //public UIBindT bindT;
    protected Action<int> _action;
    protected ChatModel _chatModel;
    protected ChatUI _chatUI;
    protected RecordUI _recordUI;
    protected GuideModel _guidModel;
    protected RecommendCardModel _recommendCardModel;
    protected ReviewQuestionModel _reviewQuestionModel;
    protected ChatController _chatController;
    protected TaskController _taskController;
    protected CurrencyController _currencyController;
    public long dialogId;
    public long msgId;
    public long taskId;
    public long avatarId;
    public long sceneId;
    public long topicId;
    public bool isInit = false;
    private string _curNewWord;
    private NewWordComponent _curNewWordComponent;
    private static string CLIENT_ID_PREFIX = "ChatCell_";
    private List<GTweener> _tweeners = new();
    protected AudioClip _curClip;
    private bool IsNew = true;
    protected int roundId;

    public virtual void OnInit(GComponent gCom, ChatCellType cellType, int bubbleId, Action<int> action,int roundId)
    {
        this.roundId = roundId;
        // 根据传入的高度进行高度变化的逻辑
        this.cellType = cellType;
        this.bubbleId = bubbleId;
        this.gCom = gCom;
        this._action = action;
        this.AddUIEvent(cell.com.onSizeChanged, ChangeHeight);
        isInit = true;
        IsNew = true;
    }

    public void ChangeHeight()
    {
        // _action(bubbleId);
    }

    public void SetModel(ChatModel model,RecommendCardModel model1,ReviewQuestionModel mode2)
    {
        _chatModel = model;
        _reviewQuestionModel = mode2;

    }

    public void SetChatUI(ChatUI ui)
    {
        _chatUI = ui;
    }
    
    public void SetRecordUI(RecordUI ui)
    {
        _recordUI = ui;
    }
    
    public void SetGuideModel(GuideModel model)
    {
        _guidModel = model;
    }

    public void SetChatController(ChatController ctrl)
    {
        _chatController = ctrl;
    }

    public void SetCurrencyController(CurrencyController currencyController)
    {
        _currencyController = currencyController;
    }

    public void SetTaskController(TaskController ctrl)
    {
        _taskController = ctrl;
    }

    public ChatModel GetChatModel()
    {
        return _chatModel;
    }

    public void StopSound()
    {
    }

    public void ReqNewWordTrans(NewWordComponent cmpt, Vector2 wordPos)
    {
        _curNewWordComponent = cmpt;
        _curNewWord = cmpt.word;
        (ControllerManager.instance.GetController(ModelConsts.Chat) as ChatController).NewWordResp += RespNewWordTrans;

        var msg = new CS_ClickWordReq();
        msg.text = cmpt.word;
        msg.dialog_id = _chatModel.dialogId;
        msg.msg_id = msgId;
        msg.task_id = taskId;
        msg.avatar_id = avatarId;
        msg.scene_id = sceneId;
        msg.topic_id = topicId;
        msg.client_id = CLIENT_ID_PREFIX + bubbleId;
        _chatController.GetUI<ChatUI>(UIConsts.Chat).AddChildCom(cmpt);
        cmpt.xy = cmpt.GlobalToLocal(wordPos);
        MsgManager.instance.SendMsg(msg,NewWordTransFail);

        //新埋点：生词点击
        DataDotClickDialogueNewWords dot = new DataDotClickDialogueNewWords();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        DataDotMgr.Collect(dot);
    }

    //生词翻译失败
    private void NewWordTransFail(GRPCManager.ErrorType et,Google.Protobuf.IMessage msg)
    {
        (ControllerManager.instance.GetController(ModelConsts.Chat) as ChatController).NewWordResp -= RespNewWordTrans;
        if (_curNewWordComponent != null && !_curNewWordComponent.isDisposed)
        {
            _curNewWordComponent.Dispose();
        }
        DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
        dataDotCutApiCase.Dialogue_id = _chatModel.dialogId;
        dataDotCutApiCase.Task_id = _chatController.curTaskId;
        dataDotCutApiCase.Dialogue_step = _chatModel.GetStepIdByRoundId(_chatModel.curRoundId);
        dataDotCutApiCase.Task_mode = (int)_chatModel.chatMode;
        dataDotCutApiCase.Api_address = "CS_ClickWordReq";
        dataDotCutApiCase.Cut_mode = 3;
        DataDotMgr.Collect(dataDotCutApiCase);
        VFDebug.LogError("BaseChatCell NewWordTransFail errorType : " + et);
    }

    public void RespNewWordTrans(SC_ClickWordAck msg)
    {
        (ControllerManager.instance.GetController(ModelConsts.Chat) as ChatController).NewWordResp -= RespNewWordTrans;
        //return;
        if (msg.data.client_id != CLIENT_ID_PREFIX + bubbleId)
            return;
        //服务器给了errorcode
        if (msg.code != 0)
        {
            Debug.Log("SC_ClickWordAck is error");
            NewWordTransFail(GRPCManager.ErrorType.None,null);
            return;
        }
        if (_curNewWordComponent != null && !_curNewWordComponent.isDisposed)
        {
            if (_curNewWordComponent.word.ToLower() == msg.data.word.ToLower())
            {
                if (_chatController.isPlayNewWordAble && !GSoundManager.instance.IsPlaying("Avatar") && !GSoundManager.instance.IsPlaying("Flow"))
                {
                    TTSManager.instance.PlayTTS(msg.data.audio_id);
                    ASRManager.instance.StopASRWithCancel();
                    _chatController.CancelRecordUI();
                }
                _curNewWordComponent.SetWord(msg.data.translation);
                _curNewWordComponent.parentTfExt.AppendNewWord(msg.data.word, msg.data.count);
                _curNewWordComponent.parentTfExt.CreateNewWord();
            }
        }
    }

    protected void SetScoreLevel(ChatBubbleScoreNumButton comNum, PB_ScoreLevelEnum scoreLevel, long score)
    {
        comNum.com.visible = true;
        switch (scoreLevel)
        {
            case PB_ScoreLevelEnum.SNone:
                comNum.ctrlStatus.selectedPage = "noneScore";
                comNum.tfNoneScore.text = score.ToString();
                break;
            case PB_ScoreLevelEnum.Low:
                comNum.ctrlStatus.selectedPage = "lowScore";
                comNum.tfLowScore.text = score.ToString();
                break;
            case PB_ScoreLevelEnum.Mid:
                comNum.ctrlStatus.selectedPage = "middleScore";
                comNum.tfMiddleScore.text = score.ToString();
                break;
            case PB_ScoreLevelEnum.High:
                comNum.ctrlStatus.selectedPage = "highScore";
                comNum.tfHighScore.text = score.ToString();
                break;
            default:
                comNum.ctrlStatus.selectedPage = "noneScore";
                comNum.tfNoneScore.text = score.ToString();
                Debug.LogError("PB_ScoreLevelEnum 为空");
                break;
        }

        if (score < 60)
        {
            SoundManger.instance.PlayUI("chat_score_below_60");
        }else
        {
            SoundManger.instance.PlayUI("chat_score_above_60");
        }
        
    }

    public void SetSelfAudio(AudioClip clip)
    {
        _curClip = clip;
    }

    //发送问题的点赞
    protected void SendFeedbackMsg(bool isLike)
    {
        return;
        CS_QuestionFeedbackReq msg = new CS_QuestionFeedbackReq();
        msg.dialog_id = _chatModel.dialogId;
        PB_Question question = _chatModel.GetQuestionByBubbleId(bubbleId);
        msg.question_id = question.question_id;
        string referenceContent = QuestionParser.GetReferenceContent(question);
        msg.content = referenceContent;
        msg.choice = isLike ? 1 : 2;
        MsgManager.instance.SendMsg(msg);
    }

    //重听avatar的话 建议  生词不走这个方法
    protected void PlayTTSRecord(TTSManager.AudioChannel audioChannel,Action callback = null,Action playStartCallBack = null)
    {
        //SpeechManager.instance.StopTTS();
        _taskController.OnClosePanel();
        ASRManager.instance.StopASRWithCancel();
        _chatController.CancelRecordUI();
        long ttsRecordId = _chatModel.GetTTSRecordIdByBubbleId(bubbleId);
        // TTSManager.AudioChannel audioChannel = (cellType != ChatCellType.Suggest && cellType != ChatCellType.Scaffold)
        //     ? TTSManager.AudioChannel.Avatar
        //     : TTSManager.AudioChannel.TTS;
        TTSManager.instance.PlayTTS(ttsRecordId, callback, true, audioChannel,1,null,playStartCallBack);
    }
    

    protected void StopTTSRecord()
    {
        long ttsRecordId = _chatModel.GetTTSRecordIdByBubbleId(bubbleId);
        TTSManager.instance.StopTTS(ttsRecordId);
    }

    //重听用户的话  生词不走这个方法
    protected void PlaySpeechRecord()
    {
        _taskController.OnClosePanel();
        ASRManager.instance.StopASRWithCancel();
        _chatController.CancelRecordUI();
        if (_curClip != null)
        {
            GSoundManager.instance.PlayTTS(_curClip);
        }

        //新埋点：对话中重听
        DataDotClickDialogueRelisten dot = new DataDotClickDialogueRelisten();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        dot.Dialogue_round = roundId;
       
        dot.Task_id = _chatController.curTaskId;
        PB_DialogMode chatMode = _chatModel.chatMode;
        dot.Task_type = "Task";
        if (chatMode == PB_DialogMode.WarmupPractice || chatMode == PB_DialogMode.Intensify || chatMode == PB_DialogMode.MistakeExercise)
        {
            dot.Task_type = "Warmup";
            PB_Question question = _reviewQuestionModel.CurrentQuestion().question;
            dot.Knowledge_id = question.question_id;
            dot.Knowledge_index = question.question_round_id;
            dot.Dialogue_round = _reviewQuestionModel.questionIndex;
            dot.Dialogue_type = _guidModel.markInfo.is_question_auto ? "Auto" : "Manual";
        }
        else if (chatMode == PB_DialogMode.Career)
        {
            dot.Task_type = "Free_talk";
        }
        DataDotMgr.Collect(dot);
    }

    public virtual void OnNewWordRefreshed(Dictionary<string, int> wordList)
    {
    }

    public void PlayCutInAni(bool isRight = false)
    {
        gCom.pivot = isRight ? new Vector2(1, 1) : new Vector2(0, 0);
        gCom.scale = new Vector2(0.3f, 0.3f);
        var tweener = gCom.TweenScale(new Vector2(1f, 1f), 0.2f).OnComplete(() => gCom.pivot = new Vector2(0, 0));
        _tweeners.Add(tweener);
    }

    public void PlayAnswerBadAni()
    {
        SoundManger.instance.PlayUI("chat_feedback_negative");
        var tweener = gCom.TweenRotate(5f, 0.05f).OnComplete(() => gCom.TweenRotate(-5f, 0.1f).OnComplete(() =>
            gCom.TweenRotate(2f, 0.1f).OnComplete(() => gCom.TweenRotate(-2f, 0.1f).OnComplete(() => gCom.TweenRotate(0f, 0.1f)))));
        _tweeners.Add(tweener);
        _chatController.GetChatState().ShowSuggestion();
        _chatController.GetUI<ChatUI>(UIConsts.Chat).ResetCombo();
    }

    public void PlayAnswerGoodAni(long score)
    {
        SoundManger.instance.PlayUI("chat_feedback_positive");
        var sp = UIPackage.CreateObject("buildin", "SpinePanel") as SpinePanelExtension;
        var spineCom = gCom.GetChild("comSpine").asCom;
        if (spineCom == null)
        {
            Debug.LogError($" chatCellType :{cellType} is not hava comSpine");
            return;
        }

        spineCom.AddChild(sp);
        sp.xy = new Vector2((gCom.width - sp.width) / 2f, (gCom.height - sp.height) / 2f);
        sp.SetModel(ResUtils.GetSpinePath("spAnswerGood"));
        sp.PlayWithCallback("animation", () =>
        {
            if (score >= 80)
            {
                SoundManger.instance.PlayUI("combo");
                PlayComboPerfectAni();
            }
            else if (score >= 60)
            {
                SoundManger.instance.PlayUI("combo");
                PlayComboGreatAni();
            }
            spineCom.RemoveChild(sp);
            sp.Dispose();
            _chatController.GetChatState().ShowSuggestion();
        });
    }
    
    public void PlayComboGreatAni()
    {
        _chatController.GetUI<ChatUI>(UIConsts.Chat).PlayComboAni("great", false);
    }
    
    public void PlayComboPerfectAni()
    {
        _chatController.GetUI<ChatUI>(UIConsts.Chat).PlayComboAni("prefec", true);
    }


    public virtual void UpdateData(object data)
    {
    }

    public virtual void SetData(object data)
    {
    }

    protected string RegisterTimer(TimerCallBack callback, int delay, int repeatCount = 1)
    {
        var key = TimerManager.instance.RegisterTimer(callback, delay, repeatCount);
        this._timerAfterShow.Add(key);
        return key;
    }

    protected void UnRegisterTimer(string key)
    {
        int index = this._timerAfterShow.IndexOf(key);
        if (index > -1)
            this._timerAfterShow.RemoveAt(index);
        TimerManager.instance.UnRegisterTimer(key);
    }

    private void RemoveTimerAfterShow()
    {
        foreach (var key in this._timerAfterShow)
        {
            TimerManager.instance.UnRegisterTimer(key);
        }
        this._timerAfterShow.Clear();
    }

    protected void AddUIEvent(FairyGUI.EventListener eventListener, FairyGUI.EventCallback0 callback, int buttonClickInterval = 300)
    {
        this.AddUIEvent(eventListener, callback, null, buttonClickInterval);
    }

    protected void AddUIEvent(FairyGUI.EventListener eventListener, FairyGUI.EventCallback1 callback, int buttonClickInterval = 300)
    {
        this.AddUIEvent(eventListener, null, callback, buttonClickInterval);
    }

    //没有联合类型好鸡儿麻烦
    private void AddUIEvent(FairyGUI.EventListener eventListener,
        FairyGUI.EventCallback0 callback0, FairyGUI.EventCallback1 callback1, int buttonClickInterval)
    {
        for (int i = 0; i < this._events.Count; i++)
        {
            BaseUIEventVO vo = this._events[i];
            if (vo.eventListener == eventListener && (vo.callback0 == callback0 || vo.callback1 == callback1))
            {
                return;
            }
        }

        //
        BaseUIEventVO eventVO = new BaseUIEventVO();
        eventVO.eventListener = eventListener;
        eventVO.callback0 = callback0;
        eventVO.callback1 = callback1;
        eventVO.eventTriggerInterval = eventListener.type == "onClick" ? buttonClickInterval : -1;
        eventVO.listenerCallback = eventContext =>
        {
            //按钮点击间隔判定
            if (eventVO.eventTriggerInterval > -1)
            {
                var currTime = TimeExt.currTime;
                if (currTime - eventVO.eventTriggerTime < eventVO.eventTriggerInterval)
                    return;
                eventVO.eventTriggerTime = currTime;
            }

            if (callback0 != null)
                callback0();
            if (callback1 != null)
                callback1(eventContext);
        };
        this._events.Add(eventVO);
        eventListener.Add(eventVO.listenerCallback);
    }

    public void RemoveUIEvent()
    {
        for (int i = 0; i < this._events.Count; i++)
        {
            BaseUIEventVO vo = this._events[i];
            vo.eventListener.Remove(vo.listenerCallback);
            vo.eventListener = null;
            vo.callback0 = null;
            vo.callback1 = null;
            vo.listenerCallback = null;
        }

        this._events.Clear();
        // bindT = null;
    }

    public abstract void ResetCell();

    public void OnRecycle()
    {
        while (_tweeners.Count > 0)
        {
            _tweeners[0].Kill();
            _tweeners.RemoveAt(0);
        }

        _curClip = null;
        RemoveEvent();
        ResetCell();
    }

    public virtual void Dispose()
    {
        while (_tweeners.Count > 0)
        {
            _tweeners[0].Kill();
            _tweeners.RemoveAt(0);
        }

        RemoveEvent();
        ResetCell();
    }

    public void RemoveEvent()
    {
        RemoveUIEvent();
        RemoveTimerAfterShow();
    }

    protected void ShowGoldNum(long score, GComponent gObj)
    {
        if (score > 80 && IsNew)//同一轮次之会出现一次
        {
            SoundManger.instance.PlayUI("GetGold");
            //超过80 金币加3
            _chatController.GetUI<CommonGetGoldTipsUI>(UIConsts.CommonGetGold).Open(3,gObj,new Vector2(40,40));
            IsNew = false;
        }
    }

}
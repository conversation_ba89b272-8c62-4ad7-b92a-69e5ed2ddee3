// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/base.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/base.proto</summary>
  public static partial class BaseReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/base.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static BaseReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Chtwcm90b2J1Zi9leHBsb3JlL2Jhc2UucHJvdG8iVAoYUEJfRW1vdGlvbkFu",
            "YWx5c2lzUmVzdWx0EjgKDXNlbnRlbmNlX2xpc3QYASADKAsyIS5QQl9FbW90",
            "aW9uQW5hbHlzaXNTZW50ZW5jZVJlc3VsdCKLAQogUEJfRW1vdGlvbkFuYWx5",
            "c2lzU2VudGVuY2VSZXN1bHQSDAoEdGV4dBgBIAEoCRITCgtmdW5jdGlvblRh",
            "ZxgCIAEoCRIUCgxpbnRlbnNpdHlUYWcYAyABKAkSFwoPc3RhcnRUaW1lU2Vj",
            "b25kGAQgASgCEhUKDWVuZFRpbWVTZWNvbmQYBSABKAIq1Q0KE1BCX0V4cGxv",
            "cmVfTXNnSWRNYXASFQoRRU9fTXNnX0lkX1Vua25vd24QABIlCh9TQ19SZWNv",
            "bW1lbmRTd2l0Y2hFbnRpdHlfTWFwX0lEEJvyARIsCiZTQ19FeHBsb3JlRG93",
            "bk1zZ0ZvclNlcnZlckJhc2ljX01hcF9JRBD/8gESKgokU0NfRXhwbG9yZURv",
            "d25Nc2dGb3JIZWFydGJlYXRfTWFwX0lEEIDzARIrCiVTQ19EaWFsb2dEb3du",
            "TXNnRm9yQXZhdGFyUmVwbHlfTWFwX0lEEOPzARI0Ci5TQ19EaWFsb2dEb3du",
            "TXNnRm9yQXZhdGFyUmVwbHlUcmFuc2xhdGVfTWFwX0lEEOTzARIuCihTQ19E",
            "aWFsb2dEb3duTXNnRm9yQXZhdGFyUmVwbHlUVFNfTWFwX0lEEOXzARIwCipT",
            "Q19EaWFsb2dEb3duTXNnRm9yVXNlclJlcGx5RXhhbXBsZV9NYXBfSUQQ5vMB",
            "EjkKM1NDX0RpYWxvZ0Rvd25Nc2dGb3JVc2VyUmVwbHlFeGFtcGxlVHJhbnNs",
            "YXRlX01hcF9JRBDn8wESMwotU0NfRGlhbG9nRG93bk1zZ0ZvclVzZXJSZXBs",
            "eUV4YW1wbGVUVFNfTWFwX0lEEOjzARIjCh1TQ19EaWFsb2dEb3duTXNnRm9y",
            "QVNSX01hcF9JRBDp8wESKAoiU0NfRGlhbG9nRG93bk1zZ0ZvckJpekV2ZW50",
            "X01hcF9JRBDq8wESKAoiU0NfRGlhbG9nRG93bk1zZ0ZvckZlZWRiYWNrX01h",
            "cF9JRBDr8wESNAouU0NfRGlhbG9nRG93bk1zZ0ZvclRhc2tHb2FsU3RhdHVz",
            "Q2hhbmdlX01hcF9JRBDs8wESJgogU0NfRGlhbG9nRG93bk1zZ0ZvckFkdmlj",
            "ZV9NYXBfSUQQ7fMBEjUKL1NDX1VzZXJTZXR0aW5nRG93bk1zZ0ZvclNhdmVV",
            "c2VyU2V0dGluZ3NfTWFwX0lEEMX0ARIxCitTQ19Vc2VyQ2hhdERvd25Nc2dG",
            "b3JVc2VyUmVjb2duaXppbmdfTWFwX0lEEI32ARIwCipTQ19Vc2VyQ2hhdERv",
            "d25Nc2dGb3JVc2VyUmVjb2duaXplZF9NYXBfSUQQjvYBEjYKMFNDX1VzZXJD",
            "aGF0RG93bk1zZ0Zvck90aGVyVXNlclJlY29nbml6aW5nX01hcF9JRBCP9gES",
            "NQovU0NfVXNlckNoYXREb3duTXNnRm9yT3RoZXJVc2VyUmVjb2duaXplZF9N",
            "YXBfSUQQkPYBEjkKM1NDX1VzZXJDaGF0RG93bk1zZ0Zvck90aGVyVXNlclJl",
            "cGx5VHJhbnNsYXRlX01hcF9JRBCR9gESMgosU0NfVXNlckNoYXREb3duTXNn",
            "Rm9yVXNlclJlcGx5RXhhbXBsZV9NYXBfSUQQkvYBEjsKNVNDX1VzZXJDaGF0",
            "RG93bk1zZ0ZvclVzZXJSZXBseUV4YW1wbGVUcmFuc2xhdGVfTWFwX0lEEJP2",
            "ARIqCiRTQ19Vc2VyQ2hhdERvd25Nc2dGb3JCaXpFdmVudF9NYXBfSUQQlPYB",
            "Ei0KJ1NDX1VzZXJDaGF0RG93bk1zZ1VzZXJDaGF0U3RhdHVzX01hcF9JRBC+",
            "9gESJgogU0NfVXNlckNoYXREb3duTXNnRm9yRXhpdF9NYXBfSUQQ7/YBEigK",
            "IlNDX01hdGNoaW5nRG93bl9NYXRjaFN0YXR1c19NYXBfSUQQ8/YBEioKJFND",
            "X01hdGNoaW5nRG93bl9NYXRjaGVkUmVzdWx0X01hcF9JRBD09gESKAoiU0Nf",
            "TWF0Y2hpbmdEb3duX01hdGNoRmFpbGVkX01hcF9JRBD19gESMwotU0NfT25i",
            "b2FyZGluZ0NoYXREb3duTXNnRm9yQXZhdGFyUmVwbHlfTWFwX0lEENX3ARI8",
            "CjZTQ19PbmJvYXJkaW5nQ2hhdERvd25Nc2dGb3JBdmF0YXJSZXBseVRyYW5z",
            "bGF0ZV9NYXBfSUQQ1vcBEjYKMFNDX09uYm9hcmRpbmdDaGF0RG93bk1zZ0Zv",
            "ckF2YXRhclJlcGx5VFRTX01hcF9JRBDX9wESKwolU0NfT25ib2FyZGluZ0No",
            "YXREb3duTXNnRm9yQVNSX01hcF9JRBDY9wESMAoqU0NfT25ib2FyZGluZ0No",
            "YXREb3duTXNnRm9yQml6RXZlbnRfTWFwX0lEENn3ARIyCixTQ19PbmJvYXJk",
            "aW5nQ2hhdERvd25Nc2dGb3JTZXR0bGVtZW50X01hcF9JRBDa9wESIgocU0Nf",
            "U2tpcE9uYm9hcmRpbmdDaGF0X01hcF9JRBC5+AEqYwoYUEJfRXhwbG9yZV9T",
            "b3VyY2VDaGFubmVsEhEKDUVPX1NDX1VOS05PV04QABIOCgpFT19FWFBMT1JF",
            "EAESEQoNRU9fTEVBUk5fUEFUSBACEhEKDUVPX09OQk9BUkRJTkcQAyqqAQoS",
            "UEJfRXhwbG9yZV9CaXpDb2RlEhcKE0VPX0JJWl9DT0RFX1VOS05PV04QABIY",
            "ChNFT19CSVpfQ09ERV9TVUNDRVNTEMgBEh4KGkVPX0JJWl9DT0RFX0lOVEVS",
            "TkFMX0VSUk9SEAESHQoZRU9fQklaX0NPREVfSU5WQUxJRF9QQVJBTRACEiIK",
            "HkVPX0JJWl9DT0RFX1NUVF9SRUNPR05JWkVfRkFJTBADKo8BChRQQl9FeHBs",
            "b3JlX0NlZnJMZXZlbBIRCg1FT19DTF9VTktOT1dOEAASDAoIRU9fQ0xfQTEQ",
            "ARIMCghFT19DTF9BMhACEgwKCEVPX0NMX0IxEAMSDAoIRU9fQ0xfQjIQBBIM",
            "CghFT19DTF9DMRAFEgwKCEVPX0NMX0MyEAYSEAoMRU9fQ0xfUFJFX0ExEAcq",
            "dwokUEJfRXhwbG9yZV9Vc2VyU2V0dGluZ19TcGVha2luZ1NwZWVkEhQKEEVP",
            "X1VTX1NTX1VOS05PV04QABIRCg1FT19VU19TU19TTE9XEAESEwoPRU9fVVNf",
            "U1NfTUVESVVNEAISEQoNRU9fVVNfU1NfRkFTVBADKmkKK1BCX0V4cGxvcmVf",
            "VXNlclNldHRpbmdfQXV0b1RyYW5zbGF0ZURpc3BsYXkSFQoRRU9fVVNfQVRE",
            "X1VOS05PV04QABIQCgxFT19VU19BVERfT04QARIRCg1FT19VU19BVERfT0ZG",
            "EAIqYQomUEJfRXhwbG9yZV9Vc2VyU2V0dGluZ19CYWNrZ3JvdW5kTXVzaWMS",
            "FAoQRU9fVVNfQk1fVU5LTk9XThAAEg8KC0VPX1VTX0JNX09OEAESEAoMRU9f",
            "VVNfQk1fT0ZGEAJCKloadmZfcHJvdG9idWYvc2VydmVyL2V4cGxvcmWqAgtN",
            "c2cuZXhwbG9yZWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.explore.PB_Explore_MsgIdMap), typeof(global::Msg.explore.PB_Explore_SourceChannel), typeof(global::Msg.explore.PB_Explore_BizCode), typeof(global::Msg.explore.PB_Explore_CefrLevel), typeof(global::Msg.explore.PB_Explore_UserSetting_SpeakingSpeed), typeof(global::Msg.explore.PB_Explore_UserSetting_AutoTranslateDisplay), typeof(global::Msg.explore.PB_Explore_UserSetting_BackgroundMusic), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_EmotionAnalysisResult), global::Msg.explore.PB_EmotionAnalysisResult.Parser, new[]{ "sentence_list" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_EmotionAnalysisSentenceResult), global::Msg.explore.PB_EmotionAnalysisSentenceResult.Parser, new[]{ "text", "functionTag", "intensityTag", "startTimeSecond", "endTimeSecond" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  ///
  /// 消息id的映射表
  /// </summary>
  public enum PB_Explore_MsgIdMap {
    [pbr::OriginalName("EO_Msg_Id_Unknown")] EO_Msg_Id_Unknown = 0,
    /// <summary>
    /// 31001~31100 推荐
    /// </summary>
    [pbr::OriginalName("SC_RecommendSwitchEntity_Map_ID")] SC_RecommendSwitchEntity_Map_ID = 31003,
    /// <summary>
    /// 31101~31200 长连接
    /// </summary>
    [pbr::OriginalName("SC_ExploreDownMsgForServerBasic_Map_ID")] SC_ExploreDownMsgForServerBasic_Map_ID = 31103,
    /// <summary>
    /// Explore服务心跳下行消息
    /// </summary>
    [pbr::OriginalName("SC_ExploreDownMsgForHeartbeat_Map_ID")] SC_ExploreDownMsgForHeartbeat_Map_ID = 31104,
    /// <summary>
    /// 31201~31300 对话
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAvatarReply_Map_ID")] SC_DialogDownMsgForAvatarReply_Map_ID = 31203,
    /// <summary>
    /// 对话下行消息 - Avatar回复翻译
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAvatarReplyTranslate_Map_ID")] SC_DialogDownMsgForAvatarReplyTranslate_Map_ID = 31204,
    /// <summary>
    /// 对话下行消息 - Avatar回复TTS
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAvatarReplyTTS_Map_ID")] SC_DialogDownMsgForAvatarReplyTTS_Map_ID = 31205,
    /// <summary>
    /// 对话下行消息 - 用户回复示例
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForUserReplyExample_Map_ID")] SC_DialogDownMsgForUserReplyExample_Map_ID = 31206,
    /// <summary>
    /// 对话下行消息 - 用户回复示例翻译
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForUserReplyExampleTranslate_Map_ID")] SC_DialogDownMsgForUserReplyExampleTranslate_Map_ID = 31207,
    /// <summary>
    /// 对话下行消息 - 用户回复示例TTS
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForUserReplyExampleTTS_Map_ID")] SC_DialogDownMsgForUserReplyExampleTTS_Map_ID = 31208,
    /// <summary>
    /// 对话下行消息 - 用户语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForASR_Map_ID")] SC_DialogDownMsgForASR_Map_ID = 31209,
    /// <summary>
    /// 对话下行消息 - 业务事件
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForBizEvent_Map_ID")] SC_DialogDownMsgForBizEvent_Map_ID = 31210,
    /// <summary>
    /// 对话下行消息 - 反馈结果
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForFeedback_Map_ID")] SC_DialogDownMsgForFeedback_Map_ID = 31211,
    /// <summary>
    /// 对话下行消息 - 任务目标状态变化
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForTaskGoalStatusChange_Map_ID")] SC_DialogDownMsgForTaskGoalStatusChange_Map_ID = 31212,
    /// <summary>
    /// 对话下行消息 - Advice
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAdvice_Map_ID")] SC_DialogDownMsgForAdvice_Map_ID = 31213,
    /// <summary>
    /// 31301~31400 用户设置
    /// </summary>
    [pbr::OriginalName("SC_UserSettingDownMsgForSaveUserSettings_Map_ID")] SC_UserSettingDownMsgForSaveUserSettings_Map_ID = 31301,
    /// <summary>
    /// 31501~31600 用户聊天
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserRecognizing_Map_ID")] SC_UserChatDownMsgForUserRecognizing_Map_ID = 31501,
    /// <summary>
    /// 用户聊天下行消息 - 用户语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserRecognized_Map_ID")] SC_UserChatDownMsgForUserRecognized_Map_ID = 31502,
    /// <summary>
    /// 用户聊天下行消息 - 他人语音识别过程结果
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForOtherUserRecognizing_Map_ID")] SC_UserChatDownMsgForOtherUserRecognizing_Map_ID = 31503,
    /// <summary>
    /// 用户聊天下行消息 - 他人语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForOtherUserRecognized_Map_ID")] SC_UserChatDownMsgForOtherUserRecognized_Map_ID = 31504,
    /// <summary>
    /// 用户聊天下行消息 - 他人回复翻译
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForOtherUserReplyTranslate_Map_ID")] SC_UserChatDownMsgForOtherUserReplyTranslate_Map_ID = 31505,
    /// <summary>
    /// 用户聊天下行消息 - 用户回复示例
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserReplyExample_Map_ID")] SC_UserChatDownMsgForUserReplyExample_Map_ID = 31506,
    /// <summary>
    /// 用户聊天下行消息 - 用户回复示例翻译
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserReplyExampleTranslate_Map_ID")] SC_UserChatDownMsgForUserReplyExampleTranslate_Map_ID = 31507,
    /// <summary>
    /// 用户聊天下行消息 - 业务事件
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForBizEvent_Map_ID")] SC_UserChatDownMsgForBizEvent_Map_ID = 31508,
    /// <summary>
    /// 用户聊天下行消息 - 用户聊天状态
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgUserChatStatus_Map_ID")] SC_UserChatDownMsgUserChatStatus_Map_ID = 31550,
    /// <summary>
    /// 用户聊天下行消息 - 退出聊天
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForExit_Map_ID")] SC_UserChatDownMsgForExit_Map_ID = 31599,
    /// <summary>
    /// 31601~31700 撮合
    /// </summary>
    [pbr::OriginalName("SC_MatchingDown_MatchStatus_Map_ID")] SC_MatchingDown_MatchStatus_Map_ID = 31603,
    /// <summary>
    /// 撮合下行消息 - 撮合成功结果
    /// </summary>
    [pbr::OriginalName("SC_MatchingDown_MatchedResult_Map_ID")] SC_MatchingDown_MatchedResult_Map_ID = 31604,
    /// <summary>
    /// 撮合下行消息 - 撮合失败结果
    /// </summary>
    [pbr::OriginalName("SC_MatchingDown_MatchFailed_Map_ID")] SC_MatchingDown_MatchFailed_Map_ID = 31605,
    /// <summary>
    /// 31701~31800 onboarding对话
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForAvatarReply_Map_ID")] SC_OnboardingChatDownMsgForAvatarReply_Map_ID = 31701,
    /// <summary>
    /// onboarding对话下行消息 - Avatar回复翻译
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForAvatarReplyTranslate_Map_ID")] SC_OnboardingChatDownMsgForAvatarReplyTranslate_Map_ID = 31702,
    /// <summary>
    /// onboarding对话下行消息 - Avatar回复TTS
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForAvatarReplyTTS_Map_ID")] SC_OnboardingChatDownMsgForAvatarReplyTTS_Map_ID = 31703,
    /// <summary>
    /// onboarding对话下行消息 - 用户语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForASR_Map_ID")] SC_OnboardingChatDownMsgForASR_Map_ID = 31704,
    /// <summary>
    /// onboarding对话下行消息 - 业务事件
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForBizEvent_Map_ID")] SC_OnboardingChatDownMsgForBizEvent_Map_ID = 31705,
    /// <summary>
    /// onboarding对话下行消息 - 对话结算
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForSettlement_Map_ID")] SC_OnboardingChatDownMsgForSettlement_Map_ID = 31706,
    /// <summary>
    /// 31801~31900 onboarding
    /// </summary>
    [pbr::OriginalName("SC_SkipOnboardingChat_Map_ID")] SC_SkipOnboardingChat_Map_ID = 31801,
  }

  /// <summary>
  ///
  /// 来源渠道
  /// </summary>
  public enum PB_Explore_SourceChannel {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_SC_UNKNOWN")] EO_SC_UNKNOWN = 0,
    /// <summary>
    /// 探索Tab
    /// </summary>
    [pbr::OriginalName("EO_EXPLORE")] EO_EXPLORE = 1,
    /// <summary>
    /// 学习路径
    /// </summary>
    [pbr::OriginalName("EO_LEARN_PATH")] EO_LEARN_PATH = 2,
    /// <summary>
    /// onboarding
    /// </summary>
    [pbr::OriginalName("EO_ONBOARDING")] EO_ONBOARDING = 3,
  }

  /// <summary>
  /// 业务状态码枚举
  /// </summary>
  public enum PB_Explore_BizCode {
    [pbr::OriginalName("EO_BIZ_CODE_UNKNOWN")] EO_BIZ_CODE_UNKNOWN = 0,
    /// <summary>
    /// 成功
    /// </summary>
    [pbr::OriginalName("EO_BIZ_CODE_SUCCESS")] EO_BIZ_CODE_SUCCESS = 200,
    /// <summary>
    /// 内部错误
    /// </summary>
    [pbr::OriginalName("EO_BIZ_CODE_INTERNAL_ERROR")] EO_BIZ_CODE_INTERNAL_ERROR = 1,
    /// <summary>
    /// 参数错误
    /// </summary>
    [pbr::OriginalName("EO_BIZ_CODE_INVALID_PARAM")] EO_BIZ_CODE_INVALID_PARAM = 2,
    /// <summary>
    /// 语音识别失败
    /// </summary>
    [pbr::OriginalName("EO_BIZ_CODE_STT_RECOGNIZE_FAIL")] EO_BIZ_CODE_STT_RECOGNIZE_FAIL = 3,
  }

  /// <summary>
  ///
  /// CEFR难度等级
  /// </summary>
  public enum PB_Explore_CefrLevel {
    [pbr::OriginalName("EO_CL_UNKNOWN")] EO_CL_UNKNOWN = 0,
    [pbr::OriginalName("EO_CL_A1")] EO_CL_A1 = 1,
    [pbr::OriginalName("EO_CL_A2")] EO_CL_A2 = 2,
    [pbr::OriginalName("EO_CL_B1")] EO_CL_B1 = 3,
    [pbr::OriginalName("EO_CL_B2")] EO_CL_B2 = 4,
    [pbr::OriginalName("EO_CL_C1")] EO_CL_C1 = 5,
    [pbr::OriginalName("EO_CL_C2")] EO_CL_C2 = 6,
    [pbr::OriginalName("EO_CL_PRE_A1")] EO_CL_PRE_A1 = 7,
  }

  /// <summary>
  ///
  /// 语速
  /// </summary>
  public enum PB_Explore_UserSetting_SpeakingSpeed {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_US_SS_UNKNOWN")] EO_US_SS_UNKNOWN = 0,
    /// <summary>
    /// 慢
    /// </summary>
    [pbr::OriginalName("EO_US_SS_SLOW")] EO_US_SS_SLOW = 1,
    /// <summary>
    /// 中等
    /// </summary>
    [pbr::OriginalName("EO_US_SS_MEDIUM")] EO_US_SS_MEDIUM = 2,
    /// <summary>
    /// 快
    /// </summary>
    [pbr::OriginalName("EO_US_SS_FAST")] EO_US_SS_FAST = 3,
  }

  /// <summary>
  ///
  /// 自动显示翻译
  /// </summary>
  public enum PB_Explore_UserSetting_AutoTranslateDisplay {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_US_ATD_UNKNOWN")] EO_US_ATD_UNKNOWN = 0,
    /// <summary>
    /// 开
    /// </summary>
    [pbr::OriginalName("EO_US_ATD_ON")] EO_US_ATD_ON = 1,
    /// <summary>
    /// 关
    /// </summary>
    [pbr::OriginalName("EO_US_ATD_OFF")] EO_US_ATD_OFF = 2,
  }

  /// <summary>
  ///
  /// 背景音乐
  /// </summary>
  public enum PB_Explore_UserSetting_BackgroundMusic {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_US_BM_UNKNOWN")] EO_US_BM_UNKNOWN = 0,
    /// <summary>
    /// 开
    /// </summary>
    [pbr::OriginalName("EO_US_BM_ON")] EO_US_BM_ON = 1,
    /// <summary>
    /// 关
    /// </summary>
    [pbr::OriginalName("EO_US_BM_OFF")] EO_US_BM_OFF = 2,
  }

  #endregion

  #region Messages
  /// <summary>
  ///*
  /// 情感分析结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_EmotionAnalysisResult : pb::IMessage<PB_EmotionAnalysisResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_EmotionAnalysisResult> _parser = new pb::MessageParser<PB_EmotionAnalysisResult>(() => new PB_EmotionAnalysisResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_EmotionAnalysisResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.BaseReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisResult(PB_EmotionAnalysisResult other) : this() {
      sentence_list_ = other.sentence_list_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisResult Clone() {
      return new PB_EmotionAnalysisResult(this);
    }

    /// <summary>Field number for the "sentence_list" field.</summary>
    public const int sentence_listFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.explore.PB_EmotionAnalysisSentenceResult> _repeated_sentence_list_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.explore.PB_EmotionAnalysisSentenceResult.Parser);
    private readonly pbc::RepeatedField<global::Msg.explore.PB_EmotionAnalysisSentenceResult> sentence_list_ = new pbc::RepeatedField<global::Msg.explore.PB_EmotionAnalysisSentenceResult>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.explore.PB_EmotionAnalysisSentenceResult> sentence_list {
      get { return sentence_list_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_EmotionAnalysisResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_EmotionAnalysisResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!sentence_list_.Equals(other.sentence_list_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= sentence_list_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      sentence_list_.WriteTo(output, _repeated_sentence_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      sentence_list_.WriteTo(ref output, _repeated_sentence_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += sentence_list_.CalculateSize(_repeated_sentence_list_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_EmotionAnalysisResult other) {
      if (other == null) {
        return;
      }
      sentence_list_.Add(other.sentence_list_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            sentence_list_.AddEntriesFrom(input, _repeated_sentence_list_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            sentence_list_.AddEntriesFrom(ref input, _repeated_sentence_list_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// 情感分析单句结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_EmotionAnalysisSentenceResult : pb::IMessage<PB_EmotionAnalysisSentenceResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_EmotionAnalysisSentenceResult> _parser = new pb::MessageParser<PB_EmotionAnalysisSentenceResult>(() => new PB_EmotionAnalysisSentenceResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_EmotionAnalysisSentenceResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.BaseReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisSentenceResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisSentenceResult(PB_EmotionAnalysisSentenceResult other) : this() {
      text_ = other.text_;
      functionTag_ = other.functionTag_;
      intensityTag_ = other.intensityTag_;
      startTimeSecond_ = other.startTimeSecond_;
      endTimeSecond_ = other.endTimeSecond_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisSentenceResult Clone() {
      return new PB_EmotionAnalysisSentenceResult(this);
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 1;
    private string text_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "functionTag" field.</summary>
    public const int functionTagFieldNumber = 2;
    private string functionTag_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string functionTag {
      get { return functionTag_; }
      set {
        functionTag_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "intensityTag" field.</summary>
    public const int intensityTagFieldNumber = 3;
    private string intensityTag_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string intensityTag {
      get { return intensityTag_; }
      set {
        intensityTag_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "startTimeSecond" field.</summary>
    public const int startTimeSecondFieldNumber = 4;
    private float startTimeSecond_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float startTimeSecond {
      get { return startTimeSecond_; }
      set {
        startTimeSecond_ = value;
      }
    }

    /// <summary>Field number for the "endTimeSecond" field.</summary>
    public const int endTimeSecondFieldNumber = 5;
    private float endTimeSecond_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float endTimeSecond {
      get { return endTimeSecond_; }
      set {
        endTimeSecond_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_EmotionAnalysisSentenceResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_EmotionAnalysisSentenceResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (text != other.text) return false;
      if (functionTag != other.functionTag) return false;
      if (intensityTag != other.intensityTag) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(startTimeSecond, other.startTimeSecond)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(endTimeSecond, other.endTimeSecond)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (functionTag.Length != 0) hash ^= functionTag.GetHashCode();
      if (intensityTag.Length != 0) hash ^= intensityTag.GetHashCode();
      if (startTimeSecond != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(startTimeSecond);
      if (endTimeSecond != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(endTimeSecond);
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (text.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(text);
      }
      if (functionTag.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(functionTag);
      }
      if (intensityTag.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(intensityTag);
      }
      if (startTimeSecond != 0F) {
        output.WriteRawTag(37);
        output.WriteFloat(startTimeSecond);
      }
      if (endTimeSecond != 0F) {
        output.WriteRawTag(45);
        output.WriteFloat(endTimeSecond);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (text.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(text);
      }
      if (functionTag.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(functionTag);
      }
      if (intensityTag.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(intensityTag);
      }
      if (startTimeSecond != 0F) {
        output.WriteRawTag(37);
        output.WriteFloat(startTimeSecond);
      }
      if (endTimeSecond != 0F) {
        output.WriteRawTag(45);
        output.WriteFloat(endTimeSecond);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (functionTag.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(functionTag);
      }
      if (intensityTag.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(intensityTag);
      }
      if (startTimeSecond != 0F) {
        size += 1 + 4;
      }
      if (endTimeSecond != 0F) {
        size += 1 + 4;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_EmotionAnalysisSentenceResult other) {
      if (other == null) {
        return;
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      if (other.functionTag.Length != 0) {
        functionTag = other.functionTag;
      }
      if (other.intensityTag.Length != 0) {
        intensityTag = other.intensityTag;
      }
      if (other.startTimeSecond != 0F) {
        startTimeSecond = other.startTimeSecond;
      }
      if (other.endTimeSecond != 0F) {
        endTimeSecond = other.endTimeSecond;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            text = input.ReadString();
            break;
          }
          case 18: {
            functionTag = input.ReadString();
            break;
          }
          case 26: {
            intensityTag = input.ReadString();
            break;
          }
          case 37: {
            startTimeSecond = input.ReadFloat();
            break;
          }
          case 45: {
            endTimeSecond = input.ReadFloat();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            text = input.ReadString();
            break;
          }
          case 18: {
            functionTag = input.ReadString();
            break;
          }
          case 26: {
            intensityTag = input.ReadString();
            break;
          }
          case 37: {
            startTimeSecond = input.ReadFloat();
            break;
          }
          case 45: {
            endTimeSecond = input.ReadFloat();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

﻿using FairyGUI;
using Msg.explore;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Explore;
using UnityEngine;

namespace UIBind.Explore.Item.ItemComponentLogic
{
    /// <summary>
    /// 脚手架
    /// </summary>
    public class ExploreScaffoldLogic:ItemComponentLogicBase
    {
        public ExploreScaffold Com;
        public TextFieldExtension TfOrigin =>  Com.comMain.tfOrigin as TextFieldExtension;

        private bool _effecting = false;
        
        private string _bubbleId = string.Empty;
        
        private ExploreItemUI _item;
        
        private string _str;
        private string _trans;
        
        private long _dialogId;
        private long _round;

        private bool _isMain = true;
        public override void Init()
        {
            base.Init();
            Com.effectSound.com.onClick.Add(OnEffectSound);
            Com.btnTranslate.com.onClick.Add(OnTranslate);
            this.Com.comTrans.txtName.SetKey("ui_explore_title_example");
            this.Com.comMain.txtName.SetKey("ui_explore_title_example");
            
            SetSoundBtnVisible(true);
            
            TfOrigin.SetFormat(Color.white, 32);
        }
        public void SetInfo(long dialogId,long round)
        {
            _dialogId = dialogId;
            _round = round;
        }
        
        public void SetParentItem(ExploreItemUI item)
        {
            _item = item;
        }
        
        public void ShowTxt(string bubbleId,string str,string translateStr)
        {
            _str = str;
            _trans = translateStr;
            SetTransformVisible(false);
            ExploreNewWordData newWordData = new ExploreNewWordData();
            newWordData.ui = _ui;
            newWordData.avatarId = 1;
            newWordData.dialogId = _controller.CurTaskId;
            newWordData.bubbleId = bubbleId;
            _translateWordComponent.SetData(newWordData);
            
            VFDebug.Log("ShowTxt  ::"+ _bubbleId);
            
            _bubbleId = bubbleId;
            var textContent = new RichContent()
            {
                Content = _str,
                IsLast = true,
            };
            TfOrigin.Reset();
            TfOrigin.OnClickNewWord += _translateWordComponent.ReqNewWordTrans;
            TfOrigin.AppendContent(textContent).Display(ShowMode.Normal);

            Com.comTrans.txtTran.text = _trans;
            Com.comMain.txtTran.text = _trans;
            
            Com.ctrl.selectedIndex = 0;
            Com.btnTranslate.ctrl.selectedIndex = 0;
            
            Com.comMain.com.visible = true;
            Com.comTrans.com.visible = false;

            _isMain = true;
            
            if (_controller.Model.AutoTranslateDisplay == PB_Explore_UserSetting_AutoTranslateDisplay.EO_US_ATD_ON)
            {
                //自动显示翻译
                SetTransformVisible(true);
            }
        }

        public void Show(string txt,long taskId,long dialogue_id,long roundId)
        {
            APPEAR_EXPLORE_EXAMPLE dotData = new APPEAR_EXPLORE_EXAMPLE();
            dotData.example_text = txt;
            dotData.task_id = taskId;
            if(dialogue_id > 0)
                dotData.dialogue_id = dialogue_id.ToString();
            dotData.dialogue_round = roundId;
            DataDotMgr.Collect(dotData);
            
            _controller.ShowBulbScaffold();
        }

        public void SetSoundBtnVisible(bool value)
        {
            Com.effectSound.com.visible = value;
        }

        private void OnEffectSound()
        {
            if (this._controller.IfCanMicrophone) return;
            if (_effecting) return;
            _item.AudioOver();
            ulong audioId = this._controller.Model.GetAudioIdByBubbleId(_bubbleId);
            Com.effectSound.ctrlState.selectedIndex = 1;
            Notifier.instance.SendNotification(NotifyConsts.ExploreScaffoldAudio,audioId);
            DotAudioBtn();
            
            if (!_isMain)
            {
                OnTranslate();
            }
        }
        
        private void OnTranslate()
        {
            if (_effecting) return;
            if (Com.btnTranslate.ctrl.selectedIndex == 0)
            {
                Com.btnTranslate.ctrl.selectedIndex = 1;

                SetTransformVisible(true);
                // ShowTranslate();
                DotTranslateBtn();
            }
            else
            {
                Com.btnTranslate.ctrl.selectedIndex = 0;
                // ShowMain();
                SetTransformVisible(false);
            }
        }
        
        public void SetTransformVisible(bool value)
        {
            Com.comMain.txtTran.visible = value;
            if (value)
            {
                Com.btnTranslate.ctrl.selectedPage = "translate";
            }
            else
            {
                Com.btnTranslate.ctrl.selectedPage = "normal";
            }
        }

        public void SetEffectVisible(bool visibleValue,bool isClick)
        {
            if (!isClick)
            {
                Com.com.alpha = 1;
                Com.com.visible = visibleValue;
                return;
            }

            _effecting = true;
            int fadeValue = visibleValue ? 1 : 0;
            Com.com.alpha = visibleValue ? 0 : 1;
            Com.com.visible = true;
            GTween.Kill(Com.com);
            Com.com.TweenFade(fadeValue, ExploreConst.CellAphla).OnComplete(() =>
            {
                _effecting = false;
                Com.com.visible = visibleValue;
            });
        }

        public void ShowTranslate()
        {
            return;
            _isMain = false;
            _effecting = true;
            Com.ctrl.selectedIndex = 1;
            Com.comMain.com.TweenFade(0, ExploreConst.CellAphla).OnComplete(() =>
            {
                Com.comMain.com.visible = false;
                Com.comTrans.com.visible = true;
                Com.comTrans.com.alpha = 0;
                Com.comTrans.com.TweenFade(1, ExploreConst.CellAphla).OnComplete(() =>
                {
                    _effecting = false;
                });
            });
        }
        
        public void ShowMain()
        {
            return;
            _isMain = true;
            _effecting = true;
            Com.ctrl.selectedIndex = 0;
            Com.comTrans.com.TweenFade(0, ExploreConst.CellAphla).OnComplete(() =>
            {
                Com.comMain.com.visible = true;
                Com.comMain.com.alpha = 0;
                Com.comTrans.com.visible = false;
                Com.comMain.com.TweenFade(1, ExploreConst.CellAphla).OnComplete(() =>
                {
                    _effecting = false;
                });
            });
        }
        
        public void AudioOver()
        {
            Com.effectSound.ctrlState.selectedIndex = 0;
        }
        
        private void DotTranslateBtn()
        {
            CLICK_EXPLORE_TRANSLATION_BUTTON data = new CLICK_EXPLORE_TRANSLATION_BUTTON();
            data.previuos_langauge = I18N.inst.MotherLanguageStr;
            data.target_language = I18N.inst.ForeignLanguageStr;
            data.root_bubble = "example";
            data.previuos_text = _str;
            data.target_text = _trans;
            
            data.task_id = _controller.CurTaskId;
            data.dialogue_id = _dialogId;   
            data.dialogue_round = _round;
            DataDotMgr.Collect(data);
            
        }
        
        private void DotAudioBtn()
        {
            CLICK_EXPLORE_PLAY_BUTTON data = new CLICK_EXPLORE_PLAY_BUTTON();
            data.root_bubble = "example";
            data.bubble_text = _str;
            
            data.task_id = _controller.CurTaskId;
            data.dialogue_id = _dialogId;   
            data.dialogue_round = _round;
            DataDotMgr.Collect(data);
            
        }
    }
}
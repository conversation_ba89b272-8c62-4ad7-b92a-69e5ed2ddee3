﻿using ScriptsHot.Game.Modules.Explore;

namespace UIBind.Explore.Item.ItemComponentLogic
{
    /// <summary>
    /// player cell
    /// </summary>
    public class ExplorepPlayerItemLogic:ItemComponentLogicBase
    {
        public ExplorepPlayerItem Com;
        
        private long _dialogId;
        private long _round;
        public override void Init()
        {
            base.Init();
            this.Com.grpTitle.visible = false;
            this.Com.txtName.SetKey("ui_explore_title_translation");
            this.Com.sp.visible = false;
        }
        
        public void SetInfo(long dialogId,long round)
        {
            _dialogId = dialogId;
            _round = round;
        }

        public void ShowPlayerTxt(string str)
        {
            this.Com.tfOrigin.text = str.TrimEnd();
            Dot();
        }

        public void Dot()
        {
            APPEAR_EXPLORE_USER_BUBBLE data = new APPEAR_EXPLORE_USER_BUBBLE();
            data.mic_status = "manual";
            data.user_text = this.Com.tfOrigin.text;
            data.task_id = _controller.CurTaskId;
            data.dialogue_id = _dialogId;
            data.dialogue_round = _round;
            DataDotMgr.Collect(data);
        }
    }
}
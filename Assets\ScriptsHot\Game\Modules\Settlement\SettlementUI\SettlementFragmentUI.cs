﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Timers;
using FairyGUI;
using Modules.DataDot;
using Msg.basic;
using UIBind.Settlement;
using Unity.Mathematics;
using UnityEngine;
using Object = UnityEngine.Object;

namespace ScriptsHot.Game.Modules.Settlement
{
    public class SettlementFragmentUI : BaseUI<SettlementFragPanel>
    {
        public override void OnBackBtnClick()
        {
            Next();
        }

        public SettlementFragmentUI(string name) : base(name)
        {
        }

        private SettlementModel SettleModel => GetModel<SettlementModel>(ModelConsts.Settlement);

        public override string uiLayer => UILayerConsts.Top;
        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(ui.compBottom.btnStart.onClick, OnClickNext);
            AddUIEvent(ui.btnStamina.onClick, OnBtnStaminaClicked);
            AddUIEvent(ui.btnResign.onClick, OnBtnResignCliced);
            
            ui.compSettleList.DoInit();
            ui.compBottom.tfReward.SetKey("common_continue");
            ui.compBottom.state.selectedIndex = 1;
            AddEffect();
        }

        private List<CompSettleList.SettleListStruct> _settleStrList = new();
        
        protected override void OnShow()
        {
            SoundManger.instance.PlayUI("question_settle");

            //高级震动模拟
#if Unity_IOS && !UNITY_EDITOR
            FairyGUI_Timers.inst.StartCoroutine(this.PlayHapticSequence());
#endif
            //取record_list[0]是约定好的
            var settlementData = GetModel<SettlementModel>(ModelConsts.Settlement).SettlementData;
            if (settlementData == null)
            {
                VFDebug.LogError("无结算数据");
                return;
            }
            
            _settleStrList.Clear();
            _settleStrList.Add(new CompSettleList.SettleListStruct()
            {
                IconType = CompSettleList.SettleIconType.Time,
                Number = int.Parse(settlementData.task_cost_time),
            });
            _settleStrList.Add(new CompSettleList.SettleListStruct()
            {
                IconType = CompSettleList.SettleIconType.Exp,
                Number = settlementData.experience,
            });
            _settleStrList.Add(new CompSettleList.SettleListStruct()
            {
                IconType = CompSettleList.SettleIconType.CorrectRate,
                Number = Mathf.CeilToInt(settlementData.correct_rate * 100),
            });
            ui.compSettleList.DoShowSettleList(_settleStrList);
            ui.compSettleMark.visible = false;
            TimerManager.instance.RegisterTimer(
                (a) => ui.compSettleMark.DoShowSettleMark(SettleModel.DialogResultListData.need_show_feedback), 1500);
            if (SettleModel.DialogResultListData.need_show_feedback)
            {
                DotAppearSessionResultFeedbackPanel dot = new DotAppearSessionResultFeedbackPanel();
                dot.session_record_id = SettleModel.SettlementData.session_record_id;
                dot.level_type = SettleModel.DialogResultListData.level_type.ToString();
                DataDotMgr.Collect(dot);
            }
            
            ui.tfText.SetKey(SettleModel.DicSpineTypeByView[settlementData.spine_type]);
            ui.spineType.selectedIndex = math.max(0, (int)settlementData.spine_type - 1);
            
            if (settlementData.dialog_source == PB_DialogSourceEnum.DialogSourceReSignEntrance)
            {
                ui.state.SetSelectedPage("resign");
                ui.sp_flow_resign.spineAnimation.AnimationState.SetAnimation(0, "1", false).Complete += entry =>
                {
                    ui.sp_flow_resign.spineAnimation.AnimationState.SetAnimation(0, "2", true);
                };
                ui.btnResign.SetKey("ui_fragmentPractice_sign_btn_quit");
                ui.tfCongraResign.SetKey("freeTalk_Reward");
            }
            else if(settlementData.dialog_source == PB_DialogSourceEnum.DialogSourceMenuBarForStamina)
            {
                ui.state.SetSelectedPage("stamina");
                ui.sp_flow_stamina.spineAnimation.AnimationState.SetAnimation(0, "1", false).Complete += entry =>
                {
                    ui.sp_flow_stamina.spineAnimation.AnimationState.SetAnimation(0, "2", true);
                };
                ui.btnStamina.SetKey("ui_fragmentPractice_stamina_btn_quit");
                ui.tfCongraStamina.SetKey("freeTalk_Reward");
                ui.tfStaminaCnt.text = "+" + Math.Floor((decimal)settlementData.award_stamina / 20);
            }
            else
            {
                if (SettleModel.DialogResultListData.is_skip_success)
                    ui.state.SetSelectedPage("normal");
                else
                {
                    DotAppearJumpFalse dot = new DotAppearJumpFalse();
                    DataDotMgr.Collect(dot);
                    
                    ui.state.SetSelectedPage("false");
                    ui.compFalse.tfText.SetKey("ui_main_path_jump_false");
                } 
                //跳关失败
                GameEntry.MainPathC.OnSettlementSkipOver(SettleModel.DialogResultListData.is_skip_success);
            }
            
            RefreshAppearDot();
        }

        private void RefreshAppearDot()
        {
            var settlementData = SettleModel.SettlementData;
            
            DotAppearSessionResultPage dot = new DotAppearSessionResultPage();
            dot.time = settlementData.task_cost_time;
            dot.exp = settlementData.experience;
            dot.acc = settlementData.correct_rate;
            dot.session_record_id = settlementData.session_record_id;
            dot.level_type = SettleModel.DialogResultListData.level_type.ToString();
            DataDotMgr.Collect(dot);
        }

        private IEnumerator PlayHapticSequence()
        {

            float timeStep = 0f;
            float maxTime = 2f;
            VibrationManager.Ins.Vibrate(0.3f, 0.5f, 0.6f);
            yield return null;
            timeStep += Time.deltaTime;

            while (timeStep <maxTime)
            {
                if (timeStep < 0.6f)
                {
                    VibrationManager.Ins.TransientHaptic(
                        intensity: 0.3f + (0.5f * (timeStep / 0.6f)), // 动态强度
                        sharpness: 0.5f + (0.4f * (timeStep / 0.6f))
                    );
                }
                else if (timeStep < 0.6f + 0.8f)
                {
                    VibrationManager.Ins.TransientHaptic(
                        intensity: 1.0f,
                        sharpness: 0.9f - (0.2f * Mathf.Abs(timeStep - 1.0f) / 0.4f) // 峰值处锐度最高
                    );
                }
                else if (timeStep < 0.6f + 0.8f + 0.4f)
                {
                    VibrationManager.Ins.TransientHaptic(
                        intensity: 0.8f - (0.6f * (timeStep - 1.4f) / 0.4f),
                        sharpness: 0.7f - (0.4f * (timeStep - 1.4f) / 0.4f)
                    );
                }
                else {
                    VibrationManager.Ins.TransientHaptic(
                        intensity: 0.4f,
                        sharpness: 0.7f + (0.3f * (timeStep - 1.8f) / 0.2f)
                    );
                }
                yield return null;
                timeStep += Time.deltaTime;
            }
            
        }
        protected override bool isFullScreen => true;

        private void OnClickNext()
        {
            if (SettleModel.DialogResultListData != null && !SettleModel.DialogResultListData.is_skip_success)
            {
                DotClickJumpFalseContinue dot = new DotClickJumpFalseContinue();
                DataDotMgr.Collect(dot);
            }
            else
            {
                DotClickSessionResultContinue dot = new DotClickSessionResultContinue();
                dot.session_record_id = SettleModel.SettlementData.session_record_id;
                dot.level_type = SettleModel.DialogResultListData?.level_type.ToString();
                DataDotMgr.Collect(dot);

                if (SettleModel.DialogResultListData != null && SettleModel.DialogResultListData.need_show_feedback)
                {
                    DotClickSessionResultFeedbackPanelButton backDot = new DotClickSessionResultFeedbackPanelButton();
                    backDot.session_record_id = SettleModel.SettlementData.session_record_id;
                    backDot.level_type = SettleModel.DialogResultListData.level_type.ToString();
                    backDot.button_type = ui.compSettleMark.GetCurrentSelect();
                    DataDotMgr.Collect(backDot);
                }
            }
            
            Next();
        }

        private void OnBtnStaminaClicked()
        {

            Next();
        }

        private void OnBtnResignCliced()
        {
            Next();
        }
        private void Next() {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            GetController<SettlementController>(ModelConsts.Settlement).ShowNextView( ()=> { Hide(); });
            
        }

        private GameObject _fxEffectObject;
        private void AddEffect()
        {
            GameObject prefab = GResManager.instance.LoadPrefab("ConffetiFX");
            _fxEffectObject = GameObject.Instantiate(prefab);
            GoWrapper wrapper = new GoWrapper(_fxEffectObject);
            ui.holder.SetNativeObject(wrapper);
            
            _fxEffectObject.transform.localPosition = Vector3.zero;
            _fxEffectObject.transform.localScale = new Vector3(1.5f, 1.5f, 1);
            _fxEffectObject.transform.Find("Conffeti_Boost").localScale = new Vector3(100, 100, 1f);
            _fxEffectObject.transform.Find("Conffeti_Stay").localScale = new Vector3(100, 100, 1f);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            if (_fxEffectObject != null)
            {
                Object.Destroy(_fxEffectObject);
                _fxEffectObject = null;
            }            
        }
    }
}
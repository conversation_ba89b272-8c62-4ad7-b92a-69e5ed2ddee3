using System;
using Msg.basic;
using Msg.incentive;
using ScriptsHot.Game.Modules.Guide;
using ScriptsHot.Game.Modules.Scene.Level.Homepage;
using ScriptsHot.Game.Modules.Shop;
using ScriptsHot.Game.Modules.Sign;
using ScriptsHot.Game.Modules.Stamina;
using UnityEngine.UI;
using UIBind.Main;
using UnityEngine;
using ScriptsHot.Game.Modules.Managers;
using Modules.DataDot;
    
public enum CenterHomeBgType
{
    Undefined,
    LessonView,
    ChatRoomBg
}

/* 1 受理外部调用
 * 2 组织首页相关的多个组件之间的调用组合问题 => 让几个组件 彼此不需要关心 其他人
 * 3 每个组件，均不涉及其自身内部视图细节逻辑
 * 
 */
public class HomepageController : BaseController
{
    private CenterHomeBgType _centerHomeBgType;
    private CenterHomeLevel _centerHomeLevel = null;
    private MainHeaderUI _headerUI = null;
    //private HomepageUI _hpUI = null;//siderUI+ { quickBtn +practiceBtn}
    internal MultiTabHomepageUI _mtHpUI = null;

    // private LearnPathModel _learnPathModel = null;
    private SignModel _signModel = null;
    //private HomepageUI hp3DUI;
    //private HomepageLevel hpLevel;

    private bool _isInitializedData = false;
    private string _initilizingTimerKey;
    
    private Action _commercialActionCache;
    private SC_GetIncentiveDataForPortalAck _commercialMsgCache;

    private ChangeChatAvatarParam validChatAvatarParamCache;
    

    public HomepageController() : base(ModelConsts.Homepage) { }

    public static void Click1() {

    }

    public static void Click2() {

    }


    #region 基础接口  !!课程转首页-不要修改这部分
    public override void OnInit()
    {
        Notifier.instance.RegisterNotification(NotifyConsts.ChangeLanguage, OnChangeLanguage);
        // 获取首页信息
        MsgManager.instance.RegisterCallBack<SC_GetIncentiveDataForPortalAck>(OnGetIncentiveDataForPortalAck);
    }

    public override void OnUIInit()
    {
        this.RegisterUI(new CenterHomeUI(UIConsts.CenterHome));
        this.RegisterUI(new MultiTabHomepageUI(UIConsts.MultiTabHomepage));
        this.RegisterUI(new TestTabUI(UIConsts.TestTabUI));
        ListItemMainBtnList.Bind();


        Notifier.instance.RegisterNotification(NotifyConsts.SceneLoadCompletePre, OnSceneLoadCompletePre);
        Notifier.instance.RegisterNotification(NotifyConsts.SceneUnloadComplete, OnSceneUnloadComplete);

        Notifier.instance.RegisterNotification(NotifyConsts.LearnPathNodeDataUpdate, OnLearnPathNodeDataUpdate);

        Notifier.instance.RegisterNotification(NotifyConsts.EnterDialog, OnEnterDialog);
        Notifier.instance.RegisterNotification(NotifyConsts.ExitDialog, OnExitDialog);
        
        //TodoX: 待清理事件
        // Notifier.instance.RegisterNotification(LearnPathCallEvent.ShowChapterProgress, ShowChapterUI);
        //
        // Notifier.instance.RegisterNotification(LearnPathCallEvent.OnObtainChatStartPageInfoComplete, OnObtainChatStartPageInfoComplete);                  //enter扉页事件
        // Notifier.instance.RegisterNotification(LearnPathCallEvent.OnExitChatStartAndCancel, OnExitChatStartAndCancel);  //扉页cancel+关闭
        //
        // Notifier.instance.RegisterNotification(LearnPathCallEvent.OnExitChatStartAndStart, OnExitChatStartAndStart);    //扉页进入chat +关闭
    }

    public override void OnEnterGame()
    {
        //这个时间节点
        VFDebug.Log("homepagectrl onEnterGame");
        ReqGetIncentiveData();
    }

    private void OnGetIncentiveDataForPortalAck(SC_GetIncentiveDataForPortalAck msg)
    {
        if (msg.code == 0)
        {
            var mainController = GetController<MainController>(ModelConsts.Main);
            GetModel<MainModel>(ModelConsts.Main).SetIncentiveData(msg.data);
            var shopModel = GetModel<ShopModel>(ModelConsts.Shop);
            shopModel.SetMemberInfo(msg.data.homepage_economic_info.member_info);
            if (msg.data.homepage_economic_info.member_info != null)
            {
                shopModel.member_status = msg.data.homepage_economic_info.member_info.SubscribeStatus.ToString();
                shopModel.member_type = msg.data.homepage_economic_info.member_info.member_type.ToString();
                PayWallDotHelper.SubscribeInfo.SetInfo( shopModel.member_status,shopModel.member_type );//todo 未来可能不需要shopmodel记录
            }

            GetController<WhatsappController>(ModelConsts.Whatsapp).SetDatas(msg.data.whatsapp_info);
       
            GetModel<StaminaModel>(ModelConsts.Stamina).SetStaminaData(msg.data.stamina_data);

            if (msg.data.homepage_economic_info != null)
            {
                SendNotification(NotifyConsts.OnGetIncentiveData);
                GetController<ShopController>(ModelConsts.Shop).ReqShopInfo();
                if (mainController.IsState<GameStateSceneIdle>())
                {
                    ShowCommercialUI(msg);
                    GetController<ShopController>(ModelConsts.Shop).ShowHomepagePlanUI(msg);
                }
                else
                {
                    mainController.RegisterActionCache(GameState.SceneIdle, () =>
                    {
                        ShowCommercialUI(msg);
                        GetController<ShopController>(ModelConsts.Shop).ShowHomepagePlanUI(msg);
                    });
                }
            }

            if (msg.data.homepage_friend_streak_info != null)
            {
                if (msg.data.homepage_friend_streak_info.pending_invite_list != null && msg.data.homepage_friend_streak_info.pending_invite_list.Count > 0)
                {
                    GetUI<FriendStreakInvitedUI>(UIConsts.FriendStreakInvitedUI).PopShow(PopUIManager.Priority.Friend , msg.data.homepage_friend_streak_info.pending_invite_list);
                }

                if (msg.data.homepage_friend_streak_info.streak_failed_info != null)
                {
                    if (msg.data.homepage_friend_streak_info.streak_failed_info.streak_failed_type == PB_FriendStreakState.OtherNotCheckin)
                    {
                        GetUI<FriendStreakOthersBreakUI>(UIConsts.FriendStreakOthersBreakUI)
                            .PopShow(PopUIManager.Priority.Friend ,msg.data.homepage_friend_streak_info.streak_failed_info.streak_failed_friend_list);
                    }
                    else if (msg.data.homepage_friend_streak_info.streak_failed_info.streak_failed_type == PB_FriendStreakState.SelfNotCheckin)
                    {
                        GetUI<FriendStreakSelfBreakUI>(UIConsts.FriendStreakSelfBreakUI)
                            .PopShow(PopUIManager.Priority.Friend ,msg.data.homepage_friend_streak_info.streak_failed_info.streak_failed_friend_list.Count);
                    }
                }
            }

            if (msg.data.system_operation_config != null && msg.data.system_operation_config.is_show_app_rating)
            {
                GetUI<AppRatingUI>(UIConsts.AppRatingUI).PopShow(PopUIManager.Priority.Middle);
            }

            SendNotification(NotifyConsts.OnIncentiveDataRefreshed);
            var mainHeader = GetUI<MainHeaderUI>(UIConsts.MainHeader);
            if (mainHeader.isShow)
            {
                mainHeader.RefershHeadBar(true, true);
            }

            var cHP = GetUI<CenterHomeUI>(UIConsts.CenterHome);
            if (cHP != null && cHP.isShow)
            {
                cHP.RefreshHeadBar();
            }

            //todo0 mtHP内的 center没有刷新，这部分应该用事件改写

            var centerHomeUI = GetUI<CenterHomeUI>(UIConsts.CenterHome);
            if (centerHomeUI.isShow)
                centerHomeUI.RefreshHeadBar();
            var staminaBottomPurchaseUI = GetUI<StaminaBottomPurchaseUI>(UIConsts.StaminaBottomPurchaseUI);
            if (staminaBottomPurchaseUI.isShow)
                staminaBottomPurchaseUI.Refresh();
        }
    }

    private void RegisterCommercialAction(SC_GetIncentiveDataForPortalAck msg)
    {
        _commercialMsgCache = msg;
        _commercialActionCache = () =>
        {
            ShowCommercialUI(_commercialMsgCache);
            Notifier.instance.UnRegisterNotification(NotifyConsts.OnFinishCurrentSequence, InvokeCommercialAction);
        };
    }

    private void InvokeCommercialAction(string name, object body)
    {
        _commercialActionCache?.Invoke();
        _commercialActionCache = null;
    }

    public void ReqGetIncentiveData()
    {
        MsgManager.instance.SendMsg(new CS_GetIncentiveDataForPortalReq()
        {
            user_id = GetModel<MainModel>(ModelConsts.Main).userID,
        });
    }

    //场景切换完成的前置事件
    private void OnSceneLoadCompletePre(string evtName, object sceneType)
    {
        if ((ESceneType)sceneType == ESceneType.HomepageRoom)
        {
            //_hpLevel = (ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene).scene as HomepageLevel);
            _headerUI = UIManager.instance.GetUI<MainHeaderUI>(UIConsts.MainHeader);
            _headerUI.Show(() => { _headerUI.SetVisible(false); });
            
            //_hpUI = UIManager.instance.GetUI<HomepageUI>(UIConsts.Homepage);

            var _rkCtrl = ControllerManager.instance.GetController<RankController>(ModelConsts.Rank);
            _rkCtrl.EnterRank();

            _centerHomeLevel = (ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene).scene as CenterHomeLevel);
            _mtHpUI = UIManager.instance.GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage);  

            // _learnPathModel = ModelManager.instance.GetModel<LearnPathModel>(ModelConsts.LearnPath);
            _signModel = ModelManager.instance.GetModel<SignModel>(ModelConsts.Sign);
        }
        else
        {
            // _learnPathModel = null;
            _centerHomeLevel = null; //底层目前所有场景都是使用的单一 single模式载入的，所以yooasset载入下一个场景时，hplevel必然就卸载了

            if( _mtHpUI != null)
            {
                _mtHpUI.DeleteUI();//首次进入世界场景 强制清理一次
            }
           
            _mtHpUI = null;

            _centerHomeBgType = CenterHomeBgType.Undefined;
        }
    }

    //退出整个首页，进入世界前
    private void OnSceneUnloadComplete(string evtName, object sceneType)
    {
        if (_mtHpUI != null && _mtHpUI.tabGList != null)
        {
            _mtHpUI.tabGList.PrintItems();
            if ((ESceneType)sceneType == ESceneType.HomepageRoom)
            {
                if (_mtHpUI.isShow)
                {
                    _mtHpUI.tabGList.RemoveChildrenToPool();
                    _mtHpUI.Hide();
                    _mtHpUI.tabGList.numItems = 0;//todo1 如果不缓存，目前是清空后下次重建

                }
                if (_headerUI != null && _headerUI.isShow)
                {
                    _headerUI.Hide();
                }
            }
        }
        else {
            Debug.LogWarning("OnSceneUnloadComplete err: _mtHpUI==null");
        }

    }
    #endregion 基础接口

    #region tabview show&hide  !!课程页转首页- 必然涉及这部分


    //首页初始默认背景为 LivingRoomBg
    //当这里触发 最初初始的Refresh时，是有可能由于数据问题而被跳过的，但后续会由其他消息协议来再次触发
    public void ShowLessonView()
    {
        Debug.Log("ShowLivingRoomBgTabView");
        //viewType的flag标志位必须先立，否则后面逻辑会有问题
        _centerHomeBgType = CenterHomeBgType.LessonView;

        if (_mtHpUI != null) {
            if (_mtHpUI.isShow != true) {


                _mtHpUI.Show().onCompleted = () =>
                {
                    Debug.Log("mtHP show complete");
                    _mtHpUI.SwitchLastTab(false, true);//没有世界时这一步意义不大
                    this.RefreshLivingRoomBgTabView();
                    
                    GetController<PermissMsgController>(ModelConsts.PermissMsg).TryShowHalfPushUI();
                    TryRefreshSignAndStreak();
                };
            }
            else
            {
                _mtHpUI.SwitchLastTab(false, true);
                this.RefreshLivingRoomBgTabView();
                Debug.Log("mtHP show  again");
                
                //_mtHpUI.tabGList.ScrollToView(1, false, true);//初始 立即跳转到 centerHome
            }
        }

        if (_headerUI is { isShow: true }) {
            _headerUI.SetHeader(MainHeaderMode.Homepage);
            _headerUI.SetVisible(false);
            _headerUI.uiComponent.visible = false;
        }
        
    }

    private async void TryRefreshSignAndStreak() 
    {
        try
        {
            var resp = await MsgManager.instance.SendAsyncMsg<SC_GetCheckinSummaryAck>(new CS_GetCheckinSummaryReq());
            VFDebug.Log("HSW CS_GetCheckinSummaryReq");
            if (resp != null)
            {
                _signModel.SetSignSummary(resp.data);
                if (resp.data.recall_checkin_days)
                {
                    GetUI(UIConsts.SignRepairUI).PopShow(PopUIManager.Priority.Sign);
                }

                //Also update in Homepage
                RefreshStreak(resp.data.checkin_streak_days.ToString());
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError("Err in Header-CheckinSummary:" + e.ToString());
            RefreshStreak("-");//todo0 跨控制器修订streak btn
        }
    }
    
    //todo0 切换到chat bg时， hpLevel要有改变。
    //如果 from 首页自身做的切换
    //仅在从进入聊天类页面时会被处罚
    private void ShowChatRoomBgTabView()
    {
        //viewType的flag标志位必须先立，否则后面逻辑会有问题
        _centerHomeBgType = CenterHomeBgType.ChatRoomBg;

        Debug.Log("ShowChatRoomBgTabView  ");
        _centerHomeLevel.ShowChatRoomBgTabView( this.validChatAvatarParamCache);

        
      

        //不再执行hide，并且header部分的显示与否 留给各业务侧决定
        //_hpUI.HideFeatureButtonList();
        //_hpUI.HideBottomButtons();

        //_headerUI.SetHeader(param.mode == Msg.basic.PB_DialogMode.Exercise || param.mode == Msg.basic.PB_DialogMode.Career || param.mode == Msg.basic.PB_DialogMode.RolePlay ? MainHeaderMode.ExerciseTalk : MainHeaderMode.Talk);// 这里只区分2中case
        //_headerUI.Show();

    }

    public void TryShowChatRoomBgEmptyView(PB_DialogMode mode) {

        // var taskType = TypeConverter.CastDialogModeIntoTaskType(mode);
        // if (taskType == PB_TaskTypeEnum.QuizTask || taskType == PB_TaskTypeEnum.WarmUpPracticeTask) {
        //     Debug.Log("TryShowChatRoomBgEmptyView");
        //     _centerHomeBgType = CenterHomeBgType.ChatRoomBg;
        //     _centerHomeLevel.RenderChatBg(taskType, 0, 0);
        // }
        
    }

    private void HideAll()
    {

    }
    #endregion


    #region Event Handler   !!课程页转首页-涉及少量，由于扉页也要改造需要综合考虑

    //这里的dialog是泛意，任何题型都算dialog
    private void OnEnterDialog(string name, object body)
    {
        
        if (this._mtHpUI==null || this._centerHomeLevel == null )
        {
            //地图类场景的流程
            
            return;
        }
        else {
            PB_DialogMode mode = (PB_DialogMode)body;
            
            //扉页真正进入chat时，使用之前cache的信息切换view，
            this.ShowChatRoomBgTabView();//bg and role 调整, 注意这一行必须在EnterLeftBodyCamView 之前调用，这里需要先enable对象
            
            this._centerHomeLevel.EnterLeftBodyCamView(mode);//机位类调整
        }
    }

    private void OnExitDialog(string name, object body)
    {
        if (this._mtHpUI==null || this._centerHomeLevel == null)
        {
            //地图类调用流程的触发点
            //Debug.LogError("OnExitDialog error, fail to change cam/bg/role");
            return;
        }
        else {
            this._centerHomeLevel.ExitLeftBodyCamView();
        }
    }

    private void OnLearnPathNodeDataUpdate(string name, object body)
    {
        //首次和非首次刷视图需要作区分

        //首次时会强制连续尝试多轮refresh 直到成功
        //因为要执行refresh 还需要其他协议返回的数据，多条协议ack返回的时间差可能比较大

        if (_isInitializedData)
        {
            this.ChangeLivingRoomBgTabView();
            return;
        }

        this._initilizingTimerKey = TimerManager.instance.RegisterTimer((t) =>
        {
            this.ChangeLivingRoomBgTabView();
        }, 500, 10);//todo0 没有考虑如果初始

    }

    //原OnEnterChatStart的逻辑,
    private void OnObtainChatStartPageInfoComplete(string name, object body)
    {
        if (this._centerHomeLevel == null)
        {
            //如果不是homepage类场景,可能是世界的场景

            //Debug.LogError("OnEnterChatStart skip");
            return;
        }
        
        //只换人 不换背景，换人比较慢需要预先处理
        
        if (body != null)
        {
            var param = (ChangeChatAvatarParam)body;
           
            _centerHomeLevel.ChangeAvatar(param,isPreload:true);//这时仅仅提前 预先载入下一个角色，但不对homepage整体的tabview进行切换（减少hpUI的一次Hide）
            this.validChatAvatarParamCache = param;

            this.ShowChatRoomBgTabView();//扉页真正进入chat时，使用之前cache的信息切换view

           
            Debug.Log("Bg do OnEnterChatStart-》ShowChatRoomBgTabView");
        }
        else
        {
            Debug.LogError("OnEnterChatStart body param error ");
        }
    }
    
    private void OnExitChatStartAndCancel(string name, object body)
    {
        if (this._centerHomeLevel == null)
        {
            //如果不是homepage类场景触发的扉页通知直接忽略
            return;
        }
        Debug.Log("Only Refresh 3DModel");


        //只回退人
        // _centerHomeLevel.Render3DModel(_learnPathModel.validNextTaskItemInfoCache,false);

        //_hpLevel.Render3DModel(_learnPathModel.validNextTaskItemInfoCache);
        this.ShowLessonView();
        //add ray 增加一个小的切换延迟
        //TimerManager.instance.RegisterTimer((c) => {
        //    _hpLevel.Render3DModel(_learnPathModel.validNextTaskItemInfoCache);//这时仅仅提前 恢复上一个首页中的角色，但不对homepage整体的tabview切换
        //}, 500,1);

    }

    private void OnExitChatStartAndStart(string name, object body)
    {
        //这里的ShowChatRoomBgTabView逻辑统一合并到 EnterDialog内了，但是事件句柄暂时还在

        //if (this._centerHomeLevel == null)
        //{
        //    Debug.LogError("TestOnExit 扉页进入对话");
        //    //如果不是homepage类场景触发的扉页通知直接忽略
        //    return;
        //}
        //this.ShowChatRoomBgTabView();//扉页真正进入chat时，使用之前cache的信息切换view
    }



    #endregion


    #region 刷新类逻辑  !!课程页转首页-几乎不需要动这部分

    private void OnRefreshLivingRoomBgTabView(string s, object body)
    {
       RefreshLessonView(false,-1);
    }

    private void RefreshLivingRoomBgTabView()
    {
        RefreshLessonView(false,-1);
    }

    private void RefreshLivingRoomBgTabView(int callCount = -1)
    {
        RefreshLessonView(false, callCount);
    }

    //外部有路径数据变动时，允许refresh和assign currentSelected同时触发
    private void ChangeLivingRoomBgTabView()
    {
        RefreshLessonView(true,-1);
    }


    //要自行决策 当前是否数据状态足以进行刷新动作
    private void RefreshLessonView(bool isAssignSelectedIndex, int callCount=-1)
    {
        
        // if (_learnPathModel == null)
        // {
        //     Debug.Log("Refresh: _learnPathModel not ready");
        //     return;// 场景加载动作未完成
        // }
        // var taskList = _learnPathModel.GetTakList();
        // if (taskList.Count == 0)
        // {
        //     Debug.Log("Refresh: taskList not ready");
        //     return;//skip
        // }
        //
        // var result = _learnPathModel.GetNextTaskItemInfo();
        //
        // //step1 先判断和处理基础model数据的refresh
        // if ( result.nextStatus != LearnPathNextTaskItemStatus.Undefined)
        // {
        //     if (result.nextStatus == LearnPathNextTaskItemStatus.Exception)
        //     {
        //         //异常case
        //         Debug.LogError("internal error： try show 3D avatar when enter game but status abnormal ");
        //         TimerManager.instance.UnRegisterTimer(_initilizingTimerKey);
        //         return;
        //     }
        //
        //     //step1.5 result有效 + 允许assign时刷curr数据
        //     _learnPathModel.validNextTaskItemInfoCache = result;
        //
        //     if (isAssignSelectedIndex)
        //     {
        //         _learnPathModel.CurrSelectedChapterTaskItemIdx = result.taskListIdx;
        //         Debug.Log("=====>refresh assign CurrSelectedChapterTaskItemIdx=" + result.taskListIdx +" currTaskID="+ result.nextPBTaskItem?.task_id);
        //     }
        //     if (_centerHomeBgType == CenterHomeBgType.LessonView) {
        //
        //
        //         var isSuccRefresh = _mtHpUI.RefreshView(result);
        //
        //
        //         //需要根据mt部分的刷新结果处理是否要继续重试
        //         if (isSuccRefresh) {
        //             _centerHomeLevel.Render3DModel(result,false);
        //             if (!string.IsNullOrEmpty(_initilizingTimerKey))
        //             {
        //                 TimerManager.instance.UnRegisterTimer(_initilizingTimerKey);
        //                 _initilizingTimerKey = null;
        //
        //
        //             }
        //             if (_isInitializedData == false)
        //             {
        //                 _isInitializedData = true;
        //             }
        //
        //         }
        //     
        //     }
        // }
        // else
        // {
        //     Debug.Log($"Refresh fail: NextTaskItemStatus.Undefined ");
        // }
        
    }

    
    public void RefreshStreak(string value)
    {
        var ui = this._mtHpUI?.CenterHome;
        if (ui != null && ui.isShow)
        {
            ui.UpdateStreakBtnValue(value);
            this.RefreshStreakState();
        }
    }

    private void RefreshStreakState()
    {
        var ui = this._mtHpUI.CenterHome;
        if (ui!=null && ui.isShow && _signModel.signSummary != null)
        {
            ui.SetStreakBtnState(_signModel.signSummary.finish_checkin);
        }
    }

    #endregion 刷新类逻辑end
    #region homepage banner &&商业化部分  !!课程页转首页-几乎不需要动这部分

    public void ShowCommercialUI(SC_GetIncentiveDataForPortalAck msg)
    {
        if (GetModel<GuideModel>(ModelConsts.Guide).currentSequence != null)
        {
            RegisterCommercialAction(msg);
            Notifier.instance.RegisterNotification(NotifyConsts.OnFinishCurrentSequence, InvokeCommercialAction);
            return;
        }

        var shopModel = GetModel<ShopModel>(ModelConsts.Shop);
        if (shopModel.shopInfoData != null) //为了等shop数据
        {
            if (msg.data.homepage_economic_info.show_commercial_popup)
            {
                if (shopModel.PayWallData != null && shopModel.PayWallData.subscription_infos != null &&
                    shopModel.PayWallData.end_milliseconds_in_utc > TimeExt.serverTimestamp)
                {
                    VFDebug.Log($"PayWallData CurTimeStamp -- {TimeExt.serverTimestamp} EndTimeStamp -- {shopModel.PayWallData.end_milliseconds_in_utc}");
                    GetUI(UIConsts.PayWallHalfUI).PopShow(PopUIManager.Priority.Business);
                }
                else
                {
                    GetUI(UIConsts.SpeakPlanBottomSubscribeUI).PopShow(PopUIManager.Priority.Business);
                }
            }
            
            if (msg.data.homepage_economic_info.show_commercial_banner)
            {
                if (shopModel.PayWallData != null && shopModel.PayWallData.subscription_infos != null &&
                    shopModel.PayWallData.end_milliseconds_in_utc > TimeExt.serverTimestamp)
                {
                    if (!msg.data.homepage_economic_info.show_commercial_popup)
                        ShowHomepageTimeBanner();//弹窗出过了 自己调用
                    return;
                }
                ShowHomepageBanner();
            }
        }
    }

    private void ShowHomepageBanner()
    {
        Action actionConfirm = ()=>
        {
            PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.bottom_bar;
            GetUI(UIConsts.SpeakPlanPromotionStep1UI).Show(SpeakShowType.Homepage);

        };
        Action actionCancel = ()=>
        {
            MsgManager.instance.SendMsg(new CS_SetShowStateReq()
            {
                show_item_type = PB_ShowItemType.CommercialBanner,
            });
        };
        SendNotification(NotifyConsts.ShowHomepageBanner, new HomepageBannerArgs()
        {
            descStr = I18N.inst.MoStr("ui_planBanner_content"),
            titleStr = I18N.inst.MoStr("ui_planBanner_btn_go"),
            confirmCb = actionConfirm,
            cancelCb = actionCancel,
        });
    }

    public void ShowHomepageTimeBanner()
    {
        var shopModel = GetModel<ShopModel>(ModelConsts.Shop);
        VFDebug.Log($"PayWallData CurTimeStamp -- {TimeExt.serverTimestamp} EndTimeStamp -- {shopModel.PayWallData.end_milliseconds_in_utc}");
        void ActionConfirm()
        {
            shopModel.SetPayWallType();
            PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.discount_bottom_bar;
            GetUI(UIConsts.SpeakPlanPromotionStep1UI).Show();
        }

        SendNotification(NotifyConsts.ShowHomepageTimeBanner, new Action(ActionConfirm));
    }

    #endregion
    public void ShowCommercialUI()
    {
        if (_commercialMsgCache != null)
            ShowCommercialUI(_commercialMsgCache);
    }

    private void OnChangeLanguage(string name, object body)
    {
        var centerHomeUI = GetUI<CenterHomeUI>(UIConsts.CenterHome);
        if (centerHomeUI.isShow && centerHomeUI.IsHomepageBannerVisible())
        {
            ShowHomepageBanner();
        }
        else if (centerHomeUI.isShow && centerHomeUI.IsHomepageTimeBannerVisible())
        {
            var shopModel = GetModel<ShopModel>(ModelConsts.Shop);
            if (shopModel.PayWallData != null && shopModel.PayWallData.subscription_infos != null &&
                shopModel.PayWallData.end_milliseconds_in_utc > TimeExt.serverTimestamp)
                ShowHomepageTimeBanner();
        }
    }

    private void ShowChapterUI(string s, object body)
    {
        Debug.LogError("课程页转首页后 此类调用不应该触发");
        //if (this._mtHpUI.isShow ) {
        //    this._mtHpUI.tabGList.ScrollToView(0, false, true);
        //}
    }
    
    /// <summary>
    /// 当前是否在探索页签
    /// </summary>
    public bool IsExploreTab()
    {
        return GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage).IsExploreTab();
    }
    
    /// <summary>
    /// 当前是否在探索页签
    /// </summary>
    public bool IsChapterTab()
    {
        var ui = GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage);
        return ui!=null && ui.isShow && ui.IsChapterTab();
    }    
}

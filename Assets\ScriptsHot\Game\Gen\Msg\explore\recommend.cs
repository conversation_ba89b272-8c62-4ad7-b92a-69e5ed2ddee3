// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/recommend.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/recommend.proto</summary>
  public static partial class RecommendReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/recommend.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static RecommendReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiBwcm90b2J1Zi9leHBsb3JlL3JlY29tbWVuZC5wcm90bxobcHJvdG9idWYv",
            "ZXhwbG9yZS9iYXNlLnByb3RvGhtwcm90b2J1Zi9iYXNpYy9kaWFsb2cucHJv",
            "dG8aG3Byb3RvYnVmL2Jhc2ljL2NvbW1vbi5wcm90bxodcHJvdG9idWYvZXhw",
            "bG9yZS9kaWFsb2cucHJvdG8iMAoWQ1NfR2V0UmVjb21tZW5kTGlzdFJlcRIW",
            "Cg5pZGVtcG90ZW50U2lnbhgBIAEoAyJ+ChdTQ19HZXRSZWNvbW1lbmRMaXN0",
            "UmVzcBIhCgRjb2RlGAEgASgOMhMuUEJfRXhwbG9yZV9CaXpDb2RlEhYKDmlk",
            "ZW1wb3RlbnRTaWduGAIgASgDEigKD3ByZWxvYWREYXRhTGlzdBgDIAMoCzIP",
            "LlBCX1ByZWxvYWREYXRhIqEBCg5QQl9QcmVsb2FkRGF0YRIQCghlbnRpdHlJ",
            "ZBgBIAEoAxIzCgplbnRpdHlUeXBlGAIgASgOMh8uUEJfRXhwbG9yZV9SZWNv",
            "bW1lbmRFbnRpdHlUeXBlEjoKFWRpYWxvZ1Rhc2tQcmVsb2FkRGF0YRgDIAEo",
            "CzIZLlBCX0RpYWxvZ1Rhc2tQcmVsb2FkRGF0YUgAQgwKCmVudGl0eURhdGEi",
            "4gEKGFBCX0RpYWxvZ1Rhc2tQcmVsb2FkRGF0YRIOCgZ0YXNrSWQYASABKAMS",
            "IgoKZGlhbG9nTW9kZRgCIAEoDjIOLlBCX0RpYWxvZ01vZGUSHwoGYXZhdGFy",
            "GAMgASgLMg8uUEJfVGFza19BdmF0YXISHQoFc2NlbmUYBCABKAsyDi5QQl9U",
            "YXNrX1NjZW5lEh8KBmRldGFpbBgFIAEoCzIPLlBCX1Rhc2tfRGV0YWlsEjEK",
            "DmZpcnN0Um91bmREYXRhGAYgASgLMhkuUEJfVGFza19GaXJzdF9Sb3VuZF9E",
            "YXRhIo4BCg5QQl9UYXNrX0F2YXRhchIQCghhdmF0YXJJZBgBIAEoAxISCgpo",
            "ZWFkUGljVXJsGAIgASgJEiwKC2hlYWRCZ0NvbG9yGAMgASgOMhcuUEJfQmFj",
            "a2dyb3VuZENvbG9yRW51bRIMCgRuYW1lGAQgASgJEgwKBHJvbGUYBSABKAkS",
            "DAoEaXMzRBgGIAEoCCJbCg1QQl9UYXNrX1NjZW5lEhAKCGJnUGljVGFnGAEg",
            "ASgJEhAKCGJnUGljVXJsGAIgASgJEhIKCmJnVm9pY2VUYWcYAyABKAkSEgoK",
            "YmdWb2ljZVVybBgEIAEoCSKJAQoOUEJfVGFza19EZXRhaWwSDAoEZGVzYxgB",
            "IAEoCRIqCghkZXNjVHlwZRgCIAEoDjIYLlBCX0V4cGxvcmVfVGFza0Rlc2NU",
            "eXBlEjMKDnJvbGVQbGF5RGV0YWlsGAMgASgLMhkuUEJfVGFza19Sb2xlX1Bs",
            "YXlfRGV0YWlsSABCCAoGZGV0YWlsIjwKGFBCX1Rhc2tfUm9sZV9QbGF5X0Rl",
            "dGFpbBIgCgVnb2FscxgBIAMoCzIRLlBCX1Rhc2tfR29hbEl0ZW0iVQoQUEJf",
            "VGFza19Hb2FsSXRlbRIOCgZnb2FsSWQYASABKAMSCgoCbm8YAiABKAUSEAoI",
            "Z29hbERlc2MYAyABKAkSEwoLaXNDb21wbGV0ZWQYBCABKAgigAMKGFBCX1Rh",
            "c2tfRmlyc3RfUm91bmRfRGF0YRIRCglyZXBseVRleHQYASABKAkSGgoScmVw",
            "bHlUcmFuc2xhdGVUZXh0GAIgASgJEjQKCnJlcGx5QXVkaW8YAyABKAsyIC5Q",
            "Ql9FeHBsb3JlX0RpYWxvZ0F1ZGlvRG93bkZyYW1lEhMKC2V4YW1wbGVUZXh0",
            "GAQgASgJEhwKFGV4YW1wbGVUcmFuc2xhdGVUZXh0GAUgASgJEjYKDGV4YW1w",
            "bGVBdWRpbxgGIAEoCzIgLlBCX0V4cGxvcmVfRGlhbG9nQXVkaW9Eb3duRnJh",
            "bWUSFQoNcmVwbHlCdWJibGVJZBgHIAEoCRIXCg9leGFtcGxlQnViYmxlSWQY",
            "CCABKAkSOAoVZW1vdGlvbkFuYWx5c2lzUmVzdWx0GAkgASgLMhkuUEJfRW1v",
            "dGlvbkFuYWx5c2lzUmVzdWx0EhIKCmFkdmljZVRleHQYCiABKAkSFgoOYWR2",
            "aWNlQnViYmxlSWQYCyABKAkiXgoRUEJfUmVjb21tZW5kVXBNc2cSPQoQdXNl",
            "clN3aXRjaEVudGl0eRgBIAEoCzIhLlBCX0V4cGxvcmVfUmVjb21tZW5kU3dp",
            "dGNoRW50aXR5SABCCgoIdXBCaXpNc2ciaQogUEJfRXhwbG9yZV9SZWNvbW1l",
            "bmRTd2l0Y2hFbnRpdHkSEAoIZW50aXR5SWQYASABKAMSMwoKZW50aXR5VHlw",
            "ZRgCIAEoDjIfLlBCX0V4cGxvcmVfUmVjb21tZW5kRW50aXR5VHlwZSItChhT",
            "Q19SZWNvbW1lbmRTd2l0Y2hFbnRpdHkSEQoJdGltZXN0YW1wGAEgASgDKlAK",
            "F1BCX0V4cGxvcmVfVGFza0Rlc2NUeXBlEhIKDkVPX1REVF9VTktOT1dOEAAS",
            "EAoMRU9fVERUX1RPUElDEAESDwoLRU9fVERUX0dPQUwQAipKCh5QQl9FeHBs",
            "b3JlX1JlY29tbWVuZEVudGl0eVR5cGUSEQoNRU9fUkVfVU5LTk9XThAAEhUK",
            "EUVPX1JFX0RJQUxPR19UQVNLEAFCKloadmZfcHJvdG9idWYvc2VydmVyL2V4",
            "cGxvcmWqAgtNc2cuZXhwbG9yZWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.explore.BaseReflection.Descriptor, global::Msg.basic.DialogReflection.Descriptor, global::Msg.basic.CommonReflection.Descriptor, global::Msg.explore.DialogReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.explore.PB_Explore_TaskDescType), typeof(global::Msg.explore.PB_Explore_RecommendEntityType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.CS_GetRecommendListReq), global::Msg.explore.CS_GetRecommendListReq.Parser, new[]{ "idempotentSign" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_GetRecommendListResp), global::Msg.explore.SC_GetRecommendListResp.Parser, new[]{ "code", "idempotentSign", "preloadDataList" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_PreloadData), global::Msg.explore.PB_PreloadData.Parser, new[]{ "entityId", "entityType", "dialogTaskPreloadData" }, new[]{ "entityData" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_DialogTaskPreloadData), global::Msg.explore.PB_DialogTaskPreloadData.Parser, new[]{ "taskId", "dialogMode", "avatar", "scene", "detail", "firstRoundData" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_Avatar), global::Msg.explore.PB_Task_Avatar.Parser, new[]{ "avatarId", "headPicUrl", "headBgColor", "name", "role", "is3D" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_Scene), global::Msg.explore.PB_Task_Scene.Parser, new[]{ "bgPicTag", "bgPicUrl", "bgVoiceTag", "bgVoiceUrl" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_Detail), global::Msg.explore.PB_Task_Detail.Parser, new[]{ "desc", "descType", "rolePlayDetail" }, new[]{ "detail" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_Role_Play_Detail), global::Msg.explore.PB_Task_Role_Play_Detail.Parser, new[]{ "goals" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_GoalItem), global::Msg.explore.PB_Task_GoalItem.Parser, new[]{ "goalId", "no", "goalDesc", "isCompleted" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_First_Round_Data), global::Msg.explore.PB_Task_First_Round_Data.Parser, new[]{ "replyText", "replyTranslateText", "replyAudio", "exampleText", "exampleTranslateText", "exampleAudio", "replyBubbleId", "exampleBubbleId", "emotionAnalysisResult", "adviceText", "adviceBubbleId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_RecommendUpMsg), global::Msg.explore.PB_RecommendUpMsg.Parser, new[]{ "userSwitchEntity" }, new[]{ "upBizMsg" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Explore_RecommendSwitchEntity), global::Msg.explore.PB_Explore_RecommendSwitchEntity.Parser, new[]{ "entityId", "entityType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_RecommendSwitchEntity), global::Msg.explore.SC_RecommendSwitchEntity.Parser, new[]{ "timestamp" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  ///
  /// 任务描述类型
  /// 1. 区分顶部文本描述内容的类型
  /// 2. 目前有两种任务描述类型
  /// </summary>
  public enum PB_Explore_TaskDescType {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_TDT_UNKNOWN")] EO_TDT_UNKNOWN = 0,
    /// <summary>
    /// 话题
    /// </summary>
    [pbr::OriginalName("EO_TDT_TOPIC")] EO_TDT_TOPIC = 1,
    /// <summary>
    /// 目标
    /// </summary>
    [pbr::OriginalName("EO_TDT_GOAL")] EO_TDT_GOAL = 2,
  }

  /// <summary>
  ///
  /// 推荐实体类型
  /// 1. 推流的每个内容都可以都可能是不同的功能单元
  /// 2. Explore一期本质上做的是任务型对话的功能单元
  /// </summary>
  public enum PB_Explore_RecommendEntityType {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_RE_UNKNOWN")] EO_RE_UNKNOWN = 0,
    /// <summary>
    /// 任务型对话（FreeTask、DIY、RolePlay、Tutor）
    /// </summary>
    [pbr::OriginalName("EO_RE_DIALOG_TASK")] EO_RE_DIALOG_TASK = 1,
  }

  #endregion

  #region Messages
  /// <summary>
  ///
  /// 获取推荐列表请求
  /// 1. 客户端本地生成请求序号，保证序号下请求，出现多次重试的幂等性
  /// 2. 幂等标识用于客户端在重试的时候保证接口的幂等性（唯一有序即可）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetRecommendListReq : pb::IMessage<CS_GetRecommendListReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetRecommendListReq> _parser = new pb::MessageParser<CS_GetRecommendListReq>(() => new CS_GetRecommendListReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetRecommendListReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetRecommendListReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetRecommendListReq(CS_GetRecommendListReq other) : this() {
      idempotentSign_ = other.idempotentSign_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetRecommendListReq Clone() {
      return new CS_GetRecommendListReq(this);
    }

    /// <summary>Field number for the "idempotentSign" field.</summary>
    public const int idempotentSignFieldNumber = 1;
    private long idempotentSign_;
    /// <summary>
    /// 幂等标识（幂等键，必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long idempotentSign {
      get { return idempotentSign_; }
      set {
        idempotentSign_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetRecommendListReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetRecommendListReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (idempotentSign != other.idempotentSign) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (idempotentSign != 0L) hash ^= idempotentSign.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (idempotentSign != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(idempotentSign);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (idempotentSign != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(idempotentSign);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (idempotentSign != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(idempotentSign);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetRecommendListReq other) {
      if (other == null) {
        return;
      }
      if (other.idempotentSign != 0L) {
        idempotentSign = other.idempotentSign;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            idempotentSign = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            idempotentSign = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 获取推荐列表响应
  /// 1. 响应也会把请求序号带回来，方便客户端保证数据的一致性
  /// 2. 使用同一个列表，转载所有推荐实体数据（Explore一期只有任务型对话）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetRecommendListResp : pb::IMessage<SC_GetRecommendListResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetRecommendListResp> _parser = new pb::MessageParser<SC_GetRecommendListResp>(() => new SC_GetRecommendListResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetRecommendListResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetRecommendListResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetRecommendListResp(SC_GetRecommendListResp other) : this() {
      code_ = other.code_;
      idempotentSign_ = other.idempotentSign_;
      preloadDataList_ = other.preloadDataList_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetRecommendListResp Clone() {
      return new SC_GetRecommendListResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "idempotentSign" field.</summary>
    public const int idempotentSignFieldNumber = 2;
    private long idempotentSign_;
    /// <summary>
    /// 幂等标识（幂等键，必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long idempotentSign {
      get { return idempotentSign_; }
      set {
        idempotentSign_ = value;
      }
    }

    /// <summary>Field number for the "preloadDataList" field.</summary>
    public const int preloadDataListFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Msg.explore.PB_PreloadData> _repeated_preloadDataList_codec
        = pb::FieldCodec.ForMessage(26, global::Msg.explore.PB_PreloadData.Parser);
    private readonly pbc::RepeatedField<global::Msg.explore.PB_PreloadData> preloadDataList_ = new pbc::RepeatedField<global::Msg.explore.PB_PreloadData>();
    /// <summary>
    /// 任务预加载数据项列表（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.explore.PB_PreloadData> preloadDataList {
      get { return preloadDataList_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetRecommendListResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetRecommendListResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (idempotentSign != other.idempotentSign) return false;
      if(!preloadDataList_.Equals(other.preloadDataList_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (idempotentSign != 0L) hash ^= idempotentSign.GetHashCode();
      hash ^= preloadDataList_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (idempotentSign != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(idempotentSign);
      }
      preloadDataList_.WriteTo(output, _repeated_preloadDataList_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (idempotentSign != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(idempotentSign);
      }
      preloadDataList_.WriteTo(ref output, _repeated_preloadDataList_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (idempotentSign != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(idempotentSign);
      }
      size += preloadDataList_.CalculateSize(_repeated_preloadDataList_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetRecommendListResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.idempotentSign != 0L) {
        idempotentSign = other.idempotentSign;
      }
      preloadDataList_.Add(other.preloadDataList_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 16: {
            idempotentSign = input.ReadInt64();
            break;
          }
          case 26: {
            preloadDataList_.AddEntriesFrom(input, _repeated_preloadDataList_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 16: {
            idempotentSign = input.ReadInt64();
            break;
          }
          case 26: {
            preloadDataList_.AddEntriesFrom(ref input, _repeated_preloadDataList_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 预加载数据
  /// 1. 预加载数据结构，需要支持扩展不同的功能单元
  /// 2. 目前按不同的实体类型做隔离功能单元（Explore一期只有任务型对话）
  /// 3. 不同的预加载数据项，可能装载相同的内容，但实体id不同
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_PreloadData : pb::IMessage<PB_PreloadData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_PreloadData> _parser = new pb::MessageParser<PB_PreloadData>(() => new PB_PreloadData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_PreloadData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_PreloadData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_PreloadData(PB_PreloadData other) : this() {
      entityId_ = other.entityId_;
      entityType_ = other.entityType_;
      switch (other.entityDataCase) {
        case entityDataOneofCase.dialogTaskPreloadData:
          dialogTaskPreloadData = other.dialogTaskPreloadData.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_PreloadData Clone() {
      return new PB_PreloadData(this);
    }

    /// <summary>Field number for the "entityId" field.</summary>
    public const int entityIdFieldNumber = 1;
    private long entityId_;
    /// <summary>
    /// 实体id（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long entityId {
      get { return entityId_; }
      set {
        entityId_ = value;
      }
    }

    /// <summary>Field number for the "entityType" field.</summary>
    public const int entityTypeFieldNumber = 2;
    private global::Msg.explore.PB_Explore_RecommendEntityType entityType_ = global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN;
    /// <summary>
    /// 实体类型（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_RecommendEntityType entityType {
      get { return entityType_; }
      set {
        entityType_ = value;
      }
    }

    /// <summary>Field number for the "dialogTaskPreloadData" field.</summary>
    public const int dialogTaskPreloadDataFieldNumber = 3;
    /// <summary>
    /// 任务型对话预加载数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_DialogTaskPreloadData dialogTaskPreloadData {
      get { return entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData ? (global::Msg.explore.PB_DialogTaskPreloadData) entityData_ : null; }
      set {
        entityData_ = value;
        entityDataCase_ = value == null ? entityDataOneofCase.None : entityDataOneofCase.dialogTaskPreloadData;
      }
    }

    private object entityData_;
    /// <summary>Enum of possible cases for the "entityData" oneof.</summary>
    public enum entityDataOneofCase {
      None = 0,
      dialogTaskPreloadData = 3,
    }
    private entityDataOneofCase entityDataCase_ = entityDataOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public entityDataOneofCase entityDataCase {
      get { return entityDataCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearentityData() {
      entityDataCase_ = entityDataOneofCase.None;
      entityData_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_PreloadData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_PreloadData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (entityId != other.entityId) return false;
      if (entityType != other.entityType) return false;
      if (!object.Equals(dialogTaskPreloadData, other.dialogTaskPreloadData)) return false;
      if (entityDataCase != other.entityDataCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (entityId != 0L) hash ^= entityId.GetHashCode();
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) hash ^= entityType.GetHashCode();
      if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) hash ^= dialogTaskPreloadData.GetHashCode();
      hash ^= (int) entityDataCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (entityId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) entityType);
      }
      if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) {
        output.WriteRawTag(26);
        output.WriteMessage(dialogTaskPreloadData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (entityId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) entityType);
      }
      if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) {
        output.WriteRawTag(26);
        output.WriteMessage(dialogTaskPreloadData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (entityId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) entityType);
      }
      if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(dialogTaskPreloadData);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_PreloadData other) {
      if (other == null) {
        return;
      }
      if (other.entityId != 0L) {
        entityId = other.entityId;
      }
      if (other.entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        entityType = other.entityType;
      }
      switch (other.entityDataCase) {
        case entityDataOneofCase.dialogTaskPreloadData:
          if (dialogTaskPreloadData == null) {
            dialogTaskPreloadData = new global::Msg.explore.PB_DialogTaskPreloadData();
          }
          dialogTaskPreloadData.MergeFrom(other.dialogTaskPreloadData);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            entityId = input.ReadInt64();
            break;
          }
          case 16: {
            entityType = (global::Msg.explore.PB_Explore_RecommendEntityType) input.ReadEnum();
            break;
          }
          case 26: {
            global::Msg.explore.PB_DialogTaskPreloadData subBuilder = new global::Msg.explore.PB_DialogTaskPreloadData();
            if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) {
              subBuilder.MergeFrom(dialogTaskPreloadData);
            }
            input.ReadMessage(subBuilder);
            dialogTaskPreloadData = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            entityId = input.ReadInt64();
            break;
          }
          case 16: {
            entityType = (global::Msg.explore.PB_Explore_RecommendEntityType) input.ReadEnum();
            break;
          }
          case 26: {
            global::Msg.explore.PB_DialogTaskPreloadData subBuilder = new global::Msg.explore.PB_DialogTaskPreloadData();
            if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) {
              subBuilder.MergeFrom(dialogTaskPreloadData);
            }
            input.ReadMessage(subBuilder);
            dialogTaskPreloadData = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 任务型对话预加载数据
  /// 1. 数据块抽象为三个，分别为：Avatar信息、场景信息和任务详情
  /// 2. 每个任务都有明确支持的对话模式，且仅能是一个，如多个则需要拆分为多个任务来表示
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_DialogTaskPreloadData : pb::IMessage<PB_DialogTaskPreloadData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_DialogTaskPreloadData> _parser = new pb::MessageParser<PB_DialogTaskPreloadData>(() => new PB_DialogTaskPreloadData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_DialogTaskPreloadData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_DialogTaskPreloadData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_DialogTaskPreloadData(PB_DialogTaskPreloadData other) : this() {
      taskId_ = other.taskId_;
      dialogMode_ = other.dialogMode_;
      avatar_ = other.avatar_ != null ? other.avatar_.Clone() : null;
      scene_ = other.scene_ != null ? other.scene_.Clone() : null;
      detail_ = other.detail_ != null ? other.detail_.Clone() : null;
      firstRoundData_ = other.firstRoundData_ != null ? other.firstRoundData_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_DialogTaskPreloadData Clone() {
      return new PB_DialogTaskPreloadData(this);
    }

    /// <summary>Field number for the "taskId" field.</summary>
    public const int taskIdFieldNumber = 1;
    private long taskId_;
    /// <summary>
    /// 任务id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long taskId {
      get { return taskId_; }
      set {
        taskId_ = value;
      }
    }

    /// <summary>Field number for the "dialogMode" field.</summary>
    public const int dialogModeFieldNumber = 2;
    private global::Msg.basic.PB_DialogMode dialogMode_ = global::Msg.basic.PB_DialogMode.MNone;
    /// <summary>
    /// 对话模式
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogMode dialogMode {
      get { return dialogMode_; }
      set {
        dialogMode_ = value;
      }
    }

    /// <summary>Field number for the "avatar" field.</summary>
    public const int avatarFieldNumber = 3;
    private global::Msg.explore.PB_Task_Avatar avatar_;
    /// <summary>
    /// Avatar信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Task_Avatar avatar {
      get { return avatar_; }
      set {
        avatar_ = value;
      }
    }

    /// <summary>Field number for the "scene" field.</summary>
    public const int sceneFieldNumber = 4;
    private global::Msg.explore.PB_Task_Scene scene_;
    /// <summary>
    /// 场景信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Task_Scene scene {
      get { return scene_; }
      set {
        scene_ = value;
      }
    }

    /// <summary>Field number for the "detail" field.</summary>
    public const int detailFieldNumber = 5;
    private global::Msg.explore.PB_Task_Detail detail_;
    /// <summary>
    /// 任务详情
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Task_Detail detail {
      get { return detail_; }
      set {
        detail_ = value;
      }
    }

    /// <summary>Field number for the "firstRoundData" field.</summary>
    public const int firstRoundDataFieldNumber = 6;
    private global::Msg.explore.PB_Task_First_Round_Data firstRoundData_;
    /// <summary>
    /// 首轮数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Task_First_Round_Data firstRoundData {
      get { return firstRoundData_; }
      set {
        firstRoundData_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_DialogTaskPreloadData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_DialogTaskPreloadData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (taskId != other.taskId) return false;
      if (dialogMode != other.dialogMode) return false;
      if (!object.Equals(avatar, other.avatar)) return false;
      if (!object.Equals(scene, other.scene)) return false;
      if (!object.Equals(detail, other.detail)) return false;
      if (!object.Equals(firstRoundData, other.firstRoundData)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (taskId != 0L) hash ^= taskId.GetHashCode();
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) hash ^= dialogMode.GetHashCode();
      if (avatar_ != null) hash ^= avatar.GetHashCode();
      if (scene_ != null) hash ^= scene.GetHashCode();
      if (detail_ != null) hash ^= detail.GetHashCode();
      if (firstRoundData_ != null) hash ^= firstRoundData.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (taskId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(taskId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) dialogMode);
      }
      if (avatar_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(avatar);
      }
      if (scene_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(scene);
      }
      if (detail_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(detail);
      }
      if (firstRoundData_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(firstRoundData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (taskId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(taskId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) dialogMode);
      }
      if (avatar_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(avatar);
      }
      if (scene_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(scene);
      }
      if (detail_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(detail);
      }
      if (firstRoundData_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(firstRoundData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (taskId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(taskId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) dialogMode);
      }
      if (avatar_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(avatar);
      }
      if (scene_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(scene);
      }
      if (detail_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(detail);
      }
      if (firstRoundData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(firstRoundData);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_DialogTaskPreloadData other) {
      if (other == null) {
        return;
      }
      if (other.taskId != 0L) {
        taskId = other.taskId;
      }
      if (other.dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        dialogMode = other.dialogMode;
      }
      if (other.avatar_ != null) {
        if (avatar_ == null) {
          avatar = new global::Msg.explore.PB_Task_Avatar();
        }
        avatar.MergeFrom(other.avatar);
      }
      if (other.scene_ != null) {
        if (scene_ == null) {
          scene = new global::Msg.explore.PB_Task_Scene();
        }
        scene.MergeFrom(other.scene);
      }
      if (other.detail_ != null) {
        if (detail_ == null) {
          detail = new global::Msg.explore.PB_Task_Detail();
        }
        detail.MergeFrom(other.detail);
      }
      if (other.firstRoundData_ != null) {
        if (firstRoundData_ == null) {
          firstRoundData = new global::Msg.explore.PB_Task_First_Round_Data();
        }
        firstRoundData.MergeFrom(other.firstRoundData);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            taskId = input.ReadInt64();
            break;
          }
          case 16: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 26: {
            if (avatar_ == null) {
              avatar = new global::Msg.explore.PB_Task_Avatar();
            }
            input.ReadMessage(avatar);
            break;
          }
          case 34: {
            if (scene_ == null) {
              scene = new global::Msg.explore.PB_Task_Scene();
            }
            input.ReadMessage(scene);
            break;
          }
          case 42: {
            if (detail_ == null) {
              detail = new global::Msg.explore.PB_Task_Detail();
            }
            input.ReadMessage(detail);
            break;
          }
          case 50: {
            if (firstRoundData_ == null) {
              firstRoundData = new global::Msg.explore.PB_Task_First_Round_Data();
            }
            input.ReadMessage(firstRoundData);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            taskId = input.ReadInt64();
            break;
          }
          case 16: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 26: {
            if (avatar_ == null) {
              avatar = new global::Msg.explore.PB_Task_Avatar();
            }
            input.ReadMessage(avatar);
            break;
          }
          case 34: {
            if (scene_ == null) {
              scene = new global::Msg.explore.PB_Task_Scene();
            }
            input.ReadMessage(scene);
            break;
          }
          case 42: {
            if (detail_ == null) {
              detail = new global::Msg.explore.PB_Task_Detail();
            }
            input.ReadMessage(detail);
            break;
          }
          case 50: {
            if (firstRoundData_ == null) {
              firstRoundData = new global::Msg.explore.PB_Task_First_Round_Data();
            }
            input.ReadMessage(firstRoundData);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Avatar信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_Avatar : pb::IMessage<PB_Task_Avatar>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_Avatar> _parser = new pb::MessageParser<PB_Task_Avatar>(() => new PB_Task_Avatar());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_Avatar> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Avatar() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Avatar(PB_Task_Avatar other) : this() {
      avatarId_ = other.avatarId_;
      headPicUrl_ = other.headPicUrl_;
      headBgColor_ = other.headBgColor_;
      name_ = other.name_;
      role_ = other.role_;
      is3D_ = other.is3D_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Avatar Clone() {
      return new PB_Task_Avatar(this);
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 1;
    private long avatarId_;
    /// <summary>
    /// AvatarId（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "headPicUrl" field.</summary>
    public const int headPicUrlFieldNumber = 2;
    private string headPicUrl_ = "";
    /// <summary>
    /// 头像url（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string headPicUrl {
      get { return headPicUrl_; }
      set {
        headPicUrl_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "headBgColor" field.</summary>
    public const int headBgColorFieldNumber = 3;
    private global::Msg.basic.PB_BackgroundColorEnum headBgColor_ = global::Msg.basic.PB_BackgroundColorEnum.BGColorNone;
    /// <summary>
    /// 头像背景色（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BackgroundColorEnum headBgColor {
      get { return headBgColor_; }
      set {
        headBgColor_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int nameFieldNumber = 4;
    private string name_ = "";
    /// <summary>
    /// 姓名（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "role" field.</summary>
    public const int roleFieldNumber = 5;
    private string role_ = "";
    /// <summary>
    /// 角色（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string role {
      get { return role_; }
      set {
        role_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is3D" field.</summary>
    public const int is3DFieldNumber = 6;
    private bool is3D_;
    /// <summary>
    /// 是否3D形象（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is3D {
      get { return is3D_; }
      set {
        is3D_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_Avatar);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_Avatar other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (avatarId != other.avatarId) return false;
      if (headPicUrl != other.headPicUrl) return false;
      if (headBgColor != other.headBgColor) return false;
      if (name != other.name) return false;
      if (role != other.role) return false;
      if (is3D != other.is3D) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (headPicUrl.Length != 0) hash ^= headPicUrl.GetHashCode();
      if (headBgColor != global::Msg.basic.PB_BackgroundColorEnum.BGColorNone) hash ^= headBgColor.GetHashCode();
      if (name.Length != 0) hash ^= name.GetHashCode();
      if (role.Length != 0) hash ^= role.GetHashCode();
      if (is3D != false) hash ^= is3D.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (avatarId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatarId);
      }
      if (headPicUrl.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(headPicUrl);
      }
      if (headBgColor != global::Msg.basic.PB_BackgroundColorEnum.BGColorNone) {
        output.WriteRawTag(24);
        output.WriteEnum((int) headBgColor);
      }
      if (name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(name);
      }
      if (role.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(role);
      }
      if (is3D != false) {
        output.WriteRawTag(48);
        output.WriteBool(is3D);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (avatarId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatarId);
      }
      if (headPicUrl.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(headPicUrl);
      }
      if (headBgColor != global::Msg.basic.PB_BackgroundColorEnum.BGColorNone) {
        output.WriteRawTag(24);
        output.WriteEnum((int) headBgColor);
      }
      if (name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(name);
      }
      if (role.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(role);
      }
      if (is3D != false) {
        output.WriteRawTag(48);
        output.WriteBool(is3D);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (headPicUrl.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(headPicUrl);
      }
      if (headBgColor != global::Msg.basic.PB_BackgroundColorEnum.BGColorNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) headBgColor);
      }
      if (name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(name);
      }
      if (role.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(role);
      }
      if (is3D != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_Avatar other) {
      if (other == null) {
        return;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.headPicUrl.Length != 0) {
        headPicUrl = other.headPicUrl;
      }
      if (other.headBgColor != global::Msg.basic.PB_BackgroundColorEnum.BGColorNone) {
        headBgColor = other.headBgColor;
      }
      if (other.name.Length != 0) {
        name = other.name;
      }
      if (other.role.Length != 0) {
        role = other.role;
      }
      if (other.is3D != false) {
        is3D = other.is3D;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            avatarId = input.ReadInt64();
            break;
          }
          case 18: {
            headPicUrl = input.ReadString();
            break;
          }
          case 24: {
            headBgColor = (global::Msg.basic.PB_BackgroundColorEnum) input.ReadEnum();
            break;
          }
          case 34: {
            name = input.ReadString();
            break;
          }
          case 42: {
            role = input.ReadString();
            break;
          }
          case 48: {
            is3D = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            avatarId = input.ReadInt64();
            break;
          }
          case 18: {
            headPicUrl = input.ReadString();
            break;
          }
          case 24: {
            headBgColor = (global::Msg.basic.PB_BackgroundColorEnum) input.ReadEnum();
            break;
          }
          case 34: {
            name = input.ReadString();
            break;
          }
          case 42: {
            role = input.ReadString();
            break;
          }
          case 48: {
            is3D = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 场景信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_Scene : pb::IMessage<PB_Task_Scene>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_Scene> _parser = new pb::MessageParser<PB_Task_Scene>(() => new PB_Task_Scene());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_Scene> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Scene() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Scene(PB_Task_Scene other) : this() {
      bgPicTag_ = other.bgPicTag_;
      bgPicUrl_ = other.bgPicUrl_;
      bgVoiceTag_ = other.bgVoiceTag_;
      bgVoiceUrl_ = other.bgVoiceUrl_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Scene Clone() {
      return new PB_Task_Scene(this);
    }

    /// <summary>Field number for the "bgPicTag" field.</summary>
    public const int bgPicTagFieldNumber = 1;
    private string bgPicTag_ = "";
    /// <summary>
    /// 背景图片标签（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgPicTag {
      get { return bgPicTag_; }
      set {
        bgPicTag_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bgPicUrl" field.</summary>
    public const int bgPicUrlFieldNumber = 2;
    private string bgPicUrl_ = "";
    /// <summary>
    /// 背景图片地址（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgPicUrl {
      get { return bgPicUrl_; }
      set {
        bgPicUrl_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bgVoiceTag" field.</summary>
    public const int bgVoiceTagFieldNumber = 3;
    private string bgVoiceTag_ = "";
    /// <summary>
    /// 背景音标签（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgVoiceTag {
      get { return bgVoiceTag_; }
      set {
        bgVoiceTag_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bgVoiceUrl" field.</summary>
    public const int bgVoiceUrlFieldNumber = 4;
    private string bgVoiceUrl_ = "";
    /// <summary>
    /// 背景音标签（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgVoiceUrl {
      get { return bgVoiceUrl_; }
      set {
        bgVoiceUrl_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_Scene);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_Scene other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (bgPicTag != other.bgPicTag) return false;
      if (bgPicUrl != other.bgPicUrl) return false;
      if (bgVoiceTag != other.bgVoiceTag) return false;
      if (bgVoiceUrl != other.bgVoiceUrl) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (bgPicTag.Length != 0) hash ^= bgPicTag.GetHashCode();
      if (bgPicUrl.Length != 0) hash ^= bgPicUrl.GetHashCode();
      if (bgVoiceTag.Length != 0) hash ^= bgVoiceTag.GetHashCode();
      if (bgVoiceUrl.Length != 0) hash ^= bgVoiceUrl.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (bgPicTag.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(bgPicTag);
      }
      if (bgPicUrl.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(bgPicUrl);
      }
      if (bgVoiceTag.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bgVoiceTag);
      }
      if (bgVoiceUrl.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(bgVoiceUrl);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (bgPicTag.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(bgPicTag);
      }
      if (bgPicUrl.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(bgPicUrl);
      }
      if (bgVoiceTag.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bgVoiceTag);
      }
      if (bgVoiceUrl.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(bgVoiceUrl);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (bgPicTag.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgPicTag);
      }
      if (bgPicUrl.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgPicUrl);
      }
      if (bgVoiceTag.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgVoiceTag);
      }
      if (bgVoiceUrl.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgVoiceUrl);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_Scene other) {
      if (other == null) {
        return;
      }
      if (other.bgPicTag.Length != 0) {
        bgPicTag = other.bgPicTag;
      }
      if (other.bgPicUrl.Length != 0) {
        bgPicUrl = other.bgPicUrl;
      }
      if (other.bgVoiceTag.Length != 0) {
        bgVoiceTag = other.bgVoiceTag;
      }
      if (other.bgVoiceUrl.Length != 0) {
        bgVoiceUrl = other.bgVoiceUrl;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            bgPicTag = input.ReadString();
            break;
          }
          case 18: {
            bgPicUrl = input.ReadString();
            break;
          }
          case 26: {
            bgVoiceTag = input.ReadString();
            break;
          }
          case 34: {
            bgVoiceUrl = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            bgPicTag = input.ReadString();
            break;
          }
          case 18: {
            bgPicUrl = input.ReadString();
            break;
          }
          case 26: {
            bgVoiceTag = input.ReadString();
            break;
          }
          case 34: {
            bgVoiceUrl = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 任务详情
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_Detail : pb::IMessage<PB_Task_Detail>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_Detail> _parser = new pb::MessageParser<PB_Task_Detail>(() => new PB_Task_Detail());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_Detail> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Detail() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Detail(PB_Task_Detail other) : this() {
      desc_ = other.desc_;
      descType_ = other.descType_;
      switch (other.detailCase) {
        case detailOneofCase.rolePlayDetail:
          rolePlayDetail = other.rolePlayDetail.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Detail Clone() {
      return new PB_Task_Detail(this);
    }

    /// <summary>Field number for the "desc" field.</summary>
    public const int descFieldNumber = 1;
    private string desc_ = "";
    /// <summary>
    /// 任务描述（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string desc {
      get { return desc_; }
      set {
        desc_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "descType" field.</summary>
    public const int descTypeFieldNumber = 2;
    private global::Msg.explore.PB_Explore_TaskDescType descType_ = global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN;
    /// <summary>
    /// 任务描述类型（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_TaskDescType descType {
      get { return descType_; }
      set {
        descType_ = value;
      }
    }

    /// <summary>Field number for the "rolePlayDetail" field.</summary>
    public const int rolePlayDetailFieldNumber = 3;
    /// <summary>
    /// 角色扮演任务详情
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Task_Role_Play_Detail rolePlayDetail {
      get { return detailCase_ == detailOneofCase.rolePlayDetail ? (global::Msg.explore.PB_Task_Role_Play_Detail) detail_ : null; }
      set {
        detail_ = value;
        detailCase_ = value == null ? detailOneofCase.None : detailOneofCase.rolePlayDetail;
      }
    }

    private object detail_;
    /// <summary>Enum of possible cases for the "detail" oneof.</summary>
    public enum detailOneofCase {
      None = 0,
      rolePlayDetail = 3,
    }
    private detailOneofCase detailCase_ = detailOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public detailOneofCase detailCase {
      get { return detailCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void Cleardetail() {
      detailCase_ = detailOneofCase.None;
      detail_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_Detail);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_Detail other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (desc != other.desc) return false;
      if (descType != other.descType) return false;
      if (!object.Equals(rolePlayDetail, other.rolePlayDetail)) return false;
      if (detailCase != other.detailCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (desc.Length != 0) hash ^= desc.GetHashCode();
      if (descType != global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN) hash ^= descType.GetHashCode();
      if (detailCase_ == detailOneofCase.rolePlayDetail) hash ^= rolePlayDetail.GetHashCode();
      hash ^= (int) detailCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (desc.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(desc);
      }
      if (descType != global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) descType);
      }
      if (detailCase_ == detailOneofCase.rolePlayDetail) {
        output.WriteRawTag(26);
        output.WriteMessage(rolePlayDetail);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (desc.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(desc);
      }
      if (descType != global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) descType);
      }
      if (detailCase_ == detailOneofCase.rolePlayDetail) {
        output.WriteRawTag(26);
        output.WriteMessage(rolePlayDetail);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (desc.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(desc);
      }
      if (descType != global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) descType);
      }
      if (detailCase_ == detailOneofCase.rolePlayDetail) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(rolePlayDetail);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_Detail other) {
      if (other == null) {
        return;
      }
      if (other.desc.Length != 0) {
        desc = other.desc;
      }
      if (other.descType != global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN) {
        descType = other.descType;
      }
      switch (other.detailCase) {
        case detailOneofCase.rolePlayDetail:
          if (rolePlayDetail == null) {
            rolePlayDetail = new global::Msg.explore.PB_Task_Role_Play_Detail();
          }
          rolePlayDetail.MergeFrom(other.rolePlayDetail);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            desc = input.ReadString();
            break;
          }
          case 16: {
            descType = (global::Msg.explore.PB_Explore_TaskDescType) input.ReadEnum();
            break;
          }
          case 26: {
            global::Msg.explore.PB_Task_Role_Play_Detail subBuilder = new global::Msg.explore.PB_Task_Role_Play_Detail();
            if (detailCase_ == detailOneofCase.rolePlayDetail) {
              subBuilder.MergeFrom(rolePlayDetail);
            }
            input.ReadMessage(subBuilder);
            rolePlayDetail = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            desc = input.ReadString();
            break;
          }
          case 16: {
            descType = (global::Msg.explore.PB_Explore_TaskDescType) input.ReadEnum();
            break;
          }
          case 26: {
            global::Msg.explore.PB_Task_Role_Play_Detail subBuilder = new global::Msg.explore.PB_Task_Role_Play_Detail();
            if (detailCase_ == detailOneofCase.rolePlayDetail) {
              subBuilder.MergeFrom(rolePlayDetail);
            }
            input.ReadMessage(subBuilder);
            rolePlayDetail = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 角色扮演任务详情
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_Role_Play_Detail : pb::IMessage<PB_Task_Role_Play_Detail>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_Role_Play_Detail> _parser = new pb::MessageParser<PB_Task_Role_Play_Detail>(() => new PB_Task_Role_Play_Detail());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_Role_Play_Detail> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Role_Play_Detail() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Role_Play_Detail(PB_Task_Role_Play_Detail other) : this() {
      goals_ = other.goals_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Role_Play_Detail Clone() {
      return new PB_Task_Role_Play_Detail(this);
    }

    /// <summary>Field number for the "goals" field.</summary>
    public const int goalsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.explore.PB_Task_GoalItem> _repeated_goals_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.explore.PB_Task_GoalItem.Parser);
    private readonly pbc::RepeatedField<global::Msg.explore.PB_Task_GoalItem> goals_ = new pbc::RepeatedField<global::Msg.explore.PB_Task_GoalItem>();
    /// <summary>
    /// 任务目标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.explore.PB_Task_GoalItem> goals {
      get { return goals_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_Role_Play_Detail);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_Role_Play_Detail other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!goals_.Equals(other.goals_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= goals_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      goals_.WriteTo(output, _repeated_goals_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      goals_.WriteTo(ref output, _repeated_goals_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += goals_.CalculateSize(_repeated_goals_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_Role_Play_Detail other) {
      if (other == null) {
        return;
      }
      goals_.Add(other.goals_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            goals_.AddEntriesFrom(input, _repeated_goals_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            goals_.AddEntriesFrom(ref input, _repeated_goals_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 任务目标
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_GoalItem : pb::IMessage<PB_Task_GoalItem>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_GoalItem> _parser = new pb::MessageParser<PB_Task_GoalItem>(() => new PB_Task_GoalItem());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_GoalItem> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_GoalItem() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_GoalItem(PB_Task_GoalItem other) : this() {
      goalId_ = other.goalId_;
      no_ = other.no_;
      goalDesc_ = other.goalDesc_;
      isCompleted_ = other.isCompleted_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_GoalItem Clone() {
      return new PB_Task_GoalItem(this);
    }

    /// <summary>Field number for the "goalId" field.</summary>
    public const int goalIdFieldNumber = 1;
    private long goalId_;
    /// <summary>
    /// 目标id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long goalId {
      get { return goalId_; }
      set {
        goalId_ = value;
      }
    }

    /// <summary>Field number for the "no" field.</summary>
    public const int noFieldNumber = 2;
    private int no_;
    /// <summary>
    /// 目标序号
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int no {
      get { return no_; }
      set {
        no_ = value;
      }
    }

    /// <summary>Field number for the "goalDesc" field.</summary>
    public const int goalDescFieldNumber = 3;
    private string goalDesc_ = "";
    /// <summary>
    /// 目标描述
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string goalDesc {
      get { return goalDesc_; }
      set {
        goalDesc_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "isCompleted" field.</summary>
    public const int isCompletedFieldNumber = 4;
    private bool isCompleted_;
    /// <summary>
    /// 是否完成
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool isCompleted {
      get { return isCompleted_; }
      set {
        isCompleted_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_GoalItem);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_GoalItem other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (goalId != other.goalId) return false;
      if (no != other.no) return false;
      if (goalDesc != other.goalDesc) return false;
      if (isCompleted != other.isCompleted) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (goalId != 0L) hash ^= goalId.GetHashCode();
      if (no != 0) hash ^= no.GetHashCode();
      if (goalDesc.Length != 0) hash ^= goalDesc.GetHashCode();
      if (isCompleted != false) hash ^= isCompleted.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (goalId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(goalId);
      }
      if (no != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(no);
      }
      if (goalDesc.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(goalDesc);
      }
      if (isCompleted != false) {
        output.WriteRawTag(32);
        output.WriteBool(isCompleted);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (goalId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(goalId);
      }
      if (no != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(no);
      }
      if (goalDesc.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(goalDesc);
      }
      if (isCompleted != false) {
        output.WriteRawTag(32);
        output.WriteBool(isCompleted);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (goalId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(goalId);
      }
      if (no != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(no);
      }
      if (goalDesc.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(goalDesc);
      }
      if (isCompleted != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_GoalItem other) {
      if (other == null) {
        return;
      }
      if (other.goalId != 0L) {
        goalId = other.goalId;
      }
      if (other.no != 0) {
        no = other.no;
      }
      if (other.goalDesc.Length != 0) {
        goalDesc = other.goalDesc;
      }
      if (other.isCompleted != false) {
        isCompleted = other.isCompleted;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            goalId = input.ReadInt64();
            break;
          }
          case 16: {
            no = input.ReadInt32();
            break;
          }
          case 26: {
            goalDesc = input.ReadString();
            break;
          }
          case 32: {
            isCompleted = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            goalId = input.ReadInt64();
            break;
          }
          case 16: {
            no = input.ReadInt32();
            break;
          }
          case 26: {
            goalDesc = input.ReadString();
            break;
          }
          case 32: {
            isCompleted = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 首轮数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_First_Round_Data : pb::IMessage<PB_Task_First_Round_Data>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_First_Round_Data> _parser = new pb::MessageParser<PB_Task_First_Round_Data>(() => new PB_Task_First_Round_Data());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_First_Round_Data> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_First_Round_Data() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_First_Round_Data(PB_Task_First_Round_Data other) : this() {
      replyText_ = other.replyText_;
      replyTranslateText_ = other.replyTranslateText_;
      replyAudio_ = other.replyAudio_ != null ? other.replyAudio_.Clone() : null;
      exampleText_ = other.exampleText_;
      exampleTranslateText_ = other.exampleTranslateText_;
      exampleAudio_ = other.exampleAudio_ != null ? other.exampleAudio_.Clone() : null;
      replyBubbleId_ = other.replyBubbleId_;
      exampleBubbleId_ = other.exampleBubbleId_;
      emotionAnalysisResult_ = other.emotionAnalysisResult_ != null ? other.emotionAnalysisResult_.Clone() : null;
      adviceText_ = other.adviceText_;
      adviceBubbleId_ = other.adviceBubbleId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_First_Round_Data Clone() {
      return new PB_Task_First_Round_Data(this);
    }

    /// <summary>Field number for the "replyText" field.</summary>
    public const int replyTextFieldNumber = 1;
    private string replyText_ = "";
    /// <summary>
    /// Avatar回复文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyText {
      get { return replyText_; }
      set {
        replyText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "replyTranslateText" field.</summary>
    public const int replyTranslateTextFieldNumber = 2;
    private string replyTranslateText_ = "";
    /// <summary>
    /// Avatar回复翻译文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyTranslateText {
      get { return replyTranslateText_; }
      set {
        replyTranslateText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "replyAudio" field.</summary>
    public const int replyAudioFieldNumber = 3;
    private global::Msg.explore.PB_Explore_DialogAudioDownFrame replyAudio_;
    /// <summary>
    /// Avatar回复TTS音频
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_DialogAudioDownFrame replyAudio {
      get { return replyAudio_; }
      set {
        replyAudio_ = value;
      }
    }

    /// <summary>Field number for the "exampleText" field.</summary>
    public const int exampleTextFieldNumber = 4;
    private string exampleText_ = "";
    /// <summary>
    /// 用户回复示例文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleText {
      get { return exampleText_; }
      set {
        exampleText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "exampleTranslateText" field.</summary>
    public const int exampleTranslateTextFieldNumber = 5;
    private string exampleTranslateText_ = "";
    /// <summary>
    /// 用户回复示例翻译文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleTranslateText {
      get { return exampleTranslateText_; }
      set {
        exampleTranslateText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "exampleAudio" field.</summary>
    public const int exampleAudioFieldNumber = 6;
    private global::Msg.explore.PB_Explore_DialogAudioDownFrame exampleAudio_;
    /// <summary>
    /// 用户回复示例音频
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_DialogAudioDownFrame exampleAudio {
      get { return exampleAudio_; }
      set {
        exampleAudio_ = value;
      }
    }

    /// <summary>Field number for the "replyBubbleId" field.</summary>
    public const int replyBubbleIdFieldNumber = 7;
    private string replyBubbleId_ = "";
    /// <summary>
    /// Avatar回复气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyBubbleId {
      get { return replyBubbleId_; }
      set {
        replyBubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "exampleBubbleId" field.</summary>
    public const int exampleBubbleIdFieldNumber = 8;
    private string exampleBubbleId_ = "";
    /// <summary>
    /// 用户回复示例气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleBubbleId {
      get { return exampleBubbleId_; }
      set {
        exampleBubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "emotionAnalysisResult" field.</summary>
    public const int emotionAnalysisResultFieldNumber = 9;
    private global::Msg.explore.PB_EmotionAnalysisResult emotionAnalysisResult_;
    /// <summary>
    /// 情感分析结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_EmotionAnalysisResult emotionAnalysisResult {
      get { return emotionAnalysisResult_; }
      set {
        emotionAnalysisResult_ = value;
      }
    }

    /// <summary>Field number for the "adviceText" field.</summary>
    public const int adviceTextFieldNumber = 10;
    private string adviceText_ = "";
    /// <summary>
    /// Advice
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string adviceText {
      get { return adviceText_; }
      set {
        adviceText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "adviceBubbleId" field.</summary>
    public const int adviceBubbleIdFieldNumber = 11;
    private string adviceBubbleId_ = "";
    /// <summary>
    /// Advice气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string adviceBubbleId {
      get { return adviceBubbleId_; }
      set {
        adviceBubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_First_Round_Data);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_First_Round_Data other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (replyText != other.replyText) return false;
      if (replyTranslateText != other.replyTranslateText) return false;
      if (!object.Equals(replyAudio, other.replyAudio)) return false;
      if (exampleText != other.exampleText) return false;
      if (exampleTranslateText != other.exampleTranslateText) return false;
      if (!object.Equals(exampleAudio, other.exampleAudio)) return false;
      if (replyBubbleId != other.replyBubbleId) return false;
      if (exampleBubbleId != other.exampleBubbleId) return false;
      if (!object.Equals(emotionAnalysisResult, other.emotionAnalysisResult)) return false;
      if (adviceText != other.adviceText) return false;
      if (adviceBubbleId != other.adviceBubbleId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (replyText.Length != 0) hash ^= replyText.GetHashCode();
      if (replyTranslateText.Length != 0) hash ^= replyTranslateText.GetHashCode();
      if (replyAudio_ != null) hash ^= replyAudio.GetHashCode();
      if (exampleText.Length != 0) hash ^= exampleText.GetHashCode();
      if (exampleTranslateText.Length != 0) hash ^= exampleTranslateText.GetHashCode();
      if (exampleAudio_ != null) hash ^= exampleAudio.GetHashCode();
      if (replyBubbleId.Length != 0) hash ^= replyBubbleId.GetHashCode();
      if (exampleBubbleId.Length != 0) hash ^= exampleBubbleId.GetHashCode();
      if (emotionAnalysisResult_ != null) hash ^= emotionAnalysisResult.GetHashCode();
      if (adviceText.Length != 0) hash ^= adviceText.GetHashCode();
      if (adviceBubbleId.Length != 0) hash ^= adviceBubbleId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (replyText.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(replyText);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(replyTranslateText);
      }
      if (replyAudio_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(replyAudio);
      }
      if (exampleText.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(exampleText);
      }
      if (exampleTranslateText.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(exampleTranslateText);
      }
      if (exampleAudio_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(exampleAudio);
      }
      if (replyBubbleId.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(replyBubbleId);
      }
      if (exampleBubbleId.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(exampleBubbleId);
      }
      if (emotionAnalysisResult_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(emotionAnalysisResult);
      }
      if (adviceText.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(adviceText);
      }
      if (adviceBubbleId.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(adviceBubbleId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (replyText.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(replyText);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(replyTranslateText);
      }
      if (replyAudio_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(replyAudio);
      }
      if (exampleText.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(exampleText);
      }
      if (exampleTranslateText.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(exampleTranslateText);
      }
      if (exampleAudio_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(exampleAudio);
      }
      if (replyBubbleId.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(replyBubbleId);
      }
      if (exampleBubbleId.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(exampleBubbleId);
      }
      if (emotionAnalysisResult_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(emotionAnalysisResult);
      }
      if (adviceText.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(adviceText);
      }
      if (adviceBubbleId.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(adviceBubbleId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (replyText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyText);
      }
      if (replyTranslateText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyTranslateText);
      }
      if (replyAudio_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(replyAudio);
      }
      if (exampleText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleText);
      }
      if (exampleTranslateText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleTranslateText);
      }
      if (exampleAudio_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(exampleAudio);
      }
      if (replyBubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyBubbleId);
      }
      if (exampleBubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleBubbleId);
      }
      if (emotionAnalysisResult_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(emotionAnalysisResult);
      }
      if (adviceText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(adviceText);
      }
      if (adviceBubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(adviceBubbleId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_First_Round_Data other) {
      if (other == null) {
        return;
      }
      if (other.replyText.Length != 0) {
        replyText = other.replyText;
      }
      if (other.replyTranslateText.Length != 0) {
        replyTranslateText = other.replyTranslateText;
      }
      if (other.replyAudio_ != null) {
        if (replyAudio_ == null) {
          replyAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
        }
        replyAudio.MergeFrom(other.replyAudio);
      }
      if (other.exampleText.Length != 0) {
        exampleText = other.exampleText;
      }
      if (other.exampleTranslateText.Length != 0) {
        exampleTranslateText = other.exampleTranslateText;
      }
      if (other.exampleAudio_ != null) {
        if (exampleAudio_ == null) {
          exampleAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
        }
        exampleAudio.MergeFrom(other.exampleAudio);
      }
      if (other.replyBubbleId.Length != 0) {
        replyBubbleId = other.replyBubbleId;
      }
      if (other.exampleBubbleId.Length != 0) {
        exampleBubbleId = other.exampleBubbleId;
      }
      if (other.emotionAnalysisResult_ != null) {
        if (emotionAnalysisResult_ == null) {
          emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
        }
        emotionAnalysisResult.MergeFrom(other.emotionAnalysisResult);
      }
      if (other.adviceText.Length != 0) {
        adviceText = other.adviceText;
      }
      if (other.adviceBubbleId.Length != 0) {
        adviceBubbleId = other.adviceBubbleId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            replyText = input.ReadString();
            break;
          }
          case 18: {
            replyTranslateText = input.ReadString();
            break;
          }
          case 26: {
            if (replyAudio_ == null) {
              replyAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
            }
            input.ReadMessage(replyAudio);
            break;
          }
          case 34: {
            exampleText = input.ReadString();
            break;
          }
          case 42: {
            exampleTranslateText = input.ReadString();
            break;
          }
          case 50: {
            if (exampleAudio_ == null) {
              exampleAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
            }
            input.ReadMessage(exampleAudio);
            break;
          }
          case 58: {
            replyBubbleId = input.ReadString();
            break;
          }
          case 66: {
            exampleBubbleId = input.ReadString();
            break;
          }
          case 74: {
            if (emotionAnalysisResult_ == null) {
              emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
            }
            input.ReadMessage(emotionAnalysisResult);
            break;
          }
          case 82: {
            adviceText = input.ReadString();
            break;
          }
          case 90: {
            adviceBubbleId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            replyText = input.ReadString();
            break;
          }
          case 18: {
            replyTranslateText = input.ReadString();
            break;
          }
          case 26: {
            if (replyAudio_ == null) {
              replyAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
            }
            input.ReadMessage(replyAudio);
            break;
          }
          case 34: {
            exampleText = input.ReadString();
            break;
          }
          case 42: {
            exampleTranslateText = input.ReadString();
            break;
          }
          case 50: {
            if (exampleAudio_ == null) {
              exampleAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
            }
            input.ReadMessage(exampleAudio);
            break;
          }
          case 58: {
            replyBubbleId = input.ReadString();
            break;
          }
          case 66: {
            exampleBubbleId = input.ReadString();
            break;
          }
          case 74: {
            if (emotionAnalysisResult_ == null) {
              emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
            }
            input.ReadMessage(emotionAnalysisResult);
            break;
          }
          case 82: {
            adviceText = input.ReadString();
            break;
          }
          case 90: {
            adviceBubbleId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 推荐功能上行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_RecommendUpMsg : pb::IMessage<PB_RecommendUpMsg>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_RecommendUpMsg> _parser = new pb::MessageParser<PB_RecommendUpMsg>(() => new PB_RecommendUpMsg());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_RecommendUpMsg> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RecommendUpMsg() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RecommendUpMsg(PB_RecommendUpMsg other) : this() {
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.userSwitchEntity:
          userSwitchEntity = other.userSwitchEntity.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RecommendUpMsg Clone() {
      return new PB_RecommendUpMsg(this);
    }

    /// <summary>Field number for the "userSwitchEntity" field.</summary>
    public const int userSwitchEntityFieldNumber = 1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_RecommendSwitchEntity userSwitchEntity {
      get { return upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity ? (global::Msg.explore.PB_Explore_RecommendSwitchEntity) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.userSwitchEntity;
      }
    }

    private object upBizMsg_;
    /// <summary>Enum of possible cases for the "upBizMsg" oneof.</summary>
    public enum upBizMsgOneofCase {
      None = 0,
      userSwitchEntity = 1,
    }
    private upBizMsgOneofCase upBizMsgCase_ = upBizMsgOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public upBizMsgOneofCase upBizMsgCase {
      get { return upBizMsgCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearupBizMsg() {
      upBizMsgCase_ = upBizMsgOneofCase.None;
      upBizMsg_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_RecommendUpMsg);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_RecommendUpMsg other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(userSwitchEntity, other.userSwitchEntity)) return false;
      if (upBizMsgCase != other.upBizMsgCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) hash ^= userSwitchEntity.GetHashCode();
      hash ^= (int) upBizMsgCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) {
        output.WriteRawTag(10);
        output.WriteMessage(userSwitchEntity);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) {
        output.WriteRawTag(10);
        output.WriteMessage(userSwitchEntity);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(userSwitchEntity);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_RecommendUpMsg other) {
      if (other == null) {
        return;
      }
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.userSwitchEntity:
          if (userSwitchEntity == null) {
            userSwitchEntity = new global::Msg.explore.PB_Explore_RecommendSwitchEntity();
          }
          userSwitchEntity.MergeFrom(other.userSwitchEntity);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::Msg.explore.PB_Explore_RecommendSwitchEntity subBuilder = new global::Msg.explore.PB_Explore_RecommendSwitchEntity();
            if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) {
              subBuilder.MergeFrom(userSwitchEntity);
            }
            input.ReadMessage(subBuilder);
            userSwitchEntity = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            global::Msg.explore.PB_Explore_RecommendSwitchEntity subBuilder = new global::Msg.explore.PB_Explore_RecommendSwitchEntity();
            if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) {
              subBuilder.MergeFrom(userSwitchEntity);
            }
            input.ReadMessage(subBuilder);
            userSwitchEntity = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 推荐上行 - 切换推荐实体
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Explore_RecommendSwitchEntity : pb::IMessage<PB_Explore_RecommendSwitchEntity>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Explore_RecommendSwitchEntity> _parser = new pb::MessageParser<PB_Explore_RecommendSwitchEntity>(() => new PB_Explore_RecommendSwitchEntity());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Explore_RecommendSwitchEntity> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_RecommendSwitchEntity() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_RecommendSwitchEntity(PB_Explore_RecommendSwitchEntity other) : this() {
      entityId_ = other.entityId_;
      entityType_ = other.entityType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_RecommendSwitchEntity Clone() {
      return new PB_Explore_RecommendSwitchEntity(this);
    }

    /// <summary>Field number for the "entityId" field.</summary>
    public const int entityIdFieldNumber = 1;
    private long entityId_;
    /// <summary>
    /// 实体id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long entityId {
      get { return entityId_; }
      set {
        entityId_ = value;
      }
    }

    /// <summary>Field number for the "entityType" field.</summary>
    public const int entityTypeFieldNumber = 2;
    private global::Msg.explore.PB_Explore_RecommendEntityType entityType_ = global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN;
    /// <summary>
    /// 实体类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_RecommendEntityType entityType {
      get { return entityType_; }
      set {
        entityType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Explore_RecommendSwitchEntity);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Explore_RecommendSwitchEntity other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (entityId != other.entityId) return false;
      if (entityType != other.entityType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (entityId != 0L) hash ^= entityId.GetHashCode();
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) hash ^= entityType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (entityId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) entityType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (entityId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) entityType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (entityId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) entityType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Explore_RecommendSwitchEntity other) {
      if (other == null) {
        return;
      }
      if (other.entityId != 0L) {
        entityId = other.entityId;
      }
      if (other.entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        entityType = other.entityType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            entityId = input.ReadInt64();
            break;
          }
          case 16: {
            entityType = (global::Msg.explore.PB_Explore_RecommendEntityType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            entityId = input.ReadInt64();
            break;
          }
          case 16: {
            entityType = (global::Msg.explore.PB_Explore_RecommendEntityType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 推荐下行 - 切换推荐实体
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_RecommendSwitchEntity : pb::IMessage<SC_RecommendSwitchEntity>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_RecommendSwitchEntity> _parser = new pb::MessageParser<SC_RecommendSwitchEntity>(() => new SC_RecommendSwitchEntity());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_RecommendSwitchEntity> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RecommendSwitchEntity() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RecommendSwitchEntity(SC_RecommendSwitchEntity other) : this() {
      timestamp_ = other.timestamp_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RecommendSwitchEntity Clone() {
      return new SC_RecommendSwitchEntity(this);
    }

    /// <summary>Field number for the "timestamp" field.</summary>
    public const int timestampFieldNumber = 1;
    private long timestamp_;
    /// <summary>
    /// 切换成功时间戳（单位：秒）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long timestamp {
      get { return timestamp_; }
      set {
        timestamp_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_RecommendSwitchEntity);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_RecommendSwitchEntity other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (timestamp != other.timestamp) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (timestamp != 0L) hash ^= timestamp.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (timestamp != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(timestamp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (timestamp != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(timestamp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (timestamp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(timestamp);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_RecommendSwitchEntity other) {
      if (other == null) {
        return;
      }
      if (other.timestamp != 0L) {
        timestamp = other.timestamp;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            timestamp = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            timestamp = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/economic/merchandise.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.economic {

  /// <summary>Holder for reflection information generated from protobuf/economic/merchandise.proto</summary>
  public static partial class MerchandiseReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/economic/merchandise.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static MerchandiseReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiNwcm90b2J1Zi9lY29ub21pYy9tZXJjaGFuZGlzZS5wcm90bxobcHJvdG9i",
            "dWYvYmFzaWMvY29tbW9uLnByb3RvGh1wcm90b2J1Zi9iYXNpYy9lY29ub21p",
            "Yy5wcm90bxoccHJvdG9idWYvZWNvbm9taWMvYmFzZS5wcm90bxofcHJvdG9i",
            "dWYvZWNvbm9taWMvYmVuZWZpdC5wcm90byJuChFDU19HZXRTaG9wSW5mb1Jl",
            "cRIUCgxjb3VudHJ5X2NvZGUYASABKAkSEgoKcmVnaW9uX2tleRgCIAEoCRIv",
            "Cg9lY29ub21pY19zb3VyY2UYAyABKA4yFi5QQl9FY29ub21pY1NvdXJjZUVu",
            "dW0iTwoSU0NfR2V0U2hvcEluZm9SZXNwEhkKBGNvZGUYASABKA4yCy5QQl9C",
            "aXpDb2RlEh4KBGRhdGEYAiABKAsyEC5QQl9TaG9wSW5mb0RhdGEiuAMKD1BC",
            "X1Nob3BJbmZvRGF0YRIwChJzdWJzY3JpcHRpb25faW5mb3MYASADKAsyFC5Q",
            "Ql9TdWJzY3JpcHRpb25JbmZvEjQKFWluX2FwcF9wdXJjaGFzZV9pbmZvcxgC",
            "IAMoCzIVLlBCX0luQXBwUHVyY2hhc2VJbmZvEjcKFnVzZXJfbWVyY2hhbmRp",
            "c2VfaW5mb3MYAyADKAsyFy5QQl9Vc2VyTWVyY2hhbmRpc2VJbmZvEicKC21l",
            "bWJlcl9pbmZvGAQgASgLMhIuUEJfTWVtYmVySW5mb1Jlc3ASLgoRbWVyY2hh",
            "bmRpc2VfaW5mb3MYBSADKAsyEy5QQl9NZXJjaGFuZGlzZUluZm8SKQoOcHJv",
            "bW90aW9uX3R5cGUYBiABKA4yES5QQl9Qcm9tb3Rpb25UeXBlEj4KGWxpbWl0",
            "ZWRfc3Vic2NyaXB0aW9uX2luZm8YByABKAsyGy5QQl9MaW1pdGVkU3Vic2Ny",
            "aXB0aW9uSW5mbxJAChpsaWZldGltZV9zdWJzY3JpcHRpb25faW5mbxgIIAEo",
            "CzIcLlBCX0xpZmV0aW1lU3Vic2NyaXB0aW9uSW5mbyJOChtQQl9MaWZldGlt",
            "ZVN1YnNjcmlwdGlvbkluZm8SLwoRc3Vic2NyaXB0aW9uX2luZm8YASABKAsy",
            "FC5QQl9TdWJzY3JpcHRpb25JbmZvIm8KGlBCX0xpbWl0ZWRTdWJzY3JpcHRp",
            "b25JbmZvEjAKEnN1YnNjcmlwdGlvbl9pbmZvcxgBIAMoCzIULlBCX1N1YnNj",
            "cmlwdGlvbkluZm8SHwoXZW5kX21pbGxpc2Vjb25kc19pbl91dGMYAiABKAMi",
            "xAMKE1BCX1N1YnNjcmlwdGlvbkluZm8SFwoLbWVtYmVyX25hbWUYASABKAlC",
            "AhgBEhIKCnByb2R1Y3RfaWQYAiABKAkSGAoQcHJpY2VfaW5fZGlzcGxheRgD",
            "IAEoCRIWCg5wcmljZV9pbl9jZW50cxgEIAEoBRIfChdvcmlnaW5fcHJpY2Vf",
            "aW5fZGlzcGxheRgFIAEoCRIgChhwZXJfZGF5X3ByaWNlX2luX2Rpc3BsYXkY",
            "BiABKAkSGwoTZGFpbHlfZGlhbW9uZF9jb3VudBgHIAEoBRIRCglmcmVlX2Rh",
            "eXMYCCABKAUSHwoXcmVtYWluX3ByaWNlX2luX2Rpc3BsYXkYCSABKAkSKgoe",
            "cHJvbW90ZWRfcHVyY2hhc2VfcmVtYWluc19kYXlzGAogASgDQgIYARIiChpw",
            "cmljZV9wZXJfbW9udGhfaW5fZGlzcGxheRgLIAEoCRITCgttb250aF9jb3Vu",
            "dBgMIAEoBRIdChVvcmlnaW5fcHJpY2VfaW5fY2VudHMYDSABKAUSIAoLbWVt",
            "YmVyX3R5cGUYDiABKA4yCy5NZW1iZXJUeXBlEhQKDGlzX3JlY29tbWVuZBgP",
            "IAEoCCL1AQoUUEJfSW5BcHBQdXJjaGFzZUluZm8SEgoKcHJvZHVjdF9pZBgB",
            "IAEoCRIYChBwcmljZV9pbl9kaXNwbGF5GAIgASgJEhYKDnByaWNlX2luX2Nl",
            "bnRzGAMgASgFEg0KBWNvdW50GAQgASgFEhEKCWljb25fcGF0aBgFIAEoCRIW",
            "Cg5pc19vbmxpbmVfaXRlbRgGIAEoCBIfChdvcmlnaW5fcHJpY2VfaW5fZGlz",
            "cGxheRgHIAEoCRImCh5wcm9tb3RlZF9wdXJjaGFzZV9yZW1haW5zX2RheXMY",
            "CCABKAMSFAoMbWF4X3ByZV91c2VyGAkgASgFIlsKHlNTX0dldE1lcmNoYW5k",
            "aXNlQ2hhbmdlSW5mb1JlcRIPCgd1c2VyX2lkGAEgASgDEigKBWl0ZW1zGAIg",
            "AygLMhkuUEJfTWVyY2hhbmRpc2VDaGFuZ2VJdGVtIo8BChhQQl9NZXJjaGFu",
            "ZGlzZUNoYW5nZUl0ZW0SEAoIYml6X2NvZGUYASABKAkSDgoGc291cmNlGAIg",
            "ASgJEhsKE21lcmNoYWRpc2Vfb3JkZXJfaWQYAyABKAkSNAoLc291cmNlX3R5",
            "cGUYBCABKA4yHy5QQl9NZXJjaGFuZGlzZUNoYW5nZVNvdXJjZVR5cGUiZQof",
            "U1NfR2V0TWVyY2hhbmRpc2VDaGFuZ2VJbmZvUmVzcBIZCgRjb2RlGAEgASgO",
            "MgsuUEJfQml6Q29kZRInCgRkYXRhGAIgASgLMhkuUEJfTWVyY2hhbmRpc2VD",
            "aGFuZ2VEYXRhIkYKGFBCX01lcmNoYW5kaXNlQ2hhbmdlRGF0YRIqCgVpdGVt",
            "cxgBIAMoCzIbLlBCX01lcmNoYW5kaXNlQ2hhbmdlRGV0YWlsItkBChpQQl9N",
            "ZXJjaGFuZGlzZUNoYW5nZURldGFpbBIQCghiaXpfY29kZRgBIAEoCRItChBt",
            "ZXJjaGFuZGlzZV90eXBlGAIgASgOMhMuUEJfTWVyY2hhbmRpc2VUeXBlEiEK",
            "CnRyYWRlX3R5cGUYAyABKA4yDS5QQl9UcmFkZVR5cGUSDgoGYW1vdW50GAQg",
            "ASgDEhQKDHByZXZfYmFsYW5jZRgFIAEoAxIUCgxjdXJyX2JhbGFuY2UYBiAB",
            "KAMSGwoTbWVyY2hhZGlzZV9vcmRlcl9pZBgHIAEoCSJ6ChxTU19HZXRVc2Vy",
            "TWVyY2hhbmRpc2VJbmZvUmVxEg8KB3VzZXJfaWQYASABKAMSGAoQbWVyY2hh",
            "bmRpc2VfdHlwZRgCIAEoCRIcChRtZXJjaGFuZGlzZV9zdWJfdHlwZRgDIAEo",
            "CRIRCglhbW91bnRfZ3QYBCABKAMiYQodU1NfR2V0VXNlck1lcmNoYW5kaXNl",
            "SW5mb1Jlc3ASGQoEY29kZRgBIAEoDjILLlBCX0JpekNvZGUSJQoEZGF0YRgC",
            "IAEoCzIXLlBCX1VzZXJNZXJjaGFuZGlzZURhdGEiQAoWUEJfVXNlck1lcmNo",
            "YW5kaXNlRGF0YRImCgVpdGVtcxgBIAMoCzIXLlBCX1VzZXJNZXJjaGFuZGlz",
            "ZUluZm8iggEKHFNTX1NldFVzZXJNZXJjaGFuZGlzZUluZm9SZXESDwoHdXNl",
            "cl9pZBgBIAEoAxIWCg5tZXJjaGFuZGlzZV9pZBgCIAEoCRIOCgZhbW91bnQY",
            "AyABKAMSFAoMaXNfdGltZV9pdGVtGAQgASgIEhMKC2lzX2FkZF9pdGVtGAUg",
            "ASgIImEKHVNTX1NldFVzZXJNZXJjaGFuZGlzZUluZm9SZXNwEhkKBGNvZGUY",
            "ASABKA4yCy5QQl9CaXpDb2RlEiUKBGRhdGEYAiABKAsyFy5QQl9Vc2VyTWVy",
            "Y2hhbmRpc2VJbmZvIjkKFENTX1BheU1lcmNoYW5kaXNlUmVxEhIKCnByb2R1",
            "Y3RfaWQYASABKAkSDQoFY291bnQYAiABKAUiWQoVU0NfUGF5TWVyY2hhbmRp",
            "c2VSZXNwEhkKBGNvZGUYASABKA4yCy5QQl9CaXpDb2RlEiUKBGRhdGEYAiAB",
            "KAsyFy5QQl9Vc2VyTWVyY2hhbmRpc2VJbmZvIk4KFFNTX1VzZU1lcmNoYW5k",
            "aXNlUmVxEg8KB3VzZXJfaWQYASABKAMSFgoObWVyY2hhbmRpc2VfaWQYAiAB",
            "KAkSDQoFY291bnQYAyABKAUiMgoVU1NfVXNlTWVyY2hhbmRpc2VSZXNwEhkK",
            "BGNvZGUYASABKA4yCy5QQl9CaXpDb2RlIkUKGlNTX0dldE1lcmNoYW5kaXNl",
            "RGV0YWlsUmVxEg8KB3VzZXJfaWQYASABKAMSFgoObWVyY2hhbmRpc2VfaWQY",
            "AiABKAkiXQobU1NfR2V0TWVyY2hhbmRpc2VEZXRhaWxSZXNwEhkKBGNvZGUY",
            "ASABKA4yCy5QQl9CaXpDb2RlEiMKBGRhdGEYAiABKAsyFS5QQl9NZXJjaGFu",
            "ZGlzZURldGFpbCKxAQoUUEJfTWVyY2hhbmRpc2VEZXRhaWwSFgoObWVyY2hh",
            "bmRpc2VfaWQYASABKAkSLQoQbWVyY2hhbmRpc2VfdHlwZRgCIAEoDjITLlBC",
            "X01lcmNoYW5kaXNlVHlwZRI0ChRtZXJjaGFuZGlzZV9zdWJfdHlwZRgDIAEo",
            "DjIWLlBCX01lcmNoYW5kaXNlU3ViVHlwZRIMCgRuYW1lGAQgASgJEg4KBmRl",
            "dGFpbBgFIAEoCSJHChxTU19HZXRVc2VySW50aW1hY3lCb3hJbmZvUmVxEg8K",
            "B3VzZXJfaWQYASABKAMSFgoOaW50aW1hY3lfbGV2ZWwYAiABKAUiXQodU1Nf",
            "R2V0VXNlckludGltYWN5Qm94SW5mb1Jlc3ASGQoEY29kZRgBIAEoDjILLlBC",
            "X0JpekNvZGUSIQoEZGF0YRgCIAEoCzITLlBCX0ludGltYWN5Qm94SW5mbyIm",
            "ChJQQl9JbnRpbWFjeUJveEluZm8SEAoIYml6X2NvZGUYASABKAkiVAoaU1Nf",
            "R2V0TWVyY2hhbmRpc2VCeVR5cGVSZXESGAoQbWVyY2hhbmRpc2VfdHlwZRgB",
            "IAEoCRIcChRtZXJjaGFuZGlzZV9zdWJfdHlwZRgCIAEoCSJdChtTU19HZXRN",
            "ZXJjaGFuZGlzZUJ5VHlwZVJlc3ASGQoEY29kZRgBIAEoDjILLlBCX0JpekNv",
            "ZGUSIwoEZGF0YRgCIAMoCzIVLlBCX01lcmNoYW5kaXNlRGV0YWlsIi0KGlNT",
            "X0dldFVzZXJMaW1pdFByb3BJbmZvUmVxEg8KB3VzZXJfaWQYASABKAMi5gEK",
            "G1NTX0dldFVzZXJMaW1pdFByb3BJbmZvUmVzcBIZCgRjb2RlGAEgASgOMgsu",
            "UEJfQml6Q29kZRJWChZ1c2VyX21lcmNoYW5kaXNlX2luZm9zGAIgAygLMjYu",
            "U1NfR2V0VXNlckxpbWl0UHJvcEluZm9SZXNwLlVzZXJNZXJjaGFuZGlzZUlu",
            "Zm9zRW50cnkaVAoZVXNlck1lcmNoYW5kaXNlSW5mb3NFbnRyeRILCgNrZXkY",
            "ASABKAkSJgoFdmFsdWUYAiABKAsyFy5QQl9Vc2VyTWVyY2hhbmRpc2VJbmZv",
            "OgI4ASIsChlTU19Jbml0VXNlck1lcmNoYW5kaXNlUmVxEg8KB3VzZXJfaWQY",
            "ASABKAMiNwoaU1NfSW5pdFVzZXJNZXJjaGFuZGlzZVJlc3ASGQoEY29kZRgB",
            "IAEoDjILLlBCX0JpekNvZGUqTQoQUEJfUHJvbW90aW9uVHlwZRIJCgVQTk9O",
            "RRAAEgwKCERJU0NPVU5UEAESDgoKRlJFRV9UUklBTBACEhAKDE9SSUdJTl9Q",
            "UklDRRADKmEKFkluQXBwUHVyY2hhc2VQcmljZVR5cGUSHwobSU5fQVBQX1BV",
            "UkNIQVNFX1BSSUNFX1RZUEVfEAASJgoiSU5fQVBQX1BVUkNIQVNFX1BSSUNF",
            "X1RZUEVfRGlhbW9uZBABKqkBCh5QQl9NZXJjaGFuZGlzZUNoYW5nZVNvdXJj",
            "ZVR5cGUSJwojUEJfTWVyY2hhbmRpc2VDaGFuZ2VTb3VyY2VUeXBlX05vbmUQ",
            "ABIuCipQQl9NZXJjaGFuZGlzZUNoYW5nZVNvdXJjZVR5cGVfSW50aW1hY3lC",
            "b3gQARIuCipQQl9NZXJjaGFuZGlzZUNoYW5nZVNvdXJjZVR5cGVfQ2hhdHB0",
            "ZXJCb3gQAkIsWht2Zl9wcm90b2J1Zi9zZXJ2ZXIvZWNvbm9taWOqAgxNc2cu",
            "ZWNvbm9taWNiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.basic.CommonReflection.Descriptor, global::Msg.basic.EconomicReflection.Descriptor, global::Msg.economic.BaseReflection.Descriptor, global::Msg.economic.BenefitReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.economic.PB_PromotionType), typeof(global::Msg.economic.InAppPurchasePriceType), typeof(global::Msg.economic.PB_MerchandiseChangeSourceType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.CS_GetShopInfoReq), global::Msg.economic.CS_GetShopInfoReq.Parser, new[]{ "country_code", "region_key", "economic_source" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SC_GetShopInfoResp), global::Msg.economic.SC_GetShopInfoResp.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.PB_ShopInfoData), global::Msg.economic.PB_ShopInfoData.Parser, new[]{ "subscription_infos", "in_app_purchase_infos", "user_merchandise_infos", "member_info", "merchandise_infos", "promotion_type", "limited_subscription_info", "lifetime_subscription_info" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.PB_LifetimeSubscriptionInfo), global::Msg.economic.PB_LifetimeSubscriptionInfo.Parser, new[]{ "subscription_info" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.PB_LimitedSubscriptionInfo), global::Msg.economic.PB_LimitedSubscriptionInfo.Parser, new[]{ "subscription_infos", "end_milliseconds_in_utc" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.PB_SubscriptionInfo), global::Msg.economic.PB_SubscriptionInfo.Parser, new[]{ "member_name", "product_id", "price_in_display", "price_in_cents", "origin_price_in_display", "per_day_price_in_display", "daily_diamond_count", "free_days", "remain_price_in_display", "promoted_purchase_remains_days", "price_per_month_in_display", "month_count", "origin_price_in_cents", "member_type", "is_recommend" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.PB_InAppPurchaseInfo), global::Msg.economic.PB_InAppPurchaseInfo.Parser, new[]{ "product_id", "price_in_display", "price_in_cents", "count", "icon_path", "is_online_item", "origin_price_in_display", "promoted_purchase_remains_days", "max_pre_user" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetMerchandiseChangeInfoReq), global::Msg.economic.SS_GetMerchandiseChangeInfoReq.Parser, new[]{ "user_id", "items" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.PB_MerchandiseChangeItem), global::Msg.economic.PB_MerchandiseChangeItem.Parser, new[]{ "biz_code", "source", "merchadise_order_id", "source_type" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetMerchandiseChangeInfoResp), global::Msg.economic.SS_GetMerchandiseChangeInfoResp.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.PB_MerchandiseChangeData), global::Msg.economic.PB_MerchandiseChangeData.Parser, new[]{ "items" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.PB_MerchandiseChangeDetail), global::Msg.economic.PB_MerchandiseChangeDetail.Parser, new[]{ "biz_code", "merchandise_type", "trade_type", "amount", "prev_balance", "curr_balance", "merchadise_order_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetUserMerchandiseInfoReq), global::Msg.economic.SS_GetUserMerchandiseInfoReq.Parser, new[]{ "user_id", "merchandise_type", "merchandise_sub_type", "amount_gt" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetUserMerchandiseInfoResp), global::Msg.economic.SS_GetUserMerchandiseInfoResp.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.PB_UserMerchandiseData), global::Msg.economic.PB_UserMerchandiseData.Parser, new[]{ "items" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_SetUserMerchandiseInfoReq), global::Msg.economic.SS_SetUserMerchandiseInfoReq.Parser, new[]{ "user_id", "merchandise_id", "amount", "is_time_item", "is_add_item" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_SetUserMerchandiseInfoResp), global::Msg.economic.SS_SetUserMerchandiseInfoResp.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.CS_PayMerchandiseReq), global::Msg.economic.CS_PayMerchandiseReq.Parser, new[]{ "product_id", "count" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SC_PayMerchandiseResp), global::Msg.economic.SC_PayMerchandiseResp.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_UseMerchandiseReq), global::Msg.economic.SS_UseMerchandiseReq.Parser, new[]{ "user_id", "merchandise_id", "count" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_UseMerchandiseResp), global::Msg.economic.SS_UseMerchandiseResp.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetMerchandiseDetailReq), global::Msg.economic.SS_GetMerchandiseDetailReq.Parser, new[]{ "user_id", "merchandise_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetMerchandiseDetailResp), global::Msg.economic.SS_GetMerchandiseDetailResp.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.PB_MerchandiseDetail), global::Msg.economic.PB_MerchandiseDetail.Parser, new[]{ "merchandise_id", "merchandise_type", "merchandise_sub_type", "name", "detail" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetUserIntimacyBoxInfoReq), global::Msg.economic.SS_GetUserIntimacyBoxInfoReq.Parser, new[]{ "user_id", "intimacy_level" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetUserIntimacyBoxInfoResp), global::Msg.economic.SS_GetUserIntimacyBoxInfoResp.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.PB_IntimacyBoxInfo), global::Msg.economic.PB_IntimacyBoxInfo.Parser, new[]{ "biz_code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetMerchandiseByTypeReq), global::Msg.economic.SS_GetMerchandiseByTypeReq.Parser, new[]{ "merchandise_type", "merchandise_sub_type" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetMerchandiseByTypeResp), global::Msg.economic.SS_GetMerchandiseByTypeResp.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetUserLimitPropInfoReq), global::Msg.economic.SS_GetUserLimitPropInfoReq.Parser, new[]{ "user_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_GetUserLimitPropInfoResp), global::Msg.economic.SS_GetUserLimitPropInfoResp.Parser, new[]{ "code", "user_merchandise_infos" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_InitUserMerchandiseReq), global::Msg.economic.SS_InitUserMerchandiseReq.Parser, new[]{ "user_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.economic.SS_InitUserMerchandiseResp), global::Msg.economic.SS_InitUserMerchandiseResp.Parser, new[]{ "code" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum PB_PromotionType {
    /// <summary>
    /// 无
    /// </summary>
    [pbr::OriginalName("PNONE")] PNONE = 0,
    /// <summary>
    /// 折扣
    /// </summary>
    [pbr::OriginalName("DISCOUNT")] DISCOUNT = 1,
    /// <summary>
    /// 免费试用
    /// </summary>
    [pbr::OriginalName("FREE_TRIAL")] FREE_TRIAL = 2,
    /// <summary>
    /// 原价
    /// </summary>
    [pbr::OriginalName("ORIGIN_PRICE")] ORIGIN_PRICE = 3,
  }

  public enum InAppPurchasePriceType {
    [pbr::OriginalName("IN_APP_PURCHASE_PRICE_TYPE_")] IN_APP_PURCHASE_PRICE_TYPE_ = 0,
    [pbr::OriginalName("IN_APP_PURCHASE_PRICE_TYPE_Diamond")] IN_APP_PURCHASE_PRICE_TYPE_Diamond = 1,
  }

  public enum PB_MerchandiseChangeSourceType {
    [pbr::OriginalName("PB_MerchandiseChangeSourceType_None")] PB_MerchandiseChangeSourceType_None = 0,
    [pbr::OriginalName("PB_MerchandiseChangeSourceType_IntimacyBox")] PB_MerchandiseChangeSourceType_IntimacyBox = 1,
    [pbr::OriginalName("PB_MerchandiseChangeSourceType_ChatpterBox")] PB_MerchandiseChangeSourceType_ChatpterBox = 2,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetShopInfoReq : pb::IMessage<CS_GetShopInfoReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetShopInfoReq> _parser = new pb::MessageParser<CS_GetShopInfoReq>(() => new CS_GetShopInfoReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetShopInfoReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetShopInfoReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetShopInfoReq(CS_GetShopInfoReq other) : this() {
      country_code_ = other.country_code_;
      region_key_ = other.region_key_;
      economic_source_ = other.economic_source_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetShopInfoReq Clone() {
      return new CS_GetShopInfoReq(this);
    }

    /// <summary>Field number for the "country_code" field.</summary>
    public const int country_codeFieldNumber = 1;
    private string country_code_ = "";
    /// <summary>
    /// 国家码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string country_code {
      get { return country_code_; }
      set {
        country_code_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "region_key" field.</summary>
    public const int region_keyFieldNumber = 2;
    private string region_key_ = "";
    /// <summary>
    /// 大区key
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string region_key {
      get { return region_key_; }
      set {
        region_key_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "economic_source" field.</summary>
    public const int economic_sourceFieldNumber = 3;
    private global::Msg.basic.PB_EconomicSourceEnum economic_source_ = global::Msg.basic.PB_EconomicSourceEnum.ECNONE;
    /// <summary>
    /// 经济渠道来源
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_EconomicSourceEnum economic_source {
      get { return economic_source_; }
      set {
        economic_source_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetShopInfoReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetShopInfoReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (country_code != other.country_code) return false;
      if (region_key != other.region_key) return false;
      if (economic_source != other.economic_source) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (country_code.Length != 0) hash ^= country_code.GetHashCode();
      if (region_key.Length != 0) hash ^= region_key.GetHashCode();
      if (economic_source != global::Msg.basic.PB_EconomicSourceEnum.ECNONE) hash ^= economic_source.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (country_code.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(country_code);
      }
      if (region_key.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(region_key);
      }
      if (economic_source != global::Msg.basic.PB_EconomicSourceEnum.ECNONE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) economic_source);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (country_code.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(country_code);
      }
      if (region_key.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(region_key);
      }
      if (economic_source != global::Msg.basic.PB_EconomicSourceEnum.ECNONE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) economic_source);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (country_code.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(country_code);
      }
      if (region_key.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(region_key);
      }
      if (economic_source != global::Msg.basic.PB_EconomicSourceEnum.ECNONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) economic_source);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetShopInfoReq other) {
      if (other == null) {
        return;
      }
      if (other.country_code.Length != 0) {
        country_code = other.country_code;
      }
      if (other.region_key.Length != 0) {
        region_key = other.region_key;
      }
      if (other.economic_source != global::Msg.basic.PB_EconomicSourceEnum.ECNONE) {
        economic_source = other.economic_source;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            country_code = input.ReadString();
            break;
          }
          case 18: {
            region_key = input.ReadString();
            break;
          }
          case 24: {
            economic_source = (global::Msg.basic.PB_EconomicSourceEnum) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            country_code = input.ReadString();
            break;
          }
          case 18: {
            region_key = input.ReadString();
            break;
          }
          case 24: {
            economic_source = (global::Msg.basic.PB_EconomicSourceEnum) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetShopInfoResp : pb::IMessage<SC_GetShopInfoResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetShopInfoResp> _parser = new pb::MessageParser<SC_GetShopInfoResp>(() => new SC_GetShopInfoResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetShopInfoResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetShopInfoResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetShopInfoResp(SC_GetShopInfoResp other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetShopInfoResp Clone() {
      return new SC_GetShopInfoResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.economic.PB_ShopInfoData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_ShopInfoData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetShopInfoResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetShopInfoResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetShopInfoResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.economic.PB_ShopInfoData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.economic.PB_ShopInfoData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.economic.PB_ShopInfoData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ShopInfoData : pb::IMessage<PB_ShopInfoData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ShopInfoData> _parser = new pb::MessageParser<PB_ShopInfoData>(() => new PB_ShopInfoData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ShopInfoData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ShopInfoData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ShopInfoData(PB_ShopInfoData other) : this() {
      subscription_infos_ = other.subscription_infos_.Clone();
      in_app_purchase_infos_ = other.in_app_purchase_infos_.Clone();
      user_merchandise_infos_ = other.user_merchandise_infos_.Clone();
      member_info_ = other.member_info_ != null ? other.member_info_.Clone() : null;
      merchandise_infos_ = other.merchandise_infos_.Clone();
      promotion_type_ = other.promotion_type_;
      limited_subscription_info_ = other.limited_subscription_info_ != null ? other.limited_subscription_info_.Clone() : null;
      lifetime_subscription_info_ = other.lifetime_subscription_info_ != null ? other.lifetime_subscription_info_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ShopInfoData Clone() {
      return new PB_ShopInfoData(this);
    }

    /// <summary>Field number for the "subscription_infos" field.</summary>
    public const int subscription_infosFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.economic.PB_SubscriptionInfo> _repeated_subscription_infos_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.economic.PB_SubscriptionInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.economic.PB_SubscriptionInfo> subscription_infos_ = new pbc::RepeatedField<global::Msg.economic.PB_SubscriptionInfo>();
    /// <summary>
    /// 订阅商品列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.economic.PB_SubscriptionInfo> subscription_infos {
      get { return subscription_infos_; }
    }

    /// <summary>Field number for the "in_app_purchase_infos" field.</summary>
    public const int in_app_purchase_infosFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Msg.economic.PB_InAppPurchaseInfo> _repeated_in_app_purchase_infos_codec
        = pb::FieldCodec.ForMessage(18, global::Msg.economic.PB_InAppPurchaseInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.economic.PB_InAppPurchaseInfo> in_app_purchase_infos_ = new pbc::RepeatedField<global::Msg.economic.PB_InAppPurchaseInfo>();
    /// <summary>
    /// 内购商品列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.economic.PB_InAppPurchaseInfo> in_app_purchase_infos {
      get { return in_app_purchase_infos_; }
    }

    /// <summary>Field number for the "user_merchandise_infos" field.</summary>
    public const int user_merchandise_infosFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Msg.basic.PB_UserMerchandiseInfo> _repeated_user_merchandise_infos_codec
        = pb::FieldCodec.ForMessage(26, global::Msg.basic.PB_UserMerchandiseInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.basic.PB_UserMerchandiseInfo> user_merchandise_infos_ = new pbc::RepeatedField<global::Msg.basic.PB_UserMerchandiseInfo>();
    /// <summary>
    /// 用户物品列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.basic.PB_UserMerchandiseInfo> user_merchandise_infos {
      get { return user_merchandise_infos_; }
    }

    /// <summary>Field number for the "member_info" field.</summary>
    public const int member_infoFieldNumber = 4;
    private global::Msg.economic.PB_MemberInfoResp member_info_;
    /// <summary>
    /// 会员信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_MemberInfoResp member_info {
      get { return member_info_; }
      set {
        member_info_ = value;
      }
    }

    /// <summary>Field number for the "merchandise_infos" field.</summary>
    public const int merchandise_infosFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Msg.basic.PB_MerchandiseInfo> _repeated_merchandise_infos_codec
        = pb::FieldCodec.ForMessage(42, global::Msg.basic.PB_MerchandiseInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.basic.PB_MerchandiseInfo> merchandise_infos_ = new pbc::RepeatedField<global::Msg.basic.PB_MerchandiseInfo>();
    /// <summary>
    /// 物品信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.basic.PB_MerchandiseInfo> merchandise_infos {
      get { return merchandise_infos_; }
    }

    /// <summary>Field number for the "promotion_type" field.</summary>
    public const int promotion_typeFieldNumber = 6;
    private global::Msg.economic.PB_PromotionType promotion_type_ = global::Msg.economic.PB_PromotionType.PNONE;
    /// <summary>
    /// 促销类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_PromotionType promotion_type {
      get { return promotion_type_; }
      set {
        promotion_type_ = value;
      }
    }

    /// <summary>Field number for the "limited_subscription_info" field.</summary>
    public const int limited_subscription_infoFieldNumber = 7;
    private global::Msg.economic.PB_LimitedSubscriptionInfo limited_subscription_info_;
    /// <summary>
    /// 限时订阅商品信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_LimitedSubscriptionInfo limited_subscription_info {
      get { return limited_subscription_info_; }
      set {
        limited_subscription_info_ = value;
      }
    }

    /// <summary>Field number for the "lifetime_subscription_info" field.</summary>
    public const int lifetime_subscription_infoFieldNumber = 8;
    private global::Msg.economic.PB_LifetimeSubscriptionInfo lifetime_subscription_info_;
    /// <summary>
    /// 终身会员信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_LifetimeSubscriptionInfo lifetime_subscription_info {
      get { return lifetime_subscription_info_; }
      set {
        lifetime_subscription_info_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ShopInfoData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ShopInfoData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!subscription_infos_.Equals(other.subscription_infos_)) return false;
      if(!in_app_purchase_infos_.Equals(other.in_app_purchase_infos_)) return false;
      if(!user_merchandise_infos_.Equals(other.user_merchandise_infos_)) return false;
      if (!object.Equals(member_info, other.member_info)) return false;
      if(!merchandise_infos_.Equals(other.merchandise_infos_)) return false;
      if (promotion_type != other.promotion_type) return false;
      if (!object.Equals(limited_subscription_info, other.limited_subscription_info)) return false;
      if (!object.Equals(lifetime_subscription_info, other.lifetime_subscription_info)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= subscription_infos_.GetHashCode();
      hash ^= in_app_purchase_infos_.GetHashCode();
      hash ^= user_merchandise_infos_.GetHashCode();
      if (member_info_ != null) hash ^= member_info.GetHashCode();
      hash ^= merchandise_infos_.GetHashCode();
      if (promotion_type != global::Msg.economic.PB_PromotionType.PNONE) hash ^= promotion_type.GetHashCode();
      if (limited_subscription_info_ != null) hash ^= limited_subscription_info.GetHashCode();
      if (lifetime_subscription_info_ != null) hash ^= lifetime_subscription_info.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      subscription_infos_.WriteTo(output, _repeated_subscription_infos_codec);
      in_app_purchase_infos_.WriteTo(output, _repeated_in_app_purchase_infos_codec);
      user_merchandise_infos_.WriteTo(output, _repeated_user_merchandise_infos_codec);
      if (member_info_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(member_info);
      }
      merchandise_infos_.WriteTo(output, _repeated_merchandise_infos_codec);
      if (promotion_type != global::Msg.economic.PB_PromotionType.PNONE) {
        output.WriteRawTag(48);
        output.WriteEnum((int) promotion_type);
      }
      if (limited_subscription_info_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(limited_subscription_info);
      }
      if (lifetime_subscription_info_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(lifetime_subscription_info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      subscription_infos_.WriteTo(ref output, _repeated_subscription_infos_codec);
      in_app_purchase_infos_.WriteTo(ref output, _repeated_in_app_purchase_infos_codec);
      user_merchandise_infos_.WriteTo(ref output, _repeated_user_merchandise_infos_codec);
      if (member_info_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(member_info);
      }
      merchandise_infos_.WriteTo(ref output, _repeated_merchandise_infos_codec);
      if (promotion_type != global::Msg.economic.PB_PromotionType.PNONE) {
        output.WriteRawTag(48);
        output.WriteEnum((int) promotion_type);
      }
      if (limited_subscription_info_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(limited_subscription_info);
      }
      if (lifetime_subscription_info_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(lifetime_subscription_info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += subscription_infos_.CalculateSize(_repeated_subscription_infos_codec);
      size += in_app_purchase_infos_.CalculateSize(_repeated_in_app_purchase_infos_codec);
      size += user_merchandise_infos_.CalculateSize(_repeated_user_merchandise_infos_codec);
      if (member_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(member_info);
      }
      size += merchandise_infos_.CalculateSize(_repeated_merchandise_infos_codec);
      if (promotion_type != global::Msg.economic.PB_PromotionType.PNONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) promotion_type);
      }
      if (limited_subscription_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(limited_subscription_info);
      }
      if (lifetime_subscription_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(lifetime_subscription_info);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ShopInfoData other) {
      if (other == null) {
        return;
      }
      subscription_infos_.Add(other.subscription_infos_);
      in_app_purchase_infos_.Add(other.in_app_purchase_infos_);
      user_merchandise_infos_.Add(other.user_merchandise_infos_);
      if (other.member_info_ != null) {
        if (member_info_ == null) {
          member_info = new global::Msg.economic.PB_MemberInfoResp();
        }
        member_info.MergeFrom(other.member_info);
      }
      merchandise_infos_.Add(other.merchandise_infos_);
      if (other.promotion_type != global::Msg.economic.PB_PromotionType.PNONE) {
        promotion_type = other.promotion_type;
      }
      if (other.limited_subscription_info_ != null) {
        if (limited_subscription_info_ == null) {
          limited_subscription_info = new global::Msg.economic.PB_LimitedSubscriptionInfo();
        }
        limited_subscription_info.MergeFrom(other.limited_subscription_info);
      }
      if (other.lifetime_subscription_info_ != null) {
        if (lifetime_subscription_info_ == null) {
          lifetime_subscription_info = new global::Msg.economic.PB_LifetimeSubscriptionInfo();
        }
        lifetime_subscription_info.MergeFrom(other.lifetime_subscription_info);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            subscription_infos_.AddEntriesFrom(input, _repeated_subscription_infos_codec);
            break;
          }
          case 18: {
            in_app_purchase_infos_.AddEntriesFrom(input, _repeated_in_app_purchase_infos_codec);
            break;
          }
          case 26: {
            user_merchandise_infos_.AddEntriesFrom(input, _repeated_user_merchandise_infos_codec);
            break;
          }
          case 34: {
            if (member_info_ == null) {
              member_info = new global::Msg.economic.PB_MemberInfoResp();
            }
            input.ReadMessage(member_info);
            break;
          }
          case 42: {
            merchandise_infos_.AddEntriesFrom(input, _repeated_merchandise_infos_codec);
            break;
          }
          case 48: {
            promotion_type = (global::Msg.economic.PB_PromotionType) input.ReadEnum();
            break;
          }
          case 58: {
            if (limited_subscription_info_ == null) {
              limited_subscription_info = new global::Msg.economic.PB_LimitedSubscriptionInfo();
            }
            input.ReadMessage(limited_subscription_info);
            break;
          }
          case 66: {
            if (lifetime_subscription_info_ == null) {
              lifetime_subscription_info = new global::Msg.economic.PB_LifetimeSubscriptionInfo();
            }
            input.ReadMessage(lifetime_subscription_info);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            subscription_infos_.AddEntriesFrom(ref input, _repeated_subscription_infos_codec);
            break;
          }
          case 18: {
            in_app_purchase_infos_.AddEntriesFrom(ref input, _repeated_in_app_purchase_infos_codec);
            break;
          }
          case 26: {
            user_merchandise_infos_.AddEntriesFrom(ref input, _repeated_user_merchandise_infos_codec);
            break;
          }
          case 34: {
            if (member_info_ == null) {
              member_info = new global::Msg.economic.PB_MemberInfoResp();
            }
            input.ReadMessage(member_info);
            break;
          }
          case 42: {
            merchandise_infos_.AddEntriesFrom(ref input, _repeated_merchandise_infos_codec);
            break;
          }
          case 48: {
            promotion_type = (global::Msg.economic.PB_PromotionType) input.ReadEnum();
            break;
          }
          case 58: {
            if (limited_subscription_info_ == null) {
              limited_subscription_info = new global::Msg.economic.PB_LimitedSubscriptionInfo();
            }
            input.ReadMessage(limited_subscription_info);
            break;
          }
          case 66: {
            if (lifetime_subscription_info_ == null) {
              lifetime_subscription_info = new global::Msg.economic.PB_LifetimeSubscriptionInfo();
            }
            input.ReadMessage(lifetime_subscription_info);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_LifetimeSubscriptionInfo : pb::IMessage<PB_LifetimeSubscriptionInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_LifetimeSubscriptionInfo> _parser = new pb::MessageParser<PB_LifetimeSubscriptionInfo>(() => new PB_LifetimeSubscriptionInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_LifetimeSubscriptionInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LifetimeSubscriptionInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LifetimeSubscriptionInfo(PB_LifetimeSubscriptionInfo other) : this() {
      subscription_info_ = other.subscription_info_ != null ? other.subscription_info_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LifetimeSubscriptionInfo Clone() {
      return new PB_LifetimeSubscriptionInfo(this);
    }

    /// <summary>Field number for the "subscription_info" field.</summary>
    public const int subscription_infoFieldNumber = 1;
    private global::Msg.economic.PB_SubscriptionInfo subscription_info_;
    /// <summary>
    /// 订阅商品信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_SubscriptionInfo subscription_info {
      get { return subscription_info_; }
      set {
        subscription_info_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_LifetimeSubscriptionInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_LifetimeSubscriptionInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(subscription_info, other.subscription_info)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (subscription_info_ != null) hash ^= subscription_info.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (subscription_info_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(subscription_info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (subscription_info_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(subscription_info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (subscription_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(subscription_info);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_LifetimeSubscriptionInfo other) {
      if (other == null) {
        return;
      }
      if (other.subscription_info_ != null) {
        if (subscription_info_ == null) {
          subscription_info = new global::Msg.economic.PB_SubscriptionInfo();
        }
        subscription_info.MergeFrom(other.subscription_info);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (subscription_info_ == null) {
              subscription_info = new global::Msg.economic.PB_SubscriptionInfo();
            }
            input.ReadMessage(subscription_info);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (subscription_info_ == null) {
              subscription_info = new global::Msg.economic.PB_SubscriptionInfo();
            }
            input.ReadMessage(subscription_info);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_LimitedSubscriptionInfo : pb::IMessage<PB_LimitedSubscriptionInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_LimitedSubscriptionInfo> _parser = new pb::MessageParser<PB_LimitedSubscriptionInfo>(() => new PB_LimitedSubscriptionInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_LimitedSubscriptionInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LimitedSubscriptionInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LimitedSubscriptionInfo(PB_LimitedSubscriptionInfo other) : this() {
      subscription_infos_ = other.subscription_infos_.Clone();
      end_milliseconds_in_utc_ = other.end_milliseconds_in_utc_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LimitedSubscriptionInfo Clone() {
      return new PB_LimitedSubscriptionInfo(this);
    }

    /// <summary>Field number for the "subscription_infos" field.</summary>
    public const int subscription_infosFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.economic.PB_SubscriptionInfo> _repeated_subscription_infos_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.economic.PB_SubscriptionInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.economic.PB_SubscriptionInfo> subscription_infos_ = new pbc::RepeatedField<global::Msg.economic.PB_SubscriptionInfo>();
    /// <summary>
    /// 订阅商品列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.economic.PB_SubscriptionInfo> subscription_infos {
      get { return subscription_infos_; }
    }

    /// <summary>Field number for the "end_milliseconds_in_utc" field.</summary>
    public const int end_milliseconds_in_utcFieldNumber = 2;
    private long end_milliseconds_in_utc_;
    /// <summary>
    /// 截止时间戳（UTC时间）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long end_milliseconds_in_utc {
      get { return end_milliseconds_in_utc_; }
      set {
        end_milliseconds_in_utc_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_LimitedSubscriptionInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_LimitedSubscriptionInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!subscription_infos_.Equals(other.subscription_infos_)) return false;
      if (end_milliseconds_in_utc != other.end_milliseconds_in_utc) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= subscription_infos_.GetHashCode();
      if (end_milliseconds_in_utc != 0L) hash ^= end_milliseconds_in_utc.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      subscription_infos_.WriteTo(output, _repeated_subscription_infos_codec);
      if (end_milliseconds_in_utc != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(end_milliseconds_in_utc);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      subscription_infos_.WriteTo(ref output, _repeated_subscription_infos_codec);
      if (end_milliseconds_in_utc != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(end_milliseconds_in_utc);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += subscription_infos_.CalculateSize(_repeated_subscription_infos_codec);
      if (end_milliseconds_in_utc != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(end_milliseconds_in_utc);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_LimitedSubscriptionInfo other) {
      if (other == null) {
        return;
      }
      subscription_infos_.Add(other.subscription_infos_);
      if (other.end_milliseconds_in_utc != 0L) {
        end_milliseconds_in_utc = other.end_milliseconds_in_utc;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            subscription_infos_.AddEntriesFrom(input, _repeated_subscription_infos_codec);
            break;
          }
          case 16: {
            end_milliseconds_in_utc = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            subscription_infos_.AddEntriesFrom(ref input, _repeated_subscription_infos_codec);
            break;
          }
          case 16: {
            end_milliseconds_in_utc = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_SubscriptionInfo : pb::IMessage<PB_SubscriptionInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_SubscriptionInfo> _parser = new pb::MessageParser<PB_SubscriptionInfo>(() => new PB_SubscriptionInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_SubscriptionInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SubscriptionInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SubscriptionInfo(PB_SubscriptionInfo other) : this() {
      member_name_ = other.member_name_;
      product_id_ = other.product_id_;
      price_in_display_ = other.price_in_display_;
      price_in_cents_ = other.price_in_cents_;
      origin_price_in_display_ = other.origin_price_in_display_;
      per_day_price_in_display_ = other.per_day_price_in_display_;
      daily_diamond_count_ = other.daily_diamond_count_;
      free_days_ = other.free_days_;
      remain_price_in_display_ = other.remain_price_in_display_;
      promoted_purchase_remains_days_ = other.promoted_purchase_remains_days_;
      price_per_month_in_display_ = other.price_per_month_in_display_;
      month_count_ = other.month_count_;
      origin_price_in_cents_ = other.origin_price_in_cents_;
      member_type_ = other.member_type_;
      is_recommend_ = other.is_recommend_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SubscriptionInfo Clone() {
      return new PB_SubscriptionInfo(this);
    }

    /// <summary>Field number for the "member_name" field.</summary>
    public const int member_nameFieldNumber = 1;
    private string member_name_ = "";
    /// <summary>
    ///会员标志
    /// </summary>
    [global::System.ObsoleteAttribute]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string member_name {
      get { return member_name_; }
      set {
        member_name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "product_id" field.</summary>
    public const int product_idFieldNumber = 2;
    private string product_id_ = "";
    /// <summary>
    /// 商品ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string product_id {
      get { return product_id_; }
      set {
        product_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "price_in_display" field.</summary>
    public const int price_in_displayFieldNumber = 3;
    private string price_in_display_ = "";
    /// <summary>
    /// 价格（格式化之后）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string price_in_display {
      get { return price_in_display_; }
      set {
        price_in_display_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "price_in_cents" field.</summary>
    public const int price_in_centsFieldNumber = 4;
    private int price_in_cents_;
    /// <summary>
    /// 价格（单位：分）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int price_in_cents {
      get { return price_in_cents_; }
      set {
        price_in_cents_ = value;
      }
    }

    /// <summary>Field number for the "origin_price_in_display" field.</summary>
    public const int origin_price_in_displayFieldNumber = 5;
    private string origin_price_in_display_ = "";
    /// <summary>
    /// 原价（格式化之后）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string origin_price_in_display {
      get { return origin_price_in_display_; }
      set {
        origin_price_in_display_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "per_day_price_in_display" field.</summary>
    public const int per_day_price_in_displayFieldNumber = 6;
    private string per_day_price_in_display_ = "";
    /// <summary>
    /// 每日价格（格式化之后）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string per_day_price_in_display {
      get { return per_day_price_in_display_; }
      set {
        per_day_price_in_display_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "daily_diamond_count" field.</summary>
    public const int daily_diamond_countFieldNumber = 7;
    private int daily_diamond_count_;
    /// <summary>
    /// 每日钻石数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int daily_diamond_count {
      get { return daily_diamond_count_; }
      set {
        daily_diamond_count_ = value;
      }
    }

    /// <summary>Field number for the "free_days" field.</summary>
    public const int free_daysFieldNumber = 8;
    private int free_days_;
    /// <summary>
    /// 免费天数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int free_days {
      get { return free_days_; }
      set {
        free_days_ = value;
      }
    }

    /// <summary>Field number for the "remain_price_in_display" field.</summary>
    public const int remain_price_in_displayFieldNumber = 9;
    private string remain_price_in_display_ = "";
    /// <summary>
    /// 剩余价格（格式化之后）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string remain_price_in_display {
      get { return remain_price_in_display_; }
      set {
        remain_price_in_display_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "promoted_purchase_remains_days" field.</summary>
    public const int promoted_purchase_remains_daysFieldNumber = 10;
    private long promoted_purchase_remains_days_;
    /// <summary>
    /// 促销剩余时间戳（秒）
    /// </summary>
    [global::System.ObsoleteAttribute]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long promoted_purchase_remains_days {
      get { return promoted_purchase_remains_days_; }
      set {
        promoted_purchase_remains_days_ = value;
      }
    }

    /// <summary>Field number for the "price_per_month_in_display" field.</summary>
    public const int price_per_month_in_displayFieldNumber = 11;
    private string price_per_month_in_display_ = "";
    /// <summary>
    /// 月价格（格式化之后）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string price_per_month_in_display {
      get { return price_per_month_in_display_; }
      set {
        price_per_month_in_display_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "month_count" field.</summary>
    public const int month_countFieldNumber = 12;
    private int month_count_;
    /// <summary>
    /// 月数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int month_count {
      get { return month_count_; }
      set {
        month_count_ = value;
      }
    }

    /// <summary>Field number for the "origin_price_in_cents" field.</summary>
    public const int origin_price_in_centsFieldNumber = 13;
    private int origin_price_in_cents_;
    /// <summary>
    /// 原价（单位：分）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int origin_price_in_cents {
      get { return origin_price_in_cents_; }
      set {
        origin_price_in_cents_ = value;
      }
    }

    /// <summary>Field number for the "member_type" field.</summary>
    public const int member_typeFieldNumber = 14;
    private global::Msg.economic.MemberType member_type_ = global::Msg.economic.MemberType.NoneMember;
    /// <summary>
    /// 会员类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.MemberType member_type {
      get { return member_type_; }
      set {
        member_type_ = value;
      }
    }

    /// <summary>Field number for the "is_recommend" field.</summary>
    public const int is_recommendFieldNumber = 15;
    private bool is_recommend_;
    /// <summary>
    /// 是否推荐
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_recommend {
      get { return is_recommend_; }
      set {
        is_recommend_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_SubscriptionInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_SubscriptionInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (member_name != other.member_name) return false;
      if (product_id != other.product_id) return false;
      if (price_in_display != other.price_in_display) return false;
      if (price_in_cents != other.price_in_cents) return false;
      if (origin_price_in_display != other.origin_price_in_display) return false;
      if (per_day_price_in_display != other.per_day_price_in_display) return false;
      if (daily_diamond_count != other.daily_diamond_count) return false;
      if (free_days != other.free_days) return false;
      if (remain_price_in_display != other.remain_price_in_display) return false;
      if (promoted_purchase_remains_days != other.promoted_purchase_remains_days) return false;
      if (price_per_month_in_display != other.price_per_month_in_display) return false;
      if (month_count != other.month_count) return false;
      if (origin_price_in_cents != other.origin_price_in_cents) return false;
      if (member_type != other.member_type) return false;
      if (is_recommend != other.is_recommend) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (member_name.Length != 0) hash ^= member_name.GetHashCode();
      if (product_id.Length != 0) hash ^= product_id.GetHashCode();
      if (price_in_display.Length != 0) hash ^= price_in_display.GetHashCode();
      if (price_in_cents != 0) hash ^= price_in_cents.GetHashCode();
      if (origin_price_in_display.Length != 0) hash ^= origin_price_in_display.GetHashCode();
      if (per_day_price_in_display.Length != 0) hash ^= per_day_price_in_display.GetHashCode();
      if (daily_diamond_count != 0) hash ^= daily_diamond_count.GetHashCode();
      if (free_days != 0) hash ^= free_days.GetHashCode();
      if (remain_price_in_display.Length != 0) hash ^= remain_price_in_display.GetHashCode();
      if (promoted_purchase_remains_days != 0L) hash ^= promoted_purchase_remains_days.GetHashCode();
      if (price_per_month_in_display.Length != 0) hash ^= price_per_month_in_display.GetHashCode();
      if (month_count != 0) hash ^= month_count.GetHashCode();
      if (origin_price_in_cents != 0) hash ^= origin_price_in_cents.GetHashCode();
      if (member_type != global::Msg.economic.MemberType.NoneMember) hash ^= member_type.GetHashCode();
      if (is_recommend != false) hash ^= is_recommend.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (member_name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(member_name);
      }
      if (product_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(product_id);
      }
      if (price_in_display.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(price_in_display);
      }
      if (price_in_cents != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(price_in_cents);
      }
      if (origin_price_in_display.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(origin_price_in_display);
      }
      if (per_day_price_in_display.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(per_day_price_in_display);
      }
      if (daily_diamond_count != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(daily_diamond_count);
      }
      if (free_days != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(free_days);
      }
      if (remain_price_in_display.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(remain_price_in_display);
      }
      if (promoted_purchase_remains_days != 0L) {
        output.WriteRawTag(80);
        output.WriteInt64(promoted_purchase_remains_days);
      }
      if (price_per_month_in_display.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(price_per_month_in_display);
      }
      if (month_count != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(month_count);
      }
      if (origin_price_in_cents != 0) {
        output.WriteRawTag(104);
        output.WriteInt32(origin_price_in_cents);
      }
      if (member_type != global::Msg.economic.MemberType.NoneMember) {
        output.WriteRawTag(112);
        output.WriteEnum((int) member_type);
      }
      if (is_recommend != false) {
        output.WriteRawTag(120);
        output.WriteBool(is_recommend);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (member_name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(member_name);
      }
      if (product_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(product_id);
      }
      if (price_in_display.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(price_in_display);
      }
      if (price_in_cents != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(price_in_cents);
      }
      if (origin_price_in_display.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(origin_price_in_display);
      }
      if (per_day_price_in_display.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(per_day_price_in_display);
      }
      if (daily_diamond_count != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(daily_diamond_count);
      }
      if (free_days != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(free_days);
      }
      if (remain_price_in_display.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(remain_price_in_display);
      }
      if (promoted_purchase_remains_days != 0L) {
        output.WriteRawTag(80);
        output.WriteInt64(promoted_purchase_remains_days);
      }
      if (price_per_month_in_display.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(price_per_month_in_display);
      }
      if (month_count != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(month_count);
      }
      if (origin_price_in_cents != 0) {
        output.WriteRawTag(104);
        output.WriteInt32(origin_price_in_cents);
      }
      if (member_type != global::Msg.economic.MemberType.NoneMember) {
        output.WriteRawTag(112);
        output.WriteEnum((int) member_type);
      }
      if (is_recommend != false) {
        output.WriteRawTag(120);
        output.WriteBool(is_recommend);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (member_name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(member_name);
      }
      if (product_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(product_id);
      }
      if (price_in_display.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(price_in_display);
      }
      if (price_in_cents != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(price_in_cents);
      }
      if (origin_price_in_display.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(origin_price_in_display);
      }
      if (per_day_price_in_display.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(per_day_price_in_display);
      }
      if (daily_diamond_count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(daily_diamond_count);
      }
      if (free_days != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(free_days);
      }
      if (remain_price_in_display.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(remain_price_in_display);
      }
      if (promoted_purchase_remains_days != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(promoted_purchase_remains_days);
      }
      if (price_per_month_in_display.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(price_per_month_in_display);
      }
      if (month_count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(month_count);
      }
      if (origin_price_in_cents != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(origin_price_in_cents);
      }
      if (member_type != global::Msg.economic.MemberType.NoneMember) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) member_type);
      }
      if (is_recommend != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_SubscriptionInfo other) {
      if (other == null) {
        return;
      }
      if (other.member_name.Length != 0) {
        member_name = other.member_name;
      }
      if (other.product_id.Length != 0) {
        product_id = other.product_id;
      }
      if (other.price_in_display.Length != 0) {
        price_in_display = other.price_in_display;
      }
      if (other.price_in_cents != 0) {
        price_in_cents = other.price_in_cents;
      }
      if (other.origin_price_in_display.Length != 0) {
        origin_price_in_display = other.origin_price_in_display;
      }
      if (other.per_day_price_in_display.Length != 0) {
        per_day_price_in_display = other.per_day_price_in_display;
      }
      if (other.daily_diamond_count != 0) {
        daily_diamond_count = other.daily_diamond_count;
      }
      if (other.free_days != 0) {
        free_days = other.free_days;
      }
      if (other.remain_price_in_display.Length != 0) {
        remain_price_in_display = other.remain_price_in_display;
      }
      if (other.promoted_purchase_remains_days != 0L) {
        promoted_purchase_remains_days = other.promoted_purchase_remains_days;
      }
      if (other.price_per_month_in_display.Length != 0) {
        price_per_month_in_display = other.price_per_month_in_display;
      }
      if (other.month_count != 0) {
        month_count = other.month_count;
      }
      if (other.origin_price_in_cents != 0) {
        origin_price_in_cents = other.origin_price_in_cents;
      }
      if (other.member_type != global::Msg.economic.MemberType.NoneMember) {
        member_type = other.member_type;
      }
      if (other.is_recommend != false) {
        is_recommend = other.is_recommend;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            member_name = input.ReadString();
            break;
          }
          case 18: {
            product_id = input.ReadString();
            break;
          }
          case 26: {
            price_in_display = input.ReadString();
            break;
          }
          case 32: {
            price_in_cents = input.ReadInt32();
            break;
          }
          case 42: {
            origin_price_in_display = input.ReadString();
            break;
          }
          case 50: {
            per_day_price_in_display = input.ReadString();
            break;
          }
          case 56: {
            daily_diamond_count = input.ReadInt32();
            break;
          }
          case 64: {
            free_days = input.ReadInt32();
            break;
          }
          case 74: {
            remain_price_in_display = input.ReadString();
            break;
          }
          case 80: {
            promoted_purchase_remains_days = input.ReadInt64();
            break;
          }
          case 90: {
            price_per_month_in_display = input.ReadString();
            break;
          }
          case 96: {
            month_count = input.ReadInt32();
            break;
          }
          case 104: {
            origin_price_in_cents = input.ReadInt32();
            break;
          }
          case 112: {
            member_type = (global::Msg.economic.MemberType) input.ReadEnum();
            break;
          }
          case 120: {
            is_recommend = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            member_name = input.ReadString();
            break;
          }
          case 18: {
            product_id = input.ReadString();
            break;
          }
          case 26: {
            price_in_display = input.ReadString();
            break;
          }
          case 32: {
            price_in_cents = input.ReadInt32();
            break;
          }
          case 42: {
            origin_price_in_display = input.ReadString();
            break;
          }
          case 50: {
            per_day_price_in_display = input.ReadString();
            break;
          }
          case 56: {
            daily_diamond_count = input.ReadInt32();
            break;
          }
          case 64: {
            free_days = input.ReadInt32();
            break;
          }
          case 74: {
            remain_price_in_display = input.ReadString();
            break;
          }
          case 80: {
            promoted_purchase_remains_days = input.ReadInt64();
            break;
          }
          case 90: {
            price_per_month_in_display = input.ReadString();
            break;
          }
          case 96: {
            month_count = input.ReadInt32();
            break;
          }
          case 104: {
            origin_price_in_cents = input.ReadInt32();
            break;
          }
          case 112: {
            member_type = (global::Msg.economic.MemberType) input.ReadEnum();
            break;
          }
          case 120: {
            is_recommend = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_InAppPurchaseInfo : pb::IMessage<PB_InAppPurchaseInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_InAppPurchaseInfo> _parser = new pb::MessageParser<PB_InAppPurchaseInfo>(() => new PB_InAppPurchaseInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_InAppPurchaseInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_InAppPurchaseInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_InAppPurchaseInfo(PB_InAppPurchaseInfo other) : this() {
      product_id_ = other.product_id_;
      price_in_display_ = other.price_in_display_;
      price_in_cents_ = other.price_in_cents_;
      count_ = other.count_;
      icon_path_ = other.icon_path_;
      is_online_item_ = other.is_online_item_;
      origin_price_in_display_ = other.origin_price_in_display_;
      promoted_purchase_remains_days_ = other.promoted_purchase_remains_days_;
      max_pre_user_ = other.max_pre_user_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_InAppPurchaseInfo Clone() {
      return new PB_InAppPurchaseInfo(this);
    }

    /// <summary>Field number for the "product_id" field.</summary>
    public const int product_idFieldNumber = 1;
    private string product_id_ = "";
    /// <summary>
    /// 商品ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string product_id {
      get { return product_id_; }
      set {
        product_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "price_in_display" field.</summary>
    public const int price_in_displayFieldNumber = 2;
    private string price_in_display_ = "";
    /// <summary>
    /// 价格（格式化之后）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string price_in_display {
      get { return price_in_display_; }
      set {
        price_in_display_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "price_in_cents" field.</summary>
    public const int price_in_centsFieldNumber = 3;
    private int price_in_cents_;
    /// <summary>
    /// 价格（单位：分）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int price_in_cents {
      get { return price_in_cents_; }
      set {
        price_in_cents_ = value;
      }
    }

    /// <summary>Field number for the "count" field.</summary>
    public const int countFieldNumber = 4;
    private int count_;
    /// <summary>
    /// 钻石数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int count {
      get { return count_; }
      set {
        count_ = value;
      }
    }

    /// <summary>Field number for the "icon_path" field.</summary>
    public const int icon_pathFieldNumber = 5;
    private string icon_path_ = "";
    /// <summary>
    /// 商品图标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string icon_path {
      get { return icon_path_; }
      set {
        icon_path_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is_online_item" field.</summary>
    public const int is_online_itemFieldNumber = 6;
    private bool is_online_item_;
    /// <summary>
    /// 是否为线上商品
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_online_item {
      get { return is_online_item_; }
      set {
        is_online_item_ = value;
      }
    }

    /// <summary>Field number for the "origin_price_in_display" field.</summary>
    public const int origin_price_in_displayFieldNumber = 7;
    private string origin_price_in_display_ = "";
    /// <summary>
    /// 原价（格式化之后）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string origin_price_in_display {
      get { return origin_price_in_display_; }
      set {
        origin_price_in_display_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "promoted_purchase_remains_days" field.</summary>
    public const int promoted_purchase_remains_daysFieldNumber = 8;
    private long promoted_purchase_remains_days_;
    /// <summary>
    /// 促销剩余时间戳（秒）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long promoted_purchase_remains_days {
      get { return promoted_purchase_remains_days_; }
      set {
        promoted_purchase_remains_days_ = value;
      }
    }

    /// <summary>Field number for the "max_pre_user" field.</summary>
    public const int max_pre_userFieldNumber = 9;
    private int max_pre_user_;
    /// <summary>
    /// 用户最大拥有数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int max_pre_user {
      get { return max_pre_user_; }
      set {
        max_pre_user_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_InAppPurchaseInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_InAppPurchaseInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (product_id != other.product_id) return false;
      if (price_in_display != other.price_in_display) return false;
      if (price_in_cents != other.price_in_cents) return false;
      if (count != other.count) return false;
      if (icon_path != other.icon_path) return false;
      if (is_online_item != other.is_online_item) return false;
      if (origin_price_in_display != other.origin_price_in_display) return false;
      if (promoted_purchase_remains_days != other.promoted_purchase_remains_days) return false;
      if (max_pre_user != other.max_pre_user) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (product_id.Length != 0) hash ^= product_id.GetHashCode();
      if (price_in_display.Length != 0) hash ^= price_in_display.GetHashCode();
      if (price_in_cents != 0) hash ^= price_in_cents.GetHashCode();
      if (count != 0) hash ^= count.GetHashCode();
      if (icon_path.Length != 0) hash ^= icon_path.GetHashCode();
      if (is_online_item != false) hash ^= is_online_item.GetHashCode();
      if (origin_price_in_display.Length != 0) hash ^= origin_price_in_display.GetHashCode();
      if (promoted_purchase_remains_days != 0L) hash ^= promoted_purchase_remains_days.GetHashCode();
      if (max_pre_user != 0) hash ^= max_pre_user.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (product_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(product_id);
      }
      if (price_in_display.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(price_in_display);
      }
      if (price_in_cents != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(price_in_cents);
      }
      if (count != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(count);
      }
      if (icon_path.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(icon_path);
      }
      if (is_online_item != false) {
        output.WriteRawTag(48);
        output.WriteBool(is_online_item);
      }
      if (origin_price_in_display.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(origin_price_in_display);
      }
      if (promoted_purchase_remains_days != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(promoted_purchase_remains_days);
      }
      if (max_pre_user != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(max_pre_user);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (product_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(product_id);
      }
      if (price_in_display.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(price_in_display);
      }
      if (price_in_cents != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(price_in_cents);
      }
      if (count != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(count);
      }
      if (icon_path.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(icon_path);
      }
      if (is_online_item != false) {
        output.WriteRawTag(48);
        output.WriteBool(is_online_item);
      }
      if (origin_price_in_display.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(origin_price_in_display);
      }
      if (promoted_purchase_remains_days != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(promoted_purchase_remains_days);
      }
      if (max_pre_user != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(max_pre_user);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (product_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(product_id);
      }
      if (price_in_display.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(price_in_display);
      }
      if (price_in_cents != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(price_in_cents);
      }
      if (count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(count);
      }
      if (icon_path.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(icon_path);
      }
      if (is_online_item != false) {
        size += 1 + 1;
      }
      if (origin_price_in_display.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(origin_price_in_display);
      }
      if (promoted_purchase_remains_days != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(promoted_purchase_remains_days);
      }
      if (max_pre_user != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(max_pre_user);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_InAppPurchaseInfo other) {
      if (other == null) {
        return;
      }
      if (other.product_id.Length != 0) {
        product_id = other.product_id;
      }
      if (other.price_in_display.Length != 0) {
        price_in_display = other.price_in_display;
      }
      if (other.price_in_cents != 0) {
        price_in_cents = other.price_in_cents;
      }
      if (other.count != 0) {
        count = other.count;
      }
      if (other.icon_path.Length != 0) {
        icon_path = other.icon_path;
      }
      if (other.is_online_item != false) {
        is_online_item = other.is_online_item;
      }
      if (other.origin_price_in_display.Length != 0) {
        origin_price_in_display = other.origin_price_in_display;
      }
      if (other.promoted_purchase_remains_days != 0L) {
        promoted_purchase_remains_days = other.promoted_purchase_remains_days;
      }
      if (other.max_pre_user != 0) {
        max_pre_user = other.max_pre_user;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            product_id = input.ReadString();
            break;
          }
          case 18: {
            price_in_display = input.ReadString();
            break;
          }
          case 24: {
            price_in_cents = input.ReadInt32();
            break;
          }
          case 32: {
            count = input.ReadInt32();
            break;
          }
          case 42: {
            icon_path = input.ReadString();
            break;
          }
          case 48: {
            is_online_item = input.ReadBool();
            break;
          }
          case 58: {
            origin_price_in_display = input.ReadString();
            break;
          }
          case 64: {
            promoted_purchase_remains_days = input.ReadInt64();
            break;
          }
          case 72: {
            max_pre_user = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            product_id = input.ReadString();
            break;
          }
          case 18: {
            price_in_display = input.ReadString();
            break;
          }
          case 24: {
            price_in_cents = input.ReadInt32();
            break;
          }
          case 32: {
            count = input.ReadInt32();
            break;
          }
          case 42: {
            icon_path = input.ReadString();
            break;
          }
          case 48: {
            is_online_item = input.ReadBool();
            break;
          }
          case 58: {
            origin_price_in_display = input.ReadString();
            break;
          }
          case 64: {
            promoted_purchase_remains_days = input.ReadInt64();
            break;
          }
          case 72: {
            max_pre_user = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetMerchandiseChangeInfoReq : pb::IMessage<SS_GetMerchandiseChangeInfoReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetMerchandiseChangeInfoReq> _parser = new pb::MessageParser<SS_GetMerchandiseChangeInfoReq>(() => new SS_GetMerchandiseChangeInfoReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetMerchandiseChangeInfoReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseChangeInfoReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseChangeInfoReq(SS_GetMerchandiseChangeInfoReq other) : this() {
      user_id_ = other.user_id_;
      items_ = other.items_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseChangeInfoReq Clone() {
      return new SS_GetMerchandiseChangeInfoReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "items" field.</summary>
    public const int itemsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Msg.economic.PB_MerchandiseChangeItem> _repeated_items_codec
        = pb::FieldCodec.ForMessage(18, global::Msg.economic.PB_MerchandiseChangeItem.Parser);
    private readonly pbc::RepeatedField<global::Msg.economic.PB_MerchandiseChangeItem> items_ = new pbc::RepeatedField<global::Msg.economic.PB_MerchandiseChangeItem>();
    /// <summary>
    /// 类目列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.economic.PB_MerchandiseChangeItem> items {
      get { return items_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetMerchandiseChangeInfoReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetMerchandiseChangeInfoReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if(!items_.Equals(other.items_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      hash ^= items_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      items_.WriteTo(output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      items_.WriteTo(ref output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      size += items_.CalculateSize(_repeated_items_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetMerchandiseChangeInfoReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      items_.Add(other.items_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            items_.AddEntriesFrom(input, _repeated_items_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            items_.AddEntriesFrom(ref input, _repeated_items_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MerchandiseChangeItem : pb::IMessage<PB_MerchandiseChangeItem>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MerchandiseChangeItem> _parser = new pb::MessageParser<PB_MerchandiseChangeItem>(() => new PB_MerchandiseChangeItem());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MerchandiseChangeItem> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseChangeItem() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseChangeItem(PB_MerchandiseChangeItem other) : this() {
      biz_code_ = other.biz_code_;
      source_ = other.source_;
      merchadise_order_id_ = other.merchadise_order_id_;
      source_type_ = other.source_type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseChangeItem Clone() {
      return new PB_MerchandiseChangeItem(this);
    }

    /// <summary>Field number for the "biz_code" field.</summary>
    public const int biz_codeFieldNumber = 1;
    private string biz_code_ = "";
    /// <summary>
    /// 业务编码；如：大宝箱=LPRewardBigChest，小宝箱=LPRewardSmallChest
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string biz_code {
      get { return biz_code_; }
      set {
        biz_code_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "source" field.</summary>
    public const int sourceFieldNumber = 2;
    private string source_ = "";
    /// <summary>
    /// 调用来源
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string source {
      get { return source_; }
      set {
        source_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "merchadise_order_id" field.</summary>
    public const int merchadise_order_idFieldNumber = 3;
    private string merchadise_order_id_ = "";
    /// <summary>
    /// 用于标识物品的领取订单
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchadise_order_id {
      get { return merchadise_order_id_; }
      set {
        merchadise_order_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "source_type" field.</summary>
    public const int source_typeFieldNumber = 4;
    private global::Msg.economic.PB_MerchandiseChangeSourceType source_type_ = global::Msg.economic.PB_MerchandiseChangeSourceType.PB_MerchandiseChangeSourceType_None;
    /// <summary>
    /// 来源类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_MerchandiseChangeSourceType source_type {
      get { return source_type_; }
      set {
        source_type_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MerchandiseChangeItem);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MerchandiseChangeItem other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (biz_code != other.biz_code) return false;
      if (source != other.source) return false;
      if (merchadise_order_id != other.merchadise_order_id) return false;
      if (source_type != other.source_type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (biz_code.Length != 0) hash ^= biz_code.GetHashCode();
      if (source.Length != 0) hash ^= source.GetHashCode();
      if (merchadise_order_id.Length != 0) hash ^= merchadise_order_id.GetHashCode();
      if (source_type != global::Msg.economic.PB_MerchandiseChangeSourceType.PB_MerchandiseChangeSourceType_None) hash ^= source_type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (biz_code.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(biz_code);
      }
      if (source.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(source);
      }
      if (merchadise_order_id.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(merchadise_order_id);
      }
      if (source_type != global::Msg.economic.PB_MerchandiseChangeSourceType.PB_MerchandiseChangeSourceType_None) {
        output.WriteRawTag(32);
        output.WriteEnum((int) source_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (biz_code.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(biz_code);
      }
      if (source.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(source);
      }
      if (merchadise_order_id.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(merchadise_order_id);
      }
      if (source_type != global::Msg.economic.PB_MerchandiseChangeSourceType.PB_MerchandiseChangeSourceType_None) {
        output.WriteRawTag(32);
        output.WriteEnum((int) source_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (biz_code.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(biz_code);
      }
      if (source.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(source);
      }
      if (merchadise_order_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchadise_order_id);
      }
      if (source_type != global::Msg.economic.PB_MerchandiseChangeSourceType.PB_MerchandiseChangeSourceType_None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) source_type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MerchandiseChangeItem other) {
      if (other == null) {
        return;
      }
      if (other.biz_code.Length != 0) {
        biz_code = other.biz_code;
      }
      if (other.source.Length != 0) {
        source = other.source;
      }
      if (other.merchadise_order_id.Length != 0) {
        merchadise_order_id = other.merchadise_order_id;
      }
      if (other.source_type != global::Msg.economic.PB_MerchandiseChangeSourceType.PB_MerchandiseChangeSourceType_None) {
        source_type = other.source_type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            biz_code = input.ReadString();
            break;
          }
          case 18: {
            source = input.ReadString();
            break;
          }
          case 26: {
            merchadise_order_id = input.ReadString();
            break;
          }
          case 32: {
            source_type = (global::Msg.economic.PB_MerchandiseChangeSourceType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            biz_code = input.ReadString();
            break;
          }
          case 18: {
            source = input.ReadString();
            break;
          }
          case 26: {
            merchadise_order_id = input.ReadString();
            break;
          }
          case 32: {
            source_type = (global::Msg.economic.PB_MerchandiseChangeSourceType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetMerchandiseChangeInfoResp : pb::IMessage<SS_GetMerchandiseChangeInfoResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetMerchandiseChangeInfoResp> _parser = new pb::MessageParser<SS_GetMerchandiseChangeInfoResp>(() => new SS_GetMerchandiseChangeInfoResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetMerchandiseChangeInfoResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseChangeInfoResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseChangeInfoResp(SS_GetMerchandiseChangeInfoResp other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseChangeInfoResp Clone() {
      return new SS_GetMerchandiseChangeInfoResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.economic.PB_MerchandiseChangeData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_MerchandiseChangeData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetMerchandiseChangeInfoResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetMerchandiseChangeInfoResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetMerchandiseChangeInfoResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.economic.PB_MerchandiseChangeData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.economic.PB_MerchandiseChangeData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.economic.PB_MerchandiseChangeData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MerchandiseChangeData : pb::IMessage<PB_MerchandiseChangeData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MerchandiseChangeData> _parser = new pb::MessageParser<PB_MerchandiseChangeData>(() => new PB_MerchandiseChangeData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MerchandiseChangeData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseChangeData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseChangeData(PB_MerchandiseChangeData other) : this() {
      items_ = other.items_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseChangeData Clone() {
      return new PB_MerchandiseChangeData(this);
    }

    /// <summary>Field number for the "items" field.</summary>
    public const int itemsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.economic.PB_MerchandiseChangeDetail> _repeated_items_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.economic.PB_MerchandiseChangeDetail.Parser);
    private readonly pbc::RepeatedField<global::Msg.economic.PB_MerchandiseChangeDetail> items_ = new pbc::RepeatedField<global::Msg.economic.PB_MerchandiseChangeDetail>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.economic.PB_MerchandiseChangeDetail> items {
      get { return items_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MerchandiseChangeData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MerchandiseChangeData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!items_.Equals(other.items_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= items_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      items_.WriteTo(output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      items_.WriteTo(ref output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += items_.CalculateSize(_repeated_items_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MerchandiseChangeData other) {
      if (other == null) {
        return;
      }
      items_.Add(other.items_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            items_.AddEntriesFrom(input, _repeated_items_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            items_.AddEntriesFrom(ref input, _repeated_items_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MerchandiseChangeDetail : pb::IMessage<PB_MerchandiseChangeDetail>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MerchandiseChangeDetail> _parser = new pb::MessageParser<PB_MerchandiseChangeDetail>(() => new PB_MerchandiseChangeDetail());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MerchandiseChangeDetail> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseChangeDetail() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseChangeDetail(PB_MerchandiseChangeDetail other) : this() {
      biz_code_ = other.biz_code_;
      merchandise_type_ = other.merchandise_type_;
      trade_type_ = other.trade_type_;
      amount_ = other.amount_;
      prev_balance_ = other.prev_balance_;
      curr_balance_ = other.curr_balance_;
      merchadise_order_id_ = other.merchadise_order_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseChangeDetail Clone() {
      return new PB_MerchandiseChangeDetail(this);
    }

    /// <summary>Field number for the "biz_code" field.</summary>
    public const int biz_codeFieldNumber = 1;
    private string biz_code_ = "";
    /// <summary>
    /// 业务编码；如：大宝箱=LPRewardBigChest，小宝箱=LPRewardSmallChest
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string biz_code {
      get { return biz_code_; }
      set {
        biz_code_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "merchandise_type" field.</summary>
    public const int merchandise_typeFieldNumber = 2;
    private global::Msg.basic.PB_MerchandiseType merchandise_type_ = global::Msg.basic.PB_MerchandiseType.MerchandiseType_None;
    /// <summary>
    /// 商品类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_MerchandiseType merchandise_type {
      get { return merchandise_type_; }
      set {
        merchandise_type_ = value;
      }
    }

    /// <summary>Field number for the "trade_type" field.</summary>
    public const int trade_typeFieldNumber = 3;
    private global::Msg.economic.PB_TradeType trade_type_ = global::Msg.economic.PB_TradeType.PB_TradeType_None;
    /// <summary>
    /// 变更类型；增，减
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_TradeType trade_type {
      get { return trade_type_; }
      set {
        trade_type_ = value;
      }
    }

    /// <summary>Field number for the "amount" field.</summary>
    public const int amountFieldNumber = 4;
    private long amount_;
    /// <summary>
    /// 变更值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long amount {
      get { return amount_; }
      set {
        amount_ = value;
      }
    }

    /// <summary>Field number for the "prev_balance" field.</summary>
    public const int prev_balanceFieldNumber = 5;
    private long prev_balance_;
    /// <summary>
    /// 变更前的余额
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long prev_balance {
      get { return prev_balance_; }
      set {
        prev_balance_ = value;
      }
    }

    /// <summary>Field number for the "curr_balance" field.</summary>
    public const int curr_balanceFieldNumber = 6;
    private long curr_balance_;
    /// <summary>
    /// 变更后余额
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long curr_balance {
      get { return curr_balance_; }
      set {
        curr_balance_ = value;
      }
    }

    /// <summary>Field number for the "merchadise_order_id" field.</summary>
    public const int merchadise_order_idFieldNumber = 7;
    private string merchadise_order_id_ = "";
    /// <summary>
    /// 用于标识物品的领取订单
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchadise_order_id {
      get { return merchadise_order_id_; }
      set {
        merchadise_order_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MerchandiseChangeDetail);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MerchandiseChangeDetail other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (biz_code != other.biz_code) return false;
      if (merchandise_type != other.merchandise_type) return false;
      if (trade_type != other.trade_type) return false;
      if (amount != other.amount) return false;
      if (prev_balance != other.prev_balance) return false;
      if (curr_balance != other.curr_balance) return false;
      if (merchadise_order_id != other.merchadise_order_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (biz_code.Length != 0) hash ^= biz_code.GetHashCode();
      if (merchandise_type != global::Msg.basic.PB_MerchandiseType.MerchandiseType_None) hash ^= merchandise_type.GetHashCode();
      if (trade_type != global::Msg.economic.PB_TradeType.PB_TradeType_None) hash ^= trade_type.GetHashCode();
      if (amount != 0L) hash ^= amount.GetHashCode();
      if (prev_balance != 0L) hash ^= prev_balance.GetHashCode();
      if (curr_balance != 0L) hash ^= curr_balance.GetHashCode();
      if (merchadise_order_id.Length != 0) hash ^= merchadise_order_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (biz_code.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(biz_code);
      }
      if (merchandise_type != global::Msg.basic.PB_MerchandiseType.MerchandiseType_None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) merchandise_type);
      }
      if (trade_type != global::Msg.economic.PB_TradeType.PB_TradeType_None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) trade_type);
      }
      if (amount != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(amount);
      }
      if (prev_balance != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(prev_balance);
      }
      if (curr_balance != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(curr_balance);
      }
      if (merchadise_order_id.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(merchadise_order_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (biz_code.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(biz_code);
      }
      if (merchandise_type != global::Msg.basic.PB_MerchandiseType.MerchandiseType_None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) merchandise_type);
      }
      if (trade_type != global::Msg.economic.PB_TradeType.PB_TradeType_None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) trade_type);
      }
      if (amount != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(amount);
      }
      if (prev_balance != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(prev_balance);
      }
      if (curr_balance != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(curr_balance);
      }
      if (merchadise_order_id.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(merchadise_order_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (biz_code.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(biz_code);
      }
      if (merchandise_type != global::Msg.basic.PB_MerchandiseType.MerchandiseType_None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) merchandise_type);
      }
      if (trade_type != global::Msg.economic.PB_TradeType.PB_TradeType_None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) trade_type);
      }
      if (amount != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(amount);
      }
      if (prev_balance != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(prev_balance);
      }
      if (curr_balance != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(curr_balance);
      }
      if (merchadise_order_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchadise_order_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MerchandiseChangeDetail other) {
      if (other == null) {
        return;
      }
      if (other.biz_code.Length != 0) {
        biz_code = other.biz_code;
      }
      if (other.merchandise_type != global::Msg.basic.PB_MerchandiseType.MerchandiseType_None) {
        merchandise_type = other.merchandise_type;
      }
      if (other.trade_type != global::Msg.economic.PB_TradeType.PB_TradeType_None) {
        trade_type = other.trade_type;
      }
      if (other.amount != 0L) {
        amount = other.amount;
      }
      if (other.prev_balance != 0L) {
        prev_balance = other.prev_balance;
      }
      if (other.curr_balance != 0L) {
        curr_balance = other.curr_balance;
      }
      if (other.merchadise_order_id.Length != 0) {
        merchadise_order_id = other.merchadise_order_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            biz_code = input.ReadString();
            break;
          }
          case 16: {
            merchandise_type = (global::Msg.basic.PB_MerchandiseType) input.ReadEnum();
            break;
          }
          case 24: {
            trade_type = (global::Msg.economic.PB_TradeType) input.ReadEnum();
            break;
          }
          case 32: {
            amount = input.ReadInt64();
            break;
          }
          case 40: {
            prev_balance = input.ReadInt64();
            break;
          }
          case 48: {
            curr_balance = input.ReadInt64();
            break;
          }
          case 58: {
            merchadise_order_id = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            biz_code = input.ReadString();
            break;
          }
          case 16: {
            merchandise_type = (global::Msg.basic.PB_MerchandiseType) input.ReadEnum();
            break;
          }
          case 24: {
            trade_type = (global::Msg.economic.PB_TradeType) input.ReadEnum();
            break;
          }
          case 32: {
            amount = input.ReadInt64();
            break;
          }
          case 40: {
            prev_balance = input.ReadInt64();
            break;
          }
          case 48: {
            curr_balance = input.ReadInt64();
            break;
          }
          case 58: {
            merchadise_order_id = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserMerchandiseInfoReq : pb::IMessage<SS_GetUserMerchandiseInfoReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserMerchandiseInfoReq> _parser = new pb::MessageParser<SS_GetUserMerchandiseInfoReq>(() => new SS_GetUserMerchandiseInfoReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserMerchandiseInfoReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserMerchandiseInfoReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserMerchandiseInfoReq(SS_GetUserMerchandiseInfoReq other) : this() {
      user_id_ = other.user_id_;
      merchandise_type_ = other.merchandise_type_;
      merchandise_sub_type_ = other.merchandise_sub_type_;
      amount_gt_ = other.amount_gt_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserMerchandiseInfoReq Clone() {
      return new SS_GetUserMerchandiseInfoReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "merchandise_type" field.</summary>
    public const int merchandise_typeFieldNumber = 2;
    private string merchandise_type_ = "";
    /// <summary>
    /// 商品类别
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchandise_type {
      get { return merchandise_type_; }
      set {
        merchandise_type_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "merchandise_sub_type" field.</summary>
    public const int merchandise_sub_typeFieldNumber = 3;
    private string merchandise_sub_type_ = "";
    /// <summary>
    /// 商品小类
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchandise_sub_type {
      get { return merchandise_sub_type_; }
      set {
        merchandise_sub_type_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "amount_gt" field.</summary>
    public const int amount_gtFieldNumber = 4;
    private long amount_gt_;
    /// <summary>
    /// 大于额度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long amount_gt {
      get { return amount_gt_; }
      set {
        amount_gt_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserMerchandiseInfoReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserMerchandiseInfoReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (merchandise_type != other.merchandise_type) return false;
      if (merchandise_sub_type != other.merchandise_sub_type) return false;
      if (amount_gt != other.amount_gt) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (merchandise_type.Length != 0) hash ^= merchandise_type.GetHashCode();
      if (merchandise_sub_type.Length != 0) hash ^= merchandise_sub_type.GetHashCode();
      if (amount_gt != 0L) hash ^= amount_gt.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (merchandise_type.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(merchandise_type);
      }
      if (merchandise_sub_type.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(merchandise_sub_type);
      }
      if (amount_gt != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(amount_gt);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (merchandise_type.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(merchandise_type);
      }
      if (merchandise_sub_type.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(merchandise_sub_type);
      }
      if (amount_gt != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(amount_gt);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (merchandise_type.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchandise_type);
      }
      if (merchandise_sub_type.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchandise_sub_type);
      }
      if (amount_gt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(amount_gt);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserMerchandiseInfoReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.merchandise_type.Length != 0) {
        merchandise_type = other.merchandise_type;
      }
      if (other.merchandise_sub_type.Length != 0) {
        merchandise_sub_type = other.merchandise_sub_type;
      }
      if (other.amount_gt != 0L) {
        amount_gt = other.amount_gt;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            merchandise_type = input.ReadString();
            break;
          }
          case 26: {
            merchandise_sub_type = input.ReadString();
            break;
          }
          case 32: {
            amount_gt = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            merchandise_type = input.ReadString();
            break;
          }
          case 26: {
            merchandise_sub_type = input.ReadString();
            break;
          }
          case 32: {
            amount_gt = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserMerchandiseInfoResp : pb::IMessage<SS_GetUserMerchandiseInfoResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserMerchandiseInfoResp> _parser = new pb::MessageParser<SS_GetUserMerchandiseInfoResp>(() => new SS_GetUserMerchandiseInfoResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserMerchandiseInfoResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserMerchandiseInfoResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserMerchandiseInfoResp(SS_GetUserMerchandiseInfoResp other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserMerchandiseInfoResp Clone() {
      return new SS_GetUserMerchandiseInfoResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.economic.PB_UserMerchandiseData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_UserMerchandiseData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserMerchandiseInfoResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserMerchandiseInfoResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserMerchandiseInfoResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.economic.PB_UserMerchandiseData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.economic.PB_UserMerchandiseData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.economic.PB_UserMerchandiseData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserMerchandiseData : pb::IMessage<PB_UserMerchandiseData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserMerchandiseData> _parser = new pb::MessageParser<PB_UserMerchandiseData>(() => new PB_UserMerchandiseData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserMerchandiseData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserMerchandiseData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserMerchandiseData(PB_UserMerchandiseData other) : this() {
      items_ = other.items_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserMerchandiseData Clone() {
      return new PB_UserMerchandiseData(this);
    }

    /// <summary>Field number for the "items" field.</summary>
    public const int itemsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.basic.PB_UserMerchandiseInfo> _repeated_items_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.basic.PB_UserMerchandiseInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.basic.PB_UserMerchandiseInfo> items_ = new pbc::RepeatedField<global::Msg.basic.PB_UserMerchandiseInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.basic.PB_UserMerchandiseInfo> items {
      get { return items_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserMerchandiseData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserMerchandiseData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!items_.Equals(other.items_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= items_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      items_.WriteTo(output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      items_.WriteTo(ref output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += items_.CalculateSize(_repeated_items_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserMerchandiseData other) {
      if (other == null) {
        return;
      }
      items_.Add(other.items_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            items_.AddEntriesFrom(input, _repeated_items_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            items_.AddEntriesFrom(ref input, _repeated_items_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SetUserMerchandiseInfoReq : pb::IMessage<SS_SetUserMerchandiseInfoReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SetUserMerchandiseInfoReq> _parser = new pb::MessageParser<SS_SetUserMerchandiseInfoReq>(() => new SS_SetUserMerchandiseInfoReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SetUserMerchandiseInfoReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserMerchandiseInfoReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserMerchandiseInfoReq(SS_SetUserMerchandiseInfoReq other) : this() {
      user_id_ = other.user_id_;
      merchandise_id_ = other.merchandise_id_;
      amount_ = other.amount_;
      is_time_item_ = other.is_time_item_;
      is_add_item_ = other.is_add_item_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserMerchandiseInfoReq Clone() {
      return new SS_SetUserMerchandiseInfoReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "merchandise_id" field.</summary>
    public const int merchandise_idFieldNumber = 2;
    private string merchandise_id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchandise_id {
      get { return merchandise_id_; }
      set {
        merchandise_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "amount" field.</summary>
    public const int amountFieldNumber = 3;
    private long amount_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long amount {
      get { return amount_; }
      set {
        amount_ = value;
      }
    }

    /// <summary>Field number for the "is_time_item" field.</summary>
    public const int is_time_itemFieldNumber = 4;
    private bool is_time_item_;
    /// <summary>
    /// 是否是限时道具
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_time_item {
      get { return is_time_item_; }
      set {
        is_time_item_ = value;
      }
    }

    /// <summary>Field number for the "is_add_item" field.</summary>
    public const int is_add_itemFieldNumber = 5;
    private bool is_add_item_;
    /// <summary>
    /// 是否累计
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_add_item {
      get { return is_add_item_; }
      set {
        is_add_item_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SetUserMerchandiseInfoReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SetUserMerchandiseInfoReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (merchandise_id != other.merchandise_id) return false;
      if (amount != other.amount) return false;
      if (is_time_item != other.is_time_item) return false;
      if (is_add_item != other.is_add_item) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (merchandise_id.Length != 0) hash ^= merchandise_id.GetHashCode();
      if (amount != 0L) hash ^= amount.GetHashCode();
      if (is_time_item != false) hash ^= is_time_item.GetHashCode();
      if (is_add_item != false) hash ^= is_add_item.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(merchandise_id);
      }
      if (amount != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(amount);
      }
      if (is_time_item != false) {
        output.WriteRawTag(32);
        output.WriteBool(is_time_item);
      }
      if (is_add_item != false) {
        output.WriteRawTag(40);
        output.WriteBool(is_add_item);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(merchandise_id);
      }
      if (amount != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(amount);
      }
      if (is_time_item != false) {
        output.WriteRawTag(32);
        output.WriteBool(is_time_item);
      }
      if (is_add_item != false) {
        output.WriteRawTag(40);
        output.WriteBool(is_add_item);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (merchandise_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchandise_id);
      }
      if (amount != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(amount);
      }
      if (is_time_item != false) {
        size += 1 + 1;
      }
      if (is_add_item != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SetUserMerchandiseInfoReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.merchandise_id.Length != 0) {
        merchandise_id = other.merchandise_id;
      }
      if (other.amount != 0L) {
        amount = other.amount;
      }
      if (other.is_time_item != false) {
        is_time_item = other.is_time_item;
      }
      if (other.is_add_item != false) {
        is_add_item = other.is_add_item;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            merchandise_id = input.ReadString();
            break;
          }
          case 24: {
            amount = input.ReadInt64();
            break;
          }
          case 32: {
            is_time_item = input.ReadBool();
            break;
          }
          case 40: {
            is_add_item = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            merchandise_id = input.ReadString();
            break;
          }
          case 24: {
            amount = input.ReadInt64();
            break;
          }
          case 32: {
            is_time_item = input.ReadBool();
            break;
          }
          case 40: {
            is_add_item = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SetUserMerchandiseInfoResp : pb::IMessage<SS_SetUserMerchandiseInfoResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SetUserMerchandiseInfoResp> _parser = new pb::MessageParser<SS_SetUserMerchandiseInfoResp>(() => new SS_SetUserMerchandiseInfoResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SetUserMerchandiseInfoResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserMerchandiseInfoResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserMerchandiseInfoResp(SS_SetUserMerchandiseInfoResp other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserMerchandiseInfoResp Clone() {
      return new SS_SetUserMerchandiseInfoResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.basic.PB_UserMerchandiseInfo data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_UserMerchandiseInfo data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SetUserMerchandiseInfoResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SetUserMerchandiseInfoResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SetUserMerchandiseInfoResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.basic.PB_UserMerchandiseInfo();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.basic.PB_UserMerchandiseInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.basic.PB_UserMerchandiseInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_PayMerchandiseReq : pb::IMessage<CS_PayMerchandiseReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_PayMerchandiseReq> _parser = new pb::MessageParser<CS_PayMerchandiseReq>(() => new CS_PayMerchandiseReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_PayMerchandiseReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_PayMerchandiseReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_PayMerchandiseReq(CS_PayMerchandiseReq other) : this() {
      product_id_ = other.product_id_;
      count_ = other.count_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_PayMerchandiseReq Clone() {
      return new CS_PayMerchandiseReq(this);
    }

    /// <summary>Field number for the "product_id" field.</summary>
    public const int product_idFieldNumber = 1;
    private string product_id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string product_id {
      get { return product_id_; }
      set {
        product_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "count" field.</summary>
    public const int countFieldNumber = 2;
    private int count_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int count {
      get { return count_; }
      set {
        count_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_PayMerchandiseReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_PayMerchandiseReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (product_id != other.product_id) return false;
      if (count != other.count) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (product_id.Length != 0) hash ^= product_id.GetHashCode();
      if (count != 0) hash ^= count.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (product_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(product_id);
      }
      if (count != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(count);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (product_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(product_id);
      }
      if (count != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(count);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (product_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(product_id);
      }
      if (count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(count);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_PayMerchandiseReq other) {
      if (other == null) {
        return;
      }
      if (other.product_id.Length != 0) {
        product_id = other.product_id;
      }
      if (other.count != 0) {
        count = other.count;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            product_id = input.ReadString();
            break;
          }
          case 16: {
            count = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            product_id = input.ReadString();
            break;
          }
          case 16: {
            count = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_PayMerchandiseResp : pb::IMessage<SC_PayMerchandiseResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_PayMerchandiseResp> _parser = new pb::MessageParser<SC_PayMerchandiseResp>(() => new SC_PayMerchandiseResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_PayMerchandiseResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_PayMerchandiseResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_PayMerchandiseResp(SC_PayMerchandiseResp other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_PayMerchandiseResp Clone() {
      return new SC_PayMerchandiseResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.basic.PB_UserMerchandiseInfo data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_UserMerchandiseInfo data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_PayMerchandiseResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_PayMerchandiseResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_PayMerchandiseResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.basic.PB_UserMerchandiseInfo();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.basic.PB_UserMerchandiseInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.basic.PB_UserMerchandiseInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_UseMerchandiseReq : pb::IMessage<SS_UseMerchandiseReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_UseMerchandiseReq> _parser = new pb::MessageParser<SS_UseMerchandiseReq>(() => new SS_UseMerchandiseReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_UseMerchandiseReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_UseMerchandiseReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_UseMerchandiseReq(SS_UseMerchandiseReq other) : this() {
      user_id_ = other.user_id_;
      merchandise_id_ = other.merchandise_id_;
      count_ = other.count_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_UseMerchandiseReq Clone() {
      return new SS_UseMerchandiseReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "merchandise_id" field.</summary>
    public const int merchandise_idFieldNumber = 2;
    private string merchandise_id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchandise_id {
      get { return merchandise_id_; }
      set {
        merchandise_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "count" field.</summary>
    public const int countFieldNumber = 3;
    private int count_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int count {
      get { return count_; }
      set {
        count_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_UseMerchandiseReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_UseMerchandiseReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (merchandise_id != other.merchandise_id) return false;
      if (count != other.count) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (merchandise_id.Length != 0) hash ^= merchandise_id.GetHashCode();
      if (count != 0) hash ^= count.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(merchandise_id);
      }
      if (count != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(count);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(merchandise_id);
      }
      if (count != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(count);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (merchandise_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchandise_id);
      }
      if (count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(count);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_UseMerchandiseReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.merchandise_id.Length != 0) {
        merchandise_id = other.merchandise_id;
      }
      if (other.count != 0) {
        count = other.count;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            merchandise_id = input.ReadString();
            break;
          }
          case 24: {
            count = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            merchandise_id = input.ReadString();
            break;
          }
          case 24: {
            count = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_UseMerchandiseResp : pb::IMessage<SS_UseMerchandiseResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_UseMerchandiseResp> _parser = new pb::MessageParser<SS_UseMerchandiseResp>(() => new SS_UseMerchandiseResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_UseMerchandiseResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_UseMerchandiseResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_UseMerchandiseResp(SS_UseMerchandiseResp other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_UseMerchandiseResp Clone() {
      return new SS_UseMerchandiseResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_UseMerchandiseResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_UseMerchandiseResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_UseMerchandiseResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetMerchandiseDetailReq : pb::IMessage<SS_GetMerchandiseDetailReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetMerchandiseDetailReq> _parser = new pb::MessageParser<SS_GetMerchandiseDetailReq>(() => new SS_GetMerchandiseDetailReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetMerchandiseDetailReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseDetailReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseDetailReq(SS_GetMerchandiseDetailReq other) : this() {
      user_id_ = other.user_id_;
      merchandise_id_ = other.merchandise_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseDetailReq Clone() {
      return new SS_GetMerchandiseDetailReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "merchandise_id" field.</summary>
    public const int merchandise_idFieldNumber = 2;
    private string merchandise_id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchandise_id {
      get { return merchandise_id_; }
      set {
        merchandise_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetMerchandiseDetailReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetMerchandiseDetailReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (merchandise_id != other.merchandise_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (merchandise_id.Length != 0) hash ^= merchandise_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(merchandise_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(merchandise_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (merchandise_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchandise_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetMerchandiseDetailReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.merchandise_id.Length != 0) {
        merchandise_id = other.merchandise_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            merchandise_id = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            merchandise_id = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetMerchandiseDetailResp : pb::IMessage<SS_GetMerchandiseDetailResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetMerchandiseDetailResp> _parser = new pb::MessageParser<SS_GetMerchandiseDetailResp>(() => new SS_GetMerchandiseDetailResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetMerchandiseDetailResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseDetailResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseDetailResp(SS_GetMerchandiseDetailResp other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseDetailResp Clone() {
      return new SS_GetMerchandiseDetailResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.economic.PB_MerchandiseDetail data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_MerchandiseDetail data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetMerchandiseDetailResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetMerchandiseDetailResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetMerchandiseDetailResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.economic.PB_MerchandiseDetail();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.economic.PB_MerchandiseDetail();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.economic.PB_MerchandiseDetail();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MerchandiseDetail : pb::IMessage<PB_MerchandiseDetail>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MerchandiseDetail> _parser = new pb::MessageParser<PB_MerchandiseDetail>(() => new PB_MerchandiseDetail());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MerchandiseDetail> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[23]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseDetail() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseDetail(PB_MerchandiseDetail other) : this() {
      merchandise_id_ = other.merchandise_id_;
      merchandise_type_ = other.merchandise_type_;
      merchandise_sub_type_ = other.merchandise_sub_type_;
      name_ = other.name_;
      detail_ = other.detail_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MerchandiseDetail Clone() {
      return new PB_MerchandiseDetail(this);
    }

    /// <summary>Field number for the "merchandise_id" field.</summary>
    public const int merchandise_idFieldNumber = 1;
    private string merchandise_id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchandise_id {
      get { return merchandise_id_; }
      set {
        merchandise_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "merchandise_type" field.</summary>
    public const int merchandise_typeFieldNumber = 2;
    private global::Msg.basic.PB_MerchandiseType merchandise_type_ = global::Msg.basic.PB_MerchandiseType.MerchandiseType_None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_MerchandiseType merchandise_type {
      get { return merchandise_type_; }
      set {
        merchandise_type_ = value;
      }
    }

    /// <summary>Field number for the "merchandise_sub_type" field.</summary>
    public const int merchandise_sub_typeFieldNumber = 3;
    private global::Msg.basic.PB_MerchandiseSubType merchandise_sub_type_ = global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_MerchandiseSubType merchandise_sub_type {
      get { return merchandise_sub_type_; }
      set {
        merchandise_sub_type_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int nameFieldNumber = 4;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "detail" field.</summary>
    public const int detailFieldNumber = 5;
    private string detail_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string detail {
      get { return detail_; }
      set {
        detail_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MerchandiseDetail);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MerchandiseDetail other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (merchandise_id != other.merchandise_id) return false;
      if (merchandise_type != other.merchandise_type) return false;
      if (merchandise_sub_type != other.merchandise_sub_type) return false;
      if (name != other.name) return false;
      if (detail != other.detail) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (merchandise_id.Length != 0) hash ^= merchandise_id.GetHashCode();
      if (merchandise_type != global::Msg.basic.PB_MerchandiseType.MerchandiseType_None) hash ^= merchandise_type.GetHashCode();
      if (merchandise_sub_type != global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None) hash ^= merchandise_sub_type.GetHashCode();
      if (name.Length != 0) hash ^= name.GetHashCode();
      if (detail.Length != 0) hash ^= detail.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(merchandise_id);
      }
      if (merchandise_type != global::Msg.basic.PB_MerchandiseType.MerchandiseType_None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) merchandise_type);
      }
      if (merchandise_sub_type != global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) merchandise_sub_type);
      }
      if (name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(name);
      }
      if (detail.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(detail);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(merchandise_id);
      }
      if (merchandise_type != global::Msg.basic.PB_MerchandiseType.MerchandiseType_None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) merchandise_type);
      }
      if (merchandise_sub_type != global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) merchandise_sub_type);
      }
      if (name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(name);
      }
      if (detail.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(detail);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (merchandise_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchandise_id);
      }
      if (merchandise_type != global::Msg.basic.PB_MerchandiseType.MerchandiseType_None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) merchandise_type);
      }
      if (merchandise_sub_type != global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) merchandise_sub_type);
      }
      if (name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(name);
      }
      if (detail.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(detail);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MerchandiseDetail other) {
      if (other == null) {
        return;
      }
      if (other.merchandise_id.Length != 0) {
        merchandise_id = other.merchandise_id;
      }
      if (other.merchandise_type != global::Msg.basic.PB_MerchandiseType.MerchandiseType_None) {
        merchandise_type = other.merchandise_type;
      }
      if (other.merchandise_sub_type != global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None) {
        merchandise_sub_type = other.merchandise_sub_type;
      }
      if (other.name.Length != 0) {
        name = other.name;
      }
      if (other.detail.Length != 0) {
        detail = other.detail;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            merchandise_id = input.ReadString();
            break;
          }
          case 16: {
            merchandise_type = (global::Msg.basic.PB_MerchandiseType) input.ReadEnum();
            break;
          }
          case 24: {
            merchandise_sub_type = (global::Msg.basic.PB_MerchandiseSubType) input.ReadEnum();
            break;
          }
          case 34: {
            name = input.ReadString();
            break;
          }
          case 42: {
            detail = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            merchandise_id = input.ReadString();
            break;
          }
          case 16: {
            merchandise_type = (global::Msg.basic.PB_MerchandiseType) input.ReadEnum();
            break;
          }
          case 24: {
            merchandise_sub_type = (global::Msg.basic.PB_MerchandiseSubType) input.ReadEnum();
            break;
          }
          case 34: {
            name = input.ReadString();
            break;
          }
          case 42: {
            detail = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserIntimacyBoxInfoReq : pb::IMessage<SS_GetUserIntimacyBoxInfoReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserIntimacyBoxInfoReq> _parser = new pb::MessageParser<SS_GetUserIntimacyBoxInfoReq>(() => new SS_GetUserIntimacyBoxInfoReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserIntimacyBoxInfoReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[24]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserIntimacyBoxInfoReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserIntimacyBoxInfoReq(SS_GetUserIntimacyBoxInfoReq other) : this() {
      user_id_ = other.user_id_;
      intimacy_level_ = other.intimacy_level_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserIntimacyBoxInfoReq Clone() {
      return new SS_GetUserIntimacyBoxInfoReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "intimacy_level" field.</summary>
    public const int intimacy_levelFieldNumber = 2;
    private int intimacy_level_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int intimacy_level {
      get { return intimacy_level_; }
      set {
        intimacy_level_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserIntimacyBoxInfoReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserIntimacyBoxInfoReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (intimacy_level != other.intimacy_level) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (intimacy_level != 0) hash ^= intimacy_level.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (intimacy_level != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(intimacy_level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (intimacy_level != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(intimacy_level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (intimacy_level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(intimacy_level);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserIntimacyBoxInfoReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.intimacy_level != 0) {
        intimacy_level = other.intimacy_level;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            intimacy_level = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            intimacy_level = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserIntimacyBoxInfoResp : pb::IMessage<SS_GetUserIntimacyBoxInfoResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserIntimacyBoxInfoResp> _parser = new pb::MessageParser<SS_GetUserIntimacyBoxInfoResp>(() => new SS_GetUserIntimacyBoxInfoResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserIntimacyBoxInfoResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[25]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserIntimacyBoxInfoResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserIntimacyBoxInfoResp(SS_GetUserIntimacyBoxInfoResp other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserIntimacyBoxInfoResp Clone() {
      return new SS_GetUserIntimacyBoxInfoResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.economic.PB_IntimacyBoxInfo data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_IntimacyBoxInfo data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserIntimacyBoxInfoResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserIntimacyBoxInfoResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserIntimacyBoxInfoResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.economic.PB_IntimacyBoxInfo();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.economic.PB_IntimacyBoxInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.economic.PB_IntimacyBoxInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_IntimacyBoxInfo : pb::IMessage<PB_IntimacyBoxInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_IntimacyBoxInfo> _parser = new pb::MessageParser<PB_IntimacyBoxInfo>(() => new PB_IntimacyBoxInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_IntimacyBoxInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[26]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IntimacyBoxInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IntimacyBoxInfo(PB_IntimacyBoxInfo other) : this() {
      biz_code_ = other.biz_code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IntimacyBoxInfo Clone() {
      return new PB_IntimacyBoxInfo(this);
    }

    /// <summary>Field number for the "biz_code" field.</summary>
    public const int biz_codeFieldNumber = 1;
    private string biz_code_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string biz_code {
      get { return biz_code_; }
      set {
        biz_code_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_IntimacyBoxInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_IntimacyBoxInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (biz_code != other.biz_code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (biz_code.Length != 0) hash ^= biz_code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (biz_code.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(biz_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (biz_code.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(biz_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (biz_code.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(biz_code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_IntimacyBoxInfo other) {
      if (other == null) {
        return;
      }
      if (other.biz_code.Length != 0) {
        biz_code = other.biz_code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            biz_code = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            biz_code = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetMerchandiseByTypeReq : pb::IMessage<SS_GetMerchandiseByTypeReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetMerchandiseByTypeReq> _parser = new pb::MessageParser<SS_GetMerchandiseByTypeReq>(() => new SS_GetMerchandiseByTypeReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetMerchandiseByTypeReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[27]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseByTypeReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseByTypeReq(SS_GetMerchandiseByTypeReq other) : this() {
      merchandise_type_ = other.merchandise_type_;
      merchandise_sub_type_ = other.merchandise_sub_type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseByTypeReq Clone() {
      return new SS_GetMerchandiseByTypeReq(this);
    }

    /// <summary>Field number for the "merchandise_type" field.</summary>
    public const int merchandise_typeFieldNumber = 1;
    private string merchandise_type_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchandise_type {
      get { return merchandise_type_; }
      set {
        merchandise_type_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "merchandise_sub_type" field.</summary>
    public const int merchandise_sub_typeFieldNumber = 2;
    private string merchandise_sub_type_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchandise_sub_type {
      get { return merchandise_sub_type_; }
      set {
        merchandise_sub_type_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetMerchandiseByTypeReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetMerchandiseByTypeReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (merchandise_type != other.merchandise_type) return false;
      if (merchandise_sub_type != other.merchandise_sub_type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (merchandise_type.Length != 0) hash ^= merchandise_type.GetHashCode();
      if (merchandise_sub_type.Length != 0) hash ^= merchandise_sub_type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (merchandise_type.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(merchandise_type);
      }
      if (merchandise_sub_type.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(merchandise_sub_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (merchandise_type.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(merchandise_type);
      }
      if (merchandise_sub_type.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(merchandise_sub_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (merchandise_type.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchandise_type);
      }
      if (merchandise_sub_type.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchandise_sub_type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetMerchandiseByTypeReq other) {
      if (other == null) {
        return;
      }
      if (other.merchandise_type.Length != 0) {
        merchandise_type = other.merchandise_type;
      }
      if (other.merchandise_sub_type.Length != 0) {
        merchandise_sub_type = other.merchandise_sub_type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            merchandise_type = input.ReadString();
            break;
          }
          case 18: {
            merchandise_sub_type = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            merchandise_type = input.ReadString();
            break;
          }
          case 18: {
            merchandise_sub_type = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetMerchandiseByTypeResp : pb::IMessage<SS_GetMerchandiseByTypeResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetMerchandiseByTypeResp> _parser = new pb::MessageParser<SS_GetMerchandiseByTypeResp>(() => new SS_GetMerchandiseByTypeResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetMerchandiseByTypeResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[28]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseByTypeResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseByTypeResp(SS_GetMerchandiseByTypeResp other) : this() {
      code_ = other.code_;
      data_ = other.data_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetMerchandiseByTypeResp Clone() {
      return new SS_GetMerchandiseByTypeResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Msg.economic.PB_MerchandiseDetail> _repeated_data_codec
        = pb::FieldCodec.ForMessage(18, global::Msg.economic.PB_MerchandiseDetail.Parser);
    private readonly pbc::RepeatedField<global::Msg.economic.PB_MerchandiseDetail> data_ = new pbc::RepeatedField<global::Msg.economic.PB_MerchandiseDetail>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.economic.PB_MerchandiseDetail> data {
      get { return data_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetMerchandiseByTypeResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetMerchandiseByTypeResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if(!data_.Equals(other.data_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      hash ^= data_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      data_.WriteTo(output, _repeated_data_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      data_.WriteTo(ref output, _repeated_data_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      size += data_.CalculateSize(_repeated_data_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetMerchandiseByTypeResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      data_.Add(other.data_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            data_.AddEntriesFrom(input, _repeated_data_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            data_.AddEntriesFrom(ref input, _repeated_data_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserLimitPropInfoReq : pb::IMessage<SS_GetUserLimitPropInfoReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserLimitPropInfoReq> _parser = new pb::MessageParser<SS_GetUserLimitPropInfoReq>(() => new SS_GetUserLimitPropInfoReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserLimitPropInfoReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[29]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserLimitPropInfoReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserLimitPropInfoReq(SS_GetUserLimitPropInfoReq other) : this() {
      user_id_ = other.user_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserLimitPropInfoReq Clone() {
      return new SS_GetUserLimitPropInfoReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserLimitPropInfoReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserLimitPropInfoReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserLimitPropInfoReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserLimitPropInfoResp : pb::IMessage<SS_GetUserLimitPropInfoResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserLimitPropInfoResp> _parser = new pb::MessageParser<SS_GetUserLimitPropInfoResp>(() => new SS_GetUserLimitPropInfoResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserLimitPropInfoResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[30]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserLimitPropInfoResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserLimitPropInfoResp(SS_GetUserLimitPropInfoResp other) : this() {
      code_ = other.code_;
      user_merchandise_infos_ = other.user_merchandise_infos_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserLimitPropInfoResp Clone() {
      return new SS_GetUserLimitPropInfoResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "user_merchandise_infos" field.</summary>
    public const int user_merchandise_infosFieldNumber = 2;
    private static readonly pbc::MapField<string, global::Msg.basic.PB_UserMerchandiseInfo>.Codec _map_user_merchandise_infos_codec
        = new pbc::MapField<string, global::Msg.basic.PB_UserMerchandiseInfo>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForMessage(18, global::Msg.basic.PB_UserMerchandiseInfo.Parser), 18);
    private readonly pbc::MapField<string, global::Msg.basic.PB_UserMerchandiseInfo> user_merchandise_infos_ = new pbc::MapField<string, global::Msg.basic.PB_UserMerchandiseInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::Msg.basic.PB_UserMerchandiseInfo> user_merchandise_infos {
      get { return user_merchandise_infos_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserLimitPropInfoResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserLimitPropInfoResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!user_merchandise_infos.Equals(other.user_merchandise_infos)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      hash ^= user_merchandise_infos.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      user_merchandise_infos_.WriteTo(output, _map_user_merchandise_infos_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      user_merchandise_infos_.WriteTo(ref output, _map_user_merchandise_infos_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      size += user_merchandise_infos_.CalculateSize(_map_user_merchandise_infos_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserLimitPropInfoResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      user_merchandise_infos_.MergeFrom(other.user_merchandise_infos_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            user_merchandise_infos_.AddEntriesFrom(input, _map_user_merchandise_infos_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            user_merchandise_infos_.AddEntriesFrom(ref input, _map_user_merchandise_infos_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_InitUserMerchandiseReq : pb::IMessage<SS_InitUserMerchandiseReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_InitUserMerchandiseReq> _parser = new pb::MessageParser<SS_InitUserMerchandiseReq>(() => new SS_InitUserMerchandiseReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_InitUserMerchandiseReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[31]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_InitUserMerchandiseReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_InitUserMerchandiseReq(SS_InitUserMerchandiseReq other) : this() {
      user_id_ = other.user_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_InitUserMerchandiseReq Clone() {
      return new SS_InitUserMerchandiseReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_InitUserMerchandiseReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_InitUserMerchandiseReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_InitUserMerchandiseReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_InitUserMerchandiseResp : pb::IMessage<SS_InitUserMerchandiseResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_InitUserMerchandiseResp> _parser = new pb::MessageParser<SS_InitUserMerchandiseResp>(() => new SS_InitUserMerchandiseResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_InitUserMerchandiseResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.economic.MerchandiseReflection.Descriptor.MessageTypes[32]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_InitUserMerchandiseResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_InitUserMerchandiseResp(SS_InitUserMerchandiseResp other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_InitUserMerchandiseResp Clone() {
      return new SS_InitUserMerchandiseResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_BizCode code_ = global::Msg.basic.PB_BizCode.PB_BizCode_Success;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_InitUserMerchandiseResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_InitUserMerchandiseResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_InitUserMerchandiseResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_BizCode.PB_BizCode_Success) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_BizCode) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/course/learn_path.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.course {

  /// <summary>Holder for reflection information generated from protobuf/course/learn_path.proto</summary>
  public static partial class LearnPathReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/course/learn_path.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static LearnPathReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiBwcm90b2J1Zi9jb3Vyc2UvbGVhcm5fcGF0aC5wcm90bxobcHJvdG9idWYv",
            "YmFzaWMvY291cnNlLnByb3RvGh1wcm90b2J1Zi9iYXNpYy9lY29ub21pYy5w",
            "cm90bxoZcHJvdG9idWYvYmFzaWMvY29kZS5wcm90bxobcHJvdG9idWYvYmFz",
            "aWMvZGlhbG9nLnByb3RvIrwBCg5QQl9TZXNzaW9uRGF0YRISCgpzZXNzaW9u",
            "X2lkGAEgASgDEhUKDXNlc3Npb25faW5kZXgYAiABKAUSKQoMc2Vzc2lvbl90",
            "eXBlGAMgASgOMhMuUEJfU2Vzc2lvblR5cGVFbnVtEgsKA2V4cBgEIAEoBRIP",
            "CgdzdGFtaW5hGAUgASgFEiMKC2RpYWxvZ19tb2RlGAYgASgOMg4uUEJfRGlh",
            "bG9nTW9kZRIRCglhdmF0YXJfaWQYByABKAMiOwoPUEJfTGV2ZWxFeHBEYXRh",
            "EhQKDHByYWN0aWNlX2V4cBgBIAEoBRISCgpsZWdlbmRfZXhwGAIgASgFIuUC",
            "CgxQQl9MZXZlbERhdGESEAoIbGV2ZWxfaWQYASABKAMSEwoLbGV2ZWxfaW5k",
            "ZXgYAiABKAUSEwoLbGV2ZWxfdGl0bGUYAyABKAkSFgoObGV2ZWxfc3VidGl0",
            "bGUYBCABKAkSJQoKbGV2ZWxfdHlwZRgFIAEoDjIRLlBCX0xldmVsVHlwZUVu",
            "dW0SJgoGc3RhdHVzGAYgASgOMhYuUEJfUHJvZ3Jlc3NTdGF0dXNFbnVtEioK",
            "D2JveF9yZXdhcmRfaW5mbxgHIAEoCzIRLlBCX0JveFJld2FyZEluZm8SKAoO",
            "bGV2ZWxfZXhwX2RhdGEYCCABKAsyEC5QQl9MZXZlbEV4cERhdGESFQoNc2Vz",
            "c2lvbl90b3RhbBgJIAEoBRIZChFzZXNzaW9uX2NvbXBsZXRlZBgKIAEoBRIq",
            "ChFzZXNzaW9uX2RhdGFfbGlzdBgLIAMoCzIPLlBCX1Nlc3Npb25EYXRhIq8B",
            "CgtQQl9Vbml0RGF0YRIPCgd1bml0X2lkGAEgASgDEhIKCnVuaXRfaW5kZXgY",
            "AiABKAUSEgoKdW5pdF90aXRsZRgDIAEoCRImCg9sZXZlbF9kYXRhX2xpc3QY",
            "BCADKAsyDS5QQl9MZXZlbERhdGESFwoPYXZhdGFyX2hlYWRfdXJsGAUgASgJ",
            "EiYKBnN0YXR1cxgGIAEoDjIWLlBCX1Byb2dyZXNzU3RhdHVzRW51bSL5AQoO",
            "UEJfU2VjdGlvbkRhdGESFQoNc2VjdGlvbl9pbmRleBgBIAEoBRIVCg1zZWN0",
            "aW9uX3RpdGxlGAIgASgJEhgKEHNlY3Rpb25fc3VidGl0bGUYAyABKAkSJAoO",
            "dW5pdF9kYXRhX2xpc3QYBCADKAsyDC5QQl9Vbml0RGF0YRISCgp1bml0X3Rv",
            "dGFsGAUgASgFEhYKDnVuaXRfY29tcGxldGVkGAYgASgFEiYKBnN0YXR1cxgH",
            "IAEoDjIWLlBCX1Byb2dyZXNzU3RhdHVzRW51bRIRCgljb3Zlcl91cmwYCCAB",
            "KAkSEgoKc2VjdGlvbl9pZBgJIAEoAyKHAQoNUEJfQ291cnNlRGF0YRIRCglj",
            "b3Vyc2VfaWQYASABKAMSKgoRc2VjdGlvbl9kYXRhX2xpc3QYAiADKAsyDy5Q",
            "Ql9TZWN0aW9uRGF0YRIPCgdvcF90eXBlGAMgASgFEiYKDXNraXBfZXhwX2Rh",
            "dGEYBCABKAsyDy5QQl9Ta2lwRXhwRGF0YSJBCg5QQl9Ta2lwRXhwRGF0YRIV",
            "Cg11bml0X3NraXBfZXhwGAEgASgFEhgKEHNlY3Rpb25fc2tpcF9leHAYAiAB",
            "KAUiOgoTQ1NfR2V0VXNlckNvdXJzZVJlcRIPCgdvcF90eXBlGAEgASgFEhIK",
            "CnNlY3Rpb25faWQYAiABKAMiSgoSU0NfR2V0VXNlclNoZWxmQWNrEhYKBGNv",
            "ZGUYASABKA4yCC5QQl9Db2RlEhwKBGRhdGEYAiABKAsyDi5QQl9Db3Vyc2VE",
            "YXRhIiUKEkNTX0dldFVzZXJTaGVsZlJlcRIPCgdvcF90eXBlGAEgASgFIksK",
            "E1NDX0dldFVzZXJDb3Vyc2VBY2sSFgoEY29kZRgBIAEoDjIILlBCX0NvZGUS",
            "HAoEZGF0YRgCIAEoCzIOLlBCX0NvdXJzZURhdGEiigEKEENTX1NraXBDb3Vy",
            "c2VSZXESEQoJY291cnNlX2lkGAEgASgDEiMKCXNraXBfdHlwZRgCIAEoDjIQ",
            "LlBCX1NraXBUeXBlRW51bRIVCg1zZWN0aW9uX2luZGV4GAMgASgFEhIKCnVu",
            "aXRfaW5kZXgYBCABKAUSEwoLbGV2ZWxfaW5kZXgYBSABKAUiKgoQU0NfU2tp",
            "cENvdXJzZUFjaxIWCgRjb2RlGAEgASgOMgguUEJfQ29kZSJkCg9DU19SZXdh",
            "cmRCb3hSZXESEQoJY291cnNlX2lkGAEgASgDEhUKDXNlY3Rpb25faW5kZXgY",
            "AiABKAUSEgoKdW5pdF9pbmRleBgDIAEoBRITCgtsZXZlbF9pbmRleBgEIAEo",
            "BSJJCg9TQ19SZXdhcmRCb3hBY2sSFgoEY29kZRgBIAEoDjIILlBCX0NvZGUS",
            "HgoEZGF0YRgCIAEoCzIQLlBCX1Jld2FyZEJveFJzcCI6Cg9QQl9SZXdhcmRC",
            "b3hSc3ASJwoNZWNvbm9taWNfaW5mbxgBIAEoCzIQLlBCX0Vjb25vbWljSW5m",
            "byJpChpTU19DaGFuZ2VDb3Vyc2VQcm9ncmVzc1JlcRIwCg1jb3Vyc2VfcGFy",
            "YW1zGAEgASgLMhkuUEJfQ291cnNlTGVhcm5QYXRoUGFyYW1zEhkKEXNlc3Np",
            "b25fcmVjb3JkX2lkGAIgASgDIjQKGlNTX0NoYW5nZUNvdXJzZVByb2dyZXNz",
            "QWNrEhYKBGNvZGUYASABKA4yCC5QQl9Db2RlIlAKH1NTX0NyZWF0ZUNvdXJz",
            "ZVNlc3Npb25SZWNvcmRSZXESEgoKc2Vzc2lvbl9pZBgBIAEoAxIZChFzZXNz",
            "aW9uX3JlY29yZF9pZBgCIAEoAyI5Ch9TU19DcmVhdGVDb3Vyc2VTZXNzaW9u",
            "UmVjb3JkQWNrEhYKBGNvZGUYASABKA4yCC5QQl9Db2RlIjQKF1NTX0V4aXRD",
            "b3Vyc2VTZXNzaW9uUmVxEhkKEXNlc3Npb25fcmVjb3JkX2lkGAEgASgDIjEK",
            "F1NTX0V4aXRDb3Vyc2VTZXNzaW9uQWNrEhYKBGNvZGUYASABKA4yCC5QQl9D",
            "b2RlIisKFVNTX1NldFVzZXJQcm9ncmVzc1JlcRISCgp1c2VyX2xldmVsGAEg",
            "ASgJIlYKFVNTX1NldFVzZXJQcm9ncmVzc0FjaxIWCgRjb2RlGAEgASgOMggu",
            "UEJfQ29kZRIlCgRkYXRhGAIgASgLMhcuU1NfU2V0VXNlclByb2dyZXNzRGF0",
            "YSJYChZTU19TZXRVc2VyUHJvZ3Jlc3NEYXRhEhUKDXNlY3Rpb25faW5kZXgY",
            "ASABKAUSEgoKdW5pdF9pbmRleBgCIAEoBRITCgtsZXZlbF9pbmRleBgDIAEo",
            "BUIoWhl2Zl9wcm90b2J1Zi9zZXJ2ZXIvY291cnNlqgIKTXNnLmNvdXJzZWIG",
            "cHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.basic.CourseReflection.Descriptor, global::Msg.basic.EconomicReflection.Descriptor, global::Msg.basic.CodeReflection.Descriptor, global::Msg.basic.DialogReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.PB_SessionData), global::Msg.course.PB_SessionData.Parser, new[]{ "session_id", "session_index", "session_type", "exp", "stamina", "dialog_mode", "avatar_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.PB_LevelExpData), global::Msg.course.PB_LevelExpData.Parser, new[]{ "practice_exp", "legend_exp" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.PB_LevelData), global::Msg.course.PB_LevelData.Parser, new[]{ "level_id", "level_index", "level_title", "level_subtitle", "level_type", "status", "box_reward_info", "level_exp_data", "session_total", "session_completed", "session_data_list" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.PB_UnitData), global::Msg.course.PB_UnitData.Parser, new[]{ "unit_id", "unit_index", "unit_title", "level_data_list", "avatar_head_url", "status" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.PB_SectionData), global::Msg.course.PB_SectionData.Parser, new[]{ "section_index", "section_title", "section_subtitle", "unit_data_list", "unit_total", "unit_completed", "status", "cover_url", "section_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.PB_CourseData), global::Msg.course.PB_CourseData.Parser, new[]{ "course_id", "section_data_list", "op_type", "skip_exp_data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.PB_SkipExpData), global::Msg.course.PB_SkipExpData.Parser, new[]{ "unit_skip_exp", "section_skip_exp" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.CS_GetUserCourseReq), global::Msg.course.CS_GetUserCourseReq.Parser, new[]{ "op_type", "section_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SC_GetUserShelfAck), global::Msg.course.SC_GetUserShelfAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.CS_GetUserShelfReq), global::Msg.course.CS_GetUserShelfReq.Parser, new[]{ "op_type" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SC_GetUserCourseAck), global::Msg.course.SC_GetUserCourseAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.CS_SkipCourseReq), global::Msg.course.CS_SkipCourseReq.Parser, new[]{ "course_id", "skip_type", "section_index", "unit_index", "level_index" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SC_SkipCourseAck), global::Msg.course.SC_SkipCourseAck.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.CS_RewardBoxReq), global::Msg.course.CS_RewardBoxReq.Parser, new[]{ "course_id", "section_index", "unit_index", "level_index" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SC_RewardBoxAck), global::Msg.course.SC_RewardBoxAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.PB_RewardBoxRsp), global::Msg.course.PB_RewardBoxRsp.Parser, new[]{ "economic_info" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SS_ChangeCourseProgressReq), global::Msg.course.SS_ChangeCourseProgressReq.Parser, new[]{ "course_params", "session_record_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SS_ChangeCourseProgressAck), global::Msg.course.SS_ChangeCourseProgressAck.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SS_CreateCourseSessionRecordReq), global::Msg.course.SS_CreateCourseSessionRecordReq.Parser, new[]{ "session_id", "session_record_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SS_CreateCourseSessionRecordAck), global::Msg.course.SS_CreateCourseSessionRecordAck.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SS_ExitCourseSessionReq), global::Msg.course.SS_ExitCourseSessionReq.Parser, new[]{ "session_record_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SS_ExitCourseSessionAck), global::Msg.course.SS_ExitCourseSessionAck.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SS_SetUserProgressReq), global::Msg.course.SS_SetUserProgressReq.Parser, new[]{ "user_level" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SS_SetUserProgressAck), global::Msg.course.SS_SetUserProgressAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.course.SS_SetUserProgressData), global::Msg.course.SS_SetUserProgressData.Parser, new[]{ "section_index", "unit_index", "level_index" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  /// <summary>
  /// session数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_SessionData : pb::IMessage<PB_SessionData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_SessionData> _parser = new pb::MessageParser<PB_SessionData>(() => new PB_SessionData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_SessionData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SessionData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SessionData(PB_SessionData other) : this() {
      session_id_ = other.session_id_;
      session_index_ = other.session_index_;
      session_type_ = other.session_type_;
      exp_ = other.exp_;
      stamina_ = other.stamina_;
      dialog_mode_ = other.dialog_mode_;
      avatar_id_ = other.avatar_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SessionData Clone() {
      return new PB_SessionData(this);
    }

    /// <summary>Field number for the "session_id" field.</summary>
    public const int session_idFieldNumber = 1;
    private long session_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long session_id {
      get { return session_id_; }
      set {
        session_id_ = value;
      }
    }

    /// <summary>Field number for the "session_index" field.</summary>
    public const int session_indexFieldNumber = 2;
    private int session_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int session_index {
      get { return session_index_; }
      set {
        session_index_ = value;
      }
    }

    /// <summary>Field number for the "session_type" field.</summary>
    public const int session_typeFieldNumber = 3;
    private global::Msg.basic.PB_SessionTypeEnum session_type_ = global::Msg.basic.PB_SessionTypeEnum.SesTNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_SessionTypeEnum session_type {
      get { return session_type_; }
      set {
        session_type_ = value;
      }
    }

    /// <summary>Field number for the "exp" field.</summary>
    public const int expFieldNumber = 4;
    private int exp_;
    /// <summary>
    /// 奖励经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int exp {
      get { return exp_; }
      set {
        exp_ = value;
      }
    }

    /// <summary>Field number for the "stamina" field.</summary>
    public const int staminaFieldNumber = 5;
    private int stamina_;
    /// <summary>
    /// 体力消耗
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int stamina {
      get { return stamina_; }
      set {
        stamina_ = value;
      }
    }

    /// <summary>Field number for the "dialog_mode" field.</summary>
    public const int dialog_modeFieldNumber = 6;
    private global::Msg.basic.PB_DialogMode dialog_mode_ = global::Msg.basic.PB_DialogMode.MNone;
    /// <summary>
    /// 对话模式
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogMode dialog_mode {
      get { return dialog_mode_; }
      set {
        dialog_mode_ = value;
      }
    }

    /// <summary>Field number for the "avatar_id" field.</summary>
    public const int avatar_idFieldNumber = 7;
    private long avatar_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatar_id {
      get { return avatar_id_; }
      set {
        avatar_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_SessionData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_SessionData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (session_id != other.session_id) return false;
      if (session_index != other.session_index) return false;
      if (session_type != other.session_type) return false;
      if (exp != other.exp) return false;
      if (stamina != other.stamina) return false;
      if (dialog_mode != other.dialog_mode) return false;
      if (avatar_id != other.avatar_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (session_id != 0L) hash ^= session_id.GetHashCode();
      if (session_index != 0) hash ^= session_index.GetHashCode();
      if (session_type != global::Msg.basic.PB_SessionTypeEnum.SesTNone) hash ^= session_type.GetHashCode();
      if (exp != 0) hash ^= exp.GetHashCode();
      if (stamina != 0) hash ^= stamina.GetHashCode();
      if (dialog_mode != global::Msg.basic.PB_DialogMode.MNone) hash ^= dialog_mode.GetHashCode();
      if (avatar_id != 0L) hash ^= avatar_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (session_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(session_id);
      }
      if (session_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(session_index);
      }
      if (session_type != global::Msg.basic.PB_SessionTypeEnum.SesTNone) {
        output.WriteRawTag(24);
        output.WriteEnum((int) session_type);
      }
      if (exp != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(exp);
      }
      if (stamina != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(stamina);
      }
      if (dialog_mode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(48);
        output.WriteEnum((int) dialog_mode);
      }
      if (avatar_id != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(avatar_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (session_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(session_id);
      }
      if (session_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(session_index);
      }
      if (session_type != global::Msg.basic.PB_SessionTypeEnum.SesTNone) {
        output.WriteRawTag(24);
        output.WriteEnum((int) session_type);
      }
      if (exp != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(exp);
      }
      if (stamina != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(stamina);
      }
      if (dialog_mode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(48);
        output.WriteEnum((int) dialog_mode);
      }
      if (avatar_id != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(avatar_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (session_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(session_id);
      }
      if (session_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(session_index);
      }
      if (session_type != global::Msg.basic.PB_SessionTypeEnum.SesTNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) session_type);
      }
      if (exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(exp);
      }
      if (stamina != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(stamina);
      }
      if (dialog_mode != global::Msg.basic.PB_DialogMode.MNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) dialog_mode);
      }
      if (avatar_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatar_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_SessionData other) {
      if (other == null) {
        return;
      }
      if (other.session_id != 0L) {
        session_id = other.session_id;
      }
      if (other.session_index != 0) {
        session_index = other.session_index;
      }
      if (other.session_type != global::Msg.basic.PB_SessionTypeEnum.SesTNone) {
        session_type = other.session_type;
      }
      if (other.exp != 0) {
        exp = other.exp;
      }
      if (other.stamina != 0) {
        stamina = other.stamina;
      }
      if (other.dialog_mode != global::Msg.basic.PB_DialogMode.MNone) {
        dialog_mode = other.dialog_mode;
      }
      if (other.avatar_id != 0L) {
        avatar_id = other.avatar_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            session_id = input.ReadInt64();
            break;
          }
          case 16: {
            session_index = input.ReadInt32();
            break;
          }
          case 24: {
            session_type = (global::Msg.basic.PB_SessionTypeEnum) input.ReadEnum();
            break;
          }
          case 32: {
            exp = input.ReadInt32();
            break;
          }
          case 40: {
            stamina = input.ReadInt32();
            break;
          }
          case 48: {
            dialog_mode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 56: {
            avatar_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            session_id = input.ReadInt64();
            break;
          }
          case 16: {
            session_index = input.ReadInt32();
            break;
          }
          case 24: {
            session_type = (global::Msg.basic.PB_SessionTypeEnum) input.ReadEnum();
            break;
          }
          case 32: {
            exp = input.ReadInt32();
            break;
          }
          case 40: {
            stamina = input.ReadInt32();
            break;
          }
          case 48: {
            dialog_mode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 56: {
            avatar_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// level经验数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_LevelExpData : pb::IMessage<PB_LevelExpData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_LevelExpData> _parser = new pb::MessageParser<PB_LevelExpData>(() => new PB_LevelExpData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_LevelExpData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LevelExpData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LevelExpData(PB_LevelExpData other) : this() {
      practice_exp_ = other.practice_exp_;
      legend_exp_ = other.legend_exp_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LevelExpData Clone() {
      return new PB_LevelExpData(this);
    }

    /// <summary>Field number for the "practice_exp" field.</summary>
    public const int practice_expFieldNumber = 1;
    private int practice_exp_;
    /// <summary>
    /// 练习经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int practice_exp {
      get { return practice_exp_; }
      set {
        practice_exp_ = value;
      }
    }

    /// <summary>Field number for the "legend_exp" field.</summary>
    public const int legend_expFieldNumber = 2;
    private int legend_exp_;
    /// <summary>
    /// 传奇经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int legend_exp {
      get { return legend_exp_; }
      set {
        legend_exp_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_LevelExpData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_LevelExpData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (practice_exp != other.practice_exp) return false;
      if (legend_exp != other.legend_exp) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (practice_exp != 0) hash ^= practice_exp.GetHashCode();
      if (legend_exp != 0) hash ^= legend_exp.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (practice_exp != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(practice_exp);
      }
      if (legend_exp != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(legend_exp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (practice_exp != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(practice_exp);
      }
      if (legend_exp != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(legend_exp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (practice_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(practice_exp);
      }
      if (legend_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(legend_exp);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_LevelExpData other) {
      if (other == null) {
        return;
      }
      if (other.practice_exp != 0) {
        practice_exp = other.practice_exp;
      }
      if (other.legend_exp != 0) {
        legend_exp = other.legend_exp;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            practice_exp = input.ReadInt32();
            break;
          }
          case 16: {
            legend_exp = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            practice_exp = input.ReadInt32();
            break;
          }
          case 16: {
            legend_exp = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// level数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_LevelData : pb::IMessage<PB_LevelData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_LevelData> _parser = new pb::MessageParser<PB_LevelData>(() => new PB_LevelData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_LevelData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LevelData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LevelData(PB_LevelData other) : this() {
      level_id_ = other.level_id_;
      level_index_ = other.level_index_;
      level_title_ = other.level_title_;
      level_subtitle_ = other.level_subtitle_;
      level_type_ = other.level_type_;
      status_ = other.status_;
      box_reward_info_ = other.box_reward_info_ != null ? other.box_reward_info_.Clone() : null;
      level_exp_data_ = other.level_exp_data_ != null ? other.level_exp_data_.Clone() : null;
      session_total_ = other.session_total_;
      session_completed_ = other.session_completed_;
      session_data_list_ = other.session_data_list_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_LevelData Clone() {
      return new PB_LevelData(this);
    }

    /// <summary>Field number for the "level_id" field.</summary>
    public const int level_idFieldNumber = 1;
    private long level_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long level_id {
      get { return level_id_; }
      set {
        level_id_ = value;
      }
    }

    /// <summary>Field number for the "level_index" field.</summary>
    public const int level_indexFieldNumber = 2;
    private int level_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int level_index {
      get { return level_index_; }
      set {
        level_index_ = value;
      }
    }

    /// <summary>Field number for the "level_title" field.</summary>
    public const int level_titleFieldNumber = 3;
    private string level_title_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string level_title {
      get { return level_title_; }
      set {
        level_title_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "level_subtitle" field.</summary>
    public const int level_subtitleFieldNumber = 4;
    private string level_subtitle_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string level_subtitle {
      get { return level_subtitle_; }
      set {
        level_subtitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "level_type" field.</summary>
    public const int level_typeFieldNumber = 5;
    private global::Msg.basic.PB_LevelTypeEnum level_type_ = global::Msg.basic.PB_LevelTypeEnum.LTNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_LevelTypeEnum level_type {
      get { return level_type_; }
      set {
        level_type_ = value;
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int statusFieldNumber = 6;
    private global::Msg.basic.PB_ProgressStatusEnum status_ = global::Msg.basic.PB_ProgressStatusEnum.PSNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_ProgressStatusEnum status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    /// <summary>Field number for the "box_reward_info" field.</summary>
    public const int box_reward_infoFieldNumber = 7;
    private global::Msg.basic.PB_BoxRewardInfo box_reward_info_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BoxRewardInfo box_reward_info {
      get { return box_reward_info_; }
      set {
        box_reward_info_ = value;
      }
    }

    /// <summary>Field number for the "level_exp_data" field.</summary>
    public const int level_exp_dataFieldNumber = 8;
    private global::Msg.course.PB_LevelExpData level_exp_data_;
    /// <summary>
    /// level经验数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.course.PB_LevelExpData level_exp_data {
      get { return level_exp_data_; }
      set {
        level_exp_data_ = value;
      }
    }

    /// <summary>Field number for the "session_total" field.</summary>
    public const int session_totalFieldNumber = 9;
    private int session_total_;
    /// <summary>
    /// session总数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int session_total {
      get { return session_total_; }
      set {
        session_total_ = value;
      }
    }

    /// <summary>Field number for the "session_completed" field.</summary>
    public const int session_completedFieldNumber = 10;
    private int session_completed_;
    /// <summary>
    /// 已完成session数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int session_completed {
      get { return session_completed_; }
      set {
        session_completed_ = value;
      }
    }

    /// <summary>Field number for the "session_data_list" field.</summary>
    public const int session_data_listFieldNumber = 11;
    private static readonly pb::FieldCodec<global::Msg.course.PB_SessionData> _repeated_session_data_list_codec
        = pb::FieldCodec.ForMessage(90, global::Msg.course.PB_SessionData.Parser);
    private readonly pbc::RepeatedField<global::Msg.course.PB_SessionData> session_data_list_ = new pbc::RepeatedField<global::Msg.course.PB_SessionData>();
    /// <summary>
    /// session数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.course.PB_SessionData> session_data_list {
      get { return session_data_list_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_LevelData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_LevelData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (level_id != other.level_id) return false;
      if (level_index != other.level_index) return false;
      if (level_title != other.level_title) return false;
      if (level_subtitle != other.level_subtitle) return false;
      if (level_type != other.level_type) return false;
      if (status != other.status) return false;
      if (!object.Equals(box_reward_info, other.box_reward_info)) return false;
      if (!object.Equals(level_exp_data, other.level_exp_data)) return false;
      if (session_total != other.session_total) return false;
      if (session_completed != other.session_completed) return false;
      if(!session_data_list_.Equals(other.session_data_list_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (level_id != 0L) hash ^= level_id.GetHashCode();
      if (level_index != 0) hash ^= level_index.GetHashCode();
      if (level_title.Length != 0) hash ^= level_title.GetHashCode();
      if (level_subtitle.Length != 0) hash ^= level_subtitle.GetHashCode();
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) hash ^= level_type.GetHashCode();
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) hash ^= status.GetHashCode();
      if (box_reward_info_ != null) hash ^= box_reward_info.GetHashCode();
      if (level_exp_data_ != null) hash ^= level_exp_data.GetHashCode();
      if (session_total != 0) hash ^= session_total.GetHashCode();
      if (session_completed != 0) hash ^= session_completed.GetHashCode();
      hash ^= session_data_list_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (level_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(level_id);
      }
      if (level_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(level_index);
      }
      if (level_title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(level_title);
      }
      if (level_subtitle.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(level_subtitle);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) level_type);
      }
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        output.WriteRawTag(48);
        output.WriteEnum((int) status);
      }
      if (box_reward_info_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(box_reward_info);
      }
      if (level_exp_data_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(level_exp_data);
      }
      if (session_total != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(session_total);
      }
      if (session_completed != 0) {
        output.WriteRawTag(80);
        output.WriteInt32(session_completed);
      }
      session_data_list_.WriteTo(output, _repeated_session_data_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (level_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(level_id);
      }
      if (level_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(level_index);
      }
      if (level_title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(level_title);
      }
      if (level_subtitle.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(level_subtitle);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) level_type);
      }
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        output.WriteRawTag(48);
        output.WriteEnum((int) status);
      }
      if (box_reward_info_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(box_reward_info);
      }
      if (level_exp_data_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(level_exp_data);
      }
      if (session_total != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(session_total);
      }
      if (session_completed != 0) {
        output.WriteRawTag(80);
        output.WriteInt32(session_completed);
      }
      session_data_list_.WriteTo(ref output, _repeated_session_data_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (level_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(level_id);
      }
      if (level_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(level_index);
      }
      if (level_title.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(level_title);
      }
      if (level_subtitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(level_subtitle);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) level_type);
      }
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) status);
      }
      if (box_reward_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(box_reward_info);
      }
      if (level_exp_data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(level_exp_data);
      }
      if (session_total != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(session_total);
      }
      if (session_completed != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(session_completed);
      }
      size += session_data_list_.CalculateSize(_repeated_session_data_list_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_LevelData other) {
      if (other == null) {
        return;
      }
      if (other.level_id != 0L) {
        level_id = other.level_id;
      }
      if (other.level_index != 0) {
        level_index = other.level_index;
      }
      if (other.level_title.Length != 0) {
        level_title = other.level_title;
      }
      if (other.level_subtitle.Length != 0) {
        level_subtitle = other.level_subtitle;
      }
      if (other.level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        level_type = other.level_type;
      }
      if (other.status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        status = other.status;
      }
      if (other.box_reward_info_ != null) {
        if (box_reward_info_ == null) {
          box_reward_info = new global::Msg.basic.PB_BoxRewardInfo();
        }
        box_reward_info.MergeFrom(other.box_reward_info);
      }
      if (other.level_exp_data_ != null) {
        if (level_exp_data_ == null) {
          level_exp_data = new global::Msg.course.PB_LevelExpData();
        }
        level_exp_data.MergeFrom(other.level_exp_data);
      }
      if (other.session_total != 0) {
        session_total = other.session_total;
      }
      if (other.session_completed != 0) {
        session_completed = other.session_completed;
      }
      session_data_list_.Add(other.session_data_list_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            level_id = input.ReadInt64();
            break;
          }
          case 16: {
            level_index = input.ReadInt32();
            break;
          }
          case 26: {
            level_title = input.ReadString();
            break;
          }
          case 34: {
            level_subtitle = input.ReadString();
            break;
          }
          case 40: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 48: {
            status = (global::Msg.basic.PB_ProgressStatusEnum) input.ReadEnum();
            break;
          }
          case 58: {
            if (box_reward_info_ == null) {
              box_reward_info = new global::Msg.basic.PB_BoxRewardInfo();
            }
            input.ReadMessage(box_reward_info);
            break;
          }
          case 66: {
            if (level_exp_data_ == null) {
              level_exp_data = new global::Msg.course.PB_LevelExpData();
            }
            input.ReadMessage(level_exp_data);
            break;
          }
          case 72: {
            session_total = input.ReadInt32();
            break;
          }
          case 80: {
            session_completed = input.ReadInt32();
            break;
          }
          case 90: {
            session_data_list_.AddEntriesFrom(input, _repeated_session_data_list_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            level_id = input.ReadInt64();
            break;
          }
          case 16: {
            level_index = input.ReadInt32();
            break;
          }
          case 26: {
            level_title = input.ReadString();
            break;
          }
          case 34: {
            level_subtitle = input.ReadString();
            break;
          }
          case 40: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 48: {
            status = (global::Msg.basic.PB_ProgressStatusEnum) input.ReadEnum();
            break;
          }
          case 58: {
            if (box_reward_info_ == null) {
              box_reward_info = new global::Msg.basic.PB_BoxRewardInfo();
            }
            input.ReadMessage(box_reward_info);
            break;
          }
          case 66: {
            if (level_exp_data_ == null) {
              level_exp_data = new global::Msg.course.PB_LevelExpData();
            }
            input.ReadMessage(level_exp_data);
            break;
          }
          case 72: {
            session_total = input.ReadInt32();
            break;
          }
          case 80: {
            session_completed = input.ReadInt32();
            break;
          }
          case 90: {
            session_data_list_.AddEntriesFrom(ref input, _repeated_session_data_list_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// unit数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UnitData : pb::IMessage<PB_UnitData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UnitData> _parser = new pb::MessageParser<PB_UnitData>(() => new PB_UnitData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UnitData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UnitData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UnitData(PB_UnitData other) : this() {
      unit_id_ = other.unit_id_;
      unit_index_ = other.unit_index_;
      unit_title_ = other.unit_title_;
      level_data_list_ = other.level_data_list_.Clone();
      avatar_head_url_ = other.avatar_head_url_;
      status_ = other.status_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UnitData Clone() {
      return new PB_UnitData(this);
    }

    /// <summary>Field number for the "unit_id" field.</summary>
    public const int unit_idFieldNumber = 1;
    private long unit_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long unit_id {
      get { return unit_id_; }
      set {
        unit_id_ = value;
      }
    }

    /// <summary>Field number for the "unit_index" field.</summary>
    public const int unit_indexFieldNumber = 2;
    private int unit_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int unit_index {
      get { return unit_index_; }
      set {
        unit_index_ = value;
      }
    }

    /// <summary>Field number for the "unit_title" field.</summary>
    public const int unit_titleFieldNumber = 3;
    private string unit_title_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string unit_title {
      get { return unit_title_; }
      set {
        unit_title_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "level_data_list" field.</summary>
    public const int level_data_listFieldNumber = 4;
    private static readonly pb::FieldCodec<global::Msg.course.PB_LevelData> _repeated_level_data_list_codec
        = pb::FieldCodec.ForMessage(34, global::Msg.course.PB_LevelData.Parser);
    private readonly pbc::RepeatedField<global::Msg.course.PB_LevelData> level_data_list_ = new pbc::RepeatedField<global::Msg.course.PB_LevelData>();
    /// <summary>
    /// level数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.course.PB_LevelData> level_data_list {
      get { return level_data_list_; }
    }

    /// <summary>Field number for the "avatar_head_url" field.</summary>
    public const int avatar_head_urlFieldNumber = 5;
    private string avatar_head_url_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string avatar_head_url {
      get { return avatar_head_url_; }
      set {
        avatar_head_url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int statusFieldNumber = 6;
    private global::Msg.basic.PB_ProgressStatusEnum status_ = global::Msg.basic.PB_ProgressStatusEnum.PSNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_ProgressStatusEnum status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UnitData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UnitData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (unit_id != other.unit_id) return false;
      if (unit_index != other.unit_index) return false;
      if (unit_title != other.unit_title) return false;
      if(!level_data_list_.Equals(other.level_data_list_)) return false;
      if (avatar_head_url != other.avatar_head_url) return false;
      if (status != other.status) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (unit_id != 0L) hash ^= unit_id.GetHashCode();
      if (unit_index != 0) hash ^= unit_index.GetHashCode();
      if (unit_title.Length != 0) hash ^= unit_title.GetHashCode();
      hash ^= level_data_list_.GetHashCode();
      if (avatar_head_url.Length != 0) hash ^= avatar_head_url.GetHashCode();
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) hash ^= status.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (unit_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(unit_id);
      }
      if (unit_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(unit_index);
      }
      if (unit_title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(unit_title);
      }
      level_data_list_.WriteTo(output, _repeated_level_data_list_codec);
      if (avatar_head_url.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(avatar_head_url);
      }
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        output.WriteRawTag(48);
        output.WriteEnum((int) status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (unit_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(unit_id);
      }
      if (unit_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(unit_index);
      }
      if (unit_title.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(unit_title);
      }
      level_data_list_.WriteTo(ref output, _repeated_level_data_list_codec);
      if (avatar_head_url.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(avatar_head_url);
      }
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        output.WriteRawTag(48);
        output.WriteEnum((int) status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (unit_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(unit_id);
      }
      if (unit_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(unit_index);
      }
      if (unit_title.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(unit_title);
      }
      size += level_data_list_.CalculateSize(_repeated_level_data_list_codec);
      if (avatar_head_url.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(avatar_head_url);
      }
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) status);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UnitData other) {
      if (other == null) {
        return;
      }
      if (other.unit_id != 0L) {
        unit_id = other.unit_id;
      }
      if (other.unit_index != 0) {
        unit_index = other.unit_index;
      }
      if (other.unit_title.Length != 0) {
        unit_title = other.unit_title;
      }
      level_data_list_.Add(other.level_data_list_);
      if (other.avatar_head_url.Length != 0) {
        avatar_head_url = other.avatar_head_url;
      }
      if (other.status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        status = other.status;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            unit_id = input.ReadInt64();
            break;
          }
          case 16: {
            unit_index = input.ReadInt32();
            break;
          }
          case 26: {
            unit_title = input.ReadString();
            break;
          }
          case 34: {
            level_data_list_.AddEntriesFrom(input, _repeated_level_data_list_codec);
            break;
          }
          case 42: {
            avatar_head_url = input.ReadString();
            break;
          }
          case 48: {
            status = (global::Msg.basic.PB_ProgressStatusEnum) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            unit_id = input.ReadInt64();
            break;
          }
          case 16: {
            unit_index = input.ReadInt32();
            break;
          }
          case 26: {
            unit_title = input.ReadString();
            break;
          }
          case 34: {
            level_data_list_.AddEntriesFrom(ref input, _repeated_level_data_list_codec);
            break;
          }
          case 42: {
            avatar_head_url = input.ReadString();
            break;
          }
          case 48: {
            status = (global::Msg.basic.PB_ProgressStatusEnum) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// section数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_SectionData : pb::IMessage<PB_SectionData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_SectionData> _parser = new pb::MessageParser<PB_SectionData>(() => new PB_SectionData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_SectionData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SectionData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SectionData(PB_SectionData other) : this() {
      section_index_ = other.section_index_;
      section_title_ = other.section_title_;
      section_subtitle_ = other.section_subtitle_;
      unit_data_list_ = other.unit_data_list_.Clone();
      unit_total_ = other.unit_total_;
      unit_completed_ = other.unit_completed_;
      status_ = other.status_;
      cover_url_ = other.cover_url_;
      section_id_ = other.section_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SectionData Clone() {
      return new PB_SectionData(this);
    }

    /// <summary>Field number for the "section_index" field.</summary>
    public const int section_indexFieldNumber = 1;
    private int section_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int section_index {
      get { return section_index_; }
      set {
        section_index_ = value;
      }
    }

    /// <summary>Field number for the "section_title" field.</summary>
    public const int section_titleFieldNumber = 2;
    private string section_title_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string section_title {
      get { return section_title_; }
      set {
        section_title_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "section_subtitle" field.</summary>
    public const int section_subtitleFieldNumber = 3;
    private string section_subtitle_ = "";
    /// <summary>
    /// 例句
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string section_subtitle {
      get { return section_subtitle_; }
      set {
        section_subtitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "unit_data_list" field.</summary>
    public const int unit_data_listFieldNumber = 4;
    private static readonly pb::FieldCodec<global::Msg.course.PB_UnitData> _repeated_unit_data_list_codec
        = pb::FieldCodec.ForMessage(34, global::Msg.course.PB_UnitData.Parser);
    private readonly pbc::RepeatedField<global::Msg.course.PB_UnitData> unit_data_list_ = new pbc::RepeatedField<global::Msg.course.PB_UnitData>();
    /// <summary>
    /// unit数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.course.PB_UnitData> unit_data_list {
      get { return unit_data_list_; }
    }

    /// <summary>Field number for the "unit_total" field.</summary>
    public const int unit_totalFieldNumber = 5;
    private int unit_total_;
    /// <summary>
    /// unit总数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int unit_total {
      get { return unit_total_; }
      set {
        unit_total_ = value;
      }
    }

    /// <summary>Field number for the "unit_completed" field.</summary>
    public const int unit_completedFieldNumber = 6;
    private int unit_completed_;
    /// <summary>
    /// 已完成unit数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int unit_completed {
      get { return unit_completed_; }
      set {
        unit_completed_ = value;
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int statusFieldNumber = 7;
    private global::Msg.basic.PB_ProgressStatusEnum status_ = global::Msg.basic.PB_ProgressStatusEnum.PSNone;
    /// <summary>
    /// 进度状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_ProgressStatusEnum status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    /// <summary>Field number for the "cover_url" field.</summary>
    public const int cover_urlFieldNumber = 8;
    private string cover_url_ = "";
    /// <summary>
    /// 封面url
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string cover_url {
      get { return cover_url_; }
      set {
        cover_url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "section_id" field.</summary>
    public const int section_idFieldNumber = 9;
    private long section_id_;
    /// <summary>
    /// section_id 
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long section_id {
      get { return section_id_; }
      set {
        section_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_SectionData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_SectionData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (section_index != other.section_index) return false;
      if (section_title != other.section_title) return false;
      if (section_subtitle != other.section_subtitle) return false;
      if(!unit_data_list_.Equals(other.unit_data_list_)) return false;
      if (unit_total != other.unit_total) return false;
      if (unit_completed != other.unit_completed) return false;
      if (status != other.status) return false;
      if (cover_url != other.cover_url) return false;
      if (section_id != other.section_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (section_index != 0) hash ^= section_index.GetHashCode();
      if (section_title.Length != 0) hash ^= section_title.GetHashCode();
      if (section_subtitle.Length != 0) hash ^= section_subtitle.GetHashCode();
      hash ^= unit_data_list_.GetHashCode();
      if (unit_total != 0) hash ^= unit_total.GetHashCode();
      if (unit_completed != 0) hash ^= unit_completed.GetHashCode();
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) hash ^= status.GetHashCode();
      if (cover_url.Length != 0) hash ^= cover_url.GetHashCode();
      if (section_id != 0L) hash ^= section_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (section_index != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(section_index);
      }
      if (section_title.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(section_title);
      }
      if (section_subtitle.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(section_subtitle);
      }
      unit_data_list_.WriteTo(output, _repeated_unit_data_list_codec);
      if (unit_total != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(unit_total);
      }
      if (unit_completed != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(unit_completed);
      }
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        output.WriteRawTag(56);
        output.WriteEnum((int) status);
      }
      if (cover_url.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(cover_url);
      }
      if (section_id != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(section_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (section_index != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(section_index);
      }
      if (section_title.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(section_title);
      }
      if (section_subtitle.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(section_subtitle);
      }
      unit_data_list_.WriteTo(ref output, _repeated_unit_data_list_codec);
      if (unit_total != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(unit_total);
      }
      if (unit_completed != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(unit_completed);
      }
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        output.WriteRawTag(56);
        output.WriteEnum((int) status);
      }
      if (cover_url.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(cover_url);
      }
      if (section_id != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(section_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (section_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(section_index);
      }
      if (section_title.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(section_title);
      }
      if (section_subtitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(section_subtitle);
      }
      size += unit_data_list_.CalculateSize(_repeated_unit_data_list_codec);
      if (unit_total != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(unit_total);
      }
      if (unit_completed != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(unit_completed);
      }
      if (status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) status);
      }
      if (cover_url.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(cover_url);
      }
      if (section_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(section_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_SectionData other) {
      if (other == null) {
        return;
      }
      if (other.section_index != 0) {
        section_index = other.section_index;
      }
      if (other.section_title.Length != 0) {
        section_title = other.section_title;
      }
      if (other.section_subtitle.Length != 0) {
        section_subtitle = other.section_subtitle;
      }
      unit_data_list_.Add(other.unit_data_list_);
      if (other.unit_total != 0) {
        unit_total = other.unit_total;
      }
      if (other.unit_completed != 0) {
        unit_completed = other.unit_completed;
      }
      if (other.status != global::Msg.basic.PB_ProgressStatusEnum.PSNone) {
        status = other.status;
      }
      if (other.cover_url.Length != 0) {
        cover_url = other.cover_url;
      }
      if (other.section_id != 0L) {
        section_id = other.section_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            section_index = input.ReadInt32();
            break;
          }
          case 18: {
            section_title = input.ReadString();
            break;
          }
          case 26: {
            section_subtitle = input.ReadString();
            break;
          }
          case 34: {
            unit_data_list_.AddEntriesFrom(input, _repeated_unit_data_list_codec);
            break;
          }
          case 40: {
            unit_total = input.ReadInt32();
            break;
          }
          case 48: {
            unit_completed = input.ReadInt32();
            break;
          }
          case 56: {
            status = (global::Msg.basic.PB_ProgressStatusEnum) input.ReadEnum();
            break;
          }
          case 66: {
            cover_url = input.ReadString();
            break;
          }
          case 72: {
            section_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            section_index = input.ReadInt32();
            break;
          }
          case 18: {
            section_title = input.ReadString();
            break;
          }
          case 26: {
            section_subtitle = input.ReadString();
            break;
          }
          case 34: {
            unit_data_list_.AddEntriesFrom(ref input, _repeated_unit_data_list_codec);
            break;
          }
          case 40: {
            unit_total = input.ReadInt32();
            break;
          }
          case 48: {
            unit_completed = input.ReadInt32();
            break;
          }
          case 56: {
            status = (global::Msg.basic.PB_ProgressStatusEnum) input.ReadEnum();
            break;
          }
          case 66: {
            cover_url = input.ReadString();
            break;
          }
          case 72: {
            section_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// course数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_CourseData : pb::IMessage<PB_CourseData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_CourseData> _parser = new pb::MessageParser<PB_CourseData>(() => new PB_CourseData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_CourseData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_CourseData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_CourseData(PB_CourseData other) : this() {
      course_id_ = other.course_id_;
      section_data_list_ = other.section_data_list_.Clone();
      op_type_ = other.op_type_;
      skip_exp_data_ = other.skip_exp_data_ != null ? other.skip_exp_data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_CourseData Clone() {
      return new PB_CourseData(this);
    }

    /// <summary>Field number for the "course_id" field.</summary>
    public const int course_idFieldNumber = 1;
    private long course_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long course_id {
      get { return course_id_; }
      set {
        course_id_ = value;
      }
    }

    /// <summary>Field number for the "section_data_list" field.</summary>
    public const int section_data_listFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Msg.course.PB_SectionData> _repeated_section_data_list_codec
        = pb::FieldCodec.ForMessage(18, global::Msg.course.PB_SectionData.Parser);
    private readonly pbc::RepeatedField<global::Msg.course.PB_SectionData> section_data_list_ = new pbc::RepeatedField<global::Msg.course.PB_SectionData>();
    /// <summary>
    /// section数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.course.PB_SectionData> section_data_list {
      get { return section_data_list_; }
    }

    /// <summary>Field number for the "op_type" field.</summary>
    public const int op_typeFieldNumber = 3;
    private int op_type_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int op_type {
      get { return op_type_; }
      set {
        op_type_ = value;
      }
    }

    /// <summary>Field number for the "skip_exp_data" field.</summary>
    public const int skip_exp_dataFieldNumber = 4;
    private global::Msg.course.PB_SkipExpData skip_exp_data_;
    /// <summary>
    /// 跳关经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.course.PB_SkipExpData skip_exp_data {
      get { return skip_exp_data_; }
      set {
        skip_exp_data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_CourseData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_CourseData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (course_id != other.course_id) return false;
      if(!section_data_list_.Equals(other.section_data_list_)) return false;
      if (op_type != other.op_type) return false;
      if (!object.Equals(skip_exp_data, other.skip_exp_data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (course_id != 0L) hash ^= course_id.GetHashCode();
      hash ^= section_data_list_.GetHashCode();
      if (op_type != 0) hash ^= op_type.GetHashCode();
      if (skip_exp_data_ != null) hash ^= skip_exp_data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      section_data_list_.WriteTo(output, _repeated_section_data_list_codec);
      if (op_type != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(op_type);
      }
      if (skip_exp_data_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(skip_exp_data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      section_data_list_.WriteTo(ref output, _repeated_section_data_list_codec);
      if (op_type != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(op_type);
      }
      if (skip_exp_data_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(skip_exp_data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (course_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(course_id);
      }
      size += section_data_list_.CalculateSize(_repeated_section_data_list_codec);
      if (op_type != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(op_type);
      }
      if (skip_exp_data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(skip_exp_data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_CourseData other) {
      if (other == null) {
        return;
      }
      if (other.course_id != 0L) {
        course_id = other.course_id;
      }
      section_data_list_.Add(other.section_data_list_);
      if (other.op_type != 0) {
        op_type = other.op_type;
      }
      if (other.skip_exp_data_ != null) {
        if (skip_exp_data_ == null) {
          skip_exp_data = new global::Msg.course.PB_SkipExpData();
        }
        skip_exp_data.MergeFrom(other.skip_exp_data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 18: {
            section_data_list_.AddEntriesFrom(input, _repeated_section_data_list_codec);
            break;
          }
          case 24: {
            op_type = input.ReadInt32();
            break;
          }
          case 34: {
            if (skip_exp_data_ == null) {
              skip_exp_data = new global::Msg.course.PB_SkipExpData();
            }
            input.ReadMessage(skip_exp_data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 18: {
            section_data_list_.AddEntriesFrom(ref input, _repeated_section_data_list_codec);
            break;
          }
          case 24: {
            op_type = input.ReadInt32();
            break;
          }
          case 34: {
            if (skip_exp_data_ == null) {
              skip_exp_data = new global::Msg.course.PB_SkipExpData();
            }
            input.ReadMessage(skip_exp_data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_SkipExpData : pb::IMessage<PB_SkipExpData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_SkipExpData> _parser = new pb::MessageParser<PB_SkipExpData>(() => new PB_SkipExpData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_SkipExpData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SkipExpData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SkipExpData(PB_SkipExpData other) : this() {
      unit_skip_exp_ = other.unit_skip_exp_;
      section_skip_exp_ = other.section_skip_exp_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SkipExpData Clone() {
      return new PB_SkipExpData(this);
    }

    /// <summary>Field number for the "unit_skip_exp" field.</summary>
    public const int unit_skip_expFieldNumber = 1;
    private int unit_skip_exp_;
    /// <summary>
    /// unit跳关经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int unit_skip_exp {
      get { return unit_skip_exp_; }
      set {
        unit_skip_exp_ = value;
      }
    }

    /// <summary>Field number for the "section_skip_exp" field.</summary>
    public const int section_skip_expFieldNumber = 2;
    private int section_skip_exp_;
    /// <summary>
    /// section跳关经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int section_skip_exp {
      get { return section_skip_exp_; }
      set {
        section_skip_exp_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_SkipExpData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_SkipExpData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (unit_skip_exp != other.unit_skip_exp) return false;
      if (section_skip_exp != other.section_skip_exp) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (unit_skip_exp != 0) hash ^= unit_skip_exp.GetHashCode();
      if (section_skip_exp != 0) hash ^= section_skip_exp.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (unit_skip_exp != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(unit_skip_exp);
      }
      if (section_skip_exp != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(section_skip_exp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (unit_skip_exp != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(unit_skip_exp);
      }
      if (section_skip_exp != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(section_skip_exp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (unit_skip_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(unit_skip_exp);
      }
      if (section_skip_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(section_skip_exp);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_SkipExpData other) {
      if (other == null) {
        return;
      }
      if (other.unit_skip_exp != 0) {
        unit_skip_exp = other.unit_skip_exp;
      }
      if (other.section_skip_exp != 0) {
        section_skip_exp = other.section_skip_exp;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            unit_skip_exp = input.ReadInt32();
            break;
          }
          case 16: {
            section_skip_exp = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            unit_skip_exp = input.ReadInt32();
            break;
          }
          case 16: {
            section_skip_exp = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 课程请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetUserCourseReq : pb::IMessage<CS_GetUserCourseReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetUserCourseReq> _parser = new pb::MessageParser<CS_GetUserCourseReq>(() => new CS_GetUserCourseReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetUserCourseReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserCourseReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserCourseReq(CS_GetUserCourseReq other) : this() {
      op_type_ = other.op_type_;
      section_id_ = other.section_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserCourseReq Clone() {
      return new CS_GetUserCourseReq(this);
    }

    /// <summary>Field number for the "op_type" field.</summary>
    public const int op_typeFieldNumber = 1;
    private int op_type_;
    /// <summary>
    /// 端上参数透传
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int op_type {
      get { return op_type_; }
      set {
        op_type_ = value;
      }
    }

    /// <summary>Field number for the "section_id" field.</summary>
    public const int section_idFieldNumber = 2;
    private long section_id_;
    /// <summary>
    /// section_id -1表示当前section >0表示对应section
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long section_id {
      get { return section_id_; }
      set {
        section_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetUserCourseReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetUserCourseReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (op_type != other.op_type) return false;
      if (section_id != other.section_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (op_type != 0) hash ^= op_type.GetHashCode();
      if (section_id != 0L) hash ^= section_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (op_type != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(op_type);
      }
      if (section_id != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(section_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (op_type != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(op_type);
      }
      if (section_id != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(section_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (op_type != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(op_type);
      }
      if (section_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(section_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetUserCourseReq other) {
      if (other == null) {
        return;
      }
      if (other.op_type != 0) {
        op_type = other.op_type;
      }
      if (other.section_id != 0L) {
        section_id = other.section_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            op_type = input.ReadInt32();
            break;
          }
          case 16: {
            section_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            op_type = input.ReadInt32();
            break;
          }
          case 16: {
            section_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 学习路径响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetUserShelfAck : pb::IMessage<SC_GetUserShelfAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetUserShelfAck> _parser = new pb::MessageParser<SC_GetUserShelfAck>(() => new SC_GetUserShelfAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetUserShelfAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserShelfAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserShelfAck(SC_GetUserShelfAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserShelfAck Clone() {
      return new SC_GetUserShelfAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.course.PB_CourseData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.course.PB_CourseData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetUserShelfAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetUserShelfAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetUserShelfAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.course.PB_CourseData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.course.PB_CourseData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.course.PB_CourseData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 课程请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetUserShelfReq : pb::IMessage<CS_GetUserShelfReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetUserShelfReq> _parser = new pb::MessageParser<CS_GetUserShelfReq>(() => new CS_GetUserShelfReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetUserShelfReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserShelfReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserShelfReq(CS_GetUserShelfReq other) : this() {
      op_type_ = other.op_type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserShelfReq Clone() {
      return new CS_GetUserShelfReq(this);
    }

    /// <summary>Field number for the "op_type" field.</summary>
    public const int op_typeFieldNumber = 1;
    private int op_type_;
    /// <summary>
    /// 端上参数透传
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int op_type {
      get { return op_type_; }
      set {
        op_type_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetUserShelfReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetUserShelfReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (op_type != other.op_type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (op_type != 0) hash ^= op_type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (op_type != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(op_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (op_type != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(op_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (op_type != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(op_type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetUserShelfReq other) {
      if (other == null) {
        return;
      }
      if (other.op_type != 0) {
        op_type = other.op_type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            op_type = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            op_type = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 学习路径响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetUserCourseAck : pb::IMessage<SC_GetUserCourseAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetUserCourseAck> _parser = new pb::MessageParser<SC_GetUserCourseAck>(() => new SC_GetUserCourseAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetUserCourseAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserCourseAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserCourseAck(SC_GetUserCourseAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserCourseAck Clone() {
      return new SC_GetUserCourseAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.course.PB_CourseData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.course.PB_CourseData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetUserCourseAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetUserCourseAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetUserCourseAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.course.PB_CourseData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.course.PB_CourseData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.course.PB_CourseData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 课程跳过请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_SkipCourseReq : pb::IMessage<CS_SkipCourseReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_SkipCourseReq> _parser = new pb::MessageParser<CS_SkipCourseReq>(() => new CS_SkipCourseReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_SkipCourseReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SkipCourseReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SkipCourseReq(CS_SkipCourseReq other) : this() {
      course_id_ = other.course_id_;
      skip_type_ = other.skip_type_;
      section_index_ = other.section_index_;
      unit_index_ = other.unit_index_;
      level_index_ = other.level_index_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SkipCourseReq Clone() {
      return new CS_SkipCourseReq(this);
    }

    /// <summary>Field number for the "course_id" field.</summary>
    public const int course_idFieldNumber = 1;
    private long course_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long course_id {
      get { return course_id_; }
      set {
        course_id_ = value;
      }
    }

    /// <summary>Field number for the "skip_type" field.</summary>
    public const int skip_typeFieldNumber = 2;
    private global::Msg.basic.PB_SkipTypeEnum skip_type_ = global::Msg.basic.PB_SkipTypeEnum.SkipTNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_SkipTypeEnum skip_type {
      get { return skip_type_; }
      set {
        skip_type_ = value;
      }
    }

    /// <summary>Field number for the "section_index" field.</summary>
    public const int section_indexFieldNumber = 3;
    private int section_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int section_index {
      get { return section_index_; }
      set {
        section_index_ = value;
      }
    }

    /// <summary>Field number for the "unit_index" field.</summary>
    public const int unit_indexFieldNumber = 4;
    private int unit_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int unit_index {
      get { return unit_index_; }
      set {
        unit_index_ = value;
      }
    }

    /// <summary>Field number for the "level_index" field.</summary>
    public const int level_indexFieldNumber = 5;
    private int level_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int level_index {
      get { return level_index_; }
      set {
        level_index_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_SkipCourseReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_SkipCourseReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (course_id != other.course_id) return false;
      if (skip_type != other.skip_type) return false;
      if (section_index != other.section_index) return false;
      if (unit_index != other.unit_index) return false;
      if (level_index != other.level_index) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (course_id != 0L) hash ^= course_id.GetHashCode();
      if (skip_type != global::Msg.basic.PB_SkipTypeEnum.SkipTNone) hash ^= skip_type.GetHashCode();
      if (section_index != 0) hash ^= section_index.GetHashCode();
      if (unit_index != 0) hash ^= unit_index.GetHashCode();
      if (level_index != 0) hash ^= level_index.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      if (skip_type != global::Msg.basic.PB_SkipTypeEnum.SkipTNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) skip_type);
      }
      if (section_index != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(section_index);
      }
      if (unit_index != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(unit_index);
      }
      if (level_index != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(level_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      if (skip_type != global::Msg.basic.PB_SkipTypeEnum.SkipTNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) skip_type);
      }
      if (section_index != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(section_index);
      }
      if (unit_index != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(unit_index);
      }
      if (level_index != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(level_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (course_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(course_id);
      }
      if (skip_type != global::Msg.basic.PB_SkipTypeEnum.SkipTNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) skip_type);
      }
      if (section_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(section_index);
      }
      if (unit_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(unit_index);
      }
      if (level_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(level_index);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_SkipCourseReq other) {
      if (other == null) {
        return;
      }
      if (other.course_id != 0L) {
        course_id = other.course_id;
      }
      if (other.skip_type != global::Msg.basic.PB_SkipTypeEnum.SkipTNone) {
        skip_type = other.skip_type;
      }
      if (other.section_index != 0) {
        section_index = other.section_index;
      }
      if (other.unit_index != 0) {
        unit_index = other.unit_index;
      }
      if (other.level_index != 0) {
        level_index = other.level_index;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 16: {
            skip_type = (global::Msg.basic.PB_SkipTypeEnum) input.ReadEnum();
            break;
          }
          case 24: {
            section_index = input.ReadInt32();
            break;
          }
          case 32: {
            unit_index = input.ReadInt32();
            break;
          }
          case 40: {
            level_index = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 16: {
            skip_type = (global::Msg.basic.PB_SkipTypeEnum) input.ReadEnum();
            break;
          }
          case 24: {
            section_index = input.ReadInt32();
            break;
          }
          case 32: {
            unit_index = input.ReadInt32();
            break;
          }
          case 40: {
            level_index = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 课程跳过响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_SkipCourseAck : pb::IMessage<SC_SkipCourseAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_SkipCourseAck> _parser = new pb::MessageParser<SC_SkipCourseAck>(() => new SC_SkipCourseAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_SkipCourseAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SkipCourseAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SkipCourseAck(SC_SkipCourseAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SkipCourseAck Clone() {
      return new SC_SkipCourseAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_SkipCourseAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_SkipCourseAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_SkipCourseAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_RewardBoxReq : pb::IMessage<CS_RewardBoxReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_RewardBoxReq> _parser = new pb::MessageParser<CS_RewardBoxReq>(() => new CS_RewardBoxReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_RewardBoxReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_RewardBoxReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_RewardBoxReq(CS_RewardBoxReq other) : this() {
      course_id_ = other.course_id_;
      section_index_ = other.section_index_;
      unit_index_ = other.unit_index_;
      level_index_ = other.level_index_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_RewardBoxReq Clone() {
      return new CS_RewardBoxReq(this);
    }

    /// <summary>Field number for the "course_id" field.</summary>
    public const int course_idFieldNumber = 1;
    private long course_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long course_id {
      get { return course_id_; }
      set {
        course_id_ = value;
      }
    }

    /// <summary>Field number for the "section_index" field.</summary>
    public const int section_indexFieldNumber = 2;
    private int section_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int section_index {
      get { return section_index_; }
      set {
        section_index_ = value;
      }
    }

    /// <summary>Field number for the "unit_index" field.</summary>
    public const int unit_indexFieldNumber = 3;
    private int unit_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int unit_index {
      get { return unit_index_; }
      set {
        unit_index_ = value;
      }
    }

    /// <summary>Field number for the "level_index" field.</summary>
    public const int level_indexFieldNumber = 4;
    private int level_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int level_index {
      get { return level_index_; }
      set {
        level_index_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_RewardBoxReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_RewardBoxReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (course_id != other.course_id) return false;
      if (section_index != other.section_index) return false;
      if (unit_index != other.unit_index) return false;
      if (level_index != other.level_index) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (course_id != 0L) hash ^= course_id.GetHashCode();
      if (section_index != 0) hash ^= section_index.GetHashCode();
      if (unit_index != 0) hash ^= unit_index.GetHashCode();
      if (level_index != 0) hash ^= level_index.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      if (section_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(section_index);
      }
      if (unit_index != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(unit_index);
      }
      if (level_index != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(level_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      if (section_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(section_index);
      }
      if (unit_index != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(unit_index);
      }
      if (level_index != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(level_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (course_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(course_id);
      }
      if (section_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(section_index);
      }
      if (unit_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(unit_index);
      }
      if (level_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(level_index);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_RewardBoxReq other) {
      if (other == null) {
        return;
      }
      if (other.course_id != 0L) {
        course_id = other.course_id;
      }
      if (other.section_index != 0) {
        section_index = other.section_index;
      }
      if (other.unit_index != 0) {
        unit_index = other.unit_index;
      }
      if (other.level_index != 0) {
        level_index = other.level_index;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 16: {
            section_index = input.ReadInt32();
            break;
          }
          case 24: {
            unit_index = input.ReadInt32();
            break;
          }
          case 32: {
            level_index = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 16: {
            section_index = input.ReadInt32();
            break;
          }
          case 24: {
            unit_index = input.ReadInt32();
            break;
          }
          case 32: {
            level_index = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 学习路径奖励发放回复
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_RewardBoxAck : pb::IMessage<SC_RewardBoxAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_RewardBoxAck> _parser = new pb::MessageParser<SC_RewardBoxAck>(() => new SC_RewardBoxAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_RewardBoxAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RewardBoxAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RewardBoxAck(SC_RewardBoxAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RewardBoxAck Clone() {
      return new SC_RewardBoxAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.course.PB_RewardBoxRsp data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.course.PB_RewardBoxRsp data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_RewardBoxAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_RewardBoxAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_RewardBoxAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.course.PB_RewardBoxRsp();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.course.PB_RewardBoxRsp();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.course.PB_RewardBoxRsp();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_RewardBoxRsp : pb::IMessage<PB_RewardBoxRsp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_RewardBoxRsp> _parser = new pb::MessageParser<PB_RewardBoxRsp>(() => new PB_RewardBoxRsp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_RewardBoxRsp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RewardBoxRsp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RewardBoxRsp(PB_RewardBoxRsp other) : this() {
      economic_info_ = other.economic_info_ != null ? other.economic_info_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RewardBoxRsp Clone() {
      return new PB_RewardBoxRsp(this);
    }

    /// <summary>Field number for the "economic_info" field.</summary>
    public const int economic_infoFieldNumber = 1;
    private global::Msg.basic.PB_EconomicInfo economic_info_;
    /// <summary>
    /// 奖励信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_EconomicInfo economic_info {
      get { return economic_info_; }
      set {
        economic_info_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_RewardBoxRsp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_RewardBoxRsp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(economic_info, other.economic_info)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (economic_info_ != null) hash ^= economic_info.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (economic_info_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(economic_info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (economic_info_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(economic_info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (economic_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(economic_info);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_RewardBoxRsp other) {
      if (other == null) {
        return;
      }
      if (other.economic_info_ != null) {
        if (economic_info_ == null) {
          economic_info = new global::Msg.basic.PB_EconomicInfo();
        }
        economic_info.MergeFrom(other.economic_info);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (economic_info_ == null) {
              economic_info = new global::Msg.basic.PB_EconomicInfo();
            }
            input.ReadMessage(economic_info);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (economic_info_ == null) {
              economic_info = new global::Msg.basic.PB_EconomicInfo();
            }
            input.ReadMessage(economic_info);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_ChangeCourseProgressReq : pb::IMessage<SS_ChangeCourseProgressReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_ChangeCourseProgressReq> _parser = new pb::MessageParser<SS_ChangeCourseProgressReq>(() => new SS_ChangeCourseProgressReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_ChangeCourseProgressReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ChangeCourseProgressReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ChangeCourseProgressReq(SS_ChangeCourseProgressReq other) : this() {
      course_params_ = other.course_params_ != null ? other.course_params_.Clone() : null;
      session_record_id_ = other.session_record_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ChangeCourseProgressReq Clone() {
      return new SS_ChangeCourseProgressReq(this);
    }

    /// <summary>Field number for the "course_params" field.</summary>
    public const int course_paramsFieldNumber = 1;
    private global::Msg.basic.PB_CourseLearnPathParams course_params_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_CourseLearnPathParams course_params {
      get { return course_params_; }
      set {
        course_params_ = value;
      }
    }

    /// <summary>Field number for the "session_record_id" field.</summary>
    public const int session_record_idFieldNumber = 2;
    private long session_record_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long session_record_id {
      get { return session_record_id_; }
      set {
        session_record_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_ChangeCourseProgressReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_ChangeCourseProgressReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(course_params, other.course_params)) return false;
      if (session_record_id != other.session_record_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (course_params_ != null) hash ^= course_params.GetHashCode();
      if (session_record_id != 0L) hash ^= session_record_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (course_params_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(course_params);
      }
      if (session_record_id != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(session_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (course_params_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(course_params);
      }
      if (session_record_id != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(session_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (course_params_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(course_params);
      }
      if (session_record_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(session_record_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_ChangeCourseProgressReq other) {
      if (other == null) {
        return;
      }
      if (other.course_params_ != null) {
        if (course_params_ == null) {
          course_params = new global::Msg.basic.PB_CourseLearnPathParams();
        }
        course_params.MergeFrom(other.course_params);
      }
      if (other.session_record_id != 0L) {
        session_record_id = other.session_record_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (course_params_ == null) {
              course_params = new global::Msg.basic.PB_CourseLearnPathParams();
            }
            input.ReadMessage(course_params);
            break;
          }
          case 16: {
            session_record_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (course_params_ == null) {
              course_params = new global::Msg.basic.PB_CourseLearnPathParams();
            }
            input.ReadMessage(course_params);
            break;
          }
          case 16: {
            session_record_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_ChangeCourseProgressAck : pb::IMessage<SS_ChangeCourseProgressAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_ChangeCourseProgressAck> _parser = new pb::MessageParser<SS_ChangeCourseProgressAck>(() => new SS_ChangeCourseProgressAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_ChangeCourseProgressAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ChangeCourseProgressAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ChangeCourseProgressAck(SS_ChangeCourseProgressAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ChangeCourseProgressAck Clone() {
      return new SS_ChangeCourseProgressAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_ChangeCourseProgressAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_ChangeCourseProgressAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_ChangeCourseProgressAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_CreateCourseSessionRecordReq : pb::IMessage<SS_CreateCourseSessionRecordReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_CreateCourseSessionRecordReq> _parser = new pb::MessageParser<SS_CreateCourseSessionRecordReq>(() => new SS_CreateCourseSessionRecordReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_CreateCourseSessionRecordReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_CreateCourseSessionRecordReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_CreateCourseSessionRecordReq(SS_CreateCourseSessionRecordReq other) : this() {
      session_id_ = other.session_id_;
      session_record_id_ = other.session_record_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_CreateCourseSessionRecordReq Clone() {
      return new SS_CreateCourseSessionRecordReq(this);
    }

    /// <summary>Field number for the "session_id" field.</summary>
    public const int session_idFieldNumber = 1;
    private long session_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long session_id {
      get { return session_id_; }
      set {
        session_id_ = value;
      }
    }

    /// <summary>Field number for the "session_record_id" field.</summary>
    public const int session_record_idFieldNumber = 2;
    private long session_record_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long session_record_id {
      get { return session_record_id_; }
      set {
        session_record_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_CreateCourseSessionRecordReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_CreateCourseSessionRecordReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (session_id != other.session_id) return false;
      if (session_record_id != other.session_record_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (session_id != 0L) hash ^= session_id.GetHashCode();
      if (session_record_id != 0L) hash ^= session_record_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (session_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(session_id);
      }
      if (session_record_id != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(session_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (session_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(session_id);
      }
      if (session_record_id != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(session_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (session_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(session_id);
      }
      if (session_record_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(session_record_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_CreateCourseSessionRecordReq other) {
      if (other == null) {
        return;
      }
      if (other.session_id != 0L) {
        session_id = other.session_id;
      }
      if (other.session_record_id != 0L) {
        session_record_id = other.session_record_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            session_id = input.ReadInt64();
            break;
          }
          case 16: {
            session_record_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            session_id = input.ReadInt64();
            break;
          }
          case 16: {
            session_record_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_CreateCourseSessionRecordAck : pb::IMessage<SS_CreateCourseSessionRecordAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_CreateCourseSessionRecordAck> _parser = new pb::MessageParser<SS_CreateCourseSessionRecordAck>(() => new SS_CreateCourseSessionRecordAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_CreateCourseSessionRecordAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_CreateCourseSessionRecordAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_CreateCourseSessionRecordAck(SS_CreateCourseSessionRecordAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_CreateCourseSessionRecordAck Clone() {
      return new SS_CreateCourseSessionRecordAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_CreateCourseSessionRecordAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_CreateCourseSessionRecordAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_CreateCourseSessionRecordAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_ExitCourseSessionReq : pb::IMessage<SS_ExitCourseSessionReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_ExitCourseSessionReq> _parser = new pb::MessageParser<SS_ExitCourseSessionReq>(() => new SS_ExitCourseSessionReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_ExitCourseSessionReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ExitCourseSessionReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ExitCourseSessionReq(SS_ExitCourseSessionReq other) : this() {
      session_record_id_ = other.session_record_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ExitCourseSessionReq Clone() {
      return new SS_ExitCourseSessionReq(this);
    }

    /// <summary>Field number for the "session_record_id" field.</summary>
    public const int session_record_idFieldNumber = 1;
    private long session_record_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long session_record_id {
      get { return session_record_id_; }
      set {
        session_record_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_ExitCourseSessionReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_ExitCourseSessionReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (session_record_id != other.session_record_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (session_record_id != 0L) hash ^= session_record_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (session_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(session_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (session_record_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(session_record_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (session_record_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(session_record_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_ExitCourseSessionReq other) {
      if (other == null) {
        return;
      }
      if (other.session_record_id != 0L) {
        session_record_id = other.session_record_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            session_record_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            session_record_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_ExitCourseSessionAck : pb::IMessage<SS_ExitCourseSessionAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_ExitCourseSessionAck> _parser = new pb::MessageParser<SS_ExitCourseSessionAck>(() => new SS_ExitCourseSessionAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_ExitCourseSessionAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ExitCourseSessionAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ExitCourseSessionAck(SS_ExitCourseSessionAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_ExitCourseSessionAck Clone() {
      return new SS_ExitCourseSessionAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_ExitCourseSessionAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_ExitCourseSessionAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_ExitCourseSessionAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SetUserProgressReq : pb::IMessage<SS_SetUserProgressReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SetUserProgressReq> _parser = new pb::MessageParser<SS_SetUserProgressReq>(() => new SS_SetUserProgressReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SetUserProgressReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserProgressReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserProgressReq(SS_SetUserProgressReq other) : this() {
      user_level_ = other.user_level_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserProgressReq Clone() {
      return new SS_SetUserProgressReq(this);
    }

    /// <summary>Field number for the "user_level" field.</summary>
    public const int user_levelFieldNumber = 1;
    private string user_level_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string user_level {
      get { return user_level_; }
      set {
        user_level_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SetUserProgressReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SetUserProgressReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_level != other.user_level) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_level.Length != 0) hash ^= user_level.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_level.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(user_level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_level.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(user_level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_level.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(user_level);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SetUserProgressReq other) {
      if (other == null) {
        return;
      }
      if (other.user_level.Length != 0) {
        user_level = other.user_level;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            user_level = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            user_level = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SetUserProgressAck : pb::IMessage<SS_SetUserProgressAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SetUserProgressAck> _parser = new pb::MessageParser<SS_SetUserProgressAck>(() => new SS_SetUserProgressAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SetUserProgressAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[23]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserProgressAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserProgressAck(SS_SetUserProgressAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserProgressAck Clone() {
      return new SS_SetUserProgressAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.course.SS_SetUserProgressData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.course.SS_SetUserProgressData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SetUserProgressAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SetUserProgressAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SetUserProgressAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.course.SS_SetUserProgressData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.course.SS_SetUserProgressData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.course.SS_SetUserProgressData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SetUserProgressData : pb::IMessage<SS_SetUserProgressData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SetUserProgressData> _parser = new pb::MessageParser<SS_SetUserProgressData>(() => new SS_SetUserProgressData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SetUserProgressData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.course.LearnPathReflection.Descriptor.MessageTypes[24]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserProgressData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserProgressData(SS_SetUserProgressData other) : this() {
      section_index_ = other.section_index_;
      unit_index_ = other.unit_index_;
      level_index_ = other.level_index_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserProgressData Clone() {
      return new SS_SetUserProgressData(this);
    }

    /// <summary>Field number for the "section_index" field.</summary>
    public const int section_indexFieldNumber = 1;
    private int section_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int section_index {
      get { return section_index_; }
      set {
        section_index_ = value;
      }
    }

    /// <summary>Field number for the "unit_index" field.</summary>
    public const int unit_indexFieldNumber = 2;
    private int unit_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int unit_index {
      get { return unit_index_; }
      set {
        unit_index_ = value;
      }
    }

    /// <summary>Field number for the "level_index" field.</summary>
    public const int level_indexFieldNumber = 3;
    private int level_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int level_index {
      get { return level_index_; }
      set {
        level_index_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SetUserProgressData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SetUserProgressData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (section_index != other.section_index) return false;
      if (unit_index != other.unit_index) return false;
      if (level_index != other.level_index) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (section_index != 0) hash ^= section_index.GetHashCode();
      if (unit_index != 0) hash ^= unit_index.GetHashCode();
      if (level_index != 0) hash ^= level_index.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (section_index != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(section_index);
      }
      if (unit_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(unit_index);
      }
      if (level_index != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(level_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (section_index != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(section_index);
      }
      if (unit_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(unit_index);
      }
      if (level_index != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(level_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (section_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(section_index);
      }
      if (unit_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(unit_index);
      }
      if (level_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(level_index);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SetUserProgressData other) {
      if (other == null) {
        return;
      }
      if (other.section_index != 0) {
        section_index = other.section_index;
      }
      if (other.unit_index != 0) {
        unit_index = other.unit_index;
      }
      if (other.level_index != 0) {
        level_index = other.level_index;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            section_index = input.ReadInt32();
            break;
          }
          case 16: {
            unit_index = input.ReadInt32();
            break;
          }
          case 24: {
            level_index = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            section_index = input.ReadInt32();
            break;
          }
          case 16: {
            unit_index = input.ReadInt32();
            break;
          }
          case 24: {
            level_index = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

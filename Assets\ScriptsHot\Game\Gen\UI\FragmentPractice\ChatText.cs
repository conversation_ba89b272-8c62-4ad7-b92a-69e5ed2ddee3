/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ChatText : GComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "ChatText";
        public static string url => "ui://cmoz5osjw1ks4e";

        public GGraph bg;
        public GLoader nameBg;
        public GTextField tfName;
        public GLoader audioBg;
        public GComponent content;
        public BtnDialogueAudio btnAudio;
        public GTextField tfDebugAudio;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ChatText));
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            bg = GetChildAt(0) as GGraph;
            nameBg = GetChildAt(1) as GLoader;
            tfName = GetChildAt(2) as GTextField;
            audioBg = GetChildAt(4) as GLoader;
            content = GetChildAt(6) as GComponent;
            btnAudio = GetChildAt(7) as BtnDialogueAudio;
            tfDebugAudio = GetChildAt(8) as GTextField;
        }
        public override void Dispose()
        {
            bg = null;
            nameBg = null;
            tfName = null;
            audioBg = null;
            content = null;
            btnAudio = null;
            tfDebugAudio = null;

            base.Dispose();
        }
    }
}
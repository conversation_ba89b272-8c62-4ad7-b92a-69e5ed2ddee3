using DG.Tweening;
using UnityEngine;

public class VibrationManager
{
    public enum VibrationType
    {
        //通用
        Vibrate,
        Pop,
        Peek,
        Nope,
        
        //ios only
        Success,
        Error,
        Warning,
        Light,
        Medium ,
        Heavy,
        Rigid,
        Soft,
        SelectionChanged,
        Transient,
        Continuous,
        
        
        //android only
        
        
        //
        NUM
    }

    
    public static VibrationManager Ins
    {
        get
        {
            if (ins == null)
            {
                ins = new VibrationManager();
            }

            return ins;
        }
    }
    private static VibrationManager ins;

    public void Init()
    {
        Vibration.Init();
    }

    private int lastVibrateFrame = -1; // 记录上一次调用 Vibrate 的帧号

    public void Vibrate(VibrationType vibrationType)
    {
        //Debug.LogError(lastVibrateFrame);
        // 检查是否在同一帧中多次调用
        if (Time.frameCount == lastVibrateFrame)
        {
            return; // 如果是同一帧，直接返回
        }
        lastVibrateFrame = Time.frameCount; // 记录当前帧号

        if(Application.isEditor)
        {
            Debug.Log("Vibrating: " + vibrationType);
            return;
        }
        
        switch(vibrationType)
        {
            case VibrationType.Vibrate:
                Vibrate();
                break;
            case VibrationType.Pop:
                VibratePop();
                break;
            case VibrationType.Peek:
                VibratePeek();
                break;
            case VibrationType.Nope:
                VibrateNope();
                break;
            case VibrationType.Transient:
                TransientHaptic();
                break;
            case VibrationType.Continuous:
                ContinuousHaptic();
                break;
            
#if UNITY_IOS && !UNITY_EDITOR
        case VibrationType.Light:
            Vibration.VibrateIOS(ImpactFeedbackStyle.Light);
            break;
        case VibrationType.Medium:
            Vibration.VibrateIOS(ImpactFeedbackStyle.Medium);
            break;
        case VibrationType.Heavy:
            Vibration.VibrateIOS(ImpactFeedbackStyle.Heavy);
            break;
        case VibrationType.Rigid:
            Vibration.VibrateIOS(ImpactFeedbackStyle.Rigid);
            break;
        case VibrationType.Soft:
            Vibration.VibrateIOS(ImpactFeedbackStyle.Soft);
            break;
        case VibrationType.Error:
            Vibration.VibrateIOS(NotificationFeedbackStyle.Error);
            break;
        case VibrationType.Success:
            Vibration.VibrateIOS(NotificationFeedbackStyle.Success);
            break;
        case VibrationType.Warning:
            Vibration.VibrateIOS(NotificationFeedbackStyle.Warning);
            break;
        case VibrationType.SelectionChanged:
            Vibration.VibrateIOS_SelectionChanged();
            break;
#endif

#if UNITY_ANDROID && !UNITY_EDITOR
        case VibrationType.Light:
            Vibration.VibrateAndroidModify(new long[] {0, 30}, new int[] {0, 98});
            break;
        case VibrationType.Medium:
            Vibration.VibrateAndroid(40);
            break;
        case VibrationType.Heavy:
            Vibration.VibrateAndroid(80);
            break;
        case VibrationType.Rigid:
            Vibration.VibrateAndroid(new long[] { 0, 60 }, -1);
            break;
        case VibrationType.Soft:
            Vibration.VibrateAndroidModify(new long[] {0, 30}, new int[] {0, 128});
            break;
        case VibrationType.Error:
            Vibration.VibrateAndroidModify(new long[] { 0, 40, 30, 40, 30, 40 }, new int[] {0, 128, 0, 168, 0, 168});
            break;
        case VibrationType.Success:
            Vibration.VibrateAndroidModify(new long[] { 0, 40, 30, 40 }, new int[] {0, 228, 0, 228});
            break;
        case VibrationType.Warning:
            Vibration.VibrateAndroidModify(new long[] { 0, 100, 30, 50 }, new int[] {0, 168, 0, 168});
            break;
        case VibrationType.SelectionChanged:
            Vibration.VibrateAndroidModify(new long[] {0, 30}, new int[] {0, 68});
            break;
#endif

            
        }
    }

    public void Vibrate(float intensity, float sharpness,float time)
    {
        HapticManager.PlayContinuousHaptic(intensity, sharpness, time);
    }


    //Common vibrations
    private void Vibrate()
    {
        //~400ms vibration
        Vibration.Vibrate();
    }

    private void VibratePop()
    {
        //Weak boom
        Vibration.VibratePop();
    }

    private void VibratePeek()
    {
        //Strong boom
        Vibration.VibratePeek();
    }

    private void VibrateNope()
    {
        //3 small vibrations (weak booms)
        Vibration.VibrateNope();
    }

    //iOS only
    private void HapticLight()
    {
#if UNITY_IOS
        Vibration.VibrateIOS(ImpactFeedbackStyle.Light);
#endif
    }

    private void HapticMedium()
    {
#if UNITY_IOS
        Vibration.VibrateIOS(ImpactFeedbackStyle.Medium);
#endif
    }

    private void HapticHeavy()
    {
#if UNITY_IOS
        Vibration.VibrateIOS(ImpactFeedbackStyle.Heavy);
#endif
    }

    private void HapticRigid()
    {
#if UNITY_IOS
        Vibration.VibrateIOS(ImpactFeedbackStyle.Rigid);
#endif
    }

    private void HapticSoft()
    {
#if UNITY_IOS
        Vibration.VibrateIOS(ImpactFeedbackStyle.Soft);
#endif
    }

    private void NotificationError()
    {
#if UNITY_IOS
        Vibration.VibrateIOS(NotificationFeedbackStyle.Error);
#endif
    }

    private void NotificationSuccess()
    {
#if UNITY_IOS
        Vibration.VibrateIOS(NotificationFeedbackStyle.Success);
#endif
    }

    private void NotificationWarning()
    {
#if UNITY_IOS
        Vibration.VibrateIOS(NotificationFeedbackStyle.Warning);
#endif
    }

    private void SelectionChanged()
    {
#if UNITY_IOS
        Vibration.VibrateIOS_SelectionChanged();
#endif
    }

    public void TransientHaptic(float intensity = 0.5f, float sharpness = 0.5f)
    {
#if UNITY_IOS
        HapticManager.PlayTransientHaptic(intensity, sharpness);
#elif UNITY_ANDROID
        Vibration.PlayTransientHapticAndroid(intensity, sharpness);
#endif

    }

    private void ContinuousHaptic()
    {
#if UNITY_IOS
        HapticManager.PlayContinuousHaptic(0.5f, 0.5f, 1.0f);
#elif UNITY_ANDROID
        Vibration.PlayContinuousHapticAndroid(0.5f, 0.5f, 1.0f);
#endif

    }
}


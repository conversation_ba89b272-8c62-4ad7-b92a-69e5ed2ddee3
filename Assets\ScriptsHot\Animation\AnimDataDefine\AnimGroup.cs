﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace AnimationSystem
{
    // 动作组
    public class AnimGroup
    {
        private List<AnimInfo> animInfos = new List<AnimInfo>();
        public List<HeadCommand> headCommands = new List<HeadCommand>();

        public string bodyGroupID;

        public Vector2 Duration { get; private set; } // 支持的最短最长播放时间

        public void Init()
        {
            if (this.Duration == Vector2.zero)
            {
                GetDuration();
            }
        }

        // 添加动画信息
        public void AddAnimInfo(AnimInfo animInfo)
        {
            animInfos.Add(animInfo);
        }

        // 获取动画信息列表
        public List<AnimInfo> GetAnimInfos()
        {
            return animInfos;
        }

        private void GetDuration()
        {
            // 计算该动作组支持的最短和最长播放时间
            float minDuration = 0;
            float maxDuration = 0;

            // 遍历每个动画，分别计算其对最短和最长时长的贡献
            for (int i = 0; i < animInfos.Count; i++)
            {
                var animInfo = animInfos[i];
                if (!animInfo.clip)
                {
                    VFDebug.LogError($"没有拿到动画资源！后续大概率报错！查配表/查资源目录！ {this.bodyGroupID}");
                    animInfos.RemoveAt(i);
                    i--;
                    continue;
                }

                float originalClipLength = animInfo.clip.length;

                // 计算当前动画在不同条件下的最短和最长时长
                float clipMinDuration = originalClipLength;
                float clipMaxDuration = originalClipLength;

                // 1. 考虑变速对时长的影响
                if (animInfo.canChangeSpeed)
                {
                    // 最短时长使用最快速度
                    clipMinDuration = originalClipLength / animInfo.speedMultiplierRange.y;
                    // 最长时长使用最慢速度
                    clipMaxDuration = originalClipLength / animInfo.speedMultiplierRange.x;
                }

                // 2. 考虑循环对时长的影响
                if (animInfo.canLoop)
                {
                    float originalLength = clipMaxDuration;
                    float blendReduceTime = animInfo.loopBlendTime;

                    // 如果融合减少的时间大于等于单次循环增加的时间，则循环无意义
                    if (blendReduceTime >= originalLength)
                    {
                        // 直接将canLoop设为false，因为循环实际上无法增加时间
                        animInfo.canLoop = false;
                    }
                    else
                    {
                        // 最短时长保持不变（不循环）
                        // 最长时长考虑最大循环次数和融合时间
                        clipMaxDuration = originalLength * animInfo.loopMultiplierRange.y - blendReduceTime;
                    }
                }

                // 3. 考虑混合对时长的影响
                // 检查当前动画是否可以混合到下一个动画
                bool canBlendToNext = i < animInfos.Count - 1;
                if (canBlendToNext && animInfo.canBlend)
                {
                    // 获取下一个动画的信息
                    var nextAnimInfo = animInfos[i + 1];

                    // 当前动画可以提前结束的时间比例
                    float currentEndBlendRatio = animInfo.blendRange.y;

                    // 下一个动画可以提前开始的时间比例（如果下一个动画支持混合）
                    float nextStartBlendRatio = 0;
                    if (nextAnimInfo.canBlend)
                    {
                        nextStartBlendRatio = nextAnimInfo.blendRange.x;
                    }

                    // 计算混合可以减少的总时间
                    float currentEndBlendTime = clipMinDuration * currentEndBlendRatio;
                    float nextStartBlendTime = 0;

                    // 计算下一个动画的最短时长（考虑变速）
                    float nextClipMinDuration = nextAnimInfo.clip.length;
                    if (nextAnimInfo.canChangeSpeed)
                    {
                        nextClipMinDuration = nextClipMinDuration / nextAnimInfo.speedMultiplierRange.y;
                    }

                    nextStartBlendTime = nextClipMinDuration * nextStartBlendRatio;

                    // 取两者中的较小值作为实际可以减少的时间
                    float totalBlendTime = Mathf.Min(currentEndBlendTime, nextStartBlendTime);

                    // 减少当前动画的最短时长
                    clipMinDuration -= totalBlendTime;
                }
                else if (animInfo.canBlend)
                {
                    clipMinDuration -= clipMinDuration * (animInfo.blendRange.y);
                }

                // 累加到总时长
                minDuration += clipMinDuration;
                maxDuration += clipMaxDuration;
            }

            minDuration = Mathf.Max(minDuration, 0.001f);
            maxDuration = Mathf.Max(maxDuration, 0.001f);
            Duration = new Vector2(minDuration, maxDuration);
        }

        public AnimGroupPlayData GetAnimStrategy(float targetDuration)
        {
            AnimGroupPlayData playData = new AnimGroupPlayData();
            playData.animGroup = this;

            // 检查时长是否在可接受范围内
            if (targetDuration < Duration.x)
            {
                // 时长过短，记录日志并返回空数据，将在外部处理为切换到idle
                //    Debug.LogWarning($"目标时长 {targetDuration} 秒小于动作组支持的最小时长 {Duration.x} 秒，无法生成播放策略");
                return new AnimGroupPlayData();
            }

            // 如果时长过长，使用支持的最长时间
            float effectiveDuration = targetDuration;
            if (targetDuration > Duration.y)
            {
                //      Debug.LogWarning($"目标时长 {targetDuration} 秒超过动作组支持的最大时长 {Duration.y} 秒，将使用最大支持时长");
                effectiveDuration = Duration.y;
            }

            // 生成播放策略
            GeneratePlayStrategy(playData, effectiveDuration);

            return playData;
        }

        private void GeneratePlayStrategy(AnimGroupPlayData playData, float targetDuration)
        {
            // 如果没有动画，直接返回
            if (animInfos.Count == 0)
            {
                return;
            }

            // 计算原始总时长
            float originalTotalDuration = 0;
            foreach (var info in animInfos)
            {
                originalTotalDuration += info.clip.length;
            }

            // 计算需要调整的时间差
            float timeDifference = targetDuration - originalTotalDuration;

            // 如果时间差很小，不需要调整
            if (Mathf.Abs(timeDifference) < 0.01f)
            {
                // 直接使用原始时长
                for (int i = 0; i < animInfos.Count; i++)
                {
                    AnimPlayData data = new AnimPlayData();
                    data.clip = animInfos[i].clip;
                    data.nextAnimIndex = i < animInfos.Count - 1 ? i + 1 : -1;
                    data.speed = 1.0f;
                    data.willBlendToNext = false;
                    data.enableFootIK = animInfos[i].enableFootIK; // 传递FootIK设置
                    playData.animPlayDatas.Add(data);
                }

                return;
            }

            // 创建初始的动画播放数据
            List<AnimPlayData> initialPlayDatas = new List<AnimPlayData>();
            for (int i = 0; i < animInfos.Count; i++)
            {
                AnimPlayData data = new AnimPlayData();
                data.clip = animInfos[i].clip;
                data.nextAnimIndex = i < animInfos.Count - 1 ? i + 1 : -1;
                data.speed = 1.0f;
                data.willBlendToNext = false;
                data.enableFootIK = animInfos[i].enableFootIK; // 传递FootIK设置
                initialPlayDatas.Add(data);
            }

            // 需要调整的剩余时间
            float remainingTimeToAdjust = timeDifference;

            // 第一步：如果需要缩短时间，尝试使用blend
            if (timeDifference < 0)
            {
                // 应用blend调整，返回剩余需要调整的时间
                remainingTimeToAdjust = ApplyBlendAdjustment(initialPlayDatas, timeDifference);
            }
            // 第一步（替代）：如果需要延长时间，尝试使用循环
            else if (timeDifference > 0)
            {
                // 应用循环调整，返回剩余需要调整的时间
                remainingTimeToAdjust = ApplyLoopAdjustment(initialPlayDatas, timeDifference);
            }

            // 第二步：使用变速调整剩余的时间差
            if (Mathf.Abs(remainingTimeToAdjust) > 0.01f)
            {
                ApplySpeedAdjustment(initialPlayDatas, remainingTimeToAdjust);
            }

            // 将调整后的数据添加到结果中
            foreach (var data in initialPlayDatas)
            {
                playData.animPlayDatas.Add(data);
            }
        }

        // 应用blend调整，返回剩余需要调整的时间
        private float ApplyBlendAdjustment(List<AnimPlayData> playDatas, float timeToAdjust)
        {
            if (timeToAdjust >= 0)
            {
                return timeToAdjust; // 不需要缩短时间，无需blend
            }

            float timeToReduce = -timeToAdjust; // 转为正数，表示需要减少的时间

            // 收集所有可以blend的动画及其可减少的最大时间
            List<int> blendableIndices = new List<int>();
            List<float> maxBlendTimes = new List<float>();
            float totalMaxBlendTime = 0;

            //最后一个动画依然考虑blend
            for (int i = 0; i < animInfos.Count; i++)
            {
                var infoCurr = animInfos[i];

                if (infoCurr.canBlend)
                {
                    if (i != animInfos.Count - 1)
                    {
                        var infoNxt = animInfos[i + 1];
                        // 计算最大可以blend的时间（基于当前动画的blend范围）
                        float maxBlendTime = Math.Min(infoCurr.clip.length * infoCurr.blendRange.y,infoNxt.clip.length * infoNxt.blendRange.x);
                        if (maxBlendTime > 0)
                        {
                            blendableIndices.Add(i);
                            maxBlendTimes.Add(maxBlendTime);
                            totalMaxBlendTime += maxBlendTime;
                        }
                    }
                    else
                    {
                        // 计算最大可以blend的时间（基于当前动画的blend范围）
                        float maxBlendTime = infoCurr.clip.length * infoCurr.blendRange.y;
                        if (maxBlendTime > 0)
                        {
                            blendableIndices.Add(i);
                            maxBlendTimes.Add(maxBlendTime);
                            totalMaxBlendTime += maxBlendTime;
                        }
                    }
                }
            }

            // 如果没有可以blend的动画，返回原始需要调整的时间
            if (blendableIndices.Count == 0)
            {
                return timeToAdjust;
            }

            // 计算实际可以通过blend减少的时间
            float actualTimeToReduce = Mathf.Min(timeToReduce, totalMaxBlendTime);

            // 计算每个可blend动画需要减少的时间（按比例分配）
            List<float> blendTimes = new List<float>();
            for (int i = 0; i < maxBlendTimes.Count; i++)
            {
                float blendTime = (maxBlendTimes[i] / totalMaxBlendTime) * actualTimeToReduce;
                blendTimes.Add(blendTime);
            }

            // 应用blend设置
            for (int i = 0; i < blendableIndices.Count; i++)
            {
                int index = blendableIndices[i];
                AnimInfo info = animInfos[index];
                AnimPlayData data = playDatas[index];

                float blendTime = blendTimes[i];
                float clipLength = info.clip.length;

                // 设置blend参数
                data.willBlendToNext = true;
                data.blendTime = blendTime;

                // 计算在动画的哪个位置开始blend（从后向前）
                data.blendToNext = 1.0f - (blendTime / clipLength);

                // // 确保blend位置在允许的范围内
                // data.blendToNext = Mathf.Clamp(data.blendToNext,
                //     1.0f - info.blendRange.y,
                //     1.0f - info.blendRange.x);
            }

            // 返回剩余需要调整的时间
            return timeToAdjust + actualTimeToReduce;
        }

        // 应用循环调整，返回剩余需要调整的时间
        private float ApplyLoopAdjustment(List<AnimPlayData> playDatas, float timeToAdjust)
        {
            if (timeToAdjust <= 0)
            {
                return timeToAdjust; // 不需要延长时间，无需循环
            }

            // 收集所有可以循环的动画及其可增加的最大时间
            List<int> loopableIndices = new List<int>();
            List<float> maxLoopTimes = new List<float>();
            float totalMaxLoopTime = 0;

            for (int i = 0; i < animInfos.Count; i++)
            {
                AnimInfo info = animInfos[i];

                if (info.canLoop) // 由于在GetDuration中已经处理过，这里的canLoop为true说明循环是有效的
                {
                    float clipLength = info.clip.length;
                    // 计算最大可以循环的时间，需要考虑融合减少的时间
                    float maxLoopTime = (clipLength * (info.loopMultiplierRange.y - 1.0f)) - info.loopBlendTime;

                    loopableIndices.Add(i);
                    maxLoopTimes.Add(maxLoopTime);
                    totalMaxLoopTime += maxLoopTime;
                }
            }

            // 如果没有可以循环的动画，返回原始需要调整的时间
            if (loopableIndices.Count == 0)
            {
                return timeToAdjust;
            }

            // 计算实际可以通过循环增加的时间
            float actualTimeToAdd = Mathf.Min(timeToAdjust, totalMaxLoopTime);

            // 按比例分配时间到每个可循环动画
            for (int i = 0; i < loopableIndices.Count; i++)
            {
                int index = loopableIndices[i];
                AnimInfo info = animInfos[index];
                AnimPlayData data = playDatas[index];

                // 按比例分配需要增加的时间
                float targetAddTime = (maxLoopTimes[i] / totalMaxLoopTime) * actualTimeToAdd;

                // 计算需要的循环次数：(目标增加时间 + 融合时间) / 原始时长
                float clipLength = info.clip.length;
                float neededLoopCount = (targetAddTime + info.loopBlendTime) / clipLength;

                // 确保不超过配置的最大循环次数
                neededLoopCount = Mathf.Min(neededLoopCount, info.loopMultiplierRange.y - 1.0f);

                // 设置循环和融合参数
                data.loopCount = neededLoopCount;
                if (neededLoopCount > 0)
                {
                    data.willBlendToNext = true;
                    data.blendTime = info.loopBlendTime;
                    // 计算融合开始的时间点
                    float totalPlayTime = clipLength * (1 + neededLoopCount);
                    data.blendToNext = (totalPlayTime - info.loopBlendTime) / clipLength;
                }

                // 计算实际增加的时间
                float actualAddedTime = (clipLength * neededLoopCount) - info.loopBlendTime;
                timeToAdjust -= actualAddedTime;
            }

            return timeToAdjust;
        }

        // 应用速度调整
        private void ApplySpeedAdjustment(List<AnimPlayData> playDatas, float timeToAdjust)
        {
            // 1. 收集可变速动画信息
            List<int> speedableIndices = new List<int>();
            List<float> originalDurations = new List<float>();
            List<Vector2> speedRanges = new List<Vector2>();
            float totalFixedDuration = 0;

            for (int i = 0; i < animInfos.Count; i++)
            {
                AnimInfo info = animInfos[i];
                float clipLength = info.clip.length;

                // 考虑blend对时长的影响
                if (playDatas[i].willBlendToNext)
                {
                    clipLength *= playDatas[i].blendToNext;
                    clipLength += playDatas[i].blendTime;
                }

                if (info.canChangeSpeed)
                {
                    speedableIndices.Add(i);
                    originalDurations.Add(clipLength);
                    speedRanges.Add(info.speedMultiplierRange);
                }
                else
                {
                    totalFixedDuration += clipLength;
                }
            }

            // 2. 计算每个clip最大可调节时间
            List<float> maxAdjustList = new List<float>();
            float totalMaxAdjust = 0;
            bool isExtend = timeToAdjust > 0;
            for (int i = 0; i < speedableIndices.Count; i++)
            {
                float orig = originalDurations[i];
                Vector2 range = speedRanges[i];
                float limitSpeed = isExtend ? range.x : range.y;
                float limitDuration = orig / limitSpeed;
                float maxAdjust = limitDuration - orig;
                maxAdjustList.Add(maxAdjust);
                totalMaxAdjust += Mathf.Abs(maxAdjust); // 用绝对值，方便后续权重分配
            }

            float needAdjust = timeToAdjust;
            // 3. 极限情况：需调整 >= 总可调节
            if ((isExtend && needAdjust >= totalMaxAdjust) || (!isExtend && -needAdjust >= totalMaxAdjust))
            {
                for (int i = 0; i < speedableIndices.Count; i++)
                {
                    int idx = speedableIndices[i];
                    playDatas[idx].speed = isExtend ? speedRanges[i].x : speedRanges[i].y;
                }

                float remain = isExtend ? (needAdjust - totalMaxAdjust) : (needAdjust + totalMaxAdjust);
                if (Mathf.Abs(remain) > 0.01f)
                {
                    VFDebug.LogWarning($"[AnimGroup] 变速已到极限，仍有未调节时间: {remain:F3} 秒");
                }

                return;
            }

            // 4. 权重分配
            for (int i = 0; i < speedableIndices.Count; i++)
            {
                int idx = speedableIndices[i];
                float orig = originalDurations[i];
                float maxAdjust = maxAdjustList[i];
                float weight = Mathf.Abs(maxAdjust) / totalMaxAdjust;
                float thisAdjust = needAdjust * weight;
                float newDuration = orig + thisAdjust;
                float speed = orig / newDuration;
                // Clamp到区间
                speed = Mathf.Clamp(speed, speedRanges[i].x, speedRanges[i].y);
                playDatas[idx].speed = speed;
            }
        }
    }
}
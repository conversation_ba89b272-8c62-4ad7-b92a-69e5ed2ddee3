using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Threading.Tasks;

using Msg.explore;

public enum VoiceChatRunningStatus
{
    UnInit = 0,              // 未初始化
    Idle = 1,                // 空闲
    PreMatch = 2,            // 预匹配，常规文档里的Matching的第1阶段
    PreMatchCancelling = 3,            // 预匹配，常规文档里的Matching的第1阶段
    PreMatchConfirming = 4,  // 很短的ack部分，常规文档里的Matching的第2阶段
    ChattingSelfJoin = 5,    // 仅自己加入
    ChattingBothJoin = 6,     // 双方都加入
    Disposing = 7           // 声网引擎正在销毁过程中的不可用时段 
}

//负责 根据整体匹配撮合状态 来wrap调用 声网的RtcEngine相关接口
namespace ScriptsHot.Game.Modules.AgoraRtc
{

    public class VoiceChatManager : MonoSingleton<VoiceChatManager>
    {
        public VoiceChatRunningStatus Status { get; private set; } = VoiceChatRunningStatus.UnInit;

        private RtcEngineManager engine = RtcEngineManager.Instance;
        internal ExploreNetStrategy CurNetStrategy;
        internal VoiceChatDialogTextController textCtrl = new VoiceChatDialogTextController();
        internal VoiceChatDialogTextModel textModel = new VoiceChatDialogTextModel();

        private long preMatchRecordID = -1;//

        private string cancelActionTimerKey;
        private int defaultClientTimerDelay= 700;//ms

        private long selfUid;
        private long partnerUid;

        #region MonoSingleton 基础行为
        protected override async UniTask OnInit()
        {
            await base.OnInit();

            textCtrl.Init(textModel);
            textCtrl.InitRegister();//双向流的下行监听

            //下行的核心消息
            MsgManager.instance.RegisterCallBack<SC_MatchingDown_MatchStatus>(this.OnRecvMatchStatus);
            MsgManager.instance.RegisterCallBack<SC_MatchingDown_MatchedResult>(this.OnRecvMatchResult);
            MsgManager.instance.RegisterCallBack<SC_MatchingDown_MatchFailed>(this.OnRecvMatchFailed);

            Notifier.instance.RegisterNotification(NotifyConsts.InitMultiTabFramework, this.OnInitMultiTabFramework);
        }
    

        #region 下行消息处理
        private void OnRecvMatchStatus(SC_MatchingDown_MatchStatus msg) {
            if (preMatchRecordID <= 0 && msg.match_record_id > 0)
            {
                preMatchRecordID = msg.match_record_id;
            }
            else if(preMatchRecordID!= msg.match_record_id && msg.match_record_id > 0)
            {
                VFDebug.LogError("OnRecvMatchStatus MatchStatus出现前后不一致，preMatchRecordID=" + preMatchRecordID+"，msgRecID="+ msg.match_record_id);

            }
            VFDebug.Log($"OnRecvMatchStatus msgStatus={msg.downMatchStatus.ToString()}  selfStatus ={this.Status.ToString()}");

            //prematch启动后
            switch (msg.downMatchStatus) {
                case ENUM_MatchingStatus.EO_MatchingStatus_Waiting:
               
                    //case1 自己PreMatchConfirm过程中，另外一端一直不confirm导致超时回退
                    //case1 自己PreMatchConfirm过程中，另外一端一直不cancel导致 回退

                    if (this.Status == VoiceChatRunningStatus.PreMatchConfirming)
                    {
                        this.StepBackFromPreMatchConfirming();
                    }
                    else if (this.Status == VoiceChatRunningStatus.PreMatch)
                    {
                        VFDebug.Log("EO_MatchingStatus_Waiting ==> 继续保持PreMatch");
                    }
                    else {

                        VFDebug.LogError("在未定义的流程上遇到 EO_MatchingStatus_Waiting， status:"+ this.Status.ToString());
                    }
                    break;

                case ENUM_MatchingStatus.EO_MatchingStatus_PreMatchSuccess:

                    this.RecvPreMatchConfirm();
                    break;

                //结束类流程
                case ENUM_MatchingStatus.EO_MatchingStatus_MatchCanceled:
                case ENUM_MatchingStatus.EO_MatchingStatus_MatchFailed://发起找人的整体timeout，另外一方 
                    //对侧取消了
                    if (this.Status == VoiceChatRunningStatus.PreMatchCancelling
                        || this.Status == VoiceChatRunningStatus.PreMatchConfirming
                        || this.Status == VoiceChatRunningStatus.PreMatch)
                    {
                        DoCancellCompleteAction();
                    }
                    else if (this.Status == VoiceChatRunningStatus.Idle)
                    {
                        VFDebug.LogWarning(msg.downMatchStatus.ToString() + ",status=" + this.Status.ToString() + " ==> 已经变为Idle了，可能是超时自动强制切换的");
                    }
                    else
                    {
                        VFDebug.LogError(msg.downMatchStatus.ToString() + ",status=" + this.Status.ToString() + " ==> do nothing");
                    }
               
                    break;
                case ENUM_MatchingStatus.EO_MatchingStatus_MatchSuccess:
                    //这个结果会发么，貌似不需要处理
                    VFDebug.LogError(msg.downMatchStatus.ToString() + ",status=" + this.Status.ToString());
                    preMatchRecordID = -1;
                    break;
            }
        }

        //prematchconfirm后的二次成功确认
        private void OnRecvMatchResult(SC_MatchingDown_MatchedResult msg)
        {
            if (preMatchRecordID != msg.match_record_id)
            {
                VFDebug.LogError("OnRecvMatchResult MatchStatus出现前后不一致，preMatchRecordID=" + preMatchRecordID + "，msgRecID=" + msg.match_record_id);
                return;
            }
  
            VFDebug.Log($"OnRecvMatchSucc,uChatId:{msg.userchat_id} avatarID:{msg.avatarId} url:{msg.headPicUrl} nick:{msg.nickname}");

            this.selfUid = (long)msg.Agora_uid_self;
            this.partnerUid = (long)msg.Agora_uid_self;
            if (Status == VoiceChatRunningStatus.PreMatchConfirming)
            {
                this.JoinChannel(msg.Agora_token, msg.Agora_channel,msg.Agora_uid_self);
            }
            else
            {
                Debug.LogError("OnRecvMatchResult 时状态异常，status="+Status);
                this.CancelPreMatchConfirming();
            }


        }

        //prematchconfirm后的匹配失败/超时确认
        private void OnRecvMatchFailed(SC_MatchingDown_MatchFailed msg)
        {
           VFDebug.Log("OnRecvMatchFailed reason:"+ msg.reason);
           this.CancelPreMatchConfirming();
           
        }

        private void DoCancellCompleteAction() {
            this.Status = VoiceChatRunningStatus.Idle;
            preMatchRecordID = -1;

            Notifier.instance.SendNotification(NotifyConsts.VoiceChat_CancelPreMatchComplete);//通知视图改变
            

            if (!string.IsNullOrEmpty(this.cancelActionTimerKey)) {
                TimerManager.instance.UnRegisterTimer(this.cancelActionTimerKey);
                this.cancelActionTimerKey = string.Empty;
            }
            VFDebug.Log("Do Cancell Complete ");
        }
        #endregion


        #endregion MonoSingleton 基础行为

        #region 状态迁移 actions
        /* 状态迁移
        StartEngine ， UnInit -> Idle
        StartPreMatch，  Idle -> PreMatch
        RecvPreMatchConfirm，  PreMatch -> PreMatchConfirming         CancelPreMatch，   PreMatch -> Idle
        CancelPreMatchConfirming ，  PreMatchConfirming -> Idle         StepBackFromPreMatchConfirming,  PreMatchConfirming -> PreMatch

        JoinChannel ，   PreMatchConfirming -> ChattingSelfJoin
        RecvOtherJoinChannelMsg， ChattingSelfJoin-> ChattingBothJoin
        LeaveChannel ，ChattingSelfJoin  -> Idle
        LeaveChannel ，ChattingBothJoin -> Idle
        */

        //启动初始化
        private void OnInitMultiTabFramework(string name, object body)
        {
            //StartEngine();
        }

        private void StartEngine(Action completeCallBack)
        {
            if (Status == VoiceChatRunningStatus.UnInit)
            {
                engine.Initialize(AppConstExt.AgoraSDKAppId);
                var ctrl = ControllerManager.instance.GetController<ExploreController>(ModelConsts.Explore) as ExploreController;
                CurNetStrategy = ctrl.CurNetStrategy;

                Status = VoiceChatRunningStatus.Idle;
                preMatchRecordID = -1;
                if (completeCallBack != null)
                {
                    completeCallBack.Invoke();
                }
            }
            else
            {
                throw new InvalidOperationException("StartEngine: 状态错误");
            }
        }

        //成功时返true
        public bool StartPreMatch()
        {
            if (Status == VoiceChatRunningStatus.UnInit)
            {
                StartEngine( ()=>{
                    //回调时认为前置一定正常完成了到了 idle状态了
                    CurNetStrategy?.SendMatchingMsg(Msg.explore.PB_MatchingUpMsg.upBizMsgOneofCase.upFindPartnerReq, preMatchRecordID);//异步请求,preMatchRecordID这时无作用
                    Status = VoiceChatRunningStatus.PreMatch;
                } );
                return true;
            }
            else  if (Status == VoiceChatRunningStatus.Idle)
            {
                CurNetStrategy?.SendMatchingMsg(Msg.explore.PB_MatchingUpMsg.upBizMsgOneofCase.upFindPartnerReq, preMatchRecordID);//异步请求,preMatchRecordID这时无作用
                Status = VoiceChatRunningStatus.PreMatch;
                
                //todo 此时可能client网络出问题？
                //Notifier.instance.SendNotification(NotifyConsts.VoiceChat_StartPreMatch);//通知视图改变
                return true;
            }
            else if(Status == VoiceChatRunningStatus.Disposing)
            {
                VFDebug.LogWarning("StartPreMatch: 等待上一轮销毁完毕");
                return false;
            }
            else 
            {
                VFDebug.LogError("StartPreMatch: 状态错误");
                return false;
                
            }
        }

        private void RecvPreMatchConfirm()
        {
            if (Status == VoiceChatRunningStatus.PreMatch)
            {
                //目前是 直接 自动接收match
                Status = VoiceChatRunningStatus.PreMatchConfirming;
                VFDebug.Log("PreMatchConfirming  向服务器发 confirm的req");
                this.CurNetStrategy.SendMatchingMsg(PB_MatchingUpMsg.upBizMsgOneofCase.upPreMatchConfirmReq, preMatchRecordID);//preMatchRecordID这时有作用

                Notifier.instance.SendNotification(NotifyConsts.VoiceChat_StartPreMatchConfirming);
                
                //todo-tanglei  UI显示 已匹配正在确认中
            }
            else if (Status == VoiceChatRunningStatus.PreMatchConfirming)
            {
                VFDebug.Log("RecvPreMatchConfirm  已经触发过PreMatchConfirmReq ");
            }
            else
            {
                throw new InvalidOperationException("RecvPreMatchConfirm: 状态错误");
            }
        }

        public void CancelPreMatch()
        {
            if (Status == VoiceChatRunningStatus.PreMatch || Status == VoiceChatRunningStatus.PreMatchConfirming )
            {
                CurNetStrategy?.SendMatchingMsg(Msg.explore.PB_MatchingUpMsg.upBizMsgOneofCase.upMatchCancelReq, preMatchRecordID);//异步请求，preMatchRecordID这时有作用
                Status = VoiceChatRunningStatus.PreMatchCancelling;
                this.cancelActionTimerKey = TimerManager.instance.RegisterTimer( c=> {
                    DoCancellCompleteAction();
                },this.defaultClientTimerDelay, 1);    
            }
            else
            {
                Debug.LogWarning("CancelPreMatch: 状态错误");
            }
        }

        private void CancelPreMatchConfirming()
        {
            if (Status == VoiceChatRunningStatus.PreMatchConfirming)
            {
                CurNetStrategy?.SendMatchingMsg(Msg.explore.PB_MatchingUpMsg.upBizMsgOneofCase.upMatchCancelReq, preMatchRecordID);
                Status = VoiceChatRunningStatus.PreMatchCancelling;

            }
            else
            {
                throw new InvalidOperationException("CancelPreMatchConfirming: 状态错误");
            }
        }

        private void StepBackFromPreMatchConfirming()
        {
            if (Status == VoiceChatRunningStatus.PreMatchConfirming)
            {
                Status = VoiceChatRunningStatus.PreMatch;
            }
            else
            {
                throw new InvalidOperationException("RecvPreMatchConfirm: 状态错误");
            }
        }

        //后端 确认给MatchResult后自动 join到房间里
        private async void JoinChannel(string token,string channel ,uint uid=0)
        {
            if (Status == VoiceChatRunningStatus.PreMatchConfirming)
            {
                int joinResult = engine.JoinChannel(token, channel, uid);
                VFDebug.Log("JoinChannel joinResult="+ joinResult);

                if (joinResult == 0)
                {
                    //succ flow
                    Status = VoiceChatRunningStatus.ChattingSelfJoin;
                    VFDebug.Log("到达 ChattingSelfJoin状态，等待userJoin事件");
                    //继续检查房间里是不是已经有2个人在了
                    //bool isBothJoin = true;//todo-tanglei 需要查询 再决策 //await engine.IsChattingBothJoin();
                    //if (isBothJoin) {
                    //    VFDebug.Log("JoinChannel succ, goto  JoinBothSide");
                    //    RecvOtherJoinChannelMsg();
                    //}
                }
                else {
                    VFDebug.LogError("JoinChannel Failed,result=" + joinResult);
                    this.CancelPreMatchConfirming();
                }
                
            }
            else
            {
                if (Status == VoiceChatRunningStatus.ChattingBothJoin)
                {
                    VFDebug.LogError("已联通的状态下 又收到了额外的matchResult 不合理, 暂不不处理,等正克排查");
                }
                else {
                    //todo0-tanglei 必须处理的case
                    throw new InvalidOperationException("JoinChannel: 状态错误，Status="+Status);
                }
                
            }
        }

        //todo 目前这里其实不只是 对方反复join
        public void RecvOtherJoinChannelMsg()
        {
            if (Status == VoiceChatRunningStatus.ChattingSelfJoin)
            {
                Status = VoiceChatRunningStatus.ChattingBothJoin;
                var isSucc=  this.engine.ConfigMicAudio(true);
                if (!isSucc) {
                    VFDebug.LogError("config mic error");
                }
                Notifier.instance.SendNotification(NotifyConsts.VoiceChat_ChattingBothJoin);
            }
            else
            {
                throw new InvalidOperationException("RecvOtherJoinChannelMsg: 状态错误");
            }
        }

        //isSelfReason =true 就是自己点退出的case
        public void LeaveChannel(bool isSelfReason)
        {
            if (Status == VoiceChatRunningStatus.ChattingSelfJoin || Status == VoiceChatRunningStatus.ChattingBothJoin)
            {
                int ret = engine.LeaveChannel();
                if (ret < 0) {
                    VFDebug.LogError(" LeaveChannel not succ, code="+ ret);
                }

                Status = VoiceChatRunningStatus.Idle;
                this.preMatchRecordID = -1;

                if (isSelfReason)
                {
                    VFDebug.Log(" LeaveChannel selfReason ");
                }
                else {
                    Notifier.instance.SendNotification(NotifyConsts.VoiceChat_RecvOtherEndExitChannel);
                }

                this.CurNetStrategy.SendUserChatExitMsg(this.selfUid, this.partnerUid);


                VFDebug.Log(" LeaveChannel complete ");
                
                //尝试隔1点时间后销毁（1帧在移动端不够） 可能与LeaveChannel的执行耗时冲突，导致崩溃
         
                Notifier.instance.SendNotification(NotifyConsts.VoiceChat_ExitChannel);
            }
            else
            {
                throw new InvalidOperationException("LeaveChannel: 状态错误");
            }
        }

        #endregion 状态迁移 actions

        public bool ConfigMicVol(bool isTurnOn) {
            return this.engine.ConfigMicAudio(isTurnOn);
        }

        public void DisposeVoiceEngine()
        {
            this.Status = VoiceChatRunningStatus.Disposing; 
            TimerManager.instance.RegisterTimer((c) =>
            {
                this.engine.Dispose();
                this.Status = VoiceChatRunningStatus.UnInit;
            },150,1);
        }
    }
}
using System;
using System.Threading.Tasks;
using AnimationSystem;
using FairyGUI;
using ScriptsHot.Game.Modules.Explore.UI;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using UnityEngine;
using YooAsset;
using FilterMode = UnityEngine.FilterMode;
using Object = UnityEngine.Object;

/// <summary>
/// 独立的3D角色背景渲染组件，每个实例管理自己的渲染环境
/// 移植自 BackGroundAvatarManager，但去除缓存机制和单例模式，支持多实例
/// </summary>
public class BackGroundAvatarRoot
{
    private static int CURRID = 1;
    private static int ACTIVE_COUNT = 0;
    
    // 渲染环境组件
    private GameObject _root;
    private GameObject _backgroundRoot;
    private GameObject _characterRoot;
    private Camera _camera;
    private Light _mainLight;
    private RenderTexture _rt;

    
    
    // 加载的资源
    private GameObject _characterGoPrefab;
    private GameObject _currentAvatar;

    public GameObject CurrentAvatar => _currentAvatar;

    public GameObject CurrentBackground => _currentBackground;

    private GameObject _currentBackground;

    private AnimationAvatarManager _manager;
    
    // 新增字段：记录当前角色ID
    private long _currentAvatarId = -1;

    public AnimationAvatarManager Manager
    {
        get
        {
            //注:依然有可能是null,外部注意,如果调用的时候还没有初始化加载角色的话。加载完才有东西。
            if (this._manager == null)
            {
                this._manager = this.CurrentAvatar?.GetComponentInChildren<AnimationAvatarManager>();
            }
            return this._manager;
        }
        
        private set => this._manager = value;
    }
    
    // 常量
  //  private static readonly int UIModelLayer = 30;
    private static readonly string CharacterGoPrefabName = "BGAvatar";
    
    // 状态
    private bool _isInitialized = false;

    /// <summary>
    /// 初始化渲染环境
    /// </summary>
    private void InitRenderEnvironment()
    {
        if (_isInitialized) return;
        
        // 创建独立的场景根节点
        _root = new GameObject("5StarBackGroundRoot_Instance");
        _root.transform.position = new Vector3(-CURRID*50, 0, 0);
        _root.transform.rotation = Quaternion.identity;
        GameObject.DontDestroyOnLoad(_root);
        
        // 创建主光源
        GameObject lightRoot = new GameObject("Light");
        lightRoot.transform.SetParent(_root.transform, false);
        lightRoot.transform.localPosition = Vector3.zero;
        lightRoot.transform.localRotation = Quaternion.Euler(new Vector3(27, 18.8f, 0));
        lightRoot.transform.localScale = Vector3.one;
        
        _mainLight = lightRoot.AddComponent<Light>();
        _mainLight.type = UnityEngine.LightType.Directional;
        _mainLight.shadows = LightShadows.Soft;
        _mainLight.shadowStrength = 0.7f;
        _mainLight.intensity = 1.0f;
        _mainLight.color = new Color(1f, 0.98f, 0.915f, 1f);
        
        // 创建摄像机
        GameObject cameraRoot = new GameObject("CameraRoot");
        cameraRoot.transform.SetParent(_root.transform, false);
        cameraRoot.transform.localPosition = new Vector3(0, 1.69f, -2.25f);
        cameraRoot.transform.localRotation = Quaternion.Euler(new Vector3(11.27f, 0, 0));
        cameraRoot.transform.localScale = Vector3.one;
        
        _camera = cameraRoot.AddComponent<Camera>();
        _camera.orthographic = false;
        _camera.fieldOfView = 30;
        _camera.farClipPlane = 10;
        _camera.backgroundColor = Color.black;
        _camera.clearFlags = CameraClearFlags.SolidColor;
        _camera.enabled = false; // 默认关闭，需要时再开启
        
        // 创建背景挂点
        GameObject backgroundRoot = new GameObject("BGImage");
        backgroundRoot.transform.SetParent(cameraRoot.transform, false);
        backgroundRoot.transform.localPosition = new Vector3(0, 0, 5);
        backgroundRoot.transform.localRotation = Quaternion.identity;
        backgroundRoot.transform.localScale = new Vector3(0.1f, 0.1f, 0.1f);
        _backgroundRoot = backgroundRoot;
        
        // 创建角色挂点
        GameObject characterRoot = new GameObject("Character");
        characterRoot.transform.SetParent(_root.transform);
        characterRoot.transform.localPosition = new Vector3(0, -0.143f, 1.553f);
        characterRoot.transform.localRotation = Quaternion.Euler(0, 180, 0);
        _characterRoot = characterRoot;
        
        _isInitialized = true;
        ACTIVE_COUNT++;
        //Debug.LogError("ACTIVE" + ACTIVE_COUNT);
        CURRID++;
    }

    /// <summary>
    /// 加载角色容器预制体
    /// </summary>
    private async Task LoadCharacterPrefab()
    {
        if (_characterGoPrefab != null) return;
        
        if (YooAssets.CheckLocationValid(CharacterGoPrefabName))
        {
            var obj = YooAssets.LoadAssetAsync<GameObject>(CharacterGoPrefabName);
            await obj.Task;

            if (!obj.IsDone || !obj.IsValid)
            {
                Debug.LogError("角色容器预制体加载失败.");
                return;
            }
            else
            {
                _characterGoPrefab = obj.AssetObject as GameObject;
            }
        }
        else
        {
            Debug.LogError("角色容器预制体加载失败:没有找到相关预制体。");
            return;
        }
    }

    /// <summary>
    /// 加载角色
    /// </summary>
    private async Task LoadAvatarAsync(long id, Level level)
    {
        // 如果ID没有变化，且当前已有角色，直接返回
        if (_currentAvatarId == id && _currentAvatar != null)
        {
            return;
        }
        
        // 清理之前的角色（放回缓存池）
        if (_currentAvatar != null)
        {
            ReleaseCurrentAvatar();
        }
        
        // 先尝试从缓存池获取
        GameObject cachedAvatar = ModelPool.GetModel(id, _characterRoot.transform);
        if (cachedAvatar != null)
        {
            _currentAvatar = cachedAvatar;
            _currentAvatarId = id;
            
            // 重置管理器引用，会在下次访问Manager属性时重新获取
            this._manager = null;
            
            return;
        }
        
        // 缓存池没有，创建新的角色
        var styleName = level.GetComponent<UnitComponent>().GetStyleNameByAvatarId(id);
        
        // 加载角色模型
        var modelGo = await level.GetComponent<AvatarComponent>().avatarLoader.LoadNAvatar(styleName, null, true);
        
        await LoadCharacterPrefab();
        
        if (modelGo && _characterGoPrefab)
        {
            // 实例化角色容器
            GameObject parentObject = GameObject.Instantiate(_characterGoPrefab);
            modelGo.transform.SetParent(parentObject.transform);
            modelGo.transform.localPosition = Vector3.zero;
            parentObject.transform.SetParent(_characterRoot.transform, false);
            parentObject.transform.localPosition = Vector3.zero;
            parentObject.transform.localRotation = Quaternion.identity;
            parentObject.SetActive(true);

            // 初始化口型插件
            GAvatarCtrl avatarCtrl = parentObject.GetComponent<GAvatarCtrl>();
            if (!avatarCtrl) avatarCtrl = parentObject.AddComponent<GAvatarCtrl>();
            
            InitLipSyncPlugin(avatarCtrl);
            MatchLipSyncToHeadNode(modelGo, styleName, avatarCtrl);
            
            _currentAvatar = parentObject;
            _currentAvatarId = id;
        }
        else
        {
            Debug.LogError("角色模型加载失败。请检查传入的id、配表、level或其他原因。");
        }
    }

    /// <summary>
    /// 加载背景
    /// </summary>
    private async Task LoadBackgroundAsync(string backgroundName)
    {
        // 清理之前的背景
        if (_currentBackground != null)
        {
            GResManager.instance.ReleaseInstance(_currentBackground);
            _currentBackground = null;
        }

        if (backgroundName != null)
        {
            if (YooAssets.CheckLocationValid(backgroundName))
            {
                var background = YooAssets.LoadAssetAsync<Sprite>(backgroundName);
                await background.Task;

                if (!background.IsDone || !background.IsValid)
                {
                    Debug.LogError("背景加载失败。");
                }
                else
                {
                    Sprite sprite = background.AssetObject as Sprite;
                    _currentBackground = CreateBackgroundGameObject(backgroundName, sprite);
                }
            }
            else
            {
                Debug.LogError("背景加载失败。没有找到对应的资源");
            }
        }
    }

    /// <summary>
    /// 创建背景GameObject
    /// </summary>
    private GameObject CreateBackgroundGameObject(string backgroundName, Sprite sprite)
    {
        GameObject backgroundGo = new GameObject($"BG{backgroundName}");
        backgroundGo.transform.SetParent(_backgroundRoot.transform, false);
        backgroundGo.transform.localPosition = Vector3.zero;
        backgroundGo.transform.localRotation = Quaternion.identity;
        backgroundGo.transform.localScale = Vector3.one;
        var sp = backgroundGo.AddComponent<SpriteRenderer>();
        sp.sprite = sprite;
        
        return backgroundGo;
    }

    /// <summary>
    /// 初始化口型同步插件
    /// </summary>
    private void InitLipSyncPlugin(GAvatarCtrl bindLipSyncAvatarCtrl)
    {
        var bindObj = bindLipSyncAvatarCtrl.audioSource.gameObject;
        
#if !UNITY_EDITOR_OSX //macOS上OVRLipSync无效
        bool added = bindObj.TryGetComponent<OVRLipSyncContext>(out OVRLipSyncContext lsc);
        if (!added)
        {
            lsc = bindObj.AddComponent<OVRLipSyncContext>();
        }
        lsc.audioLoopback = true;
        lsc.audioSource = bindLipSyncAvatarCtrl.audioSource;

        added = bindLipSyncAvatarCtrl.audioSource.gameObject.TryGetComponent<OVRLipSyncContextMorphTarget>(out OVRLipSyncContextMorphTarget lsm);
        if (!added)
        {
            lsm = bindLipSyncAvatarCtrl.audioSource.gameObject.AddComponent<OVRLipSyncContextMorphTarget>();
        }
#endif
    }

    /// <summary>
    /// 匹配口型同步到头部节点
    /// </summary>
    private void MatchLipSyncToHeadNode(GameObject modelGo, string modelStyleName, GAvatarCtrl avatarCtrl)
    {
        var headGo = ObjectUtils.FindChild(modelGo.transform, "Head", true);
        if (headGo != null)
        {
#if !UNITY_EDITOR_OSX //macOS上OVRLipSync无效
            if (avatarCtrl != null)
            {
                var lsm = avatarCtrl.audioSource.GetComponent<OVRLipSyncContextMorphTarget>();
                if (lsm != null)
                {
                    var smr = headGo.GetComponent<SkinnedMeshRenderer>();
                    if (smr != null)
                    {
                        lsm.skinnedMeshRenderer = smr;
                        Debug.Log("Bind" + modelStyleName + " lsm.smr succ");
                    }
                    else
                    {
                        Debug.LogError("Failed to get SkinnedMeshRenderer from headGo, ModelName=" + modelStyleName);
                    }
                }
                else
                {
                    Debug.LogError("Failed to find OVRLipSyncContextMorphTarget on Character.ModelName=" + modelStyleName);
                }
            }
            else
            {
                Debug.LogError("Failed to find GAvatarCtrl on Character.ModelName=" + modelStyleName);
            }
#endif
        }
        else
        {
            Debug.LogError("Failed to find head-Node of Model name=" + modelStyleName);
        }
    }

    /// <summary>
    /// 释放当前角色到缓存池
    /// </summary>
    private void ReleaseCurrentAvatar()
    {
        if (_currentAvatar && _currentAvatarId != -1)
        {
            this.Manager.SetHeadCustomRotation(Vector3.zero,0.01f);
            var baseState = new StarX5PlayAnimationState();
            baseState.PlayDefaultAnimation(0.01f);
            _currentAvatar = null;
            _currentAvatarId = -1;
            this.Manager = null;
            ModelPool.ReleaseModel(_currentAvatarId, _currentAvatar);
        }
    }

    /// <summary>
    /// 设置所有子对象的层级
    /// </summary>
    private void SetLayerRecursively()
    {
        // 暂时注释掉层级设置逻辑，确保先复原基本功能
        // if (_root != null)
        // {
        //     Transform[] transforms = _root.GetComponentsInChildren<Transform>(true);
        //     foreach (Transform t in transforms)
        //     {
        //         t.gameObject.layer = UIModelLayer;
        //     }
        // }
    }

    /// <summary>
    /// 生成RenderTexture - 对应原来的 GenerateRT 方法
    /// </summary>
    public async Task<RenderTexture> GenerateRT(long id = 16978784336289664, string backgroundName = "Assets/Build/ExploreBG/StarX5BG.png")
    {
        this._manager = null;
        
        // 初始化渲染环境
        if (!_isInitialized)
        {
            InitRenderEnvironment();
        }
        
        // 获取场景控制器
        SceneController sCon = ControllerManager.instance.GetController(ModelConsts.Scene) as SceneController;
        Level level = sCon.scene;
        
        // 加载角色和背景
        await LoadAvatarAsync(id, level);
        
        await LoadBackgroundAsync(backgroundName);
        
        // 创建RenderTexture
        if (_rt == null)
        {
            _rt = new RenderTexture((int)GRoot.inst.width, (int)GRoot.inst.height, 24, RenderTextureFormat.ARGB32);
            _rt.autoGenerateMips = false;
            _rt.antiAliasing = 1;
            _rt.filterMode = FilterMode.Bilinear;
            _camera.targetTexture = _rt;
        }
        
        // 暂时注释掉层级设置，确保先复原基本功能
        // SetLayerRecursively();
        
        // 启用渲染
        _camera.enabled = true;
        _mainLight.enabled = true;
        
        return _rt;
    }

    /// <summary>
    /// 清理资源（关闭不释放）
    /// </summary>
    public void ClearRT()
    {
        if (_camera)
        {
            _camera.enabled = false;
            _camera.targetTexture = null;
        }
        
        if (_mainLight)
        {
            _mainLight.enabled = false;
        }
        
        this._root?.gameObject.SetActive(false);
        
        if (_rt)
        {
            _rt.Release();
            _rt = null;
        }
    }
    
    /// <summary>
    /// 清理资源（关闭&释放）释放后再用要重新加载重新传参数
    /// </summary>
    public void Dispose()
    {
        _rt?.Release();
        _rt = null;
        
        // 释放当前角色到缓存池
        ReleaseCurrentAvatar();
        
        // 销毁场景根节点
        if (_root != null)
        {
            Object.DestroyImmediate(_root);
            _root = null;
        }

        ACTIVE_COUNT--;
        //Debug.LogError("ACTIVE" + ACTIVE_COUNT);
        this._isInitialized = false;
        this._manager = null;
    }
    
    /// <summary>
    /// 让现在的指定Avatar播放一个动作。基于DialogGenericDefine配置的Key决定播放什么。如果该Key对应多个动作那么随机播放其中一个。
    /// 可以有callback。（播放完成时/此动作被打断时）
    /// 可以自行指定目标时长,通常传入0代表使用动画默认时长。
    /// 本质上是调用了一个专用于播放特定动画的State。通常有复杂状态机需求建议新增AnimState来支持。这个函数只适用于调用的状态机比较简单的情况。
    /// 指定动画播放完成后会自动返回Idle。
    /// </summary>
    public void PlayAnimationByExcel(string excelAnimation,Action<StarX5PlayAnimationState.AnimCallBack> cb,float targetDuration = 0f)
    {
        if (this.Manager)
        {
            if (this.Manager.currentState is StarX5PlayAnimationState state)
            {
                // state.PlayAnimationByExcel(this._manager.);
                state.PlayAnimationByExcel(excelAnimation,cb,targetDuration);
            }
            else
            {
                var pstate = new StarX5PlayAnimationState();
                this.Manager.SetState(pstate);
                pstate.PlayAnimationByExcel(excelAnimation,cb,targetDuration);
            }
        }
        else
        {
            if (cb != null)
            {
                cb.Invoke(StarX5PlayAnimationState.AnimCallBack.None);
            }
            VFDebug.LogWarning("无法播放动画。请确认是否已经初始化完成。");
        }
    }
    
    /// <summary>
    /// 基于动画名字播放动画。比如Idle + 女性(性别自动拾取) = 播放名为GirlIdle的动画。
    /// 其它与PlayAnimationByExcel相同；
    /// </summary>
    /// <param name="nameAnimation"></param>
    /// <param name="cb"></param>
    public void PlayAnimationByName(string nameAnimation,Action<StarX5PlayAnimationState.AnimCallBack> cb,float targetDuration = 0f)
    {
        if (this.Manager)
        {
            if (this.Manager.currentState is StarX5PlayAnimationState state)
            {
                // state.PlayAnimationByExcel(this._manager.);
                state.PlayAnimationByName(nameAnimation,cb,targetDuration);
            }
            else
            {
                var pstate = new StarX5PlayAnimationState();
                this.Manager.SetState(pstate);
                pstate.PlayAnimationByName(nameAnimation,cb,targetDuration);
            }
        }
        else
        {
            VFDebug.LogWarning("无法播放动画。请确认是否已经初始化完成。");
        }
    }
    

    /// <summary>
    /// 播放Avatar TTS音频
    /// </summary>
    /// <param name="audioClip">要播放的音频剪辑</param>
    /// <param name="rate">播放速率，默认为1.0</param>
    public void PlayAvatarTTS(AudioClip audioClip, float rate = 1f)
    {
        if (!audioClip)
        {
            VFDebug.LogError("AudioClip为空，无法播放音频");
            return;
        }

        if (!_currentAvatar)
        {
            VFDebug.LogError("当前没有Avatar，无法播放音频");
            return;
        }

        // 获取Avatar的音频组件
        GAvatarCtrl avatarCtrl = _currentAvatar.GetComponent<GAvatarCtrl>();
        if (!avatarCtrl || !avatarCtrl.audioSource)
        {
            VFDebug.LogError("Avatar音频源不可用，无法播放音频");
            return;
        }

        // 设置当前Avatar音频源到GSoundManager
        GSoundManager.instance.SetCurrAvatarTTS(avatarCtrl.audioSource);
        
        // 播放Avatar TTS音频
        GSoundManager.instance.PlayAvatarTTS(audioClip, rate);
    }
    
    /// <summary>
    /// 传入完整路径.
    /// </summary>
    /// <param name="url"></param>
    public void PlayAvatarTTS(string url)
     {
         if (GResManager.instance.CheckLocationValid(url))
         {
             GResManager.instance.LoadAudio(url, (clip, handle) =>
             {
                 if (clip)
                 {
                     PlayAvatarTTS(clip);
                 }   
             });
         }
     }

    /// <summary>
    /// 停止Avatar TTS音频播放
    /// </summary>
    public void StopAvatarTTS()
    {
        GSoundManager.instance.StopAvatarTTS();
    }
    
} 
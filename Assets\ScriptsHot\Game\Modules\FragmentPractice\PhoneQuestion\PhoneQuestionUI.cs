﻿
using System;
using FairyGUI;
using Game.Modules.FragmentPractice;
using Msg.basic;
using ScriptsHot.Game.Modules.ChatStart;
using ScriptsHot.Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.MainPath;
using UIBind.common;
using UIBind.FragmentPractice;
using UnityEngine;

public class PhoneQuestionUI: BaseUI<PhoneQuestionPanel>, IQuestionEventListener
{
    public override void OnBackBtnClick()
    {
        OnClickClose();
    }

    protected override bool isFullScreen => true;

    public override string uiLayer => UILayerConsts.Top;
    private int _lastQuestionNum = 0;
    private bool isForward;
    private bool isPlay;
    private bool isForceStop;

    private float barPreValue = 0;

    private float rightAvatarMaskAlpha = 0.6f;
    
    private PhoneQuestionController ctrl =>
        GetController<PhoneQuestionController>(ModelConsts.PhoneQuestion);

    private PhoneQuestionModel model => GetModel<PhoneQuestionModel>(ModelConsts.PhoneQuestion);

    public PhoneQuestionUI(string name) : base(name)
    {
       
    }
    protected override void OnInit(GComponent uiCom)
    {
       AddUIEvent(ui.BtnClose.onClick, OnClickClose);
       AddUIEvent(ui.CompPhone.coreComp.audioCom.backup.onClick,OnPreQuestionBtnClick);
       AddUIEvent(ui.CompPhone.coreComp.audioCom.foward.onClick,OnNextQuestionBtnClick);
    //    AddUIEvent(ui.CompPhone.coreComp.audioCom.btn.onClick,OnPlayBtnClick);
    }

    private void AddNotity()
    {
        RemoveNotity();
        Notifier.instance.RegisterNotification(NotifyConsts.SC_GetCourseSettlementAck, OnGetCourseSettlementAck);
    }

    private void RemoveNotity()
    {
        Notifier.instance.UnRegisterNotification(NotifyConsts.SC_GetCourseSettlementAck, OnGetCourseSettlementAck);
    }

    private void OnGetCourseSettlementAck(string s, object body)
    {
        TimerManager.instance.RegisterTimer(c =>
        {
            GetUI<PhoneQuestionUI>(UIConsts.PhoneQuestion).Hide();
        }, 500, 1);
        
      
    }

    protected override void OnShow()
    {
        AddNotity();
        _waitSettlement = false;
        barPreValue = 0;
        ui.BarPractice.Init(100, model.allQuestionNum);
        ui.coverTitle.text = string.Empty;

        ui.rightAvatar.imgMask.alpha = rightAvatarMaskAlpha;
        ui.leftAvatar.voiceCtrl.selectedIndex = 0;
        ui.rightAvatar.voiceCtrl.selectedIndex = 0;
        
        isForward = false;
        isForceStop = false;
        ui.CompPhone.coreComp.Init(true);
        ui.PageCtrl.selectedIndex = 0;
        if (args.Length > 0)
        {
            ui.colorCtrl.selectedIndex = (int)args[0];
        }
        else
        {
            ui.colorCtrl.selectedIndex = 0;
        }
        
        QuestionEventManager.Instance.ClearAnswer();
        _lastQuestionNum = 0;
        QuestionEventManager.Instance.AddListener(this);
        
        
    }
    
    private void SetForwarState()
    {
        bool canForward = false;
        var preQuest = ctrl.GetPrevQuestion();
        if(preQuest != null)
              canForward = preQuest.Element_type == PB_ElementTypeEnum.EleTAvatarText;
              
        bool canNext = false;
        var nextQuest = ctrl.GetNextQuestion();
        if(nextQuest != null)
              canNext = nextQuest.Element_type == PB_ElementTypeEnum.EleTAvatarText;
        
        ui.CompPhone.coreComp.audioCom.playCtrl.selectedIndex = 2;
      
        VFDebug.Log($"isForward :{isForward}  canForward: {canForward}  canNext: {canNext}");
        if (isForward)
        {
            if (canNext)
            {
                ui.CompPhone.coreComp.audioCom.playCtrl.selectedIndex = 1;
            }
        }
        else
        {
            if (canForward)
            {
                ui.CompPhone.coreComp.audioCom.playCtrl.selectedIndex = 0;
            }
        }

    }
    private void SetPlayeState()
    {
        ui.CompPhone.coreComp.audioCom.btnState.selectedIndex = 2;//isPlay ? 1:0;
    }

    public void ShowPractices()
    {
        SetForwarState();
        SetPlayeState();
        RefreshProgress();
        RefreshAllPractices();
        PreloadAvatarIntoStage();//初始化一轮头像信息，避免第一轮旁白时使用过去的资源
    }
    
    private string _timeFlag = String.Empty;
    private string _timeFlag2 = String.Empty;
    public void FinalShow()
    {
       //展示
       ui.coverTitle.text = model.sessionTitle;
       SoundManger.instance.PlayBGM("phone_back");
       LoadSceneBg();
       _timeFlag = TimerManager.instance.RegisterTimer((i) =>
       {
           SoundManger.instance.StopBGM();
           ui.PageCtrl.selectedIndex = 1;
           ShowPractices();
       }, 5000);
       
       // _timeFlag2 = TimerManager.instance.RegisterTimer((i) =>
       // {
       //     SoundManger.instance.StopBGM();
       // }, 6000);
    }

  

    protected override void OnHide()
    {
        SoundManger.instance.StopBGM();
        RemoveNotity();
        if (GSoundManager.instance.IsPlaying("TTS")) TTSManager.instance.StopTTS();
        ctrl.QuitPractice();
        if(_timeFlag != String.Empty)
            TimerManager.instance.UnRegisterTimer(_timeFlag);
        
        if(_timeFlag2 != String.Empty)
            TimerManager.instance.UnRegisterTimer(_timeFlag2);
        
        QuestionEventManager.Instance.RemoveListener(this);
    }
    
    private void RefreshAllPractices()
    {
        var question = model.CurQuestion;
        if (question != null && question.QuestionId != 0)
        {
            CollectData(question);
        }
    
        if (question.Element_type == PB_ElementTypeEnum.EleTAvatarText)
        {
            Debug.Log("RefreshAllPractices-EleTAvatarText");
            ui.CompPhone.coreComp.audioCom.com.visible = true;
            if (ui.CompPhone.coreComp.questionType.selectedIndex != 4)
            {
                ui.CompPhone.reset.Play();
                PlayAnima();
            }

            ui.CompPhone.coreComp.questionType.selectedIndex = 4;
            LoadAvatar();
            TTsPlay();
        }
        else if (question.Element_type == PB_ElementTypeEnum.EleTProse)
        {
            Debug.Log("RefreshAllPractices-EleTProse");
            ui.CompPhone.coreComp.audioCom.com.visible = true;
            if (ui.CompPhone.coreComp.questionType.selectedIndex != 4)
            {
                ui.CompPhone.reset.Play();//挡板的清场
                PlayAnima();//底部挡板上浮
            }

            ui.CompPhone.coreComp.questionType.selectedIndex = 4;
            //LoadAvatar(); 旁白角色
            TTsPlay();
        }
        else
        {
            VFDebug.Log("PhoneUI: 进入问答阶段 重置状态");
            isForward = false; //进入问答阶段 重置状态
            if (question.Element_type == PB_ElementTypeEnum.EleTQuestion)
            {
                ui.CompPhone.coreComp.audioCom.com.visible = false;
            }
            else
            {
                ui.CompPhone.coreComp.audioCom.com.visible = true;
            }
 
            ui.CompPhone.coreComp.ShowPractice(model.CurQuestion);
            ui.CompPhone.reset.Play();
            PlayAnima();
        }
    }        
    
    public void CollectData(APracticeData practice)
    {            
        DotPracticeManager.Instance.qid = practice.QuestionId;
        DotPracticeManager.Instance.Collect(new DataDot_AppearPage());
    } 

    private void PlayAnima()
    {
        ui.CompPhone.com.SetChildIndex(ui.CompPhone.coreComp.com, 3);
        ui.CompPhone.normal.invalidateBatchingEveryFrame = true;
        ui.CompPhone.normal.Play(() =>
        {
            ui.CompPhone.com.SetChildIndex(ui.CompPhone.coreComp.com, 3);
        });
    }

    private void TTsPlay()
    {
        isPlay = true;
        var question = model.CurQuestion;
        VFDebug.Log($"TTsPlay： {question.AudioId}");
        var id = question.AudioId;
        if (id == -1)
        {
            NextQuestion();
            return;
        }
        // if (GSoundManager.instance.IsPlaying("TTS"))
        TTSManager.instance.StopTTS(0,false);

        TTSManager.instance.PlayTTS(id, ()=>
        {
            VFDebug.Log("PhoneUI 声音结束回调！");
            NextQuestion();
            // if (isForceStop)
            // {
            //     isForceStop = false;
            // }
            // else
            // {
            //     NextQuestion();
            // }
        });
    }

    private void TTsStop()
    {
        isPlay = false;
        if (GSoundManager.instance.IsPlaying("TTS"))
            TTSManager.instance.StopTTS();
    }

    private bool _waitSettlement = false;
    private void NextQuestion()
    {
        if (!this.isShow) return;
        
        SetForwarState();
        ui.leftAvatar.voiceCtrl.selectedIndex = 0;
        ui.rightAvatar.voiceCtrl.selectedIndex = 0;
        QuestionEventManager.Instance.Reset();
        if (ctrl.NextQuestion())
        {
            ShowPractices();
        }
        else
        {
            //UIManager.instance.GetUI<WarmupLoadingCutInUI>(UIConsts.WarmupLoadingCutInUI).Show().onCompleted = () => { };
            //结算
            RefreshProgress();
            _waitSettlement = true;
            TimerManager.instance.RegisterTimer((i) =>
            {
                _waitSettlement = false;
                ctrl.ReqSettlement();
            },1000);
        }
    }

    public void LoadSceneBg()
    {
        var bglist = model.AvatarBgInfos;
        if (bglist.Count >= 2)
        {
            _ = (ui.leftAvatar.bg as LocalImageLoader).LoadExploreBg(bglist[0].tag_name); 
            _ = (ui.rightAvatar.bg as LocalImageLoader).LoadExploreBg(bglist[1].tag_name);
        }
        else
        {
            VFDebug.LogError($"耳机题 bg数量不匹配: {bglist.Count}");
        }

    }

    //20250506 临时修订bug时写的，没有跟LoadAvatar逻辑归并（但处理基础冲突），todo：+角色的逻辑做归并
    private void PreloadAvatarIntoStage()
    {
        var curAvatarInfo = model.GetAvatarInfo(model.curAvatar);
        if (curAvatarInfo != null)
        {
            _ = (ui.leftAvatar.avaterLoader as LocalImageLoader).LoadAvatarBody(curAvatarInfo.avatar_name);
            var colorCurIndex = GetAvatarColor(curAvatarInfo);
            ui.leftAvatar.colorCtrl.selectedIndex = colorCurIndex;
            //ui.leftAvatar.voiceCtrl.selectedIndex = question.AvatarId == model.curAvatar ? 1 : 0;
            ui.leftAvatar.avatarName.text = curAvatarInfo.avatar_name;
            ui.leftAvatar.nameCtrl.selectedIndex = string.IsNullOrEmpty(curAvatarInfo.avatar_name) ? 0 : 1;
            ui.leftAvatar.maskCtrl.selectedIndex = model.curMask ? 1 : 0;
        }

        var otherAvatarInfo = model.GetAvatarInfo(model.otherAvatar);
        if (otherAvatarInfo != null)
        {
            _ = (ui.rightAvatar.avaterLoaderLeft as LocalImageLoader).LoadAvatarBody(otherAvatarInfo.avatar_name);
            var colorOtherIndex = GetAvatarColor(otherAvatarInfo);
            ui.rightAvatar.colorCtrl.selectedIndex = colorOtherIndex;
            //ui.rightAvatar.voiceCtrl.selectedIndex = question.AvatarId == model.otherAvatar ? 1 : 0;
            ui.rightAvatar.avatarName.text = otherAvatarInfo.avatar_name;
            ui.rightAvatar.nameCtrl.selectedIndex = string.IsNullOrEmpty(otherAvatarInfo.avatar_name) ? 0 : 1;

            _ = (ui.rightAvatar.avaterLoaderLeft as LocalImageLoader).LoadAvatarBody(otherAvatarInfo.avatar_name);//首次载入
        }

    }
    private void LoadAvatar()
    {
        var question = model.CurQuestion;
        if (question.AvatarId == model.otherAvatar)
        {
            model.otherMask = false;
        }

        var curAvatarInfo = model.GetAvatarInfo(model.curAvatar);
        if (curAvatarInfo != null)
        {
            
            _ = (ui.leftAvatar.avaterLoader as LocalImageLoader).LoadAvatarBody(curAvatarInfo.avatar_name);
            var colorCurIndex = GetAvatarColor(curAvatarInfo);
            ui.leftAvatar.colorCtrl.selectedIndex = colorCurIndex;
            ui.leftAvatar.voiceCtrl.selectedIndex = question.AvatarId == model.curAvatar ? 1 : 0;
            ui.leftAvatar.avatarName.text = curAvatarInfo.avatar_name;
            ui.leftAvatar.nameCtrl.selectedIndex =  string.IsNullOrEmpty(curAvatarInfo.avatar_name) ? 0 : 1;
            ui.leftAvatar.maskCtrl.selectedIndex = model.curMask ? 1 : 0;
        }
        
        if (!model.otherMask && ui.rightAvatar.imgMask.alpha == rightAvatarMaskAlpha)
        {
            ui.rightAvatar.imgMask.TweenFade(0, 1f);
        }

        if (question.AvatarId != model.curAvatar && question.AvatarId != model.otherAvatar)
        {
            model.otherAvatar = question.AvatarId;
            ui.rightAvatar.reset.Play();
            var otherAvatarInfo = model.GetAvatarInfo(model.otherAvatar);
            if (otherAvatarInfo != null)
            {
                _ = (ui.rightAvatar.avaterLoaderRight as LocalImageLoader).LoadAvatarBody(otherAvatarInfo.avatar_name);
                var colorOtherIndex = GetAvatarColor(otherAvatarInfo);
                ui.rightAvatar.colorCtrl.selectedIndex = colorOtherIndex ;
                ui.rightAvatar.voiceCtrl.selectedIndex = question.AvatarId == model.otherAvatar ? 1 : 0;
                
                ui.rightAvatar.nameCtrl.selectedIndex =  string.IsNullOrEmpty(otherAvatarInfo.avatar_name) ? 0 : 1;

                if (ui.rightAvatar.avatarName.text != otherAvatarInfo.avatar_name)
                {
                    //换人时才做动画
                    ui.rightAvatar.offset.Play(() =>
                    {
                        _ = (ui.rightAvatar.avaterLoaderLeft as LocalImageLoader).LoadAvatarBody(otherAvatarInfo.avatar_name);
                    });
                }
                else
                {
                    _ = (ui.rightAvatar.avaterLoaderLeft as LocalImageLoader).LoadAvatarBody(otherAvatarInfo.avatar_name);
                }
                ui.rightAvatar.avatarName.text = otherAvatarInfo.avatar_name;
             
            }
        }
        else
        {
            var otherAvatarInfo = model.GetAvatarInfo(model.otherAvatar);
            if (otherAvatarInfo != null)
            {
                _ = (ui.rightAvatar.avaterLoaderLeft as LocalImageLoader).LoadAvatarBody(otherAvatarInfo.avatar_name);
                var colorOtherIndex = GetAvatarColor(otherAvatarInfo);
                ui.rightAvatar.colorCtrl.selectedIndex = colorOtherIndex ;
                ui.rightAvatar.voiceCtrl.selectedIndex = question.AvatarId == model.otherAvatar ? 1 : 0;
                ui.rightAvatar.avatarName.text = otherAvatarInfo.avatar_name;
                ui.rightAvatar.nameCtrl.selectedIndex =  string.IsNullOrEmpty(otherAvatarInfo.avatar_name) ? 0 : 1;
            }
        }
    }

    private int GetAvatarColor(PB_AvatarInfo info)
    {
        switch (info.bg_color)
        {
            case PB_BackgroundColorEnum.BGColorOrangeYellow:
                return 0;
                break;
            case PB_BackgroundColorEnum.BGColorGreen:
                return 1;
                break;
            case PB_BackgroundColorEnum.BGColorBabyBlue:
                return 2;
                break;
            case PB_BackgroundColorEnum.BGColorPurple:
                return 3;
                break;
            case PB_BackgroundColorEnum.BGColorPink:
                return 4;
                break;
        }
        return 5;
    }
    
    public void OnClickClose()
    {
        if (_waitSettlement) return;
        
        DotPracticeManager.Instance.Collect(new DataDot_Exit());
        GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_common_retention_tips", Stay,
            Exit, 3, "common_keep_learning", "common_quit", false, 1);
    }   
    
    private void Stay()
    {
        // 留下来！
    }

    //中途退出
    private async void Exit()
    {
        // 确认退出
        // ModelManager.instance.GetModel<LearnPathModel>(ModelConsts.LearnPath).NeedAutoOpen = true;
        // Notifier.instance.SendNotification(NotifyConsts.LearnPathReShowEvent);
        
        ctrl.SendExitQuickPracticeReq();
        //ctrl.ExitPractice();
        Hide();
        
    }
    
    private void RefreshProgress()
    { 
        float final = model.InitProgress + model.CurQuestionNum * 100 / model.allQuestionNum;
        if (barPreValue == final) return;
        
        barPreValue = final;
 
        ui.BarPractice.SetProgress(final);
    }

    public void OnAnswered()
    {
        ctrl.DoAnswerError(); 
    }

    public void OnSubmit()
    {
        ctrl.DoAnswerCorrect();
        NextQuestion();
    }

    public void OnRetry()
    {
       
    }

    /// <summary>
    /// 经过测试 耳机只有 完成的时候才会走这里
    /// </summary>
    public void AutoCheck()
    {
        QuestionEventManager.Instance.SubmitAnswer();
    }

    public void OnReset()
    {
    }

    public void OnJumpListenTask()
    {
    }

    public void OnJumpSpeakTask()
    {
    }

    private void OnPreQuestionBtnClick()
    {
        if (ui.CompPhone.coreComp.audioCom.playCtrl.selectedIndex != 0) return;
        var curQuest = model.CurQuestion;
        if(curQuest.Element_type == PB_ElementTypeEnum.EleTQuestion)
            return;
        var preQuest = ctrl.GetPrevQuestion();
        if (preQuest == null) return;
        if(preQuest.Element_type == PB_ElementTypeEnum.EleTQuestion)
            return;
        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
        
        isForward = true;
        isForceStop = true;
        SetForwarState();
        ctrl.PreQuestion();
        
        ShowPractices();
    }
    private void OnNextQuestionBtnClick()
    {
        if (ui.CompPhone.coreComp.audioCom.playCtrl.selectedIndex != 1) return;
        
        var curQuest = model.CurQuestion;
        if(curQuest.Element_type == PB_ElementTypeEnum.EleTQuestion)
            return;
        var nextQuest = ctrl.GetNextQuestion();
        if (nextQuest == null) return;
        if(nextQuest.Element_type == PB_ElementTypeEnum.EleTQuestion)
            return;
        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
        
        isForward = false;
        isForceStop = true;
        SetForwarState();
        ctrl.NextQuestion();
        
        ShowPractices();
    }
    


    // private void OnPlayBtnClick()
    // {
    //     return;
    //     isForceStop = true;
    //     if (isPlay)
    //     {
    //         TTsStop();
    //     }
    //     else
    //     {
    //         TTsPlay();
    //     }
    // }
}

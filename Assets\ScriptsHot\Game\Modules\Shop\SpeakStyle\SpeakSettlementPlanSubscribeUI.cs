/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2025/02/12 11:13:27 星期三
# 功能：Nothing
****************************************************
*/

using FairyGUI;
using Modules.DataDot;
using Msg.incentive;
using ScriptsHot.Game.Modules.Settlement;

namespace ScriptsHot.Game.Modules.Shop
{
    public class SpeakSettlementPlanSubscribeUI: BaseUI<UIBind.Shop.SpeakSettlementPlanSubscribePanel>
    {
        public SpeakSettlementPlanSubscribeUI(string name) : base(name)
        {
        }
        
        public override string uiLayer => UILayerConsts.Top; //主UI层
        
        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(ui.btnExit.onClick, OnBtnCloseClicked);
            AddUIEvent(ui.comPlanPromotionContent.btnNext.onClick, OnBtnNextClicked);
            
            ui.comPlanPromotionContent.tfPromotionDesc1.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_promotion_desc21");
            ui.comPlanPromotionContent.tfPromotionDesc2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_promotion_desc22");
            ui.comPlanPromotionContent.tfPromotionDesc3.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_paywall_umlimited_conversations");
            ui.comPlanPromotionContent.tfPromotionTitle.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_promotion_desc25");
            ui.comPlanPromotionContent.tfbtnPromotion.SetLanguage(LanguageType.MotherTongue, refresh: false).SetKey("ui_plan_promotion_desc1");
            ui.comPlanPromotionContent.tfFreeTrail.SetLanguage(LanguageType.MotherTongue, refresh: false).SetKey("ui_plan_promotion_desc24");
        }
        
        private void OnBtnCloseClicked()
        {
            GetController<SettlementController>(ModelConsts.Settlement).ShowNextView(()=> { Hide(); });
          
            var dot = new MembershipPageLater();
            DataDotMgr.Collect(dot);
        }

        private void OnBtnNextClicked()
        {
            PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.result_page;
            GetUI(UIConsts.SpeakPlanSelectUI).Show(4);
            Hide();
        }

        protected override void OnShow()
        {
            base.OnShow();
            ui.comPlanPromotionContent.state.selectedIndex = 0;
            GetController<ShopController>(ModelConsts.Shop).ReqShopInfo();
            MsgManager.instance.SendMsg(new CS_SetShowStateReq()
            {
                show_item_type = PB_ShowItemType.CommercialSettlement,
            });
        }
    }
}
﻿using System;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using Msg.explore;
using UnityEngine.SceneManagement;

namespace AnimationSystem
{
    public class SemanticMapping
    {
        private static SemanticMapping instance;
        private bool isInitialized = false;
        
        public static SemanticMapping Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new SemanticMapping();
                }
                return instance;
            }
        }
        
        private SemanticMapping() { }
        
        #region util

        public List<SingleSentenceInfo> DeserializeObject(string jsonString)
        {
            List<SingleSentenceInfo> result = new List<SingleSentenceInfo>();
            
            try
            {
                // 反序列化JSON数据
                DialogData dialogData = JsonUtility.FromJson<DialogData>(jsonString);
                
                float lastEndTime = 0f; // 用于计算duration和willStartAt
                
                foreach (var sentence in dialogData.sentences)
                {
                    SingleSentenceInfo info = new SingleSentenceInfo();
                    
                    // 计算duration
                    info.duration = sentence.end_time_second - lastEndTime;
                    // 计算willStartAt
                    info.willStartAt = sentence.start_time_second - lastEndTime;
                    lastEndTime = sentence.end_time_second;
                    
                    // 映射function_tag到SentenceSemanticType
                    info.semanticType = MapFunctionTagToSemanticType(sentence.function_tag);
                    
                    // 映射intensity_tag到SentenceSemanticSideType
                    info.semanticSideType = MapIntensityTagToSideType(sentence.intensity_tag);
                    
                    result.Add(info);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"反序列化对话数据失败: {e.Message}");
            }
            
            return result;
        }
        
        public List<SingleSentenceInfo> DeserializeObject(List<PB_EmotionAnalysisSentenceResult> sentenceResults)
        {
            List<SingleSentenceInfo> result = new List<SingleSentenceInfo>();
            System.Text.StringBuilder sb = new System.Text.StringBuilder();
            try
            {
                float lastEndTime = 0f; // 用于计算duration和willStartAt

                


                foreach (var sentence in sentenceResults)
                {
                    SingleSentenceInfo info = new SingleSentenceInfo();
                  
                    // 计算duration
                    info.duration = sentence.endTimeSecond - lastEndTime;
                    // 计算willStartAt
                    info.willStartAt = sentence.startTimeSecond - lastEndTime;
                    lastEndTime = sentence.endTimeSecond;
                    
                    // 映射function_tag到SentenceSemanticType
                    info.semanticType = MapFunctionTagToSemanticType(sentence.functionTag);
                    
                    // 映射intensity_tag到SentenceSemanticSideType
                    info.semanticSideType = MapIntensityTagToSideType(sentence.intensityTag);
                    
                    result.Add(info);
                    sb.AppendLine($"[time:{info.willStartAt.ToString("F2")},len={info.duration.ToString("F2")}\t tag:{info.semanticType.ToString()},{info.semanticSideType.ToString()} \t{sentence.text}] ");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"反序列化对话数据失败: {e.Message}");
            }
            Debug.Log("<color=red>== 动作语意数据 ==</color>\n" + sb.ToString() );
            sb.Clear();
            return result;
        }
        
        private SentenceSemanticType MapFunctionTagToSemanticType(string functionTag)
        {
            switch (functionTag.ToLower())
            {
                case "self-reference":
                    return SentenceSemanticType.PointSelf;
                case "emphasis & contrast":
                    return SentenceSemanticType.Contrast;
                case "audience-direction":
                    return SentenceSemanticType.PointPlayer;
                case "objective description":
                    return SentenceSemanticType.State;
                default:
                    return SentenceSemanticType.Neutral;
            }
        }

        private SentenceSemanticSideType MapIntensityTagToSideType(string intensityTag)
        {
            switch (intensityTag.ToLower())
            {
                case "high":
                    return SentenceSemanticSideType.Strong;
                case "low":
                    return SentenceSemanticSideType.Weak;
                default:
                    return SentenceSemanticSideType.Weak;
            }
        }
        
        private readonly Dictionary<string, SentenceSemanticType> _map =
            new(StringComparer.OrdinalIgnoreCase) // 忽略大小写
            {
                { "Neutral", SentenceSemanticType.Neutral },
                { "Happy", SentenceSemanticType.Happy },
                { "State", SentenceSemanticType.State },
                { "Contrast", SentenceSemanticType.Contrast },
                { "PointSelf", SentenceSemanticType.PointSelf },
                { "PointPlayer", SentenceSemanticType.PointPlayer }
            };

        public List<SentenceSemanticType> MapToSemanticTypes(List<string> inputs)
        {
            var result = new List<SentenceSemanticType>();

            foreach (var input in inputs)
            {
                if (_map.TryGetValue(input, out var value))
                {
                    result.Add(value);
                }
                else
                {
                    Debug.LogError($"无效的语义类型字符串：{input}");
                }
            }

            return result;
        }
        
        private readonly Dictionary<string, SentenceSemanticSideType> _map2 =
            new(StringComparer.OrdinalIgnoreCase)
            {
                { "Strong", SentenceSemanticSideType.Strong },
                { "Weak", SentenceSemanticSideType.Weak },
            };

        public List<SentenceSemanticSideType> MapToSideTypes(List<string> inputs)
        {
            var result = new List<SentenceSemanticSideType>();

            foreach (var input in inputs)
            {
                if (_map2.TryGetValue(input, out var value))
                {
                    result.Add(value);
                }
                else
                {
                    throw new ArgumentException($"无效的 SideType 字符串：{input}");
                }
            }

            return result;
        }
        
        #endregion
        
        //3.27.2025 目前只有两大类语义标签。
        private Dictionary<SentenceSemanticSideType,List<string>> sideDictionary = new Dictionary<SentenceSemanticSideType,List<string>>();
        private Dictionary<SentenceSemanticType, List<string>> semanticDictionary = new Dictionary<SentenceSemanticType, List<string>>();
        
        //从Excel中读取所有的语义配置信息,并且缓存。(后续调用语义查找必须先初始化)
        public void InitAllGroupSemanticData()
        {
            if (isInitialized)
            {
                Debug.LogWarning("SemanticMapping已经初始化过了");
                return;
            }

            List<AvatarActionSemanticCfg> list;
            
            list = Cfg.T.TBAvatarSemanticMap.DataList;
            
            for (int i = list.Count - 1; i >= 0; i--)
            {
                if (!list[i].isDeprecated)
                {

                    List<SentenceSemanticType> semanticList1 = MapToSemanticTypes(list[i].semanticTagA);
                    List<SentenceSemanticSideType> semanticList2 = MapToSideTypes(list[i].semanticTagB);

                    // 将每个语义类型映射到actionSetKey
                    foreach (var semanticType in semanticList1)
                    {
                        if (!semanticDictionary.ContainsKey(semanticType))
                        {
                            semanticDictionary[semanticType] = new List<string>();
                        }

                        semanticDictionary[semanticType].Add(list[i].actionSetKey);
                    }

                    // 将每个语义侧边类型映射到actionSetKey
                    foreach (var sideType in semanticList2)
                    {
                        if (!sideDictionary.ContainsKey(sideType))
                        {
                            sideDictionary[sideType] = new List<string>();
                        }

                        sideDictionary[sideType].Add(list[i].actionSetKey);
                    }
                }
            }
            
            isInitialized = true;
        }
        
        //返回满足条件的所有Group.
        public List<string> MatchAllGroup(SentenceSemanticType sentenceSemanticType,
            SentenceSemanticSideType semanticSideType)
        {
            if (!isInitialized)
            {
                Debug.LogError("SemanticMapping未初始化,请先调用InitAllGroupSemanticData");
                return new List<string>();
            }

            // 获取满足语义类型的actionSetKeys
            if (semanticDictionary.TryGetValue(sentenceSemanticType, out var semanticKeys) &&
                sideDictionary.TryGetValue(semanticSideType, out var sideKeys))
            {
                return semanticKeys.Intersect(sideKeys).ToList();
            }
            
            return new List<string>();
        }
    }
}
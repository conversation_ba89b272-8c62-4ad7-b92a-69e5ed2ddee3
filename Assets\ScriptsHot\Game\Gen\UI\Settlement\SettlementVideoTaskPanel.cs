/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Settlement
{
    public partial class SettlementVideoTaskPanel : UIBindT
    {
        public override string pkgName => "Settlement";
        public override string comName => "SettlementVideoTaskPanel";

        public GImage imgBG;
        public CompBottom compBottom;
        public CompVideoSettle compVideoDetail;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            imgBG = (GImage)com.GetChildAt(0);
            compBottom = new CompBottom();
            compBottom.Construct(com.GetChildAt(1).asCom);
            compVideoDetail = new CompVideoSettle();
            compVideoDetail.Construct(com.GetChildAt(2).asCom);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            imgBG = null;
            compBottom.Dispose();
            compBottom = null;
            compVideoDetail.Dispose();
            compVideoDetail = null;
        }
    }
}
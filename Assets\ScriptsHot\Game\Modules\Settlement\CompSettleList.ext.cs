﻿using System.Collections.Generic;
using FairyGUI;
using UnityEngine;

namespace UIBind.Settlement
{
    public partial class CompSettleList
    {
        public enum SettleIconType
        {
            Time,
            Exp,
            CorrectRate,
            Words,
        }
        
        public struct SettleListStruct
        {
            public SettleIconType IconType;
            public int Number;
        }

        private List<SettleListStruct> _settleList;

        public void DoInit()
        {
            list.itemRenderer = OnRendererSettle;
        }
        
        public void DoShowSettleList(List<SettleListStruct> settleData)
        {
            _settleList = settleData;
            list.numItems = settleData.Count;
        }
        
        private void OnRendererSettle(int index, GObject obj)
        {
            obj.visible = false;
            GComponent comp = obj as GComponent;
            if(comp == null) return;
            SettleItem item = new SettleItem();
            item.Construct(comp);
            TimerManager.instance.RegisterTimer((a) =>
            {
                obj.visible = true;
                GTween.To(0.6f, 1f, 0.3f)
                    .SetTarget(obj)
                    .OnUpdate((t) =>
                    {
                        float scale = (float)t.value.x;
                        obj.SetScale(scale, scale);
                    }).OnComplete(() =>
                    {
                        // 数字递增动画
                        int targetValue = _settleList[index].Number;
                        GTween.To(0, targetValue, 0.2f)
                            .SetTarget(item.tfNum)
                            .OnUpdate((t) =>
                            {
                                item.tfNum.text = Mathf.FloorToInt((float)t.value.x).ToString();
                            });
                    });
                
            }, index * 500);
            item.icon.selectedIndex = (int)_settleList[index].IconType;
            item.tfNum.text = "0"; // 初始化为0
            item.grp.EnsureBoundsCorrect();
        }

        public void UpdateItemNum(int[] finalNum)
        {
            for (int i = 0; i < _settleList.Count; i++)
            {
                if (finalNum[i] > 0)
                    list.GetChildAt(i).asCom.GetChild("tfNum").asTextField.text = finalNum[i].ToString();
            }
        }
    }
}
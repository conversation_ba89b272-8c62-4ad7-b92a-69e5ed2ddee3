/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/08/22 20:11:59 星期四
# 功能：Nothing
****************************************************
*/

using System;
using System.Collections.Generic;
using DG.Tweening;
using FairyGUI;
using Modules.DataDot;
using Msg.incentive;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Settlement;
using ScriptsHot.Game.Modules.Shop;
using UIBind.Scene;
using UIBind.Sign;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Sign
{
    public class SignDailyRewardUI: BaseUI<UIBind.Sign.SignDailyRewardPanel>
    {
        public SignDailyRewardUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top; //主UI层
        protected override bool isFullScreen => true;

        private SettlementModel _modelSettlement => this.GetModel<SettlementModel>(ModelConsts.Settlement);
        
        private bool _isFinishGoal = false;
        
        private LinkedList<TaskCellComponent> _taskCells = new();
        private List<TaskCellComponent> _receiveAbleCells = new();
        private Queue<SignDailyAniParams> rewardQueue = new();
        
        private enum SignDailyAniType
        {
            ShowProgressBar,
            ShowReward,
            ShowCurrencyItem,
            ShowUseAbleItem,
        }
        
        private struct SignDailyAniParams
        {
            public SignDailyAniType type;
            public PB_RewardMateriaType rewardType;
            public TaskCellComponent com;
            public int pre_value;
            public int cur_value;
            public int max_value;
        }
        
        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(this.ui.btnRightConfirm.onClick, OnBtnRightConfirmClicked);
        }
        
        protected override void OnShow()
        {
            base.OnShow();
            Refresh();
            var dot = new AppearRewardGet();
            DataDotMgr.Collect(dot);
            SoundManger.instance.PlayUI("sign_flow_jump");
            AFDots.Appear_Rewards_quest_update();
        }
        
        private void Refresh()
        {
            //初始化用来播放动画
            ui.grpUseItem.visible = false;
            ui.tfDiamondBig.visible = false;
            ui.grpDiamond.visible = false;
            ui.imgItemBg.visible = false;
            ui.imgItemBgTop.visible = false;
            ui.spItem.visible = false;
            ui.spRewardEffect.visible = false;
            ui.imgLoader.visible = false;
            ui.imgItemBg.size = ui.imgBG.size;
            ui.imgItemBg.xy = ui.imgBG.xy;
            ui.imgItemBgTop.xy = ui.imgBG.xy;
            ui.imgBG.visible = true;
            ui.spItem.spineAnimation.AnimationState.SetAnimation(0, "b1", false);
            ui.imgBG.alpha = 1;
            ui.grpHeader.xy = new Vector2(32, ui.grpHeader.y);
            ui.btnRightConfirm.SetKey("common_continue");

            //刷新列表
            _taskCells = new();
            _receiveAbleCells = new();
            ui.taskLIst.RemoveChildren();
            rewardQueue.Clear();
            var hasItem = false;
            var rewardType = PB_RewardMateriaType.PB_RewardMateriaType_None;
            var isAllCompleted = true;
            
            var diamondCnt = 0;
            foreach (var item in _modelSettlement.SignDailyData.quest_list)
            {
                var com = CreateTaskCellItem(item);

                if (item.finish_cnt < item.threshold)
                {
                    isAllCompleted = false;
                }
                
                //有新的奖励
                if (item.pre_finish_cnt < item.finish_cnt && item.finish_cnt == item.threshold)
                {
                    rewardQueue.Enqueue(new SignDailyAniParams()
                    {
                        type = SignDailyAniType.ShowProgressBar,
                        com = com,
                        pre_value = item.pre_finish_cnt,
                        cur_value = item.finish_cnt,
                        max_value = item.threshold,
                    });
                    rewardQueue.Enqueue(new SignDailyAniParams()
                    {
                        type = SignDailyAniType.ShowReward,
                        rewardType = item.reward_materia_type,
                        cur_value = (int)item.reward_value,
                        com = com,
                    });
                    if (item.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_Diamond)
                        diamondCnt += (int)item.reward_value;
                    com.spReward.visible = true;
                    com.spReward.spineAnimation.AnimationState.SetAnimation(0, "a2", true);
                    com.stateComplete.selectedPage = "no";
                    com.imgLoader.visible = false;
                    com.tfCnt.visible = false;
                    
                    if (item.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_1_5 ||
                        item.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_2 ||
                        item.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_3 ||
                        item.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_StreakFreeze ||
                        item.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_UnlimitedStamina)
                    {
                        hasItem = true;
                        rewardType = item.reward_materia_type;
                    }

                    if (item.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_UnlimitedStamina)
                    {
                        var dot = new DotCutDailyQuestsFinished();
                        DataDotMgr.Collect(dot);
                    }
                    
                    var progressRate = (float)item.pre_finish_cnt / item.threshold;
                    var size = new Vector2(com.imgProgressBarBg.size.x * progressRate, com.imgProgressBarBg.size.y);
                    if (size.x < 10f)
                        size = new Vector2(0, com.imgProgressBarBg.size.y);
                    com.imgProgressBar.size = size;
                }
                //领完奖
                else if (item.pre_finish_cnt == item.finish_cnt && item.finish_cnt >= item.threshold)
                {
                    com.spReward.visible = false;
                }
                //进行中
                else
                {
                    rewardQueue.Enqueue(new SignDailyAniParams()
                    {
                        type = SignDailyAniType.ShowProgressBar,
                        com = com,
                        pre_value = item.pre_finish_cnt,
                        cur_value = item.finish_cnt,
                        max_value = item.threshold,
                    });
                    var progressRate = (float)item.pre_finish_cnt / item.threshold;
                    var size = new Vector2(com.imgProgressBarBg.size.x * progressRate, com.imgProgressBarBg.size.y);
                    if (size.x < 10f)
                        size = new Vector2(0, com.imgProgressBarBg.size.y);
                    com.imgProgressBar.size = size;
                    com.spReward.spineAnimation.AnimationState.SetAnimation(0, "a2", false);
                }
            }

            if (diamondCnt > 0)
            {
                rewardQueue.Enqueue(new SignDailyAniParams()
                {
                    type = SignDailyAniType.ShowCurrencyItem,
                    rewardType = rewardType,
                    cur_value = diamondCnt,
                });
            }

            if (isAllCompleted)
                AFDots.Appear_Rewards_quest_finish();

            foreach (var data in _modelSettlement.SignDailyData.reward_aggr_map)
            {
                if (data.Value.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_StreakFreeze ||
                    data.Value.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_UnlimitedStamina ||
                    data.Value.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_1_5 ||
                    data.Value.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_2 ||
                    data.Value.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_3)
                {
                    rewardQueue.Enqueue(new SignDailyAniParams()
                    {
                        type = SignDailyAniType.ShowUseAbleItem,
                        rewardType = data.Value.reward_materia_type,
                        cur_value = (int)data.Value.reward_materia_num,
                    });
                }
            }

            if (_modelSettlement.SignDailyData.finished_task_cnt >= _modelSettlement.SignDailyData.total_task_cnt)
            {
                ui.tfTitle.SetKey("ui_dailysign_completed_all_task");
            }
            else if (_modelSettlement.SignDailyData.finished_task_cnt > 0)
            {
                ui.tfTitle.SetKeyArgs("ui_dailysign_completed_task", _modelSettlement.SignDailyData.finished_task_cnt);
            }else{
                ui.tfTitle.SetKey("ui_dailysign_updated");
            }
            
            ui.spFlow.spineAnimation.AnimationState.SetAnimation(0, "1", false).Complete += (t) =>
            {
                ui.spFlow.spineAnimation.AnimationState.SetAnimation(0, "2", true);
            };
            ShowRewardQueue();
        }
        
        private TaskCellComponent CreateTaskCellItem(PB_CheckinReward item)
        {
            var diamond = (int)item.reward_value;
            var progress = $"{item.finish_cnt}/{item.threshold}";
            var progressRate = (float)item.finish_cnt / item.threshold;
            var type = item.task_type;
            var status = item.reward_status;
            var max = item.threshold;
            TaskCellComponent taskCellCom = new();
            taskCellCom.Construct(UIPackage.GetByName("Sign").CreateObject("TaskCellComponent").asCom);
            taskCellCom.tfCnt.text = item.reward_materia_type == PB_RewardMateriaType.PB_RewardMateriaType_Diamond
                ? diamond.ToString()
                : GetItemDesc(item.reward_materia_type, item.reward_value);
            taskCellCom.tfProgress.text = progress;
            taskCellCom.imgLoader.url = GetImgUrl(item.reward_materia_type);
            var size = new Vector2(taskCellCom.imgProgressBarBg.size.x * progressRate, taskCellCom.imgProgressBarBg.size.y);
            if (size.x < 10f)
                size = new Vector2(0, taskCellCom.imgProgressBarBg.size.y);
            taskCellCom.imgProgressBar.size = size;
            //taskCellCom.spDiamond.playing = status == PB_CheckinRewardStatus.PB_CheckinRewardStatus_Finish;
            taskCellCom.stateComplete.selectedPage = "no";
            taskCellCom.imgProgressBar.visible = progressRate >= 0.01f;
            if (item.isUnlock)
            {
                if (status == PB_CheckinRewardStatus.PB_CheckinRewardStatus_Acquired)
                {
                    taskCellCom.stateComplete.selectedPage = "yes";
                    ui.taskLIst.AddChildAt(taskCellCom.com, ui.taskLIst.numChildren);
                    _taskCells.AddFirst(taskCellCom);
                    taskCellCom.spDiamond.spineAnimation.AnimationState.SetAnimation(0, "4", false);
                    taskCellCom.imgLoader.visible = true;
                    taskCellCom.tfCnt.visible = true;
                    taskCellCom.imgLoader.scale = Vector3.one;
                    taskCellCom.spReward.visible = true;
                }
                else
                {
                    taskCellCom.imgLoader.visible = false;
                    taskCellCom.tfCnt.visible = false;
                    taskCellCom.spReward.visible = true;
                    taskCellCom.spReward.spineAnimation.AnimationState.SetAnimation(0, "a2", false);
                    ui.taskLIst.AddChildAt(taskCellCom.com, ui.taskLIst.numChildren);
                    _taskCells.AddLast(taskCellCom);
                    if (item.reward_status == PB_CheckinRewardStatus.PB_CheckinRewardStatus_Finish)
                    {
                        _receiveAbleCells.Add(taskCellCom);
                    }
                    taskCellCom.spDiamond.spineAnimation.AnimationState.SetAnimation(0, "3", false);
                }   
                
                taskCellCom.tfDesc.text = item.task_title;
            }
            else
            {
                taskCellCom.imgLoader.visible = false;
                taskCellCom.tfCnt.visible = false;
                taskCellCom.spReward.visible = false;
                ui.taskLIst.AddChildAt(taskCellCom.com, ui.taskLIst.numChildren);
                _taskCells.AddFirst(taskCellCom);
                //taskCellCom.tfDiamond.visible = false;
                taskCellCom.spDiamond.spineAnimation.AnimationState.SetAnimation(0, "1", false);
                taskCellCom.stateComplete.selectedPage = "lock";
                taskCellCom.tfDesc.text = item.task_title;
            }

            taskCellCom.spDiamond.visible = true;
            return taskCellCom;
        }

        private string GetImgUrl(PB_RewardMateriaType type)
        {
            if (type == PB_RewardMateriaType.PB_RewardMateriaType_Diamond)
                return "ui://common/item_mid_10001";
            else if (type == PB_RewardMateriaType.PB_RewardMateriaType_StreakFreeze)
            {
                return "ui://common/item_big_i0002";
            }
            else if (type == PB_RewardMateriaType.PB_RewardMateriaType_UnlimitedStamina)
            {
                if (GetModel<ShopModel>(ModelConsts.Shop).shopUIStyle == ShopModel.ShopUIStyle.Speak)
                {
                    return "ui://common/item_big_i0005";
                }
                else
                {
                    return "ui://common/item_big_i0003";   
                }
            }
            else if (type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_1_5 || type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_2 ||
                     type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_3)
            {
                return "ui://common/item_big_i0001";
            }

            return "";
        }

        private string GetItemDesc(PB_RewardMateriaType type, long rewardValue)
        {
            if (type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_1_5)
                return "1.5x";
            if (type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_2)
                return "2x";
            if (type == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_3)
                return "3x";
            if (type == PB_RewardMateriaType.PB_RewardMateriaType_UnlimitedStamina)
                return rewardValue + "m";
            return rewardValue.ToString();
        }

        private Action ShowRewardQueue()
        {
            if (rewardQueue.Count > 0)
            {
                var param = rewardQueue.Dequeue();
                switch (param.type)
                {
                    case SignDailyAniType.ShowProgressBar:
                        ShowProgressBar(param);
                        break;
                    case SignDailyAniType.ShowCurrencyItem:
                        PlayShowDiamond(param);
                        break;
                    case SignDailyAniType.ShowReward:
                        PlayShowReward(param);
                        break;
                    case SignDailyAniType.ShowUseAbleItem:
                        ShowUseAbleItem(param);
                        break;
                }
            }
            return null;
        }

        private void ShowProgressBar(SignDailyAniParams p)
        {
            var size = p.com.imgProgressBarBg.size;
            var startRate = (float)p.pre_value / p.max_value;
            var endRate = (float)p.cur_value / p.max_value;
            var startSize = new Vector2(p.com.imgProgressBarBg.size.x * startRate, size.y);
            var endSize = new Vector2(p.com.imgProgressBarBg.size.x * endRate, size.y);
            p.com.imgProgressBar.size = startSize;
            p.com.imgProgressBar.TweenResize(endSize, 0.5f).OnComplete(() =>
            {
                ShowRewardQueue();
            });
        }
        
        private void PlayShowDiamond(SignDailyAniParams p)
        {
            ui.spItem.visible = true;
            ui.tfDiamondBig.visible = true;
            ui.tfDiamondBig.text = "<font color=#D056FF,#7F2AD2>" + p.cur_value + "</font>";
            SoundManger.instance.PlayUI("GetGold");
            ui.spItem.spineAnimation.AnimationState.SetAnimation(0, "b1", false).Complete += entry =>
            {
                ui.spItem.spineAnimation.AnimationState.SetAnimation(0, "c1", false);
                ui.spItem.visible = false;
                ShowRewardQueue();
            };
            
            ui.tfDiamondBig.scale = new Vector2(0.8f, 0.8f);
            ui.tfDiamondBig.pivot = new Vector2(0.5f, 0.5f);
            ui.tfDiamondBig.TweenScale(new Vector2(1.5f, 1.5f), 0.4f).OnComplete(() =>
            {
                ui.tfDiamondBig.TweenScale(new Vector2(1.2f, 1.2f), 0.2f).OnComplete(() =>
                {
                    ui.tfDiamondBig.TweenScale(new Vector2(1.2f, 1.2f), 0.6f).OnComplete(() =>
                    {
                        ui.tfDiamondBig.pivot = Vector2.one;
                        ui.tfDiamondBig.visible = false;
                    });
                });
            });
        }

        private void PlayShowReward(SignDailyAniParams p)
        {
            // var aniName = p.rewardType == PB_RewardMateriaType.PB_RewardMateriaType_Diamond ? "b1" : "c1";
            p.com.spReward.spineAnimation.AnimationState.SetAnimation(0, "a1", false).Complete += entry =>
            {
                p.com.spReward.visible = false;
                p.com.imgLoader.visible = true;
                p.com.tfCnt.visible = true;
                p.com.imgLoader.scale = new Vector2(0.8f, 0.8f);
                p.com.imgLoader.pivot = new Vector2(0.5f, 0.5f);
                SoundManger.instance.PlayUI("good");
                p.com.imgLoader.TweenScale(new Vector2(1.2f, 1.2f), 0.2f).OnComplete(() =>
                {
                    p.com.imgLoader.TweenScale(new Vector2(1f, 1f), 0.2f).OnComplete(() =>
                    {
                        p.com.imgLoader.pivot = Vector2.one;
                        p.com.stateComplete.selectedPage = "yes";
                        ShowRewardQueue();
                    });
                });
            };
        }

        private void ShowUseAbleItem(SignDailyAniParams p)
        {
            var rate = "0";
            var time = 0;


            if (p.rewardType == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_1_5)
            {
                rate = "1.5";
                time = p.cur_value;
                ui.tfDesc.SetKeyArgs("item_13001_obtain_desc", rate, time);
                ui.tfTile.SetKey("item_name_i0001");
            }

            if (p.rewardType == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_2)
            {
                rate = "2";
                time = p.cur_value;
                ui.tfDesc.SetKeyArgs("item_13001_obtain_desc", rate, time);
                ui.tfTile.SetKey("item_name_i0001");
            }

            if (p.rewardType == PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_3)
            {
                rate = "3";
                time = p.cur_value;
                ui.tfDesc.SetKeyArgs("item_13001_obtain_desc", rate, time);
                ui.tfTile.SetKey("item_name_i0001");
            }
            
            if (p.rewardType == PB_RewardMateriaType.PB_RewardMateriaType_StreakFreeze)
            {
                ui.tfDesc.SetKeyArgs("item_obtained_desc_i0002", p.cur_value);
                ui.tfTile.SetKey("item_name_i0002");
            }
                        
            if (p.rewardType == PB_RewardMateriaType.PB_RewardMateriaType_UnlimitedStamina)
            {
                ui.tfDesc.SetKeyArgs("item_obtained_desc_i0003", p.cur_value);
                ui.tfTile.SetKey("item_name_i0003");
            }

            
            var sequence = DOTween.Sequence();
            // sequence.AppendInterval(0.1f);
            sequence.AppendCallback(() =>
            {
                ui.imgItemBg.visible = true;
                ui.imgItemBgTop.visible = true;
                ui.imgRight.xy = new Vector2(ui.imgBG.size.x + ui.imgBG.x, 0);
                ui.imgItemBg.alpha = 0;
                ui.imgItemBg.TweenFade(1, 0.5f);
                ui.imgItemBgTop.alpha = 0;
                ui.imgItemBgTop.TweenFade(1, 0.5f);
                ui.imgBG.TweenFade(0, 0.5f);
            });
            sequence.AppendInterval(0.1f);
            sequence.AppendCallback(() =>
            {
                ui.imgLoader.visible = true;
                ui.imgLoader.scale = new Vector2(0.5f, 0.5f);
                ui.imgLoader.url = GetImgUrl(p.rewardType);
                ui.imgLoader.TweenScale(new Vector2(0.8f, 0.8f), 0.3f).OnComplete(() =>
                {
                    ui.imgLoader.TweenScale(new Vector2(0.75f, 0.75f), 0.2f).OnComplete(() =>
                    {
                        ui.imgLoader.TweenScale(new Vector2(1f, 1f), 0.3f).OnComplete(() =>
                        {
                            ui.spRewardEffect.visible = true;
                            ui.spRewardEffect.spineAnimation.AnimationState.SetAnimation(0, "c1", false);
                            ui.imgLoader.TweenScale(new Vector2(0.9f, 0.9f), 0.2f);
                        });
                    });
                });
            });
            sequence.AppendInterval(0.2f);
            sequence.AppendCallback(() =>
            {
                ui.grpHeader.TweenMove(new Vector2(ui.imgBG.x - ui.imgBG.size.x, ui.grpHeader.y), 0.5f);
            });
            sequence.AppendInterval(0.1f);
            foreach (var cell in _taskCells)
            {
                sequence.AppendCallback(()=>
                {
                    cell.com.TweenMove(new Vector2(ui.imgBG.x - ui.imgBG.size.x, cell.com.y), 0.5f);
                });
                sequence.AppendInterval(0.1f);
            }
            sequence.AppendInterval(0.2f);
            sequence.AppendCallback(() =>
            {
                ui.imgRight.TweenMove(new Vector2(ui.imgBG.x, 0), 0.5f);
                ui.grpUseItem.visible = true;
            });
            sequence.AppendInterval(0.7f);
            sequence.AppendCallback(() =>
            {
                SoundManger.instance.PlayUI("sign_reward");
            });
            sequence.Play();
        }
        
        private void OnBtnRightConfirmClicked()
        {
            if (rewardQueue.Count <= 0)
            {
                this.GetController<SettlementController>(ModelConsts.Settlement).ShowNextView(() => { Hide(); });
                var dot = new ClickRewardConfirm();
                DataDotMgr.Collect(dot);   
            }
            else
            {
                ShowRewardQueue();
            }
        }
    }
}
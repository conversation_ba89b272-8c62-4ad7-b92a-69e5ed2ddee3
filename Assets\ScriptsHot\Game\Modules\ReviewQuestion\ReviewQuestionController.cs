﻿/*
 ****************************************************
 * 作者：ZhangXiaoWu
 * 创建时间：2024/03/06 10:29:59 星期三
 * 功能：Nothing
 ****************************************************
 */

using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using Google.Protobuf.WellKnownTypes;
using Msg.basic;
using Msg.dialog_task;
using Msg.question_process;
using Msg.speech;
using Msg.task_process;
using ScriptsHot.Game.Modules.ChatStart;
using ScriptsHot.Game.Modules.ReviewQuestion;
using ScriptsHot.Game.Modules.ReviewQuestion.Questions;
using ScriptsHot.Game.Modules.Settlement;
using ScriptsHot.Game.Modules.Task;
using ScriptsHot.Game.Modules.VideoLessonTask;
using UIBind.Record;
using UIBind.ReviewQuestion;
using UnityEngine;

// ReviewQuestion => {warmup  | Strengthen=强化练习}
public partial class ReviewQuestionController : BaseController
{
    private ReviewQuestionModel _reviewModel => GetModel<ReviewQuestionModel>(ModelConsts.ReviewQuestionModel);
    private RecommendCardModel _RecommendCardModel => GetModel<RecommendCardModel>(ModelConsts.RecommendCard);
    private ChatModel _chatModel => GetModel<ChatModel>(ModelConsts.Chat);
    private TaskController _taskController => GetController<TaskController>(ModelConsts.Task);
    private SettlementController _settlementController => GetController<SettlementController>(ModelConsts.Settlement);
    public ReviewQuestionController() : base(ModelConsts.ReviewQuestionModel) { }

    private ChatController _chatcontroller => this.GetController<ChatController>(ModelConsts.Chat);

    // private LearnPathModel _learnPathModel => GetModel<LearnPathModel>(ModelConsts.LearnPath);

    public override void OnUIInit()
    {
        RegisterUI(new ReviewQuestionUI(UIConsts.ReviewQuestionUI));
        RegisterUI(new ReviewQuestionCardUI(UIConsts.ReviewQuestionCardUI));

        RegisterModel(new ReviewQuestionModel());
        RegisterUI(new SpeechBulletUI(UIConsts.SpeechBulletUI));

        CompVideoTask.Bind();
        // SpeakRecordBtn.Bind();
        QuestionCardList.Bind();
        ReviewQuestionGapFilliingCom.Bind();
        // ReviewQuestionFllowCom.Bind();
        ReviewQuestionInstruction.Bind();
        ReviewQuestionRoundEnd.Bind();
    }

    public override void OnInit()
    {
        MsgManager.instance.RegisterCallBack<SC_GetDailyTaskInfoAck>(this.GetDailyTaskInfo);
        MsgManager.instance.RegisterCallBack<SC_ChangeMarkInfoAck>(this.ChangeMarkInfo);
        MsgManager.instance.RegisterCallBack<SC_StartBatchQuestionDialogAck>(this.StartBatchQuestionDialog);
    }

    public override void OnNetReConn()
    {
    }

    public PB_CourseLearnPathParams MainPathParams { get; private set; }
    
    public void OnEnter(PB_DialogMode dialogMode, long taskId, long avatarId,PB_CourseLearnPathParams mainPathParam)
    {
        
        MainPathParams = mainPathParam;
        //MainPathParams.course_type = PB_CourseTypeEnum.CTSession;//todo 附加额外属性 0503 add by raybit

        _chatModel.SetChatMode(dialogMode);
        //_reviewModel.ClientAsrAnswers = new List<PB_ClientAsrAnswer>(); 不懂 为什么写这里
        SendStartBatchQuestionDialog(dialogMode, taskId);
        GameUtils.SetCameraCullingMask(LayerMask.NameToLayer("chat"), false);

        Notifier.instance.RegisterNotification(NotifyConsts.SettlementReqDialogResultFinishEvent, ClearView);
        Notifier.instance.RegisterNotification(NotifyConsts.DoTopLeftBack, this.OnTopLeftBack);
    }


    //退出处理的核心入口，待后续重构
    //isFinish =true代表正常完成时，=false代表中途左上角退出
    public async void OnExit(bool isFinish = true)
    {

        Debug.Log("OnExit isFinish:" + isFinish);

        //step1 模型类 后置处理or清理
        _reviewModel.OnExit();
        _chatModel.SetChatMode(PB_DialogMode.MNone);
        // this.GetModel<LearnPathModel>(ModelConsts.LearnPath).OnRefreshProgress = true;//标记 再次刷新路径

        //step2 数据类请求
        await SendSubmitBatchQuestionDialog(_chatModel.chatMode, isFinish);//中途退出也发的原因是为了多捞数据？


        //step3 公共后处理
        UnlockScene();//3D世界中的清理动作
        //隐藏玩家?
        //todo:后续应该转移or消除这类逻辑
        GameUtils.SetCameraCullingMask(LayerMask.NameToLayer("player"), true);
        GameUtils.SetCameraCullingMask(LayerMask.NameToLayer("chat"), true);



        //step4 逻辑分拆
        if (isFinish)
        {
            //完成退出
            GetController<ChatController>(ModelConsts.Chat).ChangeState(ChatState.Settlement);


            //不立即触发 ClearView();
            //等待RequestDialogResult的resp中触发的 SettlementReqDialogResultFinishEvent 来调用ClearView
        }
        else
        {
            //中途退出

            DotDialogue(true);//中途退出前 弹窗点确认的埋点
            GetController<ChatController>(ModelConsts.Chat).ChangeState(ChatState.None);
            ClearView();
            JumpActions();
        }

        //step5 dispose
        //建议不清理，因为前面如果 请求结果中间网络出错，可能还需要点左上角退出：Notifier.instance.UnRegisterNotification(NotifyConsts.DoTopLeftBack, this.OnTopLeftBack);//取消监听

    }

    private void ClearView(string s, object body)
    {
        this.ClearView();
    }

    private void ClearView()
    {
        GetUI(UIConsts.ReviewQuestionUI).Hide();
        GetUI(UIConsts.ProgressStrengthen).Hide();
        GetUI<RecordUI>(UIConsts.RecordUI).ClearStatusAndTimer();
        GetUI(UIConsts.RecordUI).Hide();
        Notifier.instance.UnRegisterNotification(NotifyConsts.DoTopLeftBack, this.OnTopLeftBack);//取消监听
        Notifier.instance.UnRegisterNotification(NotifyConsts.SettlementReqDialogResultFinishEvent,
            this.ClearView); //取消监听
    }

    //实质性退出的
    private void JumpActions()
    {
        Notifier.instance.SendNotification(NotifyConsts.ExitDialog);//退dialog时，homepage要求相机视角调整

        //这是warmup题型在新首页 退出流程中最重要的补丁
        //OnExit->ExitReviewQuestion -> ToGameIdleState -> IdleState.OnEnter -> isHomepage & reset view
        Notifier.instance.SendNotification(ModelConsts.ToGameIdleState);
    }

    public void SendAsrAudio(long uuid)
    {
        MsgManager.instance.SendMsg(new CS_UploadAsrAudioReq()
        {
            audio = GMicrophoneManager.instance.GetByteString(),
            record_id = uuid,
        });

        PB_ClientAsrAnswer answer = new PB_ClientAsrAnswer();
        var questionObj = _reviewModel.CurrentQuestion();
        answer.question_id = questionObj?.question.question_id ?? 0;
        answer.asr_id = _reviewModel.asrRecordID;
        answer.round_id = _reviewModel.questionIndex;
        answer.asr_content = _reviewModel.asrcontent;
        answer.answer = _reviewModel.answer;
        _reviewModel.AddAnswerList(answer);

        ProcessNextQuestion();
    }
    //===================================协议回包======================================
    //创建对话回包

    private void StartBatchQuestionDialog(SC_StartBatchQuestionDialogAck msg)
    {
        GetUI<WarmupLoadingCutInUI>(UIConsts.WarmupLoadingCutInUI)?.TryHide(0.5f);  // 临时挡一下结束

        if (msg.code != PB_Code.Normal)
        {
            DealErrorData("SC_StartBatchQuestionDialogAck");
            return;
        }

        _reviewModel.SetQuestionlist(msg.data);

        _chatModel.SetDialogId(msg.data.dialog_id);
        DotPracticeManager.Instance.recordId = msg.data.task_record_id;
        DotPracticeManager.Instance.levelType = PB_LevelTypeEnum.LTWarmUp;


        GetUI(UIConsts.ReviewQuestionUI).Show();//展示强化
        

        //新埋点：任务开启
        DataDotCutDialogueStart dot = new DataDotCutDialogueStart();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        DataDotMgr.Collect(dot);
    }

    private void SubmitBatchQuestionDialog(SC_SubmitBatchQuestionDialogAck msg)
    {
        if (msg.code != PB_Code.Normal)
        {
            DealErrorData("SC_SubmitBatchQuestionDialogAck");
            return;
        } 
    }

    //获取每日强化题型数据回复15012
    private void GetDailyTaskInfo(SC_GetDailyTaskInfoAck msg)
    {
        //显示入口状态
        if (msg.code != PB_Code.Normal)
        {
            Debug.LogError("请求每日强化题型失败");
        }
        _reviewModel.SetDailyTaskInfo(msg.data);
    }

    private void ChangeMarkInfo(SC_ChangeMarkInfoAck mgs)
    {
        _reviewModel.SetclickWordGuide(mgs.data.mark_info.is_click_word_guide);
        SendNotification(ReviewCallEvent.OnAutoSpeak, mgs.data.mark_info.is_question_auto);
    }

    private void DealErrorData(string address)
    {
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open(I18N.inst.MoStr("common_chat_error") + "\n" + address, () =>
        {
            DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
            dataDotCutApiCase.Dialogue_id = _chatModel.dialogId;
            dataDotCutApiCase.Task_id = _RecommendCardModel.CurTaskID;
            dataDotCutApiCase.Dialogue_step = _reviewModel.next_step_id;
            dataDotCutApiCase.Task_mode = (int)PB_DialogMode.Intensify;
            dataDotCutApiCase.Api_address = address;
            dataDotCutApiCase.Cut_mode = 1;
            DataDotMgr.Collect(dataDotCutApiCase);

            OnExit(false);
        }, null, 1, null, "chat_btn_cancel");
    }

    //===================================协议请求======================================
    private void SendStartBatchQuestionDialog(PB_DialogMode dialogMode, long taskId)
    {
        var msg = new CS_StartBatchQuestionDialogReq
        {

            task_id = taskId,
            dialog_mode = dialogMode,
            dialog_source = _chatcontroller.curSourceEnum,
           
            // learn_path_params = _learnPathModel.GetPathParams()
        };
        
        MsgManager.instance.SendMsg(msg, (a, n) => DealErrorData("CS_StartBatchQuestionDialogReq"));
        GetUI<WarmupLoadingCutInUI>(UIConsts.WarmupLoadingCutInUI)?.Show();  // 临时挡一下
    }

    // bool isFinishFlag = false;
    private async UniTask SendSubmitBatchQuestionDialog(PB_DialogMode dialogMode,bool isFinish)
    {
        //_reviewModel.AddRoundId();
        var msg = new CS_SubmitBatchQuestionDialogReq
        {

            dialog_id = _reviewModel.dialogId,
            dialog_mode = dialogMode,
            is_complete = isFinish,
            course_learn_path_params = MainPathParams 
            // learn_path_params = _learnPathModel.GetPathParams(),
        };
        if (_reviewModel.ClientAsrAnswers != null && _reviewModel.ClientAsrAnswers.Count > 0) {
            msg.answer_list.AddRange(_reviewModel.ClientAsrAnswers);
        }
        // this.isFinishFlag = isFinish;
        _ = await MsgManager.instance.SendAsyncMsg<SC_SubmitBatchQuestionDialogAck>(msg, (a, n) => DealErrorData("CS_SubmitBatchQuestionDialogReq"));
        

        if (isFinish)
        {
            VFDebug.Log("SendSubmitBatchQuestionDialog");
            GetController<SettlementController>(ModelConsts.Settlement).RequestDialogResult(_reviewModel.dialogId,
                _chatModel.chatMode, _chatModel.curRoundId, _chatModel.task_record_id, MainPathParams, _chatModel.avatarId,
                _chatcontroller.curTaskId);
        }     
    }

    public void ProcessNextQuestion()
    {
        _reviewModel.SetQueResult(new PB_DialogTaskQuestionResult());
        var question = _reviewModel.crntQuestion;
        bool isFinish = false;
        if (question == null)
        {
            isFinish = true;
        }

        // todo next question?
        SendNotification(ReviewCallEvent.OnReviewQuestionIsFinish, isFinish);
    }

    // public void GotoNextQuestion()
    // {
    //     _reviewModel.NextQuestion();
    // }

    public void HandleAsrComplete(ASRResultData asrData)
    {
        // 设置模型数据
        _reviewModel.SetAsrRecordID(asrData.UUID);
        _reviewModel.SetAssessmentID(asrData.UUID);
        _reviewModel.SetAsrContent(asrData.AsrContent);

        // 发送音频数据
        SendAsrAudioData(asrData);
    }

    private void SendAsrAudioData(ASRResultData asrData)
    {
        MsgManager.instance.SendMsg(new CS_UploadAsrAudioReq()
        {
            audio = asrData.AudioData,
            record_id = asrData.UUID
        });

        // 构建并添加答案
        var answer = new PB_ClientAsrAnswer
        {
            question_id = _reviewModel.CurrentQuestion()?.question.question_id ?? 0,
            asr_id = asrData.UUID,
            round_id = _reviewModel.questionIndex,
            asr_content = asrData.AsrContent,
            answer = _reviewModel.answer
        };

        _reviewModel.AddAnswerList(answer);
    }
}
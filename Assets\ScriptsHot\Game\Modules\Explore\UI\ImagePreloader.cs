﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ScriptsHot.Game.Modules.Scene.Utils;
using UIBind.Explore.Item;
using UnityEngine;
using UnityEngine.Networking;
using YooAsset;
using Object = UnityEngine.Object;

public class ImagePreloader : SingletonClass<ImagePreloader>
{
    private static Dictionary<string, Texture2D> imageCache = new Dictionary<string, Texture2D>();
    private static List<string> recentlyUsed = new List<string>();
    
    // 缓存配置
    // 最大缓存图片数量
    private const int MAX_CACHE_SIZE = 10; 

    
    // 添加内存监控
    // 估计的内存占用(字节)
    private long estimatedMemoryUsage = 0; 
    // 最大内存使用量(50MB)
    private const long MAX_MEMORY_USAGE = 50 * 1024 * 1024;
    
    // 累计清理图片计数
    private int _totalCleanedImages = 0;
    // 清理阈值，超过此值执行GC
    private const int GC_THRESHOLD = 15;

    private int _preloadCount = 0;
    
    private Dictionary<string, AssetHandle> _assetHandles = new Dictionary<string, AssetHandle>();
    
    private ImagePreloader()
    {
    }
    public void Update()
    {
    }

    /// <summary>
    /// 检查并维护缓存大小
    /// </summary>
    private void CheckCacheSize()
    {
        // 如果缓存超过限制或内存使用过多，清理旧图片
        if (imageCache.Count > MAX_CACHE_SIZE || estimatedMemoryUsage > MAX_MEMORY_USAGE)
        {
            //根据预载个数 第一次 5个 平时3个 ，所以让内存 最少有6个 保存正显示一定正确
            int clearCount = imageCache.Count - 6 ; 
            if (clearCount > 0)
            {
                CleanCache(clearCount);
            }
        }
    }
    
    /// <summary>
    /// 清理指定数量的最少使用图片
    /// </summary>
    /// <param name="count"></param>
    private void CleanCache(int count)
    {
        VFDebug.Log($"ImagePreloader 清理图片缓存，当前缓存数量：{imageCache.Count}，将清理：{count}张");
        
        // 实际清理的图片数量
        int actualCleaned = 0;
        
        // 获取最少使用的图片
        var leastUsedKeys = recentlyUsed
            .Take(count)
            .ToList();
        
        // 清理这些图片
        foreach (var key in leastUsedKeys)
        {
            if (imageCache.TryGetValue(key, out Texture2D texture))
            {
                
                // 区分YooAsset加载的资源和网络下载的资源
                bool isYooAssetPath = key.StartsWith("Assets/");
                // 估算释放的内存
                long textureSize = CalculateTextureMemory(texture);
                estimatedMemoryUsage -= textureSize;
                
                if (isYooAssetPath)
                {
                    VFDebug.Log($"ImagePreloader 释放YooAsset资源: {key}, 估计释放内存: {textureSize/1024/1024}MB");
                    // 释放YooAsset资源句柄
                    if (_assetHandles.TryGetValue(key, out AssetHandle handle))
                    {
                        handle.Release();
                        _assetHandles.Remove(key);
                        VFDebug.Log($"ImagePreloader 释放YooAsset句柄: {key}");
                    }
                }
                else
                {
                    VFDebug.Log($"ImagePreloader 释放网络图片: {key}, 估计释放内存: {textureSize/1024/1024}MB");
                    Object.Destroy(texture);
                }
                
                // 从缓存字典中移除
                imageCache.Remove(key);
                actualCleaned++;
            }
            recentlyUsed.Remove(key);
        }
        
        // 更新累计清理计数
        _totalCleanedImages += actualCleaned;
        
        VFDebug.Log($"ImagePreloader 缓存清理完成，本次清理：{actualCleaned}张，累计清理：{_totalCleanedImages}张，剩余缓存数量：{imageCache.Count}，估计内存占用：{estimatedMemoryUsage/1024/1024}MB");
        
        // 检查是否需要执行GC
        if (_totalCleanedImages >= GC_THRESHOLD)
        {
            VFDebug.Log($"ImagePreloader 累计清理图片数量达到阈值({_totalCleanedImages} >= {GC_THRESHOLD})，执行GC");
            Resources.UnloadUnusedAssets();
            System.GC.Collect();
            _totalCleanedImages = 0;
        }
    }
    
    /// <summary>
    /// 更新最近使用记录
    /// </summary>
    /// <param name="url"></param>
    private void UpdateRecentlyUsed(string url)
    {
        // 从列表中移除
        recentlyUsed.Remove(url);
        // 添加到列表末尾(最近使用)
        recentlyUsed.Add(url);
    }
    
    /// <summary>
    /// 计算纹理内存占用
    /// </summary>
    /// <param name="texture"></param>
    /// <returns></returns>
    private long CalculateTextureMemory(Texture2D texture)
    {
        return (long)texture.width * texture.height * 4;
    }
    
    /// <summary>
    /// 预加载图片
    /// </summary>
    /// <param name="dataList"></param>
    /// <param name="firstIndex"></param>
    /// <param name="preLoadCount"></param>
    public async void PreloadImages(List<ExploreItemData> dataList, int firstIndex, Dictionary<int,GameObject> parent,int preLoadCount = 3 )
    {
        _preloadCount = preLoadCount;
        int start = Mathf.Max(0, firstIndex - _preloadCount);
        int end = Mathf.Min(dataList.Count - 1, firstIndex + _preloadCount);

        for (int i = start; i <= end; i++)
        {
            bool is3d = dataList[i].Data.dialogTaskPreloadData.avatar.is3D;
           
            string url = dataList[i].Data.dialogTaskPreloadData.scene.bgPicUrl;
            if (is3d)
            {
                url = dataList[i].Data.dialogTaskPreloadData.scene.bgPicTag;
                string backUrl = ResUtils.GetExploreBgImgPath(url);
                if (!imageCache.ContainsKey(backUrl)) 
                {
                    // VFDebug.Log($"ImagePreloader [PreloadBackImages] 预载背景  url: {backUrl}");
                    LoadBackAsync(backUrl);
                }
                else
                {
                    // 更新使用记录
                    UpdateRecentlyUsed(backUrl);
                }
                parent.TryGetValue(i, out GameObject go);
                LightManager.Instance.LoadLightForItem(url,go,enable:false);
            }
            else
            {
                if (!imageCache.ContainsKey(url)) 
                {
                    // VFDebug.LogError($"ImagePreloader [PreloadImages] 预载图片  url: {url}");
                    LoadImageAsync(url);
                }
                else
                {
                    // 更新使用记录
                    UpdateRecentlyUsed(url);
                }
            }
        }
    }

    /// <summary>
    /// 异步加载图片
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    private async Task<Texture2D> LoadImageAsync(string url)
    {
        using (UnityWebRequest request = UnityWebRequestTexture.GetTexture(url))
        {
            var operation = request.SendWebRequest();
            while (!operation.isDone) await Task.Yield(); // 等待加载完成

            if (request.result != UnityWebRequest.Result.Success)
            {
                VFDebug.LogError($"ImagePreloader 图片加载失败: {url}, Error: {request.error}");
                return null;
            }

            Texture2D texture = DownloadHandlerTexture.GetContent(request);
            texture.Compress(true);
            
            // 估算并记录内存增加
            long textureSize = CalculateTextureMemory(texture);
            estimatedMemoryUsage += textureSize;
            
            // 缓存管理
            imageCache[url] = texture;
            UpdateRecentlyUsed(url);
            
            // 检查是否需要清理
            CheckCacheSize();
            
            return texture;
        }
    }

    public async Task<Texture2D> GetImageAsync(string url)
    {
        if (imageCache.TryGetValue(url, out var texture))
        {
            // 更新使用记录
            UpdateRecentlyUsed(url);
            return texture;
        }
        return await LoadImageAsync(url);
    }
    
    // 异步加载背景
    private async Task<Texture2D> LoadBackAsync(string url)
    {
        AssetHandle handle = YooAssets.LoadAssetAsync<Texture2D>(url);
        _assetHandles[url] = handle;
        
        try
        {
            while (!handle.IsDone)
            {
                await Task.Yield();
            }
            
            if (handle.Status != EOperationStatus.Succeed)
            {
                _assetHandles.Remove(url);
                handle.Release();
                return null;
            }
            
            Texture2D texture = handle.AssetObject as Texture2D;
            if (texture == null)
            {
                _assetHandles.Remove(url);
                handle.Release();
                return null;
            }
            
            // 估算并记录内存增加
            long textureSize = CalculateTextureMemory(texture);
            estimatedMemoryUsage += textureSize;
            
            imageCache[url] = texture;
            UpdateRecentlyUsed(url);
            
            // 检查是否需要清理
            CheckCacheSize();
            
            return texture;
        }
        catch (Exception e)
        {
            if (_assetHandles.ContainsKey(url))
            {
                _assetHandles.Remove(url);
                handle.Release();
            }
            return null;
        }
    }
    
    /// <summary>
    /// 获取背景
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    public async Task<Texture2D> GetBackImageAsync(string url)
    {
        if (string.IsNullOrEmpty(url))
        {
            VFDebug.LogError("ImagePreloader GetBackImageAsync: url为空");
            return null;
        }
        
        if (imageCache.TryGetValue(url, out var texture))
        {
            // 更新使用记录
            UpdateRecentlyUsed(url);
            return texture;
        }
        
        return await LoadBackAsync(url);
    }
    
    // 低内存处理
    public void LowMemory()
    {
        VFDebug.LogWarning("ImagePreloader 收到系统低内存警告，清理图片缓存");
        CheckCacheSize();
    }
}

﻿
using ScriptsHot.Game.Modules.Explore.ExploreType.Base;

using ScriptsHot.Game.Modules.Procedure;

using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.Explore.ExploreType
{
    /// <summary>
    /// 手动语音模式
    /// </summary>
    public class ExploreActiveType:ExploreTypeBase
    {
        
        public override void Init(ExploreParam param)
        {
            _param = param;
            Type = ExploreOperationType.Active;
        }
        
        public override void Start()
        {
            base.Start();
        }

        public override void Exit()
        {
            base.Exit();
        }

        public override void AddEvent()
        {
            Notifier.instance.RegisterNotification(NotifyConsts.DoTopLeftBack,DoTopLeftBack);
            Notifier.instance.RegisterNotification(NotifyConsts.DoTopLeftGoOn,DoTopLeftGoOn);
        }

        public override void RemoveEvent()
        {
            Notifier.instance.UnRegisterNotification(NotifyConsts.DoTopLeftBack,DoTopLeftBack);
            Notifier.instance.UnRegisterNotification(NotifyConsts.DoTopLeftGoOn,DoTopLeftGoOn);
        }
        

        public override void OnNewWorldAudioStart()
        {
            //显示队列中的 脚手架
            Notifier.instance.SendNotification(NotifyConsts.procedure_do_scaffoid_dram);
            Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
            Notifier.instance.SendNotification(NotifyConsts.ExploreSoundStop);
        }

        private void DoTopLeftBack(string name, object body)
        {
            // if (_beginStep == ExploreStep.SceneDescStep)
            // {
            //     Notifier.instance.SendNotification(NotifyConsts.procedure_main_pause);
            // }
            // else if (_beginStep == ExploreStep.AvatarStep)
            // {
            //     //显示队列中的 脚手架
            //     Notifier.instance.SendNotification(NotifyConsts.procedure_do_scaffoid_dram);
            //     //先清空队列，有的drama 回通过音频结束执行 finish
            //     Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
            //     //再执行音频停止 
            //     Notifier.instance.SendNotification(NotifyConsts.StopStreamAudioTTs);
            // }
            // else if (_beginStep == ExploreStep.Player)
            // {
            //     Notifier.instance.SendNotification(NotifyConsts.StopStreamAudioTTs);
            //     Notifier.instance.SendNotification(NotifyConsts.procedure_main_pause);
            // }
            // else
            // {
            //     Notifier.instance.SendNotification(NotifyConsts.StopStreamAudioTTs);
            //     ProcedureManager.instance.Clear();
            // }
        }
        
        private void DoTopLeftGoOn(string name, object body)
        {
            // if (_beginStep == ExploreStep.SceneDescStep)
            // {
            //     VFDebug.Log("DoTopLeftGoOn---------------skip");
            //     ProcedureManager.instance.Skip();
            // }
            // else
            // {
            //     ProcedureManager.instance.Resume();
            // }
        }
 

        protected void OnAsrCompleteCallBack(ASRCompleteVO aSRCompleteVO)
        {
       
        }
        
        
        /// <summary>
        /// 点击确认按钮 发送数据
        /// </summary>
        /// <param name="aSRCompleteVO"></param>
        private void OnSendClickUserSay(ASRCompleteVO aSRCompleteVO)
        {
            // CS_DialogTaskMsgHandleReq msg = new CS_DialogTaskMsgHandleReq();
            // msg.dialog_id = _controller.CurChat.DialogTaskAck.data.dialog_id;
            // msg.bubble_id = _controller.CurChat.CurBubbleId;
            // msg.req_biz_type = PB_UpMsgBizType.UserSay;
            // msg.task_mode = _controller.CurDialogMode;
            // PB_DialogTask_UserSay_ReqFields taskUserSayReqFields = new PB_DialogTask_UserSay_ReqFields();
            // taskUserSayReqFields.dialog_id = _controller.CurChat.DialogTaskAck.data.dialog_id;
            // taskUserSayReqFields.bubble_id = _controller.CurChat.CurBubbleId;
            // taskUserSayReqFields.round_id = _controller.Model.CurRoundId;
            // taskUserSayReqFields.speech_id = aSRCompleteVO.assessmentID;
            //
            // taskUserSayReqFields.asr_id = aSRCompleteVO.asrRecordID;
            // taskUserSayReqFields.task_record_id = _controller.CurChat.DialogTaskAck.data.task_record_id;
            // taskUserSayReqFields.step_id = _controller.Model.GetStepIdByRoundId(_controller.Model.CurRoundId);
            //
            // taskUserSayReqFields.task_id = _controller.CurChat.CurChatInfo.taskId;
            // taskUserSayReqFields.avatar_id = _controller.CurChat.CurChatInfo.avatarId;
            //
            // taskUserSayReqFields.time_zone_index = TimeExt.GetTimeZoneCode();
            // taskUserSayReqFields.content = aSRCompleteVO.asrText;
            // taskUserSayReqFields.difficulty = PB_DialogDifficultyEnum.DifficultyNormal;
            //
            // // taskUserSayReqFields.goal_record_id = _chatModel.goal_record_id;
            // msg.learn_path_params = GameEntry.LoginC.GetModel<LearnPathModel>(ModelConsts.LearnPath).GetPathParams();
            // msg.user_say_data = taskUserSayReqFields;
            // //step++;
            // CommonUtils.Instance.TimeBegin("CS_DialogTaskMsgHandleReq");
            // MsgManager.instance.SendMsg(msg,(a,b)=>_controller.DealErrorData("CS_DialogTaskMsgHandleReq"));
            // this._controller.GetUI<RecordUI>(UIConsts.RecordUI).HideRecordButton();
        }
        
        protected void OnAsrFailCallBack(ASRFailVO aSRFailVO)
        {
            // aSRFailVO.desp = "PlayerTutor";
            // base.OnAsrFailCallBack(aSRFailVO);
            // this._controller.GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("common_toast",true);
            // OnCancelClick();
            //
            // if (aSRFailVO.status == ASRStatus.TimeShort)
            // {
            //     return;
            // }
            //
            // DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
            // dataDotCutApiCase.Dialogue_id = _controller.CurChat.DialogTaskAck.data.dialog_id;
            // dataDotCutApiCase.Task_id = _controller.CurChat.CurChatInfo.taskId;
            // dataDotCutApiCase.Dialogue_step = _controller.Model.GetStepIdByRoundId(_controller.Model.CurRoundId);
            // dataDotCutApiCase.Task_mode = (int)_controller.CurDialogMode;
            // dataDotCutApiCase.Api_address = "StartASRStream";
            // dataDotCutApiCase.Extra = aSRFailVO.status + "_" + aSRFailVO.desp + "_" + aSRFailVO.extra;
            // dataDotCutApiCase.Cut_mode = 3;
            // DataDotMgr.Collect(dataDotCutApiCase);
        }
        
        //点击取消按钮 取消发送数据
        private void OnCancelClick()
        {
            // _controller.OnCancelClick();
        }
    }
}
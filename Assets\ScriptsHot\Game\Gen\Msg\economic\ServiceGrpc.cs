// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/economic/service.proto
// </auto-generated>
#pragma warning disable 0414, 1591, 8981, 0612
#region Designer generated code

using grpc = global::Grpc.Core;

namespace Msg.economic {
  /// <summary>
  /// To Client（iOS）
  /// </summary>
  public static partial class EconomicService
  {
    static readonly string __ServiceName = "EconomicService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_EconomicCoinGetBalanceReq> __Marshaller_CS_EconomicCoinGetBalanceReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_EconomicCoinGetBalanceReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_EconomicCoinGetBalanceAck> __Marshaller_SC_EconomicCoinGetBalanceAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_EconomicCoinGetBalanceAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_GetEconomicInfoReq> __Marshaller_CS_GetEconomicInfoReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_GetEconomicInfoReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_GetEconomicInfoAck> __Marshaller_SC_GetEconomicInfoAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_GetEconomicInfoAck.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.CS_EconomicCoinGetBalanceReq, global::Msg.economic.SC_EconomicCoinGetBalanceAck> __Method_GetCoinBalance = new grpc::Method<global::Msg.economic.CS_EconomicCoinGetBalanceReq, global::Msg.economic.SC_EconomicCoinGetBalanceAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetCoinBalance",
        __Marshaller_CS_EconomicCoinGetBalanceReq,
        __Marshaller_SC_EconomicCoinGetBalanceAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.CS_GetEconomicInfoReq, global::Msg.economic.SC_GetEconomicInfoAck> __Method_GetEconomicInfo = new grpc::Method<global::Msg.economic.CS_GetEconomicInfoReq, global::Msg.economic.SC_GetEconomicInfoAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetEconomicInfo",
        __Marshaller_CS_GetEconomicInfoReq,
        __Marshaller_SC_GetEconomicInfoAck);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.economic.ServiceReflection.Descriptor.Services[0]; }
    }

    /// <summary>Base class for server-side implementations of EconomicService</summary>
    [grpc::BindServiceMethod(typeof(EconomicService), "BindService")]
    public abstract partial class EconomicServiceBase
    {
      /// <summary>
      /// 获取金币余额
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SC_EconomicCoinGetBalanceAck> GetCoinBalance(global::Msg.economic.CS_EconomicCoinGetBalanceReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 获取用户经济信息
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SC_GetEconomicInfoAck> GetEconomicInfo(global::Msg.economic.CS_GetEconomicInfoReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for EconomicService</summary>
    public partial class EconomicServiceClient : grpc::ClientBase<EconomicServiceClient>
    {
      /// <summary>Creates a new client for EconomicService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public EconomicServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for EconomicService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public EconomicServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected EconomicServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected EconomicServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// 获取金币余额
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_EconomicCoinGetBalanceAck GetCoinBalance(global::Msg.economic.CS_EconomicCoinGetBalanceReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetCoinBalance(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取金币余额
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_EconomicCoinGetBalanceAck GetCoinBalance(global::Msg.economic.CS_EconomicCoinGetBalanceReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetCoinBalance, null, options, request);
      }
      /// <summary>
      /// 获取金币余额
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_EconomicCoinGetBalanceAck> GetCoinBalanceAsync(global::Msg.economic.CS_EconomicCoinGetBalanceReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetCoinBalanceAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取金币余额
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_EconomicCoinGetBalanceAck> GetCoinBalanceAsync(global::Msg.economic.CS_EconomicCoinGetBalanceReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetCoinBalance, null, options, request);
      }
      /// <summary>
      /// 获取用户经济信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_GetEconomicInfoAck GetEconomicInfo(global::Msg.economic.CS_GetEconomicInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetEconomicInfo(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户经济信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_GetEconomicInfoAck GetEconomicInfo(global::Msg.economic.CS_GetEconomicInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetEconomicInfo, null, options, request);
      }
      /// <summary>
      /// 获取用户经济信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_GetEconomicInfoAck> GetEconomicInfoAsync(global::Msg.economic.CS_GetEconomicInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetEconomicInfoAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户经济信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_GetEconomicInfoAck> GetEconomicInfoAsync(global::Msg.economic.CS_GetEconomicInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetEconomicInfo, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override EconomicServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new EconomicServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(EconomicServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GetCoinBalance, serviceImpl.GetCoinBalance)
          .AddMethod(__Method_GetEconomicInfo, serviceImpl.GetEconomicInfo).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, EconomicServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GetCoinBalance, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.CS_EconomicCoinGetBalanceReq, global::Msg.economic.SC_EconomicCoinGetBalanceAck>(serviceImpl.GetCoinBalance));
      serviceBinder.AddMethod(__Method_GetEconomicInfo, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.CS_GetEconomicInfoReq, global::Msg.economic.SC_GetEconomicInfoAck>(serviceImpl.GetEconomicInfo));
    }

  }
  /// <summary>
  /// To Server（内部接口）
  /// </summary>
  public static partial class EconomicInnerService
  {
    static readonly string __ServiceName = "EconomicInnerService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GenTradeIdReq> __Marshaller_SS_GenTradeIdReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GenTradeIdReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GenTradeIdAck> __Marshaller_SS_GenTradeIdAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GenTradeIdAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_AddCoinReq> __Marshaller_SS_AddCoinReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_AddCoinReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_AddCoinAck> __Marshaller_SS_AddCoinAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_AddCoinAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_DeductCoinReq> __Marshaller_SS_DeductCoinReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_DeductCoinReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_DeductCoinAck> __Marshaller_SS_DeductCoinAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_DeductCoinAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_SettleCoinReq> __Marshaller_SS_SettleCoinReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_SettleCoinReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_SettleCoinAck> __Marshaller_SS_SettleCoinAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_SettleCoinAck.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GenTradeIdReq, global::Msg.economic.SS_GenTradeIdAck> __Method_GenTradeID = new grpc::Method<global::Msg.economic.SS_GenTradeIdReq, global::Msg.economic.SS_GenTradeIdAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GenTradeID",
        __Marshaller_SS_GenTradeIdReq,
        __Marshaller_SS_GenTradeIdAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_AddCoinReq, global::Msg.economic.SS_AddCoinAck> __Method_AddCoin = new grpc::Method<global::Msg.economic.SS_AddCoinReq, global::Msg.economic.SS_AddCoinAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "AddCoin",
        __Marshaller_SS_AddCoinReq,
        __Marshaller_SS_AddCoinAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_DeductCoinReq, global::Msg.economic.SS_DeductCoinAck> __Method_DeductCoin = new grpc::Method<global::Msg.economic.SS_DeductCoinReq, global::Msg.economic.SS_DeductCoinAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "DeductCoin",
        __Marshaller_SS_DeductCoinReq,
        __Marshaller_SS_DeductCoinAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_SettleCoinReq, global::Msg.economic.SS_SettleCoinAck> __Method_SubscribeCoinSettlement = new grpc::Method<global::Msg.economic.SS_SettleCoinReq, global::Msg.economic.SS_SettleCoinAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "SubscribeCoinSettlement",
        __Marshaller_SS_SettleCoinReq,
        __Marshaller_SS_SettleCoinAck);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.economic.ServiceReflection.Descriptor.Services[1]; }
    }

    /// <summary>Base class for server-side implementations of EconomicInnerService</summary>
    [grpc::BindServiceMethod(typeof(EconomicInnerService), "BindService")]
    public abstract partial class EconomicInnerServiceBase
    {
      /// <summary>
      /// 生成交易id
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GenTradeIdAck> GenTradeID(global::Msg.economic.SS_GenTradeIdReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 增加金币
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_AddCoinAck> AddCoin(global::Msg.economic.SS_AddCoinReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 扣减金币
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_DeductCoinAck> DeductCoin(global::Msg.economic.SS_DeductCoinReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 金币结算
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_SettleCoinAck> SubscribeCoinSettlement(global::Msg.economic.SS_SettleCoinReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for EconomicInnerService</summary>
    public partial class EconomicInnerServiceClient : grpc::ClientBase<EconomicInnerServiceClient>
    {
      /// <summary>Creates a new client for EconomicInnerService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public EconomicInnerServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for EconomicInnerService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public EconomicInnerServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected EconomicInnerServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected EconomicInnerServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// 生成交易id
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GenTradeIdAck GenTradeID(global::Msg.economic.SS_GenTradeIdReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GenTradeID(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 生成交易id
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GenTradeIdAck GenTradeID(global::Msg.economic.SS_GenTradeIdReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GenTradeID, null, options, request);
      }
      /// <summary>
      /// 生成交易id
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GenTradeIdAck> GenTradeIDAsync(global::Msg.economic.SS_GenTradeIdReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GenTradeIDAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 生成交易id
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GenTradeIdAck> GenTradeIDAsync(global::Msg.economic.SS_GenTradeIdReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GenTradeID, null, options, request);
      }
      /// <summary>
      /// 增加金币
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_AddCoinAck AddCoin(global::Msg.economic.SS_AddCoinReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return AddCoin(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 增加金币
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_AddCoinAck AddCoin(global::Msg.economic.SS_AddCoinReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_AddCoin, null, options, request);
      }
      /// <summary>
      /// 增加金币
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_AddCoinAck> AddCoinAsync(global::Msg.economic.SS_AddCoinReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return AddCoinAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 增加金币
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_AddCoinAck> AddCoinAsync(global::Msg.economic.SS_AddCoinReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_AddCoin, null, options, request);
      }
      /// <summary>
      /// 扣减金币
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_DeductCoinAck DeductCoin(global::Msg.economic.SS_DeductCoinReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return DeductCoin(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 扣减金币
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_DeductCoinAck DeductCoin(global::Msg.economic.SS_DeductCoinReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_DeductCoin, null, options, request);
      }
      /// <summary>
      /// 扣减金币
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_DeductCoinAck> DeductCoinAsync(global::Msg.economic.SS_DeductCoinReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return DeductCoinAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 扣减金币
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_DeductCoinAck> DeductCoinAsync(global::Msg.economic.SS_DeductCoinReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_DeductCoin, null, options, request);
      }
      /// <summary>
      /// 金币结算
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_SettleCoinAck SubscribeCoinSettlement(global::Msg.economic.SS_SettleCoinReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SubscribeCoinSettlement(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 金币结算
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_SettleCoinAck SubscribeCoinSettlement(global::Msg.economic.SS_SettleCoinReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_SubscribeCoinSettlement, null, options, request);
      }
      /// <summary>
      /// 金币结算
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_SettleCoinAck> SubscribeCoinSettlementAsync(global::Msg.economic.SS_SettleCoinReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SubscribeCoinSettlementAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 金币结算
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_SettleCoinAck> SubscribeCoinSettlementAsync(global::Msg.economic.SS_SettleCoinReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_SubscribeCoinSettlement, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override EconomicInnerServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new EconomicInnerServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(EconomicInnerServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GenTradeID, serviceImpl.GenTradeID)
          .AddMethod(__Method_AddCoin, serviceImpl.AddCoin)
          .AddMethod(__Method_DeductCoin, serviceImpl.DeductCoin)
          .AddMethod(__Method_SubscribeCoinSettlement, serviceImpl.SubscribeCoinSettlement).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, EconomicInnerServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GenTradeID, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GenTradeIdReq, global::Msg.economic.SS_GenTradeIdAck>(serviceImpl.GenTradeID));
      serviceBinder.AddMethod(__Method_AddCoin, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_AddCoinReq, global::Msg.economic.SS_AddCoinAck>(serviceImpl.AddCoin));
      serviceBinder.AddMethod(__Method_DeductCoin, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_DeductCoinReq, global::Msg.economic.SS_DeductCoinAck>(serviceImpl.DeductCoin));
      serviceBinder.AddMethod(__Method_SubscribeCoinSettlement, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_SettleCoinReq, global::Msg.economic.SS_SettleCoinAck>(serviceImpl.SubscribeCoinSettlement));
    }

  }
  /// <summary>
  /// To Client（iOS）
  /// </summary>
  public static partial class OrderService
  {
    static readonly string __ServiceName = "OrderService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_CreateOrderReq> __Marshaller_CS_CreateOrderReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_CreateOrderReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_CreateOrderAck> __Marshaller_SC_CreateOrderAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_CreateOrderAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_CancelOrderReq> __Marshaller_CS_CancelOrderReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_CancelOrderReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_CancelOrderAck> __Marshaller_SC_CancelOrderAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_CancelOrderAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_GetUnpaidOrderReq> __Marshaller_CS_GetUnpaidOrderReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_GetUnpaidOrderReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_GetUnpaidOrderAck> __Marshaller_SC_GetUnpaidOrderAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_GetUnpaidOrderAck.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.CS_CreateOrderReq, global::Msg.economic.SC_CreateOrderAck> __Method_CreateOrder = new grpc::Method<global::Msg.economic.CS_CreateOrderReq, global::Msg.economic.SC_CreateOrderAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "CreateOrder",
        __Marshaller_CS_CreateOrderReq,
        __Marshaller_SC_CreateOrderAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.CS_CancelOrderReq, global::Msg.economic.SC_CancelOrderAck> __Method_CancelOrder = new grpc::Method<global::Msg.economic.CS_CancelOrderReq, global::Msg.economic.SC_CancelOrderAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "CancelOrder",
        __Marshaller_CS_CancelOrderReq,
        __Marshaller_SC_CancelOrderAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.CS_GetUnpaidOrderReq, global::Msg.economic.SC_GetUnpaidOrderAck> __Method_GetUnpaidOrderByProductId = new grpc::Method<global::Msg.economic.CS_GetUnpaidOrderReq, global::Msg.economic.SC_GetUnpaidOrderAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUnpaidOrderByProductId",
        __Marshaller_CS_GetUnpaidOrderReq,
        __Marshaller_SC_GetUnpaidOrderAck);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.economic.ServiceReflection.Descriptor.Services[2]; }
    }

    /// <summary>Base class for server-side implementations of OrderService</summary>
    [grpc::BindServiceMethod(typeof(OrderService), "BindService")]
    public abstract partial class OrderServiceBase
    {
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SC_CreateOrderAck> CreateOrder(global::Msg.economic.CS_CreateOrderReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SC_CancelOrderAck> CancelOrder(global::Msg.economic.CS_CancelOrderReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SC_GetUnpaidOrderAck> GetUnpaidOrderByProductId(global::Msg.economic.CS_GetUnpaidOrderReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for OrderService</summary>
    public partial class OrderServiceClient : grpc::ClientBase<OrderServiceClient>
    {
      /// <summary>Creates a new client for OrderService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public OrderServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for OrderService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public OrderServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected OrderServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected OrderServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_CreateOrderAck CreateOrder(global::Msg.economic.CS_CreateOrderReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CreateOrder(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_CreateOrderAck CreateOrder(global::Msg.economic.CS_CreateOrderReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_CreateOrder, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_CreateOrderAck> CreateOrderAsync(global::Msg.economic.CS_CreateOrderReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CreateOrderAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_CreateOrderAck> CreateOrderAsync(global::Msg.economic.CS_CreateOrderReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_CreateOrder, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_CancelOrderAck CancelOrder(global::Msg.economic.CS_CancelOrderReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CancelOrder(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_CancelOrderAck CancelOrder(global::Msg.economic.CS_CancelOrderReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_CancelOrder, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_CancelOrderAck> CancelOrderAsync(global::Msg.economic.CS_CancelOrderReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CancelOrderAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_CancelOrderAck> CancelOrderAsync(global::Msg.economic.CS_CancelOrderReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_CancelOrder, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_GetUnpaidOrderAck GetUnpaidOrderByProductId(global::Msg.economic.CS_GetUnpaidOrderReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUnpaidOrderByProductId(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_GetUnpaidOrderAck GetUnpaidOrderByProductId(global::Msg.economic.CS_GetUnpaidOrderReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUnpaidOrderByProductId, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_GetUnpaidOrderAck> GetUnpaidOrderByProductIdAsync(global::Msg.economic.CS_GetUnpaidOrderReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUnpaidOrderByProductIdAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_GetUnpaidOrderAck> GetUnpaidOrderByProductIdAsync(global::Msg.economic.CS_GetUnpaidOrderReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUnpaidOrderByProductId, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override OrderServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new OrderServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(OrderServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_CreateOrder, serviceImpl.CreateOrder)
          .AddMethod(__Method_CancelOrder, serviceImpl.CancelOrder)
          .AddMethod(__Method_GetUnpaidOrderByProductId, serviceImpl.GetUnpaidOrderByProductId).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, OrderServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_CreateOrder, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.CS_CreateOrderReq, global::Msg.economic.SC_CreateOrderAck>(serviceImpl.CreateOrder));
      serviceBinder.AddMethod(__Method_CancelOrder, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.CS_CancelOrderReq, global::Msg.economic.SC_CancelOrderAck>(serviceImpl.CancelOrder));
      serviceBinder.AddMethod(__Method_GetUnpaidOrderByProductId, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.CS_GetUnpaidOrderReq, global::Msg.economic.SC_GetUnpaidOrderAck>(serviceImpl.GetUnpaidOrderByProductId));
    }

  }
  /// <summary>
  /// To Server（内部接口）
  /// </summary>
  public static partial class OrderInnerService
  {
    static readonly string __ServiceName = "OrderInnerService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_OrderPaymentChangeReq> __Marshaller_SS_OrderPaymentChangeReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_OrderPaymentChangeReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_OrderPaymentChangeAck> __Marshaller_SS_OrderPaymentChangeAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_OrderPaymentChangeAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetOrderDetailReq> __Marshaller_SS_GetOrderDetailReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetOrderDetailReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetOrderDetailAck> __Marshaller_SS_GetOrderDetailAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetOrderDetailAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdReq> __Marshaller_SS_GetRecentlyCreatedOrderByProductIdReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdAck> __Marshaller_SS_GetRecentlyCreatedOrderByProductIdAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdAck.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_OrderPaymentChangeReq, global::Msg.economic.SS_OrderPaymentChangeAck> __Method_OrderPaymentChange = new grpc::Method<global::Msg.economic.SS_OrderPaymentChangeReq, global::Msg.economic.SS_OrderPaymentChangeAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "OrderPaymentChange",
        __Marshaller_SS_OrderPaymentChangeReq,
        __Marshaller_SS_OrderPaymentChangeAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GetOrderDetailReq, global::Msg.economic.SS_GetOrderDetailAck> __Method_GetOrderDetail = new grpc::Method<global::Msg.economic.SS_GetOrderDetailReq, global::Msg.economic.SS_GetOrderDetailAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetOrderDetail",
        __Marshaller_SS_GetOrderDetailReq,
        __Marshaller_SS_GetOrderDetailAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdReq, global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdAck> __Method_GetRecentlyCreatedOrderByProductId = new grpc::Method<global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdReq, global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetRecentlyCreatedOrderByProductId",
        __Marshaller_SS_GetRecentlyCreatedOrderByProductIdReq,
        __Marshaller_SS_GetRecentlyCreatedOrderByProductIdAck);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.economic.ServiceReflection.Descriptor.Services[3]; }
    }

    /// <summary>Base class for server-side implementations of OrderInnerService</summary>
    [grpc::BindServiceMethod(typeof(OrderInnerService), "BindService")]
    public abstract partial class OrderInnerServiceBase
    {
      /// <summary>
      /// 订单关注的支付事件变更
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_OrderPaymentChangeAck> OrderPaymentChange(global::Msg.economic.SS_OrderPaymentChangeReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 获取订单详情
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GetOrderDetailAck> GetOrderDetail(global::Msg.economic.SS_GetOrderDetailReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 获取最近一次创建的订单信息
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdAck> GetRecentlyCreatedOrderByProductId(global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for OrderInnerService</summary>
    public partial class OrderInnerServiceClient : grpc::ClientBase<OrderInnerServiceClient>
    {
      /// <summary>Creates a new client for OrderInnerService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public OrderInnerServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for OrderInnerService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public OrderInnerServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected OrderInnerServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected OrderInnerServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// 订单关注的支付事件变更
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_OrderPaymentChangeAck OrderPaymentChange(global::Msg.economic.SS_OrderPaymentChangeReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return OrderPaymentChange(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 订单关注的支付事件变更
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_OrderPaymentChangeAck OrderPaymentChange(global::Msg.economic.SS_OrderPaymentChangeReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_OrderPaymentChange, null, options, request);
      }
      /// <summary>
      /// 订单关注的支付事件变更
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_OrderPaymentChangeAck> OrderPaymentChangeAsync(global::Msg.economic.SS_OrderPaymentChangeReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return OrderPaymentChangeAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 订单关注的支付事件变更
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_OrderPaymentChangeAck> OrderPaymentChangeAsync(global::Msg.economic.SS_OrderPaymentChangeReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_OrderPaymentChange, null, options, request);
      }
      /// <summary>
      /// 获取订单详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetOrderDetailAck GetOrderDetail(global::Msg.economic.SS_GetOrderDetailReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetOrderDetail(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取订单详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetOrderDetailAck GetOrderDetail(global::Msg.economic.SS_GetOrderDetailReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetOrderDetail, null, options, request);
      }
      /// <summary>
      /// 获取订单详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetOrderDetailAck> GetOrderDetailAsync(global::Msg.economic.SS_GetOrderDetailReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetOrderDetailAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取订单详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetOrderDetailAck> GetOrderDetailAsync(global::Msg.economic.SS_GetOrderDetailReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetOrderDetail, null, options, request);
      }
      /// <summary>
      /// 获取最近一次创建的订单信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdAck GetRecentlyCreatedOrderByProductId(global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetRecentlyCreatedOrderByProductId(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取最近一次创建的订单信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdAck GetRecentlyCreatedOrderByProductId(global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetRecentlyCreatedOrderByProductId, null, options, request);
      }
      /// <summary>
      /// 获取最近一次创建的订单信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdAck> GetRecentlyCreatedOrderByProductIdAsync(global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetRecentlyCreatedOrderByProductIdAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取最近一次创建的订单信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdAck> GetRecentlyCreatedOrderByProductIdAsync(global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetRecentlyCreatedOrderByProductId, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override OrderInnerServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new OrderInnerServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(OrderInnerServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_OrderPaymentChange, serviceImpl.OrderPaymentChange)
          .AddMethod(__Method_GetOrderDetail, serviceImpl.GetOrderDetail)
          .AddMethod(__Method_GetRecentlyCreatedOrderByProductId, serviceImpl.GetRecentlyCreatedOrderByProductId).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, OrderInnerServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_OrderPaymentChange, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_OrderPaymentChangeReq, global::Msg.economic.SS_OrderPaymentChangeAck>(serviceImpl.OrderPaymentChange));
      serviceBinder.AddMethod(__Method_GetOrderDetail, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GetOrderDetailReq, global::Msg.economic.SS_GetOrderDetailAck>(serviceImpl.GetOrderDetail));
      serviceBinder.AddMethod(__Method_GetRecentlyCreatedOrderByProductId, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdReq, global::Msg.economic.SS_GetRecentlyCreatedOrderByProductIdAck>(serviceImpl.GetRecentlyCreatedOrderByProductId));
    }

  }
  /// <summary>
  /// To Client（IOS）
  /// </summary>
  public static partial class BenefitService
  {
    static readonly string __ServiceName = "BenefitService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_QueryMemberInfoReq> __Marshaller_CS_QueryMemberInfoReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_QueryMemberInfoReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_QueryMemberInfoAck> __Marshaller_SC_QueryMemberInfoAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_QueryMemberInfoAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_NotifyMemberBenefitReq> __Marshaller_SC_NotifyMemberBenefitReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_NotifyMemberBenefitReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_NotifyMemberBenefitAck> __Marshaller_CS_NotifyMemberBenefitAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_NotifyMemberBenefitAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_MemberTypePushReq> __Marshaller_SC_MemberTypePushReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_MemberTypePushReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_MemberTypePushAck> __Marshaller_CS_MemberTypePushAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_MemberTypePushAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_PurchaseStaminaReq> __Marshaller_CS_PurchaseStaminaReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_PurchaseStaminaReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_PurchaseStaminaAck> __Marshaller_SC_PurchaseStaminaAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_PurchaseStaminaAck.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.CS_QueryMemberInfoReq, global::Msg.economic.SC_QueryMemberInfoAck> __Method_QueryMemberInfo = new grpc::Method<global::Msg.economic.CS_QueryMemberInfoReq, global::Msg.economic.SC_QueryMemberInfoAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "QueryMemberInfo",
        __Marshaller_CS_QueryMemberInfoReq,
        __Marshaller_SC_QueryMemberInfoAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SC_NotifyMemberBenefitReq, global::Msg.economic.CS_NotifyMemberBenefitAck> __Method_NotifyMemberBenefit = new grpc::Method<global::Msg.economic.SC_NotifyMemberBenefitReq, global::Msg.economic.CS_NotifyMemberBenefitAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "NotifyMemberBenefit",
        __Marshaller_SC_NotifyMemberBenefitReq,
        __Marshaller_CS_NotifyMemberBenefitAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SC_MemberTypePushReq, global::Msg.economic.CS_MemberTypePushAck> __Method_MemberTypePush = new grpc::Method<global::Msg.economic.SC_MemberTypePushReq, global::Msg.economic.CS_MemberTypePushAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "MemberTypePush",
        __Marshaller_SC_MemberTypePushReq,
        __Marshaller_CS_MemberTypePushAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.CS_PurchaseStaminaReq, global::Msg.economic.SC_PurchaseStaminaAck> __Method_PurchaseStamina = new grpc::Method<global::Msg.economic.CS_PurchaseStaminaReq, global::Msg.economic.SC_PurchaseStaminaAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "PurchaseStamina",
        __Marshaller_CS_PurchaseStaminaReq,
        __Marshaller_SC_PurchaseStaminaAck);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.economic.ServiceReflection.Descriptor.Services[4]; }
    }

    /// <summary>Base class for server-side implementations of BenefitService</summary>
    [grpc::BindServiceMethod(typeof(BenefitService), "BindService")]
    public abstract partial class BenefitServiceBase
    {
      /// <summary>
      /// 查询会员信息
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SC_QueryMemberInfoAck> QueryMemberInfo(global::Msg.economic.CS_QueryMemberInfoReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 会员权益通知
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.CS_NotifyMemberBenefitAck> NotifyMemberBenefit(global::Msg.economic.SC_NotifyMemberBenefitReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 会员类型变更通知
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.CS_MemberTypePushAck> MemberTypePush(global::Msg.economic.SC_MemberTypePushReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 购买体力值
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SC_PurchaseStaminaAck> PurchaseStamina(global::Msg.economic.CS_PurchaseStaminaReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for BenefitService</summary>
    public partial class BenefitServiceClient : grpc::ClientBase<BenefitServiceClient>
    {
      /// <summary>Creates a new client for BenefitService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public BenefitServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for BenefitService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public BenefitServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected BenefitServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected BenefitServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// 查询会员信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_QueryMemberInfoAck QueryMemberInfo(global::Msg.economic.CS_QueryMemberInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return QueryMemberInfo(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 查询会员信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_QueryMemberInfoAck QueryMemberInfo(global::Msg.economic.CS_QueryMemberInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_QueryMemberInfo, null, options, request);
      }
      /// <summary>
      /// 查询会员信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_QueryMemberInfoAck> QueryMemberInfoAsync(global::Msg.economic.CS_QueryMemberInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return QueryMemberInfoAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 查询会员信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_QueryMemberInfoAck> QueryMemberInfoAsync(global::Msg.economic.CS_QueryMemberInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_QueryMemberInfo, null, options, request);
      }
      /// <summary>
      /// 会员权益通知
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.CS_NotifyMemberBenefitAck NotifyMemberBenefit(global::Msg.economic.SC_NotifyMemberBenefitReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return NotifyMemberBenefit(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 会员权益通知
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.CS_NotifyMemberBenefitAck NotifyMemberBenefit(global::Msg.economic.SC_NotifyMemberBenefitReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_NotifyMemberBenefit, null, options, request);
      }
      /// <summary>
      /// 会员权益通知
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.CS_NotifyMemberBenefitAck> NotifyMemberBenefitAsync(global::Msg.economic.SC_NotifyMemberBenefitReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return NotifyMemberBenefitAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 会员权益通知
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.CS_NotifyMemberBenefitAck> NotifyMemberBenefitAsync(global::Msg.economic.SC_NotifyMemberBenefitReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_NotifyMemberBenefit, null, options, request);
      }
      /// <summary>
      /// 会员类型变更通知
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.CS_MemberTypePushAck MemberTypePush(global::Msg.economic.SC_MemberTypePushReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return MemberTypePush(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 会员类型变更通知
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.CS_MemberTypePushAck MemberTypePush(global::Msg.economic.SC_MemberTypePushReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_MemberTypePush, null, options, request);
      }
      /// <summary>
      /// 会员类型变更通知
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.CS_MemberTypePushAck> MemberTypePushAsync(global::Msg.economic.SC_MemberTypePushReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return MemberTypePushAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 会员类型变更通知
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.CS_MemberTypePushAck> MemberTypePushAsync(global::Msg.economic.SC_MemberTypePushReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_MemberTypePush, null, options, request);
      }
      /// <summary>
      /// 购买体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_PurchaseStaminaAck PurchaseStamina(global::Msg.economic.CS_PurchaseStaminaReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return PurchaseStamina(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 购买体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_PurchaseStaminaAck PurchaseStamina(global::Msg.economic.CS_PurchaseStaminaReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_PurchaseStamina, null, options, request);
      }
      /// <summary>
      /// 购买体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_PurchaseStaminaAck> PurchaseStaminaAsync(global::Msg.economic.CS_PurchaseStaminaReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return PurchaseStaminaAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 购买体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_PurchaseStaminaAck> PurchaseStaminaAsync(global::Msg.economic.CS_PurchaseStaminaReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_PurchaseStamina, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override BenefitServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new BenefitServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(BenefitServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_QueryMemberInfo, serviceImpl.QueryMemberInfo)
          .AddMethod(__Method_NotifyMemberBenefit, serviceImpl.NotifyMemberBenefit)
          .AddMethod(__Method_MemberTypePush, serviceImpl.MemberTypePush)
          .AddMethod(__Method_PurchaseStamina, serviceImpl.PurchaseStamina).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, BenefitServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_QueryMemberInfo, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.CS_QueryMemberInfoReq, global::Msg.economic.SC_QueryMemberInfoAck>(serviceImpl.QueryMemberInfo));
      serviceBinder.AddMethod(__Method_NotifyMemberBenefit, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SC_NotifyMemberBenefitReq, global::Msg.economic.CS_NotifyMemberBenefitAck>(serviceImpl.NotifyMemberBenefit));
      serviceBinder.AddMethod(__Method_MemberTypePush, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SC_MemberTypePushReq, global::Msg.economic.CS_MemberTypePushAck>(serviceImpl.MemberTypePush));
      serviceBinder.AddMethod(__Method_PurchaseStamina, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.CS_PurchaseStaminaReq, global::Msg.economic.SC_PurchaseStaminaAck>(serviceImpl.PurchaseStamina));
    }

  }
  /// <summary>
  /// To Server（内部接口）
  /// </summary>
  public static partial class BenefitInnerService
  {
    static readonly string __ServiceName = "BenefitInnerService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_NotifySubscribeStatusReq> __Marshaller_SS_NotifySubscribeStatusReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_NotifySubscribeStatusReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_NotifySubscribeStatusAck> __Marshaller_SS_NotifySubscribeStatusAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_NotifySubscribeStatusAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_TriggerMemberBenefitReq> __Marshaller_SS_TriggerMemberBenefitReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_TriggerMemberBenefitReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_TriggerMemberBenefitAck> __Marshaller_SS_TriggerMemberBenefitAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_TriggerMemberBenefitAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_TriggerUserStaminaBenefitReq> __Marshaller_SS_TriggerUserStaminaBenefitReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_TriggerUserStaminaBenefitReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_TriggerUserStaminaBenefitAck> __Marshaller_SS_TriggerUserStaminaBenefitAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_TriggerUserStaminaBenefitAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetMemberInfoReq> __Marshaller_SS_GetMemberInfoReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetMemberInfoReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetMemberInfoAck> __Marshaller_SS_GetMemberInfoAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetMemberInfoAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_StaminaRewardReq> __Marshaller_SS_StaminaRewardReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_StaminaRewardReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_StaminaRewardAck> __Marshaller_SS_StaminaRewardAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_StaminaRewardAck.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_NotifySubscribeStatusReq, global::Msg.economic.SS_NotifySubscribeStatusAck> __Method_NotifySubscribeStatus = new grpc::Method<global::Msg.economic.SS_NotifySubscribeStatusReq, global::Msg.economic.SS_NotifySubscribeStatusAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "NotifySubscribeStatus",
        __Marshaller_SS_NotifySubscribeStatusReq,
        __Marshaller_SS_NotifySubscribeStatusAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_TriggerMemberBenefitReq, global::Msg.economic.SS_TriggerMemberBenefitAck> __Method_TriggerMemberBenefit = new grpc::Method<global::Msg.economic.SS_TriggerMemberBenefitReq, global::Msg.economic.SS_TriggerMemberBenefitAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "TriggerMemberBenefit",
        __Marshaller_SS_TriggerMemberBenefitReq,
        __Marshaller_SS_TriggerMemberBenefitAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_TriggerUserStaminaBenefitReq, global::Msg.economic.SS_TriggerUserStaminaBenefitAck> __Method_TriggerUserStaminaBenefit = new grpc::Method<global::Msg.economic.SS_TriggerUserStaminaBenefitReq, global::Msg.economic.SS_TriggerUserStaminaBenefitAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "TriggerUserStaminaBenefit",
        __Marshaller_SS_TriggerUserStaminaBenefitReq,
        __Marshaller_SS_TriggerUserStaminaBenefitAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GetMemberInfoReq, global::Msg.economic.SS_GetMemberInfoAck> __Method_GetMemberInfo = new grpc::Method<global::Msg.economic.SS_GetMemberInfoReq, global::Msg.economic.SS_GetMemberInfoAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetMemberInfo",
        __Marshaller_SS_GetMemberInfoReq,
        __Marshaller_SS_GetMemberInfoAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_StaminaRewardReq, global::Msg.economic.SS_StaminaRewardAck> __Method_StaminaReward = new grpc::Method<global::Msg.economic.SS_StaminaRewardReq, global::Msg.economic.SS_StaminaRewardAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "StaminaReward",
        __Marshaller_SS_StaminaRewardReq,
        __Marshaller_SS_StaminaRewardAck);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.economic.ServiceReflection.Descriptor.Services[5]; }
    }

    /// <summary>Base class for server-side implementations of BenefitInnerService</summary>
    [grpc::BindServiceMethod(typeof(BenefitInnerService), "BindService")]
    public abstract partial class BenefitInnerServiceBase
    {
      /// <summary>
      /// 订阅状态回调
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_NotifySubscribeStatusAck> NotifySubscribeStatus(global::Msg.economic.SS_NotifySubscribeStatusReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 触发会员权益
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_TriggerMemberBenefitAck> TriggerMemberBenefit(global::Msg.economic.SS_TriggerMemberBenefitReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 获取用户体力值
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_TriggerUserStaminaBenefitAck> TriggerUserStaminaBenefit(global::Msg.economic.SS_TriggerUserStaminaBenefitReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 查询会员信息
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GetMemberInfoAck> GetMemberInfo(global::Msg.economic.SS_GetMemberInfoReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 奖励体力值
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_StaminaRewardAck> StaminaReward(global::Msg.economic.SS_StaminaRewardReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for BenefitInnerService</summary>
    public partial class BenefitInnerServiceClient : grpc::ClientBase<BenefitInnerServiceClient>
    {
      /// <summary>Creates a new client for BenefitInnerService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public BenefitInnerServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for BenefitInnerService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public BenefitInnerServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected BenefitInnerServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected BenefitInnerServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// 订阅状态回调
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_NotifySubscribeStatusAck NotifySubscribeStatus(global::Msg.economic.SS_NotifySubscribeStatusReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return NotifySubscribeStatus(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 订阅状态回调
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_NotifySubscribeStatusAck NotifySubscribeStatus(global::Msg.economic.SS_NotifySubscribeStatusReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_NotifySubscribeStatus, null, options, request);
      }
      /// <summary>
      /// 订阅状态回调
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_NotifySubscribeStatusAck> NotifySubscribeStatusAsync(global::Msg.economic.SS_NotifySubscribeStatusReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return NotifySubscribeStatusAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 订阅状态回调
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_NotifySubscribeStatusAck> NotifySubscribeStatusAsync(global::Msg.economic.SS_NotifySubscribeStatusReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_NotifySubscribeStatus, null, options, request);
      }
      /// <summary>
      /// 触发会员权益
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_TriggerMemberBenefitAck TriggerMemberBenefit(global::Msg.economic.SS_TriggerMemberBenefitReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return TriggerMemberBenefit(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 触发会员权益
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_TriggerMemberBenefitAck TriggerMemberBenefit(global::Msg.economic.SS_TriggerMemberBenefitReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_TriggerMemberBenefit, null, options, request);
      }
      /// <summary>
      /// 触发会员权益
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_TriggerMemberBenefitAck> TriggerMemberBenefitAsync(global::Msg.economic.SS_TriggerMemberBenefitReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return TriggerMemberBenefitAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 触发会员权益
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_TriggerMemberBenefitAck> TriggerMemberBenefitAsync(global::Msg.economic.SS_TriggerMemberBenefitReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_TriggerMemberBenefit, null, options, request);
      }
      /// <summary>
      /// 获取用户体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_TriggerUserStaminaBenefitAck TriggerUserStaminaBenefit(global::Msg.economic.SS_TriggerUserStaminaBenefitReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return TriggerUserStaminaBenefit(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_TriggerUserStaminaBenefitAck TriggerUserStaminaBenefit(global::Msg.economic.SS_TriggerUserStaminaBenefitReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_TriggerUserStaminaBenefit, null, options, request);
      }
      /// <summary>
      /// 获取用户体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_TriggerUserStaminaBenefitAck> TriggerUserStaminaBenefitAsync(global::Msg.economic.SS_TriggerUserStaminaBenefitReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return TriggerUserStaminaBenefitAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_TriggerUserStaminaBenefitAck> TriggerUserStaminaBenefitAsync(global::Msg.economic.SS_TriggerUserStaminaBenefitReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_TriggerUserStaminaBenefit, null, options, request);
      }
      /// <summary>
      /// 查询会员信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetMemberInfoAck GetMemberInfo(global::Msg.economic.SS_GetMemberInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetMemberInfo(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 查询会员信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetMemberInfoAck GetMemberInfo(global::Msg.economic.SS_GetMemberInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetMemberInfo, null, options, request);
      }
      /// <summary>
      /// 查询会员信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetMemberInfoAck> GetMemberInfoAsync(global::Msg.economic.SS_GetMemberInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetMemberInfoAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 查询会员信息
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetMemberInfoAck> GetMemberInfoAsync(global::Msg.economic.SS_GetMemberInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetMemberInfo, null, options, request);
      }
      /// <summary>
      /// 奖励体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_StaminaRewardAck StaminaReward(global::Msg.economic.SS_StaminaRewardReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return StaminaReward(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 奖励体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_StaminaRewardAck StaminaReward(global::Msg.economic.SS_StaminaRewardReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_StaminaReward, null, options, request);
      }
      /// <summary>
      /// 奖励体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_StaminaRewardAck> StaminaRewardAsync(global::Msg.economic.SS_StaminaRewardReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return StaminaRewardAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 奖励体力值
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_StaminaRewardAck> StaminaRewardAsync(global::Msg.economic.SS_StaminaRewardReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_StaminaReward, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override BenefitInnerServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new BenefitInnerServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(BenefitInnerServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_NotifySubscribeStatus, serviceImpl.NotifySubscribeStatus)
          .AddMethod(__Method_TriggerMemberBenefit, serviceImpl.TriggerMemberBenefit)
          .AddMethod(__Method_TriggerUserStaminaBenefit, serviceImpl.TriggerUserStaminaBenefit)
          .AddMethod(__Method_GetMemberInfo, serviceImpl.GetMemberInfo)
          .AddMethod(__Method_StaminaReward, serviceImpl.StaminaReward).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, BenefitInnerServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_NotifySubscribeStatus, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_NotifySubscribeStatusReq, global::Msg.economic.SS_NotifySubscribeStatusAck>(serviceImpl.NotifySubscribeStatus));
      serviceBinder.AddMethod(__Method_TriggerMemberBenefit, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_TriggerMemberBenefitReq, global::Msg.economic.SS_TriggerMemberBenefitAck>(serviceImpl.TriggerMemberBenefit));
      serviceBinder.AddMethod(__Method_TriggerUserStaminaBenefit, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_TriggerUserStaminaBenefitReq, global::Msg.economic.SS_TriggerUserStaminaBenefitAck>(serviceImpl.TriggerUserStaminaBenefit));
      serviceBinder.AddMethod(__Method_GetMemberInfo, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GetMemberInfoReq, global::Msg.economic.SS_GetMemberInfoAck>(serviceImpl.GetMemberInfo));
      serviceBinder.AddMethod(__Method_StaminaReward, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_StaminaRewardReq, global::Msg.economic.SS_StaminaRewardAck>(serviceImpl.StaminaReward));
    }

  }
  /// <summary>
  /// 资产账号服务（内部接口, 不对外暴露）
  /// </summary>
  public static partial class AssetAccountInnerService
  {
    static readonly string __ServiceName = "AssetAccountInnerService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetAssetAccountReq> __Marshaller_SS_GetAssetAccountReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetAssetAccountReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetAssetAccountAck> __Marshaller_SS_GetAssetAccountAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetAssetAccountAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GrantBenefitReq> __Marshaller_SS_GrantBenefitReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GrantBenefitReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GrantBenefitAck> __Marshaller_SS_GrantBenefitAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GrantBenefitAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GrantOrderAssetReq> __Marshaller_SS_GrantOrderAssetReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GrantOrderAssetReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GrantOrderAssetAck> __Marshaller_SS_GrantOrderAssetAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GrantOrderAssetAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GrantChatAssetReq> __Marshaller_SS_GrantChatAssetReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GrantChatAssetReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GrantChatAssetAck> __Marshaller_SS_GrantChatAssetAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GrantChatAssetAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GrantTaskAssetReq> __Marshaller_SS_GrantTaskAssetReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GrantTaskAssetReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GrantTaskAssetAck> __Marshaller_SS_GrantTaskAssetAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GrantTaskAssetAck.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_IncentiveAssetReq> __Marshaller_SS_IncentiveAssetReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_IncentiveAssetReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_IncentiveAssetAck> __Marshaller_SS_IncentiveAssetAck = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_IncentiveAssetAck.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GetAssetAccountReq, global::Msg.economic.SS_GetAssetAccountAck> __Method_GetAssetAccount = new grpc::Method<global::Msg.economic.SS_GetAssetAccountReq, global::Msg.economic.SS_GetAssetAccountAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetAssetAccount",
        __Marshaller_SS_GetAssetAccountReq,
        __Marshaller_SS_GetAssetAccountAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GrantBenefitReq, global::Msg.economic.SS_GrantBenefitAck> __Method_GrantBenefit = new grpc::Method<global::Msg.economic.SS_GrantBenefitReq, global::Msg.economic.SS_GrantBenefitAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GrantBenefit",
        __Marshaller_SS_GrantBenefitReq,
        __Marshaller_SS_GrantBenefitAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GrantOrderAssetReq, global::Msg.economic.SS_GrantOrderAssetAck> __Method_GrantOrderAsset = new grpc::Method<global::Msg.economic.SS_GrantOrderAssetReq, global::Msg.economic.SS_GrantOrderAssetAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GrantOrderAsset",
        __Marshaller_SS_GrantOrderAssetReq,
        __Marshaller_SS_GrantOrderAssetAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GrantChatAssetReq, global::Msg.economic.SS_GrantChatAssetAck> __Method_GrantChatAsset = new grpc::Method<global::Msg.economic.SS_GrantChatAssetReq, global::Msg.economic.SS_GrantChatAssetAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GrantChatAsset",
        __Marshaller_SS_GrantChatAssetReq,
        __Marshaller_SS_GrantChatAssetAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GrantTaskAssetReq, global::Msg.economic.SS_GrantTaskAssetAck> __Method_GrantTaskAsset = new grpc::Method<global::Msg.economic.SS_GrantTaskAssetReq, global::Msg.economic.SS_GrantTaskAssetAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GrantTaskAsset",
        __Marshaller_SS_GrantTaskAssetReq,
        __Marshaller_SS_GrantTaskAssetAck);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_IncentiveAssetReq, global::Msg.economic.SS_IncentiveAssetAck> __Method_IncentiveAsset = new grpc::Method<global::Msg.economic.SS_IncentiveAssetReq, global::Msg.economic.SS_IncentiveAssetAck>(
        grpc::MethodType.Unary,
        __ServiceName,
        "IncentiveAsset",
        __Marshaller_SS_IncentiveAssetReq,
        __Marshaller_SS_IncentiveAssetAck);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.economic.ServiceReflection.Descriptor.Services[6]; }
    }

    /// <summary>Base class for server-side implementations of AssetAccountInnerService</summary>
    [grpc::BindServiceMethod(typeof(AssetAccountInnerService), "BindService")]
    public abstract partial class AssetAccountInnerServiceBase
    {
      /// <summary>
      /// 获取用户资产账号详情
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GetAssetAccountAck> GetAssetAccount(global::Msg.economic.SS_GetAssetAccountReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 权益变更资产
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GrantBenefitAck> GrantBenefit(global::Msg.economic.SS_GrantBenefitReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 订单变更资产
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GrantOrderAssetAck> GrantOrderAsset(global::Msg.economic.SS_GrantOrderAssetReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 对话行为变更资产
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GrantChatAssetAck> GrantChatAsset(global::Msg.economic.SS_GrantChatAssetReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 任务变更资产
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GrantTaskAssetAck> GrantTaskAsset(global::Msg.economic.SS_GrantTaskAssetReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 激励变更资产
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_IncentiveAssetAck> IncentiveAsset(global::Msg.economic.SS_IncentiveAssetReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for AssetAccountInnerService</summary>
    public partial class AssetAccountInnerServiceClient : grpc::ClientBase<AssetAccountInnerServiceClient>
    {
      /// <summary>Creates a new client for AssetAccountInnerService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public AssetAccountInnerServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for AssetAccountInnerService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public AssetAccountInnerServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected AssetAccountInnerServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected AssetAccountInnerServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// 获取用户资产账号详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetAssetAccountAck GetAssetAccount(global::Msg.economic.SS_GetAssetAccountReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetAssetAccount(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户资产账号详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetAssetAccountAck GetAssetAccount(global::Msg.economic.SS_GetAssetAccountReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetAssetAccount, null, options, request);
      }
      /// <summary>
      /// 获取用户资产账号详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetAssetAccountAck> GetAssetAccountAsync(global::Msg.economic.SS_GetAssetAccountReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetAssetAccountAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户资产账号详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetAssetAccountAck> GetAssetAccountAsync(global::Msg.economic.SS_GetAssetAccountReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetAssetAccount, null, options, request);
      }
      /// <summary>
      /// 权益变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GrantBenefitAck GrantBenefit(global::Msg.economic.SS_GrantBenefitReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GrantBenefit(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 权益变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GrantBenefitAck GrantBenefit(global::Msg.economic.SS_GrantBenefitReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GrantBenefit, null, options, request);
      }
      /// <summary>
      /// 权益变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GrantBenefitAck> GrantBenefitAsync(global::Msg.economic.SS_GrantBenefitReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GrantBenefitAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 权益变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GrantBenefitAck> GrantBenefitAsync(global::Msg.economic.SS_GrantBenefitReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GrantBenefit, null, options, request);
      }
      /// <summary>
      /// 订单变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GrantOrderAssetAck GrantOrderAsset(global::Msg.economic.SS_GrantOrderAssetReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GrantOrderAsset(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 订单变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GrantOrderAssetAck GrantOrderAsset(global::Msg.economic.SS_GrantOrderAssetReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GrantOrderAsset, null, options, request);
      }
      /// <summary>
      /// 订单变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GrantOrderAssetAck> GrantOrderAssetAsync(global::Msg.economic.SS_GrantOrderAssetReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GrantOrderAssetAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 订单变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GrantOrderAssetAck> GrantOrderAssetAsync(global::Msg.economic.SS_GrantOrderAssetReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GrantOrderAsset, null, options, request);
      }
      /// <summary>
      /// 对话行为变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GrantChatAssetAck GrantChatAsset(global::Msg.economic.SS_GrantChatAssetReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GrantChatAsset(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 对话行为变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GrantChatAssetAck GrantChatAsset(global::Msg.economic.SS_GrantChatAssetReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GrantChatAsset, null, options, request);
      }
      /// <summary>
      /// 对话行为变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GrantChatAssetAck> GrantChatAssetAsync(global::Msg.economic.SS_GrantChatAssetReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GrantChatAssetAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 对话行为变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GrantChatAssetAck> GrantChatAssetAsync(global::Msg.economic.SS_GrantChatAssetReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GrantChatAsset, null, options, request);
      }
      /// <summary>
      /// 任务变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GrantTaskAssetAck GrantTaskAsset(global::Msg.economic.SS_GrantTaskAssetReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GrantTaskAsset(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 任务变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GrantTaskAssetAck GrantTaskAsset(global::Msg.economic.SS_GrantTaskAssetReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GrantTaskAsset, null, options, request);
      }
      /// <summary>
      /// 任务变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GrantTaskAssetAck> GrantTaskAssetAsync(global::Msg.economic.SS_GrantTaskAssetReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GrantTaskAssetAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 任务变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GrantTaskAssetAck> GrantTaskAssetAsync(global::Msg.economic.SS_GrantTaskAssetReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GrantTaskAsset, null, options, request);
      }
      /// <summary>
      /// 激励变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_IncentiveAssetAck IncentiveAsset(global::Msg.economic.SS_IncentiveAssetReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return IncentiveAsset(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 激励变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_IncentiveAssetAck IncentiveAsset(global::Msg.economic.SS_IncentiveAssetReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_IncentiveAsset, null, options, request);
      }
      /// <summary>
      /// 激励变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_IncentiveAssetAck> IncentiveAssetAsync(global::Msg.economic.SS_IncentiveAssetReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return IncentiveAssetAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 激励变更资产
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_IncentiveAssetAck> IncentiveAssetAsync(global::Msg.economic.SS_IncentiveAssetReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_IncentiveAsset, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override AssetAccountInnerServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new AssetAccountInnerServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(AssetAccountInnerServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GetAssetAccount, serviceImpl.GetAssetAccount)
          .AddMethod(__Method_GrantBenefit, serviceImpl.GrantBenefit)
          .AddMethod(__Method_GrantOrderAsset, serviceImpl.GrantOrderAsset)
          .AddMethod(__Method_GrantChatAsset, serviceImpl.GrantChatAsset)
          .AddMethod(__Method_GrantTaskAsset, serviceImpl.GrantTaskAsset)
          .AddMethod(__Method_IncentiveAsset, serviceImpl.IncentiveAsset).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, AssetAccountInnerServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GetAssetAccount, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GetAssetAccountReq, global::Msg.economic.SS_GetAssetAccountAck>(serviceImpl.GetAssetAccount));
      serviceBinder.AddMethod(__Method_GrantBenefit, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GrantBenefitReq, global::Msg.economic.SS_GrantBenefitAck>(serviceImpl.GrantBenefit));
      serviceBinder.AddMethod(__Method_GrantOrderAsset, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GrantOrderAssetReq, global::Msg.economic.SS_GrantOrderAssetAck>(serviceImpl.GrantOrderAsset));
      serviceBinder.AddMethod(__Method_GrantChatAsset, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GrantChatAssetReq, global::Msg.economic.SS_GrantChatAssetAck>(serviceImpl.GrantChatAsset));
      serviceBinder.AddMethod(__Method_GrantTaskAsset, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GrantTaskAssetReq, global::Msg.economic.SS_GrantTaskAssetAck>(serviceImpl.GrantTaskAsset));
      serviceBinder.AddMethod(__Method_IncentiveAsset, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_IncentiveAssetReq, global::Msg.economic.SS_IncentiveAssetAck>(serviceImpl.IncentiveAsset));
    }

  }
  public static partial class PayService
  {
    static readonly string __ServiceName = "PayService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_VerifyReceiptDataReq> __Marshaller_CS_VerifyReceiptDataReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_VerifyReceiptDataReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_VerifyReceiptDataResp> __Marshaller_SC_VerifyReceiptDataResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_VerifyReceiptDataResp.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.CS_VerifyReceiptDataReq, global::Msg.economic.SC_VerifyReceiptDataResp> __Method_VerifyReceipt = new grpc::Method<global::Msg.economic.CS_VerifyReceiptDataReq, global::Msg.economic.SC_VerifyReceiptDataResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "VerifyReceipt",
        __Marshaller_CS_VerifyReceiptDataReq,
        __Marshaller_SC_VerifyReceiptDataResp);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.economic.ServiceReflection.Descriptor.Services[7]; }
    }

    /// <summary>Base class for server-side implementations of PayService</summary>
    [grpc::BindServiceMethod(typeof(PayService), "BindService")]
    public abstract partial class PayServiceBase
    {
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SC_VerifyReceiptDataResp> VerifyReceipt(global::Msg.economic.CS_VerifyReceiptDataReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for PayService</summary>
    public partial class PayServiceClient : grpc::ClientBase<PayServiceClient>
    {
      /// <summary>Creates a new client for PayService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public PayServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for PayService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public PayServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected PayServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected PayServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_VerifyReceiptDataResp VerifyReceipt(global::Msg.economic.CS_VerifyReceiptDataReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return VerifyReceipt(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_VerifyReceiptDataResp VerifyReceipt(global::Msg.economic.CS_VerifyReceiptDataReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_VerifyReceipt, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_VerifyReceiptDataResp> VerifyReceiptAsync(global::Msg.economic.CS_VerifyReceiptDataReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return VerifyReceiptAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_VerifyReceiptDataResp> VerifyReceiptAsync(global::Msg.economic.CS_VerifyReceiptDataReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_VerifyReceipt, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override PayServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new PayServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(PayServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_VerifyReceipt, serviceImpl.VerifyReceipt).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, PayServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_VerifyReceipt, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.CS_VerifyReceiptDataReq, global::Msg.economic.SC_VerifyReceiptDataResp>(serviceImpl.VerifyReceipt));
    }

  }
  public static partial class MerchandiseService
  {
    static readonly string __ServiceName = "MerchandiseService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_GetShopInfoReq> __Marshaller_CS_GetShopInfoReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_GetShopInfoReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_GetShopInfoResp> __Marshaller_SC_GetShopInfoResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_GetShopInfoResp.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.CS_PayMerchandiseReq> __Marshaller_CS_PayMerchandiseReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.CS_PayMerchandiseReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SC_PayMerchandiseResp> __Marshaller_SC_PayMerchandiseResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SC_PayMerchandiseResp.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.CS_GetShopInfoReq, global::Msg.economic.SC_GetShopInfoResp> __Method_GetShopInfo = new grpc::Method<global::Msg.economic.CS_GetShopInfoReq, global::Msg.economic.SC_GetShopInfoResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetShopInfo",
        __Marshaller_CS_GetShopInfoReq,
        __Marshaller_SC_GetShopInfoResp);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.CS_PayMerchandiseReq, global::Msg.economic.SC_PayMerchandiseResp> __Method_PayMerchandise = new grpc::Method<global::Msg.economic.CS_PayMerchandiseReq, global::Msg.economic.SC_PayMerchandiseResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "PayMerchandise",
        __Marshaller_CS_PayMerchandiseReq,
        __Marshaller_SC_PayMerchandiseResp);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.economic.ServiceReflection.Descriptor.Services[8]; }
    }

    /// <summary>Base class for server-side implementations of MerchandiseService</summary>
    [grpc::BindServiceMethod(typeof(MerchandiseService), "BindService")]
    public abstract partial class MerchandiseServiceBase
    {
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SC_GetShopInfoResp> GetShopInfo(global::Msg.economic.CS_GetShopInfoReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SC_PayMerchandiseResp> PayMerchandise(global::Msg.economic.CS_PayMerchandiseReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for MerchandiseService</summary>
    public partial class MerchandiseServiceClient : grpc::ClientBase<MerchandiseServiceClient>
    {
      /// <summary>Creates a new client for MerchandiseService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public MerchandiseServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for MerchandiseService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public MerchandiseServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected MerchandiseServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected MerchandiseServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_GetShopInfoResp GetShopInfo(global::Msg.economic.CS_GetShopInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetShopInfo(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_GetShopInfoResp GetShopInfo(global::Msg.economic.CS_GetShopInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetShopInfo, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_GetShopInfoResp> GetShopInfoAsync(global::Msg.economic.CS_GetShopInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetShopInfoAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_GetShopInfoResp> GetShopInfoAsync(global::Msg.economic.CS_GetShopInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetShopInfo, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_PayMerchandiseResp PayMerchandise(global::Msg.economic.CS_PayMerchandiseReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return PayMerchandise(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SC_PayMerchandiseResp PayMerchandise(global::Msg.economic.CS_PayMerchandiseReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_PayMerchandise, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_PayMerchandiseResp> PayMerchandiseAsync(global::Msg.economic.CS_PayMerchandiseReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return PayMerchandiseAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SC_PayMerchandiseResp> PayMerchandiseAsync(global::Msg.economic.CS_PayMerchandiseReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_PayMerchandise, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override MerchandiseServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new MerchandiseServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(MerchandiseServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GetShopInfo, serviceImpl.GetShopInfo)
          .AddMethod(__Method_PayMerchandise, serviceImpl.PayMerchandise).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, MerchandiseServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GetShopInfo, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.CS_GetShopInfoReq, global::Msg.economic.SC_GetShopInfoResp>(serviceImpl.GetShopInfo));
      serviceBinder.AddMethod(__Method_PayMerchandise, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.CS_PayMerchandiseReq, global::Msg.economic.SC_PayMerchandiseResp>(serviceImpl.PayMerchandise));
    }

  }
  /// <summary>
  /// 物品内部服务
  /// </summary>
  public static partial class MerchandiseInnerService
  {
    static readonly string __ServiceName = "MerchandiseInnerService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetMerchandiseChangeInfoReq> __Marshaller_SS_GetMerchandiseChangeInfoReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetMerchandiseChangeInfoReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetMerchandiseChangeInfoResp> __Marshaller_SS_GetMerchandiseChangeInfoResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetMerchandiseChangeInfoResp.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetUserMerchandiseInfoReq> __Marshaller_SS_GetUserMerchandiseInfoReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetUserMerchandiseInfoReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetUserMerchandiseInfoResp> __Marshaller_SS_GetUserMerchandiseInfoResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetUserMerchandiseInfoResp.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_SetUserMerchandiseInfoReq> __Marshaller_SS_SetUserMerchandiseInfoReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_SetUserMerchandiseInfoReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_SetUserMerchandiseInfoResp> __Marshaller_SS_SetUserMerchandiseInfoResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_SetUserMerchandiseInfoResp.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetMerchandiseDetailReq> __Marshaller_SS_GetMerchandiseDetailReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetMerchandiseDetailReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetMerchandiseDetailResp> __Marshaller_SS_GetMerchandiseDetailResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetMerchandiseDetailResp.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetUserIntimacyBoxInfoReq> __Marshaller_SS_GetUserIntimacyBoxInfoReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetUserIntimacyBoxInfoReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetUserIntimacyBoxInfoResp> __Marshaller_SS_GetUserIntimacyBoxInfoResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetUserIntimacyBoxInfoResp.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetMerchandiseByTypeReq> __Marshaller_SS_GetMerchandiseByTypeReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetMerchandiseByTypeReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetMerchandiseByTypeResp> __Marshaller_SS_GetMerchandiseByTypeResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetMerchandiseByTypeResp.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_UseMerchandiseReq> __Marshaller_SS_UseMerchandiseReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_UseMerchandiseReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_UseMerchandiseResp> __Marshaller_SS_UseMerchandiseResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_UseMerchandiseResp.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetUserLimitPropInfoReq> __Marshaller_SS_GetUserLimitPropInfoReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetUserLimitPropInfoReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_GetUserLimitPropInfoResp> __Marshaller_SS_GetUserLimitPropInfoResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_GetUserLimitPropInfoResp.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_InitUserMerchandiseReq> __Marshaller_SS_InitUserMerchandiseReq = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_InitUserMerchandiseReq.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Msg.economic.SS_InitUserMerchandiseResp> __Marshaller_SS_InitUserMerchandiseResp = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Msg.economic.SS_InitUserMerchandiseResp.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GetMerchandiseChangeInfoReq, global::Msg.economic.SS_GetMerchandiseChangeInfoResp> __Method_GetMerchandiseChangeInfo = new grpc::Method<global::Msg.economic.SS_GetMerchandiseChangeInfoReq, global::Msg.economic.SS_GetMerchandiseChangeInfoResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetMerchandiseChangeInfo",
        __Marshaller_SS_GetMerchandiseChangeInfoReq,
        __Marshaller_SS_GetMerchandiseChangeInfoResp);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GetUserMerchandiseInfoReq, global::Msg.economic.SS_GetUserMerchandiseInfoResp> __Method_GetUserMerchandiseInfo = new grpc::Method<global::Msg.economic.SS_GetUserMerchandiseInfoReq, global::Msg.economic.SS_GetUserMerchandiseInfoResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUserMerchandiseInfo",
        __Marshaller_SS_GetUserMerchandiseInfoReq,
        __Marshaller_SS_GetUserMerchandiseInfoResp);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_SetUserMerchandiseInfoReq, global::Msg.economic.SS_SetUserMerchandiseInfoResp> __Method_SetUserMerchandiseInfo = new grpc::Method<global::Msg.economic.SS_SetUserMerchandiseInfoReq, global::Msg.economic.SS_SetUserMerchandiseInfoResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "SetUserMerchandiseInfo",
        __Marshaller_SS_SetUserMerchandiseInfoReq,
        __Marshaller_SS_SetUserMerchandiseInfoResp);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GetMerchandiseDetailReq, global::Msg.economic.SS_GetMerchandiseDetailResp> __Method_GetMerchandiseDetail = new grpc::Method<global::Msg.economic.SS_GetMerchandiseDetailReq, global::Msg.economic.SS_GetMerchandiseDetailResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetMerchandiseDetail",
        __Marshaller_SS_GetMerchandiseDetailReq,
        __Marshaller_SS_GetMerchandiseDetailResp);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GetUserIntimacyBoxInfoReq, global::Msg.economic.SS_GetUserIntimacyBoxInfoResp> __Method_GetUserIntimacyInfoBox = new grpc::Method<global::Msg.economic.SS_GetUserIntimacyBoxInfoReq, global::Msg.economic.SS_GetUserIntimacyBoxInfoResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUserIntimacyInfoBox",
        __Marshaller_SS_GetUserIntimacyBoxInfoReq,
        __Marshaller_SS_GetUserIntimacyBoxInfoResp);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GetMerchandiseByTypeReq, global::Msg.economic.SS_GetMerchandiseByTypeResp> __Method_GetMerchandiseByType = new grpc::Method<global::Msg.economic.SS_GetMerchandiseByTypeReq, global::Msg.economic.SS_GetMerchandiseByTypeResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetMerchandiseByType",
        __Marshaller_SS_GetMerchandiseByTypeReq,
        __Marshaller_SS_GetMerchandiseByTypeResp);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_UseMerchandiseReq, global::Msg.economic.SS_UseMerchandiseResp> __Method_UseMerchandise = new grpc::Method<global::Msg.economic.SS_UseMerchandiseReq, global::Msg.economic.SS_UseMerchandiseResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "UseMerchandise",
        __Marshaller_SS_UseMerchandiseReq,
        __Marshaller_SS_UseMerchandiseResp);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_GetUserLimitPropInfoReq, global::Msg.economic.SS_GetUserLimitPropInfoResp> __Method_GetUserLimitPropInfo = new grpc::Method<global::Msg.economic.SS_GetUserLimitPropInfoReq, global::Msg.economic.SS_GetUserLimitPropInfoResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUserLimitPropInfo",
        __Marshaller_SS_GetUserLimitPropInfoReq,
        __Marshaller_SS_GetUserLimitPropInfoResp);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Msg.economic.SS_InitUserMerchandiseReq, global::Msg.economic.SS_InitUserMerchandiseResp> __Method_InitUserMerchandise = new grpc::Method<global::Msg.economic.SS_InitUserMerchandiseReq, global::Msg.economic.SS_InitUserMerchandiseResp>(
        grpc::MethodType.Unary,
        __ServiceName,
        "InitUserMerchandise",
        __Marshaller_SS_InitUserMerchandiseReq,
        __Marshaller_SS_InitUserMerchandiseResp);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Msg.economic.ServiceReflection.Descriptor.Services[9]; }
    }

    /// <summary>Base class for server-side implementations of MerchandiseInnerService</summary>
    [grpc::BindServiceMethod(typeof(MerchandiseInnerService), "BindService")]
    public abstract partial class MerchandiseInnerServiceBase
    {
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GetMerchandiseChangeInfoResp> GetMerchandiseChangeInfo(global::Msg.economic.SS_GetMerchandiseChangeInfoReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 获取用户的物品
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GetUserMerchandiseInfoResp> GetUserMerchandiseInfo(global::Msg.economic.SS_GetUserMerchandiseInfoReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 设置用户物品
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_SetUserMerchandiseInfoResp> SetUserMerchandiseInfo(global::Msg.economic.SS_SetUserMerchandiseInfoReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 获取商品详情
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GetMerchandiseDetailResp> GetMerchandiseDetail(global::Msg.economic.SS_GetMerchandiseDetailReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 获取用户亲密度宝箱
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GetUserIntimacyBoxInfoResp> GetUserIntimacyInfoBox(global::Msg.economic.SS_GetUserIntimacyBoxInfoReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 根据类型获取商品列表
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GetMerchandiseByTypeResp> GetMerchandiseByType(global::Msg.economic.SS_GetMerchandiseByTypeReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_UseMerchandiseResp> UseMerchandise(global::Msg.economic.SS_UseMerchandiseReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 获取用户限时道具使用情况
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_GetUserLimitPropInfoResp> GetUserLimitPropInfo(global::Msg.economic.SS_GetUserLimitPropInfoReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// 初始化用户道具
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Msg.economic.SS_InitUserMerchandiseResp> InitUserMerchandise(global::Msg.economic.SS_InitUserMerchandiseReq request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for MerchandiseInnerService</summary>
    public partial class MerchandiseInnerServiceClient : grpc::ClientBase<MerchandiseInnerServiceClient>
    {
      /// <summary>Creates a new client for MerchandiseInnerService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public MerchandiseInnerServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for MerchandiseInnerService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public MerchandiseInnerServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected MerchandiseInnerServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected MerchandiseInnerServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetMerchandiseChangeInfoResp GetMerchandiseChangeInfo(global::Msg.economic.SS_GetMerchandiseChangeInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetMerchandiseChangeInfo(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetMerchandiseChangeInfoResp GetMerchandiseChangeInfo(global::Msg.economic.SS_GetMerchandiseChangeInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetMerchandiseChangeInfo, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetMerchandiseChangeInfoResp> GetMerchandiseChangeInfoAsync(global::Msg.economic.SS_GetMerchandiseChangeInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetMerchandiseChangeInfoAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetMerchandiseChangeInfoResp> GetMerchandiseChangeInfoAsync(global::Msg.economic.SS_GetMerchandiseChangeInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetMerchandiseChangeInfo, null, options, request);
      }
      /// <summary>
      /// 获取用户的物品
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetUserMerchandiseInfoResp GetUserMerchandiseInfo(global::Msg.economic.SS_GetUserMerchandiseInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserMerchandiseInfo(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户的物品
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetUserMerchandiseInfoResp GetUserMerchandiseInfo(global::Msg.economic.SS_GetUserMerchandiseInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUserMerchandiseInfo, null, options, request);
      }
      /// <summary>
      /// 获取用户的物品
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetUserMerchandiseInfoResp> GetUserMerchandiseInfoAsync(global::Msg.economic.SS_GetUserMerchandiseInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserMerchandiseInfoAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户的物品
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetUserMerchandiseInfoResp> GetUserMerchandiseInfoAsync(global::Msg.economic.SS_GetUserMerchandiseInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUserMerchandiseInfo, null, options, request);
      }
      /// <summary>
      /// 设置用户物品
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_SetUserMerchandiseInfoResp SetUserMerchandiseInfo(global::Msg.economic.SS_SetUserMerchandiseInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SetUserMerchandiseInfo(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 设置用户物品
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_SetUserMerchandiseInfoResp SetUserMerchandiseInfo(global::Msg.economic.SS_SetUserMerchandiseInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_SetUserMerchandiseInfo, null, options, request);
      }
      /// <summary>
      /// 设置用户物品
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_SetUserMerchandiseInfoResp> SetUserMerchandiseInfoAsync(global::Msg.economic.SS_SetUserMerchandiseInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SetUserMerchandiseInfoAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 设置用户物品
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_SetUserMerchandiseInfoResp> SetUserMerchandiseInfoAsync(global::Msg.economic.SS_SetUserMerchandiseInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_SetUserMerchandiseInfo, null, options, request);
      }
      /// <summary>
      /// 获取商品详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetMerchandiseDetailResp GetMerchandiseDetail(global::Msg.economic.SS_GetMerchandiseDetailReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetMerchandiseDetail(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取商品详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetMerchandiseDetailResp GetMerchandiseDetail(global::Msg.economic.SS_GetMerchandiseDetailReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetMerchandiseDetail, null, options, request);
      }
      /// <summary>
      /// 获取商品详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetMerchandiseDetailResp> GetMerchandiseDetailAsync(global::Msg.economic.SS_GetMerchandiseDetailReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetMerchandiseDetailAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取商品详情
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetMerchandiseDetailResp> GetMerchandiseDetailAsync(global::Msg.economic.SS_GetMerchandiseDetailReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetMerchandiseDetail, null, options, request);
      }
      /// <summary>
      /// 获取用户亲密度宝箱
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetUserIntimacyBoxInfoResp GetUserIntimacyInfoBox(global::Msg.economic.SS_GetUserIntimacyBoxInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserIntimacyInfoBox(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户亲密度宝箱
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetUserIntimacyBoxInfoResp GetUserIntimacyInfoBox(global::Msg.economic.SS_GetUserIntimacyBoxInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUserIntimacyInfoBox, null, options, request);
      }
      /// <summary>
      /// 获取用户亲密度宝箱
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetUserIntimacyBoxInfoResp> GetUserIntimacyInfoBoxAsync(global::Msg.economic.SS_GetUserIntimacyBoxInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserIntimacyInfoBoxAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户亲密度宝箱
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetUserIntimacyBoxInfoResp> GetUserIntimacyInfoBoxAsync(global::Msg.economic.SS_GetUserIntimacyBoxInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUserIntimacyInfoBox, null, options, request);
      }
      /// <summary>
      /// 根据类型获取商品列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetMerchandiseByTypeResp GetMerchandiseByType(global::Msg.economic.SS_GetMerchandiseByTypeReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetMerchandiseByType(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 根据类型获取商品列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetMerchandiseByTypeResp GetMerchandiseByType(global::Msg.economic.SS_GetMerchandiseByTypeReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetMerchandiseByType, null, options, request);
      }
      /// <summary>
      /// 根据类型获取商品列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetMerchandiseByTypeResp> GetMerchandiseByTypeAsync(global::Msg.economic.SS_GetMerchandiseByTypeReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetMerchandiseByTypeAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 根据类型获取商品列表
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetMerchandiseByTypeResp> GetMerchandiseByTypeAsync(global::Msg.economic.SS_GetMerchandiseByTypeReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetMerchandiseByType, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_UseMerchandiseResp UseMerchandise(global::Msg.economic.SS_UseMerchandiseReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return UseMerchandise(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_UseMerchandiseResp UseMerchandise(global::Msg.economic.SS_UseMerchandiseReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_UseMerchandise, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_UseMerchandiseResp> UseMerchandiseAsync(global::Msg.economic.SS_UseMerchandiseReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return UseMerchandiseAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_UseMerchandiseResp> UseMerchandiseAsync(global::Msg.economic.SS_UseMerchandiseReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_UseMerchandise, null, options, request);
      }
      /// <summary>
      /// 获取用户限时道具使用情况
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetUserLimitPropInfoResp GetUserLimitPropInfo(global::Msg.economic.SS_GetUserLimitPropInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserLimitPropInfo(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户限时道具使用情况
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_GetUserLimitPropInfoResp GetUserLimitPropInfo(global::Msg.economic.SS_GetUserLimitPropInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUserLimitPropInfo, null, options, request);
      }
      /// <summary>
      /// 获取用户限时道具使用情况
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetUserLimitPropInfoResp> GetUserLimitPropInfoAsync(global::Msg.economic.SS_GetUserLimitPropInfoReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserLimitPropInfoAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 获取用户限时道具使用情况
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_GetUserLimitPropInfoResp> GetUserLimitPropInfoAsync(global::Msg.economic.SS_GetUserLimitPropInfoReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUserLimitPropInfo, null, options, request);
      }
      /// <summary>
      /// 初始化用户道具
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_InitUserMerchandiseResp InitUserMerchandise(global::Msg.economic.SS_InitUserMerchandiseReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return InitUserMerchandise(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 初始化用户道具
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Msg.economic.SS_InitUserMerchandiseResp InitUserMerchandise(global::Msg.economic.SS_InitUserMerchandiseReq request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_InitUserMerchandise, null, options, request);
      }
      /// <summary>
      /// 初始化用户道具
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_InitUserMerchandiseResp> InitUserMerchandiseAsync(global::Msg.economic.SS_InitUserMerchandiseReq request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return InitUserMerchandiseAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// 初始化用户道具
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Msg.economic.SS_InitUserMerchandiseResp> InitUserMerchandiseAsync(global::Msg.economic.SS_InitUserMerchandiseReq request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_InitUserMerchandise, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override MerchandiseInnerServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new MerchandiseInnerServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(MerchandiseInnerServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GetMerchandiseChangeInfo, serviceImpl.GetMerchandiseChangeInfo)
          .AddMethod(__Method_GetUserMerchandiseInfo, serviceImpl.GetUserMerchandiseInfo)
          .AddMethod(__Method_SetUserMerchandiseInfo, serviceImpl.SetUserMerchandiseInfo)
          .AddMethod(__Method_GetMerchandiseDetail, serviceImpl.GetMerchandiseDetail)
          .AddMethod(__Method_GetUserIntimacyInfoBox, serviceImpl.GetUserIntimacyInfoBox)
          .AddMethod(__Method_GetMerchandiseByType, serviceImpl.GetMerchandiseByType)
          .AddMethod(__Method_UseMerchandise, serviceImpl.UseMerchandise)
          .AddMethod(__Method_GetUserLimitPropInfo, serviceImpl.GetUserLimitPropInfo)
          .AddMethod(__Method_InitUserMerchandise, serviceImpl.InitUserMerchandise).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, MerchandiseInnerServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GetMerchandiseChangeInfo, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GetMerchandiseChangeInfoReq, global::Msg.economic.SS_GetMerchandiseChangeInfoResp>(serviceImpl.GetMerchandiseChangeInfo));
      serviceBinder.AddMethod(__Method_GetUserMerchandiseInfo, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GetUserMerchandiseInfoReq, global::Msg.economic.SS_GetUserMerchandiseInfoResp>(serviceImpl.GetUserMerchandiseInfo));
      serviceBinder.AddMethod(__Method_SetUserMerchandiseInfo, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_SetUserMerchandiseInfoReq, global::Msg.economic.SS_SetUserMerchandiseInfoResp>(serviceImpl.SetUserMerchandiseInfo));
      serviceBinder.AddMethod(__Method_GetMerchandiseDetail, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GetMerchandiseDetailReq, global::Msg.economic.SS_GetMerchandiseDetailResp>(serviceImpl.GetMerchandiseDetail));
      serviceBinder.AddMethod(__Method_GetUserIntimacyInfoBox, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GetUserIntimacyBoxInfoReq, global::Msg.economic.SS_GetUserIntimacyBoxInfoResp>(serviceImpl.GetUserIntimacyInfoBox));
      serviceBinder.AddMethod(__Method_GetMerchandiseByType, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GetMerchandiseByTypeReq, global::Msg.economic.SS_GetMerchandiseByTypeResp>(serviceImpl.GetMerchandiseByType));
      serviceBinder.AddMethod(__Method_UseMerchandise, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_UseMerchandiseReq, global::Msg.economic.SS_UseMerchandiseResp>(serviceImpl.UseMerchandise));
      serviceBinder.AddMethod(__Method_GetUserLimitPropInfo, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_GetUserLimitPropInfoReq, global::Msg.economic.SS_GetUserLimitPropInfoResp>(serviceImpl.GetUserLimitPropInfo));
      serviceBinder.AddMethod(__Method_InitUserMerchandise, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Msg.economic.SS_InitUserMerchandiseReq, global::Msg.economic.SS_InitUserMerchandiseResp>(serviceImpl.InitUserMerchandise));
    }

  }
}
#endregion

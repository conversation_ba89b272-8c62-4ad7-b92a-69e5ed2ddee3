/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/12/04 10:51:49 星期三
# 功能：Nothing
****************************************************
*/

using System.Collections.Generic;
using DG.Tweening;
using FairyGUI;
using Msg.basic;
using Msg.incentive;
using ScriptsHot.Game.Modules.Growth;
using ScriptsHot.Game.Modules.Profile;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Settlement.SettlementUI
{
    public class SettlementGrowthUI : GrowthRadarUIBase<UIBind.Settlement.SettlementGrowthPanel>
    {
        public SettlementGrowthUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top;
        
        protected override bool isFullScreen => true;

        private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);
        private ProfileController _profileController => GetController<ProfileController>(ModelConsts.Profile);
        
        private HeadUrlController _headUrlController => GetController<HeadUrlController>(ModelConsts.HeadUrl);
        
        private DG.Tweening.Sequence _aniSequence;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            AddUIEvent(ui.btnConfirm.onClick, OnBtnConfirmClicked);
            AddUIEvent(ui.rewardItem.btnSetFrame.onClick,OnSetFrame);
            AddUIEvent(ui.rewardItem.btnSetNameStyle.onClick,OnSetNameStyle);
        }

        private void OnBtnConfirmClicked()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            GetController<SettlementController>(ModelConsts.Settlement).ShowNextView(() => { Hide(); });
        }

        protected override void OnShow()
        {
            base.OnShow();
            if (args.Length <= 0)
            {
                VFDebug.LogError("无结算数据");
                return;
            }
            var growthData = (PB_UserGrowthSettlementData)args[0];
            // for local test
            // var growthData = CreateTestData();
            ui.comGrowthLevel.com.xy = ui.imgLevelStartPos.xy;
            ui.comGrowthLevel.com.scale = Vector2.one;
            ui.comGrowthLevel.tfLevelNext.visible = false;
            ui.comGrowthLevel.tfLevelNow.visible = true;
            ui.spStar.visible = false;
            ui.comGrowthLevel.bg.visible = true;
            ui.rewardItem.com.visible = false;
            ui.spProgressStar.visible = false;
            ui.grpRadar.alpha = 1;
            ui.grpOthers.alpha = 1;
            var nextLevel = growthData.growth_level;
            var nowLevel = growthData.pre_growth_level;
            var isShowLevelUp = growthData.growth_level > growthData.pre_growth_level;
            var isReward = (growthData.new_level_reward != null && growthData.new_level_reward.Count > 0 );
            ui.tfTitle.SetKey(isShowLevelUp ? "ui_settlementGrowth_title_level_up" : "ui_settlementGrowth_title");
            ui.btnConfirm.SetKey("ui_settlementGrowth_btn_continue");
            ui.rewardItem.tfSetFrame.SetKey("ui_settlementgrowth_reward_setframe");
            ui.rewardItem.tfSetNameStyle.SetKey("ui_settlementgrowth_reward_setName");
            ui.tfLevelNext.text = (nowLevel + 1).ToString();
            ui.comGrowthLevel.tfLevelNow.text = nowLevel.ToString();
            ui.comGrowthLevel.tfLevelNext.text = nextLevel.ToString();

            ui.btnConfirm.visible = !isShowLevelUp;
            Notifier.instance.SendNotification(NotifyConsts.AddGrowthExp, (int)(growthData.growth_value - growthData.pre_growth_value));

            _headUrlController.SetHead(ui.rewardItem.com,_mainModel.avatarHeadId,_mainModel.avatarHeadType,HeadSource.SettlementGrowth);

            SetRewardInfo(isReward,growthData);
            CreateAniSequence(isShowLevelUp, isReward,growthData);
            AFDots.Appear_Rewards_growth_boosted();
        }


        private void SetRewardInfo( bool isReward,PB_UserGrowthSettlementData growthData)
        {
            if(!isReward)
                return;
            ui.rewardItem.btnSetItemCtrl.selectedIndex = 0;
            var list = growthData.new_level_reward;
            var ctrl = ui.rewardItem.iconCtrl;
            for (int i = 0; i < list.Count; i++)
            {
                if(!Cfg.T.TBItemTable.DataMap.ContainsKey(long.Parse(list[i].merchandise_id)))
                    continue;
                var cfg = Cfg.T.TBItemTable.Get(long.Parse(list[i].merchandise_id));
                if (list[i].merchandise_type == PB_MerchandiseType.MerchandiseType_Economic)
                {
                    ctrl.selectedIndex = 2;
                    ui.rewardItem.iconCurrency.url = cfg.iconNormalizePath;
                    ui.rewardItem.tfCurrencyy.text =  string.Format("<font color={1}>+{0}</font>", list[i].reward_value, "#DC50FF,#681985");                
                    
                }
                else if (list[i].merchandise_type == PB_MerchandiseType.MerchandiseType_DressUp)
                {
                    if (list[i].merchandise_sub_type == PB_MerchandiseSubType.MerchandiseSubType_Frame)
                    {
                        ctrl.selectedIndex = 0;
                        
                        _headUrlController.SetFrame(ui.rewardItem.com,list[i].merchandise_id);

                        ui.rewardItem.btnSetFrame.data = list[i].merchandise_id;
                    }else if (list[i].merchandise_sub_type == PB_MerchandiseSubType.MerchandiseSubType_NameStyle)
                    {
                        ctrl.selectedIndex = 1;
                        _headUrlController.SetName(ui.rewardItem.com,list[i].merchandise_id,_mainModel.playerName);
                        ui.rewardItem.btnSetNameStyle.data = list[i].merchandise_id;
                    }
                }else if (list[i].merchandise_type == PB_MerchandiseType.MerchandiseType_Materia)
                {
                    ctrl.selectedIndex = 2;
                    ui.rewardItem.iconCurrency.url = cfg.iconNormalizePath;
                    ui.rewardItem.tfCurrencyy.text =  string.Format("<font color={1}>+{0}</font>", list[i].reward_value, "#DC50FF,#681985");     
                }
            }
        }
        
        private void OnSetFrame(EventContext context)
        {
            string id = ((context.sender as GGraph).data).ToString();
            PB_SetUserDressUpItem item = new PB_SetUserDressUpItem();
            item.merchandise_id = id;
            item.merchandise_sub_type = PB_MerchandiseSubType.MerchandiseSubType_Frame;
            List<PB_SetUserDressUpItem> dressUpList = new List<PB_SetUserDressUpItem>();
            dressUpList.Add(item);
            if(dressUpList.Count > 0)
                _profileController.RequestSetUserDressUpItem(dressUpList,SetDressUpCallBack);
        }

        private void OnSetNameStyle(EventContext context) 
        {
            string id = ((context.sender as GGraph).data).ToString();
            PB_SetUserDressUpItem item = new PB_SetUserDressUpItem();
            item.merchandise_id = id;
            item.merchandise_sub_type = PB_MerchandiseSubType.MerchandiseSubType_NameStyle;
            List<PB_SetUserDressUpItem> dressUpList = new List<PB_SetUserDressUpItem>();
            dressUpList.Add(item);
            if(dressUpList.Count > 0)
                _profileController.RequestSetUserDressUpItem(dressUpList,SetDressUpCallBack);
        }

        private void SetDressUpCallBack()
        {
            ui.rewardItem.btnSetItemCtrl.selectedIndex = 1;
        }

        protected override void OnHide()
        {
            base.OnHide();
            if (_aniSequence != null)
            {
                _aniSequence.Kill();
                _aniSequence = null;
            }
        }

        private void CreateAniSequence(bool isShowLevelUp, bool isReward,PB_UserGrowthSettlementData growthData)
        {
            
            _aniSequence = DOTween.Sequence();
            _aniSequence.AppendCallback(() =>
            {
                ui.comGrowthRadar.asCom.alpha = 0f;
                ui.comGrowthRadar.asCom.GetChild("spineLoader").visible = false;
                SetProperty(ui.comGrowthRadar.GetChild("comDiligence").asCom, (int)GetProperty(PB_UserGrowthFactorType.Checkin, growthData).growth_value);
                SetProperty(ui.comGrowthRadar.GetChild("comParticipation").asCom, (int)GetProperty(PB_UserGrowthFactorType.Ranking, growthData).growth_value);
                SetProperty(ui.comGrowthRadar.GetChild("comFriendliness").asCom, (int)GetProperty(PB_UserGrowthFactorType.Intimacy, growthData).growth_value);
                InitPropertyAni(ui.comGrowthRadar.GetChild("comDiligence").asCom);
                InitPropertyAni(ui.comGrowthRadar.GetChild("comParticipation").asCom);
                InitPropertyAni(ui.comGrowthRadar.GetChild("comFriendliness").asCom);
                PlayProgressBarAni((int)growthData.pre_growth_value, (int)growthData.growth_value,
                    (int)growthData.pre_level_value_lower, (int)growthData.pre_level_value_upper);
            });
            _aniSequence.AppendInterval(0.3f);
            _aniSequence.AppendCallback(() => { ui.comGrowthRadar.TweenFade(1f, 0.3f); });
            _aniSequence.AppendInterval(0.3f);
            _aniSequence.AppendCallback(() =>
            {
                var checkinData = GetProperty(PB_UserGrowthFactorType.Checkin, growthData);
                var rankingData = GetProperty(PB_UserGrowthFactorType.Ranking, growthData);
                var intimacyData = GetProperty(PB_UserGrowthFactorType.Intimacy, growthData);
                ui.comGrowthRadar.asCom.GetChild("spineLoader").visible = true;
                var sum = (int)((checkinData.coefficient + rankingData.coefficient + intimacyData.coefficient) * 100);
                CreateRadar3D(ui.comGrowthRadar, true, sum,(int)(checkinData.coefficient / checkinData.coefficient_upper * 100),
                    (int)(rankingData.coefficient / rankingData.coefficient_upper * 100),
                    (int)(intimacyData.coefficient / intimacyData.coefficient_upper * 100));
            });
            _aniSequence.AppendInterval(0.5f);
            _aniSequence.AppendCallback(() => { PlayPropertyAni(ui.comGrowthRadar.GetChild("comDiligence").asCom); });
            _aniSequence.AppendInterval(0.2f);
            _aniSequence.AppendCallback(() => { PlayPropertyAni(ui.comGrowthRadar.GetChild("comParticipation").asCom); });
            _aniSequence.AppendInterval(0.2f);
            _aniSequence.AppendCallback(() => { PlayPropertyAni(ui.comGrowthRadar.GetChild("comFriendliness").asCom); });
            _aniSequence.AppendInterval(0.2f);
            if (isShowLevelUp)
            {
                _aniSequence.AppendInterval(1f);
                _aniSequence.AppendCallback(() =>
                {
                    AFDots.Appear_Rewards_level_up();
                    SoundManger.instance.PlayUI("learnpath_yellow_progress_long");
                    ui.grpRadar.TweenFade(0f, 0.1f);
                    ui.grpOthers.TweenFade(0f, 0.1f);
                    ui.comGrowthRadar.asCom.GetChild("spineLoader").visible = false;
                    ui.comGrowthLevel.com.TweenMove(ui.imgCenterPos.xy, 0.5f);
                    ui.comGrowthLevel.com.TweenScale(new Vector2(2.9f, 2.9f), 0.5f);
                });
                _aniSequence.AppendInterval(0.5f);
                _aniSequence.AppendCallback(() =>
                {
                    ui.comGrowthLevel.bg.visible = false;
                    ui.spStar.visible = true;
                    ui.spStar.spineAnimation.AnimationState.SetAnimation(0, "1", false).Complete += entry =>
                    {
                        ui.spStar.spineAnimation.AnimationState.SetAnimation(0, "2", true);
                    };
                });
                _aniSequence.AppendInterval(0.5f);
                _aniSequence.AppendCallback(() =>
                {
                    ui.comGrowthLevel.com.TweenScale(new Vector2(-2.9f, 2.9f), 0.8f);
                });
                _aniSequence.AppendInterval(0.2f);
                _aniSequence.AppendCallback(() =>
                {
                    VibrationManager.Ins.Vibrate(0.4f,0.4f,1.2f);
                    ui.comGrowthLevel.tfLevelNow.visible = false;
                    ui.comGrowthLevel.tfLevelNext.visible = true;
                    ui.btnConfirm.visible = !isReward;
                    ui.rewardItem.com.visible = isReward;
                });
                if (isReward)
                {
                    _aniSequence.AppendInterval(0.1f);
                    _aniSequence.AppendCallback(() =>
                    {
                        // var list = growthData.new_level_reward;
                        // var btnVisible =  true;
                        // for (int i = 0; i < list.Count; i++)
                        // {
                        //     if (list[i].merchandise_type == PB_MerchandiseType.MerchandiseType_DressUp)
                        //     {
                        //         btnVisible = false;
                        //         break;
                        //     }
                        // }
                        ui.rewardItem.com.GetTransition("ani").Play();
                      
                    });
                    _aniSequence.AppendInterval(0.75f);
                    _aniSequence.AppendCallback(() =>
                    {
                        ui.btnConfirm.visible = true;
                    });
                }
            }
        }

        private PB_FactorItemForSettlement GetProperty(PB_UserGrowthFactorType type, PB_UserGrowthSettlementData growthData)
        {
            foreach (var property in growthData.factor_list)
            {
                if (property.factor == type)
                    return property;
            }

            return null;
        }

        private void PlayProgressBarAni(int pre_value, int now_value, int capacityLower, int capacityUpper)
        {
            ui.grpTip.visible = true;
            ui.tfPropertyCnt.text = (now_value - pre_value).ToString();
            var capacity = capacityUpper - capacityLower;
            var tipsPos = ui.grpTip.xy;
            var bar = ui.comExpBar.GetChild("core").asCom.GetChild("bar").asImage;
            var bg = ui.comExpBar.GetChild("core").asCom.GetChild("n0").asGraph;
            var text = ui.comExpBar.GetChild("expText").asTextField;
            var width = bg.size.x;
            var height = bar.height;
            bar.size = new Vector2((float)(pre_value - capacityLower) / capacity * width, height);
            var targetValue = now_value >= capacityUpper ? capacity  : now_value  - capacityLower;
            bar.TweenResize(new Vector2((float)targetValue / capacity * width, height), 0.5f).OnUpdate(() =>
            {
                var newPos = new Vector2(bar.x + bar.size.x - ui.grpTip.size.x/2f, 0);
                ui.grpTip.xy = new Vector2(ui.grpTip.parent.GlobalToLocal(bar.LocalToGlobal(newPos)).x, tipsPos.y);
            }).SetEase(EaseType.QuartInOut).OnComplete(() =>
            {
				VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);
                ui.spProgressStar.visible = true;
                ui.spProgressStar.xy = new Vector2(ui.grpTip.x + 15, ui.spProgressStar.y);
                ui.spProgressStar.spineAnimation.AnimationState.SetAnimation(0, "animation", false).Complete += entry =>
                {
                    ui.spProgressStar.visible = false;
                };
            });
            var num = pre_value;
            DOTween.To(() => num, value => num = value, now_value, 0.5f).OnUpdate(() =>
            {
                text.text = now_value + "/" + capacityUpper;
            }).SetEase(Ease.InOutQuart);
        }

        //创造测试数据，不删除无伤大雅
        private PB_UserGrowthSettlementData CreateTestData()
        {
            var growthData = new PB_UserGrowthSettlementData()
            {
                factor_list =
                {
                    new PB_FactorItemForSettlement()
                    {
                        factor = PB_UserGrowthFactorType.Ranking, growth_value = 10, coefficient = 0.3,coefficient_upper = 0.6,
                    },
                    new PB_FactorItemForSettlement()
                    {
                        factor = PB_UserGrowthFactorType.Checkin, growth_value = 20, coefficient = 0.4,coefficient_upper = 0.6,
                    },
                    new PB_FactorItemForSettlement()
                    {
                        factor = PB_UserGrowthFactorType.Intimacy, growth_value = 30, coefficient = 0.5,coefficient_upper = 0.6,
                    },
                },
                pre_growth_level = 4,
                growth_level = 5,
                pre_growth_value = 420,
                growth_value = 550,
                pre_level_value_upper = 500,
                pre_level_value_lower = 400,
            };
            return growthData;
        }
        
    }
}
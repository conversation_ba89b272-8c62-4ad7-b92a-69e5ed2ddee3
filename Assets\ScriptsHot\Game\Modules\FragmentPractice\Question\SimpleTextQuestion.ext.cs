using CommonUI;
using FairyGUI;
using FairyGUI.Utils;
using Game.Modules.Record;
using Msg.question;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using System;
using System.Collections.Generic;
using Game.Modules.FragmentPractice;
using UnityEngine;
using System.Linq;

namespace UIBind.FragmentPractice
{   //单句型 speak题
    public partial class SimpleTextQuestion : AFragQuestion, IRecordEventListener,IQuestionEventListener
    {
        private int recordId = -1;

        TextFieldExtension TfQuestion => tfQuestion as TextFieldExtension;

        public bool IsReceivingEvents => true;

        override protected void OnAddedToStage()
        {
            TfQuestion.SetNewFont(FontCfg.DinNextRegular, TextFieldExtension.TEXT_SPEAK_UNHIT, 36);
            TfQuestion.TextGtf.textFormat.lineSpacing = 28;

            RecordEventManager.Instance.AddListener(this);
            QuestionEventManager.Instance.AddListener(this);
            //Notifier.instance.RegisterNotification(NotifyConsts.RecordAnswerRefreshMatchResultEvent, RefreshTextRender);
        }

        override protected void OnRemovedFromStage()
        {
            RecordEventManager.Instance.RemoveListener(this);
            QuestionEventManager.Instance.RemoveListener(this);
        }

        #region refresh
        override public void ShowPractice(AFragAnswer answerComp)
        {
            var playAudio = Practice.AudioId != 0;
            btnPlayAudio.AudioId = Practice.AudioId;
            audio.selectedPage = playAudio ? "visible" : "invisible";

            if (playAudio && IsCurrent)
            {
                btnPlayAudio.Play();
            }

            btnPlayAudio.onClick.Add(OnClickBtnPlayAudio);

            FillQuestionText();
        }

        private void OnClickBtnPlayAudio(EventContext context)
        {            
            RecordEventManager.Instance.DispatchRecordCancel();
            DotPracticeManager.Instance.Collect(new DataDot_PlayTTS());
        }

        private void FillQuestionText()
        {
            TextFieldExtension textExt = TfQuestion;
            textExt.Reset();

            textExt.onTranslated += (word, translation) =>
            {
                DotPracticeManager.Instance.Collect(new DataDot_Translate(word, translation));
            };
            textExt.StartNewWordTrans();
            List<RichContent> contents = RichContentUtil.SplitPracticeWords(Practice.GetStem(), Practice.QuestionType,
                Practice.GetBlankRanges(), QuestionEventManager.Instance.State == QuestStateEnum.Submit);

            foreach (var richContent in contents)
                textExt.AppendContent(richContent);


            //SpeechToTextManager.instance.ResetRecognizedWords();//高度依赖此时 内部的refText是已经赋值过的状态
            //var matchResults = SpeechToTextManager.instance.MatchWordsInReference(TextFieldExtension.TEXT_SPEAK_HIT, TextFieldExtension.TEXT_SPEAK_UNHIT);

            //step3 遍历matchResults每个子mr，根据_datas内的 mask信息，对mr中需要Hide的 displayword做隐藏
            //todo:  MarkHideInMatchResults(matchResults, true);//会交叉 datas和 matchResults并且改变results内的部分hide相关的信息，这部分信息对于 一次性Display完成很重要
            //textExt.SetEvaluateResult(matchResults);

            if (Practice.QuestionType == PB_QuickPracticeType.readSenAndSpeakSen)
            {
                textExt.Display(ShowMode.SpeakInit);
            }
            else 
            {
                textExt.Display(ShowMode.QuestionNormal);   //SpeakInit没有在这里使用，因为这里面既要使用mask信息，又要表现为underline的样式，而非speak的box
            }
          
            textExt.AddDot(contents, true);
            textExt.ResizeToFit();
        }
        #endregion

        #region IRecordEventListener
        public void OnRecordStart(string rawString, int recordId) 
        { 
            this.recordId = recordId;

            // 关闭可能的 TTS
            TTSManager.instance.StopTTS();
        }

        public void OnRecordStop()
        {
            this.recordId = -1;
            RefreshResult(TextFieldExtension.TEXT_SPEAK_FINAL_HIT, TextFieldExtension.TEXT_SPEAK_UNHIT);
        }

        public void OnRecordCancel() { OnRecordStop(); }

        public void OnVad() { OnRecordStop(); }

        public void OnCountDown() { OnRecordStop(); }

        public void OnMatchAll() { OnRecordStop(); }

        public void OnTranscription(string transcribedText, int recordId)
        {
            if (this.recordId != recordId) return;
            if (GMicrophoneManager.instance.IsRecording)
            {
                RefreshResult(TextFieldExtension.TEXT_SPEAK_HIT, TextFieldExtension.TEXT_SPEAK_UNHIT);
            }
            else
            {
                RefreshResult(TextFieldExtension.TEXT_SPEAK_FINAL_HIT, TextFieldExtension.TEXT_SPEAK_UNHIT);
            }

            //暂未启用，基本结果应该是一致的
        }

        private void RefreshResult(Color clrMatch, Color clrNotMatch)
        {
            // TfQuestion.displayObject.cacheAsBitmap = true; 
            List<MatchResult> result = SpeechToTextManager.instance.MatchWordsInReference(clrMatch, clrNotMatch);
            
            
            TfQuestion.SetEvaluateResult(result);
            TfQuestion.Display(ShowMode.SpeakEval);//强制重写文本区域 不需要clear
        }
        #endregion

        public void OnAnswered()
        {
            
        }

        public void OnSubmit()
        {
            if (Practice.QuestionType == PB_QuickPracticeType.listenSenAndChooseWord)
                FillQuestionText();
        }

        public void OnRetry()
        {
            FillQuestionText();
        }
        
        public void AutoCheck(){}
        public void OnReset(){}
        public void OnJumpListenTask() { }
        public void OnJumpSpeakTask() { }
    }
}
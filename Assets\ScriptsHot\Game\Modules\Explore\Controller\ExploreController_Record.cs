﻿using System;
using Msg.dialog_task;
using Msg.explore;
using Msg.incentive;
using ScriptsHot.Game.Modules.Explore;

public enum RecordState
{
    Normal,
    Recording,
    Loading,
    Finish,
}

/// <summary>
///  _recordUI 按钮处理
/// </summary>
public partial class ExploreController:BaseController
{
    private RecordUI _recordUI;
    
    /// <summary>
    /// action : 0 active  1 脚手架
    /// </summary>
    private BulbType _isScaffoldOrActive = BulbType.Active;
   
    
    /// <summary>
    /// 是否等待提交
    /// </summary>
    private bool _pendingSubmit = false;
    
    /// <summary>
    /// 录音超时定时器ID
    /// </summary>
    private string _recordTimeoutTimerId = string.Empty;
    
    /// <summary>
    /// 是否已收到录音开始响应
    /// </summary>
    private bool _receivedStartResponse = false;
    
    /// <summary>
    /// 但钱录音界面状态
    /// </summary>
    public RecordState RecordState = RecordState.Normal;

    public bool RecordLock = false;

    public ExploreController(string name) : base(name)
    {
    }

    /// <summary>
    /// 进入新的阶段 清理脚手架
    /// </summary>
    public void ClearSaffold()
    {
        _isScaffoldOrActive = BulbType.Active;
    }
    
    public void CloseRecordUI()
    {
        HideRecordUI();
    }
    
    private void OnExploreShowRecordUI(string s, object body)
    {
        ShowRecordUI(!IsSeniorPlayer);
    }

    public void HideRecordUI()
    {
        VFDebug.Log("Explore关闭 录音界面！！！！！");
        if (_recordUI == null) return;
        if (GetUI<RecordUI>(UIConsts.RecordUI).isShow)
        {
            GetUI<RecordUI>(UIConsts.RecordUI).Hide();
        }
    }

    public void ShowRecordUI(bool isSeniorPlayer = false)
    {
        _recordUI = this.GetUI<RecordUI>(UIConsts.RecordUI);
        
        if (!GetUI<RecordUI>(UIConsts.RecordUI).isShow)
        {
            GetUI<RecordUI>(UIConsts.RecordUI).Show().onCompleted += () =>
            {
                _recordUI.ShowRecord(true, OnRecordingUserAudio, OnSendClick, OnCancelClick,true,SendStartRecord,false,OnCompleteClickButton,true);
                _recordUI.OpenNewSocial(true);
                _recordUI.SetTouchLong(true);
                _recordUI.HideNewRecordText();
                RecordState = RecordState.Normal;
                UIManager.instance.SwitchLayer(UIConsts.RecordUI, UILayerConsts.HomePage);

                _recordUI.ShowBulbNew(OnBulbClick,_isScaffoldOrActive);
                OnBulbClick(_isScaffoldOrActive,false);
                
                if (RecordLock)
                {
                    _recordUI.HideBulbNew();
                    _recordUI.Lock();
                }
            
            };
        }
        else
        {
            _recordUI.ShowRecord(true, OnRecordingUserAudio, OnSendClick, OnCancelClick,true,SendStartRecord,false,OnCompleteClickButton,true);
            _recordUI.OpenNewSocial(true);
            _recordUI.SetTouchLong(true);
            _recordUI.HideNewRecordText();
            RecordState = RecordState.Normal;
            UIManager.instance.SwitchLayer(UIConsts.RecordUI, UILayerConsts.HomePage);
            _recordUI.ShowBulbNew(OnBulbClick,_isScaffoldOrActive);
            OnBulbClick(_isScaffoldOrActive,false);
            
            if (RecordLock)
            {
                _recordUI.HideBulbNew();
                _recordUI.Lock();
            }
        }
    }

    //开始点击录音
    private void OnRecordingUserAudio()
    {
        RecordState = RecordState.Recording;
        //停止所有声音
        Notifier.instance.SendNotification(NotifyConsts.ExploreSoundStop);
        //清空队列
        Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
        Notifier.instance.SendNotification(NotifyConsts.ExploreStopCellAudioEffect);

        this.CurNetStrategy.SendDialogReq(PB_Explore_DialogUpBizEvent.EO_USER_MANUAL_START,this._curTaskId,this._dialogMode);

        RequestAsr("",false,true,String.Empty,true);
        IfCanMicrophone = true;
        
        // 重置响应标志
        _receivedStartResponse = false;
        
        // 清除之前的定时器
        ClearRecordTimeoutTimer();
        
        ModelDialogManager?.EnterPlayerSpeakingState();
        
        // 设置10秒超时定时器
        _recordTimeoutTimerId = TimerManager.instance.RegisterTimer((a) =>
        {
            if (!_receivedStartResponse)
            {
                VFDebug.LogError("录音开始请求超时，执行取消操作");
                OnCancelRecored();
            }
            _recordTimeoutTimerId = string.Empty;
        }, ExploreConst.MicStartDelayTime); // 10秒超时

        CLICK_EXPLORE_MIC_START dot = new CLICK_EXPLORE_MIC_START();
        dot.task_id = this._curTaskId;
        dot.dialogue_id = this.CurEnterEntity.LogicEntity.DialogId;
        dot.dialogue_round = this.CurEnterEntity.LogicEntity.Round;
        DataDotMgr.Collect(dot);
        
        //test--
        ExploreTwoWayNetStrategy test = (ExploreTwoWayNetStrategy)this._netStrategy;
        if(test != null)
        {
            //VFDebug.LogError($"_beginASRStream :{test.BeginASRStream}");
        }

        //test--end
    }
    
    /// <summary>
    /// 清除录音超时定时器
    /// </summary>
    private void ClearRecordTimeoutTimer()
    {
        if (!string.IsNullOrEmpty(_recordTimeoutTimerId))
        {
            TimerManager.instance.UnRegisterTimer(_recordTimeoutTimerId);
            _recordTimeoutTimerId = string.Empty;
        }
    }
    
    //点击对号
    private void OnSendClick(DownsideEvent evt)
    {
        // VFDebug.LogError("OnCompleteClick");

        if (!IsConsumeTime)
        {
            AskExperienceTimeBegin();
        }


        RecordState = RecordState.Loading;
        Notifier.instance.SendNotification(NotifyConsts.ExploreStepStateChange,ExploreStep.Recording);
 
        ClearSaffold();
        _recordUI.ShowRecordLoading();
        HideBulbLight();
        Notifier.instance.SendNotification(NotifyConsts.ExploreClearScreen);
        
        // 检查是否已经收到了返回协议
        if (IfCanMicrophoneSend)
        {
            TimerManager.instance.RegisterTimer((a) =>
            {
                _netStrategy.StopASR();
                // 如果已经收到了返回协议，直接发送提交协议
                this.CurNetStrategy.SendDialogReq(PB_Explore_DialogUpBizEvent.EO_USER_MANUAL_SUBMIT, this._curTaskId, this._dialogMode);
            }, 300);  // 这个值 是根据 net 写入  设置的间隔200 ms定的
            
        }
        else
        {
            // 设置等待提交标志
            _pendingSubmit = true;
        }
        
        CLICK_EXPLORE_MIC_END dot = new CLICK_EXPLORE_MIC_END();
        dot.mic_sec = _recordUI.CurTime;
        dot.task_id = this._curTaskId;
        dot.dialogue_id = this.CurEnterEntity.LogicEntity.DialogId;
        dot.dialogue_round = this.CurEnterEntity.LogicEntity.Round;
        DataDotMgr.Collect(dot);
    }
    
    //点击取消按钮 取消发送数据
    public void OnCancelClick(DownsideEvent evt)
    {
        RecordState = RecordState.Normal;
        this.CurNetStrategy.SendDialogReq(PB_Explore_DialogUpBizEvent.EO_USER_CANCEL_UPLOAD,this._curTaskId,this._dialogMode);
        VFDebug.Log("OnCancelClick");
        OnCancelRecored();

        CLICK_EXPLORE_MIC_CANCEL dot = new CLICK_EXPLORE_MIC_CANCEL();
        dot.task_id = this._curTaskId;
        dot.dialogue_id = this.CurEnterEntity.LogicEntity.DialogId;
        dot.dialogue_round = this.CurEnterEntity.LogicEntity.Round;
        DataDotMgr.Collect(dot);
    }
    
    public void OnCancelRecored()
    {
        ModelDialogManager?.EnterDialogueIdleState();
        
        _pendingSubmit = false;
        if (_curExplore != null && _curExplore.Type == ExploreOperationType.Active && !IsPause)
        {
            _netStrategy.ClearASRWithCancel();

            if (_recordUI == null) return;
            _recordUI.ShowRecord(true, OnRecordingUserAudio, OnSendClick, OnCancelClick,true,SendStartRecord,false,OnCompleteClickButton,true);
            _recordUI.OpenNewSocial(true);
            _recordUI.SetTouchLong(true);
            RecordState = RecordState.Normal;
            UIManager.instance.SwitchLayer(UIConsts.RecordUI, UILayerConsts.HomePage);
            _recordUI.ShowBulbNew(OnBulbClick,_isScaffoldOrActive);
            OnBulbClick(_isScaffoldOrActive,false);
            
            if (RecordLock)
            {
                _recordUI.HideBulbNew();
                _recordUI.Lock();
            }
        }
    }
    
    /// <summary>
    /// 自动出脚手架功能 已经屏蔽
    /// </summary>
    /// <param name="s"></param>
    /// <param name="body"></param>
    private void OnClickScaffoldBtn(string s, object body)
    {
        _isScaffoldOrActive = BulbType.Scaffold;
        OnBulbClick(_isScaffoldOrActive,false);
    }

    /// <summary>
    /// 脚手架 type :  0 active 1 脚手架
    /// </summary>
    private void OnBulbClick(BulbType type,bool isClick)
    {
        _isScaffoldOrActive = type;
        if (isClick)
        {
            CLICK_EXPLORE_EXAMPLE_BUTTON dot = new CLICK_EXPLORE_EXAMPLE_BUTTON();
            dot.task_id = this._curTaskId;
            dot.dialogue_id = this.CurEnterEntity.LogicEntity.DialogId;
            dot.dialogue_round = this.CurEnterEntity.LogicEntity.Round;
            DataDotMgr.Collect(dot);
        }

        if (_isScaffoldOrActive == BulbType.Active)
        {
            Notifier.instance.SendNotification(NotifyConsts.ExploreShowActive,isClick);
        }
        else
        {
            Notifier.instance.SendNotification(NotifyConsts.ExploreShowScaffold,isClick);   
        }
    }
    private void SendStartRecord()
    {
        // HideBulbLight();
    }
    
    public void OnCompleteClickButton()
    {
    }
    
    public void ShowBulbScaffold()
    {
        _recordUI.ShowBulbScaffoldNew();
    }
    
    public void ShowBulbActive()
    {
        _recordUI.ShowBulbActiveNew();
    }
    public void HideBulbLight()
    {
        _isScaffoldOrActive = BulbType.Active;
        _recordUI.HideBulbNew();
    }

    public void HideRecordButton()
    {
        _recordUI.HideRecordButton();
    }

    public void ShowNextRecord(bool value)
    {
        _recordUI.ShowNext(value);
    }
    

    private string _showFinishTimeFlag = String.Empty;
    public void ShowFinish()
    {
        RecordState = RecordState.Finish;
        ClearShowFinishTime();

        _showFinishTimeFlag = TimerManager.instance.RegisterTimer((a) =>
        {
            HideAllView();
            _recordUI.ShowFinish(true,OnEndRecordClick);
            _recordUI.HideBulbNew();
            _showFinishTimeFlag = String.Empty;
        }, 800);// 由于 完整数据 是 与 avatar 一条协议过来，产品 想让avatar先显示 再显示完成
    }

    private void ClearShowFinishTime()
    {
        if (!string.IsNullOrEmpty(_showFinishTimeFlag))
        {
            TimerManager.instance.UnRegisterTimer(_showFinishTimeFlag);
            _showFinishTimeFlag = String.Empty;
        }
    }

    private void OnEndRecordClick()
    {
    }

    /// <summary>
    /// 主动结束对话，且进入结算
    /// </summary>
    /// <param name="s"></param>
    /// <param name="body"></param>
    private void OnEndRecordEvent(string s, object body)
    {
        SoundManger.instance.PlayUI("audio_record_record");
        OnEndRecordClick();

        //新埋点：点击end
        DataDotDialogueEnd dot = new DataDotDialogueEnd();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        dot.Dialogue_round = GetModel<ChatModel>(ModelConsts.Chat).curRoundId;
        DataDotMgr.Collect(dot);
    }

    public void HideAllView()
    {
        if (_recordUI.isShow)
        {
            _recordUI.HideBulbNew();
            _recordUI.HideRecordButton();
        }
    }

    /// <summary>
    /// 请求开始体验倒计时
    /// </summary>
    private async void AskExperienceTimeBegin()
    {
        VFDebug.Log("AskExperienceTimeBegin !!!!!!!!");
        CS_StartConsumeReq msg = new CS_StartConsumeReq();
        msg.consume_type = PB_ConsumeType.CT_ExploreTime;
        var resp = await MsgManager.instance.SendAsyncMsg<SC_StartConsumeAck>(msg);
        if (resp == null || resp.code != 0)
            return;
        
        //请求成功
        IsConsumeTime = true;
        CanExperienceTimeDown = IsConsumeTime;
        VFDebug.Log("请求体验时间成功");
    }

    /// <summary>
    /// 对话下行 - 业务事件
    /// </summary>
    /// <param name="msg"></param>
    private void OnDialogDownMsgForBizEvent(SC_DialogDownMsgForBizEvent msg)
    {
        //收到消息 就当网络恢复了
        Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.NoNetworkTipUI);
        VFDebug.Log($"log---Explore收到对话下行业务事件:{msg.bizEvent} + msg.code：{msg.code}");
        
        //test 延迟
        // TimerManager.instance.RegisterTimer((a) =>
        // {
        switch (msg.bizEvent)
        {
            case PB_Explore_DialogUpBizEvent.EO_USER_MANUAL_START://用户手动开始
                //VFDebug.LogError($"Explore收到开始事件:{msg.bizEvent} + msg.code：{msg.code}");
                
                // 如果已经超时取消了录音，则忽略这个消息
                if (_recordTimeoutTimerId == string.Empty && !_receivedStartResponse)
                {
                    VFDebug.Log("Explore录音已超时取消，忽略后续消息");
                    return;
                }
                
                // 标记已收到响应
                _receivedStartResponse = true;
                // 清除超时定时器
                ClearRecordTimeoutTimer();
                
                if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
                {
                    IfCanMicrophoneSend = true;
                    
                    // 如果等待提交，则发送提交协议
                    if (_pendingSubmit)
                    {
                        TimerManager.instance.RegisterTimer((a) =>
                        {
                            _pendingSubmit = false;
                            _netStrategy.StopASR();
                            this.CurNetStrategy.SendDialogReq(PB_Explore_DialogUpBizEvent.EO_USER_MANUAL_SUBMIT, this._curTaskId, this._dialogMode);
                        }, 300); //等待音频写入
                    }
                }
                else
                {
                    //开始录音失败
                    VFDebug.LogError("Explore  开始录音失败");
                    _pendingSubmit = false; // 重置等待提交标志
                }
        
                break;
            case PB_Explore_DialogUpBizEvent.EO_USER_SWITCH_MANUAL://切换手动模式
                if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
                {
                }
                else
                {
                    
                }
                break;
            case PB_Explore_DialogUpBizEvent.EO_USER_SWITCH_AUTO://切换自动模式
                if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
                {
                }
                else
                {
                }
                break;
        }
        // }, 7000);

    }

    private bool _test_touch = false;
    public void Test()
    {
        _recordUI.SetTouchLong(_test_touch);
        _test_touch = !_test_touch;
    }
}

public class RemoveCellInfo
{
    public int BubbleId = 0;
    public ChatCellType Type = ChatCellType.Empty;

}

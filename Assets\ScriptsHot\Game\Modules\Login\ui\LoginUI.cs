﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using CommonUI;
using FairyGUI;
using UnityEngine;
using Google;
using UIBind.Login;
using ScriptsHot.Game.UGUI.WebView;
using Object = UnityEngine.Object;

public partial class LoginUI : BaseUI<UIBind.Login.Login0416Panel>
{
    public LoginUI(string name) : base(name) { }
    public override string uiLayer => UILayerConsts.Home;
    protected override bool isFullScreen => true;

    private bool isLogining = false;


    
    enum loginTp
    {
        normal_ios,
        normal_android,
        mail_login,
        mail_regis,
        mail_verifi,
        mail_passward,
    }
    enum loginBtnController
    {
        apple,
        google,
        cn,
        mail
    }
    
    protected override void OnInit(GComponent uiCom)
    {
        DataDotAppear_Talkit_entry model = new DataDotAppear_Talkit_entry();
        DataDotMgr.Collect(model);

        AddUIEvent(ui.tfAgreement.onClickLink, OnTfAgreementClickLink);
        AddUIEvent(ui.btnAgreee.onClick,OnClickAgree);
        AddUIEvent(ui.LoginBtnDebug.com.onClick, OnDebugLoginBtnClick);

        InitMailLogin();
        InitMailRegis();
        InitNormalBtns();

    }
    private void InitNormalBtns()
    {
        /*
         *
    三个按钮在ios和安卓上的顺序不一样：
  - ios：
    - Apple
    - 邮箱
    - Google
  - 安卓：
    - Google
    - 邮箱
         */
        bool isAndroid = Application.platform == RuntimePlatform.Android;

        ui.normalLoginBtn3.com.visible = !isAndroid;
        
        if (isAndroid)
        {
            ui.normalLoginBtn1.platform.selectedIndex = (int)loginBtnController.google;
            ui.normalLoginBtn2.platform.selectedIndex = (int)loginBtnController.mail;
            AddUIEvent(ui.normalLoginBtn1.com.onClick , OnGoogleLoginBtnClick);
            AddUIEvent(ui.normalLoginBtn2.com.onClick , OnMailChangeBtnClick);
        }
        else
        {
            ui.normalLoginBtn1.platform.selectedIndex = (int)loginBtnController.apple;
            ui.normalLoginBtn2.platform.selectedIndex = (int)loginBtnController.mail;
            ui.normalLoginBtn3.platform.selectedIndex = (int)loginBtnController.google;
            AddUIEvent(ui.normalLoginBtn1.com.onClick , OnAppleLoginBtnClick);
            AddUIEvent(ui.normalLoginBtn2.com.onClick , OnMailChangeBtnClick);
            AddUIEvent(ui.normalLoginBtn3.com.onClick , OnGoogleLoginBtnClick);
        }
    }

    private void InitAllI18n()
    {
        ui.forgetPswBtn.text = I18N.inst.MoStr("ui_login02");
        ui.regitBtn.text = I18N.inst.MoStr("ui_login03");
        
        bool isAndroid = Application.platform == RuntimePlatform.Android;
        if (isAndroid)
        {
            ui.normalLoginBtn1.title_google.text = I18N.inst.MoStr("ui_login33");
            ui.normalLoginBtn2.title_mail.text = I18N.inst.MoStr("ui_login01");
        }
        else
        {
            ui.normalLoginBtn1.title_apple.text = I18N.inst.MoStr("ui_login32");
            ui.normalLoginBtn2.title_mail.text = I18N.inst.MoStr("ui_login01");
            ui.normalLoginBtn3.title_google.text = I18N.inst.MoStr("ui_login33");
        }

    }

    protected override void OnShow()
    {
        appear_login_landing_panel();
        
        ui.input_mail.text = String.Empty;
        ui.input_mail_psw.text = String.Empty;

        // ui.loginDesc.text = $"<b>{I18N.inst.MoStr("ui_login04")}</b> {I18N.inst.MoStr("ui_login05")}";;
        ui.loginDesc.text = I18N.inst.MoStr("ui_login04");
        ui.loginDesc2.text = I18N.inst.MoStr("ui_login05");
        SetTipDisplay(false);
        ui.btnAgree.selected = true;
        ui.btnAgree.visible = false;
        //RefreshNormalBtnsState(false);
        InitAllI18n();
        ConfigUI();
        GameEntry.LoginC.HideDefaultCanvas();
        Debug.Log("[LoginCNUI]Opened");
        updateTimer = TimerManager.instance.RegisterTimer(MyUpdate, 1000, int.MaxValue);
        
        Debug.Log($"LoginConst.usertermsUrl = {LoginConst.usertermsUrl}");
        Debug.Log($"LoginConst.privacyUrl = {LoginConst.privacyUrl}");

        SetUIType2Normal();
        
        Debug.Log("[LoginUI]Opened");
    }
    
    private void SetUIType2Normal()
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            SetUIType(loginTp.normal_android);
        }
        else
        {
            SetUIType(loginTp.normal_ios);
        }
        CheckIsDebug();
    }

    private void RefreshNormalBtnsState(bool flag)
    {
        ui.normalLoginBtn1.state.selectedIndex = flag ? 0 : 1;
        ui.normalLoginBtn2.state.selectedIndex = flag ? 0 : 1;
        ui.normalLoginBtn3.state.selectedIndex = flag ? 0 : 1;
    }

    private void SetUIType(loginTp tp)
    {
        ui.tp.selectedIndex = (int)tp;
    }
    
    private void CheckIsDebug()
    {
        bool isDebug = AppConst.AppChannel == LoginChannel.debug;
        bool isNormalTp = ui.tp.selectedIndex == (int)loginTp.normal_ios || ui.tp.selectedIndex == (int)loginTp.normal_android;
        bool isShowDebug = isDebug && isNormalTp;
        ui.isDebug.selectedIndex = isShowDebug ? 0 : 1;
        if (isShowDebug)
        {
            ui.comDebug.tfInput.text = LocalCfgMgr.instance.GetGlobal("debug_account" , string.Empty);
            ui.LoginBtnDebug.platform.selectedIndex = (int)loginBtnController.cn;
            ui.LoginBtnDebug.title_cn.text = "debug登录";
        }
    }
    
    private void SetTipDisplay(bool flag)
    {
        flag = false;
        ui.tipsObj.visible = flag;
    }
    
    protected override void OnHide()
    {
        if (!string.IsNullOrEmpty(updateTimer))
        {
            TimerManager.instance.UnRegisterTimer(updateTimer);
            updateTimer = string.Empty;
        }
        GetUI(UIConsts.CommBusy).Hide();
    }

    public void SetBtnStartDisable(bool flag)
    {
        // if (flag)
        // {
        //     startBtnCtrl.selectedIndex = 0;
        //     startBtnCtrl2.selectedIndex = 0;
        // }
        // else
        // {
        //     startBtnCtrl.selectedIndex = 1;
        //     startBtnCtrl2.selectedIndex = 1;
        // }

        isLogining = !flag;
        if(isLogining)
            GetUI(UIConsts.CommBusy).Show();
        else
            GetUI(UIConsts.CommBusy).Hide();
        VFDebug.Log("isLogining  "+isLogining);
    }

    
    
    private bool CheckIsAgree()
    {
        bool isAgree = ui.btnAgree.selected;
        if (!isAgree)
        {
            SetTipDisplay(true);
        }
        return isAgree;
    }
    private void OnClickAgree(EventContext context)
    {
        return;
        if (!ui.btnAgree.selected)
        {
            AFHelper.Click_Privacy_policy();  
            ui.btnAgree.selected = true;
            SetTipDisplay(false);
        }
        else
        {
            ui.btnAgree.selected = false;
        }
        // RefreshNormalBtnsState(ui.btnAgree.selected);
    }
    
    private void OnTfAgreementClickLink(EventContext e)
    {        
        string url = e.data as string;
        if (url == "userterms")
        {
#if UNITY_EDITOR
            OpenUrl(LoginConst.usertermsUrl);
#else
            OpenUrl(LoginConst.usertermsUrl);
            //DeviceAdapter.OpenWebPage(LoginConst.usertermsUrl);
#endif
        }
        else if (url == "privacy")
        {
#if UNITY_EDITOR
            OpenUrl(LoginConst.privacyUrl);
#else
            OpenUrl(LoginConst.privacyUrl);
            //DeviceAdapter.OpenWebPage(LoginConst.privacyUrl);
#endif
        }
    }

    private void ConfigUI()
    {
        // string fmtAllStr = string.Format("[color=#111111]{0}[/color]", I18N.inst.MoStr("login_privacy_desc"));
        // string fmtUserterms = string.Format("[url=userterms][color=#6632ff]{0}[/color][/url]", I18N.inst.MoStr("login_privacy_userterms_link"));
        // string fmtPrivacy = string.Format("[url=privacy] [color=#6632ff]{0}[/color][/url]", I18N.inst.MoStr("login_privacy_link"));
        // string content = string.Format(fmtAllStr, fmtUserterms, fmtPrivacy);
        // ui.tfAgreement.text = content;
        
        string fmtAllStr = string.Format("[color=#111111]{0}[/color]", I18N.inst.MoStr("ui_login34"));
        string fmtUserterms = string.Format("[url=userterms][color=#6632ff]{0}[/color][/url]", I18N.inst.MoStr("login_privacy_userterms_link"));
        string fmtPrivacy = string.Format("[url=privacy] [color=#6632ff]{0}[/color][/url]", I18N.inst.MoStr("login_privacy_link"));
        string content = string.Format(fmtAllStr, fmtUserterms, fmtPrivacy);
        ui.tfAgreement.text = content;
    }

    private void OpenUrl(string url)
    {
        MainModel mainModel = GetModel<MainModel>(ModelConsts.Main);

        // GameObject ctlPrefab = Resources.Load<GameObject>("Prefabs/WebViewCtl");
        GameObject ctlPrefab = GResManager.instance.LoadPrefab("WebViewCtl");     
        GameObject newCtl = Object.Instantiate(ctlPrefab);
            
        WebViewCtl ctl = newCtl.GetComponent<WebViewCtl>();
        if (ctl == null)
        {
            ctl = newCtl.AddComponent<WebViewCtl>();
        }
        ctl.Init(10f, I18N.inst.MotherLanguage.ToString(), I18N.inst.ForeignLanguage.ToString(), mainModel.toKen, I18N.inst.MotherLanguage.ToString(),
            true,
            true,
            () =>
            { 
                GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
                {
                    GetUI<MainHeaderUI>(UIConsts.MainHeader).Refresh();
                });
            },() =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            },
            () =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            }
        );
        ctl.LoadUrl(url);
    }
    
    private void OnGoogleLoginBtnClick()
    {
#if UNITY_EDITOR
        GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("phone only");
#else
        if (!CheckIsAgree())
        {
            return;
        }
        GameEntry.LoginC.DoGoogleSignInStart();
#endif

        click_login_landing_panel_google_button();
    }
    
    private void OnAppleLoginBtnClick()
    {
        if (Application.platform != RuntimePlatform.IPhonePlayer)
        {
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("Iphone only", pos:2);
            return;
        }

        if (!CheckIsAgree())
        {
            return;
        }
        AFHelper.Click_Apple_id();
        StartUpAmazonBI.click_apple_id();
        
        GameEntry.LoginC.DoAppleSignInStart();

        click_login_landing_panel_apple_button();
    }

    private void OnMailChangeBtnClick()
    {
        if (!CheckIsAgree())
        {
            return;
        }
        if (ui.tp.selectedIndex == (int) loginTp.normal_android ||
            ui.tp.selectedIndex == (int) loginTp.normal_ios)
        {
            SetUIType2MailLogin();
        }
        CheckIsDebug();

        click_login_landing_panel_email_button();
        appear_login_sign_in_panel();
    }

    private void SetUIType2MailLogin()
    {
        SetUIType(loginTp.mail_login);
        ui.input_mail.text = String.Empty;
        ui.input_mail_psw.text = String.Empty;
        ui.input_mail.promptText = $"[color=#B2B2B2][size=32]{I18N.inst.MoStr("ui_login07")}[/color]";
        ui.input_mail_psw.promptText = $"[color=#B2B2B2][size=32]{I18N.inst.MoStr("ui_login08")}[/color]";
        ui.forgetPswBtn.text = $"[color=#6632FF]{I18N.inst.MoStr("ui_login09")}[/color]";
        ui.regitBtn.text = $"[color=#6632FF]{I18N.inst.MoStr("ui_login03")}[/color]";
        ui.loginMailBtn.txt1.text = I18N.inst.MoStr("ui_login06");
        ui.loginMailBtn.txt2.text = I18N.inst.MoStr("ui_login06");
        ui.signTxt.text = I18N.inst.MoStr("ui_login06");
            
        OnInputMailChanged();
        OnInputMailPswChanged();
        ui.input_mail.RequestFocus();
    }

    
    private void OnDebugLoginBtnClick()
    {
        GameEntry.LoginC.DoUILoginStart(ui.comDebug.tfInput.text , true);
        LocalCfgMgr.instance.SetGlobal("debug_account", ui.comDebug.tfInput.text);
        Debug.Log($"[TestAccount] {ui.comDebug.tfInput.text}");
    }


    public void GMFillAccount(string account)
     {
         ui.comDebug.tfInput.text = account;
     }

     public void GMQuickLogin(string account)
     {
         ui.comDebug.tfInput.text = account;
         OnClickAgree(null);
         OnDebugLoginBtnClick();
     }

     
     private void ttt()
     {
         if (string.IsNullOrEmpty(ui.input_mail.text) )
         {
             GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip1", pos:2);
             return;
         }
         if (string.IsNullOrEmpty(ui.input_mail_psw.text))
         {
             GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip3", pos:2);
             return;
         }
        
         Debug.Log($"server = {SignVerifiCodeHelper.Ins.CacheVerification_code}");
         Debug.Log($"ui = {ui.input_mail_psw.text}");
         if (!SignVerifiCodeHelper.Ins.CachePhoneNum.Equals(ui.input_mail.text))
         {
             GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip6", pos:2);            
             return;
         }
        
         if (!SignVerifiCodeHelper.Ins.CacheVerification_code.Equals(ui.input_mail_psw.text))
         {
             requestVerifiCodeErrorCount++;
 
             OnBtnClearMailPswClick();
             if (requestVerifiCodeErrorCount >= REQUEST_VERIFICODE_MAX_ERROR_COUNT)
             {
 
                 GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip4", pos:2);
                 requestVerifiCodeCd = REQUEST_VERIFICODE_MAX_ERROR_TIME;
             }
             else
             {
                 GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip3", pos:2);
             }
             return;
         }
        
         if (isLogining)
         {
             return;
         }

         if (ui.tp.selectedIndex == 1)
         {
             ui.tp.selectedIndex = 0;
         }

         GameEntry.LoginC.DoUILoginStart("");
     }
}

﻿using System;
using UnityEngine;
using Random = UnityEngine.Random;

namespace AnimationSystem
{
    // 播放所选动画 状态类。
    public class StarX5PlayAnimationState : AnimState
    {
        public float animationTimer = 0f;
        public float currClipLength = 0f;
        public bool isPlaying = false;
        public Action<AnimCallBack> cb = null;
        
        public override void OnEnter()
        {
            //后续将使用talkingIdle.
            var defaultIdle = Util.LoadAnimGenericTags("defaultIdle");
            if (defaultIdle != null && defaultIdle.Count > 0)
            {
                var talkingIdle = Util.LoadAnimGroup(defaultIdle[0], manager.animTypeId);
                if (talkingIdle != null) this.defaultAnimGroup = talkingIdle;
            }
            
            PlayDefaultAnimation();

            //启动眨眼
            manager.SetAutoBlinkEnabled(true);
            
            //启动眼球
            manager.SetEyeControlEnabled(true);
            
            manager.SetHeadCustomRotation(Vector3.zero,0.7f);
        }

        public override void OnUpdate()
        {
            if (isPlaying)
            {
                animationTimer += Time.deltaTime;
            }

            if (animationTimer > currClipLength)
            {
                isPlaying = false;
                animationTimer = 0f;
                currClipLength = 0f;
                if (this.cb != null)
                {
                    this.cb.Invoke(AnimCallBack.Ended);
                    this.cb = null;
                }
                this.PlayDefaultAnimation();
            }
        }

        public enum AnimCallBack
        {
            None = 0,
            Interrupted = 1,
            Ended = 2,
        }

        /// <summary>
        /// A.基于提供动画名称，结合动画类型播放动画。[1次]
        /// </summary>
        public void PlayAnimationByName(string animGroupName,Action<AnimCallBack> callback = null,float targetLength = 0)
        {
            var animation = Util.LoadAnimGroup(animGroupName, manager.animTypeId);
            if (animation != null)
            {
                PlayAnimation(animation,targetLength,callback);
            }
            else
            {
                VFDebug.LogError("资源目录>所述动画不存在!");
            }
        }
        
        /// <summary>
        /// B.基于提供动画配表信息，结合动画类播放动画。[1次]
        /// </summary>
        public void PlayAnimationByExcel(string animGroupName,Action<AnimCallBack> callback = null,float targetLength = 0)
        {
            var animations = Util.LoadAnimGenericTags(animGroupName);
            if (animations != null && animations.Count > 0)
            {
                var random = Random.Range(0, animations.Count);
                
                var animation = Util.LoadAnimGroup(animations[random], manager.animTypeId);
                PlayAnimation(animation,targetLength,callback);
            }
            else
            {
                VFDebug.LogError("配表>所述动画不存在!");
            }
        }
        
        private void PlayAnimation(AnimGroup animGroup,float targetLength,Action<AnimCallBack> callback = null)
        {
            if (this.cb != null)
            {
                this.cb.Invoke(AnimCallBack.Interrupted);
                this.cb = null;
            }
            this.cb = callback;
            float naturalDuration = 0f;
            if (targetLength == 0f)
            {
                if (animGroup.GetAnimInfos() == null)
                {
                    Debug.LogError($"{animGroup.bodyGroupID}无此动画!");
                    return;
                }
                foreach (var animInfo in animGroup.GetAnimInfos())
                {
                    naturalDuration += animInfo.clip.length;
                }
            }
            else
            {
                naturalDuration = targetLength;
            }

            AnimGroupPlayData animGroupPlayData = animGroup.GetAnimStrategy(naturalDuration);
            
            this.manager.PlayAnimGroupData(animGroupPlayData,false);
            this.animationTimer = 0f;
            this.currClipLength = Math.Max(naturalDuration,0.5f);
            isPlaying = true;
        }

        public override void OnExit()
        {
            VFDebug.Log("DialogEnterState: 退出对话开始状态");
            base.OnExit();
        }
        
    }
}
﻿using System;
using System.Collections.Generic;
using CommonUI;
using FairyGUI;
using Modules.DataDot;
using Msg.basic;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using UIBind.common;
using UIBind.Settlement;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Settlement.SettlementUI
{
    public class SettlementCommonUI : BaseUI<SettlementCommonPanel>
    {
        public SettlementCommonUI(string name) : base(name)
        {
        }

        private SettlementModel SettlementMod => GetModel<SettlementModel>(ModelConsts.Settlement);
        private SettlementController SettlementCtrl => GetController<SettlementController>(ModelConsts.Settlement);

        public override string uiLayer => UILayerConsts.Top;
        
        private GComponent _showNewWordComp;
        private NewWordComponent _curNewWordComp;
        private const string ClientIdPrefix = "SettlementCommonUI_";
        private readonly Color _blackColor = new(17 / 255f, 17 / 255f, 17 / 255f, 1);

        private List<ScoreTipCom> _tipList = new List<ScoreTipCom>();
        private string _tipTimeFlag = String.Empty;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            ui.CompSettle.list.itemRenderer = OnRendererPoint;
            
            AddUIEvent(ui.CompBottom.btnStart.onClick, OnClickContinue);
            ui.CompSettle.compSettleList.DoInit();
            ui.CompSettle.tfCongra.SetKey("freeTalk_Reward");
            ui.CompSettle.tfLanguage.SetKey("drill_hub_point");
            ui.CompBottom.tfReward.SetKey("common_continue");
        }

        private Dictionary<GComponent, int> _tempTrans = new();
        private Dictionary<int, float> _tempWidth = new();
        private List<CompSettleList.SettleListStruct> _settleStrList = new();
        
        protected override void OnShow()
        {
            base.OnShow();
            _isPlayingScoreEffect = false;
            
            SoundManger.instance.PlayUI("settlement");
            DataDotAppear_Dialogue_Result dot = new DataDotAppear_Dialogue_Result();
            dot.Dialogue_id = GetModel<ChatModel>(ModelConsts.Chat).dialogId;
            dot.Dialogue_type = SettlementMod.DialogMode;
            DataDotMgr.Collect(dot);

            //TODO:训练中心上了就有知识点了 这个再删掉
            ui.CompSettle.grpPoint.visible = SettlementMod.DialogMode != PB_DialogMode.Challenge && SettlementMod.DialogMode != PB_DialogMode.WarmupPractice;
            
            _tempTrans.Clear();
            _tempWidth.Clear();
            RefreshSettle();

            VFDebug.Log($"SettlementCommonUI task_success：{SettlementMod.SettlementData.task_success} ");
            if (SettlementMod.SettlementData.task_success)
            {
                ui.CompSettle.tfCongra.SetKey("freeTalk_Reward");
            }
            else
            {
                ui.CompSettle.tfCongra.SetKey("taskReward_completed");
            }
        }

        protected override void OnHide()
        {
            base.OnHide();
            if(_tipTimeFlag != String.Empty)
              TimerManager.instance.UnRegisterTimer(_tipTimeFlag);
            _tipTimeFlag = String.Empty;
        }

        protected override bool isFullScreen => true;

        private void RefreshSettle()
        {
            PB_DialogSettlementData settlementData = SettlementMod.SettlementData;
            
            _settleStrList.Clear();
            _settleStrList.Add(new CompSettleList.SettleListStruct()
            {
                IconType = CompSettleList.SettleIconType.Time,
                Number = int.Parse(settlementData.task_cost_time),
            });
            _settleStrList.Add(new CompSettleList.SettleListStruct()
            {
                IconType = CompSettleList.SettleIconType.Exp,
                Number = settlementData.experience,
            });
            // _settleStrList.Add(new CompSettleList.SettleListStruct()
            // {
            //     IconType = CompSettleList.SettleIconType.Words,
            //     Number = int.Parse(settlementData.word_num),
            // });
            ui.CompSettle.compSettleList.DoShowSettleList(_settleStrList);
            _tipList.Clear();
            ui.CompSettle.list.numItems = settlementData.knowledge_point.Count;
            ui.CompSettle.list.ResizeToFit();
            ui.CompSettle.grpPoint.EnsureBoundsCorrect();
            
            _tipTimeFlag = TimerManager.instance.RegisterTimer((a) =>
            {
                for (int i = 0; i < _tipList.Count; i++)
                {
                    _tipList[i].spineStar.visible = false;
                    _tipList[i].com.visible = false;
                }
            }, 2000, 1);
            
            if (settlementData.task_success)
            {
                ui.CompSettle.tfCongra.SetKey("freeTalk_Reward");
                // ui.CompSettle.tf_xp_num.text = $"+{settlementData.experience + GetOtherExp()}";
            }
            else
            {
                ui.CompSettle.tfCongra.SetKey("freeTalk_Reward");
            }
        }

        /// <summary>
        /// 命中关键点 的加分
        /// </summary>
        /// <returns></returns>
        private int GetOtherExp()
        {
            int exp = 0;
            for (int i = 0; i < SettlementMod.SettlementData.knowledge_point.Count; i++)
            {
                exp += SettlementMod.SettlementData.knowledge_point[i].additional_experience;
            }

            return exp;
        }

        private void OnClickContinue()
        {
            if (_isPlayingScoreEffect) return;
            SoundManger.instance.PlayUI("button_next");
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            DataDotClick_Dialogue_result_continue dot = new DataDotClick_Dialogue_result_continue();
            dot.Dialogue_id = GetModel<ChatModel>(ModelConsts.Chat).dialogId;
            dot.Dialogue_type = SettlementMod.DialogMode;
            DataDotMgr.Collect(dot);
            
            SettlementCtrl.ShowNextView( ()=> { Hide(); });
            
        }

        private void OnRendererPoint(int index, GObject obj)
        {
            GComponent comp = obj as GComponent;
            CompLanguageItem item = new CompLanguageItem();
            PB_KnowledgePoints knowledge = SettlementMod.SettlementData.knowledge_point[index];
            item.Construct(comp);
            item.tfTemp.text = knowledge.knowledge;
            _tempWidth[index] = item.tfTemp.width;

            VFDebug.Log($"task_success：{SettlementMod.SettlementData.task_success}   additional_experience：{knowledge.additional_experience}");
            if (knowledge.additional_experience > 0)
            {
                if (true)  //SettlementMod.SettlementData.task_success  成功失败都显示命中的加分
                {
                    ScoreTipCom tipCom = new ScoreTipCom();
                    tipCom.Construct(UIPackage.GetByName("common").CreateObject("ScoreTipCom").asCom);
                    tipCom.txtScore.text = "+" + knowledge.additional_experience.ToString();
                    item.com.AddChild(tipCom.com);
                    tipCom.com.position = item.tipPosFlag.position;
                    _tipList.Add(tipCom);

                    TimerManager.instance.RegisterNextFrame((a) => {
                        Vector2 globalPos = tipCom.com.LocalToGlobal(new Vector2(tipCom.com.x, tipCom.com.y));
                        tipCom.spineStar.visible = true;
                        tipCom.spineStar.spineAnimation.AnimationState.SetAnimation(0, "1", false).Complete += _ => {
                            ui.CompSettle.com.AddChild(tipCom.com);
                            Vector2 newLocalPos = ui.CompSettle.com.GlobalToLocal(globalPos);
                            tipCom.com.SetXY(newLocalPos.x, newLocalPos.y);

                            tipCom.com.TweenMove(ui.CompSettle.effectPosFlag.xy, _scoreEffectTime/1000f).OnComplete(() => {
                                tipCom.spineStar.visible = false;
                            });

                            PlayScoreEffect();
                        };
                    });
                }
                item.ctrlBack.selectedPage = "Green";
            }
            else
            {
                item.ctrlBack.selectedPage = "Normal";
            }

            
            if (item.tfPoint is TextFieldExtension tfPoint)
            {
                tfPoint.Reset();
                var contents = RichContentUtil.SplitWords(knowledge.knowledge);
                foreach (var content in contents)
                    tfPoint.AppendContent(content);
             
                tfPoint.SetNewFont(FontCfg.DinNextMedium,_blackColor, 32);

                tfPoint.Display(ShowMode.QuestionNormal);
                tfPoint.AddDot(contents);
                
                _tempTrans[tfPoint] = index;
                AddUIEvent(item.tfPoint.onClick, ClickRequestTrans);
            }
            if (item.tfExplain is TextFieldExtension tfExplain)
            {
                tfExplain.Reset();
                var contents = RichContentUtil.SplitWords(knowledge.explanation);
                foreach (var content in contents)
                    tfExplain.AppendContent(content);
              
                tfExplain.SetNewFont(FontCfg.DinNextRegular,_blackColor, 32);
                tfExplain.OnClickNewWord += RequestNewWordTrans;
                tfExplain.Display(ShowMode.Normal);
            }
        }

        private bool _isPlayingScoreEffect = false;
        private int _scoreEffectTime = 1000;
        private void PlayScoreEffect()
        {
            if (_isPlayingScoreEffect) return;
            _isPlayingScoreEffect = true;

            TimerManager.instance.RegisterTimer((e) =>
            {
                ui.CompSettle.compSettleList.UpdateItemNum(new[]
                    { SettlementMod.SettlementData.experience + GetOtherExp(), -1, -1 });
            }, _scoreEffectTime);
        }


        private void ClickRequestTrans(EventContext context)
        {
            if (context.sender is not GComponent comp) return;
            TextFieldExtension ext = comp as TextFieldExtension;
            if (null == ext) return;
            if (_tempTrans.TryGetValue(ext, out int index))
            {
                PB_KnowledgePoints knowledge = SettlementMod.SettlementData.knowledge_point[index];
                NewWordComponent newComp = UIPackage.CreateObject("common", "NewWordCom") as NewWordComponent;
                if(null == newComp) return;
                newComp.parentTfExt = ext;
                newComp.word = knowledge.knowledge;
            
                _curNewWordComp = newComp;
                SettlementCtrl.NewWordResp += RespNewWordTrans;

                _tempWidth.TryGetValue(index, out float tempWidth);
                var wordPos = ext.LocalToGlobal(new Vector2(tempWidth / 2f, 0));
                var msg = new CS_ClickWordReq
                {
                    text = knowledge.knowledge,
                    client_id = ClientIdPrefix,
                };
                AddChildCom(newComp);
                newComp.xy = newComp.GlobalToLocal(wordPos);
                MsgManager.instance.SendMsg(msg, NewWordTransFail);
            }
        }
        
        private void RequestNewWordTrans(NewWordComponent comp,Vector2 wordPos)
        {
            _curNewWordComp = comp;
            SettlementCtrl.NewWordResp += RespNewWordTrans;
         
            var msg = new CS_ClickWordReq
            {
                text = comp.word,
                client_id = ClientIdPrefix,
            };
            AddChildCom(comp);
            comp.xy = comp.GlobalToLocal(wordPos);
            MsgManager.instance.SendMsg(msg, NewWordTransFail);
        }
        
        private void RespNewWordTrans(SC_ClickWordAck msg)
        {
            if (msg.data.client_id != ClientIdPrefix)
                return;
            if (msg.code != 0)
            {
                Debug.Log("SC_ClickWordAck is error");
                NewWordTransFail(GRPCManager.ErrorType.None,null);
                return;
            }
            
            if (_curNewWordComp != null && !_curNewWordComp.isDisposed)
            {
                if (_curNewWordComp.word.ToLower() == msg.data.word.ToLower())
                {
                    TTSManager.instance.PlayTTS(msg.data.audio_id);
                }
                _curNewWordComp.SetWord(msg.data.translation,3F,true);
                _curNewWordComp.parentTfExt.AppendNewWord(msg.data.word, msg.data.count);
                _curNewWordComp.parentTfExt.CreateNewWord();
            }
        }
        
        //生词翻译失败
        private void NewWordTransFail(GRPCManager.ErrorType et,Google.Protobuf.IMessage msg)
        {
            if (_curNewWordComp != null && !_curNewWordComp.isDisposed)
                _curNewWordComp.Dispose();
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("translate-error-toast");
        }

        private void AddChildCom(GComponent cmp)
        {
            if (_showNewWordComp != null)
            {
                _showNewWordComp.Dispose();
                _showNewWordComp = null;
            }
            _showNewWordComp = cmp;
            ui.com.AddChild(cmp);
        }
    }
}
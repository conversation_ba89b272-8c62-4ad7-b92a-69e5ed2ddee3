﻿using System;
using System.Collections.Generic;
using Google.Protobuf.Collections;
using Msg;
using Msg.basic;
using Msg.dialog_task;
using ScriptsHot.Game.Modules.ChatLogicNew.ChatState;
using ScriptsHot.Game.Modules.ChatLogicNew.ChatType;
using ScriptsHot.Game.Modules.ChatLogicNew.ChatType.ChatAUA;
using ScriptsHot.Game.Modules.ChatLogicNew.ChatType.ChatVideoTask;
using ScriptsHot.Game.Modules.ChatLogicNew.UI.ChatAUA;
using ScriptsHot.Game.Modules.ChatLogicNew.UI.ChatEmpty;
using ScriptsHot.Game.Modules.ChatLogicNew.UI.ChatStory;
using ScriptsHot.Game.Modules.Record;
using ScriptsHot.Game.Modules.Settlement;
using UIBind.ChatStart;
using UnityEngine;

namespace ScriptsHot.Game.Modules.ChatLogicNew
{

    /// <summary>
    /// 所有聊天类型公共的逻辑写这里
    /// </summary>
    public partial class ChatLogicController:BaseController
    {
        public static byte[] test = null;
        
        private ChatTypeBase _curChat = null;
        public ChatTypeBase CurChat => _curChat;

        private long _curAvatarId = 0;
        public long CurAvatarId => _curAvatarId;
        
        private Dictionary<ChatCellType, string> _chatCellResDic = new();

        private ChatLogicModel _model;
        public ChatLogicModel Model => _model;
        
        // public LearnPathModel LearnPathModel => GetModel<LearnPathModel>(ModelConsts.LearnPath);

        private PB_DialogMode _curDialogMode;
        public PB_DialogMode CurDialogMode => _curDialogMode;
        public PB_CourseLearnPathParams CourseParams { get; private set; }
        
        //测试数据 之后删除
        public static List<Int64> TestAvatarChat = new List<Int64>()
               {16994958287469380, 
                17162685095175687, 
                2982162755590144000,
                17162663944164736,
                17162670537025719,
                17162675486697525,
                17013370304127552,
                17031552949694799,
                17013385164029492};

        public ChatLogicController() : base(ModelConsts.ChatLogic)
        {
        }
        
        public override void OnInit()
        {
            _model = new ChatLogicModel();  
            this.RegisterModel(_model);
            
            this.RegisterUI(new ChatAUAUI(UIConsts.ChatAUA));
            this.RegisterUI(new ChatStoryPanelUI(UIConsts.ChatStory));
            this.RegisterUI(new ChatHomePageUI(UIConsts.ChatHomePage));
            this.RegisterUI(new ChatTopicPageUI(UIConsts.ChatTopicUI));
            this.RegisterUI(new TwoWayRecordUI(UIConsts.TwoWayRecordUI));
            this.RegisterUI(new ChatStoryOutUI(UIConsts.ChatStoryOutUI));
            this.RegisterUI(new ChatImagePreviewUI(UIConsts.ChatImagePreviewUI));
            this.RegisterUI(new ChatEmptyUI(UIConsts.ChatEmptyUI));
            AddEvent();
            BindChatCellComponent();
        }

        public override void OnUpdate(int interval)
        {
            base.OnUpdate(interval);
            _curChat?.Update(interval);
        }

        private void AddEvent()
        {
            RemoveEvent();
            Notifier.instance.RegisterNotification(NotifyConsts.ChatNewEnter,EnterDialogue);
            Notifier.instance.RegisterNotification(NotifyConsts.ChatNewExit,ExitDialogue);
            
            Notifier.instance.RegisterNotification(NotifyConsts.ExitChatNew,DoExitChat);
            Notifier.instance.RegisterNotification(NotifyConsts.ShowRecordUI,ShowRecordUI);
            Notifier.instance.RegisterNotification(NotifyConsts.SetScaffoldShowState,OnSetScaffoldShow);
            Notifier.instance.RegisterNotification(NotifyConsts.StopStreamAudioTTs,OnStopStreamAudioTTs);
            Notifier.instance.RegisterNotification(NotifyConsts.SetScaffoldCanShow,OnSetScaffoldCanShow);
            Notifier.instance.RegisterNotification(NotifyConsts.NewWorldAudioStart,OnNewWorldAudioStart);
            Notifier.instance.RegisterNotification(NotifyConsts.EndChatAndEnterProgress,OnEndRecordEvent);
            
            
            // Notifier.instance.RegisterNotification(NotifyConsts.SendGetDialogScaffold,SendGetDialogScaffold);

            //10009  任务对话回复
            MsgManager.instance.RegisterCallBack<SC_DialogTaskMsgHandleAck>(this.DialogTaskMsgHandleAck);
            //10009  任务对话回复
            MsgManager.instance.RegisterCallBack<SC_GetDialogTaskNextRoundMsgAck>(this.DialogTaskCacheMsgAck);
            //脚手架回复
            MsgManager.instance.RegisterCallBack<SC_GetDialogScaffoldAck>(GetDialogScaffoldAck);
            // 事后建议
            MsgManager.instance.RegisterCallBack<SC_DialogSuggestionAck>(OnSCDialogSuggestionAck);
            // 翻译
            MsgManager.instance.RegisterCallBack<SC_DialogTranslateAck>(OnSCDialogTranslateAck);
            
            AddStoryEvent();
            AddRolePlay2Event();
        }

       
        private void RemoveEvent()
        {
            Notifier.instance.UnRegisterNotification(NotifyConsts.ChatNewExit,ExitDialogue);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ChatNewEnter,EnterDialogue);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExitChatNew,DoExitChat);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ShowRecordUI,ShowRecordUI);
            Notifier.instance.UnRegisterNotification(NotifyConsts.SetScaffoldShowState,OnSetScaffoldShow);
            Notifier.instance.UnRegisterNotification(NotifyConsts.StopStreamAudioTTs,OnStopStreamAudioTTs);
            Notifier.instance.UnRegisterNotification(NotifyConsts.SetScaffoldCanShow,OnSetScaffoldCanShow);
            Notifier.instance.UnRegisterNotification(NotifyConsts.NewWorldAudioStart,OnNewWorldAudioStart);
            // Notifier.instance.UnRegisterNotification(NotifyConsts.SendGetDialogScaffold,SendGetDialogScaffold);
            Notifier.instance.UnRegisterNotification(NotifyConsts.EndChatAndEnterProgress,OnEndRecordEvent);
            
            //10009  任务对话回复
            MsgManager.instance.UnRegisterCallBack<SC_DialogTaskMsgHandleAck>(this.DialogTaskMsgHandleAck);
            //10009  任务对话回复
            MsgManager.instance.UnRegisterCallBack<SC_GetDialogTaskNextRoundMsgAck>(this.DialogTaskCacheMsgAck);
            //脚手架回复
            MsgManager.instance.UnRegisterCallBack<SC_GetDialogScaffoldAck>(GetDialogScaffoldAck);
            // 事后建议
            MsgManager.instance.UnRegisterCallBack<SC_DialogSuggestionAck>(OnSCDialogSuggestionAck);
            MsgManager.instance.UnRegisterCallBack<SC_DialogTranslateAck>(OnSCDialogTranslateAck);

            
            RemoveStoryEvent();
            RemoveRolePlay2Event();
        }

        private void OnStopStreamAudioTTs(string s, object body)
        {
            GSoundManager.instance.StopAvatarTTS();
            // ProcedureManager.instance.Clear();
            _curChat.CurNetStrategy?.StopTTS();
        }
        
        private void OnNewWorldAudioStart(string s, object body)
        {
            _curChat?.OnNewWorldAudioStart();
            GSoundManager.instance.StopAvatarTTS();
        }

        private void DoExitChat(string s, object body)
        {
            if (_curChat != null)
            {
                GMicrophoneManager.instance.EndRecord();
                _curDialogMode = PB_DialogMode.MNone;
                ClearShowFinishTime();
                _curChat.Exit();
                _curChat.CurNetStrategy?.Stop();
                _curChat.ChangeState(ChatStateName.Exit);
            }

            _curChat = null;
        }

        private void EnterDialogue(string s, object body)
        {
            if(_curChat != null) _curChat.Dispose();

            _model.Reset();
            IsAddScaffold = false;
            ScaffoldCanShow = false;
            SceneStateChatParam param = (SceneStateChatParam)body;
            _curAvatarId = param.avatarId;
            _curDialogMode = param.chatMode;
            CourseParams = param.mainPathParams;
            _curChat = CreateDialogue(param.chatMode);
            if (_curChat == null) return;
            _curChat.Init(param);
            _curChat.Start();
        }
        
        private void ExitDialogue(string s, object body)
        {
            _curDialogMode = PB_DialogMode.MNone;
            ClearShowFinishTime();
            if (_curChat == null) return;
            _curChat.Exit();
        }

        private ChatTypeBase CreateDialogue(PB_DialogMode type) {
            switch(type) {
                case PB_DialogMode.WorldStory:    
                    return new ChatStory();
                case PB_DialogMode.Career: 
                    return new ChatFree();
                case PB_DialogMode.RolePlay:
                    return new ChatRolePlay2();
                case PB_DialogMode.Tutor:
                    return new ChatTutor();
                case PB_DialogMode.Flash:
                    return new ChatAUA();
                case PB_DialogMode.Video:
                    return new ChatVideoTask();
                default:
                    return null;
            }
        }

        //任务回复
        public void DialogTaskMsgHandleAck(SC_DialogTaskMsgHandleAck msg)
        {
            Debug.Log("收到 ：MsgHandleAck");
            if (!IsNewDialogMode(_curDialogMode))
            {
                return;
            }
            //chatgpt封控
            if (msg.code == PB_Code.ChatGptRiskControl)
            {
                if (msg.data != null && msg.data.msg_items.Count >= 1)
                {
                    if (msg.data.msg_items[0].dialog_id == _curChat.DialogTaskAck.data.dialog_id &&
                        msg.data.msg_items[0].round_id == _model.CurRoundId)
                    {
                        bool isOk = _curChat.ChatGptLock(msg.data.msg_items[0].bubble_id);

                        RoundData roundData = Model.GetRoundDataByID(_model.CurRoundId);
                        
                        DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
                        dataDotCutApiCase.Dialogue_id = _curChat.DialogTaskAck.data.dialog_id;
                        dataDotCutApiCase.Task_id = _curChat.CurChatInfo.taskId;
                        dataDotCutApiCase.Dialogue_step = roundData.StepID;
                        dataDotCutApiCase.Task_mode = (int)_curChat.CurChatInfo.chatMode;
                        dataDotCutApiCase.Api_address = "SC_DialogTaskMsgHandleAck";
                        dataDotCutApiCase.Cut_mode = 3;
                        DataDotMgr.Collect(dataDotCutApiCase);
                        
                        if(isOk)
                            return;
                    }
                }
            }

            if (msg.code != PB_Code.Normal || msg.data == null || (msg.data.msg_items.Count == 0 && msg.data.ext_items.Count ==0 && _curChat.CurChatInfo.chatMode != PB_DialogMode.Tutor))
            {
                DealErrorData(msg.code.ToString());
                return;
            }

            if (_model.GetDialogTaskMsgHandleDealState(_model.CurRoundId))
            {
                VFDebug.LogWarning("当前轮次已走过MsgHandle 此条不处理");
                return;
            }

            _model.SetDialogTaskMsgHandleDeal(_model.CurRoundId);//表示当前轮次已处理过
            short msgId = MsgMap.msgT2ID[typeof(SC_DialogTaskMsgHandleAck)];
            DealDialogTaskData(msg.data, msgId);
        }

        //获取下一轮任务对话消息回复
        private void DialogTaskCacheMsgAck(SC_GetDialogTaskNextRoundMsgAck msg)
        {
            if (!IsNewDialogMode(_curDialogMode))
            {
                return;
            }

            if (msg.code != PB_Code.Normal|| (msg.data.msg_items.Count == 0 && msg.data.ext_items.Count ==0))
            {
                DealErrorData(msg.code.ToString());
                return;
            }

            if (_model.GetNextRoundMsgAckDealState(_model.CurRoundId))
            {
                VFDebug.LogWarning("当前轮次已走过NextRound 此条不处理");
                return;
            }

            _model.SetNextRoundMsgAckDeal(_model.CurRoundId);//表示当前轮次已处理过
            // for (int i = 0; i < msg.data.msg_items.Count; i++)
            // {
            //     PB_DialogTaskMsgItem item = msg.data.msg_items[i];
            //     if (item.msg_biz_type == PB_DownMsgBizType.FreetalkAdvice)
            //     {
            //         RoundData roundData = _model.GetRoundDataByID(item.round_id);
            //         roundData.AdviceAck = item.freetalk_advice_data;
            //     }
            // }
            short msgId = MsgMap.msgT2ID[typeof(SC_GetDialogTaskNextRoundMsgAck)];
            // float time = TimeExt.currTime - TestCreateDialogTime;
            // VFDebug.Log($"DialogueSlow: dialogid:{_chatModel.dialogId}, time:{time}, timestamp:{TimeExt.currTime}, type: 收到avatar信息, roundId:{_chatModel.curRoundId}, taskId:{curTaskId}");
            DealDialogTaskData(msg.data, msgId);
        }
        
        private void GetDialogScaffoldAck(SC_GetDialogScaffoldAck msg)
        {
            if (!IsNewDialogMode(_curDialogMode))
            {
                return;
            }
        
            if (msg.code != PB_Code.Normal)
            {
                VFDebug.LogError("SC_GetDialogScaffoldAck is error: "+msg.code);
                return;
            }

            if (_curChat.DialogTaskAck.data.dialog_id != 0 && msg.data.dialog_id != _curChat.DialogTaskAck.data.dialog_id)
            {
                VFDebug.LogError("SC_GetDialogScaffoldAck is error: "+msg.code);
                return;
            }
            if(msg.data.round_id != _model.CurRoundId)
                return;
        
            MsgData msgData = new MsgData();
            short msgId = MsgMap.msgT2ID[typeof(SC_GetDialogScaffoldAck)];
            msgData.msgId = msgId;
            msgData.data = msg;
            _curChat.OnHandleMsg(msgData);
        }
        
        private void OnSCDialogSuggestionAck(SC_DialogSuggestionAck msg)
        {
            if (!IsNewDialogMode(_curDialogMode))
            {
                return;
            }
            if (msg.code != PB_Code.Normal || msg.data == null)
            {
                VFDebug.LogError("获取建议报错: "+msg.code);
                return;
            }

            if (msg.data.dialog_mode == PB_DialogMode.Tutor)
            {
                BubbleData bubbleData = _model.GetBubbleDataByID(msg.data.tutor_suggestion_resp.bubble_id);
                bubbleData.SuggestionResp = msg.data.tutor_suggestion_resp;
            }
            else if (msg.data.dialog_mode == PB_DialogMode.Career)
            {
                BubbleData bubbleData = _model.GetBubbleDataByID(msg.data.career_suggestion_resp.bubble_id);
                bubbleData.SuggestionResp = msg.data.career_suggestion_resp;
            }
            else if (msg.data.dialog_mode == PB_DialogMode.Challenge || msg.data.dialog_mode == PB_DialogMode.RolePlay)
            {
                BubbleData bubbleData = _model.GetBubbleDataByID(msg.data.challenge_suggestion_resp.bubble_id);
                bubbleData.SuggestionResp = msg.data.challenge_suggestion_resp;
            }
            SendNotification(NotifyConsts.ChatSuggestionInfo);
            MsgData msgData = new MsgData();
            msgData.msgId = MsgMap.msgT2ID[typeof(SC_DialogSuggestionAck)];
            msgData.data = msg;
            _curChat.OnHandleMsg(msgData);
        }
        
        private void OnSCDialogTranslateAck(SC_DialogTranslateAck msg)
        {
            if (!IsNewDialogMode(_curDialogMode))
            {
                return;
            }

            if (msg.code != PB_Code.Normal || msg.data == null)
            {
                VFDebug.LogError("SC_DialogTranslateAck iserror: "+msg);
                return;
            }
        
            if (msg.data.dialog_id != _curChat.DialogTaskAck.data.dialog_id)
                return;
        
            Notifier.instance.SendNotification(NotifyConsts.ShowTranslateInfo,msg);
        }


        private bool IsNewDialogMode(PB_DialogMode mode)
        {
            //TODO:迁移完了可以删掉了
            if (mode == PB_DialogMode.RolePlay || mode == PB_DialogMode.WorldStory || mode == PB_DialogMode.Tutor ||
                mode == PB_DialogMode.Career || mode == PB_DialogMode.Flash)
            {
                return true;
            }

            return false;
        }
        
        public void DealErrorData(string address, string errcode = "")
        {
            RoundData roundData = Model.GetRoundDataByID(_model.CurRoundId);
            
            DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
            dataDotCutApiCase.Dialogue_id = _curChat.DialogTaskAck.data.dialog_id;
            dataDotCutApiCase.Task_id = _curChat.CurChatInfo.taskId;
            dataDotCutApiCase.Dialogue_step = roundData.StepID;
            dataDotCutApiCase.Task_mode = (int)_curChat.CurChatInfo.chatMode;
            dataDotCutApiCase.Api_address = address;
            dataDotCutApiCase.Cut_mode = 1;
            DataDotMgr.Collect(dataDotCutApiCase);
            Debug.LogError("server error "+address);
            //点击返回出弹窗
            this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open(I18N.inst.MoStr("common_chat_error") + "\n" + address, () =>
            {
                Notifier.instance.SendNotification(NotifyConsts.ExitChatNew);
            }, null, 1, null, "chat_btn_cancel");
        }
        
        //处理服务器对话数据
        private void DealDialogTaskData(PB_DialogTaskMsgResp msg, short msgId)
        {
            if (msg == null)
            {
                Debug.LogError("DealDialogTaskData data is null");
                DealErrorData("DealDialogTaskData data is null");
                return;
            }

            if (_curChat.GetMode() == PB_DialogMode.RolePlay && msg.msg_items.Count > 0)
            {
                ResetAvatarAndNarration();
            }
            else if (_curChat.GetMode() == PB_DialogMode.Tutor && msg.msg_items.Count > 0)
            {
                ResetTutorAvatar();
            }

            for (int i = 0; i < msg.msg_items.Count; i++)
            {
                PB_DialogTaskMsgItem item = msg.msg_items[i];
                if (DealPastMsgData(item.round_id, item.dialog_id))
                {
                    MsgData msgData = new MsgData();
                    msgData.msgId = msgId;
                    msgData.data = item;
                    DealWaitsSequenceLogic(item.sequence_id, msgData);
                }
            }
            
            if (_curChat.GetMode() == PB_DialogMode.RolePlay && msg.msg_items.Count > 0)
            {
                ShowAvatarAndNarration();
            }
            else if (_curChat.GetMode() == PB_DialogMode.Tutor && msg.msg_items.Count > 0)
            {
                ShowTutorAvatar();
            }

            for (int i = 0; i < msg.ext_items.Count; i++)
            {
                PB_DialogTaskExtItem item = msg.ext_items[i];
                if (DealPastMsgData(item.round_id, item.dialog_id))
                {
                    MsgData msgData = new MsgData();
                    msgData.msgId = msgId;
                    msgData.data = item;
                    int bubble_id = GetExtBubbleId(msg.msg_items,item.relate_index);
                    _curChat.OnHandleMsg(msgData,bubble_id);
                }
            }

            for (int i = 0; i < msg.next_items.Count; i++)
            {
                PB_DialogTaskNextItem item = msg.next_items[i];
                if (DealPastMsgData(item.round_id, item.dialog_id))
                {
                    MsgData msgData = new MsgData();
                    msgData.msgId = msgId;
                    msgData.data = item;
                    _curChat.OnHandleMsg(msgData);
                }
            }
        }

        /// <summary>
        /// 获取扩展数据的 BubbleId
        /// </summary>
        /// <param name="msgList"></param>
        /// <param name="relate_index"></param>
        /// <returns></returns>
        private int GetExtBubbleId(RepeatedField<PB_DialogTaskMsgItem> msgList,long relate_index)
        {
            foreach (var item in msgList)
            {
                if (item.relate_index == relate_index)
                    return item.bubble_id;
            }
            return 0;
        }

        private bool DealPastMsgData(int roundId, long dialogId)
        {
            //TODO:应该再完善一下这部分
            if (_curDialogMode == PB_DialogMode.Flash)
                return true;
            
            //判断roundId 是否是之前的 并且dialogId需要一致 
            if (_model.CurRoundId != roundId || _curChat.DialogTaskAck.data.dialog_id != dialogId)
            {
                Debug.Log(" DealPastMsgData data is error:cur roundId " + _model.CurRoundId +" msg roundid "+roundId+ " cur dialogId " +
                          _curChat.DialogTaskAck.data.dialog_id+" msg  dialogId "+dialogId);
                if(_model.CurRoundId != 0)
                    DealErrorData("_chatModel.curRoundId is error");
                return false;
            }
            else
                return true;
        }
    
        private int _waitSequeneId = 1;
        private int _endSequeneId = 1;
        private Dictionary<int, MsgData> _waitDialogTaskMsgRespToSeqId = new Dictionary<int, MsgData>();
        //处理死等逻辑
        private void DealWaitsSequenceLogic(int msgSequenceId, MsgData msgData)
        {
            if (_curChat == null)
            {
                VFDebug.LogError("聊天模式 null  请检查");
                return;
            }

            _curChat.OnHandleMsg(msgData);
            return;  //下面逻辑 不清楚作用
            if (msgSequenceId < _waitSequeneId)
                Debug.LogError($"SequenceId is error: msgSequenceId:{msgSequenceId}  _waitSequeneId:{_waitSequeneId}");
            _waitDialogTaskMsgRespToSeqId[msgSequenceId] = msgData;
            int length = _waitDialogTaskMsgRespToSeqId.Count;
            for (int i = 0; i < length; i++)
            {
                if (_waitDialogTaskMsgRespToSeqId.ContainsKey(_waitSequeneId))
                {
                    bool isAccept = _curChat.OnHandleMsg(_waitDialogTaskMsgRespToSeqId[_waitSequeneId]);
                    // if (!isAccept)
                    //     _handleMsgDatas.Add(msgData);
                    _waitDialogTaskMsgRespToSeqId.Remove(_waitSequeneId);
                    _waitSequeneId++;
                }
                else
                    break;
            }
        }
        
        // 结算界面点击返回 退出聊天
        public void SettlementExitChat(SettlementClickType param = SettlementClickType.ExitChat)
        {
            _curChat.ChangeState(ChatStateName.Exit, param);
            // Notifier.instance.SendNotification(NotifyConsts.DoChatBack);
            GetController<SettlementController>(ModelConsts.Settlement).ClearData();
            Notifier.instance.SendNotification(NotifyConsts.LearnPathReShowEvent);
            GetModel<MainModel>(ModelConsts.Main).SetChatEnterPos();
            Notifier.instance.SendNotification(NotifyConsts.ShowGrowthExp);
      
        }

        /// <summary>
        /// 结算点继续
        /// </summary>
        /// <param name="param"></param>
        public void SettlementExitAndContinueChat(SettlementClickType param = SettlementClickType.ExitChat)
        {
            _curChat.ChangeState(ChatStateName.Exit, param);
            GetController<SettlementController>(ModelConsts.Settlement).ClearData();
            Notifier.instance.SendNotification(NotifyConsts.LearnPathReShowEvent);
        }

        public override void OnNetDisConn()
        {
            base.OnNetDisConn();
            if (_curChat != null)
            {
                //其他模式不出现弹窗
                if(_curChat.CurChatInfo.chatMode == PB_DialogMode.WorldStory)
                     Notifier.instance.SendNotification(NotifyConsts.DoTopLeftBack,true);
            }
        }

        public override void OnNetReConn()
        {
            base.OnNetReConn();
            if (_curChat != null)
            {
                DoExitChat(string.Empty,null);
                Notifier.instance.SendNotification(NotifyConsts.DoTopLeftBack,true);
            }
        }

        public override void OnApplicationQuit()
        {
            base.OnApplicationQuit();
            RemoveEvent();
        }
        
        private void BindChatCellComponent()
        {
            // avatar普通气泡 + mask功能
            _chatCellResDic.Add(ChatCellType.AvatarNormal, "AvatarNormalCellCom");
            // avatar等待spine
            _chatCellResDic.Add(ChatCellType.PreTalk, "PreTalkCellCom");
            //玩家自己
            _chatCellResDic.Add(ChatCellType.PlayerNormal, "PlayerNormalCellCom");
            // player跟读气泡 + 状态
            _chatCellResDic.Add(ChatCellType.PlayerFollow, "PlayerFollowCellCom");
            // toast气泡
            _chatCellResDic.Add(ChatCellType.Toast, "ToastCellCom");
            // 脚手架
            _chatCellResDic.Add(ChatCellType.Scaffold, "ScaffoldCellCom");
            // suggestion气泡
            _chatCellResDic.Add(ChatCellType.Suggest, "SuggestCellCom");
            // Advice
            _chatCellResDic.Add(ChatCellType.Advice, "AdviceCellCom");
            // empty气泡
            _chatCellResDic.Add(ChatCellType.Empty, "EmptyCellCom");
            //姓名和职业
            _chatCellResDic.Add(ChatCellType.NameAndProfession, "NameAndProfessionCell");
            //旁白
            _chatCellResDic.Add(ChatCellType.Narration, "NarrationCellCom");
            //对话场景描述
            _chatCellResDic.Add(ChatCellType.ChatSceneDesc, "ChatSceneDescCom");
            //Image
            _chatCellResDic.Add(ChatCellType.Image, "ChatImageCellCom");
        }
        
        public string GetChatCellResName(ChatCellType type)
        {
            return _chatCellResDic[type];
        }

        #region bubbleId 生成

        private int _bubbleId = 0;
        public int GetBubbleId()
        {
            _bubbleId++;
            Debug.Log("GetBubbleId::" + _bubbleId);
            return _bubbleId;
        }

        public void ResetBubbleId()
        {
            _bubbleId = 0;
        }

        #endregion
    }

    public class ChatAudioInfo
    {
        public long tts_record_id = 0;
        /// <summary>
        /// 是否通过缓存播放
        /// </summary>
        public bool cache = false;

        /// <summary>
        /// ChatCellAudioType 
        /// </summary>
        public string audioFram;
    }
}














  
﻿using System;
using System.Collections.Generic;
using CommonUI;
using FairyGUI;
using Lean.Touch;
using LittleMap;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.Consts;
using ScriptsHot.Game.Modules.Profile;

using UnityEngine;
using UnityEngine.AI;
using UnityEngine.EventSystems;
using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.Scene.Level.Component
{
    public class LeanTouchComponent:ComponentBase
    {

        /// <summary>
        /// 最小触发跑动距离
        /// </summary>
        private const float _runMinDistance = 0.7f;
        public  PathFindEnum pathFindEnum;
        
        private Level _scene;
        private NotifyInfo _notifyInfo;
        public LeanTouchComponent(IComponetOwner owner):base(owner)
        {
            this._scene = owner as Level;
        }

        public Level scene
        {
            get { return _scene; }
        }

        public override void OnInit()
        {
            _notifyInfo = new NotifyInfo();
        }

        public override void OnDispose()
        {
        }

        public override void Refresh()
        {
        }

        public override void Update(int interval)
        {
        }

        public override void EnterScene()
        {
        }

        public override void ExitScene()
        {
        }

        public override void Clear()
        {
        }
        
        public void OnLeanTouchGestureDown()
        {
        }
        
        public void OnLeanTouchGestureUp(Dictionary<float, GestureParam> gestureParams = null)
        {
        }
        
        public void OnLeanTouchGesturePress(List<LeanFinger> fingers)
        {
        }
        
        public void OnLeanTouchFingerTap(LeanFinger finger)
        {
            
            if (LittleMapPublic.BeClickLittleMapUI(finger.ScreenPosition))
            {
                Debug.Log("Click UGUI  LittleMap");
                return;
            }

            if (EventSystem.current.IsPointerOverGameObject())
            {
                Debug.Log("Click UGUI ");
                return;
            }
      
            //TODO
            if (Stage.isTouchOnUI) return;
   
            List<LeanFinger> leanFingers = LeanTouch.GetFingers(true, true);
            if (leanFingers.Count == 0) return;
            //
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
            //获取所有碰撞到的物体
            Ray ray = finger.GetStartRay();
            RaycastHit raycastHit = default;
            RaycastHit[] raycastHits = Physics.RaycastAll(ray, Mathf.Infinity);

            Array.Sort(raycastHits, (x, y) => x.distance.CompareTo(y.distance));

            foreach (RaycastHit _raycastHit in raycastHits)
            {
                //剔除主角
                if (_raycastHit.collider != null)
                {
                    var hitMainCtrl = _raycastHit.collider.GetComponent<GAvatarCtrl>();
                    Unit hitMainUnit = null;
                    if (hitMainCtrl != null)
                    {
                        hitMainUnit = _scene.GetCharacter(hitMainCtrl.uid) as Unit;
                        if (hitMainUnit != null && hitMainUnit.avatarType == ESceneAvatar.Role)
                        {
                            if (hitMainUnit.isMainUnit)
                            {
                                continue;
                            }
                        }
                    }
                }

                if (!_raycastHit.collider.gameObject.CompareTag(ESceneTag.BigGround))
                {
                    raycastHit = _raycastHit;
                    break;
                }
            }

            (_scene.GetComponent<EffectComponent>() as EffectComponent).StopUniqueEffect("select");

            if (raycastHit.collider == null)
            {
                GHeadBarManager.instance.HideCurShowingAvatarRecommendCmp();
                return;
            }

            //点击了非主角的其他玩家
            var hitAvatarCtrl = raycastHit.collider.GetComponent<GAvatarCtrl>();
            Unit hitUnit = null;
            if (hitAvatarCtrl != null)
            {
                hitUnit = _scene.GetCharacter(hitAvatarCtrl.uid) as Unit;
                if (hitUnit != null && hitUnit.avatarType == ESceneAvatar.Role)
                {
                    var socialCardCtrl = ControllerManager.instance.GetController(ModelConsts.SocialCard) as SocialCardController;
                    if (!hitUnit.isMainUnit)
                    {
                        Debug.Log("点到人了啊啊啊啊啊" + hitUnit.account_ID);
                        socialCardCtrl.OnClickOther(hitUnit.account_ID, hitUnit.name, hitUnit.roleUniqueName);
                        return;
                    }
                }
            }

            if (raycastHit.collider.CompareTag(ESceneTag.Ground) || raycastHit.collider.CompareTag(ESceneTag.Building))
            {
                GHeadBarManager.instance.HideCurShowingAvatarRecommendCmp();
                //关闭头顶说明提示  TODO
                GHeadBarManager.instance.HideStoryTipsBubble();
                GHeadBarManager.instance.HideStoryTitlePage();
            }

            //
            // if (NavMesh.SamplePosition(raycastHit.point, out var navMeshHit, 0.1f, NavMesh.AllAreas))
            if (CheckCanReach(raycastHit.point, out var navMeshHit))
            {
                // 点击的位置在烘焙过的区域上
                if (raycastHit.collider.CompareTag(ESceneTag.Ground))
                {
                    (_scene.GetComponent<EffectComponent>() as EffectComponent).PlayUniqueEffect("clickeff", navMeshHit.position, 1f, -1);
                    this.DoClickGround(navMeshHit.position);
                }
            }
            else
            {
                if (raycastHit.collider.CompareTag(ESceneTag.Building))
                {
                    GBuildingCtrl buildingCtrl = raycastHit.collider.gameObject.GetComponent<GBuildingCtrl>();
                    if (buildingCtrl != null)
                    {
                        string buildingName = raycastHit.collider.gameObject.name;
                        this.DoClickBuilding(buildingCtrl.uid, buildingCtrl.transform.position);
                        Debug.Log("Click Building");
                    }
                    else
                    {
                        
                        SceneController sceneController = ControllerManager.instance.GetController(ModelConsts.Scene) as SceneController;
                        sceneController.GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_build_tips", true);
                        return;
                         
                    }
                    
                }
                else if (hitUnit != null)
                {
                    this.DoClickAvatar(hitUnit);
                }
                else
                {
                    // 点击的位置不在烘焙过的区域上
                    (_scene.GetComponent<EffectComponent>() as EffectComponent).PlayEffect("slip", raycastHit.point, 1f, 0.433f);
                }
            }
        }
        
        public void OnLeanTouchFingerSwipe(LeanFinger finger)
        {
        }

        private void DoClickGround(Vector3 position)
        {
            // 唯一交互，TODO Event
            // 埋点
            LittleMapPublic.JudgeCutAutoRunCancelDot();
                
            pathFindEnum = PathFindEnum.Ground;
            //Notifier.instance.SendNotification(NotifyConsts.LittlePlayRunAni);
            (_scene.GetComponent<MainCtrlComponent>() as MainCtrlComponent).AutoRun( new Vector2(position.x, position.z),()=>
            {
                Notifier.instance.SendNotification(NotifyConsts.LittleStopRunAni);
                Notifier.instance.SendNotification(NotifyConsts.RefreshWorldPath);
                (_scene.GetComponent<EffectComponent>() as EffectComponent).StopUniqueEffect("clickeff");
            });
        }
        
        public void DoClickBuilding(int sceneId, Vector3 buildPosition)
        {
            SceneCfg cfg = Cfg.T.TbScene.GetOrDefault(sceneId);
            if (cfg == null) return;
            var mainUnit = _scene.GetComponent<UnitComponent>().mainUnit;
            if (mainUnit == null) return;

            if (string.IsNullOrEmpty(cfg.sceneMode))
            {
                SceneController sceneController = ControllerManager.instance.GetController(ModelConsts.Scene) as SceneController;
                sceneController.GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_build_tips", true);
                return;
            }

            //
            bool isBuildingOval = mainUnit.IsDistanceLess(new Vector2(buildPosition.x, buildPosition.z), 5f);
            if (isBuildingOval)
            {
                pathFindEnum = PathFindEnum.Building;
                //有效范围内，直接进入场景
                Debug.Log("Click and Direct Enter scene");
                this.DoEnterBuilding(cfg);
            }
            else
            {
                if (cfg.outPos.Count != 3) // TODO  pos + rot
                {
                    return;
                }

                buildPosition = new Vector3(cfg.outPos[0], cfg.outPos[1], cfg.outPos[2]);
                Debug.Log("Click and go to door and Enter scene，sceneId="+ sceneId+" x="+ cfg.outPos[0]+" z="+ cfg.outPos[2]);
                //有效范围外，寻路到门口
                (_scene.GetComponent<MainCtrlComponent>() as MainCtrlComponent).AutoRun(new Vector2(buildPosition.x, buildPosition.z), () =>
                {
                    this.DoEnterBuilding(cfg);
                });
            }

            //新埋点：点击建筑物
            //DataDotBuilding dot = new DataDotBuilding();
            //dot.Building_id = buildingId;
            //DataDotMgr.Collect(dot);
        }
        
        private void DoEnterBuilding(SceneCfg cfg)
        {
            Notifier.instance.SendNotification(NotifyConsts.LittleStopRunAni);
            SceneController sceneController = ControllerManager.instance.GetController(ModelConsts.Scene) as SceneController;
            sceneController.ReqChangeScene(cfg.sceneId );
        }
        
        private bool CheckCanReach(Vector3 point, out NavMeshHit realPos)
        {
            if (NavMesh.SamplePosition(point, out realPos, 0.1f, NavMesh.AllAreas))
            {
                return (_scene.GetComponent<MainCtrlComponent>() as MainCtrlComponent).CanReach(new Vector2(realPos.position.x, realPos.position.z));
            }

            return false;
        }
        
        public void DoClickLittleMapGround(Vector3 position)
        {
            pathFindEnum = PathFindEnum.LittleMapPart;
            (_scene.GetComponent<MainCtrlComponent>() as MainCtrlComponent).AutoRun(new Vector2(position.x, position.z),
            () =>
            {
                Notifier.instance.SendNotification(NotifyConsts.LittleStopRunAni);
                (_scene.GetComponent<EffectComponent>() as EffectComponent).StopUniqueEffect("clickeff");
            });
        }
        
        public void PreAutoRunToTarget(Vector3 position)
        {
            Unit mainUnit = this._scene.GetComponent<UnitComponent>().mainUnit;
            if (mainUnit == null) return;
         
            if (mainUnit.modMove.isMoving) return;
      
            pathFindEnum = PathFindEnum.LittleMapPart;
            (_scene.GetComponent<MainCtrlComponent>() as MainCtrlComponent).PreAutoRun(new Vector2(position.x, position.z));
        }
        
        public void DoClickAvatar(Unit hitUnit)
        {
            if (AppConst.IsDebug)
                Debug.Log($"Scene;Click Avatar. uid:{hitUnit.uid}");

            //TODO
            //Managers.AudioMgr.PlayAudio(AudioConfig.CLICK_AVATAR);

            //新埋点：点击avatar
            DataDotAvatar dot = new DataDotAvatar();
            dot.Avatar_id = hitUnit.avatarTid;
            dot.Background = DataDotMgr.GetBackground();
            DataDotMgr.Collect(dot);


            if (hitUnit.avatarType == ESceneAvatar.Avatar)
            {
                SoundManger.instance.PlayUI("click_Avatar");
            }
            else
            {
                SoundManger.instance.PlayUI("click_User");
            }


            scene.GetComponent<ChatComponent>().SetCurrChatAvatar(hitUnit.avatarTid, hitUnit.name);

            scene.GetComponent<ChatComponent>().SetNextAvatar(hitUnit.avatarTid,1);
            //先锁定Avatar，等待Avatar锁定成功
            this._scene.GetComponent<UnitComponent>().LockUnit(hitUnit, () =>
            {
                //单位选中特效
                var hitUnitPosition = new Vector3(hitUnit.position.x, 0f, hitUnit.position.y);
                scene.GetComponent<EffectComponent>().PlayUniqueEffect("select", hitUnitPosition, 1f, -1);
                //TODO 预准备对话数据
                Unit mainUnit = this._scene.GetComponent<UnitComponent>().mainUnit;
                if (mainUnit.IsDistanceGreat(hitUnit.position, _runMinDistance))
                {
                    Avatar avatar = this._scene.GetComponent<AvatarComponent>().GetAvatar(hitUnit.uid);
                    scene.GetComponent<MainCtrlComponent>().AutoRun(hitUnit.position, () =>
                        {
                            pathFindEnum = PathFindEnum.Avatar;
                            Notifier.instance.SendNotification(NotifyConsts.LittleStopRunAni);
                            MoveToOpenTitle(hitUnit);
                        },
                    isRun2Avatar:true);
                }
                else
                {
                    pathFindEnum = PathFindEnum.Avatar;
                    Notifier.instance.SendNotification(NotifyConsts.LittleStopRunAni);
                    MoveToOpenTitle(hitUnit);
                }
            },() =>
            {
                pathFindEnum = PathFindEnum.Avatar;
                Notifier.instance.SendNotification(NotifyConsts.LittleStopRunAni);
            });
        }

        private void MoveToOpenTitle(Unit hitUnit)
        {
            if (ChatLogicController.TestAvatarChat.IndexOf(hitUnit.avatarTid) >= 0)
            {
                Notifier.instance.SendNotification(NotifyConsts.ClickAvatarEnterStoryTitle,hitUnit.avatarTid);
            }
            else
            {
                //先打开卡片
                AvatarTaskController avatarTaskController = ControllerManager.instance.GetController(ModelConsts.AvatarTask) as AvatarTaskController;
                avatarTaskController.SendGetAvatarJobInfoReq(hitUnit);
            }
        }

        //todo 这个复杂逻辑写在camera里也不合适
        // private void OnClickLearnPathAvatarAndShowUI()
        // {
        //     var avatarID = GetLearnPathCurrAvatarID();
        //     if (avatarID <= 0)
        //     {
        //         Debug.LogError("Internal Error,OnClickLearnPathAvatarAndShowUI avatar data error ");
        //         return;
        //     }
        //     MainController mainCtrl = ControllerManager.instance.GetController(ModelConsts.Main) as MainController;
        //     var avatar = (mainCtrl.GetController<SceneController>(ModelConsts.Scene).scene.GetComponent<AvatarComponent>() as AvatarComponent).GetAvatarByAvatarId(avatarID);
        //     if (avatar == null)
        //     {
        //         Debug.LogError("Internal Error,OnClickLearnPathAvatarAndShowUI avatar not exist, id="+ avatarID);
        //         return;
        //     }
        //
        //     // var scene = mainCtrl.GetController<SceneController>(ModelConsts.Scene).scene;
        //     // var taskItem = mainCtrl.GetModel<LearnPathModel>(ModelConsts.LearnPath).GetLastUnlockedTaskItem();
        //     //
        //     // //challenge,career,tutor  //level 是常规NPC的career
        //     // string taskState = string.Empty;
        //     // switch (taskItem.task_type)
        //     // {
        //     //    
        //     //     case PB_TaskTypeEnum.ChallengeTask:
        //     //         taskState = "challenge";
        //     //         break;
        //     //     case PB_TaskTypeEnum.FreeTask:
        //     //         taskState = "career";
        //     //         break;
        //     //     case PB_TaskTypeEnum.TutorTask:
        //     //         taskState = "tutor";
        //     //         break;
        //     //     case PB_TaskTypeEnum.TNone:
        //     //     default:
        //     //         Debug.LogError("Internal Error,OnClickLearnPathAvatarAndShowUI task type not valid, task-id=" + taskItem.task_id);
        //     //         return;
        //     // }
        //     // Debug.Log("task title=" + taskItem.task_name);
        //     // avatar.ShowTalkUI(
        //     //     desc: taskItem.task_name,
        //     //     cost: taskItem.task_price_info.diamond_price.ToString(),
        //     //     state: taskState,
        //     //     () =>{
        //     //         ChapterUI.OnLearnPathButton_AllInOneClick(taskItem, mainCtrl);
        //     //     }); ;
        //
        // }
        
        // private long GetLearnPathCurrAvatarID() {
        //     MainController mainCtrl = ControllerManager.instance.GetController(ModelConsts.Main) as MainController;
        //     var learnPathModel = mainCtrl.GetModel<LearnPathModel>(ModelConsts.LearnPath);
        //     var progressInfo = learnPathModel.GetchapterProgressInfo();
        //     if (progressInfo!=null && progressInfo.avatar_info != null)
        //     {
        //         return progressInfo.avatar_info.avatar_id;
        //     }
        //     else {
        //         return -1;
        //     } 
        // }
        
        public void StopUniqueEffect()
        {
            (_scene.GetComponent<EffectComponent>() as EffectComponent).StopUniqueEffect("select");
        }
        
        #region 单击寻路类型
        public enum PathFindEnum
        {
            Avatar,
            Building,
            Ground,
            LittleMapAvatar,
            LittleMapBuilding,
            LittleMapPart,
            LearnPath,
            None,
        }

        #endregion
        
    }
}
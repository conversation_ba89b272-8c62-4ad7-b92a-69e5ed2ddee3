/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/09 17:43:29 星期四
# 功能：Nothing
****************************************************
*/

using FairyGUI;
using Game;
using Modules.DataDot;
using Msg.economic;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Shop
{
    public class PlanInstructionUI: ShopUIBase<UIBind.Shop.PlanInstructionPanel>
    {
        public PlanInstructionUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top; //主UI层
        
        private ShopModel _shopModel => GetModel<ShopModel>(ModelConsts.Shop);
        
        private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);
        
        private CurrencyModel _currencyModel => GetModel<CurrencyModel>(ModelConsts.CurrencyController);
        
        public enum PlanTimeType
        {
            Month,
            Year,
        }
        
        public enum PlanType
        {
            Premium,
            Lite,
        }

        private PlanTimeType _curPlanTimeType = PlanTimeType.Year;
        private PlanType _curPlanType = PlanType.Premium;

        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(this.ui.btnExit.onClick, OnBtnExitClicked);
            AddUIEvent(this.ui.comPlanInstruction.btnPremium.onClick, (I) => SetPlanType(PlanType.Premium));
            //AddUIEvent(this.ui.comPlanInstruction.btnBtnLite.onClick, (I) => SetPlanType(PlanType.Lite));
            AddUIEvent(this.ui.comPlanInstruction.btnSelectedYear.onClick, (I) => SetPlanTimeType(PlanTimeType.Year));
            AddUIEvent(this.ui.comPlanInstruction.btnSelectedMonth.onClick, (I) => SetPlanTimeType(PlanTimeType.Month));
            AddUIEvent(this.ui.comPlanInstruction.btnSubscribe.onClick, DoSubscribe);
            AddUIEvent(this.ui.comPlanInstruction.btnUpgrade.onClick, DoSubscribe);
            AddUIEvent(ui.btnServiceDetail.onClick, OnServiceDetailClicked);
            AddUIEvent(ui.btnPrivate.onClick, OnPrivacyClicked);
            AddUIEvent(ui.tfRevertPurchase.onClick, OnRestorePurchaseClicked);
            AddUIEvent(ui.comPlanInstruction.btnFree.onClick, OnBtnFreeClicked);
            // AddUIEvent(this.ui.comPlanInstruction.btnPremium.onClick, (I) => SetPlanPremium());
            InitUI();
        }

        private void InitUI()
        {
            //ui.comPlanInstruction.tfBtnLite.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_lite_title");
            ui.comPlanInstruction.tfBtnPremium.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_premium_title");
            ui.comPlanInstruction.tfBtnUpgrade.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_btn_upgrade");
            ui.comPlanInstruction.tfBtnSubscribe.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_btn_subscribe");
            ui.comPlanInstruction.tfBtnFree.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_btn_free");
            ui.tfServiceDetail.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_service");
            ui.tfPrivate.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_privacy");
            ui.tfRevertPurchase.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_restore");
        }
        
        protected override void OnShow()
        {
            SetPlanTimeType(_curPlanTimeType);
            SetPlanType(_curPlanType);
            Refresh();
        }

        public void ChangePlanTimeType(PlanTimeType planTimeType)
        {
            _curPlanTimeType = planTimeType;
        }
        
        public void ChangePlanType(PlanType planType)
        {
            _curPlanType = planType;
        }

        public void Refresh()
        {
            ui.comPlanInstruction.grpBtnSubscribe.visible =
                _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Canceled ||
                _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.FreeTrialCanceled;
            ui.comPlanInstruction.grpBtnFree.visible = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.UnSubscribe;

            var visible = false;
            var upgradeVisible = false;
            if (_curPlanType == PlanType.Premium)
            {
                if (_curPlanTimeType == PlanTimeType.Month)
                {
                    if (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthPremium)
                        visible = true;
                    if (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthLite)
                        upgradeVisible = true;
                }

                if (_curPlanTimeType == PlanTimeType.Year)
                {
                    if (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.YearPremium)
                        visible = true;
                    if (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthPremium || _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthLite ||
                        _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.YearLite)
                        upgradeVisible = true;
                }
            }
            else if (_curPlanType == PlanType.Lite)
            {
                if (_curPlanTimeType == PlanTimeType.Month)
                {
                    if (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthLite)
                        visible = true;
                }

                if (_curPlanTimeType == PlanTimeType.Year)
                {
                    if (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.YearLite)
                        visible = true;
                    if (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthLite)
                        upgradeVisible = true;
                }
            }

            ui.comPlanInstruction.grpCurPlan.visible = visible;
            ui.comPlanInstruction.tfEndDate.visible = false;
            ui.comPlanInstruction.tfFreePlan.visible = false;
            ui.comPlanInstruction.grpBtnUpgrade.visible = upgradeVisible;
            if (upgradeVisible)
            {
                ui.comPlanInstruction.spBtnUpgrade.spineAnimation.AnimationState.ClearListenerNotifications();
                ui.comPlanInstruction.spBtnUpgrade.spineAnimation.AnimationState.SetAnimation(0, "a1", false).Complete += (t) =>
                {
                    ui.comPlanInstruction.spBtnUpgrade.spineAnimation.AnimationState.SetAnimation(0, "a2", true);
                };
            }

            var s = "";
            var prefix_cur_plan =
                _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.YearPremium || _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthPremium
                    ? I18N.inst.MoStr("ui_plan_common_premium_title")
                    : I18N.inst.MoStr("ui_plan_common_lite_title");
            var prefix_cur_time = _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.YearPremium || _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.YearLite
                ? I18N.inst.MoStr("ui_plan_common_btn_yearly")
                : I18N.inst.MoStr("ui_plan_common_btn_monthly");
            var prefix_plan = _curPlanType == PlanType.Premium ? I18N.inst.MoStr("ui_plan_common_premium_title") : I18N.inst.MoStr("ui_plan_common_lite_title");
            var prefix_time = _curPlanTimeType == PlanTimeType.Month ? I18N.inst.MoStr("ui_plan_common_btn_monthly") : I18N.inst.MoStr("ui_plan_common_btn_yearly");
            
            var cfg = _shopModel.GetMemberTypeByName($"{prefix_plan.ToLower()}_{prefix_time.ToLower()}");
            var cfg2 = _shopModel.GetMemberTypeByName($"{prefix_cur_plan.ToLower()}_{prefix_cur_time.ToLower()}");
            var prefix_free_days = cfg.free_days.ToString();
            
            if (visible)
            {
                ui.comPlanInstruction.tfEndDate.SetLanguageKeyArgs(LanguageType.MotherTongue, "ui_plan_instruction_desc13", _shopModel.GetMemberExpiredTime().ToString(I18N.inst.MoStr("ui_plan_promotion_desc20")));
                ui.comPlanInstruction.tfEndDate.visible = true;
            }
            
            if (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.YearPremium || _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthPremium ||
                _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.YearLite || _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthLite)
                s = string.Format(I18N.inst.MoStr("ui_plan_instruction_desc14"), prefix_cur_plan, prefix_cur_time);


            if (s != "" && !visible)
            {
                ui.comPlanInstruction.tfEndDate.text = s;
                ui.comPlanInstruction.tfEndDate.visible = true;
            }

            if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.UnSubscribe)
            {
                ui.comPlanInstruction.tfFreePlan.visible = true;
                ui.comPlanInstruction.tfFreePlan.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_plan_instruction_desc12", prefix_free_days, prefix_plan);
            }

            ui.comPlanInstruction.grpExperiencedDesc.visible = _curPlanTimeType == PlanTimeType.Year;
            // ui.comPlanInstruction.tfSubscribeDesc2.text = prefix5;
            // var a = I18N.inst.MoStr("ui_plan_instruction_desc10");
            ui.comPlanInstruction.tfSubscribeDesc2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_plan_instruction_desc9", cfg.origin_price_in_display);
            // var b = I18N.inst.MoStr("ui_plan_instruction_desc9");
            ui.comPlanInstruction.tfSubscribeDesc4.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_plan_instruction_desc11", cfg.per_day_price_in_display);
            // var c = I18N.inst.MoStr("ui_plan_instruction_desc11");
            ui.comPlanInstruction.tfPlanDesc.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_instruction_desc5");
            ui.comPlanInstruction.tfPlanDesc2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_instruction_desc1");
            ui.comPlanInstruction.tfPlanDesc3.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_instruction_desc2");
            ui.comPlanInstruction.tfPlanDesc4.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_instruction_desc3");
            //ui.comPlanInstruction.tfPlanDesc5.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_instruction_desc4");
            ui.comPlanInstruction.tfCurPlan.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_instruction_desc8");
            ui.comPlanInstruction.tfYear.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_instruction_desc6");
            ui.comPlanInstruction.tfMonth.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_instruction_desc7");
            ui.comPlanInstruction.tfSubscribeDesc5.text = "";
            if (upgradeVisible)
            {
                ui.comPlanInstruction.tfSubscribeDesc5.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_plan_instruction_desc15", cfg.remain_price_in_display);
            }
            else
            {
                ui.comPlanInstruction.tfSubscribeDesc5.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_plan_instruction_desc10",cfg.price_in_display, _curPlanTimeType == PlanTimeType.Month ? I18N.inst.MoStr("ui_plan_instruction_desc7") : I18N.inst.MoStr("ui_plan_instruction_desc6"));
            }
        }
        
        public void SetPlanTimeType(PlanTimeType type)
        {
            if (type == PlanTimeType.Month)
            {
                ui.comPlanInstruction.stateTime.SetSelectedPage("month");
                //新埋点：用户在套餐页，点击年度
                DataDotSubscriptionYearly dot = new DataDotSubscriptionYearly();
                
                
                dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
                dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
                dot.clicked_member_type = GetClickedMemberType();
                DataDotMgr.Collect(dot);
            }
            else if (type == PlanTimeType.Year)
            {
                ui.comPlanInstruction.stateTime.SetSelectedPage("year");
                //新埋点：用户在套餐页，点击月度
                DataDotSubscriptionMonthly dot = new DataDotSubscriptionMonthly();
                
                
                dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
                dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
                dot.clicked_member_type = GetClickedMemberType();
                DataDotMgr.Collect(dot);
            }
            _curPlanTimeType = type;
            Refresh();
        }
        
        public void SetPlanType(PlanType type)
        {
            if (type == PlanType.Premium)
            {
                ui.comPlanInstruction.statePlan.SetSelectedPage("premium");
                //新埋点：用户在套餐页，点击Premium
                DataDotSubscriptionPremium dot = new DataDotSubscriptionPremium();
                
                
                dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
                dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
                dot.clicked_member_type = GetClickedMemberType();
                DataDotMgr.Collect(dot);
            }
            else if (type == PlanType.Lite)
            {
                ui.comPlanInstruction.statePlan.SetSelectedPage("lite");
                //新埋点：用户在套餐页，点击Lite
                DataDotSubscriptionLite dot = new DataDotSubscriptionLite();
                
                
                dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
                dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
                dot.clicked_member_type = GetClickedMemberType();
                DataDotMgr.Collect(dot);
            }
            _curPlanType = type;
            Refresh();
        }

        private void DoSubscribe()
        {
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            PurchasingManager.instance.StartPurchasing(GetCurrentPlanId(), PB_ProductType.PB_ProductType_Subscribe, OnSubscribeSuccess, OnBuyFail);
            AFHelper.Click_Trial_choose_next(GetCurrentPlanId());
            var cfg = GetCurCfg();
            DataDotSubscriptionPurchase dot = new DataDotSubscriptionPurchase();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            dot.clicked_member_type = GetClickedMemberType();
            dot.subscribe_price = cfg.price_in_cents;
            dot.price_currency = OsFuncAdapter.Ins.GetCountryCode();
            DataDotMgr.Collect(dot);
            
           
            
            product_id = GetCurrentPlanId();
            product_type = PB_ProductType.PB_ProductType_Subscribe.ToString();
        }

        private void OnBtnFreeClicked()
        {
            PurchasingManager.instance.StartPurchasing(GetCurrentPlanId(), PB_ProductType.PB_ProductType_Subscribe, OnSubscribeSuccess, OnBuyFail);
            AFHelper.Click_Trial_choose_next(GetCurrentPlanId());
            // GetUI<PlanPromotionStep1UI>(UIConsts.PlanPromotionStep1UI).Show();
            
           
            
            product_id = GetCurrentPlanId();
            product_type = PB_ProductType.PB_ProductType_Subscribe.ToString();
        }

        private void OnSubscribeSuccess()
        {
            OnBuySuccess();
            GetUI<SubscribeSuccessUI>(UIConsts.SubscribeSuccessUI).prefix =
                GetCurCfg().member_name == "lite_monthly" || GetCurCfg().member_name == "lite_yearly" ? "Lite" : "Premium";
            GetUI<SubscribeSuccessUI>(UIConsts.SubscribeSuccessUI).Show();
            Hide();
    
        }

        private string GetCurrentPlanId()
        {
            return GetCurCfg().product_id;
        }

        private PB_SubscriptionInfo GetCurCfg()
        {
            var prefix_plan = _curPlanType == PlanType.Premium ? I18N.inst.MoStr("ui_plan_common_premium_title") : I18N.inst.MoStr("ui_plan_common_lite_title");
            var prefix_time = _curPlanTimeType == PlanTimeType.Month ? I18N.inst.MoStr("ui_plan_common_btn_monthly") : I18N.inst.MoStr("ui_plan_common_btn_yearly");
            return _shopModel.GetMemberTypeByName($"{prefix_plan.ToLower()}_{prefix_time.ToLower()}");
        }

        private void OnBtnExitClicked()
        {
            Hide();
            //新埋点：用户在套餐页，点击退出
            DataDotSubscriptionQuit dot = new DataDotSubscriptionQuit();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            dot.clicked_member_type = GetClickedMemberType();
            DataDotMgr.Collect(dot);
        }

        private string GetClickedMemberType()
        {
            if (_curPlanTimeType == PlanTimeType.Year)
            {
                if (_curPlanType == PlanType.Premium)
                {
                    return "YearPremium";
                }
                if (_curPlanType == PlanType.Lite)
                {
                    return "YearLite";
                }
            }
            if (_curPlanTimeType == PlanTimeType.Month)
            {
                if (_curPlanType == PlanType.Premium)
                {
                    return "MonthPremium";
                }
                if (_curPlanType == PlanType.Lite)
                {
                    return "MonthLite";
                }
            }

            return null;
        }
    }
}
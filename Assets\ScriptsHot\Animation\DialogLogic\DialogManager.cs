﻿using System;
using System.Collections.Generic;
using Msg.explore;
using UnityEngine;

namespace AnimationSystem
{
    /// <summary>
    /// 说明：
    ///  目前需要以MonoBehaviour的形式添加在GO上。目前需要把此MB添加到与目标Animator相同的GO上。
    /// </summary>
    public class DialogManager : MonoBehaviour
    {
        // 对话状态枚举
        public enum DialogueStateType
        {
            None,  //无状态,不执行Update,角色不做任何事情。
            Custom,   //角色处于一个自定义的状态。角色将只基于当前状态执行逻辑。
            DialogueStart,      // 对话开始
            CharacterSpeaking,  // 角色说话中
            DialogueIdle,       // 对话间闲时
            PlayerSpeaking,     // 玩家说话中
            DialogueEnd         // 对话结束
        }
        
        public DialogueStateType currentState;
        public AnimationAvatarManager manager;
        
        // 各种对话状态使用的动画组
        private AnimGroup welcomeAnimGroup;
        private AnimGroup speakingAnimGroup;
        private AnimGroup idleAnimGroup;
        private AnimGroup listeningAnimGroup;
        private AnimGroup endAnimGroup;

        public void Start()
        {
            this.Initialize();
            this.EnterDialogueStartState();
        }

        public void Update()
        {
            // 如果需要在不同状态下执行不同的更新逻辑，可以在这里添加
        }

        private void Initialize()
        {
            if (!this.manager)
            {
                manager = GetComponent<AnimationAvatarManager>();
                if (!manager)
                {
                    this.manager = gameObject.AddComponent<AnimationAvatarManager>();
                }
            
                this.manager.animator = GetComponent<Animator>();
                var headMask = AvatarMaskHelper.CreateHeadMask();
                var bodyMask = AvatarMaskHelper.CreateBodyMask();
            
                this.manager.SetHeadMask(headMask);
                this.manager.SetBodyMask(bodyMask);

                this.manager.InitializePlayableGraph();
            }
            
            //初始化读取所有语义表。
            SemanticMapping.Instance.InitAllGroupSemanticData();
        }

        // 更新当前状态枚举值
        private void UpdateCurrentState(DialogueStateType newState)
        {
            if (this.currentState != newState)
            {
                this.currentState = newState;
                Debug.Log($"切换到状态：{newState}");
            }
        }
        
        #region api
        
        /// <summary>
        /// 进入对话开始状态
        /// </summary>
        public void EnterDialogueStartState()
        {
            UpdateCurrentState(DialogueStateType.DialogueStart);
            
            // 创建对话开始状态
            // 注意：DialogEnterState已经在内部完成了动画组的初始化
            DialogEnterState state = new DialogEnterState();
            this.manager.SetState(state);
        }
        
        /// <summary>
        /// 进入角色说话状态
        /// </summary>
        /// <param name="speakingAnimGroup">角色说话动画组</param>
        public void EnterCharacterSpeakingState()
        {
            UpdateCurrentState(DialogueStateType.CharacterSpeaking);
            
            // 创建角色说话状态
            DialogCharacterSpeakingState state = new DialogCharacterSpeakingState();
            this.manager.SetState(state);
        }
        
        /// <summary>
        /// 进入对话闲置状态
        /// </summary>
        /// <param name="idleAnimGroup">闲置动画组</param>
        public void EnterDialogueIdleState()
        {
            UpdateCurrentState(DialogueStateType.DialogueIdle);
            
            // 创建对话闲置状态
            DialogIdleState state = new DialogIdleState();
            this.manager.SetState(state);
        }
        
        /// <summary>
        /// 进入玩家说话状态
        /// </summary>
        /// <param name="listeningAnimGroup">倾听动画组</param>
        public void EnterPlayerSpeakingState()
        {
            UpdateCurrentState(DialogueStateType.PlayerSpeaking);
            
            // 创建玩家说话状态
            DialogPlayerSpeakingState state = new DialogPlayerSpeakingState();
            this.manager.SetState(state);
        }
        
        /// <summary>
        /// 进入对话结束状态
        /// </summary>
        /// <param name="endAnimGroup">结束动画组</param>
        public void EnterDialogueEndState()
        {
            UpdateCurrentState(DialogueStateType.DialogueEnd);
            
            // 创建对话结束状态
            DialogEndState state = new DialogEndState();
            this.manager.SetState(state);
        }
        
        /// <summary>
        /// 进入空状态，不执行任何动画
        /// </summary>
        public void EnterNoneState()
        {
            UpdateCurrentState(DialogueStateType.None);
            Debug.Log("进入None状态，不设置AnimState");
            IdleState state = new IdleState();
            this.manager.SetState(state);
        }

        public void EnterOnBoardingTalkingState()
        {
            
        }

        public void EnterCustomState()
        {
            UpdateCurrentState(DialogueStateType.Custom);
            
        }

        /// <summary>
        /// 必須在角色说话state才允许调用,否则调用无效。（产生警告）
        /// </summary>
        /// <param name="sentences"></param>
        public void ProcessDialogue(List<SingleSentenceInfo> sentences)
        {
            if (this.currentState == DialogueStateType.CharacterSpeaking)
            {
                (this.manager.currentState as DialogCharacterSpeakingState)?.ProcessDialogue(sentences);
            }
            else
            {
                Debug.LogWarning("不允许在非CharacterSpeaking下调用，请先调用方法切换到该State");
            }
        }
        
        /// <summary>
        /// 必須在角色说话state才允许调用,否则调用无效。（产生警告）
        /// </summary>
        /// <param name="sentences"></param>
        public void ProcessDialogue(List<PB_EmotionAnalysisSentenceResult> sentences)
        {
            if (this.currentState == DialogueStateType.CharacterSpeaking)
            {
                List<SingleSentenceInfo> sentencesInfos = new List<SingleSentenceInfo>();

                sentencesInfos = SemanticMapping.Instance.DeserializeObject(sentences);
                
                (this.manager.currentState as DialogCharacterSpeakingState)?.ProcessDialogue(sentencesInfos);
            }
            else
            {
                Debug.LogWarning("不允许在非CharacterSpeaking下调用，请先调用方法切换到该State");
            }
        }
        
        #endregion
    }
}
﻿using System;
using FairyGUI;
using UIBind.OnBoardInfo;
using UnityEngine;
using System.Collections.Generic;
using Msg.basic;
using Msg.incentive;

namespace ScriptsHot.Game.Modules.Login.OnBoarding
{
    public class OnBoardInfoUI : BaseUI<OnBoardInfoPanel>
    {
        public OnBoardInfoUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Home;
        
        private LoginOnBoardingModel BoardingModel => GetModel<LoginOnBoardingModel>(ModelConsts.Login);
        private PermissMsgController PermissionMsgCtrl => GetController<PermissMsgController>(ModelConsts.PermissMsg);
        private OnBoardUI OnBoardUI => GetUI<OnBoardUI>(UIConsts.OnBoardUI);
        private readonly HashSet<int> _singleChoiceMode = new HashSet<int>();
        private float _initProgress = 7f;
        
        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            AddUIEvent(ui.btnNext.com.onClick, OnClickNext);
            AddUIEvent(ui.btnBack.onClick, OnClickLastStep);

            AddUIEvent(ui.compShow.compList.list.onClickItem, OnClickOnBoardItem);
            ui.compShow.compList.list.itemRenderer = OnRendererOnBoardInfo;
            ui.compNextShow.compList.list.itemRenderer = OnRendererOnBoardInfoNext;
            
            ui.compShow.compLiner.tfContent.SetKey("ui_onboard_progress_learn_on");
            ui.compNextShow.compLiner.tfContent.SetKey("ui_onboard_progress_learn_on");

            _singleChoiceMode.Clear();
            _singleChoiceMode.Add((int)LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal);
            _singleChoiceMode.Add((int)LoginOnBoardingModel.OnBoardInfoEnum.EnglishLevel);
            _singleChoiceMode.Add((int)LoginOnBoardingModel.OnBoardInfoEnum.StudyTime);
            _singleChoiceMode.Add((int)LoginOnBoardingModel.OnBoardInfoEnum.Gender);
            _singleChoiceMode.Add((int)LoginOnBoardingModel.OnBoardInfoEnum.Age);
        }

        private Action _action;
        private int _curStep;
        
        private Vector3 _initHeadPos;//初始head坐标
        private Vector3 _initShowPos;//初始show坐标
        private Vector3 _initBtnPos;//初始btn坐标

        private bool _isShowPush;
        
        protected override void OnShow()
        {
            base.OnShow();
            _initHeadPos = ui.compHead.com.position;
            _initShowPos = ui.compShow.com.position;
            _initBtnPos = ui.btnNext.com.position;
            
            _action = (Action)args[0];
            _curStep = 0;
            _isShowPush = PermissionMsgCtrl.PremissNotifIsOpen;
            ui.compBar.bar.max = 100;
            ui.compBar.bar.value = _initProgress;
            ShowNextView();
            RefreshHead(_curStep);
            ShowAnim(ui.compShow);

        }

        protected override void OnHide()  
        {
            base.OnHide();
            OnBoardUI.StopTTS();
        }

        protected override bool isFullScreen => true;

        private void OnClickNext()
        {
            BoardingModel.DicOnBoardSelect.TryGetValue((LoginOnBoardingModel.OnBoardInfoEnum)_curStep, out int value);
            if (value == 0 && _curStep != (int)LoginOnBoardingModel.OnBoardInfoEnum.Progress)
                return;
            
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Heavy);
            DotAndReqNextOnBoardInfo();
            
            if (_curStep == (int)LoginOnBoardingModel.OnBoardInfoEnum.StudyTime && !_isShowPush)
            {
                _isShowPush = true;
                GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("push_guider_03",
                    () => GetController<PermissMsgController>(ModelConsts.PermissMsg).OpenSystemSetting(), null, 2,
                    "push_guider_btn_txt_01", "push_guider_btn_txt_02");
                return;
            }
            
            
            if (_curStep == Enum.GetNames(typeof(LoginOnBoardingModel.OnBoardInfoEnum)).Length)
            {
                RefreshProgress(_curStep);
                TimerManager.instance.RegisterTimer((a) =>
                {
                    _action?.Invoke();
                    Hide();
                }, 400);
            }
            else
            {
                RefreshProgress(_curStep);

                ui.btnNext.state.selectedIndex = 0;
                HideAnim();
            }
        }

        private void OnClickLastStep()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            _curStep = Mathf.Max(0, _curStep - 2);
            RefreshProgress(_curStep);
            ShowNextView();
            RefreshHead(_curStep);
        }
        
        private void OnClickOnBoardItem(EventContext ext)
        {
            GComponent comp = ext.sender as GComponent;
            if (comp == null) return;
            
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);
            
            int index = comp.GetChildIndex(ext.data as GObject);
            int selectVal = 0;
            if (!_singleChoiceMode.Contains(_curStep))
                BoardingModel.DicOnBoardSelect.TryGetValue((LoginOnBoardingModel.OnBoardInfoEnum)_curStep,
                    out selectVal);
            selectVal = ToggleOption(index, selectVal);
            BoardingModel.SetOnBoardSelect((LoginOnBoardingModel.OnBoardInfoEnum)_curStep, selectVal);

            RefreshCompShow(ui.compShow, _curStep);
            ui.btnNext.state.selectedIndex = selectVal > 0 ? 1 : 0;
        }

        /// <summary>
        /// 1 : cur show
        /// 2 : cur hide next show -> cur center
        /// 3 : 2
        /// </summary>
        /// <param name="compShow"></param>
        
        private void ShowNextView()
        {
            int maxStep = Enum.GetNames(typeof(LoginOnBoardingModel.OnBoardInfoEnum)).Length;
            if (_curStep < maxStep)
                _curStep++;
            DotAppearOnBoardInfo();
            RefreshCompShow(ui.compShow, _curStep);
            RefreshCompShow(ui.compNextShow, _curStep + 1);
            BoardingModel.DicOnBoardSelect.TryGetValue((LoginOnBoardingModel.OnBoardInfoEnum)_curStep, out int value);
            ui.btnBack.visible = _curStep > 1;
            ui.first.selectedIndex = _curStep > 1 ? 1 : 0;
            ui.btnNext.state.selectedIndex =
                _curStep == (int)LoginOnBoardingModel.OnBoardInfoEnum.Progress || value > 0 ? 1 : 0;
            ui.btnNext.tfNext.SetKey(_curStep == (int)LoginOnBoardingModel.OnBoardInfoEnum.Progress
                ? "ui_onboard_start"
                : "ui_settlement_next");
        }

        private void RefreshCompShow(CompShow comp, int step)
        {
            if (step <= 0 || step > Enum.GetNames(typeof(LoginOnBoardingModel.OnBoardInfoEnum)).Length)
                return;
            
            comp.compList.list.RemoveChildren(0,-1,true);
            
            comp.style.selectedIndex = 0;
            if (BoardingModel.DicOnBoardItemUrl.ContainsKey((LoginOnBoardingModel.OnBoardInfoEnum)step))
                comp.compList.list.defaultItem =
                    BoardingModel.DicOnBoardItemUrl[(LoginOnBoardingModel.OnBoardInfoEnum)step];

            comp.compList.list.columnCount = 1;
            if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.Progress)
                comp.style.selectedIndex = 1;
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal)
                comp.compList.list.numItems = BoardingModel.CfgOnBoardPrimaryGoal.Count;
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.UseEnglish)
            {
                BoardingModel.DicOnBoardSelect.TryGetValue(LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal, out int goalVal);
                if (goalVal == 0) return;
                int index = GetSelectedIndexes(BoardingModel.CfgOnBoardPrimaryGoal.Count, goalVal)[0];
                int goalId = BoardingModel.CfgOnBoardPrimaryGoal[index].ID;
                comp.compList.list.numItems = BoardingModel.DicOnBoardUseEnglish[goalId].Count;
            }
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.EnglishLevel)
                comp.compList.list.numItems = BoardingModel.CfgOnBoardEnglishLevel.Count;
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.Hobby)
            {
                comp.compList.list.columnCount = 2;
                comp.compList.list.numItems = BoardingModel.CfgOnBoardHobbies.Count;
            }
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.StudyTime)
                comp.compList.list.numItems = BoardingModel.CfgOnBoardTime.Count;
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.Gender)
                comp.compList.list.numItems = BoardingModel.CfgOnBoardGender.Count;
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.Age)
            {
                comp.compList.list.columnCount = 2;
                comp.compList.list.numItems = BoardingModel.CfgOnBoardAge.Count;
            }
        }

        private void RefreshHead(int step)
        {
            if (step <= 0 || step > Enum.GetNames(typeof(LoginOnBoardingModel.OnBoardInfoEnum)).Length)
                return;
            
            BoardingModel.DicOnBoardFlowSay.TryGetValue((LoginOnBoardingModel.OnBoardInfoEnum)step, out var language);
            if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.UseEnglish)
            {
                BoardingModel.DicOnBoardSelect.TryGetValue(LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal, out int goalVal);
                if (goalVal == 0) return;
                int index = GetSelectedIndexes(BoardingModel.CfgOnBoardPrimaryGoal.Count, goalVal)[0];
                language = BoardingModel.CfgOnBoardPrimaryGoal[index].habitTitleKey;
            }

            ui.compHead.tfFlow.SetKey(language);
            ui.compHead.grp.EnsureBoundsCorrect();
            OnBoardUI.PlayTTS(I18N.inst.MoStr(language));
        }

        private void RefreshProgress(int curStep)
        {
            int maxStep = Enum.GetNames(typeof(LoginOnBoardingModel.OnBoardInfoEnum)).Length;
            float final = _initProgress + Mathf.Max(0, curStep) * 1.0f / maxStep *
                (100 - _initProgress);
            ui.compBar.bar.TweenValue(final, 0.2f);
        }
        
        private void OnRendererOnBoardInfo(int index, GObject obj)
        {
            GComponent comp = obj as GComponent;
            if (comp == null) return;
            
            BoardingModel.DicOnBoardSelect.TryGetValue((LoginOnBoardingModel.OnBoardInfoEnum)_curStep,
                out int selectVal);
            RefreshItemInfo(comp, index, _curStep);
            comp.GetController("choice").selectedIndex = IsOptionSelected(index, selectVal) ? 1 : 0;

            if (comp.GetChild("tfLabel") != null && index >= 0 && index < BoardingModel.ListOnBoardLevelName.Count)
            {
                comp.GetChild("tfLabel").asTextField.text = BoardingModel.ListOnBoardLevelName[index];
                comp.GetChild("grp").asGroup.EnsureBoundsCorrect();
            }
        }

        private void OnRendererOnBoardInfoNext(int index,GObject obj)
        {
            GComponent comp = obj as GComponent;
            if (comp == null) return;

            BoardingModel.DicOnBoardSelect.TryGetValue((LoginOnBoardingModel.OnBoardInfoEnum)(_curStep + 1),
                out int selectVal);
            RefreshItemInfo(comp, index, _curStep + 1);
            comp.GetController("choice").selectedIndex = IsOptionSelected(index, selectVal) ? 1 : 0;

            if (comp.GetChild("tfLabel") != null && index >= 0 && index < BoardingModel.ListOnBoardLevelName.Count)
            {
                comp.GetChild("tfLabel").asTextField.text = BoardingModel.ListOnBoardLevelName[index];
                comp.GetChild("grp").asGroup.EnsureBoundsCorrect();
            }
        }

        private void RefreshItemInfo(GComponent comp, int index, int step)
        {
            if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal)
            {
                comp.GetChild("ldr").asLoader.icon = BoardingModel.CfgOnBoardPrimaryGoal[index].icon;
                comp.GetChild("title0").asTextField.SetKey(BoardingModel.CfgOnBoardPrimaryGoal[index].titleKey);
            }
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.UseEnglish)
            {
                BoardingModel.DicOnBoardSelect.TryGetValue(LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal,
                    out int goalVal);
                int goalIndex = GetSelectedIndexes(BoardingModel.CfgOnBoardPrimaryGoal.Count, goalVal)[0];
                int goalId = BoardingModel.CfgOnBoardPrimaryGoal[goalIndex].ID;
                comp.GetChild("ldr").asLoader.icon = BoardingModel.DicOnBoardUseEnglish[goalId][index].icon;
                comp.GetChild("title0").asTextField.SetKey(BoardingModel.DicOnBoardUseEnglish[goalId][index].habitKey);
            }
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.EnglishLevel)
            {
                comp.GetChild("ldr").asLoader.icon = BoardingModel.CfgOnBoardEnglishLevel[index].icon;
                comp.GetChild("title0").asTextField.SetKey(BoardingModel.CfgOnBoardEnglishLevel[index].titleKey);
            }
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.Hobby)
            {
                comp.GetChild("ldr").asLoader.icon = BoardingModel.CfgOnBoardHobbies[index].icon;
                comp.GetChild("title0").asTextField.SetKey(BoardingModel.CfgOnBoardHobbies[index].titleKey);
            }
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.StudyTime)
            {
                comp.GetChild("ldr").asLoader.icon = BoardingModel.CfgOnBoardTime[index].icon;
                comp.GetChild("title0").asTextField.SetKey(BoardingModel.CfgOnBoardTime[index].titleKey);
            }
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.Gender)
            {
                comp.GetChild("ldr").asLoader.icon = BoardingModel.CfgOnBoardGender[index].icon;
                comp.GetChild("title0").asTextField.SetKey(BoardingModel.CfgOnBoardGender[index].titleKey);
            }
            else if (step == (int)LoginOnBoardingModel.OnBoardInfoEnum.Age)
            {
                comp.GetChild("ldr").asLoader.icon = BoardingModel.CfgOnBoardAge[index].icon;
                comp.GetChild("title0").asTextField.SetKey(BoardingModel.CfgOnBoardAge[index].titleKey);
            }
        }

        private void ShowBtnNextAnim()
        {
            ui.btnNext.com.alpha = 0;
            ui.btnNext.com.position = _initBtnPos + new Vector3(0, -200, 0);
            GTween.To(0f, 1f, 0.3f)
                .SetDelay(0.1f)
                .OnUpdate((t) =>
                {
                    float v = t.value.x;
                    ui.btnNext.com.alpha = v;
                    ui.btnNext.com.position = Vector3.Lerp(_initBtnPos + new Vector3(0, -200, 0), _initBtnPos, v);
                });
        }

        private void ShowAnim(CompShow compShow, Action callback = null)
        {
            // compHead 动画：0-300ms
            ui.compHead.com.alpha = 0;
            ui.compHead.com.position = _initHeadPos + new Vector3(0, -20, 0);
            GTween.To(0f, 1f, 0.3f)
                .SetDelay(0f)
                .OnUpdate((t) =>
                {
                    ui.compHead.com.alpha = t.value.x;
                    ui.compHead.com.position =
                        Vector3.Lerp(_initHeadPos + new Vector3(0, -20, 0), _initHeadPos, t.value.x);
                });

            // compShow 动画：100-400ms
            compShow.com.visible = true;
            compShow.com.alpha = 0;
            compShow.com.position = _initShowPos + new Vector3(0, -200, 0);
            if (compShow == ui.compShow)
                ShowBtnNextAnim();
            GTween.To(0f, 1f, 0.3f)
                .SetDelay(0.1f)
                .OnUpdate((t) =>
                {
                    float v = t.value.x;
                    compShow.com.alpha = v;
                    compShow.com.position = Vector3.Lerp(_initShowPos + new Vector3(0, -200, 0), _initShowPos, v);
                }).OnComplete(() => callback?.Invoke());
        }

        private void HideAnim()
        {
            ui.compShow.com.touchable = false;
            // compHead 动画：0-300ms
            ui.compHead.com.alpha = 1;
            ui.compHead.com.position = _initHeadPos;
            GTween.To(1f, 0f, 0.3f)
                .SetDelay(0f)
                .OnUpdate((t) =>
                {
                    ui.compHead.com.alpha = t.value.x;
                    ui.compHead.com.position = Vector3.Lerp(_initHeadPos, _initHeadPos + new Vector3(0, -20, 0), 1 - t.value.x);
                });

            // compShow 动画：100-400ms
            ui.compShow.com.alpha = 1;
            ui.compShow.com.position = _initShowPos;
            ui.btnNext.com.alpha = 1;
            ui.btnNext.com.touchable = false;
            ui.btnNext.com.position = _initBtnPos;
            GTween.To(1f, 0f, 0.3f)
                .SetDelay(0.1f)
                .OnUpdate((t) =>
                {
                    float v = t.value.x;
                    ui.compShow.com.alpha = v;
                    ui.compShow.com.position =
                        Vector3.Lerp(_initShowPos, _initShowPos + new Vector3(0, -200, 0), 1 - v);
                })
                .OnComplete(() =>
                {
                    ui.compShow.com.visible = false;
                    RefreshCompShow(ui.compNextShow, _curStep + 1);
                    RefreshHead(_curStep + 1);
                    ShowAnim(ui.compNextShow, Reset);
                });
        }

        private void Reset()
        {
            ShowNextView();
            ui.compShow.com.position = _initShowPos;
            ui.compShow.com.alpha = 1;
            ui.compShow.com.touchable = true;
            ui.btnNext.com.touchable = true;
            ui.compShow.com.visible = true;
            ui.compNextShow.com.visible = false;
        }

        #region 与或非

        /// <summary>
        /// 判断第n个选项是否被选中（n从0开始）
        /// </summary>
        private bool IsOptionSelected(int n, int value)
        {
            return (value & (1 << n)) != 0;
        }

        /// <summary>
        /// 切换第n个选项的选中状态（选中/取消）
        /// </summary>
        private int ToggleOption(int n, int value)
        {
            value ^= (1 << n);
            return value;
        }

        /// <summary>
        /// 获取所有被选中的选项索引
        /// </summary>
        private List<int> GetSelectedIndexes(int maxOptionCount, int value)
        {
            var list = new List<int>();
            for (int i = 0; i < maxOptionCount; i++)
            {
                if ((value & (1 << i)) != 0)
                    list.Add(i);
            }
            return list;
        }

        #endregion

        private void DotAppearOnBoardInfo()
        {
            switch (_curStep)
            {
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal:
                    DataDotMgr.Collect(new DataDotAppear_Userinfo_target());
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.UseEnglish:
                    DataDotMgr.Collect(new DataDotAppear_Userinfo_target_detail());
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.EnglishLevel:
                    DataDotMgr.Collect(new DataDotAppear_Userinfo_level());
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.Hobby:
                    DataDotMgr.Collect(new DataDotAppear_Userinfo_hobby());
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.StudyTime:
                    DataDotMgr.Collect(new DataDotAppear_Userinfo_learning_time());
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.Progress:
                    DataDotMgr.Collect(new DataDotAppear_Userinfo_learning_curve());
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.Gender:
                    DataDotMgr.Collect(new DataDotAppear_Userinfo_gender());
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.Age:
                    DataDotMgr.Collect(new DataDotAppear_Userinfo_age());
                    break;
            }
        }
        
        private void DotAndReqNextOnBoardInfo()
        {
            var msg = new CS_SetUserPortraitsReq();
            string target = String.Empty;
            BoardingModel.DicOnBoardSelect.TryGetValue((LoginOnBoardingModel.OnBoardInfoEnum)_curStep, out int select);
            switch (_curStep)
            {
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal:
                    int pSelect = GetSelectedIndexes(BoardingModel.CfgOnBoardPrimaryGoal.Count, select)[0];
                    DataDotMgr.Collect(new DataDotClick_Userinfo_target_next()
                    {
                        Learning_target = BoardingModel.CfgOnBoardPrimaryGoal[pSelect].ID.ToString()
                    });
                    msg.interest_goal_id = (PB_LearningGoalEnum)BoardingModel.CfgOnBoardPrimaryGoal[pSelect].ID;
                    MsgManager.instance.SendMsg(msg);
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.UseEnglish:
                    BoardingModel.DicOnBoardSelect.TryGetValue(LoginOnBoardingModel.OnBoardInfoEnum.PrimaryGoal, out int goalVal);
                    if (goalVal != 0)
                    {
                        int goalIndex = GetSelectedIndexes(BoardingModel.CfgOnBoardPrimaryGoal.Count, goalVal)[0];
                        int goalId = BoardingModel.CfgOnBoardPrimaryGoal[goalIndex].ID;
                        List<int> detail = new List<int>();
                        if (BoardingModel.DicOnBoardUseEnglish.TryGetValue(goalId, out var useEnglishList))
                        {
                            List<int> useSelect = GetSelectedIndexes(useEnglishList.Count, select);
                            foreach (var index in useSelect)
                            {
                                detail.Add(useEnglishList[index].ID);
                                msg.interest_scene_ids.Add((PB_LearningSceneEnum)useEnglishList[index].ID);
                            }
                        }
                        DataDotMgr.Collect(new DataDotClick_Userinfo_target_detail_next()
                        {
                            Learning_target = goalId.ToString(),
                            Learning_detail = detail,
                        });
                    }
                    MsgManager.instance.SendMsg(msg);
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.EnglishLevel:
                    int lSelect = GetSelectedIndexes(BoardingModel.CfgOnBoardEnglishLevel.Count, select)[0];
                    DataDotMgr.Collect(new DataDotClick_Userinfo_level_confirm()
                    {
                        User_level = BoardingModel.CfgOnBoardEnglishLevel[lSelect].level
                    });
                    msg.language_level = BoardingModel.CfgOnBoardEnglishLevel[lSelect].level;
                    MsgManager.instance.SendMsg(msg);
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.Hobby:
                    List<int> hSelect = GetSelectedIndexes(BoardingModel.CfgOnBoardHobbies.Count, select);
                    foreach (var index in hSelect)
                    {
                        target = $"{target},{(PB_HobbyEnum)BoardingModel.CfgOnBoardHobbies[index].ID}";
                        msg.hobbies.Add((PB_HobbyEnum)BoardingModel.CfgOnBoardHobbies[index].ID);
                    }
                    DataDotMgr.Collect(new DataDotClick_Userinfo_hobby_next()
                    {
                        Hobby = target
                    });
                    MsgManager.instance.SendMsg(msg);
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.StudyTime:
                    int tSelect = GetSelectedIndexes(BoardingModel.CfgOnBoardTime.Count, select)[0];
                    DataDotMgr.Collect(new DataDotClick_Userinfo_learning_time_next()
                    {
                        Learning_time = ((PB_LearningPlanEnum)BoardingModel.CfgOnBoardTime[tSelect].ID).ToString()
                    });
                    msg.expected_learning_time = (PB_LearningPlanEnum)BoardingModel.CfgOnBoardTime[tSelect].ID;
                    MsgManager.instance.SendMsg(msg);
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.Progress:
                    DataDotMgr.Collect(new DataDotClick_Userinfo_learning_curve_next());
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.Gender:
                    int gSelect = GetSelectedIndexes(BoardingModel.CfgOnBoardGender.Count, select)[0];
                    DataDotMgr.Collect(new DataDotClick_Userinfo_gender_next()
                    {
                        User_gender = ((PB_GenderEnum)(gSelect + 1)).ToString()
                    });
                    msg.gender = (PB_GenderEnum)(gSelect + 1);
                    MsgManager.instance.SendMsg(msg);
                    break;
                case (int)LoginOnBoardingModel.OnBoardInfoEnum.Age:
                    int aSelect = GetSelectedIndexes(BoardingModel.CfgOnBoardAge.Count, select)[0];
                    DataDotMgr.Collect(new DataDotClick_Userinfo_age_next()
                    {
                        User_age = ((PB_AgeEnum)BoardingModel.CfgOnBoardAge[aSelect].ID).ToString()
                    });
                    msg.age = (PB_AgeEnum)BoardingModel.CfgOnBoardAge[aSelect].ID;
                    MsgManager.instance.SendMsg(msg);
                    break;
            }
        }
    }
}
﻿using CommonUI;

public partial class ExploreController
{
    private ExploreNetStrategy _netStrategy;
    public ExploreNetStrategy CurNetStrategy => _netStrategy;
    public void SetNet(ExploreNetStrategy strategy) {
        _netStrategy = strategy;
        _netStrategy.AddEvent();
    }
    public virtual void RequestAsr(string referenceText,bool isAssess = false, bool isTranslate = false, string source = "",bool record = true)
    {
        // 忘了之前为何要在这里添加 停止声音 ，先注释掉了
        // OnStopStreamAudioTTs(string.Empty,null);
        
        ASRReqParam param = new ASRReqParam();
        param.referenceText = referenceText;
        param.isAssess = isAssess;
        param.is_translate = isTranslate;
        param.source = string.IsNullOrEmpty(source)?string.Empty:source;
        //VFDebug.LogError("_netStrategy.StartASR");
        _netStrategy.StartASR(param,
            (aSRProgressVO) => OnAsrProgressCallBack(aSRProgressVO),
            (aSRCompleteVO) => OnAsrCompleteCallBack(aSRCompleteVO),
            (aSRFailVO) => OnAsrFailCallBack(aSRFailVO),record);
    }
    protected void OnAsrProgressCallBack(ASRProgressVO data)
    {
  
    }
    protected void OnAsrCompleteCallBack(ASRCompleteVO aSRCompleteVO)
    {

    }
    protected void OnAsrFailCallBack(ASRFailVO aSRFailVO)
    {
        aSRFailVO.desp = "Explore Error";

        if (aSRFailVO.status == ASRStatus.TimeShort)
        {
            this.GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("common_toast",true);
        }
        else if (aSRFailVO.status == ASRStatus.Exception)
        { 
            // 因为 OnAsrFailCallBack 会捕获很多问题，不一定是网络 ，所以 在触发的地方添加了log ，不做弹窗处理， 因为弹窗描述的不准确
            // this.GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("common_netError",true);
        }
        HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
        if (hCon.IsExploreTab())
        {
            OnCancelRecored();
            Notifier.instance.SendNotification(NotifyConsts.ExploreGRPCFail);
        }
    }
    
}
using Cysharp.Threading.Tasks;
using System;
using UnityEngine;
using YooAsset;

public class HotUpdateFlow
{
    public static string YooAssetPackage = "main";
    private bool _init = false;

    // 保存RemoteServices实例，用于后续动态更新URL
    public HotUpdateCore.RemoteServices RemoteServices { get; private set; }
    
    private async UniTask StartOfflineMode()
    {
        //完全offline的本地模式
        Debug.Log("当前热更模式 = offline");
        MainLoadingUI.Ins.SetProgressValue(100f);
        await this.StartYooAssetsOfflineMode();
    }
    
    public async UniTask<HotUpdateCore.ReadyHotUpdateResult> ReadyHotUpdate(HotUpdateCore core , HotUpdateResult result)
    {
        if (result == null || core == null)
        {
            return HotUpdateCore.ReadyHotUpdateResult.Fail;
        }
        
        Debug.Log($"main.IsHotUpdateMode = {result.Mode}");
        
        if (result.Mode == HotUpdateMode.Simul)
        {
            await this.StartYooAssetsSimulateMode();
            return HotUpdateCore.ReadyHotUpdateResult.Success;
        }
        else if (result.Mode == HotUpdateMode.Offline)
        {
            await StartOfflineMode();
            return HotUpdateCore.ReadyHotUpdateResult.Success;
        }
        else if (result.Mode == HotUpdateMode.Hotupdate)
        {
            try
            {
                string hotUpdateUrl = result.CdnUrl;
                if (string.IsNullOrEmpty(hotUpdateUrl))
                {
                    return HotUpdateCore.ReadyHotUpdateResult.Fail;
                }
                Debug.Log("HotUpdateFlow cdn: " + hotUpdateUrl);
                var initResult = await this.StartYooAssetsHotUpdate(hotUpdateUrl);
                if (!initResult)
                {
                    var errorMsg = HotUpdateLangCfg.GetStr(HUKey.updateSysException, (int) HotUpdateCore.ErrorCode.YooAssets_Init);  
                    VFDebug.LogError(errorMsg);
                    // core.OpenConfirmUI(errorMsg, null, null, 0);
                    return HotUpdateCore.ReadyHotUpdateResult.CanRetry;
                }
 
                //这步因为PackageVersion 和 cdn文件夹用的同一个名字，尝试优化调，可以省100ms时间
                var packageVersion = result.PkgVer;
                // var packageVersion = await this.UpdatePackageVersion();
                // if (string.IsNullOrEmpty(packageVersion))
                // {
                //     var errorMsg = HotUpdateLangCfg.GetStr(HUKey.updateSysException, (int) HotUpdateCore.ErrorCode.YooAssets_Update_Package_Version);  
                //     VFDebug.LogError(errorMsg);
                //     // core.OpenConfirmUI(errorMsg, null, null, 0);
                //     return HotUpdateCore.ReadyHotUpdateResult.CanRetry;
                // }

                var maifestResult = await this.UpdatePackageManifest(packageVersion);
                if (!maifestResult)
                {
                    var errorMsg = HotUpdateLangCfg.GetStr(HUKey.updateSysException, (int) HotUpdateCore.ErrorCode.YooAssets_Update_Package_Manifest);  
                    VFDebug.LogError(errorMsg);
                    // core.OpenConfirmUI(errorMsg, null, null, 0);
                    return HotUpdateCore.ReadyHotUpdateResult.CanRetry;
                }    
                await this.ReadyPackageDownload(result);
                return HotUpdateCore.ReadyHotUpdateResult.Success;
            }
            catch (Exception e)
            {
                Debug.LogError($"HotUpdateFlow Exception.{e.Message}");
                return HotUpdateCore.ReadyHotUpdateResult.Fail;
            }
        }
        return HotUpdateCore.ReadyHotUpdateResult.Fail;;
    }

    #region old
//     public async UniTask<bool> StartHotUpdate()
//     {
//         Debug.Log($"AppConst.IsHotUpdateMode = {AppConst.IsHotUpdateMode}");
//         //非热更新模式
//         if (!AppConst.IsHotUpdateMode)
//         {
// #if UNITY_EDITOR
//             Debug.Log("当前热更模式 = Simulate");
//             MainLoadingUI.Ins.SetProgressValue(100f);
//             await this.StartYooAssetsSimulateMode();
//             return true;
// #else
//             await StartOfflineMode();
//             return true;
// #endif
//         }
//          
//         
//         Debug.Log("当前热更模式 = hotupdate");
//         string hotVersionData = await GetHotVersionData();
//         if (string.IsNullOrEmpty(hotVersionData))
//         {
//             // var errorMsg = HotUpdateLangCfg.GetStr(HUKey.updateSysException, (int) HotUpdateCore.ErrorCode.Nacos);
//             // OpenConfirmUI(errorMsg, null, null, 0);
//              
//             var errorMsg = HotUpdateLangCfg.GetStr(HUKey.networkNotAvailableTip);
//             OpenConfirmUI(errorMsg, null, null, 0);
//             return false;
//         }
//
//         Debug.Log($"hotVersionData = {hotVersionData}");
//         //todo0 ToObject(resp.data) 解析失败时，很可能还需要再进入拉取的重试
//         //目前存在ToObject优势就是会异常解析的奇特bug
//
//         try
//         {
//             HotVersionResp resp = JsonMapper.ToObject<HotVersionResp>(hotVersionData);
//             JsonData versionData = JsonMapper.ToObject(resp.data);
//             VersionData = versionData;
//             string appVersion = Application.version; //AppConst.AotVersion;//检查底包是否有热更新版本
//
//             if (!versionData.ContainsKey(appVersion))
//             {
//                 var errorMsg = HotUpdateLangCfg.GetStr(HUKey.checkVersionErrTip, appVersion);
//                 OpenConfirmUI(errorMsg, null, null, 0);
//                 Debug.LogError(errorMsg);
//                 return false;
//             }
//
//
//             string platformTag = "";
//             switch (Application.platform)
//             {
//                 case RuntimePlatform.Android:
//                     platformTag = "android";
//                     break;
//                 case RuntimePlatform.IPhonePlayer:
//                     platformTag = "ios";//固定写为 全大写
//                     break;
//                 default:
//                     break;
//             }
//             
//             #if UNITY_EDITOR
//             platformTag = "ios";//固定写为 全大写
//             #endif
//
//             if (string.IsNullOrEmpty(platformTag))
//             {
//                 var errorMsg = HotUpdateLangCfg.GetStr(HUKey.checkVersionErrTip, appVersion + " ErrInGetPlatformTag");
//                 OpenConfirmUI(errorMsg, null, null, 0);
//                 Debug.LogError(errorMsg);
//                 return false;
//             }
//
//
//             //EOL=end if life
//             if (versionData[appVersion].ContainsKey("isEOL")) {
//                 bool isEOL = (bool)versionData[appVersion]["isEOL"];
//                 if (isEOL)
//                 {
//                     var mainTip = HotUpdateLangCfg.GetStr(HUKey.forceJumpDownloadPageTip);
//                     var comfirmBtnTip = HotUpdateLangCfg.GetStr(HUKey.forceJumpBtn);
//
//                     string shopURL = "";
//                     if (versionData.ContainsKey(platformTag+"_shopURL"))
//                     {
//                         shopURL = (string)versionData[platformTag + "_shopURL"];
//                     }
//                     else {
//                         var errorMsg = HotUpdateLangCfg.GetStr(HUKey.checkVersionErrTip, appVersion + " ErrInGet("+ platformTag + "_shopURL)");
//                         OpenConfirmUI(errorMsg, null, null, 0);
//                         Debug.LogError(errorMsg);
//                         return false;
//                     }
//                     
//                     var tcs = new UniTaskCompletionSource<bool>();
//                     OpenConfirmUI(mainTip,
//                         () =>
//                         {
//                             Application.OpenURL(shopURL);
//                             tcs.TrySetResult(false);
//                         },
//                         null,
//                         2,
//                         comfirmBtnTip
//                     );
//                     return await tcs.Task;
//                     // OpenConfirmUI(mainTip,
//                     //     () => Application.OpenURL(shopURL),//(resp.server), //todo0 跳转的商店app商店url
//                     //     null,
//                     //     2,
//                     //     comfirmBtnTip,
//                     //     null
//                     // ); ;
//                     // return false;
//                 }
//                 
//             }
//
//             #region process cdn
//             //解析defaultCDN, fallbackCDN尚未启用
//             string cdnUrl_prefix = "";//此cdn不携带平台标记（IOS ANDROID）
//             if (versionData.ContainsKey("defaultCDN"))
//             {
//                 cdnUrl_prefix = (string)versionData["defaultCDN"];
//             }
//
//             if (versionData[appVersion].ContainsKey("cdn"))
//             {
//                 cdnUrl_prefix = (string)versionData[appVersion]["cdn"];
//             }
//
//             if (string.IsNullOrEmpty(cdnUrl_prefix))
//             {
//                 var errorMsg = HotUpdateLangCfg.GetStr(HUKey.checkVersionErrTip, appVersion + " CDNErrInPrefix");
//                 OpenConfirmUI(errorMsg, null, null, 0);
//                 Debug.LogError(errorMsg);
//                 return false;
//             }
//
//      
//
//             string cdnUrl = cdnUrl_prefix + platformTag.ToUpper()+"/";  //特殊约定此处全大写 && 需要补文件链接符号
//             #endregion
//
//
//             //todo0 看看是否统一改为 version_hash, 目前这是个时间戳
//             string versionHash = (string)versionData[appVersion][platformTag.ToLower()+"_version_hash"];//特殊约定，此处全小写
//
//             Debug.Log("======================================");
//             Debug.Log($"======Hotupdate appVer:{appVersion}==hash:{versionHash } ");
//             Debug.Log($"======appSrv:{AppConst.LoginSrv} ");
//             Debug.Log($"======cdnBase:{cdnUrl} ");
//
//             string hotUpdateUrl = cdnUrl + versionHash;
//             AppConst.VersionHash = versionHash;//
//
//             Debug.Log("HotUpdateFlow cdn: " + hotUpdateUrl);
//             var initResult = await this.StartYooAssetsHotUpdate(hotUpdateUrl);
//             if (!initResult)
//             {
//                 var errorMsg = HotUpdateLangCfg.GetStr(HUKey.updateSysException, (int) HotUpdateCore.ErrorCode.YooAssets_Init);                
//                 OpenConfirmUI(errorMsg, null, null, 0);
//                 return false;
//             }
//  
//             var packageVersion = await this.UpdatePackageVersion();
//             if (string.IsNullOrEmpty(packageVersion))
//             {
//                 var errorMsg = HotUpdateLangCfg.GetStr(HUKey.updateSysException, (int) HotUpdateCore.ErrorCode.YooAssets_Update_Package_Version);  
//                 OpenConfirmUI(errorMsg, null, null, 0);
//                 return false;
//             }
//
//             var maifestResult = await this.UpdatePackageManifest(packageVersion);
//             if (!maifestResult)
//             {
//                 var errorMsg = HotUpdateLangCfg.GetStr(HUKey.updateSysException, (int) HotUpdateCore.ErrorCode.YooAssets_Update_Package_Manifest);  
//                 OpenConfirmUI(errorMsg, null, null, 0);
//                 return false;
//             }    
//
//             await this.UpdatePackageDownload();
//             return true;
//         }
//         catch (Exception e)
//         {
//             Debug.LogError($"HotUpdateFlow Exception.{e.Message}");
//             return false;
//         }
//     }
    #endregion

    //YooAsset初始化
    private void InitYooAssetsPackage()
    {
        if (!_init)
        {
            string packageName = YooAssetPackage;
            YooAssets.Initialize();
            var package = YooAssets.CreatePackage(packageName);
            YooAssets.SetDefaultPackage(package);
            _init = true;
        }

    }

    private async UniTask StartYooAssetsSimulateMode()
    {
        InitYooAssetsPackage();
        //
        string packageName = YooAssetPackage;
        var package = YooAssets.GetPackage(packageName);
        var initParameters = new EditorSimulateModeParameters();
        initParameters.SimulateManifestFilePath = EditorSimulateModeHelper.
            SimulateBuild(EDefaultBuildPipeline.BuiltinBuildPipeline, packageName);
        await package.InitializeAsync(initParameters).ToUniTask();
    }

    private async UniTask StartYooAssetsOfflineMode()
    {
        InitYooAssetsPackage();
        //
        string packageName = YooAssetPackage;
        var package = YooAssets.GetPackage(packageName);
        var initParameters = new OfflinePlayModeParameters();
        await package.InitializeAsync(initParameters).ToUniTask();
    }

    //YooAsset热更新流程
    private async UniTask<bool> StartYooAssetsHotUpdate(string cdnUrl)
    {
        InitYooAssetsPackage();
        //
        string packageName = YooAssetPackage;
        var package = YooAssets.GetPackage(packageName);

        var initParameters = new HostPlayModeParameters();
        initParameters.BuildinQueryServices = new GameQueryServices();
        RemoteServices = new HotUpdateCore.RemoteServices(cdnUrl, cdnUrl);
        initParameters.RemoteServices = RemoteServices;
        initParameters.DeliveryLoadServices = new DeliveryLoadServices();
        initParameters.DeliveryQueryServices = new DeliveryQueryServices();
        var initOperation = package.InitializeAsync(initParameters);
        await initOperation;
        
        string appVersion = package.GetPackageVersion();
        Debug.Log("当前资源包版本（App 版本）: " + appVersion);
        
        if (initOperation.Status == EOperationStatus.Succeed)
        {
            Debug.Log("资源包初始化成功！");
            return true;
        }
        else
        {
            Debug.LogError($"资源包初始化失败：{initOperation.Error}");
            return false;
        }
        return true;
    }

    private async UniTask<string> UpdatePackageVersion()
    {
        string packageName = YooAssetPackage;
        var package = YooAssets.GetPackage(packageName);
        var operation = package.UpdatePackageVersionAsync();
        await operation;
        //
        if (operation.Status == EOperationStatus.Succeed)
        {
            //更新成功
            string packageVersion = operation.PackageVersion;
            Debug.Log($"Updated package Version : {packageVersion}");
            return packageVersion;
        }
        else
        {
            //更新失败
            Debug.Log($"Updated package Version Error: {operation.Error}");
            return string.Empty;
        }
    }


    private async UniTask<bool> UpdatePackageManifest(string packageVersion)
    {
        string packageName = YooAssetPackage;
        var package = YooAssets.GetPackage(packageName);
        var operation = package.UpdatePackageManifestAsync(packageVersion, true);
        await operation;
        //
        if (operation.Status == EOperationStatus.Succeed)
        {
            Debug.Log($"Updated package Manifest Success");
            return true;
        }
        else
        {
            //更新失败
            Debug.Log($"Updated package Manifest Error. {operation.Error}");
            return false;
        }
    }

    //更新包下载
    private async UniTask<bool> ReadyPackageDownload(HotUpdateResult result)
    {
        int downloadingMaxNum = 10;
        int failedTryAgain = 3;
        var package = YooAssets.GetPackage(YooAssetPackage);
        var downloader = package.CreateResourceDownloader(downloadingMaxNum, failedTryAgain);

        result.downloader = downloader;
        return true;
    }
}

﻿using System.Collections.Generic;
using FairyGUI;
using Game.Modules.FragmentPractice;
using Game.Modules.Record;
using Msg.question;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using UnityEngine;

namespace UIBind.FragmentPractice
{
    public partial class HideShowQuestion : IRecordEventListener,IQuestionEventListener
    {
        TextFieldExtension TfQuestion => tfSentence as TextFieldExtension;

        protected override void OnAddedToStage()
        {
            TfQuestion.TextGtf.textFormat.lineSpacing = 28;

            // btnDisplay.SetKey("ui_fragment_display");
            btnDisplay.onClick.Add(OnClickDisplayStem);
            RecordEventManager.Instance.AddListener(this);
        }

        protected override void OnRemovedFromStage()
        {
            base.OnRemovedFromStage();
            RecordEventManager.Instance.RemoveListener(this);
            TTSManager.instance.StopTTS();//清空声音
        }

        public override void ShowPractice(AFragAnswer answerComp)
        {
            if (Practice.AudioId <= 0)
                VFDebug.LogError("Practice.AudioId 为:: " + Practice.AudioId);

            _isRecording = false;
            showStem.selectedIndex = 0;
            audio.selectedIndex = Practice.AudioId > 0 ? 1 : 0;

            TfQuestion.SetNewFont(FontCfg.DinNextRegular, ClearColor, 36);
            TfQuestion.Reset();
            TfQuestion.onTranslated += (word, translation) =>
            {
                DotPracticeManager.Instance.Collect(new DataDot_Translate(word, translation));
            };
            TfQuestion.StartNewWordTrans();
            List<RichContent> contents = RichContentUtil.SplitFragmentHideShowStem(Practice.GetStem());
            foreach (var richContent in contents)
                TfQuestion.AppendContent(richContent);
            TfQuestion.Display(ShowMode.FragmentHideShowQuestion);

            if (!IsCurrent) return;
            btnPlay.AudioId = Practice.AudioId;
            btnPlay.Play();
            btnPlay.onClick.Add(() =>
            {                
                RecordEventManager.Instance.DispatchRecordCancel();
            });
        }

        private void OnClickDisplayStem()
        {
            if (!IsCurrent) return;
            RecordEventManager.Instance.DispatchRecordCancel();

            showStem.selectedIndex = 1;
            TfQuestion.SetNewFont(FontCfg.DinNextRegular, BlackColor, 36);
            TfQuestion.Display(ShowMode.FragmentHideShowQuestion);

            btnPlay.Play();

            DotPracticeManager.Instance.Collect(new DataDot_DisplayText());
        }

        private bool _isRecording;//是否在录音状态中。 录音和显示文本互斥
        private int recordId;


        public bool IsReceivingEvents => IsCurrent;
        
        public void OnRecordStart(string rawString, int recordId)
        {
            this.recordId = recordId;
            btnPlay.Stop();
            _isRecording = true;
            showStem.selectedIndex = 0;
            TfQuestion.SetNewFont(FontCfg.DinNextRegular, ClearColor, 36);
            TfQuestion.Display(ShowMode.SpeakEval);
        }

        public void OnRecordStop()
        {
            this.recordId = -1;
            StopRecord(true);
        }

        public void OnRecordCancel()
        {
            StopRecord(false);
        }

        public void OnVad()
        {
            StopRecord(true);
        }

        public void OnCountDown()
        {
            // StopRecord(true);
        }

        public void OnMatchAll()
        {
            StopRecord(true);
            
        }

        public void OnTranscription(string transcribedText, int recordId)
        {            
            if (this.recordId != recordId) return;
            if (!_isRecording)
                StopRecord(true);
        }

        private void StopRecord(bool showText)
        {
            _isRecording = false;

            if (showText)
            {
                showStem.selectedIndex = 1;
                TfQuestion.SetNewFont(FontCfg.DinNextRegular, BlackColor, 36);
                List<MatchResult> result =
                    SpeechToTextManager.instance.MatchWordsInReference(PurpleColor, BlackColor);
                TfQuestion.SetEvaluateResult(result);
                TfQuestion.Display(ShowMode.SpeakEval);
            }
        }

        public void OnAnswered() { }

        public void OnSubmit() { }

        public void OnRetry()
        {
            if (showStem == null)
                return;
            showStem.selectedIndex = 0;
            TfQuestion.SetNewFont(FontCfg.DinNextRegular, ClearColor, 36);
            TfQuestion.Display(ShowMode.FragmentHideShowQuestion);
        }
        
        public void AutoCheck(){}
        public void OnReset(){}
        public void OnJumpListenTask() { }
        public void OnJumpSpeakTask() { }
    }
}
# FairyGUI GList 事件使用纠正

## 问题发现

原始代码中使用了以下事件，但这些事件在GList中并不存在：

```csharp
// ❌ 错误的事件使用
AddUIEvent(ui.selectList.onDragStart , OnSelectDragStartClick);
AddUIEvent(ui.selectList.onDragMove , OnSelectDragMoveClick);
AddUIEvent(ui.selectList.onDragEnd , OnSelectDragEndClick);
AddUIEvent(ui.selectList.onDrop , OnSelectDropClick);
```

## 源码分析结果

通过查看FairyGUI源码发现：

### 1. GList可用的事件
- `onClickItem`: 当列表项被点击时触发
- `onRightClickItem`: 当列表项被右键点击时触发

### 2. 拖拽相关事件的正确位置
- `onDragStart`, `onDragMove`, `onDragEnd` 是 **GObject** 的基础事件
- 这些事件存在于每个GObject实例上，但不是GList容器级别的事件

### 3. 滚动相关事件的正确位置
- 滚动相关的事件在 **ScrollPane** 类中：
  - `onScroll`: 滚动过程中持续触发
  - `onScrollEnd`: 滚动结束时触发
  - `onPullDownRelease`: 下拉释放时触发
  - `onPullUpRelease`: 上拉释放时触发

## 正确的解决方案

### ✅ 正确的事件注册方式

```csharp
protected override void OnInit(GComponent uiCom)
{
    base.OnInit(uiCom);

    // 注册列表项点击事件
    AddUIEvent(ui.selectList.onClickItem, OnSelectListClick);
    
    // 注册滚动相关事件 - 使用ScrollPane的事件
    if (ui.selectList.scrollPane != null)
    {
        AddUIEvent(ui.selectList.scrollPane.onScroll, OnSelectListScroll);
        AddUIEvent(ui.selectList.scrollPane.onScrollEnd, OnSelectListScrollEnd);
    }
}
```

### ✅ 正确的事件处理方法

```csharp
/// <summary>
/// 列表item点击事件
/// </summary>
private void OnSelectListClick(EventContext context)
{
    if (_isAnimating) return;
    
    // onClickItem事件的data参数就是被点击的item对象
    GObject clickedItem = context.data as GObject;
    if (clickedItem?.data != null)
    {
        int clickedIndex = (int)clickedItem.data;
        MoveToCenter(clickedIndex);
    }
}

/// <summary>
/// 列表滚动事件 - 滚动过程中持续触发
/// </summary>
private void OnSelectListScroll()
{
    if (!_isDragging)
    {
        _isDragging = true;
    }
    
    // 在滚动过程中实时更新item显示效果
    UpdateItemsDisplay();
}

/// <summary>
/// 列表滚动结束事件 - 滚动停止时触发
/// </summary>
private void OnSelectListScrollEnd()
{
    _isDragging = false;
    
    // 滚动结束后，吸附到最近的中心位置
    SnapToCenter();
}
```

## 事件参数说明

### onClickItem 事件
- **context.data**: 被点击的GObject item
- **context.sender**: 触发事件的对象（通常是GList）

### onScroll 事件
- 无特殊参数，通过ScrollPane的属性获取滚动信息

### onScrollEnd 事件
- 无特殊参数，表示滚动动画已结束

## 关键学习点

1. **事件层级理解**: 不同类型的事件属于不同的类
   - 容器级事件：GList.onClickItem
   - 滚动级事件：ScrollPane.onScroll
   - 对象级事件：GObject.onDragStart

2. **事件参数理解**: 
   - context.data 通常包含事件相关的数据对象
   - context.sender 是触发事件的对象

3. **源码查看的重要性**: 
   - 直接查看源码确认可用的事件和参数
   - 避免基于假设编写代码

## 最终实现效果

修正后的代码能够正确实现：
- ✅ 点击item移动到中心
- ✅ 滚动过程中的弧形显示更新
- ✅ 滚动结束后的自动吸附
- ✅ 平滑的动画过渡效果

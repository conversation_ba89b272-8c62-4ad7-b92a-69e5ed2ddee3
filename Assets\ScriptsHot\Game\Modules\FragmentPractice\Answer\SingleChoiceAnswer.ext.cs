using System.Collections.Generic;
using FairyGUI;
using UnityEngine;
using Game.Modules.FragmentPractice;
using Msg.question;
using ScriptsHot.Game.Modules.FragmentPractice;
using System;
using Cysharp.Threading.Tasks;
using System.Linq;

namespace UIBind.FragmentPractice
{
    public partial class SingleChoiceAnswer : AFragAnswer, IQuestionEventListener
    {        
        private int selectedAnswerIndex = -1;
        private bool selectRight = false;
        private bool isFirst = true;
        (string, long)[] options;  // 选项内容和音频id
        
        override protected void OnAddedToStage()
        {
            listAnswers.itemRenderer = OnRendererAnswer;
        }
         override protected void OnRemovedFromStage()
        {
            QuestionEventManager.Instance.RemoveListener(this);
        }

        public override void Init(bool isCurrent, APracticeData practice)
        {
            base.Init(isCurrent, practice);
            if (isCurrent)
            {
                listAnswers.onClickItem.Add(OnClickAnswer);
                QuestionEventManager.Instance.AddListener(this);
            }
        }

        override public void ShowPractice(AFragQuestion questionComp)
        {
            isFirst = true;
            // 新题开始
            selectedAnswerIndex = -1;

            if (Practice.QuestionType == PB_QuickPracticeType.Explanation || Practice.QuestionType == PB_QuickPracticeType.Reading)
            {
                tfQuest.text = Practice.GetQuestion();
                title.selectedIndex = 1;
            }
            else
            {
                title.selectedIndex = 0;
            }

            var answerOptions = Practice.GetAnswerOptions();
            var answerOptionTts = Practice.GetAnswerOptionTts();
            options = answerOptions.Select((option, index) =>
                (option, index < answerOptionTts.Length ? answerOptionTts[index] : 0L)).ToArray();

            if (options.Length > 2)
            {
                // 大于2才打乱，因为2个的都是 yes/no，保持顺序
                System.Random rng = new System.Random();
                // 高德纳洗牌
                for (int i = options.Length - 1; i > 0; i--)
                {
                    int j = UnityEngine.Random.Range(0, i + 1);
                    (options[i], options[j]) = (options[j], options[i]);
                }
            }

            listAnswers.touchable = true;
            listAnswers.numItems = Practice.GetAnswerOptions().Length;
            
            for (int i = 0; i < listAnswers.numItems; i++)
            {
                BtnBaseAnswer option = listAnswers.GetChildAt(i) as BtnBaseAnswer;
                option.SetAutoSize(true);
            }
            listAnswers.ResizeToFit();
        }

        override public void FitToPreferedHeight()
        {
            height = listAnswers.height;
        }


        #region 检查答案        
        private void OnClickAnswer(EventContext context)
        {
            if (!IsCurrent || Practice == null) return;
            if (selectRight) return;
            
            if (context.sender is not GComponent comp) return;

            BtnBaseAnswer selectedBtn = context.data as BtnBaseAnswer;
            if (selectedBtn?.TtsId > 0)
            {
                TTSManager.instance.StopTTS();
                TTSManager.instance.PlayTTS(selectedBtn.TtsId);
            }
            int index = comp.GetChildIndex(selectedBtn);
            if (index == selectedAnswerIndex) return;

            selectedAnswerIndex = index;

            if (ForceCorrect)
            {
                CorrectSelection(selectedBtn);
            }
            else
            {
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);
                
                // -- 鑫淼 之前这段代码 写在了 AudioSelectAnswer 中 ，后来被移动到这里
                if (display == BtnBaseAnswer.BtnDisPlay.select) //普通单选
                {
                    for (int i = 0; i < listAnswers.numItems; i++)
                    {
                        BtnBaseAnswer option = listAnswers.GetChildAt(i) as BtnBaseAnswer;
                        if (option == null) return;
                        if (selectedAnswerIndex != i)
                            option.SetState(BtnBaseAnswer.State.Normal);
                        else
                            option.SetState(BtnBaseAnswer.State.Select);
                    }
                    CommitAnswer(options[selectedAnswerIndex].Item1 == Practice.CorrectAnswerContent ? 1f : 0f);
                }
                else//选错会被锁住 - 耳机
                {
                    var obj = context.data as GObject;
                    if (obj == null) return;
                    BtnBaseAnswer option = obj.asCom as BtnBaseAnswer;
                    if (option == null) return;
                    var bl = options[selectedAnswerIndex].Item1 == Practice.CorrectAnswerContent;
                    selectRight = bl;
                    if (bl)
                    {
                        TimerManager.instance.RegisterTimer((t) =>
                        {
                            CommitAnswer(bl ? 1f : 0f,true,true);
                        },2000);
                        SoundManger.instance.PlayUI("question_right");
                        option.SetState(BtnBaseAnswer.State.Right);
                        option.ShowChoiceEffect();
                    }
                    else
                    {
                        if (isFirst)
                        {
                            isFirst = false;
                            CommitAnswer(0f);
                        }
                        OnErrorAnswer();
                        option.SetState(BtnBaseAnswer.State.Error);
                        option.touchable = false;
                        TimerManager.instance.RegisterTimer((T) =>
                        {
                            option.SetState(BtnBaseAnswer.State.Gray);
                        }, 1000);
                    }
                }
            }

        }

        private async void CorrectSelection(BtnBaseAnswer selectedBtn)
        {
            touchable = false;
            selectedBtn.touchable = false;
            bool isCorrect = options[selectedAnswerIndex].Item1 == Practice.CorrectAnswerContent;
            selectedBtn.SetState(isCorrect ? BtnBaseAnswer.State.Right : BtnBaseAnswer.State.Error);

            if (!isCorrect)
            {
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium); //正确不震动 因为commit后有 就不震了
                // 错误，半秒钟之后变为灰色
                OnErrorAnswer();
                await UniTask.Delay(TimeSpan.FromSeconds(0.1f));
                selectedBtn.SetState(BtnBaseAnswer.State.Gray);
                touchable = true;
            }else
            {
                // 正确，半秒钟之后变为绿色
                await UniTask.Delay(TimeSpan.FromSeconds(0.1f));
                selectedBtn.SetState(BtnBaseAnswer.State.Right);
                selectedBtn.ShowChoiceEffect();
                touchable = true;
                
                CommitAnswer(1f, true, true);
            }
        }

        private void OnRendererAnswer(int index, GObject obj)
        {
            BtnBaseAnswer option = obj.asCom as BtnBaseAnswer;
            if (option == null) return;
            option.UnEnable(); //自己播音效
            
            option.SetMode(options[index].Item1, options[index].Item2, true, false);
            option.SetState(BtnBaseAnswer.State.Normal);
        }
        #endregion

        #region IQuestionEventListener
        public void OnAnswered(){}

        public void OnSubmit()
        {
            listAnswers.touchable = false;
            if (display != BtnBaseAnswer.BtnDisPlay.select) return;//耳机关已经设置过状态了 不用管了
            for (int i = 0; i < listAnswers.numItems; i++)
            {
                BtnBaseAnswer option = listAnswers.GetChildAt(i) as BtnBaseAnswer;
                if (option == null) return;
                bool isCorrect = options[selectedAnswerIndex].Item1 == Practice.CorrectAnswerContent;
                if (i == selectedAnswerIndex)
                {
                    if (isCorrect) option.ShowChoiceEffect();
                    option.SetState(isCorrect ? BtnBaseAnswer.State.Right : BtnBaseAnswer.State.Error);
                }
                else
                    option.SetState(BtnBaseAnswer.State.Normal);
            }
        }
        
        public void OnRetry()
        {
            
        }
        
        public void AutoCheck(){}
        public void OnReset(){}

        public void OnJumpListenTask()
        {
            listAnswers.touchable = false;
        }

        public void OnJumpSpeakTask()
        {
            listAnswers.touchable = false;
        }

        #endregion
    }
}
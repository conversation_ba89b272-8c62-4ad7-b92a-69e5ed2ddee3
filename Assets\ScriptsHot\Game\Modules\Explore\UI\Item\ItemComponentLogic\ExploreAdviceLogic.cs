﻿
using FairyGUI;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Explore;
using UnityEngine;

namespace UIBind.Explore.Item.ItemComponentLogic
{
    /// <summary>
    /// advice
    /// </summary>
    public class ExploreAdviceLogic:ItemComponentLogicBase
    {
        public ExploreAdviceItem Com;
        
        public TextFieldExtension TfOrigin =>  Com.tfOrigin as TextFieldExtension;
        
        private string _bubbleId = string.Empty;
        
        private ExploreItemUI _item;
        
        private string _str;
        
        private long _dialogId;
        private long _round;
        private bool _effecting;

        public override void Init()
        {
            base.Init();
            Com.txtName.SetKey("ui_explore_title_advice");
            TfOrigin.SetFormat(Color.white, 32);
        }
        public void SetInfo(long dialogId,long round)
        {
            _dialogId = dialogId;
            _round = round;
        }
        
        public void SetParentItem(ExploreItemUI item)
        {
            _item = item;
        }
        
        public void ShowTxt(string bubbleId,string str)
        {
            _str = str;
             // Debug.LogError("ShowTxt bubbleId:" + str);
            _bubbleId = bubbleId;
            
            ExploreNewWordData newWordData = new ExploreNewWordData();
            newWordData.ui = _ui;
            newWordData.avatarId = 1;
            newWordData.dialogId = _controller.CurTaskId;
            newWordData.bubbleId = bubbleId;
            _translateWordComponent.SetData(newWordData);

            var textContent = new RichContent()
            {
                Content = _str,
                IsLast = true,
            };
            TfOrigin.Reset();
            TfOrigin.OnClickNewWord += _translateWordComponent.ReqNewWordTrans;
            TfOrigin.AppendContent(textContent).Display(ShowMode.Normal);
        }

        public void Show(string txt,long taskId,long dialogue_id,long roundId)
        {
            // APPEAR_EXPLORE_EXAMPLE dotData = new APPEAR_EXPLORE_EXAMPLE();
            // dotData.example_text = txt;
            // dotData.task_id = taskId;
            // if(dialogue_id > 0)
            //     dotData.dialogue_id = dialogue_id.ToString();
            // dotData.dialogue_round = roundId;
            // DataDotMgr.Collect(dotData);
            
            _controller.ShowBulbActive();
        }
        
        public void SetEffectVisible(bool visibleValue,bool isClick)
        {
            if (!isClick)
            {
                Com.com.alpha = 1;
                Com.com.visible = visibleValue;
                return;
            }
            
            _effecting = true;
            int fadeValue = visibleValue ? 1 : 0;
            Com.com.alpha = visibleValue ? 0 : 1;
            Com.com.visible = true;
            GTween.Kill(Com.com);
            Com.com.TweenFade(fadeValue, ExploreConst.CellAphla).OnComplete(() =>
            {
                _effecting = false;
                Com.com.visible = visibleValue;
            });
        }
    }
}
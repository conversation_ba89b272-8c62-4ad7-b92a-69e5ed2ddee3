using System.Collections.Generic;
using Luban;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.SceneManagement;
using YooAsset;

namespace AnimationSystem
{
    public class Util : MonoBehaviour
{
    public static void PrintPlayableGraph(PlayableGraph graph)
    {
        int rootPlayableCount = graph.GetRootPlayableCount();
        Debug.Log($"Root Playables: {rootPlayableCount}");

        for (int i = 0; i < rootPlayableCount; i++)
        {
            Playable rootPlayable = graph.GetRootPlayable(i);
            Debug.Log($"Root Playable {i}: {rootPlayable.GetPlayableType()}");
            TraversePlayable(rootPlayable, 0);
        }
    }
    
    static void TraversePlayable(Playable playable, int depth)
    {
        if (!playable.IsValid())
            return;

        string indent = new string('-', depth * 2);
        Debug.Log($"{indent}> Playable: {playable.GetPlayableType()}, Inputs: {playable.GetInputCount()}");

        for (int i = 0; i < playable.GetInputCount(); i++)
        {
            Playable input = playable.GetInput(i);
            if (input.IsValid())
            {
                Debug.Log($"{indent}  |- Input {i}: {input.GetPlayableType()} (Weight: {playable.GetInputWeight(i)})");
                TraversePlayable(input, depth + 1);
            }
        }
    }
    
    public static List<Transform> GetAllHumanBoneTransforms(Animator animator)
    {
        List<Transform> boneTransforms = new List<Transform>();

        if (animator == null || !animator.isHuman)
        {
            VFDebug.LogWarning("Animator is null or not humanoid.");
            return boneTransforms;
        }

        foreach (HumanBodyBones bone in System.Enum.GetValues(typeof(HumanBodyBones)))
        {
            if (bone == HumanBodyBones.LastBone) continue; // 排除无效的枚举值
            Transform boneTransform = animator.GetBoneTransform(bone);
            if (boneTransform != null)
            {
                boneTransforms.Add(boneTransform);
            }
        }

        return boneTransforms;
    }

    /// <summary>
    /// 返回当前是不是DEMO
    /// </summary>
    /// <returns></returns>
    public static bool CheckDEMO()
    {
        //demo场景
        if (SceneManager.GetActiveScene().name == "AnimationSystem")
        {
            return true;
        }
        else return false;
    }

    #region 读表逻辑
    
    //給定一個id,返回讀表數據
    //ID暂时是string.
    public static AnimGroup LoadAnimGroup(string id,int animTypeId)
    {
        AnimGroup animGroup = new AnimGroup();
        
        int bodyGroupIndex = 0;
        Cfg.T.TBAvatarActionSet.DataMap.TryGetValue(id, out var val);
        if (val == null)
        {
            VFDebug.LogWarning($"AvatarAction {id} not found.");
            return null;
        }
        
        //body.
        var body = val.bodyActionKey;
        Cfg.T.TBAvatarBodyAction.DataMap.TryGetValue(body, out var bodyAnimList);
        if (bodyAnimList == null)
        {
            //body必须有
            VFDebug.LogWarning($"BodyAnimList {id} not found.");
            return null;
        }
        List<AnimInfo> animInfos = new List<AnimInfo>();
        List<string> animTags = bodyAnimList.animInfo;
        for (int i = 0; i < animTags.Count; i++ )
        {
            Cfg.T.TBAvatarBodyClip.DataMap.TryGetValue(animTags[i], out var animInfoExcelData);
            if (animInfoExcelData != null)
            {
                AnimInfo animInfo = new AnimInfo();
                animInfo.animName = animInfoExcelData.clipName;

                var clip = ClipResourceManager.Instance.GetClip(animInfo.animName,animTypeId);
                if (!clip)
                {
                    VFDebug.LogWarning("clip not found.");
                    return null;
                }
                
                animInfo.clip = clip;
                
                animInfo.canBlend = animInfoExcelData.canBlend;
                animInfo.blendRange = new Vector2(animInfoExcelData.blendIn, animInfoExcelData.blendOut);
                animInfo.canLoop = animInfoExcelData.canLoop;
                animInfo.loopMultiplierRange = new Vector2(1, animInfoExcelData.loopMax);
                animInfo.loopBlendTime = animInfoExcelData.loopBlendTime;
                animInfo.enableFootIK = animInfoExcelData.footIK;
                animInfo.canChangeSpeed = animInfoExcelData.canChangeSpeed;
                animInfo.speedMultiplierRange =
                    new Vector2(animInfoExcelData.speedRangeMin, animInfoExcelData.speedRangeMax);
                animInfos.Add(animInfo);
            }
            else
            {
                VFDebug.LogWarning($"AnimInfo {id} load failed.");
            }
        }
        for (int i = 0; i < animInfos.Count; i++)
        {
            animGroup.AddAnimInfo(animInfos[i]);
        }
        
        //head.
     //   var head = val.headActionKey;
     //   Cfg.T.TBAvatarHeadAction.DataMap.TryGetValue(head, out var headAnimList);
     
        //final.init
        animGroup.bodyGroupID = body;
        animGroup.Init();
        
        return animGroup;
    }

    //返回一个AnimInfo
    public static AnimInfo LoadAnimInfo(string id)
    {
        Cfg.T.TBAvatarBodyClip.DataMap.TryGetValue(id, out var animInfoExcelData);
        if (animInfoExcelData != null)
        {
            AnimInfo animInfo = new AnimInfo();
            animInfo.animName = animInfoExcelData.clipName;
            var handle = GAvatarResManager.instance.SyncLoad(animInfo.animName);
            if (handle != null && handle.AssetObject != null)
            {
                AnimationClip clip = handle.AssetObject as AnimationClip;
                animInfo.clip = clip;
            }

            animInfo.canBlend = animInfoExcelData.canBlend;
            animInfo.blendRange = new Vector2(animInfoExcelData.blendIn, animInfoExcelData.blendOut);
            animInfo.canLoop = animInfoExcelData.canLoop;
            animInfo.loopMultiplierRange = new Vector2(1, animInfoExcelData.loopMax);
            animInfo.loopBlendTime = animInfoExcelData.loopBlendTime;
            animInfo.enableFootIK = animInfoExcelData.footIK;
            animInfo.canChangeSpeed = animInfoExcelData.canChangeSpeed;
            animInfo.speedMultiplierRange =
                new Vector2(animInfoExcelData.speedRangeMin, animInfoExcelData.speedRangeMax);
            return animInfo;
        }
        else return null;
    }

    //给定一个通用表id，返回一个List<string>(动作集合id)
    public static List<string> LoadAnimGenericTags(string id)
    {
        Cfg.T.TBAvatarDialogGenericDefine.DataMap.TryGetValue(id, out var value);
        if (value != null)
        {
            return value.actionSetList;
        }
        else return new List<string>();
    }
    
    #endregion
}
    
    /**
     * TODO 仅在目前DEMO场景中使用。非DEMO场景后续请删除此类。
     */
    public class Cfg
    {

        public static Tables t;
        
        public static Tables T
        {
            get
            {
                if (Util.CheckDEMO())
                {
                    return t;
                }
                else return global::Cfg.T;
            }
            private set => t = value;
        }
    
        private static Cfg _instance = null;

        public static Cfg instance
        {
            get
            {
                if (_instance == null)
                    _instance = new Cfg();
                return _instance;
            }
        }

        public void LoadAllCfg()
        {
            var tables = new Tables(LoadByteBuf);
            Cfg.T = tables;
        }

        private static ByteBuf LoadByteBuf(string file)
        {
            byte[] bytes = LoadBinConfig(GetBinConfigPath(file));
            return new ByteBuf(bytes);
        }
    
        public static byte[] LoadBinConfig(string path)
        {
            var handle = YooAssets.LoadAssetSync<TextAsset>(path);
            var textAsset = handle.AssetObject as TextAsset;
            byte[] bytes = textAsset.bytes;
            handle.Release();
            return bytes;
        }
    
        public static string GetBinConfigPath(string name)
        {
            return $"Assets/Build/Config/{name}.bytes";
        }
    }
}


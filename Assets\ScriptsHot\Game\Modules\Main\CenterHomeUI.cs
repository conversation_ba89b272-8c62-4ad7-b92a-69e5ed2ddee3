using UIBind.Main;
using UnityEngine;
using DG.Tweening;
using FairyGUI;
using ScriptsHot.Game.Modules.Sign;
using ScriptsHot.Game.Modules.Contacts;
using ScriptsHot.Game.Modules.IncentiveTask;
using System;
using System.Collections.Generic;
using Msg.basic;
using ScriptsHot.Game.Modules.DrillHub;
using ScriptsHot.Game.UGUI.WebView;
using Game.Core.RedDot;
using Game.Modules.WhatsApp;
using LitJson;
using Modules.DataDot;
using Msg.social;
using ScriptsHot.Game.Modules.Shop;
using ScriptsHot.Game.Modules.VideoLessonTask;
using UIBind.MainPath;

public struct ChangeChatAvatarParam
{
    public long avatarID;
    public long taskID;
    public PB_DialogMode mode;
    public string bgTag;//实际充当了背景图名称
}

/*
 * 核心1 仅关心两侧siderBar 以及下方 原enterUI以及 practiceBtn的逻辑
 * 核心2 不要和
 * 
 */

public class CenterHomeUI: BaseUI<HomepagePanel>, IBaseUIUpdate
{
    public override void OnBackBtnClick()
    {
    }

    public override string uiLayer => UILayerConsts.HomePage;//主UI层

    protected override bool isFullScreen => true;
    // private LearnPathModel learnPathModel = ModelManager.instance.GetModel<LearnPathModel>(ModelConsts.LearnPath);
    private MainModel mainModel => GetModel<MainModel>(ModelConsts.Main);
    // private LearnPathController learnPathController = ControllerManager.instance.GetController<LearnPathController>(ModelConsts.LearnPath);

    private MainController _mainController => GetController<MainController>(ModelConsts.Main);
    private ShopModel _shopModel => GetModel<ShopModel>(ModelConsts.Shop);
    private HomepageController _homepageController => GetController<HomepageController>(ModelConsts.Homepage);
    private long _lastUpdateStaminaTime;
    private long _lastUpdateTime;

    private MainPathPanel _mainPath;

    // 新增：缓存左右列表item的字典
    private Dictionary<HomepageEntranceTypes, GComponent> _leftButtonDict = new System.Collections.Generic.Dictionary<HomepageEntranceTypes, GComponent>();
    private Dictionary<HomepageEntranceTypes, GComponent> _rightButtonDict = new System.Collections.Generic.Dictionary<HomepageEntranceTypes, GComponent>();

    public CenterHomeUI(string name) : base(name) { }

    public CenterHomeUI(string name, GObject obj) : base(name)
    {
        this.ui = new HomepagePanel();
        this.uiCom =  obj.asCom;
        Debug.Log("comName=" + this.uiCom.name + ",goName=" + this.uiCom.gameObjectName);
        this.ui.Construct(this.uiCom);
    }

    private const float MainListShowDuration = 0.25f;
    private const float MainListShowInterval = 0.067f;
    private const float MainListShowDeltaX = 150f;
    private const Ease MainListShowEase = Ease.OutBack;

    public bool IsFeatureButtonListVisible { get; private set; } = false;
    protected override void OnInit(GComponent uiCom)
    {
        ui.headBar.OnInit();
        ui.homepageBanner.OnInit();
        _mainPath = new MainPathPanel();
        _mainPath.Construct(ui.mainPath);
        _mainPath.OnInit(this);

        RebuildMainButtons();
        FixFont();

        AddUIEvent(ui.homepageTimeBanner.btnEnter.onClick, OnClickPayWallEnter);
        AddUIEvent(ui.homepageTimeBanner.btnHide.onClick, OnClickPayWallHide);
        AddUIEvent(ui.homepageTimeBanner.btnShow.onClick, OnClickPayWallShow);
    }

    protected override void OnShow()
    {
        base.OnShow();
        _mainPath.OnShow(ui.btnToTop);
        
        //header部分
        this.ui.headBar.OnShow();
        this.ui.headBar.Refresh();

        _mainController.RequsetUnReadMessageNum();
        
        //Todo0
        _ = GetController<CurrencyController>(ModelConsts.CurrencyController)
            .SendGetEconomicInfoReqAsync(GameEventName.GameEnter, ui.headBar.RefreshCurrency);

        RedDotManager.Instance.LoadAllDots();
        
        DotAppearHomePageChatIcon dot = new DotAppearHomePageChatIcon();
        DataDotMgr.Collect(dot);
    }

    private void FixFont() {
        // 修改FairyGUI Streak类型字体. 
        var textField = _mainPath.mainListLeft.GetChildAt(0).asCom.GetChild("title").asTextField;
        var textFormat = textField.textFormat;
        textFormat.font = FontCfg.DinNextBold;// "D-DIN-Bold";
        textField.textFormat = textFormat;
    }

    public void RefreshHeadBar() {
        this.ui.headBar.Refresh();
    }
    private void RebuildMainButtons()
    {
        //Left List
        _mainPath.mainListLeft.RemoveChildrenToPool();

        //tab页改造:世界和rank消失了
        //AddSideListButtonI18N(SideListPositions.Left, HomepageEntranceTypes.Leaderboard, "ui_main_ranks_btn", OnBtnLeaderboardClicked);
        //AddSideListButtonI18N(SideListPositions.Left, HomepageEntranceTypes.WorldMap, "ui_main_world_btn", OnBtnWorldMapClicked);

        //新首页改课程页，隐藏了
        //AddSideListButton(SideListPositions.Left, HomepageEntranceTypes.Streaks, "0", OnBtnStreaksClicked);//Will be refreshed separately
        //AddSideListButtonI18N(SideListPositions.Left, HomepageEntranceTypes.DIY, "ui_main_diy_btn", OnBtnDIYClicked);


        if (!AppConst.IsCN)
        {
            //20250701 由于cn用户调研需要消化 暂停海外的survey
            // AddSideListButtonI18N(SideListPositions.Left, HomepageEntranceTypes.SURVEY, "ui_main_survey_btn", OnBtnSurveyClicked);
            // _mainPath.mainListLeft.ResizeToFit();
        }

        //Right List
        _mainPath.mainListRight.RemoveChildrenToPool();
        _leftButtonDict.Clear();
        _rightButtonDict.Clear();
        AddSideListButtonI18N(SideListPositions.Right, HomepageEntranceTypes.Quest, "ui_main_quests_btn", OnBtnQuestClicked);
        _mainPath.mainListRight.ResizeToFit();
        // 新首页改课程页，隐藏了
        //AddSideListButtonI18N(SideListPositions.Right, HomepageEntranceTypes.Gallery, "ui_story_Libray_btn", OnBtnGalleryClicked);
        //AddSideListButtonI18N(SideListPositions.Right, HomepageEntranceTypes.Contacts, "ui_main_contacts_btn", OnBtnContactsClicked);
        //AddSideListButtonI18N(SideListPositions.Right, HomepageEntranceTypes.DrillHub, "drill_hub_title", OnBtnDrillHubClicked);
        AddSideListButtonI18N(SideListPositions.Left, HomepageEntranceTypes.Msg, "ui_socialchat_messages", OnBtnMsgClicked);

        if (GetController<WhatsappController>(ModelConsts.Whatsapp).ShowMainWhatsappBtn)
        {
            AddSideListButtonI18N(SideListPositions.Left, HomepageEntranceTypes.Whatsapp, "ui_home_WhatsApp_icon", OnBtnWhatsappClicked);
        }

        _mainController.TryGetCacheLaunchOptions();
    }

    public void ShowFeatureButtonList(float delay, bool useAnimation = true, bool ignoreVisibleCheck = false)
    {
        ShowFeatureButtonList(delay, MainListShowInterval, MainListShowDuration, MainListShowDeltaX, MainListShowEase, useAnimation, ignoreVisibleCheck);
    }

    public void ShowFeatureButtonList(float startDelay = 0f, float interval = MainListShowInterval, float duration = MainListShowDuration, float deltaX = MainListShowDeltaX, Ease ease = MainListShowEase, bool useAnimation = true, bool ignoreVisibleCheck = false)
    {
        if(IsFeatureButtonListVisible && !ignoreVisibleCheck) return;
        if(useAnimation)
        {
            Debug.LogError("useAnim");
            //Left list
            for(int i = 0; i < _mainPath.mainListLeft.numItems; i++)
            {
                var item = _mainPath.mainListLeft.GetChildAt(i).asCom;
                item.alpha = 0.25f;
                item.x -= deltaX;
                DOTween.To(() => item.alpha, x => item.alpha = x, 1f, duration).SetDelay(startDelay + i * interval);
                DOTween.To(() => item.x, x => item.x = x, 0f, duration).SetDelay(startDelay + i * interval).SetEase(ease)
                .OnComplete(()=> item.touchable = true);
            }
            //Right list
            for(int i = 0; i < _mainPath.mainListRight.numItems; i++)
            {
                var item = _mainPath.mainListRight.GetChildAt(i).asCom;
                item.alpha = 0.25f;
                item.x += deltaX;
                DOTween.To(() => item.alpha, x => item.alpha = x, 1f, duration).SetDelay(startDelay + i * interval);
                DOTween.To(() => item.x, x => item.x = x, 0f, duration).SetDelay(startDelay + i * interval).SetEase(ease)
                .OnComplete(()=> item.touchable = true);
            }
        }
        else
        {
            //Left list
            for(int i = 0; i < _mainPath.mainListLeft.numItems; i++)
            {
                var item = _mainPath.mainListLeft.GetChildAt(i).asCom;
                item.touchable = true;
            }
            //Right list
            _mainPath.mainListRight.visible = true;
            for(int i = 0; i < _mainPath.mainListRight.numItems; i++)
            {
                var item = _mainPath.mainListRight.GetChildAt(i).asCom;
                item.touchable = true;
            }
        }   
        IsFeatureButtonListVisible = true;
    }
    public void HideFeatureButtonList(float delay = 0f, bool useAnimation = true)
    {
        HideFeatureButtonList(delay, MainListShowInterval, MainListShowDuration, MainListShowDeltaX, MainListShowEase, useAnimation);
    }
    public void HideFeatureButtonList(float startDelay, float interval = MainListShowInterval, float duration = MainListShowDuration, float deltaX = MainListShowDeltaX, Ease ease = MainListShowEase, bool useAnimation = true)
    {
        if(!IsFeatureButtonListVisible) return;
        if(useAnimation)
        {
            //Left list
            for(int i = 0; i < _mainPath.mainListLeft.numItems; i++)
            {
                var item = _mainPath.mainListLeft.GetChildAt(i).asCom;
                item.alpha = 1f;
                item.x = 0f;
                DOTween.To(() => item.alpha, x => item.alpha = x, 0f, duration).SetDelay(startDelay + i * interval);
                DOTween.To(() => item.x, x => item.x = x, -deltaX, duration).SetDelay(startDelay + i * interval).SetEase(ease)
                .OnComplete(()=> item.touchable = false);
            }
            //Right list
            for(int i = 0; i < _mainPath.mainListRight.numItems; i++)
            {
                var item = _mainPath.mainListRight.GetChildAt(i).asCom;
                item.alpha = 1f;
                item.x = 0f;
                DOTween.To(() => item.alpha, x => item.alpha = x, 0f, duration).SetDelay(startDelay + i * interval);
                DOTween.To(() => item.x, x => item.x = x, deltaX, duration).SetDelay(startDelay + i * interval).SetEase(ease)
                .OnComplete(()=> item.touchable = false);
            }
        }
        else
        {
            for(int i = 0; i < _mainPath.mainListLeft.numItems; i++)
            {
                var item = _mainPath.mainListLeft.GetChildAt(i).asCom;
                item.touchable = false;
            }   
            for(int i = 0; i < _mainPath.mainListRight.numItems; i++)
            {
                var item = _mainPath.mainListRight.GetChildAt(i).asCom;
                item.touchable = false;
            }
        }

        IsFeatureButtonListVisible = false;
    }

    public void Update(int interval)
    {
        if (!this.isShow) return;
        // 每个小时刷新一次
        var lastUpdateDate = DateTimeOffset.FromUnixTimeSeconds(_lastUpdateStaminaTime / 1000).DateTime;
        if (TimeExt.serverTime.Hour != lastUpdateDate.Hour)
        {
            if (_lastUpdateStaminaTime > 0)
            {
                VFDebug.Log("OnReqGetIncentiveData");
                _homepageController.ReqGetIncentiveData();
            }
            _lastUpdateStaminaTime = TimeExt.serverTimestamp;
        }

        if (TimeExt.serverTimestamp - _lastUpdateTime > 200f)
        {
            if (mainModel.incentiveData == null)
            {
                // 没有incentiveData会产生大问题
                VFDebug.Log("启动进入首页后竟然没有incentiveData？重新请求");
                _homepageController.ReqGetIncentiveData();
            }
            else
            {
                ui.headBar.UpdateStaminaItem();   
            }
            _lastUpdateTime = TimeExt.serverTimestamp;
        }

        long endTimeStamp = _shopModel.PayWallData?.end_milliseconds_in_utc ?? 0;
        if (endTimeStamp > TimeExt.serverTimestamp)
        {
            long curTimeStamp = TimeExt.serverTimestamp;
            long leftSeconds = (endTimeStamp - curTimeStamp) / 1000;
            if (leftSeconds < 0) leftSeconds = 0;
            int minutes = (int)(leftSeconds / 60);
            int seconds = (int)(leftSeconds % 60);
            ui.homepageTimeBanner.tfTime.text = $"{minutes:D2}:{seconds:D2}";
            ui.homepageTimeBanner.tfTimeSmall.text = ui.homepageTimeBanner.tfTime.text;
        }
        else
        {
            ui.homepageTimeBanner.com.visible = false;
        }
    }


    public void UpdateStreakBtnValue(string streakValue)
    {
        var item = GetMainButtonWithType(HomepageEntranceTypes.Streaks);
        if (item != null)
        {
            item.asButton.title = streakValue.ToString();
        }
    }

    public void SetStreakBtnState(bool isCompleted)
    {
        var item = GetMainButtonWithType(HomepageEntranceTypes.Streaks);
        if (item != null)
        {
            var fire = item.asCom.GetChild("fireNew");
            (fire as GLoader3D).animationName = isCompleted ? "2" : "1";
        }
    }

    private void AddSideListButtonI18N(SideListPositions position, HomepageEntranceTypes type, string titleKey, FairyGUI.EventCallback0 onClick)
    {
        AddSideListButton(position, type, "", onClick).SetKey(titleKey);
    }

    private GObject AddSideListButton(SideListPositions position, HomepageEntranceTypes type, string title, FairyGUI.EventCallback0 onClick)
    {
        var item = (position == SideListPositions.Left ? _mainPath.mainListLeft.AddItemFromPool() : _mainPath.mainListRight.AddItemFromPool()) as ListItemMainBtnList;
        item.GetController("type").selectedIndex = (int)type;
        item.asCom.data = type;
        item.asButton.title = title;
        // (item.redDot as RedDotBase).CfgId = (int)type + 100;
        AddUIEvent(item.asButton.onClick, onClick);
        // 新增：写入字典缓存
        if (position == SideListPositions.Left)
        {
            _leftButtonDict[type] = item.asCom;
        }
        else
        {
            _rightButtonDict[type] = item.asCom;
        }
        return item;
    }

    private GComponent GetMainButtonWithType(HomepageEntranceTypes type)
    {
        // 优先从字典获取
        if (_leftButtonDict.TryGetValue(type, out var leftItem))
            return leftItem;
        if (_rightButtonDict.TryGetValue(type, out var rightItem))
            return rightItem;
        return null;
    }
    #region  click event handler
    private void OnBtnStreaksClicked()
    {
        SoundManger.instance.PlayUI("contacts_click_collection");
        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
        GetController<SignController>(ModelConsts.Sign).EnterSign();

        // var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_streaks, learnPathModel.validNextTaskItemInfoCache);
        // DataDotMgr.Collect(dot);
    }

    //转移到底tab了
    //private void OnBtnLeaderboardClicked()
    //{
    //    SoundManger.instance.PlayUI("contacts_click_collection");
    //    HapticUtils.Vibrate(HapticType.ImpactSoft);
    //    GetController<RankController>(ModelConsts.Rank).EnterRank();

    //    var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_leaderboard, learnPathModel.validNextTaskItemInfoCache);
    //    DataDotMgr.Collect(dot);
    //}

    //
    private void OnBtnWorldMapClicked()
    {
        Debug.LogError("世界入口icon 20250312已封闭，此逻辑不应该被触发");
        return ;
        if (!mainModel.isIncentiveDataReceived)
            return;
        if (mainModel.incentiveData != null && mainModel.incentiveData.user_data != null && mainModel.incentiveData.user_data.has_created_role)
        {
            SoundManger.instance.PlayUI("contacts_click_collection");
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            OnEnterWorldTest();

            // var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_world, learnPathModel.validNextTaskItemInfoCache);
            // DataDotMgr.Collect(dot);
        }
        else
        {
            GetUI<CreateRoleUI>(UIConsts.CreateRole).Show(true);
        }
    }

    private void OnBtnQuestClicked()
    {
        SoundManger.instance.PlayUI("contacts_click_collection");
        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
        GetController<IncentiveTaskController>(ModelConsts.IncentiveTask).EnterIncentiveTask();

        // var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_quest, learnPathModel.validNextTaskItemInfoCache);
        // DataDotMgr.Collect(dot);
    }

    // private void OnBtnGalleryClicked()
    // {
    //     SoundManger.instance.PlayUI("contacts_click_collection");
    //     HapticUtils.Vibrate(HapticType.ImpactSoft);
    //     GetUI(UIConsts.StoryUI).Show().onCompleted += () => { GetController<LearnPathController>(ModelConsts.LearnPath).SendGetUserGoalDetail(); };
    //
    //     var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_gallery, learnPathModel.validNextTaskItemInfoCache);
    //     DataDotMgr.Collect(dot);
    // }

    private void OnBtnContactsClicked()
    {
        SoundManger.instance.PlayUI("contacts_click_collection");
        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
        GetController<ContactsController>(ModelConsts.ContactsController).EnterContacts();

        // var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_contacts, learnPathModel.validNextTaskItemInfoCache);
        // DataDotMgr.Collect(dot);
    }

    private void OnBtnDIYClicked()
    {
        SoundManger.instance.PlayUI("contacts_click_collection");
        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
        //TODO: 当前为GMUI中的方法，后续需要从类似DIY Controller之类的方法中调用
        
        MainModel _mainModel = GetModel<MainModel>(ModelConsts.Main);
        // GameObject ctlPrefab = Resources.Load<GameObject>("Prefabs/WebViewCtl");
        GameObject ctlPrefab = GResManager.instance.LoadPrefab("WebViewCtl");           
        GameObject newCtl = UnityEngine.Object.Instantiate(ctlPrefab);
        
        WebViewCtl ctl = newCtl.GetComponent<WebViewCtl>();
        if (ctl == null)
        {
            ctl = newCtl.AddComponent<WebViewCtl>();
        }
        ctl.Init(5f, I18N.inst.MotherLanguageStr, I18N.inst.ForeignLanguageStr, _mainModel.toKen, I18N.inst.TempUILanguageStr,
            false,
            false,
            () =>
            { 
                GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
                {
                    //GetUI<MainHeaderUI>(UIConsts.MainHeader).Refresh();
                    this.ui.headBar.Refresh();
                });
            },() =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            },
            () =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            }
        );
        ctl.LoadUrl(GameEntry.WebviewC.GetUrl("diy"));

        // var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_diy, learnPathModel.validNextTaskItemInfoCache);
        // DataDotMgr.Collect(dot);
    }

    private void OnBtnDrillHubClicked()
    {
        GetController<DrillHubController>(ModelConsts.DrillHub).EnterDrillHub();
        
    
        // var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_drillhub, learnPathModel.validNextTaskItemInfoCache);
        // DataDotMgr.Collect(dot);
    }

    private void OnBtnMsgClicked()
    {
        DotClickHomePageChatIcon dot = new DotClickHomePageChatIcon();
        DataDotMgr.Collect(dot);
        
        ControllerManager.instance.GetController<MessagesController>(ModelConsts.Messages).EnterMessages();
    }
    
    private void OnBtnWhatsappClicked()
    {
        UIManager.instance.GetUI<WhatsappGuiderUI>(UIConsts.WhatsappGuider).Show();
    }

    private void OnBtnSurveyClicked()
    {

        SoundManger.instance.PlayUI("contacts_click_collection");
        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);

        //===
        MainModel _mainModel = GetModel<MainModel>(ModelConsts.Main);
        GameObject ctlPrefab = GResManager.instance.LoadPrefab("WebViewCtl");
        GameObject newCtl = UnityEngine.Object.Instantiate(ctlPrefab);

        WebViewCtl ctl = newCtl.GetComponent<WebViewCtl>();
        if (ctl == null)
        {
            ctl = newCtl.AddComponent<WebViewCtl>();
        }

  
        ctl.Init(5f, I18N.inst.MotherLanguageStr, I18N.inst.ForeignLanguageStr, _mainModel.toKen, I18N.inst.TempUILanguageStr,
            false,
            false,
            () =>
            {
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
                GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
                {
                    GetUI<MainHeaderUI>(UIConsts.MainHeader).Refresh();
                });
            }, () =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            },
            () =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            }
            );

        ctl.LoadUrl(GameEntry.WebviewC.GetUrl("survey"));//这里特指homepage中的survey
        //===
        // var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_survey, learnPathModel.validNextTaskItemInfoCache);
        // DataDotMgr.Collect(dot);
    }

    #endregion

    // public void UpdateSpeakState(LearnPathNextTaskItemInfo itemInfo)
    // {
    //  
    // }

    
    public void CheckAutoOpenDrillHub()
    {
        if (mainModel.DrillHubNeedAutoOpen && mainModel.DrillHubAutoShowFlag)
        {
            GetController<DrillHubController>(ModelConsts.DrillHub).EnterDrillHubForJump(mainModel.DrillHubViewType);
            mainModel.SetDrillHubAutoShow(false);
        }
    }

    private Action _enterPayWallAction = null;
    
    private void ShowBanner(Action callback)
    {
        if (ui.homepageTimeBanner.com.visible == false)
            DataDotMgr.Collect(new DotAppearMonetizationDiscountBottomBar());
        
        _enterPayWallAction = callback;
        ui.homepageTimeBanner.com.visible = true;
        ui.homepageTimeBanner.state.selectedIndex = 1;
        ui.homepageTimeBanner.tfDiscount.text =
            ShopUIUtil.GetSaleOffValue(_shopModel.PayWallData.subscription_infos[0]).ToString();
        ui.homepageTimeBanner.tfExpiring.SetKey("ui_pay_wall_expiring");
        ui.homepageTimeBanner.grp.EnsureBoundsCorrect();
    }

    private void OnClickPayWallEnter()
    {
        DataDotMgr.Collect(new DotClickMonetizationDiscountBottomBar());
        _enterPayWallAction?.Invoke();
    }
    
    private void OnClickPayWallShow()
    {
        DataDotMgr.Collect(new DotClickMonetizationDiscountBottomBarDiscountIcon());
        ui.homepageTimeBanner.state.selectedIndex = 1;
        DataDotMgr.Collect(new DotAppearMonetizationDiscountBottomBar());
    }
    
    private void OnClickPayWallHide()
    {
        DataDotMgr.Collect(new DotClickMonetizationDiscountBottomBarExitButton());
        ui.homepageTimeBanner.state.selectedIndex = 0;
        DataDotMgr.Collect(new DotAppearMonetizationDiscountBottomBarDiscountIcon());
    }

    protected override void HandleNotification(string name, object body)
    {
        var enterPos = mainModel.ChatEnterPos;
        switch (name)
        {
            //todo0
            case NotifyConsts.GoldNumChange:
                ui.headBar.SetGold();
                break;
            case EconomicCallEvent.OnRefreshEconomicInfo:
                // SetDiamond();
                ui.headBar.RefreshCurrency();
                break;

            case NotifyConsts.DrillHubReShowEvent:
                mainModel.SetDrillHubAutoShow(true, (DrillHubViewEnum)body);
                if (enterPos != ChatEnterPos.DrillHub) return;
                CheckAutoOpenDrillHub();
                break;
            case NotifyConsts.ShowHomepageBanner:
                var args = (HomepageBannerArgs)body;
                ui.homepageBanner.ShowBanner(args);
                break;
            case NotifyConsts.HideHomepageBanner:
                ui.homepageBanner.HideBanner();
                break;
            case NotifyConsts.ShowHomepageTimeBanner:
                ShowBanner((Action)body);
                break;
            case NotifyConsts.HideHomepageTimeBanner:
                ui.homepageTimeBanner.com.visible = false;
                break;
            case NotifyConsts.AfterDealResultData:
                ui.headBar.RefreshFire();
                break;
            case SocialChatEvent.OnRefreshUnreadCount:
                _mainController.RequsetUnReadMessageNum();
                break;
            case NotifyConsts.RefreshMainBtns:
                RebuildMainButtons();
                break;           
        }
    }

    public bool IsHomepageBannerVisible()
    {
        return ui.homepageBanner.isShow;
    }

    public bool IsHomepageTimeBannerVisible()
    {
        return ui.homepageTimeBanner.com.visible;
    }

    protected override string[] ListNotificationInterests()
    {
        return new string[]
        {
            //LearnPathCallEvent.OnRefreshEnterUI,
            NotifyConsts.DrillHubReShowEvent,
            NotifyConsts.ShowHomepageBanner,
            NotifyConsts.HideHomepageBanner,
            NotifyConsts.AfterDealResultData,
            SocialChatEvent.OnRefreshUnreadCount,
            NotifyConsts.ShowHomepageTimeBanner,
            NotifyConsts.HideHomepageTimeBanner,
            NotifyConsts.RefreshMainBtns,
        };
    }

    public enum HomepageEntranceTypes
    {
        Streaks = 0,
        Leaderboard = 1,
        WorldMap = 2,
        Quest = 3,
        Gallery = 4,
        Contacts = 5,
        DrillHub = 6,
        Moments = 7,
        DIY = 8,
        SURVEY = 9,
        Msg = 10,
        Whatsapp = 11,
    }

    private enum SideListPositions
    {
        Left = 0,
        Right = 1,
    }

    string waitNetConnTimer;
    private void OnEnterWorldTest()
    {
        //如果进入世界前已经 断开tcp了那么需要先恢复tcp再进入
        if (NetManager.instance.IsClose())
        {
            NetManager.instance.Close();
            ControllerManager.instance.GetController<LoginController>(ModelConsts.Login).StartTCPConn();

            waitNetConnTimer = TimerManager.instance.RegisterTimer((count) =>
            {
                if (NetManager.instance.IsConn())
                {
                    
                    ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene).EnterWoard_Test();
                    if (!string.IsNullOrEmpty(waitNetConnTimer))
                    {
                        TimerManager.instance.UnRegisterTimer(waitNetConnTimer);
                    }
                    GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                    return;
                }

                if (count == 2) {
                    GetUI<CommBusyUI>(UIConsts.CommBusy).Show();
                }
                if (count == 7)
                {
                    Debug.LogError("Fail to recover TCP's conn from close in 10 retry");
                    GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                    NetManager.instance.Close();
                }
            }, 800, 7);//todo 暂定只重试10轮 tcp连接， 没有复杂兜底逻辑
        }
        else
        {
            if (NetManager.instance.IsConn())
            {
                ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene).EnterWoard_Test();
            }
            else {
                
                waitNetConnTimer = TimerManager.instance.RegisterTimer((count) =>
                {
                    if (NetManager.instance.IsConn())
                    {
                        ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene).EnterWoard_Test();
                        if (!string.IsNullOrEmpty(waitNetConnTimer))
                        {
                            TimerManager.instance.UnRegisterTimer(waitNetConnTimer);
                        }
                        GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                        return;
                    }
                    if (count == 2)
                    {
                        GetUI<CommBusyUI>(UIConsts.CommBusy).Show();
                    }
                    if (count == 7)
                    {
                        Debug.LogError("Fail to recover TCP's conn from not conn in 10 retry");
                        GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                    }
                }, 800, 70);//todo 暂定只重试10轮 tcp连接， 没有复杂兜底逻辑
            }
        }
            
        // this.Hide();
    }

    // public void RefreshBottomTab(LearnPathNextTaskItemInfo result)
    // {
    //   
    // }

    public void OnScrollToTop()
    {
    
    }
    
    public void SetSocialChatUnReadNum()
    {
        GComponent comp = GetMainButtonWithType(HomepageEntranceTypes.Msg);
        if (comp == null)
            return;
        var unreadCount = mainModel.SocialChatUnreadCount;
        if (unreadCount == 0)
        {
            comp.GetController("markType").selectedIndex = 0; //不显示红点
        }
        else if (unreadCount < 10)
        {
            comp.GetController("markType").selectedIndex = 3; //显示个位数红点
            comp.GetChild("text_single").asTextField.text = unreadCount.ToString();
        }
        else 
        {
            comp.GetController("markType").selectedIndex = 4; //显示多位数红点
            if (unreadCount > 99)
            {
                comp.GetChild("text_double").asTextField.text = "...";
            }
            else
            {
                comp.GetChild("text_double").asTextField.text = unreadCount.ToString();
            }
        }
    }

}

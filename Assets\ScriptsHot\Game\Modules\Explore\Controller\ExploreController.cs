﻿using System;
using System.Collections.Generic;
using System.Linq;
using AnimationSystem;
using CommonUI;
using Grpc.Core;
using Msg.basic;
using Msg.explore;
using Msg.incentive;
using ScriptsHot.Game.Modules.ChatLogicNew.ChatType;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Explore.ExploreType;
using ScriptsHot.Game.Modules.Explore.ExploreType.Base;
using ScriptsHot.Game.Modules.Explore.UI;
using UIBind.Explore.Item;
using UIBind.FragmentPractice;
using UnityEngine;
using UnityEngine.Rendering;

public partial class ExploreController : BaseController
{

    public ExploreModel Model;
    
    private ExploreTypeBase _curExplore = null;
    public ExploreTypeBase CurExplore => _curExplore;
    
    
    
    /// <summary>
    /// 默认 手动模式
    /// </summary>
    private ExploreOperationType _curOperationType = ExploreOperationType.Active;

    private ExploreOperationType CurOperationType => _curOperationType;

    /// <summary>
    /// 浏览到的索引
    /// </summary>
    private int _lookAtIndex = 0;

    public int LookAtIndex => _lookAtIndex;

    /// <summary>
    /// 当前浏览的实体 id
    /// </summary>
    private long _curTaskId = 0;
    public long CurTaskId => _curTaskId;
    
    /// <summary>
    /// 上一次浏览的实体 id
    /// </summary>
    private long _upTaskId = 0;
    public long UpTaskId => _upTaskId;


    /// <summary>
    /// 当前对话实体的 mode
    /// </summary>
    private PB_DialogMode _dialogMode = 0;
    public PB_DialogMode DialogMode => _dialogMode;
    
    /// <summary>
    /// 剩余多少条数据时候 申请预载数据
    /// </summary>
    private int _preloadDataCount = 2;
    /// <summary>
    /// 数据申请状态
    /// </summary>
    private bool _dataReq = false;
    
    /// <summary>
    /// 是否可以录音
    /// </summary>
    public bool IfCanMicrophone = false;
    
    /// <summary>
    /// 是否可以把录音发送
    /// </summary>
    public bool IfCanMicrophoneSend = false;

    /// <summary>
    /// 是否是资深用户
    /// </summary>
    public bool IsSeniorPlayer = false;
    
    /// <summary>
    /// 暂停
    /// </summary>
    public bool IsPause = false;

    /// <summary>
    /// 是否使用mp3格式
    /// </summary>
    public bool UseMp3Audio = true;
    
    /// <summary>
    /// 是否自动消耗体验时间
    /// </summary>
    public bool IsConsumeTime = false;
    
    /// <summary>
    /// 是否能开始体验倒计时
    /// </summary>
    public bool CanExperienceTimeDown = false;
    
    public PB_Explore_CefrLevel CefrLevel;
    
    public DialogManager ModelDialogManager;

    #region 打点使用
    /// <summary>
    /// 这个东西之所以放这里 ，存粹是因为打点 需求 要的数据的耦合度太高，没办法
    /// </summary>
    public ExploreCellBase CurEnterEntity;


    #endregion

    // 内存监控相关
    private const float MEMORY_CHECK_INTERVAL = 20f; // 内存检查间隔(秒)
    private float _memoryCheckTimer = 0;
    private int _lowMemoryWarningCount = 0;
    private const int MAX_MEMORY_WARNING = 3; // 达到此警告次数后强制清理资源
    

    // 请求时间记录
    private float _requestStartTime = 0;

    private bool _sceneLoaded = false;
    private bool _dataListBack = false;
    /// <summary>
    /// 第一次 需要预加载
    /// </summary>
    private bool _isFirst = true;

    private string asktimeKey;
    #region 设置
    // 背景音乐 临时数据
    public bool TempOpenMusic = true;
    
    // 自动显示翻译 临时数据
    public bool TempAutoTransform = false;

    // 语速 1：慢 2：中 3：快 临时数据
    public int TempSpeakingSpeed = 1;

    #endregion

    public ExploreController() : base(ModelConsts.Explore)
    {
    }

    public override void OnUIInit()
    {
        RegisterUI(new ExplorePanelUI(UIConsts.ExploreUI));
        RegisterUI(new ExploreAwardNoticeUI(UIConsts.ExploreAwardNoticeUI));
        RegisterUI(new ExploreNewhandPanelUI(UIConsts.ExploreNewhandPanelUI));
        RegisterUI(new ExploreSettingPanelUI(UIConsts.ExploreSettingPanelUI));
    }

    public override void OnInit()
    {
        Model = new ExploreModel();
        Model.OnInit();
        RegisterModel(Model);
        SetNet(new ExploreTwoWayNetStrategy());
        
        // 初始化状态变量
        _sceneLoaded = false;
        _dataListBack = false;
        _isFirst = true;
        
        AddEvent();
        
        // 注册低内存回调
        Application.lowMemory += OnLowMemory;
    }

    public override void OnUpdate(int interval)
    {
        base.OnUpdate(interval);
        _curExplore?.Update(interval);
        _netStrategy?.Update(interval);
        ImagePreloader.Instance.Update();
        // 监控内存使用
        MonitorMemory(interval);
    }

    private void AddEvent()
    {
        RemoveEvent();
        MsgManager.instance.RegisterCallBack<SC_GetRecommendListResp>(this.OnGetRecommendListResp);
        MsgManager.instance.RegisterCallBack<SC_DialogDownMsgForBizEvent>(this.OnDialogDownMsgForBizEvent);
        MsgManager.instance.RegisterCallBack<SC_ExploreDownMsgForServerBasic>(this.OnExploreDownMsgForServerBasic);
        MsgManager.instance.RegisterCallBack<SC_UserSettingDownMsgForSaveUserSettings>(this.OnExploreUserSettingDownMsgForSaveUserSettings);
        
        
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreInfoAsk,OnExploreInfoAsk);
        Notifier.instance.RegisterNotification(NotifyConsts.SceneLoadComplete,OnSceneLoadComplete);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreEnter,EnterExplore);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreChangeOperationType,ChangeExploreOperationType);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreNetConect,ExploreNetConect);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreNetExit,OnExploreNetExit);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreSoundStop,OnStopStreamAudioTTs);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreClickScaffoldBtn,OnClickScaffoldBtn);
        Notifier.instance.RegisterNotification(NotifyConsts.NewWorldAudioStart,OnNewWorldAudioStart);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreShowRecordUI,OnExploreShowRecordUI);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreNoNetworkTipShow,OnNoNetworkTipShow);
        
    }
    

    private void RemoveEvent()
    {
        MsgManager.instance.UnRegisterCallBack<SC_GetRecommendListResp>(this.OnGetRecommendListResp);
        MsgManager.instance.UnRegisterCallBack<SC_DialogDownMsgForBizEvent>(this.OnDialogDownMsgForBizEvent);
        MsgManager.instance.UnRegisterCallBack<SC_ExploreDownMsgForServerBasic>(this.OnExploreDownMsgForServerBasic);
        MsgManager.instance.UnRegisterCallBack<SC_UserSettingDownMsgForSaveUserSettings>(this.OnExploreUserSettingDownMsgForSaveUserSettings);
        
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreInfoAsk,OnExploreInfoAsk);
        Notifier.instance.UnRegisterNotification(NotifyConsts.SceneLoadComplete,OnSceneLoadComplete);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreEnter,EnterExplore);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreChangeOperationType,ChangeExploreOperationType);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreNetConect,ExploreNetConect);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreNetExit,OnExploreNetExit);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreSoundStop,OnStopStreamAudioTTs);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreClickScaffoldBtn,OnClickScaffoldBtn);
        Notifier.instance.UnRegisterNotification(NotifyConsts.NewWorldAudioStart,OnNewWorldAudioStart);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreShowRecordUI,OnExploreShowRecordUI);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreNoNetworkTipShow,OnNoNetworkTipShow);
        
        _netStrategy?.RemoveEvent();
    }

    private void OnNewWorldAudioStart(string s, object body)
    {
        OnStopStreamAudioTTs(string.Empty,null);
    }

    private void OnNoNetworkTipShow(string s, object body)
    {
        // 两个提示不能同时出现
        if (GetUI<NoNetworkPanelUI>(UIConsts.NoNetworkPanelUI).isShow) return;
        
        HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
        if (hCon.IsExploreTab())
        {
            Notifier.instance.SendNotification(NotifyConsts.OpenUI,UIConsts.NoNetworkTipUI);
        }
    }

    /// <summary>
    /// 这个消息每次重新和服务器建立 GRPC 都会收到 ，可以用来做 Explore内部GRPC 断线重连
    /// </summary>
    /// <param name="msg"></param>
    private void OnExploreDownMsgForServerBasic(SC_ExploreDownMsgForServerBasic msg)
    {
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            CefrLevel = msg.userInfo.cerfLevel;
            IsSeniorPlayer = msg.userInfo.cerfLevel > PB_Explore_CefrLevel.EO_CL_A2;
            TempAutoTransform = !IsSeniorPlayer;
            UseMp3Audio = msg.audioInfo.audioFormat == PB_Explore_AudioFormat.EO_AF_MP3;
            if (msg.userSettingInfo != null)
            {
                Model.SpeakingSpeed = msg.userSettingInfo.speakingSpeed;
                Model.AutoTranslateDisplay = msg.userSettingInfo.autoTranslateDisplay;
                Model.BackgroundMusic = msg.userSettingInfo.backgroundMusic;
            }

            Notifier.instance.SendNotification(NotifyConsts.ExploreDownMsgForServerBasic, msg);
        }
        
        VFDebug.Log("收到用户等级消息  msg.user_info.cerfLevel::" + msg.userInfo.cerfLevel + "     UseMp3Audio:" + UseMp3Audio);
    }
    
    /// <summary>
    /// 设置
    /// </summary>
    /// <param name="msg"></param>
    private void OnExploreUserSettingDownMsgForSaveUserSettings(SC_UserSettingDownMsgForSaveUserSettings msg)
    {
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            Model.BackgroundMusic = TempOpenMusic?Msg.explore.PB_Explore_UserSetting_BackgroundMusic.EO_US_BM_ON:Msg.explore.PB_Explore_UserSetting_BackgroundMusic.EO_US_BM_OFF;
            Model.SpeakingSpeed = GetSpeakingSpeed();
            Model.AutoTranslateDisplay = TempAutoTransform?Msg.explore.PB_Explore_UserSetting_AutoTranslateDisplay.EO_US_ATD_ON:Msg.explore.PB_Explore_UserSetting_AutoTranslateDisplay.EO_US_ATD_OFF;
            
            Notifier.instance.SendNotification(NotifyConsts.ExploreBGMopen,TempOpenMusic);
        }
    }

    public Msg.explore.PB_Explore_UserSetting_SpeakingSpeed GetSpeakingSpeed()
    {
        Msg.explore.PB_Explore_UserSetting_SpeakingSpeed enumSpeed = PB_Explore_UserSetting_SpeakingSpeed.EO_US_SS_SLOW;
        switch (TempSpeakingSpeed)
        {
            case 1:
                enumSpeed = PB_Explore_UserSetting_SpeakingSpeed.EO_US_SS_SLOW;
                break;
            case 2:
                enumSpeed = PB_Explore_UserSetting_SpeakingSpeed.EO_US_SS_MEDIUM;
                break;
            case 3:
                enumSpeed = PB_Explore_UserSetting_SpeakingSpeed.EO_US_SS_FAST;
                break;
        }

        return enumSpeed;
    }

    /// <summary>
    /// 语音速度
    /// </summary>
    /// <returns></returns>
    public float GetAudioSpeed()
    {
        float speed = 1;
        return speed;
        if (CefrLevel == PB_Explore_CefrLevel.EO_CL_A1 || CefrLevel == PB_Explore_CefrLevel.EO_CL_A2)
        {
            speed = 0.8f;
        }
        else if (CefrLevel == PB_Explore_CefrLevel.EO_CL_B1 || CefrLevel == PB_Explore_CefrLevel.EO_CL_B2)
        {
            speed = 1f;
        }
        else if (CefrLevel == PB_Explore_CefrLevel.EO_CL_C1 || CefrLevel == PB_Explore_CefrLevel.EO_CL_C2)
        {
            speed = 1.2f;
        }

        VFDebug.LogError($"语音in速度：{speed} + CefrLevel：{CefrLevel}");
        return speed;
    }

    private void ExploreNetConect(string s, object body)
    {
        RequestAsr("",false,true,String.Empty,false);
    }

    private void EnterExplore(string s, object body)
    {
        if(_curExplore != null) _curExplore.Dispose();

        Model.Reset();

        ExploreOperationType typeValue = body != null ? (ExploreOperationType) body : ExploreOperationType.Active;

        _curOperationType = typeValue;

        _curExplore = CreateExplore(_curOperationType);
        if (_curExplore == null) return;
        _curExplore.Init(new ExploreParam());
        _curExplore.Start();
    }
        
    private void ExitDialogue(string s, object body)
    {
        if (_curExplore == null) return;
        _curExplore.Exit();
    }
    
    private void ChangeExploreOperationType(string s, object body)
    {
        if(_curExplore != null) _curExplore.Dispose();

        ExploreOperationType typeValue = body != null ? (ExploreOperationType) body : ExploreOperationType.Active;

        _curOperationType = typeValue;

        _curExplore = CreateExplore(_curOperationType);
        if (_curExplore == null) return;
        _curExplore.Init(new ExploreParam());
        _curExplore.Start();
    }

    private ExploreTypeBase CreateExplore(ExploreOperationType type) {
        switch(type) {
            case ExploreOperationType.Active:    
                return new ExploreActiveType();
                break;
            default:
                return null;
        }
    }

    private void OnStopStreamAudioTTs(string s, object body)
    {
        TTSManager.instance.StopTTS();//recordId = 0时 目前必然触发 GSoundManager.instance.StopAvatarTTS();
        ModelDialogManager?.EnterDialogueIdleState();
    }

    public void UpdateCurEntityInfo(long taskId,PB_DialogMode dialogMode)
    {
        _upTaskId = _curTaskId;
        _curTaskId = taskId;
        _dialogMode = dialogMode;
    }

    public void UpdateLookAtIndex(int value)
    {
        _lookAtIndex = value;
        if (Model.AllData.Count - _preloadDataCount <= value)
        {
            VFDebug.Log($"Explore准备拉取新数据------- Model.AllData.Count::{Model.AllData.Count} + value::{value}");
            CS_GetRecommendListReq();
        }
    }
    
    /// <summary>
    /// GRPC 断开了 
    /// </summary>
    /// <param name="s"></param>
    /// <param name="body"></param>
    private void OnExploreNetExit(string s, object body)
    {
        GMicrophoneManager.instance.EndRecord();
        ClearShowFinishTime();
        _netStrategy.Stop();
        
        TimerManager.instance.RegisterTimer((a) =>
        {
            //重新链接
            ExploreNetConect(string.Empty,null);
        }, ExploreConst.NetConnectTime);//2s后重连
    }

    
    #region 协议

    private void OnExploreInfoAsk(string s, object body)
    {
        CS_GetRecommendListReq();
    }

    private void OnSceneLoadComplete(string s, object body)
    {
        VFDebug.Log("Explore场景加载完成");
        _sceneLoaded = true;
        
        TryPreloadResources();
    }
    
    /// <summary>
    /// 获取推荐列表
    /// </summary>
    public void CS_GetRecommendListReq()
    {
        if(_dataReq) return;
        _dataReq = true;

        // VFDebug.LogError("拉取新数据-------------------------------");
        //test
        // Model.Test();
        // Notifier.instance.SendNotification(NotifyConsts.RefreshExploreData);
        // return;
        CS_GetRecommendListReq msg = new CS_GetRecommendListReq();
        msg.idempotentSign = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        
        // 记录请求开始时间
        _requestStartTime = Time.realtimeSinceStartup;
        
        TimerLogHelper.Ins.SaveTimeStart(TimerLogHelper.TimeType.LoadData_Explore);
        MsgManager.instance.SendMsg(msg,GetRecommendListFail);
    }
    
    public void GetRecommendListFail(GRPCManager.ErrorType et,Google.Protobuf.IMessage msg)
    {
        _dataReq = false;
        VFDebug.LogError("获取探索预加载列表数据失败！");

        OnNoNetworkTipShow(String.Empty,null);
    }
    
    private void OnGetRecommendListResp(SC_GetRecommendListResp msg)
    {
        _dataReq = false;
        TimerLogHelper.Ins.SaveTimeEnd(TimerLogHelper.TimeType.LoadData_Explore);
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            
            // 计算请求耗时
            float requestTime = Time.realtimeSinceStartup - _requestStartTime;
            // VFDebug.LogError($"Explore network SC_GetRecommendListResp 请求成功，耗时: {requestTime:F2}秒  数据个数：{msg.preloadDataList.Count}");
            
            Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.NoNetworkTipUI);
            Model.SetPreLoadDataList(msg.preloadDataList.ToList());
            
            _dataListBack = true;
            TryPreloadResources();
            
            Notifier.instance.SendNotification(NotifyConsts.RefreshExploreData);

            //TimerManager.instance.RegisterTimer(cc=>{
            //    isFirstTurnDataReady = true;
            //},5000,1);
        }
        else
        {
            HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
            if (hCon.IsExploreTab())
            {
                VFDebug.LogError("获取探索预加载列表数据,服务器返回错误！");
                OnNoNetworkTipShow(String.Empty,null);
            }
        }
        VFDebug.Log("preload event :Explore 数据回包");
        Notifier.instance.SendNotification(NotifyConsts.ScenePreLoadEvent_Explore);
    }
    
    /// <summary>
    /// 请求体验剩余时间
    /// </summary>
    public void AskExperienceLastTime()
    {
        VFDebug.Log("请求体验剩余时间 !!!!!!!!");
        CS_GetIncentiveDataForExploreReq req = new CS_GetIncentiveDataForExploreReq();
        MsgManager.instance.SendMsg(req, FailedGetIncentiveDataForExplore);
    }

    private void FailedGetIncentiveDataForExplore(GRPCManager.ErrorType arg1, Google.Protobuf.IMessage arg2)
    {
        //LogError会导致重复计入
        VFDebug.Log("获取探索剩余时间失败！");
    }
    
    #endregion

    #region 本地存储
    private const string FLAG_KEY = "ExploreFlag_";
    
    /// <summary>
    /// 保存标记到本地
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="flag">标记值</param>
    public void SaveFlag(string keyValue, bool flag)
    {
        string key = FLAG_KEY + keyValue;
        PlayerPrefs.SetInt(key, flag ? 1 : 0);
        PlayerPrefs.Save();
    }

    /// <summary>
    /// 从本地获取标记
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>标记值，如果不存在则返回false</returns>
    public bool GetFlag(string keyName)
    {
        string key = FLAG_KEY + keyName;
        if (PlayerPrefs.HasKey(key))
        {
            return PlayerPrefs.GetInt(key) == 1;
        }
        return false;
    }

    /// <summary>
    /// 清除指定任务ID的标记
    /// </summary>
    /// <param name="taskId">任务ID</param>
    public void ClearFlag(string keyName)
    {
        string key = FLAG_KEY + keyName;
        if (PlayerPrefs.HasKey(key))
        {
            PlayerPrefs.DeleteKey(key);
            PlayerPrefs.Save();
        }
    }

    #endregion

    #region 内存处理
    
    // 监控内存
    private void MonitorMemory(int interval)
    {
        _memoryCheckTimer += interval / 1000f;
        
        if (_memoryCheckTimer >= MEMORY_CHECK_INTERVAL)
        {
            _memoryCheckTimer = 0;
            // 检查系统内存
            if (SystemInfo.systemMemorySize < 300)
            {
                _lowMemoryWarningCount++;
                VFDebug.Log($"Explore memory 内存警告({_lowMemoryWarningCount}/{MAX_MEMORY_WARNING}): 系统内存低于300MB");
                
                if (_lowMemoryWarningCount >= MAX_MEMORY_WARNING)
                {
                    VFDebug.Log("Explore memory 触发强制内存清理");
                    CleanupMemory();
                    _lowMemoryWarningCount = 0;
                }
            }
            else
            {
                _lowMemoryWarningCount = 0;
            }
        }
    }
    
    // 低内存回调
    private void OnLowMemory()
    {
        VFDebug.Log("Explore memory 收到系统低内存警告，执行资源清理");
        CleanupMemory();
    }
    
    // 清理内存资源
    public void CleanupMemory()
    {
        VFDebug.Log("Explore memory 内存报警，执行资源清理");
        // 清理音频缓存 -- 方法中会GC
        ImagePreloader.Instance.LowMemory();
        Model.ClearAllAudioCache();
    }
    
    public override void OnApplicationQuit()
    {
        base.OnApplicationQuit();
        RemoveEvent();
        Application.lowMemory -= OnLowMemory;
    }
    
    public override void OnApplicationPause(bool pause)
    {
        VFDebug.Log("Explore memory 进入后台:" + pause);
        if (!pause)
        {
            var ctrl = GetController<LoginController>(ModelConsts.Login);
            if (ctrl.isLoginComplete)
            {
                //防止反复切后台时 反复未到定时点，反复触发
                if(string.IsNullOrEmpty(asktimeKey) )
                {
                    //GRPC Channel 从后台恢复是需要一些时间的，不适合立即请求。目前不能确认3s是最适合的，但是太短时重连可能未完成。只能先缓解下观察一下
                    asktimeKey = TimerManager.instance.RegisterTimer((c) =>
                    {
                        AskExperienceLastTime();
                        asktimeKey = string.Empty;
                    }, 3000, 1);
                }
            }
                
            
        }
    }
    #endregion

    private void TryPreloadResources()
    {
        if (_sceneLoaded && _dataListBack && _isFirst && Model.AllData.Count > 0)
        {
            VFDebug.Log("Explore场景和数据都已准备好，开始预加载模型");
            ModelPreloader.Instance.PreloadModels(Model.AllData, 0, 3);
            _isFirst = false;
        }
    }
    
    public AsyncDuplexStreamingCall<global::Msg.explore.CS_ExploreUpMsg, global::Msg.explore.SC_ExploreDownMsg> GetClientCell()
    {
        return (this._netStrategy as ExploreTwoWayNetStrategy).GetClientCell();
    }
}

public enum ExploreOperationType
{
    Active,
    Auto,
}

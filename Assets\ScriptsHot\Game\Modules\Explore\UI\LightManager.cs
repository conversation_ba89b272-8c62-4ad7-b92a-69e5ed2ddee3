﻿using System.Collections.Generic;
using UIBind.Profile;
using UnityEngine;
using UnityEngine.Rendering;
using YooAsset;

namespace UIBind.Explore.Item
{
    //5.23 探索页灯光需求
    //主要逻辑：在角色身上创建一个RTLights挂点，并且关闭RTLight光照。
    //基于读表逻辑，创建多个RTLight，并且指定到该层级。
    //刷新机制：每个ExploreItem加载结束时，重新触发读表逻辑和RTLight逻辑，强制清零所有灯光重新加载。保存该RTLights挂点。
    //关闭机制：原先关闭RTLight的地方，提供接口清理灯光。
    //开启机制：原先开启RTLight的地方，改为无逻辑。
    public class LightManager
    {
        private static LightManager _instance;
        public static LightManager Instance
        {
            get
            {
                if (_instance == null)
                    _instance = new LightManager();
                return _instance;
            }
        }

        // 私有构造函数，防止外部new
        private LightManager() { }
        
        private List<GameObject> lights = new List<GameObject>();

        public Light rtLight;

        /// <summary>
        /// 给定一个ExploreItemGO、BGID,加载好其中的灯光。如果本来就有,替换掉。
        /// </summary>
        public void LoadLightForItem(string bgId,GameObject parent,int layerMask = -1,bool enable = true)
        {
            if(parent == null) return;
            
            void LoadDefault(Transform go)
            {
                if (go)
                {
                    if (YooAssets.CheckLocationValid("RTLight"))
                    {
                        // 注意：不再使用GResManager，因为其GGameObjectPool存在Transform异常问题：
                        // - 使用极大坐标值(int.MaxValue)导致浮点精度问题&坐标异常问题
                        // - 使用无效四元数(0,0,0,0)导致旋转计算异常
                        var handle = YooAssets.LoadAssetAsync<GameObject>("RTLight");
                        handle.Completed += (assetHandle) =>
                        {
                            if (assetHandle != null && assetHandle.AssetObject != null && assetHandle.IsDone)
                            {
                                var goo = assetHandle.AssetObject as GameObject;
                                goo = Object.Instantiate(goo, go, true);
                                SetMainLightPrefabCullingMask(goo, layerMask,enable);
                            }
                        };
                    }
                }
            }

            var go = parent.transform.Find("RTLights");

            if (!go)
            {
                var gameObject = new GameObject("RTLights");
                gameObject.transform.SetParent(parent.transform);
                this.lights.Add(gameObject);
                go = gameObject.transform;
                go.localScale = Vector3.one;
                go.localPosition = Vector3.zero;
            }

            if (go)
            {
                foreach (Transform child in go)
                {
                    UnityEngine.Object.DestroyImmediate(child.gameObject);
                }

                //没配表加载默认的Light。
                AnimationSystem.Cfg.T.TBBgLights.DataMap.TryGetValue(bgId, out var lightType);
                if (lightType == null)
                {
                    LoadDefault(go);
                    return;
                }
                AnimationSystem.Cfg.T.TBBgLightTypes.DataMap.TryGetValue(bgId, out var lightData);
                if (lightData == null)
                {
                    LoadDefault(go);
                    return;
                }

                // 加载主光预制体
                if (lightData.bgMainLightPrefab != "" && YooAssets.CheckLocationValid(lightData.bgMainLightPrefab))
                {
                    var handle = YooAssets.LoadAssetAsync<GameObject>(lightData.bgMainLightPrefab);
                    handle.Completed += (assetHandle) =>
                    {
                        if (assetHandle != null && assetHandle.AssetObject != null && assetHandle.IsDone)
                        {
                            var goo = assetHandle.AssetObject as GameObject;
                            goo = Object.Instantiate(goo, go, true);
                            SetMainLightPrefabCullingMask(goo, layerMask,enable);
                        }
                    };
                }

                // 加载额外光预制体
                if (lightData.bgSubLightPrefabs != null)
                {
                    foreach (var name in lightData.bgSubLightPrefabs)
                    {
                        if (YooAssets.CheckLocationValid(name))
                        {
                            var handle = YooAssets.LoadAssetAsync<GameObject>(name);
                            handle.Completed += (assetHandle) =>
                            {
                                if (assetHandle != null && assetHandle.AssetObject != null && assetHandle.IsDone)
                                {
                                    var goo = assetHandle.AssetObject as GameObject;
                                    goo = Object.Instantiate(goo, go, true);
                                    SetSubLightPrefabCullingMask(goo, layerMask,enable);
                                }
                            };
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 设置主光预制体的cullingMask
        /// 主光预制体结构：根节点是主光，子节点是额外光
        /// </summary>
        /// <param name="gameObject">主光预制体根节点</param>
        /// <param name="layerMask">要设置的layerMask值</param>
        private void SetMainLightPrefabCullingMask(GameObject gameObject, int layerMask,bool enable)
        {
            // 设置根节点的主光
            var mainLight = gameObject.GetComponent<Light>();
            if (mainLight)
            {
                mainLight.cullingMask = GetLayerMask(layerMask);
                mainLight.enabled = enable;
            }
            
            var envLight = gameObject.GetComponent<EnvLighting>();
            if (envLight)
            {
                if (envLight.lightType == LightType.Gradient)
                {
                    RenderSettings.ambientMode = AmbientMode.Trilight;
                    RenderSettings.ambientSkyColor = envLight.skyColor;
                    RenderSettings.ambientEquatorColor = envLight.equatorColor;
                    RenderSettings.ambientGroundColor = envLight.groundColor;
                }
                else
                {
                    RenderSettings.ambientMode = AmbientMode.Flat;
                    RenderSettings.ambientLight = envLight.skyColor;
                }
            }
            else
            {
                RenderSettings.ambientMode = AmbientMode.Skybox;
                RenderSettings.ambientSkyColor = new Color(0.221f, 0.227f, 0.259f);
            }

            // 设置子节点的额外光
            foreach (Transform child in gameObject.transform)
            {
                var subLight = child.GetComponent<Light>();
                if (subLight)
                {
                    subLight.cullingMask = GetLayerMask(layerMask);
                    subLight.enabled =enable;
                }
            }
        }

        /// <summary>
        /// 设置额外光预制体的cullingMask
        /// 额外光预制体结构：根节点是空GO，子节点是额外光
        /// </summary>
        /// <param name="gameObject">额外光预制体根节点</param>
        /// <param name="layerMask">要设置的layerMask值</param>
        private void SetSubLightPrefabCullingMask(GameObject gameObject, int layerMask,bool enable)
        {
            // 根节点是空GO，不处理
            // 只设置子节点的额外光
            foreach (Transform child in gameObject.transform)
            {
                var subLight = child.GetComponent<Light>();
                if (subLight != null)
                {
                    subLight.cullingMask = GetLayerMask(layerMask);
                    subLight.enabled = enable;
                }
            }
        }

        private LayerMask GetLayerMask(int layerMask)
        {
            if(layerMask == -1) return LayerMask.GetMask("RT1","RT2","RT3","RT4","RT5"); 
            else return (LayerMask)layerMask;
        }

        public void CloseLight(GameObject go)
        {
            if (go == null) return;
            
            var light = go.transform.Find("RTLights");
            if (light)
            {
                Object.DestroyImmediate(light.gameObject);
            }
        }

        /// <summary>
        /// 清理所有的灯光,但不会Destroy。等着跟着角色GO一块儿Destroy。
        /// [由于现在不关灯了。所以这个也不用调用了。但逻辑先保留,以免未来关灯。]
        /// </summary>
        public void ClearAllLights()
        {
            for (int i = lights.Count - 1; i >= 0; i--)
            {
                if (lights[i] != null)
                {
                    lights[i].SetActive(false);
                }
            }
            lights.Clear();
        }
    }
}
/*
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/16 15:54:57 星期四
# 功能：Nothing
****************************************************
*/

using System;
using FairyGUI;
using Modules.DataDot;
using ScriptsHot.Game.Modules.Settlement;

namespace ScriptsHot.Game.Modules.Shop
{
    [Obsolete]
    public class PlanPromotionStep3UI : BaseUI<UIBind.Shop.PlanPromotionStep3Panel>
    {
        public PlanPromotionStep3UI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top; //主UI层
        
        private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);
        
        private ShopModel _shopModel => GetModel<ShopModel>(ModelConsts.Shop);
        
        private CurrencyModel _currencyModel => GetModel<CurrencyModel>(ModelConsts.CurrencyController);
        
        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(this.ui.btnExit.onClick, OnBtnExitClicked);
            AddUIEvent(this.ui.comPlanPromotionContent3.btnNext.onClick, OnbBtnNextClicked);
            InitUI();
        }
        
        private void InitUI()
        {
            ui.comPlanPromotionContent3.tfBtnNext.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_plan_promotion_desc23", _shopModel.GetRecommendPlanDays());
            ui.comPlanPromotionContent3.tfPromotionDesc1.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_promotion_desc16");
            ui.comPlanPromotionContent3.tfPromotionDesc2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKeyArgs("ui_plan_promotion_desc17", DateTime.Now.AddDays(_shopModel.GetRecommendPlanDays() - 5).ToString(I18N.inst.MoStr("ui_plan_promotion_desc20")));
        }
        
        protected override void OnShow()
        {
            RemindPage dot = new RemindPage();
            DataDotMgr.Collect(dot);
            ui.comPlanPromotionContent3.Cutin.Play();
        }
        
        private void OnbBtnNextClicked()
        {
            GetUI<PlanSelectUI>(UIConsts.PlanSelectUI).Show(args);
            Hide();
            //新埋点：未免费订阅过的用户，在「5天前提醒」页点免费14天体验
            DataDotTrialDetailNext dot = new DataDotTrialDetailNext();
            
            
            // dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            // dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            dot.source_page = nameof(PayWallDotHelper.LastSourcePage);
            DataDotMgr.Collect(dot);
            AFDots.Click_Remind_page_get();
            MyFirebaseAnalytics.LogEvent("trial_remind");
        }
        
        private void OnBtnExitClicked()
        {
            //新埋点：未未免费订阅过的用户，在「5天前提醒」页点退出
            DataDotTrialDetailQuit dot = new DataDotTrialDetailQuit();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            DataDotMgr.Collect(dot);
            bool needHide = true;
            if (args != null && args.Length > 0)
            {
                var source = (int)args[0];
                if (source == 4)
                {
                    needHide = false;
                    GetController<SettlementController>(ModelConsts.Settlement).ShowNextView(()
                    =>
                    {
                        GetController<ShopController>(ModelConsts.Shop).CheckSendOnBoardOverMsg();
                        Hide();
                    });
                }
            }
            if (needHide)
            {
                GetController<ShopController>(ModelConsts.Shop).CheckSendOnBoardOverMsg();
                Hide();
            }
        }
    }
}
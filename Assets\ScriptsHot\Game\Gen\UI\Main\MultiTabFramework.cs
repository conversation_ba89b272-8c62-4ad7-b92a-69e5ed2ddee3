/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Main
{
    public partial class MultiTabFramework : UIBindT
    {
        public override string pkgName => "Main";
        public override string comName => "MultiTabFramework";

        public BgMask BgOfBg;
        public GGraph imgBG;
        public GComponent TabGList;
        public MuLtiTabInsertContainer insertContainer;
        public BottomTabRegion BottomTabRegion;
        public GGroup bottomBar;
        public showTabBtn showTabsBtn;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            BgOfBg = new BgMask();
            BgOfBg.Construct(com.GetChildAt(0).asCom);
            imgBG = (GGraph)com.GetChildAt(1);
            TabGList = (GComponent)com.GetChildAt(2);
            insertContainer = new MuLtiTabInsertContainer();
            insertContainer.Construct(com.GetChildAt(3).asCom);
            BottomTabRegion = new BottomTabRegion();
            BottomTabRegion.Construct(com.GetChildAt(4).asCom);
            bottomBar = (GGroup)com.GetChildAt(5);
            showTabsBtn = new showTabBtn();
            showTabsBtn.Construct(com.GetChildAt(6).asCom);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            BgOfBg.Dispose();
            BgOfBg = null;
            imgBG = null;
            TabGList = null;
            insertContainer.Dispose();
            insertContainer = null;
            BottomTabRegion.Dispose();
            BottomTabRegion = null;
            bottomBar = null;
            showTabsBtn.Dispose();
            showTabsBtn = null;
        }
    }
}
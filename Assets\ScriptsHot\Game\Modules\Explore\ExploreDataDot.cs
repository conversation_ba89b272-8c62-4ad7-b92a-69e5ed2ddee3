namespace ScriptsHot.Game.Modules.Explore
{

    
    //开始播放avatar第一个句子的tts
    // first_sentence_text: avatar第一句话的文本
    // task_id: 本条内容的id
    public class PLAY_EXPLORE_AVATAR_FIRST_AUDIO : DataDotBase
    {
        public override string Event_name => "play_explore_avatar_first_audio";
    
        public long task_id;
    }
    
    //底tab收起
    // first_sentence_text: avatar第一句话的文本
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    public class HIDE_TAB_BAR : DataDotBase
    {
        public override string Event_name => "hide_tab_bar";
    
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //用户点击叉号以重新放出底tab
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_HIDDEN_TAB_CLOSE : DataDotBase
    {
        public override string Event_name => "click_hidden_tab_close";
    
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //用户点击翻译按钮
    // root_bubble:翻译按钮属于哪类气泡，e.g.avatar/example
    // previuos_langauge:点击前展示语言，枚举值包括 English以及profile页母语的设置项枚举值；
    // target_language:点击后展示语言，枚举值包括 English以及profile页母语的设置项枚举值；
    // previuos_text:点击前展示文本；
    // target_text:点击后展示文本
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_EXPLORE_TRANSLATION_BUTTON : DataDotBase
    {
        public override string Event_name => "click_explore_translation_button";

        public string previuos_langauge;
        public string target_language;
        public string root_bubble;
        public string previuos_text;
        public string target_text;
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
        public int is_onboarding = 0;
    }
    
    //用户点击播放声音按钮
    // root_bubble:翻译按钮属于哪类气泡，e.g.avatar/example
    // bubble_text:播放的文本
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_EXPLORE_PLAY_BUTTON : DataDotBase
    {
        public override string Event_name => "click_explore_play_button";

        public string root_bubble;
        public string bubble_text;
        public long task_id;
        public long dialogue_id;    
        public long dialogue_round;
        public int is_onboarding = 0;
        
    }
    //出现example气泡
    // example_text: 脚手架文本
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_EXPLORE_EXAMPLE : DataDotBase
    {
        public override string Event_name => "appear_explore_example";

        public string example_text;
        public long task_id;
        public string dialogue_id;
        public long dialogue_round;
    }
    
    //用户点击切换麦克风状态
    // mic_switch_result:点击后麦克风状态，e.g. manual（手动）/ auto（自动）
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_EXPLORE_MIC_SWITCH : DataDotBase
    {
        public override string Event_name => "click_explore_mic_switch";

        public string example_text;
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //用户点击开启麦克风
    // task_id: 本条内容的id；
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_EXPLORE_MIC_START : DataDotBase
    {
        public override string Event_name => "click_explore_mic_start";

        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //用户点击脚手架
    // task_id: 本条内容的id；
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_EXPLORE_EXAMPLE_BUTTON : DataDotBase
    {
        public override string Event_name => "click_explore_example_button";

        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //用户点击结束麦克风
    // mic_sec: 点击麦克风结束时显示的秒数
    // task_id: 本条内容的id；
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_EXPLORE_MIC_END : DataDotBase
    {
        public override string Event_name => "click_explore_mic_end";

        public long mic_sec;
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //出现avatar气泡
    // avatar_text: avatar的文本
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_EXPLORE_AVATAR_BUBBLE : DataDotBase
    {
        public override string Event_name => "appear_explore_avatar_bubble";

        public string avatar_text;
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
        public int is_onboarding = 0;
    }
    
    //出现用户气泡
    // mic_status:产生此条文本时麦克风状态，e.g. manual（手动）/ auto（自动）
    // user_text: user的文本
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_EXPLORE_USER_BUBBLE : DataDotBase
    {
        public override string Event_name => "appear_explore_user_bubble";

        public string mic_status;
        public string user_text;
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
        public int is_onboarding = 0;
    }
    
    //出现经验值奖励
    // xp_bonus_amount:本次奖励的经验值
    // xp_total_amount:本次奖励后累计的经验值
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_EXPLORE_XP_BONUS : DataDotBase
    {
        public override string Event_name => "appear_explore_xp_bonus";

        public long xp_bonus_amount;
        public long xp_total_amount;
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //出现语速奖励
    // spped_bonus_amount:本次显示的语速增加值
    // speed_total_amount:本次显示后累计的语速
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_EXPLORE_SPEED_BONUS : DataDotBase
    {
        public override string Event_name => "appear_explore_speed_bonus";

        public long spped_bonus_amount;
        public long speed_total_amount;
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //出现词汇量奖励
    // vocab_bonus_amount:本次显示的词汇量增加值
    // vocab_total_amount:本次显示后累计的词汇量
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_EXPLORE_VOCAB_BONUS : DataDotBase
    {
        public override string Event_name => "appear_explore_vocab_bonus";

        public long vocab_bonus_amount;
        public long vocab_total_amount;
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //出现avatar思考气泡  ---  由于气泡每次都出现 ，跟产品沟通 去掉了这个 打点
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: （出现思考气泡时算作已进入下一轮次）本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_EXPLORE_AVATAR_THINKING_BUBBLE : DataDotBase
    {
        public override string Event_name => "appear_explore_avatar_thinking_bubble";

        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //出现Check your feedback in 'Review'
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_EXPLORE_FEEDBACK_REMINDER : DataDotBase
    {
        public override string Event_name => "appear_explore_feedback_reminder";

        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //出现即时反馈解释说明窗口
    // tips_target: 解释的对象，枚举值参考下方UI文案key，当只解释一个对象时，枚举值为其中一个，当解释对象为两个时，用&连接，如ui_explore_xp_first&ui_explore_speaking_speed_below_80
    // UI文案key：
    // ui_explore_vocabulary_first
    // ui_explore_vocabulary_half_a1
    // ui_explore_vocabulary_half_a2
    // ui_explore_vocabulary_half_b1
    // ui_explore_vocabulary_half_b2
    // ui_explore_vocabulary_half_c1
    // ui_explore_vocabulary_half_c2
    // ui_explore_speaking_speed_below_80
    // ui_explore_speaking_speed_between_80_110
    // ui_explore_speaking_speed_between_110_160
    // ui_explore_speaking_speed_above_160
    // ui_explore_speaking_speed_close_native
    // ui_explore_xp_first
    // ui_explore_xp_daily_max
    //
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_EXPLORE_TIPS_POPUP : DataDotBase
    {
        public override string Event_name => "appear_explore_tips_popup";

        public string tips_target;
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //用户点击关闭即时反馈解释说明窗口
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_EXPLORE_TIPS_POPUP_CLOSE : DataDotBase
    {
        public override string Event_name => "click_explore_tips_popup_close";

        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //用户刷explore feed流
    // swipe_side:上滑或下滑，e.g. up（上滑）/down（下滑）
    // target_task_id:滑动之后的task id，若无则为空
    // previous_task_id:滑动前的task id，若无则为空
    // is_tab_bar: 底tab状态，e.g. appear（出现）/hide（隐藏）
    public class SWIPE_EXPLORE_FEED : DataDotBase
    {
        public override string Event_name => "swipe_explore_feed";

        public string swipe_side;
        public long target_task_id;
        public long previous_task_id;
        public string is_tab_bar;
    }
    
    //出现weak network提示
    public class APPEAR_EXPLORE_WEAK_NETWORK : DataDotBase
    {
        public override string Event_name => "appear_explore_weak_network";
    }
    
    //点击weak network提示的retry
    public class CLICK_EXPLORE_WEAK_NETWORK_RETRY : DataDotBase
    {
        public override string Event_name => "click_explore_weak_network_retry";
    }
    
    //出现继续聊天button
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_EXLPORE_CONTINUE_CHATTING : DataDotBase
    {
        public override string Event_name => "appear_exlpore_continue_chatting";
        
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //点击继续聊天button
    // task_id: 本条内容的id
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_EXLPORE_CONTINUE_CHATTING : DataDotBase
    {
        public override string Event_name => "click_exlpore_continue_chatting";
        
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //用户点击example和avatar文本中的单词翻译
    // target_word: 点击的单词
    // target_translation：出现的翻译文本
    // root_bubble: 来源气泡，e.g. avatar/example
    public class CLICK_EXPLORE_VOCAB_TRANSLATION : DataDotBase
    {
        public override string Event_name => "click_explore_vocab_translation";
        
        public string target_word;
        public string target_translation;
        public string root_bubble;
    }
    
    //在录音中途点击取消
    // task_id: 本条内容的id；
    // dialogue_id: 本次对话的id
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_EXPLORE_MIC_CANCEL : DataDotBase
    {
        public override string Event_name => "click_explore_mic_cancel";
        
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //下一条背景图加载1s时打点
    // target_task_id:滑动之后的task id，若无则为空
    // previous_task_id:滑动前的task id，若无则为空
    // is_tab_bar: 底tab状态，e.g. appear（出现）/hide（隐藏）
    public class CUT_EXPLORE_LOAD_1S : DataDotBase
    {
        public override string Event_name => "cut_explore_load_1s";
        
        public long target_task_id;
        public long previous_task_id;
        public string is_tab_bar;
    }

    //下一条背景图加载2s时打点
    // target_task_id:滑动之后的task id，若无则为空
    // previous_task_id:滑动前的task id，若无则为空
    // is_tab_bar: 底tab状态，e.g. appear（出现）/hide（隐藏）
    public class CUT_EXPLORE_LOAD_2S : DataDotBase
    {
        public override string Event_name => "cut_explore_load_2s";
        
        public long target_task_id;
        public long previous_task_id;
        public string is_tab_bar;
    }
    
    //下一条背景图加载大于等于3s时打点
    // target_task_id:滑动之后的task id，若无则为空
    // previous_task_id:滑动前的task id，若无则为空
    // is_tab_bar: 底tab状态，e.g. appear（出现）/hide（隐藏）
    public class CUT_EXPLORE_LOAD_3S : DataDotBase
    {
        public override string Event_name => "cut_explore_load_3s";
        
        public long target_task_id;
        public long previous_task_id;
        public string is_tab_bar;
    }

    #region rolePlay

    //显示总任务
    // task_id: 本条内容的id;
    // dialogue_id: 本次对话的id;
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_MAIN_GOAL : DataDotBase
    {
        public override string Event_name => "appear_main_goal";
        
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }

    //点击“展开”
    // task_id: 本条内容的id;
    // dialogue_id: 本次对话的id;
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_PULL_DOWN_BUTTON : DataDotBase
    {
        public override string Event_name => "click_pull_down_button";
        
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    //点击“合上”
    // goal_count：返回任务数量;
    // task_id: 本条内容的id;
    // dialogue_id: 本次对话的id;
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class CLICK_PULL_UP_BUTTON : DataDotBase
    {
        public override string Event_name => "click_pull_up_button";

        public int goal_count;
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }
    
    //显示所有的任务
    // goal_count：返回任务数量;
    // task_id: 本条内容的id;
    // dialogue_id: 本次对话的id;
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    public class APPEAR_GOAL_BOARD : DataDotBase
    {
        public override string Event_name => "appear_goal_board";

        public int goal_count;
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
    }

    //子目标进入完成状态
    // dialogue_id: 本次对话的id;
    // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
    // subgoal_id
    // subgoal_index:1/2/3/4/5....
    public class CUT_SUBGOAL_FINISHED : DataDotBase
    {
        public override string Event_name => "cut_subgoal_finished";
        
        public long task_id;
        public long dialogue_id;
        public long dialogue_round;
        public long subgoal_id;
        public long subgoal_index;

    }
    #endregion

    #region 商业化

    /// <summary>
    /// 点击倒计时按钮
    /// </summary>
    public class CLICK_EXPLORE_MAIN_PAGE_COUNTDOWN_ICON : DataDotBase
    {
        public override string Event_name => "click_explore_main_page_countdown_icon";
        
        public int countdown_time;
    }
    
    /// <summary>
    /// 点击unlock按钮
    /// </summary>
    public class CLICK_EXPLORE_MAIN_PAGE_UNLOCK_ICON : DataDotBase
    {
        public override string Event_name => "click_explore_main_page_unlock_icon";
        
    }

    /// <summary>
    /// 点击已被禁用的麦克风按钮
    /// </summary>
    public class CLICK_EXPLORE_MAIN_PAGE_NORECORD_BUTTON : DataDotBase
    {
        public override string Event_name => "click_explore_main_page_norecord_button";
        
    }
    #endregion
    
}
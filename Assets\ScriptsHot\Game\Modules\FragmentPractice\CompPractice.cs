using System;
using System.Collections.Generic;
using System.Linq;
using FairyGUI;
using Msg.question;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.FragmentPractice;
using UIBind.common;
using UnityEngine;

namespace UIBind.FragmentPractice
{
    public partial class CompPractice : ExtendedComponent
    {
        private static FragmentPracticeModel FragModel => ModelManager.instance.GetModel<FragmentPracticeModel>(ModelConsts.FragmentPractice);

        private static FragmentPracticeController FragController =>
            ControllerManager.instance.GetController<FragmentPracticeController>(ModelConsts.FragmentPractice);

        private readonly List<TextFieldExtension> _comTask = new List<TextFieldExtension>();
        private bool isCurrent;
        private APracticeData question;

        private AutoSizeLoader QuestionLoader => loaderQuestion as AutoSizeLoader;
        private AutoSizeLoader AnswerLoader => loaderAnswer as AutoSizeLoader;

        public void Init(bool isCurrent)
        {
            this.isCurrent = isCurrent;

            if (!isCurrent)
            {
                displayObject.cacheAsBitmap = true; // 一种优化策略
            }
        }

        #region refresh
        public void ShowPractice(APracticeData practice)
        {
            this.question = practice;
            if (AppConst.IsDebug)
            {
                tfQuestionId.visible = true;
                tfQuestionId.text = practice.QuestionId.ToString("#### #### #### #### ###0");
            }
            else
            {
                tfQuestionId.visible = false;
            }
            

            question.SetStartServerTime();

            var practiceIndex = FragModel.CurQuestionNum;
            if (!isCurrent)
            {
                practiceIndex = FragModel.CurQuestionNum + 1;
            }
            UnityEngine.Random.InitState(practiceIndex);

            string questionUrl = practice.GetQuestionComponentUrl();
            string answerUrl = practice.GetAnswerComponentUrl();
            
            QuestionLoader.Load(questionUrl, true);
            AnswerLoader.Load(answerUrl, true);

            var compQuestion = QuestionLoader.content as AFragQuestion;
            var compAnswer = AnswerLoader.content as AFragAnswer;


#if UNITY_EDITOR
    if (!string.IsNullOrEmpty(questionUrl) && compQuestion == null)
    {
        Debug.LogError($"Question 组件是不是没注册没注册 {questionUrl}");
    }
    if (!string.IsNullOrEmpty(answerUrl) && compAnswer == null)
    {
        Debug.LogError($"Answer 组件是不是没注册没注册{answerUrl}");
    }
#endif

            compQuestion?.Init(isCurrent, practice);
            compAnswer?.Init(isCurrent, practice);

            compQuestion?.ShowPractice(compAnswer);
            compAnswer?.ShowPractice(compQuestion);

            compQuestion?.EnsureBoundsCorrect();
            compAnswer?.EnsureBoundsCorrect();

            //
            if (isCurrent && compAnswer.HasInput)
            {
                onFocusIn.Add(OnFocusIn);
                onFocusOut.Add(OnFocusOut);
            }
            else
            {
                onFocusIn.Remove(OnFocusIn);
                onFocusOut.Remove(OnFocusOut);
            }

            previous.selectedIndex = 0;
            if (practice.QuestionTag != PB_QuestionTagType.NoneTag || FragModel.Round > 0)
            {
                previous.selectedIndex = 1;
                compTag.SetState(practice.QuestionTag, FragModel.Round);
            }
            
            tfQuestType.SetKey(practice.GetQuestionTypeLanguageKey());

            if (!isCurrent)
            {
                // 更新一下缓存内容
                TimerManager.instance.RegisterTimer(
                    (c) => { displayObject.cacheAsBitmap = true; }, 500
                );
            }
        }

        private void OnFocusOut(EventContext context)
        {
            BubbleEvent("onInputFocusOut", this);
        }

        private void OnFocusIn(EventContext context)
        {
            BubbleEvent("onInputFocusIn", this);

            // Find the first input in the answer component and give it focus
            if (AnswerLoader?.content is AFragAnswer answer)
            {
                // Get all input controls in the answer component
                var inputs = new List<GObject>();
                GetChildrenRecursive(answer, inputs);
                
                // Helper method to recursively get all child components
                void GetChildrenRecursive(GComponent component, List<GObject> result)
                {
                    for (int i = 0; i < component.numChildren; i++)
                    {
                        var child = component.GetChildAt(i);
                        if (child is GTextInput)
                            result.Add(child);
                        
                        if (child is GComponent childComponent)
                            GetChildrenRecursive(childComponent, result);
                    }
                }
                var input = inputs.FirstOrDefault(t=>t is GTextInput);
                input?.RequestFocus();
            }
        }

        #endregion

    }
}
﻿namespace Modules.DataDot
{
    public class DotClickFireIcon : DataDotBase
    {
        public override string Event_name => "click_home_top_bar_fire_icon";
        public int fire_count;//连胜火焰次数
    }
    
    public class DotAppearStartTips : DataDotBase
    {
        public override string Event_name => "appear_home_content_popup";
        public string title;
        public string information;
        public string reward;
        public string status;//"lock""unlock"
    }
    
    public class DotClickStartTipsStart : DataDotBase
    {
        public override string Event_name => "click_home_content_popup_start_button";
        public string title;
        public string information;
        public string reward;
        public string status;//"lock""unlock"
    }
    
    public class DotClickStartTipsSkip : DataDotBase
    {
        public override string Event_name => "click_home_content_popup_skip_button";
        public string title;
        public string information;
        public string reward;
        public string status;//"lock""unlock"
    }
    
    public class DotClickStartTipsReview : DataDotBase
    {
        public override string Event_name => "click_home_content_popup_review_button";
        public string title;
        public string information;
        public string reward;
        public string status;//"lock""unlock"
    }
    
    public class DotAppearMainPath : DataDotBase
    {
        public override string Event_name => "appear_home_path_page";
    }
    
    public class DotClickMainPathLevel : DataDotBase
    {
        public override string Event_name => "click_home_path_page_level_button";
        public long level_id;
    }
    
    public class DotClickSkipItemSkip : DataDotBase //路径最下面那个
    {
        public override string Event_name => "click_home_skip_panel_skip_button";
        public int unit_id;//实际是sectionid
        public int is_skip;//是否要跳
    }

    public class DotAppearHomeTransitionPage : DataDotBase
    {
        public override string Event_name => "appear_home_transition_page";
        public long skip_id;//跳关目标sectionid unitId
        public string skip_type;//跳关的类型（枚举值：unit/section）
    }
    
    public class DotClickSkipPanelStart : DataDotBase
    {
        public override string Event_name => "click_home_transition_page_test_button";
    }
    
    public class DotClickSkipPanelNo : DataDotBase
    {
        public override string Event_name => "click_home_transition_page_no_button";
    }
    
    public class DotAppearBookShelf : DataDotBase
    {
        public override string Event_name => "appear_home_course_page";
    }
    
    public class DotClickBookshelfSection : DataDotBase
    {
        public override string Event_name => "click_home_course_card";
        public long unit_id;//实际是sectionid
    }
    
    public class DotClickBookshelfSkip : DataDotBase
    {
        public override string Event_name => "click_home_course_card_skip_button";
        public long unit_id;//实际是sectionid
    }
    
    public class DotAppearJumpFalse : DataDotBase
    {
        public override string Event_name => "appear_motivation_fail_page";
    }
    
    public class DotClickJumpFalseContinue : DataDotBase
    {
        public override string Event_name => "click_motivation_fail_page_continue_button";
    }
    
    public class DotAppearSessionResultPage : DataDotBase
    {
        public override string Event_name => "appear_session_result_page";
        public string time;//做题时长(单位：秒)；
        public int exp;//做题经验
        public float acc;//做题准确率
        public long session_record_id;
        public string level_type;
    }
    
    public class DotClickSessionResultContinue : DataDotBase
    {
        public override string Event_name => "click_session_result_continue";
        public long session_record_id;
        public string level_type;
    }
    
    public class DotAppearSessionResultFeedbackPanel : DataDotBase
    {
        public override string Event_name => "appear_session_result_feedback_panel";
        public long session_record_id;
        public string level_type;
    }
    
    public class DotClickSessionResultFeedbackPanelButton : DataDotBase
    {
        public override string Event_name => "click_session_result_feedback_panel_button";
        public long session_record_id;
        public string level_type;
        public string button_type;
    }
    
    public class DotAppearSkipBar : DataDotBase
    {
        public override string Event_name => "appear_speak_skip_bar";
        public int shield_count;
    }
    
    public class DotAppearSessionTransitionalPage : DataDotBase
    {
        public override string Event_name => "appear_session_transitional_page";
        public string page_source;//过渡页来源（five_row/ten_row/com_on/correct_exercises）
    }
    
    public class DotClickSessionTransitionalContinue : DataDotBase
    {
        public override string Event_name => "click_session_transitional_continue";
        public string page_source;//过渡页来源（five_row/ten_row/com_on/correct_exercises）
    }
}
﻿using FairyGUI;
using Modules.DataDot;
using Msg.incentive;

namespace ScriptsHot.Game.Modules.Shop
{
    [TransitionUI(CutInEffect.BottomToTop)]
    public class PayWallHalfUI : BaseUI<UIBind.Shop.PayWallHalfPanel>,IBaseUIUpdate
    {
        public PayWallHalfUI(string name) : base(name)
        {
        }
        
        public override string uiLayer => UILayerConsts.Top;

        public ShopModel ShopModel => GetModel<ShopModel>(ModelConsts.Shop);
        
        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(ui.btnClose.onClick, OnClickClose);
            AddUIEvent(ui.btnGo.onClick, OnClickGo);

            ui.tfExpiring.SetKey("ui_pay_wall_expiring");
            ui.tfUnlimited.SetKey("ui_planBottomSubscribe_desc");
        }

        protected override void OnShow()
        {
            base.OnShow();

            DataDotMgr.Collect(new DotAppearMonetizationDiscountPopup());
            
            int discount = ShopUIUtil.GetSaleOffValue(ShopModel.PayWallData.subscription_infos[0]);
            ui.tfEnjoy.SetKeyArgs("ui_pay_wall_enjoy", $"{discount}%");
            ui.tfGo.SetKeyArgs("ui_pay_wall_btn_go", $"{discount}%");
            ui.tfDiscount.text = discount.ToString();
            
            MsgManager.instance.SendMsg(new CS_SetShowStateReq()
            {
                show_item_type = PB_ShowItemType.CommercialPopup,
            });

            GetController<HomepageController>(ModelConsts.Homepage).ShowHomepageTimeBanner();
        }

        protected override void OnHide()
        {
            base.OnHide();
        }

        protected override bool isFullScreen => true;

        public void Update(int interval)
        {
            long endTimeStamp = ShopModel.PayWallData?.end_milliseconds_in_utc ?? 0;
            if (endTimeStamp > TimeExt.serverTimestamp)
            {
                long curTimeStamp = TimeExt.serverTimestamp;
                long leftSeconds = (endTimeStamp - curTimeStamp) / 1000;
                if (leftSeconds < 0) leftSeconds = 0;
                int minutes = (int)(leftSeconds / 60);
                int seconds = (int)(leftSeconds % 60);
                ui.tfTime.text = $"{minutes:D2}:{seconds:D2}";
            }
            else
            {
                Hide();
            }
        }

        private void OnClickClose()
        {
            DataDotMgr.Collect(new DotClickMonetizationDiscountPopupExitButton());
            Hide();
        }

        private void OnClickGo()
        {
            DataDotMgr.Collect(new DotClickMonetizationDiscountPopupGetButton());
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);
            ShopModel.SetPayWallType();
            PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.discount_popup ;
            GetUI(UIConsts.SpeakPlanPromotionStep1UI).Show();
        }
    }
}
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class SimpleTextQuestion : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "SimpleTextQuestion";
        public static string url => "ui://cmoz5osjz7rm2g";

        public Controller audio;
        public GComponent tfQuestion;
        public BtnAudio btnPlayAudio;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(SimpleTextQuestion));
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            audio = GetControllerAt(0);
            tfQuestion = GetChildAt(0) as GComponent;
            btnPlayAudio = GetChildAt(1) as BtnAudio;
        }
        public override void Dispose()
        {
            audio = null;
            tfQuestion = null;
            btnPlayAudio = null;

            base.Dispose();
        }
    }
}
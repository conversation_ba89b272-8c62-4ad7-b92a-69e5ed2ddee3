﻿

using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Google.Protobuf;
using Grpc.Core;
using Msg;
using Msg.basic;
using Msg.explore;
using Msg.social;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Explore.ExploreType.Base;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using UnityEngine;

/// <summary>
/// 全双工 所有数据
/// </summary>
public class ExploreTwoWayNetStrategy:ExploreNetStrategy
{
    private bool _canSend = false;

    private bool _beginASRStream = false;
    public bool BeginASRStream => _beginASRStream;
    private AsyncDuplexStreamingCall<global::Msg.explore.CS_ExploreUpMsg, global::Msg.explore.SC_ExploreDownMsg>  _clientCall = null;


    public ExploreTwoWayNetStrategy() : base()
    {
    }
    public override void AddEvent()
    {
    }
    public override void RemoveEvent()
    {
    }

    protected override async void StartASRStream()
    {
        if (_beginASRStream) return;
        _beginASRStream = true;
        
        CancellationTokenSource rpcToken = new CancellationTokenSource();
        this._asrVO.rpcToken = rpcToken;
        ASRvo asrVO = this._asrVO;
        //
        if (_tokenSource != null)
        {
            _tokenSource.Cancel();
        }
        _tokenSource = rpcToken;
        
        VFDebug.Log("log--Explore 开始双工！！！！");
        
  
        if (_clientCall != null)
        {
            VFDebug.LogError("log--Explore _clientCall 已经存在 不可重复创建！关闭和创建是对应的 ，不应该存在重复创建， 请检查");
            return;
        }

        var client = GRPCManager.instance.GetGrpcClient<ExploreBizService.ExploreBizServiceClient>((short)GameMSGID.CS_ExploreUpMsg_ID);
        _clientCall = client.ExploreConnection(null, null, rpcToken.Token);
        //启动写入任务
        StartASRReqTask(asrVO);
        try
        {
            await foreach (SC_ExploreDownMsg result in _clientCall.ResponseStream.ReadAllAsync())
            {
                if (result.code != PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
                {
                    VFDebug.LogError("Explore数据报错：code::" + result.code + "  result.msgId::" + result.msgId);
                    if (asrVO.failCallback != null)
                        asrVO.failCallback(new ASRFailVO
                        {
                            status = ASRStatus.TimeShort,
                            desp = "ExploreNet",
                            extra = result.code.ToString()
                        }) ;
                    StopASRInternal(ASRStatus.ClientCancel);
                }
                else
                {
                    VFDebug.Log("收到消息：msgid::" + result.msgId);
                    MsgManager.instance.HandleMsg((short)result.msgId, result.data.ToByteArray());
                }
            }
        }
        catch (RpcException rpcEx)
        {
            if (rpcEx.StatusCode == StatusCode.Cancelled)
            {
                VFDebug.LogError("Explore gRPC 流被取消");
            }
            else if (rpcEx.StatusCode == StatusCode.Unavailable)
            {
                VFDebug.LogError("Explore gRPC 流不可用，可能是网络中断或应用后台切换");
                StopASRInternal(ASRStatus.ClientCancel);
            }
            else
            {
                VFDebug.LogError("Explore gRPC 流不可用，且没有确定的rpcEx.StatusCode ");
                asrVO.failCallback?.Invoke(new ASRFailVO { status = ASRStatus.Exception, extra = rpcEx.Message });
                StopASRInternal(ASRStatus.ClientCancel);
            }
            
            NetExit();
        }
        catch (Exception e)
        {
            VFDebug.LogError("Explore gRPC 流不可用 未知异常: " + e.Message);
            asrVO.failCallback?.Invoke(new ASRFailVO { status = ASRStatus.Exception,extra= e.Message });
        }
        finally
        {
            VFDebug.Log("Explore finally  _clientCall  CompleteAsync");
            if (_clientCall != null)
            {
                try
                {
                    await _clientCall.RequestStream.CompleteAsync();
                }
                catch (Exception e)
                {
                }
                _clientCall = null;
            }
        }
    }

    private void NetExit()
    {
        Debug.Log("Explore 断线了 NetExit");
        Notifier.instance.SendNotification(NotifyConsts.ExploreNoNetworkTipShow);
        
        Notifier.instance.SendNotification(NotifyConsts.ExploreNetExit);
      
        //不使用带确定按钮的弹窗
        // UIManager.instance.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("page-error-pop", () =>
        // {
        //     Notifier.instance.SendNotification(NotifyConsts.ExploreNetExit);
        // },null, 1);
    }

    private bool _testLogFlag = false;
    private void StartASRReqTask(ASRvo asrVO)
    {
        SceneController sCon = ControllerManager.instance.GetController(ModelConsts.Scene) as SceneController;
        
        Task.Run(async () =>
        {
            while (true)
            {
                await Task.Delay(ASRGapTime);
                
                if (_beginASRStream && _controller.IfCanMicrophoneSend) 
                {
                    int nowDataLength = this._micPhoneData.Length;
                    byte[] data = new byte[nowDataLength];
                    Array.Copy(this._micPhoneData, 0, data, 0, nowDataLength);
                    this._micPhoneData = new byte[0];
                    
                    if(data.Length == 0) continue;

                    ByteString strByte = ByteString.CopyFrom(data);
               
                    if (!_testLogFlag)
                    {
                        _testLogFlag = true;
                        VFDebug.Log("explore 音频发送长度::" + data.Length);
                    }

                    CS_ExploreUpMsg reqMsg = new CS_ExploreUpMsg
                    {
                        dialogMsg = new PB_DialogUpMsg()
                        {
                            userInputAudio = new PB_Explore_DialogAudioUpFrame()
                            {
                                audio = strByte,
                                sample_rate = (uint)GMicrophoneManager.instance.SampleRate,
                                num_channels = 1,
                            }, 
                            taskId = _controller.CurTaskId,
                            dialogMode = _controller.DialogMode,
                        },
                    };

                    this._asrVO.rpcClipIndex++;
                    await _clientCall?.RequestStream.WriteAsync(reqMsg);
                }
                else
                {
                    _testLogFlag = false;
                }
            }
            //---不能关闭流！！！
            // await clientCall.RequestStream.CompleteAsync();
            //追加强制检测，让底层吞掉的异常能再次暴露
            // var code = clientCall.GetStatus().StatusCode;
            // if (code != StatusCode.OK)
            // {
            //     throw new Exception("Grpc-Stream-CompleteAsFailed");
            // }
            // else
            // {
            //     VFDebug.Log("ast CompleteAsync succ ");
            // }
        });
    }

    /// <summary>
    /// 推荐上行消息
    /// </summary>
    /// <param name="entityId"></param>
    public override async void SendRecommendReq(long entityId)
    {
        SceneController sCon = ControllerManager.instance.GetController(ModelConsts.Scene) as SceneController;
        CS_ExploreUpMsg reqMsg = new CS_ExploreUpMsg
        {
            recommendMsg = new PB_RecommendUpMsg()
            {
                userSwitchEntity = new PB_Explore_RecommendSwitchEntity()
                {
                    entityId = entityId,
                    entityType = PB_Explore_RecommendEntityType.EO_RE_DIALOG_TASK,
                }, 
            },
        };
        
        try
        {
            await _clientCall?.RequestStream.WriteAsync(reqMsg);
        }
        catch (Exception e)
        {
            // 由 _clientCall 创建时候的 try catch 统一检测状态 ，这里处理会导致 多次触发 _clientCall的 创建
            // StopTwoway();
            // VFDebug.LogError("_clientCall SendRecommendReq，已经断开，重新链接");
            // Notifier.instance.SendNotification(NotifyConsts.ExploreNetConect);
        }
    }
    
    /// <summary>
    /// 对话上行消息
    /// </summary>
    /// <param name="type"></param>
    public override async void SendDialogReq(PB_Explore_DialogUpBizEvent type,long taskId,PB_DialogMode dialogMode)
    {
        VFDebug.Log($"log---Explore SendDialogReq，type:::{type}   taskId:::{taskId}   dialogMode:::{dialogMode}");
        SceneController sCon = ControllerManager.instance.GetController(ModelConsts.Scene) as SceneController;
        CS_ExploreUpMsg reqMsg = new CS_ExploreUpMsg
        {
            dialogMsg = new PB_DialogUpMsg()
            {
                upBizEvent = type,
                taskId = taskId,
                dialogMode = dialogMode,
            },
        };

        try
        {
            await _clientCall?.RequestStream.WriteAsync(reqMsg);
        }
        catch (Exception e)
        {
            // 由 _clientCall 创建时候的 try catch 统一检测状态 ，这里处理会导致 多次触发 _clientCall的 创建
            // StopTwoway();
            // VFDebug.LogError("_clientCall 已经断开，重新链接");
            // Notifier.instance.SendNotification(NotifyConsts.ExploreNetConect);
        }

    }
     #region 语音1v1部分，"userSettingMsg", "matchingMsg", "userChatMsg"
    /// <summary>
    /// 设置
    /// </summary>
    public override async void SendSetting()
    {
        SceneController sCon = ControllerManager.instance.GetController(ModelConsts.Scene) as SceneController;
        CS_ExploreUpMsg reqMsg = new CS_ExploreUpMsg
        {
            userSettingMsg = new PB_UserSettingUpMsg()
            {
                userSettingSave = new PB_Explore_UserSettingSave()
                {
                    speakingSpeed = _controller.GetSpeakingSpeed(),
                    autoTranslateDisplay = _controller.TempAutoTransform?Msg.explore.PB_Explore_UserSetting_AutoTranslateDisplay.EO_US_ATD_ON:Msg.explore.PB_Explore_UserSetting_AutoTranslateDisplay.EO_US_ATD_OFF,
                    backgroundMusic = _controller.TempOpenMusic?Msg.explore.PB_Explore_UserSetting_BackgroundMusic.EO_US_BM_ON:Msg.explore.PB_Explore_UserSetting_BackgroundMusic.EO_US_BM_OFF
                },
            },
        };

        try
        {
            await _clientCall?.RequestStream.WriteAsync(reqMsg);
        }
        catch (Exception e)
        {
            // 由 _clientCall 创建时候的 try catch 统一检测状态 ，这里处理会导致 多次触发 _clientCall的 创建
            // StopTwoway();
            // VFDebug.LogError("_clientCall SendSetting 设置失败，已经断开，重新链接");
            // Notifier.instance.SendNotification(NotifyConsts.ExploreNetConect);
        }
    }

    //多合一型消息，仅根据上传类别发送，不携带参数？
    //matchRecordId 只有部分消息需要
    public override async void SendMatchingMsg(PB_MatchingUpMsg.upBizMsgOneofCase msgCase, long preMatchRecordId)
    {
        VFDebug.Log($"log---Explore SendMatchingMsg");//，type:::{type}   taskId:::{taskId}   dialogMode:::{dialogMode}");

        //定义 内部属性，本身就是选择消息类别（upModMsgCase），这个类别值不需要单独赋予
        CS_ExploreUpMsg reqMsg = null;
        switch (msgCase)
        {
            //发起匹配（撮合）
            case PB_MatchingUpMsg.upBizMsgOneofCase.upFindPartnerReq:
                reqMsg = new CS_ExploreUpMsg
                {
                    
                    matchingMsg = new PB_MatchingUpMsg
                    {
                        //unused = 
                        upFindPartnerReq = new PB_MatchingUp_MatchStart() {
                            
                        }
                    },
                };
                break;

            //取消匹配
            case PB_MatchingUpMsg.upBizMsgOneofCase.upMatchCancelReq:
                reqMsg = new CS_ExploreUpMsg
                {
                    matchingMsg = new PB_MatchingUpMsg
                    {
                        upMatchCancelReq = new PB_MatchingUp_MatchCancel() {
                            match_record_id = preMatchRecordId
                        }
                    },
                };
                break;
            //上报 预撮合信息已收到，以做确认
            case PB_MatchingUpMsg.upBizMsgOneofCase.upPreMatchConfirmReq:
                if (preMatchRecordId <= 0) {
                    Debug.LogError("matchRecordId <0无效");
                }
                reqMsg = new CS_ExploreUpMsg
                {
                    matchingMsg = new PB_MatchingUpMsg
                    {
                        upPreMatchConfirmReq = new PB_MatchingUp_PreMatchConfirm() {
                            match_record_id = preMatchRecordId
                        }
                    },
                };
                break;

            case PB_MatchingUpMsg.upBizMsgOneofCase.upGetMatchStatusReq:
                reqMsg = new CS_ExploreUpMsg
                {
                    matchingMsg = new PB_MatchingUpMsg
                    {
                        upGetMatchStatusReq = new PB_MatchingUp_GetMatchStatus() {
                            match_record_id = preMatchRecordId
                        }
                    },
                };
                break;
            case PB_MatchingUpMsg.upBizMsgOneofCase.None:
            default:
                Debug.LogError("SendMatchingMsg 进入未定义的流程,未触发上行消息，后续流程被中断");
                break;
        }



      
        if (_clientCall == null)
        {
            VFDebug.LogError("防御动作 clientCall重新链接");
            Notifier.instance.SendNotification(NotifyConsts.ExploreNetConect);
        }
            
        try
        {
            if (reqMsg != null)
            {
                await _clientCall.RequestStream.WriteAsync(reqMsg);
            }
            else {
                throw new Exception("SendMatchingMsg in undefined flow");
            }
            
        }
        catch (Exception e)
        {
            StopTwoway();
            VFDebug.LogError("_clientCall 已经断开，重新链接,E="+e.ToString());
            Notifier.instance.SendNotification(NotifyConsts.ExploreNetConect);
        }
    }

    public override async void SendUserChatExitMsg(long selfUserId,long partnerUserId)//PB_Explore_DialogUpBizEvent type, long taskId, PB_DialogMode dialogMode)
    {
        SceneController sCon = ControllerManager.instance.GetController(ModelConsts.Scene) as SceneController;
        CS_ExploreUpMsg reqMsg = new CS_ExploreUpMsg
        {
               userChatMsg = new PB_UserChatUpMsg() {
                   userChatExit = new PB_Explore_UserChatExit() {
                       userChatId = selfUserId,
                       partnerUserId = partnerUserId
                   }
               }    
        };

        try
        {
            await _clientCall?.RequestStream.WriteAsync(reqMsg);
        }
        catch (Exception e)
        {
       
            VFDebug.LogError("_clientCall SendUserChatMsg 失败:"+e.ToString());
            
        }
    }

    //依靠AgoraRtc的事件机制不断触发，而不通过子线程的方式
    public override async void SendRawAudioUp(ByteString strByte)
    {
        CS_ExploreUpMsg reqMsg = new CS_ExploreUpMsg
        {
            userChatMsg = new PB_UserChatUpMsg()
            {
                userInputAudio = new PB_Explore_UserChatAudioUpFrame()
                {
                    audio = strByte,
                    sample_rate = (uint)ScriptsHot.Game.Modules.AgoraRtc.RtcEngineManager.SAMPLE_RATE,
                    num_channels = (uint)ScriptsHot.Game.Modules.AgoraRtc.RtcEngineManager.CHANNEL //一般里面用1, 1是单声道的含义
                },
            },
        };


        await _clientCall.RequestStream.WriteAsync(reqMsg);



    }
    #endregion

    public override void Stop()
    {
        StopASR();
        StopTwoway();
    }
    
    private async void StopTwoway()
    {
        _beginASRStream = false;
        if (_clientCall != null)
        {
            //这里 的try 目前没办法的办法，情况出现在 _clientCall已经断开 但是又不是 null的时候 ，如果不try  _clientCall.RequestStream.CompleteAsync()本身就会报错阻断
            try
            {
                await _clientCall.RequestStream.CompleteAsync();
            }
            catch (Exception e)
            {
            }
            _clientCall = null;
            VFDebug.Log("log---停止全双工！！！！！_clientCall = null");
        }
        
        VFDebug.Log("log---停止全双工！！！！！！！！！！！！！！！！！！！！！！！！！over");
    }

    public override void Update(int interval)
    {
        if (this._controller.IfCanMicrophone == false) return;
        base.Update(interval);
    }
    
    public AsyncDuplexStreamingCall<global::Msg.explore.CS_ExploreUpMsg, global::Msg.explore.SC_ExploreDownMsg> GetClientCell()
    {
        return _clientCall;
    }
}
using ScriptsHot.Game.Modules.Explore;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Procedure.Drama
{
    /// <summary>
    /// player cell 显示
    /// </summary>
    public class DramaExploreEntityPlayerCellShow : BaseDrama
    {
        public override void OnEvent()
        {
            base.OnEvent();
        }

        public override void Do(bool canFinish = true)
        {
            base.Do();

            Notifier.instance.SendNotification(NotifyConsts.ExploreChatPlayerShow);
            Notifier.instance.SendNotification(NotifyConsts.ExploreStepStateChange,ExploreStep.Player);
            
            TimerManager.instance.RegisterTimer((a) =>
            {
                Notifier.instance.SendNotification(NotifyConsts.ExploreChatPlayerShowOver);
                Finish(); 
            }, ExploreConst.PlayerCellToAvatarCellInterval);
        }

        public override void OnFinish(bool isBreak)
        {
            base.OnFinish(isBreak);
        }
        
        public override void Dispose()
        {
            base.Dispose();
        }
    }
} 
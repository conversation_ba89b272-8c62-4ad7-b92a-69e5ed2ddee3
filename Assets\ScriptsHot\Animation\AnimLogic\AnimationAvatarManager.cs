﻿using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;
using System.Collections;
using System.Drawing;
using System.Collections.Generic;
using System;
using Spine;
using UnityEngine.Serialization;

namespace AnimationSystem
{
    //未来作为一个Avatar的Animator的管理类,它分别管理三个管理类：头、身、面部表情。三个管理类分别Update
    public class AnimationAvatarManager : MonoBehaviour
    {
        #region 头身graph
        public Animator animator;
        public PlayableGraph playableGraph;
        private AnimationPlayableOutput playableOutput;
        private AnimationLayerMixerPlayable avatarLayerMixerPlayable;
        #endregion
        
        #region 表情graph
        public Animator emotionAnimator;
        public SkinnedMeshRenderer emotionSkinnedMeshRenderer;
        public PlayableGraph emotionGraph;
        private AnimationPlayableOutput emotionGraphAnimOutput;
        private ScriptPlayableOutput emotionGraphScriptOutput;
        #endregion
        
        #region avatar数据

        //女性男性
        [FormerlySerializedAs("sex")] public int animTypeId = 1;
        
        //角色使用的是哪种动画？
        public bool animIsGeneric = false;
        public SkinnedMeshRenderer bodySkinnedMeshRenderer; //如果是generic,那么需要身体SMR才能获取到骨骼。
        
        #endregion
        
        private bool isInitialized = false;
        public AnimState currentState;
        
        [SerializeField] private AvatarMask headMask;  // 头部的遮罩
        [SerializeField] private AvatarMask bodyMask;  // 身体的遮罩
        
        private AnimationHeadManager _animationHeadManager;
        private AnimationBodyManager _animationBodyManager;
        private AnimationEmotionManager _animationEmotionManager;
        
        //运行时数据
        public AvatarAnimationDataObject avatarAnimationDataObject;

        #region 业务逻辑
        
        // 看向功能的状态枚举
        private enum LookAtState
        {
            None,               // 无看向动作
            RotatingToTarget,   // 正在旋转到目标方向
            HoldingTarget,      // 保持在目标方向
            RotatingBack        // 正在旋转回初始位置
        }
        
        // 看向功能的状态变量
        private LookAtState _lookAtState = LookAtState.None;
        private Vector3 _lookAtDirection = Vector3.zero;
        private float _lookAtTransitionDuration = 0.5f;
        private float _lookAtHoldDuration = 2.0f;
        private float _lookAtTimer = 0f;
        
        /// <summary>
        /// 看向指定方向，持续一段时间后恢复
        /// </summary>
        /// <param name="direction">目标方向的欧拉角</param>
        /// <param name="transitionDuration">转向过渡时间（秒）</param>
        /// <param name="holdDuration">保持注视的时间（秒）</param>
        public void LookAtDirection(Vector3 direction, float transitionDuration, float holdDuration)
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法执行看向动作");
                return;
            }
            
            if (_animationHeadManager == null || !_animationHeadManager.isInitialized)
            {
                VFDebug.LogWarning("头部管理器未初始化，无法执行看向动作");
                return;
            }
            
            // 确保参数有效
            transitionDuration = Mathf.Max(0.1f, transitionDuration);
            holdDuration = Mathf.Max(0.0f, holdDuration);
            
            // 设置状态为自定义旋转
            _lookAtState = LookAtState.RotatingToTarget;
            _lookAtDirection = direction;
            _lookAtTransitionDuration = transitionDuration;
            _lookAtHoldDuration = holdDuration;
            _lookAtTimer = 0f;
            
            // 立即开始旋转到目标方向
            _animationHeadManager.SetCustomRotation(direction, transitionDuration);
            
            //VFDebug.Log($"开始看向动作: 方向={direction}, 过渡时间={transitionDuration}秒, 持续时间={holdDuration}秒");
        }
        
        /// <summary>
        /// 快速重置头部旋转到初始位置（0.2秒内完成）
        /// </summary>
        public void QuickResetHeadRotation()
        {
            if (!isInitialized)
            {
                return;
            }
            
            const float quickReturnDuration = 0.2f; // 快速返回的时间固定为0.2秒
            
            if (_animationHeadManager != null && _animationHeadManager.isInitialized)
            {
                // 立即开始旋转回初始位置，使用较短的过渡时间
                _animationHeadManager.SetCustomRotation(Vector3.zero, quickReturnDuration);
                
                // 如果当前正在执行LookAt动作，重置其状态
                if (_lookAtState != LookAtState.None)
                {
                    _lookAtState = LookAtState.None;
                    _lookAtTimer = 0f;
                    VFDebug.Log("快速重置头部旋转，取消当前看向动作");
                }
                else
                {
                    VFDebug.Log($"快速重置头部旋转，过渡时间={quickReturnDuration}秒");
                }
            }
        }
        
        // 处理看向功能的状态更新
        private void UpdateLookAtState()
        {
            if (_lookAtState == LookAtState.None) return;
            
            _lookAtTimer += Time.deltaTime;
            
            switch (_lookAtState)
            {
                case LookAtState.RotatingToTarget:
                    // 当旋转到目标的过渡完成时，进入保持状态
                    if (_lookAtTimer >= _lookAtTransitionDuration)
                    {
                        _lookAtState = LookAtState.HoldingTarget;
                        _lookAtTimer = 0f;
                        //VFDebug.Log("看向过渡完成，开始保持目标方向");
                    }
                    break;
                    
                case LookAtState.HoldingTarget:
                    // 当保持时间结束时，开始旋转回初始位置
                    if (_lookAtTimer >= _lookAtHoldDuration)
                    {
                        _lookAtState = LookAtState.RotatingBack;
                        _lookAtTimer = 0f;
                        _animationHeadManager.SetCustomRotation(Vector3.zero, _lookAtTransitionDuration);
                        //VFDebug.Log("保持时间结束，开始旋转回初始位置");
                    }
                    break;
                    
                case LookAtState.RotatingBack:
                    // 当旋转回初始位置完成时，重置状态
                    if (_lookAtTimer >= _lookAtTransitionDuration)
                    {
                        _lookAtState = LookAtState.None;
                        _lookAtTimer = 0f;
                        
                        //VFDebug.Log($"完成看向动作返回: 方向={Vector3.zero}, 过渡时间={_lookAtTransitionDuration}秒");
                    }
                    break;
            }
        }

        #endregion
        
        #region lifecycle
        // 检查是否满足初始化条件
        private bool CanInitialize()
        {
            if (animator == null)
            {
                VFDebug.LogWarning("无法初始化：Animator引用为null");
                return false;
            }
            
            if (headMask == null)
            {
                VFDebug.LogWarning("无法初始化：头部遮罩为null");
                return false;
            }
            
            // if (bodyMask == null)
            // {
            //     VFDebug.LogWarning("无法初始化：身体遮罩为null");
            //     return false;
            // }

            if (emotionSkinnedMeshRenderer == null)
            {
                VFDebug.LogWarning("无法初始化：头smr是null");
                return false;
            }

            if (avatarAnimationDataObject == null)
            {
                VFDebug.LogWarning("无法初始化：运行时数据是null");
                return false;
            }
            
            return true;
        }

        // 公共初始化方法，允许外部调用
        public bool InitializePlayableGraph()
        {
            if (isInitialized) return true;
            
            // 检查必要参数
            if (!CanInitialize())
            {
                return false;
            }
        
            playableGraph = PlayableGraph.Create("AvatarAnimationGraph");
            playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);
            
            //暂时先创建两个,因为目前只有头和身.表情之后再说
            avatarLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
        
            playableOutput = AnimationPlayableOutput.Create(playableGraph, "Output", animator);
            playableOutput.SetSourcePlayable(avatarLayerMixerPlayable);
            playableOutput.SetWeight(1.0f); // 确保输出权重为1
        
            playableGraph.Play();
            isInitialized = true;
        
            //VFDebug.Log($"PlayableGraph初始化完成: 输出有效={playableOutput.IsOutputValid()}, " +
            //          $"Animator={animator?.name ?? "null"}, Graph有效={playableGraph.IsValid()}");
            
            //然后初始化自身的Manager
            _animationBodyManager = new AnimationBodyManager();
            _animationBodyManager.Initialize(this, animator, playableGraph, avatarLayerMixerPlayable, playableOutput, bodyMask);
            
            //初始化头部管理器
            _animationHeadManager = new AnimationHeadManager();
            _animationHeadManager.Initialize(this, animator, playableGraph, avatarLayerMixerPlayable, playableOutput, headMask);
            
            emotionGraph = PlayableGraph.Create("EmotionAnimationGraph");
            emotionGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);
            
            emotionGraphAnimOutput = AnimationPlayableOutput.Create(emotionGraph, "Output", emotionAnimator);
            emotionGraphAnimOutput.SetWeight(1.0f);
            emotionGraphScriptOutput = ScriptPlayableOutput.Create(emotionGraph, "Script");
            emotionGraphScriptOutput.SetWeight(1.0f);
            
            emotionGraph.Play();

            _animationEmotionManager = new AnimationEmotionManager();
            _animationEmotionManager.Initialize(this, emotionAnimator, emotionSkinnedMeshRenderer,emotionGraph, emotionGraphAnimOutput, emotionGraphScriptOutput);
            return true;
        }

        private void Update()
        {
            if (!isInitialized) return;
            
            if (currentState != null)
            {
                currentState.OnUpdate();
            }
            
            _animationBodyManager.Update();
            _animationHeadManager.Update();
            // 更新表情管理器
            if (_animationEmotionManager != null && _animationEmotionManager.isInitialized)
            {
                _animationEmotionManager.Update();
            }
            
            // 处理看向功能的状态
            UpdateLookAtState();
        }

        private void OnDestroy()
        {
            if (!isInitialized) return;
            
            _animationBodyManager.OnDestroy();
            _animationHeadManager.OnDestroy();
            // 清理表情管理器资源
            if (_animationEmotionManager != null && _animationEmotionManager.isInitialized)
            {
                _animationEmotionManager.OnDestroy();
            }
            
            if (playableGraph.IsValid())
            {
                playableGraph.Destroy();
            }
            
            // 清理表情图形
            if (emotionGraph.IsValid())
            {
                emotionGraph.Destroy();
            }
        }

        private void OnEnable()
        {
            if (isInitialized && playableGraph.IsValid())
            {
                _animationBodyManager.OnEnable();
                _animationHeadManager.OnEnable();
                
                // 启用表情管理器
                if (_animationEmotionManager != null && _animationEmotionManager.isInitialized)
                {
                    _animationEmotionManager.OnEnable();
                }
                
                playableGraph.Play();
                
                // 启动表情图形
                if (emotionGraph.IsValid())
                {
                    emotionGraph.Play();
                }
            }
        }

        private void OnDisable()
        {
            if (isInitialized && playableGraph.IsValid())
            {
                _animationBodyManager.OnDisable();
                _animationHeadManager.OnDisable();
                
                // 禁用表情管理器
                if (_animationEmotionManager != null && _animationEmotionManager.isInitialized)
                {
                    _animationEmotionManager.OnDisable();
                }
                
                playableGraph.Stop();
                
                // 停止表情图形
                if (emotionGraph.IsValid())
                {
                    emotionGraph.Stop();
                }
            }
        }
        
        #endregion
        
        #region 数据SETTER
        // 设置新的Animator目标
        public void SetAnimator(Animator newAnimator)
        {
            if (newAnimator == animator) return; // 相同引用，无需处理
        
            VFDebug.Log($"切换Animator: {animator?.name ?? "null"} -> {newAnimator?.name ?? "null"}");
            animator = newAnimator;
        
            // 如果PlayableGraph已初始化，直接更新输出目标
            if (isInitialized && playableOutput.IsOutputValid())
            {
                playableOutput.SetTarget(animator);
            }
            
            //先检查是否初始化
            if (isInitialized)
            {
                if (_animationHeadManager != null && !_animationHeadManager.isInitialized)
                {
                    _animationHeadManager.Initialize(this, animator, playableGraph, avatarLayerMixerPlayable, playableOutput, headMask);
                }
                
                if (_animationBodyManager != null)
                {
                    _animationBodyManager.animator = newAnimator;
                }
                
                if (_animationHeadManager != null)
                {
                    _animationHeadManager.animator = newAnimator;
                }
            }
        }
        
        // 设置头部遮罩
        public void SetHeadMask(AvatarMask mask)
        {
            if (mask == headMask) return; // 相同引用，无需处理
            
            //VFDebug.Log($"设置头部遮罩: {mask?.name ?? "null"}");
            headMask = mask;
            
            // 如果已初始化，需要更新遮罩
            if (isInitialized && _animationHeadManager != null && _animationHeadManager.isInitialized)
            {
                // 这里暂时不实现运行时更换遮罩的逻辑，只是打印日志
                VFDebug.LogWarning("运行时更换头部遮罩暂不支持，请重新初始化");
            }
        }
        
        // 设置身体遮罩
        public void SetBodyMask(AvatarMask mask)
        {
            if (mask == bodyMask) return; // 相同引用，无需处理
            
            //VFDebug.Log($"设置身体遮罩: {mask?.name ?? "null"}");
            bodyMask = mask;
            
            // 如果已初始化，调用BodyManager的方法来更新遮罩
            if (isInitialized && _animationBodyManager != null && _animationBodyManager.isInitialized)
            {
                // 调用BodyManager的SetAvatarMask方法来动态更新遮罩
                bool success = _animationBodyManager.SetAvatarMask(mask);
                if (!success)
                {
                    VFDebug.LogWarning("运行时更新身体遮罩失败，遮罩可能在下次初始化时生效");
                }
            }
        }
        
        // 获取初始化状态
        public bool IsInitialized()
        {
            return isInitialized;
        }
        
        // 设置新的动画状态
        public void SetState(AnimState newState)
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法设置动画状态");
                return;
            }
            
            if (currentState != null)
            {
                currentState.OnExit();
            }

            currentState = newState;
            
            if (currentState != null)
            {
                currentState.manager = this;
                currentState.OnEnter();
            }
            
            this._animationBodyManager.SetAnimState(currentState);
        }

        public void SetAnimDataObject(AvatarAnimationDataObject newData)
        {
            this.avatarAnimationDataObject = newData;
        }
        
        #endregion
        
        #region 身体控制
        public void PlayAnimGroupData(AnimGroupPlayData data, bool isDefault = false,float duration = 0.5f)
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法播放动画");
                return;
            }
            
            this._animationBodyManager.PlayAnimGroupData(data, duration,isDefault);
        }
        
        #endregion
        
        #region HeadControl
        
        // 设置头部旋转
        public void SetHeadRotation(Vector3 rotation)
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法设置头部旋转");
                return;
            }
            
            if (_animationHeadManager != null && isInitialized)
            {
                _animationHeadManager.SetHeadRotation(rotation);
            }
        }
        
        // 设置头部位置偏移
        public void SetHeadPositionOffset(Vector3 offset)
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法设置头部位置偏移");
                return;
            }
            
            if (_animationHeadManager != null && isInitialized)
            {
              //  _animationHeadManager.SetHeadPositionOffset(offset);
            }
        }
        
        // 设置头部旋转过渡时间
        public void SetHeadRotationTransitionTime(float frameDuration)
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法设置头部旋转过渡时间");
                return;
            }
            
            if (_animationHeadManager != null && isInitialized)
            {
                _animationHeadManager.SetHeadRotationTransitionTime(frameDuration);
            }
        }
        
        // 设置头部随机旋转间隔
        public void SetHeadRandomInterval(int frameInterval)
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法设置头部随机旋转间隔");
                return;
            }
            
            if (_animationHeadManager != null && isInitialized)
            {
                _animationHeadManager.SetHeadRandomInterval(frameInterval);
            }
        }
        
        // 设置头部随机种子
        public void SetHeadRandomSeed(uint seed)
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法设置头部随机种子");
                return;
            }
            
            if (_animationHeadManager != null && isInitialized)
            {
                _animationHeadManager.SetHeadRandomSeed(seed);
            }
        }
        
        /// <summary>
        /// 触发头部随机旋转(人体工学)
        /// </summary>
        /// <param name="speed">旋转速度(0.3-3.0之间,1.0为正常速度)</param>
        public Vector3 SetHeadRandomHumanoidRotation(float speed = 1.0f, float neutralBonus = 0.0f)
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法设置头部随机旋转");
                return Vector3.zero;
            }
            
            if (_animationHeadManager != null && isInitialized)
            {
                return _animationHeadManager.SetHeadRandomHumanoidRotation(speed,neutralBonus);
            }
            else return Vector3.zero;
        }
        
        /// <summary>
        /// 设置头部自定义旋转
        /// </summary>
        /// <param name="rotation">旋转角度（欧拉角）</param>
        /// <param name="durationInSeconds">过渡时间（秒）</param>
        public void SetHeadCustomRotation(Vector3 rotation, float durationInSeconds)
        {
            if (_animationHeadManager == null || !_animationHeadManager.isInitialized)
            {
                VFDebug.LogWarning("AnimationHeadManager未初始化，无法设置头部自定义旋转");
                return;
            }
            
            _animationHeadManager.SetCustomRotation(rotation, durationInSeconds);
        }
        
        /// <summary>
        /// 设置头部旋转的贝塞尔曲线参数
        /// </summary>
        /// <param name="x1">第一个控制点X坐标</param>
        /// <param name="y1">第一个控制点Y坐标</param>
        /// <param name="x2">第二个控制点X坐标</param>
        /// <param name="y2">第二个控制点Y坐标</param>
        public void SetHeadBezierCurveParameters(float x1, float y1, float x2, float y2)
        {
            if (_animationHeadManager == null || !_animationHeadManager.isInitialized)
            {
                VFDebug.LogWarning("AnimationHeadManager未初始化，无法设置贝塞尔曲线参数");
                return;
            }
            
            _animationHeadManager.SetBezierCurveParameters(x1, y1, x2, y2);
        }
        
        /// <summary>
        /// 设置头部旋转的贝塞尔曲线类型
        /// </summary>
        /// <param name="curveType">曲线类型</param>
        public void SetHeadBezierCurveType(BezierCurveType curveType)
        {
            if (_animationHeadManager == null || !_animationHeadManager.isInitialized)
            {
                VFDebug.LogWarning("AnimationHeadManager未初始化，无法设置贝塞尔曲线类型");
                return;
            }
            
            _animationHeadManager.SetBezierCurveType(curveType);
        }
        
        // 执行点头动作
        public void PerformHeadNodding(float speed = 1.0f, int loopCount = 3)
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法执行点头动作");
                return;
            }
            
            if (_animationHeadManager != null && isInitialized)
            {
                _animationHeadManager.PerformNodding(speed, loopCount);
            }
        }
        
        // 执行摇头动作
        public void PerformHeadShaking(float speed = 1.0f, int loopCount = 3)
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法执行摇头动作");
                return;
            }
            
            if (_animationHeadManager != null && isInitialized)
            {
                _animationHeadManager.PerformShaking(speed, loopCount);
            }
        }
        
        /// <summary>
        /// 启动头部随机移动
        /// </summary>
        public void StartRandomMove()
        {
            if (!isInitialized)
            {
                VFDebug.LogWarning("AnimationAvatarManager未初始化，无法启动头部随机移动");
                return;
            }
            
            if (_animationHeadManager != null && isInitialized)
            {
                _animationHeadManager.StartRandomMove();
            }
        }
        
        #endregion
        
        #region 表情控制
        /// <summary>
        /// 播放表情动画
        /// </summary>
        /// <param name="clip">要播放的表情动画片段</param>
        public void PlayEmotionAnimation(AnimationClip clip)
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法播放表情动画");
                return;
            }
            
            _animationEmotionManager.PlayAnimation(clip);
          //  VFDebug.Log($"播放表情动画: {clip?.name ?? "null"}");
        }
        
        #region 眉毛控制
        /// <summary>
        /// 设置眉毛控制模式
        /// </summary>
        /// <param name="mode">控制模式：Random为随机控制，Curve为曲线控制</param>
        public void SetBrowControlMode(EmotionSB.BrowControlMode mode)
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法设置眉毛控制模式");
                return;
            }
            
            _animationEmotionManager.SetBrowControlMode(mode);
           // VFDebug.Log($"设置眉毛控制模式: {mode}");
        }

        /// <summary>
        /// 设置眉毛随机模式数据
        /// </summary>
        /// <param name="data">眉毛随机模式数据，包括能量恢复速度、能量区间配置等</param>
        public void SetBrowData(EmotionSB.BrowRandomModeData data)
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法设置眉毛随机模式数据");
                return;
            }
            
            _animationEmotionManager.SetBrowData(data);
        //    VFDebug.Log($"设置眉毛随机模式数据: 能量恢复速度={data.energyRegenSpeed}, 能量区间数量={data.energyRanges?.Count ?? 0}");
        }

        /// <summary>
        /// 设置眉毛曲线
        /// </summary>
        /// <param name="curve">动画曲线</param>
        /// <param name="duration">动画持续时间（秒）</param>
        /// <param name="loop">是否循环播放</param>
        public void SetBrowCurve(AnimationCurve curve, float duration, bool loop = true)
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法播放眉毛曲线动画");
                return;
            }
            
            _animationEmotionManager.SetBrowCurve(curve, duration, loop);
           // VFDebug.Log($"设置眉毛曲线动画: 持续时间={duration}秒, 循环={loop}");
        }

        /// <summary>
        /// 设置眉毛模式权重
        /// </summary>
        /// <param name="mode">控制模式：Random为随机控制，Curve为曲线控制</param>
        /// <param name="weight">权重值(0-1之间)</param>
        /// <param name="transitionDuration">过渡时间(秒)</param>
        public void SetBrowModeWeight(EmotionSB.BrowControlMode mode, float weight, float transitionDuration = 0.2f)
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法设置眉毛模式权重");
                return;
            }
            
            _animationEmotionManager.SetBrowModeWeight(mode, weight, transitionDuration);
       //     VFDebug.Log($"设置眉毛{mode}模式权重: {weight}, 过渡时间={transitionDuration}秒");
        }

        /// <summary>
        /// 停止眉毛曲线动画
        /// </summary>
        public void StopBrowCurve()
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法停止眉毛曲线动画");
                return;
            }
            
            _animationEmotionManager.StopBrowCurve();
          //  VFDebug.Log("停止眉毛曲线动画");
        }
        #endregion
        
        #region 嘴巴控制
        
        /// <summary>
        /// 启用/禁用嘴巴控制
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void SetMouthControlEnabled(bool enabled)
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法设置嘴巴控制状态");
                return;
            }
            
            _animationEmotionManager.SetMouthControlEnabled(enabled);
          //  VFDebug.Log($"设置嘴巴控制状态: {(enabled ? "启用" : "禁用")}");
        }
        
        /// <summary>
        /// 播放嘴巴曲线动画
        /// </summary>
        /// <param name="curve">动画曲线，值范围0-1之间</param>
        /// <param name="duration">动画持续时间（秒）</param>
        /// <param name="loop">是否循环播放</param>
        public void SetMouthCurve(AnimationCurve curve, float duration, bool loop = true)
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法播放嘴巴曲线动画");
                return;
            }
            
            _animationEmotionManager.PlayMouthCurve(curve, duration, loop);
           // VFDebug.Log($"播放嘴巴曲线动画: 持续时间={duration}秒, 循环={loop}");
        }
        
        /// <summary>
        /// 停止嘴巴曲线动画
        /// </summary>
        public void StopMouthCurve()
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法停止嘴巴曲线动画");
                return;
            }
            
            _animationEmotionManager.StopMouthCurve();
           // VFDebug.Log("停止嘴巴曲线动画");
        }
        
        #endregion
        
        #region 眨眼控制
        
        /// <summary>
        /// 设置眨眼参数
        /// </summary>
        /// <param name="duration">眨眼持续时间（秒）</param>
        /// <param name="minInterval">最小间隔时间（秒）</param>
        /// <param name="maxInterval">最大间隔时间（秒）</param>
        public void SetBlinkParameters(float duration, float minInterval, float maxInterval)
        {
            if (!isInitialized || _animationEmotionManager == null) 
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法设置眨眼参数");
                return;
            }
            
            _animationEmotionManager.SetBlinkParameters(duration, minInterval, maxInterval);
        }
        
        /// <summary>
        /// 立即触发一次眨眼
        /// </summary>
        public void TriggerBlink()
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法触发眨眼");
                return;
            }
            
            _animationEmotionManager.TriggerBlink();
        }
        
        /// <summary>
        /// 启用/禁用自动眨眼
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void SetAutoBlinkEnabled(bool enabled)
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法设置自动眨眼状态");
                return;
            }
            
            _animationEmotionManager.SetAutoBlinkEnabled(enabled);
        }
        
        #endregion
        
        #region 眼动控制
        
        /// <summary>
        /// 启用/禁用眼动自动控制
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void SetEyeControlEnabled(bool enabled)
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法设置眼动控制状态");
                return;
            }
            
            _animationEmotionManager.SetEyeControlEnabled(enabled);
         //   VFDebug.Log($"设置眼动控制状态: {(enabled ? "启用" : "禁用")}");
        }
        
        /// <summary>
        /// 手动设置眼睛位置
        /// </summary>
        /// <param name="position">眼睛位置，x为水平方向(-2到2度)，y为垂直方向(-2到2度)</param>
        public void SetEyePosition(Vector2 position)
        {
            if (!isInitialized || _animationEmotionManager == null)
            {
                VFDebug.LogWarning("AnimationEmotionManager未初始化，无法设置眼睛位置");
                return;
            }
            
            _animationEmotionManager.SetEyePosition(position);
         //   VFDebug.Log($"设置眼睛位置: {position}");
        }
        #endregion
        
        #endregion

        // 设置表情Animator
        public void SetEmotionAnimator(Animator emotionAnim)
        {
            if (emotionAnim == emotionAnimator) return; // 相同引用，无需处理
            
            VFDebug.Log($"切换表情Animator: {emotionAnimator?.name ?? "null"} -> {emotionAnim?.name ?? "null"}");
            emotionAnimator = emotionAnim;
            
            if (isInitialized && _animationEmotionManager != null && _animationEmotionManager.isInitialized)
            {
                _animationEmotionManager.emotionAnimator = emotionAnim;
                
                // 更新输出目标
                if (emotionGraph.IsValid() && emotionGraphAnimOutput.IsOutputValid())
                {
                    emotionGraphAnimOutput.SetTarget(emotionAnim);
                }
            }
        }
        
        // 设置表情网格渲染器
        public void SetEmotionSkinnedMeshRenderer(SkinnedMeshRenderer emotionSMR)
        {
            if (emotionSMR == emotionSkinnedMeshRenderer) return; // 相同引用，无需处理
            
           VFDebug.Log($"切换表情网格渲染器: {emotionSkinnedMeshRenderer?.name ?? "null"} -> {emotionSMR?.name ?? "null"}");
            emotionSkinnedMeshRenderer = emotionSMR;
            
            if (isInitialized && _animationEmotionManager != null && _animationEmotionManager.isInitialized)
            {
                _animationEmotionManager.emotionMeshRenderer = emotionSMR;
            }
        }

        #region 头部旋转事件
        public event Action<Vector3, Vector3, float> OnHeadRotationChanged; // 参数1: 当前位置, 参数2: 目标位置, 参数3: 旋转时间
        
        public void TriggerHeadRotationEvent(Vector3 currentRotation, Vector3 targetRotation, float duration)
        {
            OnHeadRotationChanged?.Invoke(currentRotation, targetRotation, duration);
        }
        #endregion
        
        #region debugger

        public void TriggerDebugger()
        {
            if (_animationBodyManager != null)
            {
                _animationBodyManager.trigger = true;
            }
        }
        
        #endregion
    }
}
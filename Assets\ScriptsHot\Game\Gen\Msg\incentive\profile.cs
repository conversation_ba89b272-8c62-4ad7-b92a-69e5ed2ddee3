// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/incentive/profile.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.incentive {

  /// <summary>Holder for reflection information generated from protobuf/incentive/profile.proto</summary>
  public static partial class ProfileReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/incentive/profile.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ProfileReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiBwcm90b2J1Zi9pbmNlbnRpdmUvcHJvZmlsZS5wcm90bxofcHJvdG9idWYv",
            "aW5jZW50aXZlL2dyb3d0aC5wcm90bxoecHJvdG9idWYvYmFzaWMvaW5jZW50",
            "aXZlLnByb3RvGhtwcm90b2J1Zi9iYXNpYy9jb21tb24ucHJvdG8iJwoUQ1Nf",
            "R2V0VXNlclByb2ZpbGVSZXESDwoHdXNlcl9pZBgBIAEoAyK4BwoZU0NfR2V0",
            "VXNlclByb2ZpbGVSZXNwb25zZRIPCgd1c2VyX2lkGAEgASgDEh8KF3Rhc2tf",
            "ZmluaXNoZWRfdG90YWxfY250GAQgASgFEiIKGnRhc2tfZmluaXNoZWRfZGlz",
            "dGluY3RfY250GAUgASgFEh0KFWV4ZXJjaXNlX2ZpbmlzaGVkX2NudBgGIAEo",
            "BRIeChZ0aW1lX3NwZW5kX29uX2xlYXJuaW5nGAcgASgFEhUKDXRhc2tfc3Rh",
            "cl9jbnQYCCABKAUSGgoSdGFza19hdmVyYWdlX3Njb3JlGAkgASgCEiQKHHRh",
            "c2tfYXZlcmFnZV9zY29yZV9wcm9ub3VuY2UYCiABKAISJQoddGFza19hdmVy",
            "YWdlX3Njb3JlX2ludG9uYXRpb24YCyABKAISIgoadGFza19hdmVyYWdlX3Nj",
            "b3JlX2ZsdWVuY3kYDCABKAISIwobdGFza19hdmVyYWdlX3Njb3JlX3JpY2hu",
            "ZXNzGA0gASgCEiQKHHRhc2tfYXZlcmFnZV9zY29yZV9pbnRlZ3JpdHkYDiAB",
            "KAISGgoSaXNfcHJvZmlsZV92aXNpYmxlGA8gASgFEhAKCGVycl9jb2RlGBAg",
            "ASgFEjMKEXZvaWNlX2luZm9ybWF0aW9uGBEgASgLMhguUEJfVXNlclZvaWNl",
            "SW5mb3JtYXRpb24SEAoIaGVhZF91cmwYEiABKAkSEAoIc3R5bGVfaWQYEyAB",
            "KAkSGAoQdG90YWxfZXhwZXJpZW5jZRgUIAEoBRIdChVjaGVja2luX2NvbnRp",
            "bnVlX2RheXMYFSABKAUSHwoJcmFua19pbmZvGBYgASgLMgwuUEJfUmFua0lu",
            "Zm8SJwoLZ3Jvd3RoX2RhdGEYFyABKAsyEi5QQl9Vc2VyR3Jvd3RoRGF0YRIr",
            "Cg1wcm9ncmVzc19zdGF0GBggASgLMhQuUEJfVXNlclByb2dyZXNzU3RhdBIm",
            "Cg1kcmVzc191cF9kYXRhGBkgASgLMg8uUEJfRHJlc3NVcERhdGESFAoMaGVh",
            "ZF9pdGVtX2lkGBogASgDEigKDmhlYWRfaXRlbV90eXBlGBsgASgOMhAuUEJf",
            "SGVhZEl0ZW1UeXBlEhgKEGhhc19jcmVhdGVkX3JvbGUYHCABKAgSLwoPZnJp",
            "ZW5kc2hpcF9pbmZvGB0gASgLMhYuUEJfVXNlckZyaWVuZHNoaXBJbmZvEhMK",
            "C3BsYXllcl9uYW1lGB4gASgJEhgKEHJvbGVfdW5pcXVlX25hbWUYHyABKAki",
            "PQoLUEJfUmFua0luZm8SFQoNY3VycmVudF9sZXZlbBgBIAEoBRIXCg9pc19s",
            "ZXZlbF91bmxvY2sYAiABKAgiZQoXUEJfVXNlclZvaWNlSW5mb3JtYXRpb24S",
            "HQoVaGFzX3ZvaWNlX2luZm9ybWF0aW9uGAEgASgIEhkKEXZvaWNlX3RpbWVf",
            "bGVuZ3RoGAIgASgFEhAKCHZvaWNlX2lkGAMgASgDIhQKEkNTX0dldE15UHJv",
            "ZmlsZVJlcSJGChdTQ19HZXRNeVByb2ZpbGVSZXNwb25zZRIMCgRjb2RlGAEg",
            "ASgFEh0KBGRhdGEYAiABKAsyDy5QQl9Vc2VyUHJvZmlsZSKbBwoOUEJfVXNl",
            "clByb2ZpbGUSDwoHdXNlcl9pZBgBIAEoAxIfChd0YXNrX2ZpbmlzaGVkX3Rv",
            "dGFsX2NudBgEIAEoBRIiChp0YXNrX2ZpbmlzaGVkX2Rpc3RpbmN0X2NudBgF",
            "IAEoBRIdChVleGVyY2lzZV9maW5pc2hlZF9jbnQYBiABKAUSHgoWdGltZV9z",
            "cGVuZF9vbl9sZWFybmluZxgHIAEoBRIVCg10YXNrX3N0YXJfY250GAggASgF",
            "EhoKEnRhc2tfYXZlcmFnZV9zY29yZRgJIAEoAhIkChx0YXNrX2F2ZXJhZ2Vf",
            "c2NvcmVfcHJvbm91bmNlGAogASgCEiUKHXRhc2tfYXZlcmFnZV9zY29yZV9p",
            "bnRvbmF0aW9uGAsgASgCEiIKGnRhc2tfYXZlcmFnZV9zY29yZV9mbHVlbmN5",
            "GAwgASgCEiMKG3Rhc2tfYXZlcmFnZV9zY29yZV9yaWNobmVzcxgNIAEoAhIk",
            "Chx0YXNrX2F2ZXJhZ2Vfc2NvcmVfaW50ZWdyaXR5GA4gASgCEhoKEmlzX3By",
            "b2ZpbGVfdmlzaWJsZRgPIAEoBRIzChF2b2ljZV9pbmZvcm1hdGlvbhgQIAEo",
            "CzIYLlBCX1VzZXJWb2ljZUluZm9ybWF0aW9uEhAKCGhlYWRfdXJsGBEgASgJ",
            "EhAKCHN0eWxlX2lkGBIgASgJEhgKEHRvdGFsX2V4cGVyaWVuY2UYEyABKAUS",
            "HQoVY2hlY2tpbl9jb250aW51ZV9kYXlzGBQgASgFEh8KCXJhbmtfaW5mbxgV",
            "IAEoCzIMLlBCX1JhbmtJbmZvEicKC2dyb3d0aF9kYXRhGBYgASgLMhIuUEJf",
            "VXNlckdyb3d0aERhdGESKwoNcHJvZ3Jlc3Nfc3RhdBgXIAEoCzIULlBCX1Vz",
            "ZXJQcm9ncmVzc1N0YXQSJgoNZHJlc3NfdXBfZGF0YRgZIAEoCzIPLlBCX0Ry",
            "ZXNzVXBEYXRhEhQKDGhlYWRfaXRlbV9pZBgaIAEoAxIoCg5oZWFkX2l0ZW1f",
            "dHlwZRgbIAEoDjIQLlBCX0hlYWRJdGVtVHlwZRIYChBoYXNfY3JlYXRlZF9y",
            "b2xlGBwgASgIEi8KD2ZyaWVuZHNoaXBfaW5mbxgdIAEoCzIWLlBCX1VzZXJG",
            "cmllbmRzaGlwSW5mbxITCgtwbGF5ZXJfbmFtZRgeIAEoCRIYChByb2xlX3Vu",
            "aXF1ZV9uYW1lGB8gASgJInkKFVBCX1VzZXJGcmllbmRzaGlwSW5mbxIXCg9m",
            "b2xsb3dpbmdfY291bnQYASABKAUSFgoOZm9sbG93ZXJfY291bnQYAiABKAUS",
            "LwoTcmVsYXRpb25zaGlwX3N0YXR1cxgDIAEoDjISLlBCX0ZyaWVuZHNoaXBU",
            "eXBlIkwKHUNTX1NldFVzZXJQcm9maWxlQXV0aG9yaXR5UmVxEg8KB3VzZXJf",
            "aWQYASABKAMSGgoSaXNfcHJvZmlsZV92aXNpYmxlGAIgASgFIjIKHlNDX1Nl",
            "dFVzZXJQcm9maWxlQXV0aG9yaXR5UmVzcBIQCghlcnJfY29kZRgBIAEoBSIw",
            "Ch1DU19HZXRVc2VyUHJvZmlsZUF1dGhvcml0eVJlcRIPCgd1c2VyX2lkGAEg",
            "ASgDIk0KHlNDX0dldFVzZXJQcm9maWxlQXV0aG9yaXR5UmVzcBIPCgd1c2Vy",
            "X2lkGAEgASgDEhoKEmlzX3Byb2ZpbGVfdmlzaWJsZRgCIAEoBSI3ChJDU19T",
            "ZW5kRmVlZGJhY2tSZXESDwoHdXNlcl9pZBgBIAEoAxIQCghmZWVkYmFjaxgC",
            "IAEoCSInChNTQ19TZW5kRmVlZGJhY2tSZXNwEhAKCGVycl9jb2RlGAEgASgF",
            "IkwKHUNTX1NldFVzZXJWb2ljZUluZm9ybWF0aW9uUmVxEhAKCHZvaWNlX2lk",
            "GAEgASgDEhkKEXZvaWNlX3RpbWVfbGVuZ3RoGAIgASgFIjEKHVNDX1NldFVz",
            "ZXJWb2ljZUluZm9ybWF0aW9uQWNrEhAKCGVycl9jb2RlGAEgASgFItsCChZD",
            "U19TZXRVc2VyUG9ydHJhaXRzUmVxEhkKDWludGVyZXN0X2dvYWwYASABKAlC",
            "AhgBEhsKD2ludGVyZXN0X3NjZW5lcxgCIAMoCUICGAESFgoObGFuZ3VhZ2Vf",
            "bGV2ZWwYAyABKAkSLgoQaW50ZXJlc3RfZ29hbF9pZBgEIAEoDjIULlBCX0xl",
            "YXJuaW5nR29hbEVudW0SMQoSaW50ZXJlc3Rfc2NlbmVfaWRzGAUgAygOMhUu",
            "UEJfTGVhcm5pbmdTY2VuZUVudW0SHgoHaG9iYmllcxgGIAMoDjINLlBCX0hv",
            "YmJ5RW51bRI0ChZleHBlY3RlZF9sZWFybmluZ190aW1lGAcgASgOMhQuUEJf",
            "TGVhcm5pbmdQbGFuRW51bRIeCgZnZW5kZXIYCCABKA4yDi5QQl9HZW5kZXJF",
            "bnVtEhgKA2FnZRgJIAEoDjILLlBCX0FnZUVudW0iKgoWU0NfU2V0VXNlclBv",
            "cnRyYWl0c0FjaxIQCghlcnJfY29kZRgBIAEoBSIYChZDU19HZXRVc2VyUG9y",
            "dHJhaXRzUmVxIksKFlNDX0dldFVzZXJQb3J0cmFpdHNBY2sSEAoIZXJyX2Nv",
            "ZGUYASABKAUSHwoEZGF0YRgCIAEoCzIRLlBCX1VzZXJQb3J0cmFpdHMixQEK",
            "EFBCX1VzZXJQb3J0cmFpdHMSGQoNaW50ZXJlc3RfZ29hbBgBIAEoCUICGAES",
            "GwoPaW50ZXJlc3Rfc2NlbmVzGAIgAygJQgIYARIWCg5sYW5ndWFnZV9sZXZl",
            "bBgDIAEoCRIuChBpbnRlcmVzdF9nb2FsX2lkGAQgASgOMhQuUEJfTGVhcm5p",
            "bmdHb2FsRW51bRIxChJpbnRlcmVzdF9zY2VuZV9pZHMYBSADKA4yFS5QQl9M",
            "ZWFybmluZ1NjZW5lRW51bSIpChZTU19HZXRVc2VyUG9ydHJhaXRzUmVxEg8K",
            "B3VzZXJfaWQYASABKAMiSwoWU1NfR2V0VXNlclBvcnRyYWl0c0FjaxIQCghl",
            "cnJfY29kZRgBIAEoBRIfCgRkYXRhGAIgASgLMhEuUEJfVXNlclBvcnRyYWl0",
            "cyI0ChpDU19TZXRVc2VyTGFuZ3VhZ2VMZXZlbFJlcRIWCg5sYW5ndWFnZV9s",
            "ZXZlbBgBIAEoCSIuChpTQ19TZXRVc2VyTGFuZ3VhZ2VMZXZlbEFjaxIQCghl",
            "cnJfY29kZRgBIAEoBSJuChNQQl9Vc2VyUHJvZ3Jlc3NTdGF0EiwKCnZvY2Fi",
            "X2xpc3QYASADKAsyGC5QQl9Vc2VyUHJvZ3Jlc3NTdGF0SXRlbRIpCgd4cF9s",
            "aXN0GAIgAygLMhguUEJfVXNlclByb2dyZXNzU3RhdEl0ZW0iMgoXUEJfVXNl",
            "clByb2dyZXNzU3RhdEl0ZW0SCgoCdHMYASABKAMSCwoDbnVtGAIgASgDIlcK",
            "FUNTX1NldFVzZXJIZWFkSXRlbVJlcRIUCgxoZWFkX2l0ZW1faWQYASABKAMS",
            "KAoOaGVhZF9pdGVtX3R5cGUYAiABKA4yEC5QQl9IZWFkSXRlbVR5cGUiJQoV",
            "U0NfU2V0VXNlckhlYWRJdGVtQWNrEgwKBGNvZGUYASABKAVCLlocdmZfcHJv",
            "dG9idWYvc2VydmVyL2luY2VudGl2ZaoCDU1zZy5pbmNlbnRpdmViBnByb3Rv",
            "Mw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.incentive.GrowthReflection.Descriptor, global::IncentiveReflection.Descriptor, global::Msg.basic.CommonReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_GetUserProfileReq), global::Msg.incentive.CS_GetUserProfileReq.Parser, new[]{ "user_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_GetUserProfileResponse), global::Msg.incentive.SC_GetUserProfileResponse.Parser, new[]{ "user_id", "task_finished_total_cnt", "task_finished_distinct_cnt", "exercise_finished_cnt", "time_spend_on_learning", "task_star_cnt", "task_average_score", "task_average_score_pronounce", "task_average_score_intonation", "task_average_score_fluency", "task_average_score_richness", "task_average_score_integrity", "is_profile_visible", "err_code", "voice_information", "head_url", "style_id", "total_experience", "checkin_continue_days", "rank_info", "growth_data", "progress_stat", "dress_up_data", "head_item_id", "head_item_type", "has_created_role", "friendship_info", "player_name", "role_unique_name" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_RankInfo), global::Msg.incentive.PB_RankInfo.Parser, new[]{ "current_level", "is_level_unlock" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_UserVoiceInformation), global::Msg.incentive.PB_UserVoiceInformation.Parser, new[]{ "has_voice_information", "voice_time_length", "voice_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_GetMyProfileReq), global::Msg.incentive.CS_GetMyProfileReq.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_GetMyProfileResponse), global::Msg.incentive.SC_GetMyProfileResponse.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_UserProfile), global::Msg.incentive.PB_UserProfile.Parser, new[]{ "user_id", "task_finished_total_cnt", "task_finished_distinct_cnt", "exercise_finished_cnt", "time_spend_on_learning", "task_star_cnt", "task_average_score", "task_average_score_pronounce", "task_average_score_intonation", "task_average_score_fluency", "task_average_score_richness", "task_average_score_integrity", "is_profile_visible", "voice_information", "head_url", "style_id", "total_experience", "checkin_continue_days", "rank_info", "growth_data", "progress_stat", "dress_up_data", "head_item_id", "head_item_type", "has_created_role", "friendship_info", "player_name", "role_unique_name" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_UserFriendshipInfo), global::Msg.incentive.PB_UserFriendshipInfo.Parser, new[]{ "following_count", "follower_count", "relationship_status" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_SetUserProfileAuthorityReq), global::Msg.incentive.CS_SetUserProfileAuthorityReq.Parser, new[]{ "user_id", "is_profile_visible" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_SetUserProfileAuthorityResp), global::Msg.incentive.SC_SetUserProfileAuthorityResp.Parser, new[]{ "err_code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_GetUserProfileAuthorityReq), global::Msg.incentive.CS_GetUserProfileAuthorityReq.Parser, new[]{ "user_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_GetUserProfileAuthorityResp), global::Msg.incentive.SC_GetUserProfileAuthorityResp.Parser, new[]{ "user_id", "is_profile_visible" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_SendFeedbackReq), global::Msg.incentive.CS_SendFeedbackReq.Parser, new[]{ "user_id", "feedback" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_SendFeedbackResp), global::Msg.incentive.SC_SendFeedbackResp.Parser, new[]{ "err_code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_SetUserVoiceInformationReq), global::Msg.incentive.CS_SetUserVoiceInformationReq.Parser, new[]{ "voice_id", "voice_time_length" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_SetUserVoiceInformationAck), global::Msg.incentive.SC_SetUserVoiceInformationAck.Parser, new[]{ "err_code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_SetUserPortraitsReq), global::Msg.incentive.CS_SetUserPortraitsReq.Parser, new[]{ "interest_goal", "interest_scenes", "language_level", "interest_goal_id", "interest_scene_ids", "hobbies", "expected_learning_time", "gender", "age" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_SetUserPortraitsAck), global::Msg.incentive.SC_SetUserPortraitsAck.Parser, new[]{ "err_code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_GetUserPortraitsReq), global::Msg.incentive.CS_GetUserPortraitsReq.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_GetUserPortraitsAck), global::Msg.incentive.SC_GetUserPortraitsAck.Parser, new[]{ "err_code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_UserPortraits), global::Msg.incentive.PB_UserPortraits.Parser, new[]{ "interest_goal", "interest_scenes", "language_level", "interest_goal_id", "interest_scene_ids" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_GetUserPortraitsReq), global::Msg.incentive.SS_GetUserPortraitsReq.Parser, new[]{ "user_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_GetUserPortraitsAck), global::Msg.incentive.SS_GetUserPortraitsAck.Parser, new[]{ "err_code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_SetUserLanguageLevelReq), global::Msg.incentive.CS_SetUserLanguageLevelReq.Parser, new[]{ "language_level" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_SetUserLanguageLevelAck), global::Msg.incentive.SC_SetUserLanguageLevelAck.Parser, new[]{ "err_code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_UserProgressStat), global::Msg.incentive.PB_UserProgressStat.Parser, new[]{ "vocab_list", "xp_list" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_UserProgressStatItem), global::Msg.incentive.PB_UserProgressStatItem.Parser, new[]{ "ts", "num" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_SetUserHeadItemReq), global::Msg.incentive.CS_SetUserHeadItemReq.Parser, new[]{ "head_item_id", "head_item_type" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_SetUserHeadItemAck), global::Msg.incentive.SC_SetUserHeadItemAck.Parser, new[]{ "code" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  /// <summary>
  /// 获取用户个人信息请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetUserProfileReq : pb::IMessage<CS_GetUserProfileReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetUserProfileReq> _parser = new pb::MessageParser<CS_GetUserProfileReq>(() => new CS_GetUserProfileReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetUserProfileReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserProfileReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserProfileReq(CS_GetUserProfileReq other) : this() {
      user_id_ = other.user_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserProfileReq Clone() {
      return new CS_GetUserProfileReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    /// <summary>
    /// 用户ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetUserProfileReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetUserProfileReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetUserProfileReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 获取用户个人信息响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetUserProfileResponse : pb::IMessage<SC_GetUserProfileResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetUserProfileResponse> _parser = new pb::MessageParser<SC_GetUserProfileResponse>(() => new SC_GetUserProfileResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetUserProfileResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserProfileResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserProfileResponse(SC_GetUserProfileResponse other) : this() {
      user_id_ = other.user_id_;
      task_finished_total_cnt_ = other.task_finished_total_cnt_;
      task_finished_distinct_cnt_ = other.task_finished_distinct_cnt_;
      exercise_finished_cnt_ = other.exercise_finished_cnt_;
      time_spend_on_learning_ = other.time_spend_on_learning_;
      task_star_cnt_ = other.task_star_cnt_;
      task_average_score_ = other.task_average_score_;
      task_average_score_pronounce_ = other.task_average_score_pronounce_;
      task_average_score_intonation_ = other.task_average_score_intonation_;
      task_average_score_fluency_ = other.task_average_score_fluency_;
      task_average_score_richness_ = other.task_average_score_richness_;
      task_average_score_integrity_ = other.task_average_score_integrity_;
      is_profile_visible_ = other.is_profile_visible_;
      err_code_ = other.err_code_;
      voice_information_ = other.voice_information_ != null ? other.voice_information_.Clone() : null;
      head_url_ = other.head_url_;
      style_id_ = other.style_id_;
      total_experience_ = other.total_experience_;
      checkin_continue_days_ = other.checkin_continue_days_;
      rank_info_ = other.rank_info_ != null ? other.rank_info_.Clone() : null;
      growth_data_ = other.growth_data_ != null ? other.growth_data_.Clone() : null;
      progress_stat_ = other.progress_stat_ != null ? other.progress_stat_.Clone() : null;
      dress_up_data_ = other.dress_up_data_ != null ? other.dress_up_data_.Clone() : null;
      head_item_id_ = other.head_item_id_;
      head_item_type_ = other.head_item_type_;
      has_created_role_ = other.has_created_role_;
      friendship_info_ = other.friendship_info_ != null ? other.friendship_info_.Clone() : null;
      player_name_ = other.player_name_;
      role_unique_name_ = other.role_unique_name_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserProfileResponse Clone() {
      return new SC_GetUserProfileResponse(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    /// <summary>
    /// 用户ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "task_finished_total_cnt" field.</summary>
    public const int task_finished_total_cntFieldNumber = 4;
    private int task_finished_total_cnt_;
    /// <summary>
    /// 完成任务总数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int task_finished_total_cnt {
      get { return task_finished_total_cnt_; }
      set {
        task_finished_total_cnt_ = value;
      }
    }

    /// <summary>Field number for the "task_finished_distinct_cnt" field.</summary>
    public const int task_finished_distinct_cntFieldNumber = 5;
    private int task_finished_distinct_cnt_;
    /// <summary>
    /// 完成不同任务数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int task_finished_distinct_cnt {
      get { return task_finished_distinct_cnt_; }
      set {
        task_finished_distinct_cnt_ = value;
      }
    }

    /// <summary>Field number for the "exercise_finished_cnt" field.</summary>
    public const int exercise_finished_cntFieldNumber = 6;
    private int exercise_finished_cnt_;
    /// <summary>
    /// 完成练习数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int exercise_finished_cnt {
      get { return exercise_finished_cnt_; }
      set {
        exercise_finished_cnt_ = value;
      }
    }

    /// <summary>Field number for the "time_spend_on_learning" field.</summary>
    public const int time_spend_on_learningFieldNumber = 7;
    private int time_spend_on_learning_;
    /// <summary>
    /// 学习时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int time_spend_on_learning {
      get { return time_spend_on_learning_; }
      set {
        time_spend_on_learning_ = value;
      }
    }

    /// <summary>Field number for the "task_star_cnt" field.</summary>
    public const int task_star_cntFieldNumber = 8;
    private int task_star_cnt_;
    /// <summary>
    /// 任务星星数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int task_star_cnt {
      get { return task_star_cnt_; }
      set {
        task_star_cnt_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score" field.</summary>
    public const int task_average_scoreFieldNumber = 9;
    private float task_average_score_;
    /// <summary>
    /// 任务平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score {
      get { return task_average_score_; }
      set {
        task_average_score_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score_pronounce" field.</summary>
    public const int task_average_score_pronounceFieldNumber = 10;
    private float task_average_score_pronounce_;
    /// <summary>
    /// 发音平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score_pronounce {
      get { return task_average_score_pronounce_; }
      set {
        task_average_score_pronounce_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score_intonation" field.</summary>
    public const int task_average_score_intonationFieldNumber = 11;
    private float task_average_score_intonation_;
    /// <summary>
    /// 语调平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score_intonation {
      get { return task_average_score_intonation_; }
      set {
        task_average_score_intonation_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score_fluency" field.</summary>
    public const int task_average_score_fluencyFieldNumber = 12;
    private float task_average_score_fluency_;
    /// <summary>
    /// 流畅度平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score_fluency {
      get { return task_average_score_fluency_; }
      set {
        task_average_score_fluency_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score_richness" field.</summary>
    public const int task_average_score_richnessFieldNumber = 13;
    private float task_average_score_richness_;
    /// <summary>
    /// 丰富度平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score_richness {
      get { return task_average_score_richness_; }
      set {
        task_average_score_richness_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score_integrity" field.</summary>
    public const int task_average_score_integrityFieldNumber = 14;
    private float task_average_score_integrity_;
    /// <summary>
    /// 完整度平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score_integrity {
      get { return task_average_score_integrity_; }
      set {
        task_average_score_integrity_ = value;
      }
    }

    /// <summary>Field number for the "is_profile_visible" field.</summary>
    public const int is_profile_visibleFieldNumber = 15;
    private int is_profile_visible_;
    /// <summary>
    /// 是否显示个人信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int is_profile_visible {
      get { return is_profile_visible_; }
      set {
        is_profile_visible_ = value;
      }
    }

    /// <summary>Field number for the "err_code" field.</summary>
    public const int err_codeFieldNumber = 16;
    private int err_code_;
    /// <summary>
    /// 错误代码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int err_code {
      get { return err_code_; }
      set {
        err_code_ = value;
      }
    }

    /// <summary>Field number for the "voice_information" field.</summary>
    public const int voice_informationFieldNumber = 17;
    private global::Msg.incentive.PB_UserVoiceInformation voice_information_;
    /// <summary>
    /// 用户语音信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserVoiceInformation voice_information {
      get { return voice_information_; }
      set {
        voice_information_ = value;
      }
    }

    /// <summary>Field number for the "head_url" field.</summary>
    public const int head_urlFieldNumber = 18;
    private string head_url_ = "";
    /// <summary>
    /// 头像URL
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string head_url {
      get { return head_url_; }
      set {
        head_url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "style_id" field.</summary>
    public const int style_idFieldNumber = 19;
    private string style_id_ = "";
    /// <summary>
    /// 风格ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string style_id {
      get { return style_id_; }
      set {
        style_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "total_experience" field.</summary>
    public const int total_experienceFieldNumber = 20;
    private int total_experience_;
    /// <summary>
    /// 总经验
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int total_experience {
      get { return total_experience_; }
      set {
        total_experience_ = value;
      }
    }

    /// <summary>Field number for the "checkin_continue_days" field.</summary>
    public const int checkin_continue_daysFieldNumber = 21;
    private int checkin_continue_days_;
    /// <summary>
    /// 连续签到天数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int checkin_continue_days {
      get { return checkin_continue_days_; }
      set {
        checkin_continue_days_ = value;
      }
    }

    /// <summary>Field number for the "rank_info" field.</summary>
    public const int rank_infoFieldNumber = 22;
    private global::Msg.incentive.PB_RankInfo rank_info_;
    /// <summary>
    /// 排行榜信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_RankInfo rank_info {
      get { return rank_info_; }
      set {
        rank_info_ = value;
      }
    }

    /// <summary>Field number for the "growth_data" field.</summary>
    public const int growth_dataFieldNumber = 23;
    private global::Msg.incentive.PB_UserGrowthData growth_data_;
    /// <summary>
    /// Growth信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserGrowthData growth_data {
      get { return growth_data_; }
      set {
        growth_data_ = value;
      }
    }

    /// <summary>Field number for the "progress_stat" field.</summary>
    public const int progress_statFieldNumber = 24;
    private global::Msg.incentive.PB_UserProgressStat progress_stat_;
    /// <summary>
    /// progress统计信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserProgressStat progress_stat {
      get { return progress_stat_; }
      set {
        progress_stat_ = value;
      }
    }

    /// <summary>Field number for the "dress_up_data" field.</summary>
    public const int dress_up_dataFieldNumber = 25;
    private global::PB_DressUpData dress_up_data_;
    /// <summary>
    /// 用户装扮
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_DressUpData dress_up_data {
      get { return dress_up_data_; }
      set {
        dress_up_data_ = value;
      }
    }

    /// <summary>Field number for the "head_item_id" field.</summary>
    public const int head_item_idFieldNumber = 26;
    private long head_item_id_;
    /// <summary>
    /// 头像ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long head_item_id {
      get { return head_item_id_; }
      set {
        head_item_id_ = value;
      }
    }

    /// <summary>Field number for the "head_item_type" field.</summary>
    public const int head_item_typeFieldNumber = 27;
    private global::PB_HeadItemType head_item_type_ = global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED;
    /// <summary>
    /// 头像类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_HeadItemType head_item_type {
      get { return head_item_type_; }
      set {
        head_item_type_ = value;
      }
    }

    /// <summary>Field number for the "has_created_role" field.</summary>
    public const int has_created_roleFieldNumber = 28;
    private bool has_created_role_;
    /// <summary>
    /// 是否已创角
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool has_created_role {
      get { return has_created_role_; }
      set {
        has_created_role_ = value;
      }
    }

    /// <summary>Field number for the "friendship_info" field.</summary>
    public const int friendship_infoFieldNumber = 29;
    private global::Msg.incentive.PB_UserFriendshipInfo friendship_info_;
    /// <summary>
    /// 好友关系信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserFriendshipInfo friendship_info {
      get { return friendship_info_; }
      set {
        friendship_info_ = value;
      }
    }

    /// <summary>Field number for the "player_name" field.</summary>
    public const int player_nameFieldNumber = 30;
    private string player_name_ = "";
    /// <summary>
    /// 角色名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string player_name {
      get { return player_name_; }
      set {
        player_name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "role_unique_name" field.</summary>
    public const int role_unique_nameFieldNumber = 31;
    private string role_unique_name_ = "";
    /// <summary>
    ///用户名 ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string role_unique_name {
      get { return role_unique_name_; }
      set {
        role_unique_name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetUserProfileResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetUserProfileResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (task_finished_total_cnt != other.task_finished_total_cnt) return false;
      if (task_finished_distinct_cnt != other.task_finished_distinct_cnt) return false;
      if (exercise_finished_cnt != other.exercise_finished_cnt) return false;
      if (time_spend_on_learning != other.time_spend_on_learning) return false;
      if (task_star_cnt != other.task_star_cnt) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score, other.task_average_score)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score_pronounce, other.task_average_score_pronounce)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score_intonation, other.task_average_score_intonation)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score_fluency, other.task_average_score_fluency)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score_richness, other.task_average_score_richness)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score_integrity, other.task_average_score_integrity)) return false;
      if (is_profile_visible != other.is_profile_visible) return false;
      if (err_code != other.err_code) return false;
      if (!object.Equals(voice_information, other.voice_information)) return false;
      if (head_url != other.head_url) return false;
      if (style_id != other.style_id) return false;
      if (total_experience != other.total_experience) return false;
      if (checkin_continue_days != other.checkin_continue_days) return false;
      if (!object.Equals(rank_info, other.rank_info)) return false;
      if (!object.Equals(growth_data, other.growth_data)) return false;
      if (!object.Equals(progress_stat, other.progress_stat)) return false;
      if (!object.Equals(dress_up_data, other.dress_up_data)) return false;
      if (head_item_id != other.head_item_id) return false;
      if (head_item_type != other.head_item_type) return false;
      if (has_created_role != other.has_created_role) return false;
      if (!object.Equals(friendship_info, other.friendship_info)) return false;
      if (player_name != other.player_name) return false;
      if (role_unique_name != other.role_unique_name) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (task_finished_total_cnt != 0) hash ^= task_finished_total_cnt.GetHashCode();
      if (task_finished_distinct_cnt != 0) hash ^= task_finished_distinct_cnt.GetHashCode();
      if (exercise_finished_cnt != 0) hash ^= exercise_finished_cnt.GetHashCode();
      if (time_spend_on_learning != 0) hash ^= time_spend_on_learning.GetHashCode();
      if (task_star_cnt != 0) hash ^= task_star_cnt.GetHashCode();
      if (task_average_score != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score);
      if (task_average_score_pronounce != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score_pronounce);
      if (task_average_score_intonation != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score_intonation);
      if (task_average_score_fluency != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score_fluency);
      if (task_average_score_richness != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score_richness);
      if (task_average_score_integrity != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score_integrity);
      if (is_profile_visible != 0) hash ^= is_profile_visible.GetHashCode();
      if (err_code != 0) hash ^= err_code.GetHashCode();
      if (voice_information_ != null) hash ^= voice_information.GetHashCode();
      if (head_url.Length != 0) hash ^= head_url.GetHashCode();
      if (style_id.Length != 0) hash ^= style_id.GetHashCode();
      if (total_experience != 0) hash ^= total_experience.GetHashCode();
      if (checkin_continue_days != 0) hash ^= checkin_continue_days.GetHashCode();
      if (rank_info_ != null) hash ^= rank_info.GetHashCode();
      if (growth_data_ != null) hash ^= growth_data.GetHashCode();
      if (progress_stat_ != null) hash ^= progress_stat.GetHashCode();
      if (dress_up_data_ != null) hash ^= dress_up_data.GetHashCode();
      if (head_item_id != 0L) hash ^= head_item_id.GetHashCode();
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) hash ^= head_item_type.GetHashCode();
      if (has_created_role != false) hash ^= has_created_role.GetHashCode();
      if (friendship_info_ != null) hash ^= friendship_info.GetHashCode();
      if (player_name.Length != 0) hash ^= player_name.GetHashCode();
      if (role_unique_name.Length != 0) hash ^= role_unique_name.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (task_finished_total_cnt != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(task_finished_total_cnt);
      }
      if (task_finished_distinct_cnt != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(task_finished_distinct_cnt);
      }
      if (exercise_finished_cnt != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(exercise_finished_cnt);
      }
      if (time_spend_on_learning != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(time_spend_on_learning);
      }
      if (task_star_cnt != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(task_star_cnt);
      }
      if (task_average_score != 0F) {
        output.WriteRawTag(77);
        output.WriteFloat(task_average_score);
      }
      if (task_average_score_pronounce != 0F) {
        output.WriteRawTag(85);
        output.WriteFloat(task_average_score_pronounce);
      }
      if (task_average_score_intonation != 0F) {
        output.WriteRawTag(93);
        output.WriteFloat(task_average_score_intonation);
      }
      if (task_average_score_fluency != 0F) {
        output.WriteRawTag(101);
        output.WriteFloat(task_average_score_fluency);
      }
      if (task_average_score_richness != 0F) {
        output.WriteRawTag(109);
        output.WriteFloat(task_average_score_richness);
      }
      if (task_average_score_integrity != 0F) {
        output.WriteRawTag(117);
        output.WriteFloat(task_average_score_integrity);
      }
      if (is_profile_visible != 0) {
        output.WriteRawTag(120);
        output.WriteInt32(is_profile_visible);
      }
      if (err_code != 0) {
        output.WriteRawTag(128, 1);
        output.WriteInt32(err_code);
      }
      if (voice_information_ != null) {
        output.WriteRawTag(138, 1);
        output.WriteMessage(voice_information);
      }
      if (head_url.Length != 0) {
        output.WriteRawTag(146, 1);
        output.WriteString(head_url);
      }
      if (style_id.Length != 0) {
        output.WriteRawTag(154, 1);
        output.WriteString(style_id);
      }
      if (total_experience != 0) {
        output.WriteRawTag(160, 1);
        output.WriteInt32(total_experience);
      }
      if (checkin_continue_days != 0) {
        output.WriteRawTag(168, 1);
        output.WriteInt32(checkin_continue_days);
      }
      if (rank_info_ != null) {
        output.WriteRawTag(178, 1);
        output.WriteMessage(rank_info);
      }
      if (growth_data_ != null) {
        output.WriteRawTag(186, 1);
        output.WriteMessage(growth_data);
      }
      if (progress_stat_ != null) {
        output.WriteRawTag(194, 1);
        output.WriteMessage(progress_stat);
      }
      if (dress_up_data_ != null) {
        output.WriteRawTag(202, 1);
        output.WriteMessage(dress_up_data);
      }
      if (head_item_id != 0L) {
        output.WriteRawTag(208, 1);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(216, 1);
        output.WriteEnum((int) head_item_type);
      }
      if (has_created_role != false) {
        output.WriteRawTag(224, 1);
        output.WriteBool(has_created_role);
      }
      if (friendship_info_ != null) {
        output.WriteRawTag(234, 1);
        output.WriteMessage(friendship_info);
      }
      if (player_name.Length != 0) {
        output.WriteRawTag(242, 1);
        output.WriteString(player_name);
      }
      if (role_unique_name.Length != 0) {
        output.WriteRawTag(250, 1);
        output.WriteString(role_unique_name);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (task_finished_total_cnt != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(task_finished_total_cnt);
      }
      if (task_finished_distinct_cnt != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(task_finished_distinct_cnt);
      }
      if (exercise_finished_cnt != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(exercise_finished_cnt);
      }
      if (time_spend_on_learning != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(time_spend_on_learning);
      }
      if (task_star_cnt != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(task_star_cnt);
      }
      if (task_average_score != 0F) {
        output.WriteRawTag(77);
        output.WriteFloat(task_average_score);
      }
      if (task_average_score_pronounce != 0F) {
        output.WriteRawTag(85);
        output.WriteFloat(task_average_score_pronounce);
      }
      if (task_average_score_intonation != 0F) {
        output.WriteRawTag(93);
        output.WriteFloat(task_average_score_intonation);
      }
      if (task_average_score_fluency != 0F) {
        output.WriteRawTag(101);
        output.WriteFloat(task_average_score_fluency);
      }
      if (task_average_score_richness != 0F) {
        output.WriteRawTag(109);
        output.WriteFloat(task_average_score_richness);
      }
      if (task_average_score_integrity != 0F) {
        output.WriteRawTag(117);
        output.WriteFloat(task_average_score_integrity);
      }
      if (is_profile_visible != 0) {
        output.WriteRawTag(120);
        output.WriteInt32(is_profile_visible);
      }
      if (err_code != 0) {
        output.WriteRawTag(128, 1);
        output.WriteInt32(err_code);
      }
      if (voice_information_ != null) {
        output.WriteRawTag(138, 1);
        output.WriteMessage(voice_information);
      }
      if (head_url.Length != 0) {
        output.WriteRawTag(146, 1);
        output.WriteString(head_url);
      }
      if (style_id.Length != 0) {
        output.WriteRawTag(154, 1);
        output.WriteString(style_id);
      }
      if (total_experience != 0) {
        output.WriteRawTag(160, 1);
        output.WriteInt32(total_experience);
      }
      if (checkin_continue_days != 0) {
        output.WriteRawTag(168, 1);
        output.WriteInt32(checkin_continue_days);
      }
      if (rank_info_ != null) {
        output.WriteRawTag(178, 1);
        output.WriteMessage(rank_info);
      }
      if (growth_data_ != null) {
        output.WriteRawTag(186, 1);
        output.WriteMessage(growth_data);
      }
      if (progress_stat_ != null) {
        output.WriteRawTag(194, 1);
        output.WriteMessage(progress_stat);
      }
      if (dress_up_data_ != null) {
        output.WriteRawTag(202, 1);
        output.WriteMessage(dress_up_data);
      }
      if (head_item_id != 0L) {
        output.WriteRawTag(208, 1);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(216, 1);
        output.WriteEnum((int) head_item_type);
      }
      if (has_created_role != false) {
        output.WriteRawTag(224, 1);
        output.WriteBool(has_created_role);
      }
      if (friendship_info_ != null) {
        output.WriteRawTag(234, 1);
        output.WriteMessage(friendship_info);
      }
      if (player_name.Length != 0) {
        output.WriteRawTag(242, 1);
        output.WriteString(player_name);
      }
      if (role_unique_name.Length != 0) {
        output.WriteRawTag(250, 1);
        output.WriteString(role_unique_name);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (task_finished_total_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(task_finished_total_cnt);
      }
      if (task_finished_distinct_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(task_finished_distinct_cnt);
      }
      if (exercise_finished_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(exercise_finished_cnt);
      }
      if (time_spend_on_learning != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(time_spend_on_learning);
      }
      if (task_star_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(task_star_cnt);
      }
      if (task_average_score != 0F) {
        size += 1 + 4;
      }
      if (task_average_score_pronounce != 0F) {
        size += 1 + 4;
      }
      if (task_average_score_intonation != 0F) {
        size += 1 + 4;
      }
      if (task_average_score_fluency != 0F) {
        size += 1 + 4;
      }
      if (task_average_score_richness != 0F) {
        size += 1 + 4;
      }
      if (task_average_score_integrity != 0F) {
        size += 1 + 4;
      }
      if (is_profile_visible != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(is_profile_visible);
      }
      if (err_code != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(err_code);
      }
      if (voice_information_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(voice_information);
      }
      if (head_url.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(head_url);
      }
      if (style_id.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(style_id);
      }
      if (total_experience != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(total_experience);
      }
      if (checkin_continue_days != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(checkin_continue_days);
      }
      if (rank_info_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(rank_info);
      }
      if (growth_data_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(growth_data);
      }
      if (progress_stat_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(progress_stat);
      }
      if (dress_up_data_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(dress_up_data);
      }
      if (head_item_id != 0L) {
        size += 2 + pb::CodedOutputStream.ComputeInt64Size(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) head_item_type);
      }
      if (has_created_role != false) {
        size += 2 + 1;
      }
      if (friendship_info_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(friendship_info);
      }
      if (player_name.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(player_name);
      }
      if (role_unique_name.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(role_unique_name);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetUserProfileResponse other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.task_finished_total_cnt != 0) {
        task_finished_total_cnt = other.task_finished_total_cnt;
      }
      if (other.task_finished_distinct_cnt != 0) {
        task_finished_distinct_cnt = other.task_finished_distinct_cnt;
      }
      if (other.exercise_finished_cnt != 0) {
        exercise_finished_cnt = other.exercise_finished_cnt;
      }
      if (other.time_spend_on_learning != 0) {
        time_spend_on_learning = other.time_spend_on_learning;
      }
      if (other.task_star_cnt != 0) {
        task_star_cnt = other.task_star_cnt;
      }
      if (other.task_average_score != 0F) {
        task_average_score = other.task_average_score;
      }
      if (other.task_average_score_pronounce != 0F) {
        task_average_score_pronounce = other.task_average_score_pronounce;
      }
      if (other.task_average_score_intonation != 0F) {
        task_average_score_intonation = other.task_average_score_intonation;
      }
      if (other.task_average_score_fluency != 0F) {
        task_average_score_fluency = other.task_average_score_fluency;
      }
      if (other.task_average_score_richness != 0F) {
        task_average_score_richness = other.task_average_score_richness;
      }
      if (other.task_average_score_integrity != 0F) {
        task_average_score_integrity = other.task_average_score_integrity;
      }
      if (other.is_profile_visible != 0) {
        is_profile_visible = other.is_profile_visible;
      }
      if (other.err_code != 0) {
        err_code = other.err_code;
      }
      if (other.voice_information_ != null) {
        if (voice_information_ == null) {
          voice_information = new global::Msg.incentive.PB_UserVoiceInformation();
        }
        voice_information.MergeFrom(other.voice_information);
      }
      if (other.head_url.Length != 0) {
        head_url = other.head_url;
      }
      if (other.style_id.Length != 0) {
        style_id = other.style_id;
      }
      if (other.total_experience != 0) {
        total_experience = other.total_experience;
      }
      if (other.checkin_continue_days != 0) {
        checkin_continue_days = other.checkin_continue_days;
      }
      if (other.rank_info_ != null) {
        if (rank_info_ == null) {
          rank_info = new global::Msg.incentive.PB_RankInfo();
        }
        rank_info.MergeFrom(other.rank_info);
      }
      if (other.growth_data_ != null) {
        if (growth_data_ == null) {
          growth_data = new global::Msg.incentive.PB_UserGrowthData();
        }
        growth_data.MergeFrom(other.growth_data);
      }
      if (other.progress_stat_ != null) {
        if (progress_stat_ == null) {
          progress_stat = new global::Msg.incentive.PB_UserProgressStat();
        }
        progress_stat.MergeFrom(other.progress_stat);
      }
      if (other.dress_up_data_ != null) {
        if (dress_up_data_ == null) {
          dress_up_data = new global::PB_DressUpData();
        }
        dress_up_data.MergeFrom(other.dress_up_data);
      }
      if (other.head_item_id != 0L) {
        head_item_id = other.head_item_id;
      }
      if (other.head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        head_item_type = other.head_item_type;
      }
      if (other.has_created_role != false) {
        has_created_role = other.has_created_role;
      }
      if (other.friendship_info_ != null) {
        if (friendship_info_ == null) {
          friendship_info = new global::Msg.incentive.PB_UserFriendshipInfo();
        }
        friendship_info.MergeFrom(other.friendship_info);
      }
      if (other.player_name.Length != 0) {
        player_name = other.player_name;
      }
      if (other.role_unique_name.Length != 0) {
        role_unique_name = other.role_unique_name;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 32: {
            task_finished_total_cnt = input.ReadInt32();
            break;
          }
          case 40: {
            task_finished_distinct_cnt = input.ReadInt32();
            break;
          }
          case 48: {
            exercise_finished_cnt = input.ReadInt32();
            break;
          }
          case 56: {
            time_spend_on_learning = input.ReadInt32();
            break;
          }
          case 64: {
            task_star_cnt = input.ReadInt32();
            break;
          }
          case 77: {
            task_average_score = input.ReadFloat();
            break;
          }
          case 85: {
            task_average_score_pronounce = input.ReadFloat();
            break;
          }
          case 93: {
            task_average_score_intonation = input.ReadFloat();
            break;
          }
          case 101: {
            task_average_score_fluency = input.ReadFloat();
            break;
          }
          case 109: {
            task_average_score_richness = input.ReadFloat();
            break;
          }
          case 117: {
            task_average_score_integrity = input.ReadFloat();
            break;
          }
          case 120: {
            is_profile_visible = input.ReadInt32();
            break;
          }
          case 128: {
            err_code = input.ReadInt32();
            break;
          }
          case 138: {
            if (voice_information_ == null) {
              voice_information = new global::Msg.incentive.PB_UserVoiceInformation();
            }
            input.ReadMessage(voice_information);
            break;
          }
          case 146: {
            head_url = input.ReadString();
            break;
          }
          case 154: {
            style_id = input.ReadString();
            break;
          }
          case 160: {
            total_experience = input.ReadInt32();
            break;
          }
          case 168: {
            checkin_continue_days = input.ReadInt32();
            break;
          }
          case 178: {
            if (rank_info_ == null) {
              rank_info = new global::Msg.incentive.PB_RankInfo();
            }
            input.ReadMessage(rank_info);
            break;
          }
          case 186: {
            if (growth_data_ == null) {
              growth_data = new global::Msg.incentive.PB_UserGrowthData();
            }
            input.ReadMessage(growth_data);
            break;
          }
          case 194: {
            if (progress_stat_ == null) {
              progress_stat = new global::Msg.incentive.PB_UserProgressStat();
            }
            input.ReadMessage(progress_stat);
            break;
          }
          case 202: {
            if (dress_up_data_ == null) {
              dress_up_data = new global::PB_DressUpData();
            }
            input.ReadMessage(dress_up_data);
            break;
          }
          case 208: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 216: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
          case 224: {
            has_created_role = input.ReadBool();
            break;
          }
          case 234: {
            if (friendship_info_ == null) {
              friendship_info = new global::Msg.incentive.PB_UserFriendshipInfo();
            }
            input.ReadMessage(friendship_info);
            break;
          }
          case 242: {
            player_name = input.ReadString();
            break;
          }
          case 250: {
            role_unique_name = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 32: {
            task_finished_total_cnt = input.ReadInt32();
            break;
          }
          case 40: {
            task_finished_distinct_cnt = input.ReadInt32();
            break;
          }
          case 48: {
            exercise_finished_cnt = input.ReadInt32();
            break;
          }
          case 56: {
            time_spend_on_learning = input.ReadInt32();
            break;
          }
          case 64: {
            task_star_cnt = input.ReadInt32();
            break;
          }
          case 77: {
            task_average_score = input.ReadFloat();
            break;
          }
          case 85: {
            task_average_score_pronounce = input.ReadFloat();
            break;
          }
          case 93: {
            task_average_score_intonation = input.ReadFloat();
            break;
          }
          case 101: {
            task_average_score_fluency = input.ReadFloat();
            break;
          }
          case 109: {
            task_average_score_richness = input.ReadFloat();
            break;
          }
          case 117: {
            task_average_score_integrity = input.ReadFloat();
            break;
          }
          case 120: {
            is_profile_visible = input.ReadInt32();
            break;
          }
          case 128: {
            err_code = input.ReadInt32();
            break;
          }
          case 138: {
            if (voice_information_ == null) {
              voice_information = new global::Msg.incentive.PB_UserVoiceInformation();
            }
            input.ReadMessage(voice_information);
            break;
          }
          case 146: {
            head_url = input.ReadString();
            break;
          }
          case 154: {
            style_id = input.ReadString();
            break;
          }
          case 160: {
            total_experience = input.ReadInt32();
            break;
          }
          case 168: {
            checkin_continue_days = input.ReadInt32();
            break;
          }
          case 178: {
            if (rank_info_ == null) {
              rank_info = new global::Msg.incentive.PB_RankInfo();
            }
            input.ReadMessage(rank_info);
            break;
          }
          case 186: {
            if (growth_data_ == null) {
              growth_data = new global::Msg.incentive.PB_UserGrowthData();
            }
            input.ReadMessage(growth_data);
            break;
          }
          case 194: {
            if (progress_stat_ == null) {
              progress_stat = new global::Msg.incentive.PB_UserProgressStat();
            }
            input.ReadMessage(progress_stat);
            break;
          }
          case 202: {
            if (dress_up_data_ == null) {
              dress_up_data = new global::PB_DressUpData();
            }
            input.ReadMessage(dress_up_data);
            break;
          }
          case 208: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 216: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
          case 224: {
            has_created_role = input.ReadBool();
            break;
          }
          case 234: {
            if (friendship_info_ == null) {
              friendship_info = new global::Msg.incentive.PB_UserFriendshipInfo();
            }
            input.ReadMessage(friendship_info);
            break;
          }
          case 242: {
            player_name = input.ReadString();
            break;
          }
          case 250: {
            role_unique_name = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 排行榜信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_RankInfo : pb::IMessage<PB_RankInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_RankInfo> _parser = new pb::MessageParser<PB_RankInfo>(() => new PB_RankInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_RankInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankInfo(PB_RankInfo other) : this() {
      current_level_ = other.current_level_;
      is_level_unlock_ = other.is_level_unlock_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RankInfo Clone() {
      return new PB_RankInfo(this);
    }

    /// <summary>Field number for the "current_level" field.</summary>
    public const int current_levelFieldNumber = 1;
    private int current_level_;
    /// <summary>
    /// 当前段位
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int current_level {
      get { return current_level_; }
      set {
        current_level_ = value;
      }
    }

    /// <summary>Field number for the "is_level_unlock" field.</summary>
    public const int is_level_unlockFieldNumber = 2;
    private bool is_level_unlock_;
    /// <summary>
    /// 段位是否解锁
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_level_unlock {
      get { return is_level_unlock_; }
      set {
        is_level_unlock_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_RankInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_RankInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (current_level != other.current_level) return false;
      if (is_level_unlock != other.is_level_unlock) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (current_level != 0) hash ^= current_level.GetHashCode();
      if (is_level_unlock != false) hash ^= is_level_unlock.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (current_level != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(current_level);
      }
      if (is_level_unlock != false) {
        output.WriteRawTag(16);
        output.WriteBool(is_level_unlock);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (current_level != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(current_level);
      }
      if (is_level_unlock != false) {
        output.WriteRawTag(16);
        output.WriteBool(is_level_unlock);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (current_level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(current_level);
      }
      if (is_level_unlock != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_RankInfo other) {
      if (other == null) {
        return;
      }
      if (other.current_level != 0) {
        current_level = other.current_level;
      }
      if (other.is_level_unlock != false) {
        is_level_unlock = other.is_level_unlock;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            current_level = input.ReadInt32();
            break;
          }
          case 16: {
            is_level_unlock = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            current_level = input.ReadInt32();
            break;
          }
          case 16: {
            is_level_unlock = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 用户语音信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserVoiceInformation : pb::IMessage<PB_UserVoiceInformation>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserVoiceInformation> _parser = new pb::MessageParser<PB_UserVoiceInformation>(() => new PB_UserVoiceInformation());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserVoiceInformation> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserVoiceInformation() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserVoiceInformation(PB_UserVoiceInformation other) : this() {
      has_voice_information_ = other.has_voice_information_;
      voice_time_length_ = other.voice_time_length_;
      voice_id_ = other.voice_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserVoiceInformation Clone() {
      return new PB_UserVoiceInformation(this);
    }

    /// <summary>Field number for the "has_voice_information" field.</summary>
    public const int has_voice_informationFieldNumber = 1;
    private bool has_voice_information_;
    /// <summary>
    /// 是否有语音信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool has_voice_information {
      get { return has_voice_information_; }
      set {
        has_voice_information_ = value;
      }
    }

    /// <summary>Field number for the "voice_time_length" field.</summary>
    public const int voice_time_lengthFieldNumber = 2;
    private int voice_time_length_;
    /// <summary>
    ///语音长度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int voice_time_length {
      get { return voice_time_length_; }
      set {
        voice_time_length_ = value;
      }
    }

    /// <summary>Field number for the "voice_id" field.</summary>
    public const int voice_idFieldNumber = 3;
    private long voice_id_;
    /// <summary>
    /// 语音ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long voice_id {
      get { return voice_id_; }
      set {
        voice_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserVoiceInformation);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserVoiceInformation other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (has_voice_information != other.has_voice_information) return false;
      if (voice_time_length != other.voice_time_length) return false;
      if (voice_id != other.voice_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (has_voice_information != false) hash ^= has_voice_information.GetHashCode();
      if (voice_time_length != 0) hash ^= voice_time_length.GetHashCode();
      if (voice_id != 0L) hash ^= voice_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (has_voice_information != false) {
        output.WriteRawTag(8);
        output.WriteBool(has_voice_information);
      }
      if (voice_time_length != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(voice_time_length);
      }
      if (voice_id != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(voice_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (has_voice_information != false) {
        output.WriteRawTag(8);
        output.WriteBool(has_voice_information);
      }
      if (voice_time_length != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(voice_time_length);
      }
      if (voice_id != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(voice_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (has_voice_information != false) {
        size += 1 + 1;
      }
      if (voice_time_length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(voice_time_length);
      }
      if (voice_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(voice_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserVoiceInformation other) {
      if (other == null) {
        return;
      }
      if (other.has_voice_information != false) {
        has_voice_information = other.has_voice_information;
      }
      if (other.voice_time_length != 0) {
        voice_time_length = other.voice_time_length;
      }
      if (other.voice_id != 0L) {
        voice_id = other.voice_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            has_voice_information = input.ReadBool();
            break;
          }
          case 16: {
            voice_time_length = input.ReadInt32();
            break;
          }
          case 24: {
            voice_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            has_voice_information = input.ReadBool();
            break;
          }
          case 16: {
            voice_time_length = input.ReadInt32();
            break;
          }
          case 24: {
            voice_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 获取我的个人信息请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetMyProfileReq : pb::IMessage<CS_GetMyProfileReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetMyProfileReq> _parser = new pb::MessageParser<CS_GetMyProfileReq>(() => new CS_GetMyProfileReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetMyProfileReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetMyProfileReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetMyProfileReq(CS_GetMyProfileReq other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetMyProfileReq Clone() {
      return new CS_GetMyProfileReq(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetMyProfileReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetMyProfileReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetMyProfileReq other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 获取我的个人信息响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetMyProfileResponse : pb::IMessage<SC_GetMyProfileResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetMyProfileResponse> _parser = new pb::MessageParser<SC_GetMyProfileResponse>(() => new SC_GetMyProfileResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetMyProfileResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetMyProfileResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetMyProfileResponse(SC_GetMyProfileResponse other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetMyProfileResponse Clone() {
      return new SC_GetMyProfileResponse(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    /// <summary>
    /// 响应码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_UserProfile data_;
    /// <summary>
    /// 用户个人信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserProfile data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetMyProfileResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetMyProfileResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetMyProfileResponse other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_UserProfile();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_UserProfile();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_UserProfile();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 用户个人信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserProfile : pb::IMessage<PB_UserProfile>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserProfile> _parser = new pb::MessageParser<PB_UserProfile>(() => new PB_UserProfile());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserProfile> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserProfile() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserProfile(PB_UserProfile other) : this() {
      user_id_ = other.user_id_;
      task_finished_total_cnt_ = other.task_finished_total_cnt_;
      task_finished_distinct_cnt_ = other.task_finished_distinct_cnt_;
      exercise_finished_cnt_ = other.exercise_finished_cnt_;
      time_spend_on_learning_ = other.time_spend_on_learning_;
      task_star_cnt_ = other.task_star_cnt_;
      task_average_score_ = other.task_average_score_;
      task_average_score_pronounce_ = other.task_average_score_pronounce_;
      task_average_score_intonation_ = other.task_average_score_intonation_;
      task_average_score_fluency_ = other.task_average_score_fluency_;
      task_average_score_richness_ = other.task_average_score_richness_;
      task_average_score_integrity_ = other.task_average_score_integrity_;
      is_profile_visible_ = other.is_profile_visible_;
      voice_information_ = other.voice_information_ != null ? other.voice_information_.Clone() : null;
      head_url_ = other.head_url_;
      style_id_ = other.style_id_;
      total_experience_ = other.total_experience_;
      checkin_continue_days_ = other.checkin_continue_days_;
      rank_info_ = other.rank_info_ != null ? other.rank_info_.Clone() : null;
      growth_data_ = other.growth_data_ != null ? other.growth_data_.Clone() : null;
      progress_stat_ = other.progress_stat_ != null ? other.progress_stat_.Clone() : null;
      dress_up_data_ = other.dress_up_data_ != null ? other.dress_up_data_.Clone() : null;
      head_item_id_ = other.head_item_id_;
      head_item_type_ = other.head_item_type_;
      has_created_role_ = other.has_created_role_;
      friendship_info_ = other.friendship_info_ != null ? other.friendship_info_.Clone() : null;
      player_name_ = other.player_name_;
      role_unique_name_ = other.role_unique_name_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserProfile Clone() {
      return new PB_UserProfile(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    /// <summary>
    /// 用户ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "task_finished_total_cnt" field.</summary>
    public const int task_finished_total_cntFieldNumber = 4;
    private int task_finished_total_cnt_;
    /// <summary>
    /// 完成任务总数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int task_finished_total_cnt {
      get { return task_finished_total_cnt_; }
      set {
        task_finished_total_cnt_ = value;
      }
    }

    /// <summary>Field number for the "task_finished_distinct_cnt" field.</summary>
    public const int task_finished_distinct_cntFieldNumber = 5;
    private int task_finished_distinct_cnt_;
    /// <summary>
    /// 完成不同任务数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int task_finished_distinct_cnt {
      get { return task_finished_distinct_cnt_; }
      set {
        task_finished_distinct_cnt_ = value;
      }
    }

    /// <summary>Field number for the "exercise_finished_cnt" field.</summary>
    public const int exercise_finished_cntFieldNumber = 6;
    private int exercise_finished_cnt_;
    /// <summary>
    /// 完成练习数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int exercise_finished_cnt {
      get { return exercise_finished_cnt_; }
      set {
        exercise_finished_cnt_ = value;
      }
    }

    /// <summary>Field number for the "time_spend_on_learning" field.</summary>
    public const int time_spend_on_learningFieldNumber = 7;
    private int time_spend_on_learning_;
    /// <summary>
    /// 学习时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int time_spend_on_learning {
      get { return time_spend_on_learning_; }
      set {
        time_spend_on_learning_ = value;
      }
    }

    /// <summary>Field number for the "task_star_cnt" field.</summary>
    public const int task_star_cntFieldNumber = 8;
    private int task_star_cnt_;
    /// <summary>
    /// 任务星星数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int task_star_cnt {
      get { return task_star_cnt_; }
      set {
        task_star_cnt_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score" field.</summary>
    public const int task_average_scoreFieldNumber = 9;
    private float task_average_score_;
    /// <summary>
    /// 任务平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score {
      get { return task_average_score_; }
      set {
        task_average_score_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score_pronounce" field.</summary>
    public const int task_average_score_pronounceFieldNumber = 10;
    private float task_average_score_pronounce_;
    /// <summary>
    /// 发音平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score_pronounce {
      get { return task_average_score_pronounce_; }
      set {
        task_average_score_pronounce_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score_intonation" field.</summary>
    public const int task_average_score_intonationFieldNumber = 11;
    private float task_average_score_intonation_;
    /// <summary>
    /// 语调平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score_intonation {
      get { return task_average_score_intonation_; }
      set {
        task_average_score_intonation_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score_fluency" field.</summary>
    public const int task_average_score_fluencyFieldNumber = 12;
    private float task_average_score_fluency_;
    /// <summary>
    /// 流畅度平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score_fluency {
      get { return task_average_score_fluency_; }
      set {
        task_average_score_fluency_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score_richness" field.</summary>
    public const int task_average_score_richnessFieldNumber = 13;
    private float task_average_score_richness_;
    /// <summary>
    /// 丰富度平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score_richness {
      get { return task_average_score_richness_; }
      set {
        task_average_score_richness_ = value;
      }
    }

    /// <summary>Field number for the "task_average_score_integrity" field.</summary>
    public const int task_average_score_integrityFieldNumber = 14;
    private float task_average_score_integrity_;
    /// <summary>
    /// 完整度平均分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float task_average_score_integrity {
      get { return task_average_score_integrity_; }
      set {
        task_average_score_integrity_ = value;
      }
    }

    /// <summary>Field number for the "is_profile_visible" field.</summary>
    public const int is_profile_visibleFieldNumber = 15;
    private int is_profile_visible_;
    /// <summary>
    /// 是否显示个人信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int is_profile_visible {
      get { return is_profile_visible_; }
      set {
        is_profile_visible_ = value;
      }
    }

    /// <summary>Field number for the "voice_information" field.</summary>
    public const int voice_informationFieldNumber = 16;
    private global::Msg.incentive.PB_UserVoiceInformation voice_information_;
    /// <summary>
    /// 用户语音信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserVoiceInformation voice_information {
      get { return voice_information_; }
      set {
        voice_information_ = value;
      }
    }

    /// <summary>Field number for the "head_url" field.</summary>
    public const int head_urlFieldNumber = 17;
    private string head_url_ = "";
    /// <summary>
    /// 头像URL
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string head_url {
      get { return head_url_; }
      set {
        head_url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "style_id" field.</summary>
    public const int style_idFieldNumber = 18;
    private string style_id_ = "";
    /// <summary>
    /// 风格ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string style_id {
      get { return style_id_; }
      set {
        style_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "total_experience" field.</summary>
    public const int total_experienceFieldNumber = 19;
    private int total_experience_;
    /// <summary>
    /// 总经验
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int total_experience {
      get { return total_experience_; }
      set {
        total_experience_ = value;
      }
    }

    /// <summary>Field number for the "checkin_continue_days" field.</summary>
    public const int checkin_continue_daysFieldNumber = 20;
    private int checkin_continue_days_;
    /// <summary>
    /// 连续签到天数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int checkin_continue_days {
      get { return checkin_continue_days_; }
      set {
        checkin_continue_days_ = value;
      }
    }

    /// <summary>Field number for the "rank_info" field.</summary>
    public const int rank_infoFieldNumber = 21;
    private global::Msg.incentive.PB_RankInfo rank_info_;
    /// <summary>
    /// 排行榜信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_RankInfo rank_info {
      get { return rank_info_; }
      set {
        rank_info_ = value;
      }
    }

    /// <summary>Field number for the "growth_data" field.</summary>
    public const int growth_dataFieldNumber = 22;
    private global::Msg.incentive.PB_UserGrowthData growth_data_;
    /// <summary>
    /// Growth信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserGrowthData growth_data {
      get { return growth_data_; }
      set {
        growth_data_ = value;
      }
    }

    /// <summary>Field number for the "progress_stat" field.</summary>
    public const int progress_statFieldNumber = 23;
    private global::Msg.incentive.PB_UserProgressStat progress_stat_;
    /// <summary>
    /// progress统计信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserProgressStat progress_stat {
      get { return progress_stat_; }
      set {
        progress_stat_ = value;
      }
    }

    /// <summary>Field number for the "dress_up_data" field.</summary>
    public const int dress_up_dataFieldNumber = 25;
    private global::PB_DressUpData dress_up_data_;
    /// <summary>
    /// 用户装扮
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_DressUpData dress_up_data {
      get { return dress_up_data_; }
      set {
        dress_up_data_ = value;
      }
    }

    /// <summary>Field number for the "head_item_id" field.</summary>
    public const int head_item_idFieldNumber = 26;
    private long head_item_id_;
    /// <summary>
    /// 头像ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long head_item_id {
      get { return head_item_id_; }
      set {
        head_item_id_ = value;
      }
    }

    /// <summary>Field number for the "head_item_type" field.</summary>
    public const int head_item_typeFieldNumber = 27;
    private global::PB_HeadItemType head_item_type_ = global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED;
    /// <summary>
    /// 头像类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_HeadItemType head_item_type {
      get { return head_item_type_; }
      set {
        head_item_type_ = value;
      }
    }

    /// <summary>Field number for the "has_created_role" field.</summary>
    public const int has_created_roleFieldNumber = 28;
    private bool has_created_role_;
    /// <summary>
    /// 是否已创角
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool has_created_role {
      get { return has_created_role_; }
      set {
        has_created_role_ = value;
      }
    }

    /// <summary>Field number for the "friendship_info" field.</summary>
    public const int friendship_infoFieldNumber = 29;
    private global::Msg.incentive.PB_UserFriendshipInfo friendship_info_;
    /// <summary>
    /// 好友关系信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserFriendshipInfo friendship_info {
      get { return friendship_info_; }
      set {
        friendship_info_ = value;
      }
    }

    /// <summary>Field number for the "player_name" field.</summary>
    public const int player_nameFieldNumber = 30;
    private string player_name_ = "";
    /// <summary>
    /// 角色名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string player_name {
      get { return player_name_; }
      set {
        player_name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "role_unique_name" field.</summary>
    public const int role_unique_nameFieldNumber = 31;
    private string role_unique_name_ = "";
    /// <summary>
    ///用户名 ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string role_unique_name {
      get { return role_unique_name_; }
      set {
        role_unique_name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserProfile);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserProfile other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (task_finished_total_cnt != other.task_finished_total_cnt) return false;
      if (task_finished_distinct_cnt != other.task_finished_distinct_cnt) return false;
      if (exercise_finished_cnt != other.exercise_finished_cnt) return false;
      if (time_spend_on_learning != other.time_spend_on_learning) return false;
      if (task_star_cnt != other.task_star_cnt) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score, other.task_average_score)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score_pronounce, other.task_average_score_pronounce)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score_intonation, other.task_average_score_intonation)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score_fluency, other.task_average_score_fluency)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score_richness, other.task_average_score_richness)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(task_average_score_integrity, other.task_average_score_integrity)) return false;
      if (is_profile_visible != other.is_profile_visible) return false;
      if (!object.Equals(voice_information, other.voice_information)) return false;
      if (head_url != other.head_url) return false;
      if (style_id != other.style_id) return false;
      if (total_experience != other.total_experience) return false;
      if (checkin_continue_days != other.checkin_continue_days) return false;
      if (!object.Equals(rank_info, other.rank_info)) return false;
      if (!object.Equals(growth_data, other.growth_data)) return false;
      if (!object.Equals(progress_stat, other.progress_stat)) return false;
      if (!object.Equals(dress_up_data, other.dress_up_data)) return false;
      if (head_item_id != other.head_item_id) return false;
      if (head_item_type != other.head_item_type) return false;
      if (has_created_role != other.has_created_role) return false;
      if (!object.Equals(friendship_info, other.friendship_info)) return false;
      if (player_name != other.player_name) return false;
      if (role_unique_name != other.role_unique_name) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (task_finished_total_cnt != 0) hash ^= task_finished_total_cnt.GetHashCode();
      if (task_finished_distinct_cnt != 0) hash ^= task_finished_distinct_cnt.GetHashCode();
      if (exercise_finished_cnt != 0) hash ^= exercise_finished_cnt.GetHashCode();
      if (time_spend_on_learning != 0) hash ^= time_spend_on_learning.GetHashCode();
      if (task_star_cnt != 0) hash ^= task_star_cnt.GetHashCode();
      if (task_average_score != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score);
      if (task_average_score_pronounce != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score_pronounce);
      if (task_average_score_intonation != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score_intonation);
      if (task_average_score_fluency != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score_fluency);
      if (task_average_score_richness != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score_richness);
      if (task_average_score_integrity != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(task_average_score_integrity);
      if (is_profile_visible != 0) hash ^= is_profile_visible.GetHashCode();
      if (voice_information_ != null) hash ^= voice_information.GetHashCode();
      if (head_url.Length != 0) hash ^= head_url.GetHashCode();
      if (style_id.Length != 0) hash ^= style_id.GetHashCode();
      if (total_experience != 0) hash ^= total_experience.GetHashCode();
      if (checkin_continue_days != 0) hash ^= checkin_continue_days.GetHashCode();
      if (rank_info_ != null) hash ^= rank_info.GetHashCode();
      if (growth_data_ != null) hash ^= growth_data.GetHashCode();
      if (progress_stat_ != null) hash ^= progress_stat.GetHashCode();
      if (dress_up_data_ != null) hash ^= dress_up_data.GetHashCode();
      if (head_item_id != 0L) hash ^= head_item_id.GetHashCode();
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) hash ^= head_item_type.GetHashCode();
      if (has_created_role != false) hash ^= has_created_role.GetHashCode();
      if (friendship_info_ != null) hash ^= friendship_info.GetHashCode();
      if (player_name.Length != 0) hash ^= player_name.GetHashCode();
      if (role_unique_name.Length != 0) hash ^= role_unique_name.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (task_finished_total_cnt != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(task_finished_total_cnt);
      }
      if (task_finished_distinct_cnt != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(task_finished_distinct_cnt);
      }
      if (exercise_finished_cnt != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(exercise_finished_cnt);
      }
      if (time_spend_on_learning != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(time_spend_on_learning);
      }
      if (task_star_cnt != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(task_star_cnt);
      }
      if (task_average_score != 0F) {
        output.WriteRawTag(77);
        output.WriteFloat(task_average_score);
      }
      if (task_average_score_pronounce != 0F) {
        output.WriteRawTag(85);
        output.WriteFloat(task_average_score_pronounce);
      }
      if (task_average_score_intonation != 0F) {
        output.WriteRawTag(93);
        output.WriteFloat(task_average_score_intonation);
      }
      if (task_average_score_fluency != 0F) {
        output.WriteRawTag(101);
        output.WriteFloat(task_average_score_fluency);
      }
      if (task_average_score_richness != 0F) {
        output.WriteRawTag(109);
        output.WriteFloat(task_average_score_richness);
      }
      if (task_average_score_integrity != 0F) {
        output.WriteRawTag(117);
        output.WriteFloat(task_average_score_integrity);
      }
      if (is_profile_visible != 0) {
        output.WriteRawTag(120);
        output.WriteInt32(is_profile_visible);
      }
      if (voice_information_ != null) {
        output.WriteRawTag(130, 1);
        output.WriteMessage(voice_information);
      }
      if (head_url.Length != 0) {
        output.WriteRawTag(138, 1);
        output.WriteString(head_url);
      }
      if (style_id.Length != 0) {
        output.WriteRawTag(146, 1);
        output.WriteString(style_id);
      }
      if (total_experience != 0) {
        output.WriteRawTag(152, 1);
        output.WriteInt32(total_experience);
      }
      if (checkin_continue_days != 0) {
        output.WriteRawTag(160, 1);
        output.WriteInt32(checkin_continue_days);
      }
      if (rank_info_ != null) {
        output.WriteRawTag(170, 1);
        output.WriteMessage(rank_info);
      }
      if (growth_data_ != null) {
        output.WriteRawTag(178, 1);
        output.WriteMessage(growth_data);
      }
      if (progress_stat_ != null) {
        output.WriteRawTag(186, 1);
        output.WriteMessage(progress_stat);
      }
      if (dress_up_data_ != null) {
        output.WriteRawTag(202, 1);
        output.WriteMessage(dress_up_data);
      }
      if (head_item_id != 0L) {
        output.WriteRawTag(208, 1);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(216, 1);
        output.WriteEnum((int) head_item_type);
      }
      if (has_created_role != false) {
        output.WriteRawTag(224, 1);
        output.WriteBool(has_created_role);
      }
      if (friendship_info_ != null) {
        output.WriteRawTag(234, 1);
        output.WriteMessage(friendship_info);
      }
      if (player_name.Length != 0) {
        output.WriteRawTag(242, 1);
        output.WriteString(player_name);
      }
      if (role_unique_name.Length != 0) {
        output.WriteRawTag(250, 1);
        output.WriteString(role_unique_name);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (task_finished_total_cnt != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(task_finished_total_cnt);
      }
      if (task_finished_distinct_cnt != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(task_finished_distinct_cnt);
      }
      if (exercise_finished_cnt != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(exercise_finished_cnt);
      }
      if (time_spend_on_learning != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(time_spend_on_learning);
      }
      if (task_star_cnt != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(task_star_cnt);
      }
      if (task_average_score != 0F) {
        output.WriteRawTag(77);
        output.WriteFloat(task_average_score);
      }
      if (task_average_score_pronounce != 0F) {
        output.WriteRawTag(85);
        output.WriteFloat(task_average_score_pronounce);
      }
      if (task_average_score_intonation != 0F) {
        output.WriteRawTag(93);
        output.WriteFloat(task_average_score_intonation);
      }
      if (task_average_score_fluency != 0F) {
        output.WriteRawTag(101);
        output.WriteFloat(task_average_score_fluency);
      }
      if (task_average_score_richness != 0F) {
        output.WriteRawTag(109);
        output.WriteFloat(task_average_score_richness);
      }
      if (task_average_score_integrity != 0F) {
        output.WriteRawTag(117);
        output.WriteFloat(task_average_score_integrity);
      }
      if (is_profile_visible != 0) {
        output.WriteRawTag(120);
        output.WriteInt32(is_profile_visible);
      }
      if (voice_information_ != null) {
        output.WriteRawTag(130, 1);
        output.WriteMessage(voice_information);
      }
      if (head_url.Length != 0) {
        output.WriteRawTag(138, 1);
        output.WriteString(head_url);
      }
      if (style_id.Length != 0) {
        output.WriteRawTag(146, 1);
        output.WriteString(style_id);
      }
      if (total_experience != 0) {
        output.WriteRawTag(152, 1);
        output.WriteInt32(total_experience);
      }
      if (checkin_continue_days != 0) {
        output.WriteRawTag(160, 1);
        output.WriteInt32(checkin_continue_days);
      }
      if (rank_info_ != null) {
        output.WriteRawTag(170, 1);
        output.WriteMessage(rank_info);
      }
      if (growth_data_ != null) {
        output.WriteRawTag(178, 1);
        output.WriteMessage(growth_data);
      }
      if (progress_stat_ != null) {
        output.WriteRawTag(186, 1);
        output.WriteMessage(progress_stat);
      }
      if (dress_up_data_ != null) {
        output.WriteRawTag(202, 1);
        output.WriteMessage(dress_up_data);
      }
      if (head_item_id != 0L) {
        output.WriteRawTag(208, 1);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(216, 1);
        output.WriteEnum((int) head_item_type);
      }
      if (has_created_role != false) {
        output.WriteRawTag(224, 1);
        output.WriteBool(has_created_role);
      }
      if (friendship_info_ != null) {
        output.WriteRawTag(234, 1);
        output.WriteMessage(friendship_info);
      }
      if (player_name.Length != 0) {
        output.WriteRawTag(242, 1);
        output.WriteString(player_name);
      }
      if (role_unique_name.Length != 0) {
        output.WriteRawTag(250, 1);
        output.WriteString(role_unique_name);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (task_finished_total_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(task_finished_total_cnt);
      }
      if (task_finished_distinct_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(task_finished_distinct_cnt);
      }
      if (exercise_finished_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(exercise_finished_cnt);
      }
      if (time_spend_on_learning != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(time_spend_on_learning);
      }
      if (task_star_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(task_star_cnt);
      }
      if (task_average_score != 0F) {
        size += 1 + 4;
      }
      if (task_average_score_pronounce != 0F) {
        size += 1 + 4;
      }
      if (task_average_score_intonation != 0F) {
        size += 1 + 4;
      }
      if (task_average_score_fluency != 0F) {
        size += 1 + 4;
      }
      if (task_average_score_richness != 0F) {
        size += 1 + 4;
      }
      if (task_average_score_integrity != 0F) {
        size += 1 + 4;
      }
      if (is_profile_visible != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(is_profile_visible);
      }
      if (voice_information_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(voice_information);
      }
      if (head_url.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(head_url);
      }
      if (style_id.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(style_id);
      }
      if (total_experience != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(total_experience);
      }
      if (checkin_continue_days != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(checkin_continue_days);
      }
      if (rank_info_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(rank_info);
      }
      if (growth_data_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(growth_data);
      }
      if (progress_stat_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(progress_stat);
      }
      if (dress_up_data_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(dress_up_data);
      }
      if (head_item_id != 0L) {
        size += 2 + pb::CodedOutputStream.ComputeInt64Size(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) head_item_type);
      }
      if (has_created_role != false) {
        size += 2 + 1;
      }
      if (friendship_info_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(friendship_info);
      }
      if (player_name.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(player_name);
      }
      if (role_unique_name.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(role_unique_name);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserProfile other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.task_finished_total_cnt != 0) {
        task_finished_total_cnt = other.task_finished_total_cnt;
      }
      if (other.task_finished_distinct_cnt != 0) {
        task_finished_distinct_cnt = other.task_finished_distinct_cnt;
      }
      if (other.exercise_finished_cnt != 0) {
        exercise_finished_cnt = other.exercise_finished_cnt;
      }
      if (other.time_spend_on_learning != 0) {
        time_spend_on_learning = other.time_spend_on_learning;
      }
      if (other.task_star_cnt != 0) {
        task_star_cnt = other.task_star_cnt;
      }
      if (other.task_average_score != 0F) {
        task_average_score = other.task_average_score;
      }
      if (other.task_average_score_pronounce != 0F) {
        task_average_score_pronounce = other.task_average_score_pronounce;
      }
      if (other.task_average_score_intonation != 0F) {
        task_average_score_intonation = other.task_average_score_intonation;
      }
      if (other.task_average_score_fluency != 0F) {
        task_average_score_fluency = other.task_average_score_fluency;
      }
      if (other.task_average_score_richness != 0F) {
        task_average_score_richness = other.task_average_score_richness;
      }
      if (other.task_average_score_integrity != 0F) {
        task_average_score_integrity = other.task_average_score_integrity;
      }
      if (other.is_profile_visible != 0) {
        is_profile_visible = other.is_profile_visible;
      }
      if (other.voice_information_ != null) {
        if (voice_information_ == null) {
          voice_information = new global::Msg.incentive.PB_UserVoiceInformation();
        }
        voice_information.MergeFrom(other.voice_information);
      }
      if (other.head_url.Length != 0) {
        head_url = other.head_url;
      }
      if (other.style_id.Length != 0) {
        style_id = other.style_id;
      }
      if (other.total_experience != 0) {
        total_experience = other.total_experience;
      }
      if (other.checkin_continue_days != 0) {
        checkin_continue_days = other.checkin_continue_days;
      }
      if (other.rank_info_ != null) {
        if (rank_info_ == null) {
          rank_info = new global::Msg.incentive.PB_RankInfo();
        }
        rank_info.MergeFrom(other.rank_info);
      }
      if (other.growth_data_ != null) {
        if (growth_data_ == null) {
          growth_data = new global::Msg.incentive.PB_UserGrowthData();
        }
        growth_data.MergeFrom(other.growth_data);
      }
      if (other.progress_stat_ != null) {
        if (progress_stat_ == null) {
          progress_stat = new global::Msg.incentive.PB_UserProgressStat();
        }
        progress_stat.MergeFrom(other.progress_stat);
      }
      if (other.dress_up_data_ != null) {
        if (dress_up_data_ == null) {
          dress_up_data = new global::PB_DressUpData();
        }
        dress_up_data.MergeFrom(other.dress_up_data);
      }
      if (other.head_item_id != 0L) {
        head_item_id = other.head_item_id;
      }
      if (other.head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        head_item_type = other.head_item_type;
      }
      if (other.has_created_role != false) {
        has_created_role = other.has_created_role;
      }
      if (other.friendship_info_ != null) {
        if (friendship_info_ == null) {
          friendship_info = new global::Msg.incentive.PB_UserFriendshipInfo();
        }
        friendship_info.MergeFrom(other.friendship_info);
      }
      if (other.player_name.Length != 0) {
        player_name = other.player_name;
      }
      if (other.role_unique_name.Length != 0) {
        role_unique_name = other.role_unique_name;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 32: {
            task_finished_total_cnt = input.ReadInt32();
            break;
          }
          case 40: {
            task_finished_distinct_cnt = input.ReadInt32();
            break;
          }
          case 48: {
            exercise_finished_cnt = input.ReadInt32();
            break;
          }
          case 56: {
            time_spend_on_learning = input.ReadInt32();
            break;
          }
          case 64: {
            task_star_cnt = input.ReadInt32();
            break;
          }
          case 77: {
            task_average_score = input.ReadFloat();
            break;
          }
          case 85: {
            task_average_score_pronounce = input.ReadFloat();
            break;
          }
          case 93: {
            task_average_score_intonation = input.ReadFloat();
            break;
          }
          case 101: {
            task_average_score_fluency = input.ReadFloat();
            break;
          }
          case 109: {
            task_average_score_richness = input.ReadFloat();
            break;
          }
          case 117: {
            task_average_score_integrity = input.ReadFloat();
            break;
          }
          case 120: {
            is_profile_visible = input.ReadInt32();
            break;
          }
          case 130: {
            if (voice_information_ == null) {
              voice_information = new global::Msg.incentive.PB_UserVoiceInformation();
            }
            input.ReadMessage(voice_information);
            break;
          }
          case 138: {
            head_url = input.ReadString();
            break;
          }
          case 146: {
            style_id = input.ReadString();
            break;
          }
          case 152: {
            total_experience = input.ReadInt32();
            break;
          }
          case 160: {
            checkin_continue_days = input.ReadInt32();
            break;
          }
          case 170: {
            if (rank_info_ == null) {
              rank_info = new global::Msg.incentive.PB_RankInfo();
            }
            input.ReadMessage(rank_info);
            break;
          }
          case 178: {
            if (growth_data_ == null) {
              growth_data = new global::Msg.incentive.PB_UserGrowthData();
            }
            input.ReadMessage(growth_data);
            break;
          }
          case 186: {
            if (progress_stat_ == null) {
              progress_stat = new global::Msg.incentive.PB_UserProgressStat();
            }
            input.ReadMessage(progress_stat);
            break;
          }
          case 202: {
            if (dress_up_data_ == null) {
              dress_up_data = new global::PB_DressUpData();
            }
            input.ReadMessage(dress_up_data);
            break;
          }
          case 208: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 216: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
          case 224: {
            has_created_role = input.ReadBool();
            break;
          }
          case 234: {
            if (friendship_info_ == null) {
              friendship_info = new global::Msg.incentive.PB_UserFriendshipInfo();
            }
            input.ReadMessage(friendship_info);
            break;
          }
          case 242: {
            player_name = input.ReadString();
            break;
          }
          case 250: {
            role_unique_name = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 32: {
            task_finished_total_cnt = input.ReadInt32();
            break;
          }
          case 40: {
            task_finished_distinct_cnt = input.ReadInt32();
            break;
          }
          case 48: {
            exercise_finished_cnt = input.ReadInt32();
            break;
          }
          case 56: {
            time_spend_on_learning = input.ReadInt32();
            break;
          }
          case 64: {
            task_star_cnt = input.ReadInt32();
            break;
          }
          case 77: {
            task_average_score = input.ReadFloat();
            break;
          }
          case 85: {
            task_average_score_pronounce = input.ReadFloat();
            break;
          }
          case 93: {
            task_average_score_intonation = input.ReadFloat();
            break;
          }
          case 101: {
            task_average_score_fluency = input.ReadFloat();
            break;
          }
          case 109: {
            task_average_score_richness = input.ReadFloat();
            break;
          }
          case 117: {
            task_average_score_integrity = input.ReadFloat();
            break;
          }
          case 120: {
            is_profile_visible = input.ReadInt32();
            break;
          }
          case 130: {
            if (voice_information_ == null) {
              voice_information = new global::Msg.incentive.PB_UserVoiceInformation();
            }
            input.ReadMessage(voice_information);
            break;
          }
          case 138: {
            head_url = input.ReadString();
            break;
          }
          case 146: {
            style_id = input.ReadString();
            break;
          }
          case 152: {
            total_experience = input.ReadInt32();
            break;
          }
          case 160: {
            checkin_continue_days = input.ReadInt32();
            break;
          }
          case 170: {
            if (rank_info_ == null) {
              rank_info = new global::Msg.incentive.PB_RankInfo();
            }
            input.ReadMessage(rank_info);
            break;
          }
          case 178: {
            if (growth_data_ == null) {
              growth_data = new global::Msg.incentive.PB_UserGrowthData();
            }
            input.ReadMessage(growth_data);
            break;
          }
          case 186: {
            if (progress_stat_ == null) {
              progress_stat = new global::Msg.incentive.PB_UserProgressStat();
            }
            input.ReadMessage(progress_stat);
            break;
          }
          case 202: {
            if (dress_up_data_ == null) {
              dress_up_data = new global::PB_DressUpData();
            }
            input.ReadMessage(dress_up_data);
            break;
          }
          case 208: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 216: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
          case 224: {
            has_created_role = input.ReadBool();
            break;
          }
          case 234: {
            if (friendship_info_ == null) {
              friendship_info = new global::Msg.incentive.PB_UserFriendshipInfo();
            }
            input.ReadMessage(friendship_info);
            break;
          }
          case 242: {
            player_name = input.ReadString();
            break;
          }
          case 250: {
            role_unique_name = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 用户好友关系信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserFriendshipInfo : pb::IMessage<PB_UserFriendshipInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserFriendshipInfo> _parser = new pb::MessageParser<PB_UserFriendshipInfo>(() => new PB_UserFriendshipInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserFriendshipInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserFriendshipInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserFriendshipInfo(PB_UserFriendshipInfo other) : this() {
      following_count_ = other.following_count_;
      follower_count_ = other.follower_count_;
      relationship_status_ = other.relationship_status_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserFriendshipInfo Clone() {
      return new PB_UserFriendshipInfo(this);
    }

    /// <summary>Field number for the "following_count" field.</summary>
    public const int following_countFieldNumber = 1;
    private int following_count_;
    /// <summary>
    /// 关注数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int following_count {
      get { return following_count_; }
      set {
        following_count_ = value;
      }
    }

    /// <summary>Field number for the "follower_count" field.</summary>
    public const int follower_countFieldNumber = 2;
    private int follower_count_;
    /// <summary>
    /// 被关注数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int follower_count {
      get { return follower_count_; }
      set {
        follower_count_ = value;
      }
    }

    /// <summary>Field number for the "relationship_status" field.</summary>
    public const int relationship_statusFieldNumber = 3;
    private global::PB_FriendshipType relationship_status_ = global::PB_FriendshipType.FNONE;
    /// <summary>
    /// 关系状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_FriendshipType relationship_status {
      get { return relationship_status_; }
      set {
        relationship_status_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserFriendshipInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserFriendshipInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (following_count != other.following_count) return false;
      if (follower_count != other.follower_count) return false;
      if (relationship_status != other.relationship_status) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (following_count != 0) hash ^= following_count.GetHashCode();
      if (follower_count != 0) hash ^= follower_count.GetHashCode();
      if (relationship_status != global::PB_FriendshipType.FNONE) hash ^= relationship_status.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (following_count != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(following_count);
      }
      if (follower_count != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(follower_count);
      }
      if (relationship_status != global::PB_FriendshipType.FNONE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) relationship_status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (following_count != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(following_count);
      }
      if (follower_count != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(follower_count);
      }
      if (relationship_status != global::PB_FriendshipType.FNONE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) relationship_status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (following_count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(following_count);
      }
      if (follower_count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(follower_count);
      }
      if (relationship_status != global::PB_FriendshipType.FNONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) relationship_status);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserFriendshipInfo other) {
      if (other == null) {
        return;
      }
      if (other.following_count != 0) {
        following_count = other.following_count;
      }
      if (other.follower_count != 0) {
        follower_count = other.follower_count;
      }
      if (other.relationship_status != global::PB_FriendshipType.FNONE) {
        relationship_status = other.relationship_status;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            following_count = input.ReadInt32();
            break;
          }
          case 16: {
            follower_count = input.ReadInt32();
            break;
          }
          case 24: {
            relationship_status = (global::PB_FriendshipType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            following_count = input.ReadInt32();
            break;
          }
          case 16: {
            follower_count = input.ReadInt32();
            break;
          }
          case 24: {
            relationship_status = (global::PB_FriendshipType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 设置用户个人信息权限请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_SetUserProfileAuthorityReq : pb::IMessage<CS_SetUserProfileAuthorityReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_SetUserProfileAuthorityReq> _parser = new pb::MessageParser<CS_SetUserProfileAuthorityReq>(() => new CS_SetUserProfileAuthorityReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_SetUserProfileAuthorityReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserProfileAuthorityReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserProfileAuthorityReq(CS_SetUserProfileAuthorityReq other) : this() {
      user_id_ = other.user_id_;
      is_profile_visible_ = other.is_profile_visible_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserProfileAuthorityReq Clone() {
      return new CS_SetUserProfileAuthorityReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    /// <summary>
    /// 用户ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "is_profile_visible" field.</summary>
    public const int is_profile_visibleFieldNumber = 2;
    private int is_profile_visible_;
    /// <summary>
    /// 是否显示个人信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int is_profile_visible {
      get { return is_profile_visible_; }
      set {
        is_profile_visible_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_SetUserProfileAuthorityReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_SetUserProfileAuthorityReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (is_profile_visible != other.is_profile_visible) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (is_profile_visible != 0) hash ^= is_profile_visible.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (is_profile_visible != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(is_profile_visible);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (is_profile_visible != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(is_profile_visible);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (is_profile_visible != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(is_profile_visible);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_SetUserProfileAuthorityReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.is_profile_visible != 0) {
        is_profile_visible = other.is_profile_visible;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            is_profile_visible = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            is_profile_visible = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 设置用户个人信息权限响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_SetUserProfileAuthorityResp : pb::IMessage<SC_SetUserProfileAuthorityResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_SetUserProfileAuthorityResp> _parser = new pb::MessageParser<SC_SetUserProfileAuthorityResp>(() => new SC_SetUserProfileAuthorityResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_SetUserProfileAuthorityResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserProfileAuthorityResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserProfileAuthorityResp(SC_SetUserProfileAuthorityResp other) : this() {
      err_code_ = other.err_code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserProfileAuthorityResp Clone() {
      return new SC_SetUserProfileAuthorityResp(this);
    }

    /// <summary>Field number for the "err_code" field.</summary>
    public const int err_codeFieldNumber = 1;
    private int err_code_;
    /// <summary>
    /// 错误代码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int err_code {
      get { return err_code_; }
      set {
        err_code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_SetUserProfileAuthorityResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_SetUserProfileAuthorityResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (err_code != other.err_code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (err_code != 0) hash ^= err_code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (err_code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(err_code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_SetUserProfileAuthorityResp other) {
      if (other == null) {
        return;
      }
      if (other.err_code != 0) {
        err_code = other.err_code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 获取用户个人信息权限请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetUserProfileAuthorityReq : pb::IMessage<CS_GetUserProfileAuthorityReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetUserProfileAuthorityReq> _parser = new pb::MessageParser<CS_GetUserProfileAuthorityReq>(() => new CS_GetUserProfileAuthorityReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetUserProfileAuthorityReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserProfileAuthorityReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserProfileAuthorityReq(CS_GetUserProfileAuthorityReq other) : this() {
      user_id_ = other.user_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserProfileAuthorityReq Clone() {
      return new CS_GetUserProfileAuthorityReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    /// <summary>
    /// 用户ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetUserProfileAuthorityReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetUserProfileAuthorityReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetUserProfileAuthorityReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 获取用户个人信息权限响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetUserProfileAuthorityResp : pb::IMessage<SC_GetUserProfileAuthorityResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetUserProfileAuthorityResp> _parser = new pb::MessageParser<SC_GetUserProfileAuthorityResp>(() => new SC_GetUserProfileAuthorityResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetUserProfileAuthorityResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserProfileAuthorityResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserProfileAuthorityResp(SC_GetUserProfileAuthorityResp other) : this() {
      user_id_ = other.user_id_;
      is_profile_visible_ = other.is_profile_visible_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserProfileAuthorityResp Clone() {
      return new SC_GetUserProfileAuthorityResp(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    /// <summary>
    /// 用户ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "is_profile_visible" field.</summary>
    public const int is_profile_visibleFieldNumber = 2;
    private int is_profile_visible_;
    /// <summary>
    /// 是否显示个人信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int is_profile_visible {
      get { return is_profile_visible_; }
      set {
        is_profile_visible_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetUserProfileAuthorityResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetUserProfileAuthorityResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (is_profile_visible != other.is_profile_visible) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (is_profile_visible != 0) hash ^= is_profile_visible.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (is_profile_visible != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(is_profile_visible);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (is_profile_visible != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(is_profile_visible);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (is_profile_visible != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(is_profile_visible);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetUserProfileAuthorityResp other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.is_profile_visible != 0) {
        is_profile_visible = other.is_profile_visible;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            is_profile_visible = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            is_profile_visible = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 发送反馈请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_SendFeedbackReq : pb::IMessage<CS_SendFeedbackReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_SendFeedbackReq> _parser = new pb::MessageParser<CS_SendFeedbackReq>(() => new CS_SendFeedbackReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_SendFeedbackReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SendFeedbackReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SendFeedbackReq(CS_SendFeedbackReq other) : this() {
      user_id_ = other.user_id_;
      feedback_ = other.feedback_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SendFeedbackReq Clone() {
      return new CS_SendFeedbackReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    /// <summary>
    /// 用户ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "feedback" field.</summary>
    public const int feedbackFieldNumber = 2;
    private string feedback_ = "";
    /// <summary>
    /// 反馈信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string feedback {
      get { return feedback_; }
      set {
        feedback_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_SendFeedbackReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_SendFeedbackReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (feedback != other.feedback) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (feedback.Length != 0) hash ^= feedback.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (feedback.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(feedback);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (feedback.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(feedback);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (feedback.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(feedback);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_SendFeedbackReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.feedback.Length != 0) {
        feedback = other.feedback;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            feedback = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            feedback = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 发送反馈响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_SendFeedbackResp : pb::IMessage<SC_SendFeedbackResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_SendFeedbackResp> _parser = new pb::MessageParser<SC_SendFeedbackResp>(() => new SC_SendFeedbackResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_SendFeedbackResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SendFeedbackResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SendFeedbackResp(SC_SendFeedbackResp other) : this() {
      err_code_ = other.err_code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SendFeedbackResp Clone() {
      return new SC_SendFeedbackResp(this);
    }

    /// <summary>Field number for the "err_code" field.</summary>
    public const int err_codeFieldNumber = 1;
    private int err_code_;
    /// <summary>
    /// 错误代码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int err_code {
      get { return err_code_; }
      set {
        err_code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_SendFeedbackResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_SendFeedbackResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (err_code != other.err_code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (err_code != 0) hash ^= err_code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (err_code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(err_code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_SendFeedbackResp other) {
      if (other == null) {
        return;
      }
      if (other.err_code != 0) {
        err_code = other.err_code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 设置用户语音信息请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_SetUserVoiceInformationReq : pb::IMessage<CS_SetUserVoiceInformationReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_SetUserVoiceInformationReq> _parser = new pb::MessageParser<CS_SetUserVoiceInformationReq>(() => new CS_SetUserVoiceInformationReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_SetUserVoiceInformationReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserVoiceInformationReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserVoiceInformationReq(CS_SetUserVoiceInformationReq other) : this() {
      voice_id_ = other.voice_id_;
      voice_time_length_ = other.voice_time_length_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserVoiceInformationReq Clone() {
      return new CS_SetUserVoiceInformationReq(this);
    }

    /// <summary>Field number for the "voice_id" field.</summary>
    public const int voice_idFieldNumber = 1;
    private long voice_id_;
    /// <summary>
    /// 语音ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long voice_id {
      get { return voice_id_; }
      set {
        voice_id_ = value;
      }
    }

    /// <summary>Field number for the "voice_time_length" field.</summary>
    public const int voice_time_lengthFieldNumber = 2;
    private int voice_time_length_;
    /// <summary>
    /// 语音长度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int voice_time_length {
      get { return voice_time_length_; }
      set {
        voice_time_length_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_SetUserVoiceInformationReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_SetUserVoiceInformationReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (voice_id != other.voice_id) return false;
      if (voice_time_length != other.voice_time_length) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (voice_id != 0L) hash ^= voice_id.GetHashCode();
      if (voice_time_length != 0) hash ^= voice_time_length.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (voice_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(voice_id);
      }
      if (voice_time_length != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(voice_time_length);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (voice_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(voice_id);
      }
      if (voice_time_length != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(voice_time_length);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (voice_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(voice_id);
      }
      if (voice_time_length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(voice_time_length);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_SetUserVoiceInformationReq other) {
      if (other == null) {
        return;
      }
      if (other.voice_id != 0L) {
        voice_id = other.voice_id;
      }
      if (other.voice_time_length != 0) {
        voice_time_length = other.voice_time_length;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            voice_id = input.ReadInt64();
            break;
          }
          case 16: {
            voice_time_length = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            voice_id = input.ReadInt64();
            break;
          }
          case 16: {
            voice_time_length = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 设置用户语音信息响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_SetUserVoiceInformationAck : pb::IMessage<SC_SetUserVoiceInformationAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_SetUserVoiceInformationAck> _parser = new pb::MessageParser<SC_SetUserVoiceInformationAck>(() => new SC_SetUserVoiceInformationAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_SetUserVoiceInformationAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserVoiceInformationAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserVoiceInformationAck(SC_SetUserVoiceInformationAck other) : this() {
      err_code_ = other.err_code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserVoiceInformationAck Clone() {
      return new SC_SetUserVoiceInformationAck(this);
    }

    /// <summary>Field number for the "err_code" field.</summary>
    public const int err_codeFieldNumber = 1;
    private int err_code_;
    /// <summary>
    /// 错误代码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int err_code {
      get { return err_code_; }
      set {
        err_code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_SetUserVoiceInformationAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_SetUserVoiceInformationAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (err_code != other.err_code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (err_code != 0) hash ^= err_code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (err_code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(err_code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_SetUserVoiceInformationAck other) {
      if (other == null) {
        return;
      }
      if (other.err_code != 0) {
        err_code = other.err_code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 设置用户画像信息请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_SetUserPortraitsReq : pb::IMessage<CS_SetUserPortraitsReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_SetUserPortraitsReq> _parser = new pb::MessageParser<CS_SetUserPortraitsReq>(() => new CS_SetUserPortraitsReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_SetUserPortraitsReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserPortraitsReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserPortraitsReq(CS_SetUserPortraitsReq other) : this() {
      interest_goal_ = other.interest_goal_;
      interest_scenes_ = other.interest_scenes_.Clone();
      language_level_ = other.language_level_;
      interest_goal_id_ = other.interest_goal_id_;
      interest_scene_ids_ = other.interest_scene_ids_.Clone();
      hobbies_ = other.hobbies_.Clone();
      expected_learning_time_ = other.expected_learning_time_;
      gender_ = other.gender_;
      age_ = other.age_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserPortraitsReq Clone() {
      return new CS_SetUserPortraitsReq(this);
    }

    /// <summary>Field number for the "interest_goal" field.</summary>
    public const int interest_goalFieldNumber = 1;
    private string interest_goal_ = "";
    /// <summary>
    /// 学习目标
    /// </summary>
    [global::System.ObsoleteAttribute]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string interest_goal {
      get { return interest_goal_; }
      set {
        interest_goal_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "interest_scenes" field.</summary>
    public const int interest_scenesFieldNumber = 2;
    private static readonly pb::FieldCodec<string> _repeated_interest_scenes_codec
        = pb::FieldCodec.ForString(18);
    private readonly pbc::RepeatedField<string> interest_scenes_ = new pbc::RepeatedField<string>();
    /// <summary>
    /// 学习场景列表
    /// </summary>
    [global::System.ObsoleteAttribute]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> interest_scenes {
      get { return interest_scenes_; }
    }

    /// <summary>Field number for the "language_level" field.</summary>
    public const int language_levelFieldNumber = 3;
    private string language_level_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string language_level {
      get { return language_level_; }
      set {
        language_level_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "interest_goal_id" field.</summary>
    public const int interest_goal_idFieldNumber = 4;
    private global::Msg.basic.PB_LearningGoalEnum interest_goal_id_ = global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE;
    /// <summary>
    /// 目标点ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_LearningGoalEnum interest_goal_id {
      get { return interest_goal_id_; }
      set {
        interest_goal_id_ = value;
      }
    }

    /// <summary>Field number for the "interest_scene_ids" field.</summary>
    public const int interest_scene_idsFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Msg.basic.PB_LearningSceneEnum> _repeated_interest_scene_ids_codec
        = pb::FieldCodec.ForEnum(42, x => (int) x, x => (global::Msg.basic.PB_LearningSceneEnum) x);
    private readonly pbc::RepeatedField<global::Msg.basic.PB_LearningSceneEnum> interest_scene_ids_ = new pbc::RepeatedField<global::Msg.basic.PB_LearningSceneEnum>();
    /// <summary>
    /// 场景ID列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.basic.PB_LearningSceneEnum> interest_scene_ids {
      get { return interest_scene_ids_; }
    }

    /// <summary>Field number for the "hobbies" field.</summary>
    public const int hobbiesFieldNumber = 6;
    private static readonly pb::FieldCodec<global::Msg.basic.PB_HobbyEnum> _repeated_hobbies_codec
        = pb::FieldCodec.ForEnum(50, x => (int) x, x => (global::Msg.basic.PB_HobbyEnum) x);
    private readonly pbc::RepeatedField<global::Msg.basic.PB_HobbyEnum> hobbies_ = new pbc::RepeatedField<global::Msg.basic.PB_HobbyEnum>();
    /// <summary>
    /// 兴趣爱好
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.basic.PB_HobbyEnum> hobbies {
      get { return hobbies_; }
    }

    /// <summary>Field number for the "expected_learning_time" field.</summary>
    public const int expected_learning_timeFieldNumber = 7;
    private global::Msg.basic.PB_LearningPlanEnum expected_learning_time_ = global::Msg.basic.PB_LearningPlanEnum.PLAN_NONE;
    /// <summary>
    /// 预期学习时长
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_LearningPlanEnum expected_learning_time {
      get { return expected_learning_time_; }
      set {
        expected_learning_time_ = value;
      }
    }

    /// <summary>Field number for the "gender" field.</summary>
    public const int genderFieldNumber = 8;
    private global::Msg.basic.PB_GenderEnum gender_ = global::Msg.basic.PB_GenderEnum.GNone;
    /// <summary>
    /// 性别
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_GenderEnum gender {
      get { return gender_; }
      set {
        gender_ = value;
      }
    }

    /// <summary>Field number for the "age" field.</summary>
    public const int ageFieldNumber = 9;
    private global::Msg.basic.PB_AgeEnum age_ = global::Msg.basic.PB_AgeEnum.AGE_NONE;
    /// <summary>
    /// 年龄
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_AgeEnum age {
      get { return age_; }
      set {
        age_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_SetUserPortraitsReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_SetUserPortraitsReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (interest_goal != other.interest_goal) return false;
      if(!interest_scenes_.Equals(other.interest_scenes_)) return false;
      if (language_level != other.language_level) return false;
      if (interest_goal_id != other.interest_goal_id) return false;
      if(!interest_scene_ids_.Equals(other.interest_scene_ids_)) return false;
      if(!hobbies_.Equals(other.hobbies_)) return false;
      if (expected_learning_time != other.expected_learning_time) return false;
      if (gender != other.gender) return false;
      if (age != other.age) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (interest_goal.Length != 0) hash ^= interest_goal.GetHashCode();
      hash ^= interest_scenes_.GetHashCode();
      if (language_level.Length != 0) hash ^= language_level.GetHashCode();
      if (interest_goal_id != global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE) hash ^= interest_goal_id.GetHashCode();
      hash ^= interest_scene_ids_.GetHashCode();
      hash ^= hobbies_.GetHashCode();
      if (expected_learning_time != global::Msg.basic.PB_LearningPlanEnum.PLAN_NONE) hash ^= expected_learning_time.GetHashCode();
      if (gender != global::Msg.basic.PB_GenderEnum.GNone) hash ^= gender.GetHashCode();
      if (age != global::Msg.basic.PB_AgeEnum.AGE_NONE) hash ^= age.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (interest_goal.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(interest_goal);
      }
      interest_scenes_.WriteTo(output, _repeated_interest_scenes_codec);
      if (language_level.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(language_level);
      }
      if (interest_goal_id != global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE) {
        output.WriteRawTag(32);
        output.WriteEnum((int) interest_goal_id);
      }
      interest_scene_ids_.WriteTo(output, _repeated_interest_scene_ids_codec);
      hobbies_.WriteTo(output, _repeated_hobbies_codec);
      if (expected_learning_time != global::Msg.basic.PB_LearningPlanEnum.PLAN_NONE) {
        output.WriteRawTag(56);
        output.WriteEnum((int) expected_learning_time);
      }
      if (gender != global::Msg.basic.PB_GenderEnum.GNone) {
        output.WriteRawTag(64);
        output.WriteEnum((int) gender);
      }
      if (age != global::Msg.basic.PB_AgeEnum.AGE_NONE) {
        output.WriteRawTag(72);
        output.WriteEnum((int) age);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (interest_goal.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(interest_goal);
      }
      interest_scenes_.WriteTo(ref output, _repeated_interest_scenes_codec);
      if (language_level.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(language_level);
      }
      if (interest_goal_id != global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE) {
        output.WriteRawTag(32);
        output.WriteEnum((int) interest_goal_id);
      }
      interest_scene_ids_.WriteTo(ref output, _repeated_interest_scene_ids_codec);
      hobbies_.WriteTo(ref output, _repeated_hobbies_codec);
      if (expected_learning_time != global::Msg.basic.PB_LearningPlanEnum.PLAN_NONE) {
        output.WriteRawTag(56);
        output.WriteEnum((int) expected_learning_time);
      }
      if (gender != global::Msg.basic.PB_GenderEnum.GNone) {
        output.WriteRawTag(64);
        output.WriteEnum((int) gender);
      }
      if (age != global::Msg.basic.PB_AgeEnum.AGE_NONE) {
        output.WriteRawTag(72);
        output.WriteEnum((int) age);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (interest_goal.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(interest_goal);
      }
      size += interest_scenes_.CalculateSize(_repeated_interest_scenes_codec);
      if (language_level.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(language_level);
      }
      if (interest_goal_id != global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) interest_goal_id);
      }
      size += interest_scene_ids_.CalculateSize(_repeated_interest_scene_ids_codec);
      size += hobbies_.CalculateSize(_repeated_hobbies_codec);
      if (expected_learning_time != global::Msg.basic.PB_LearningPlanEnum.PLAN_NONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) expected_learning_time);
      }
      if (gender != global::Msg.basic.PB_GenderEnum.GNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) gender);
      }
      if (age != global::Msg.basic.PB_AgeEnum.AGE_NONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) age);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_SetUserPortraitsReq other) {
      if (other == null) {
        return;
      }
      if (other.interest_goal.Length != 0) {
        interest_goal = other.interest_goal;
      }
      interest_scenes_.Add(other.interest_scenes_);
      if (other.language_level.Length != 0) {
        language_level = other.language_level;
      }
      if (other.interest_goal_id != global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE) {
        interest_goal_id = other.interest_goal_id;
      }
      interest_scene_ids_.Add(other.interest_scene_ids_);
      hobbies_.Add(other.hobbies_);
      if (other.expected_learning_time != global::Msg.basic.PB_LearningPlanEnum.PLAN_NONE) {
        expected_learning_time = other.expected_learning_time;
      }
      if (other.gender != global::Msg.basic.PB_GenderEnum.GNone) {
        gender = other.gender;
      }
      if (other.age != global::Msg.basic.PB_AgeEnum.AGE_NONE) {
        age = other.age;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            interest_goal = input.ReadString();
            break;
          }
          case 18: {
            interest_scenes_.AddEntriesFrom(input, _repeated_interest_scenes_codec);
            break;
          }
          case 26: {
            language_level = input.ReadString();
            break;
          }
          case 32: {
            interest_goal_id = (global::Msg.basic.PB_LearningGoalEnum) input.ReadEnum();
            break;
          }
          case 42:
          case 40: {
            interest_scene_ids_.AddEntriesFrom(input, _repeated_interest_scene_ids_codec);
            break;
          }
          case 50:
          case 48: {
            hobbies_.AddEntriesFrom(input, _repeated_hobbies_codec);
            break;
          }
          case 56: {
            expected_learning_time = (global::Msg.basic.PB_LearningPlanEnum) input.ReadEnum();
            break;
          }
          case 64: {
            gender = (global::Msg.basic.PB_GenderEnum) input.ReadEnum();
            break;
          }
          case 72: {
            age = (global::Msg.basic.PB_AgeEnum) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            interest_goal = input.ReadString();
            break;
          }
          case 18: {
            interest_scenes_.AddEntriesFrom(ref input, _repeated_interest_scenes_codec);
            break;
          }
          case 26: {
            language_level = input.ReadString();
            break;
          }
          case 32: {
            interest_goal_id = (global::Msg.basic.PB_LearningGoalEnum) input.ReadEnum();
            break;
          }
          case 42:
          case 40: {
            interest_scene_ids_.AddEntriesFrom(ref input, _repeated_interest_scene_ids_codec);
            break;
          }
          case 50:
          case 48: {
            hobbies_.AddEntriesFrom(ref input, _repeated_hobbies_codec);
            break;
          }
          case 56: {
            expected_learning_time = (global::Msg.basic.PB_LearningPlanEnum) input.ReadEnum();
            break;
          }
          case 64: {
            gender = (global::Msg.basic.PB_GenderEnum) input.ReadEnum();
            break;
          }
          case 72: {
            age = (global::Msg.basic.PB_AgeEnum) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 设置用户画像信息响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_SetUserPortraitsAck : pb::IMessage<SC_SetUserPortraitsAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_SetUserPortraitsAck> _parser = new pb::MessageParser<SC_SetUserPortraitsAck>(() => new SC_SetUserPortraitsAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_SetUserPortraitsAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserPortraitsAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserPortraitsAck(SC_SetUserPortraitsAck other) : this() {
      err_code_ = other.err_code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserPortraitsAck Clone() {
      return new SC_SetUserPortraitsAck(this);
    }

    /// <summary>Field number for the "err_code" field.</summary>
    public const int err_codeFieldNumber = 1;
    private int err_code_;
    /// <summary>
    /// 错误代码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int err_code {
      get { return err_code_; }
      set {
        err_code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_SetUserPortraitsAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_SetUserPortraitsAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (err_code != other.err_code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (err_code != 0) hash ^= err_code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (err_code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(err_code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_SetUserPortraitsAck other) {
      if (other == null) {
        return;
      }
      if (other.err_code != 0) {
        err_code = other.err_code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 获取用户画像信息请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetUserPortraitsReq : pb::IMessage<CS_GetUserPortraitsReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetUserPortraitsReq> _parser = new pb::MessageParser<CS_GetUserPortraitsReq>(() => new CS_GetUserPortraitsReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetUserPortraitsReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserPortraitsReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserPortraitsReq(CS_GetUserPortraitsReq other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserPortraitsReq Clone() {
      return new CS_GetUserPortraitsReq(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetUserPortraitsReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetUserPortraitsReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetUserPortraitsReq other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 获取用户画像信息响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetUserPortraitsAck : pb::IMessage<SC_GetUserPortraitsAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetUserPortraitsAck> _parser = new pb::MessageParser<SC_GetUserPortraitsAck>(() => new SC_GetUserPortraitsAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetUserPortraitsAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserPortraitsAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserPortraitsAck(SC_GetUserPortraitsAck other) : this() {
      err_code_ = other.err_code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserPortraitsAck Clone() {
      return new SC_GetUserPortraitsAck(this);
    }

    /// <summary>Field number for the "err_code" field.</summary>
    public const int err_codeFieldNumber = 1;
    private int err_code_;
    /// <summary>
    /// 错误代码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int err_code {
      get { return err_code_; }
      set {
        err_code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_UserPortraits data_;
    /// <summary>
    /// 用户画像信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserPortraits data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetUserPortraitsAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetUserPortraitsAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (err_code != other.err_code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (err_code != 0) hash ^= err_code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (err_code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(err_code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetUserPortraitsAck other) {
      if (other == null) {
        return;
      }
      if (other.err_code != 0) {
        err_code = other.err_code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_UserPortraits();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_UserPortraits();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_UserPortraits();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 用户画像信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserPortraits : pb::IMessage<PB_UserPortraits>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserPortraits> _parser = new pb::MessageParser<PB_UserPortraits>(() => new PB_UserPortraits());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserPortraits> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserPortraits() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserPortraits(PB_UserPortraits other) : this() {
      interest_goal_ = other.interest_goal_;
      interest_scenes_ = other.interest_scenes_.Clone();
      language_level_ = other.language_level_;
      interest_goal_id_ = other.interest_goal_id_;
      interest_scene_ids_ = other.interest_scene_ids_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserPortraits Clone() {
      return new PB_UserPortraits(this);
    }

    /// <summary>Field number for the "interest_goal" field.</summary>
    public const int interest_goalFieldNumber = 1;
    private string interest_goal_ = "";
    /// <summary>
    /// 学习目标
    /// </summary>
    [global::System.ObsoleteAttribute]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string interest_goal {
      get { return interest_goal_; }
      set {
        interest_goal_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "interest_scenes" field.</summary>
    public const int interest_scenesFieldNumber = 2;
    private static readonly pb::FieldCodec<string> _repeated_interest_scenes_codec
        = pb::FieldCodec.ForString(18);
    private readonly pbc::RepeatedField<string> interest_scenes_ = new pbc::RepeatedField<string>();
    /// <summary>
    /// 学习场景列表
    /// </summary>
    [global::System.ObsoleteAttribute]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> interest_scenes {
      get { return interest_scenes_; }
    }

    /// <summary>Field number for the "language_level" field.</summary>
    public const int language_levelFieldNumber = 3;
    private string language_level_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string language_level {
      get { return language_level_; }
      set {
        language_level_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "interest_goal_id" field.</summary>
    public const int interest_goal_idFieldNumber = 4;
    private global::Msg.basic.PB_LearningGoalEnum interest_goal_id_ = global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE;
    /// <summary>
    /// 兴趣点ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_LearningGoalEnum interest_goal_id {
      get { return interest_goal_id_; }
      set {
        interest_goal_id_ = value;
      }
    }

    /// <summary>Field number for the "interest_scene_ids" field.</summary>
    public const int interest_scene_idsFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Msg.basic.PB_LearningSceneEnum> _repeated_interest_scene_ids_codec
        = pb::FieldCodec.ForEnum(42, x => (int) x, x => (global::Msg.basic.PB_LearningSceneEnum) x);
    private readonly pbc::RepeatedField<global::Msg.basic.PB_LearningSceneEnum> interest_scene_ids_ = new pbc::RepeatedField<global::Msg.basic.PB_LearningSceneEnum>();
    /// <summary>
    /// 兴趣场景ID列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.basic.PB_LearningSceneEnum> interest_scene_ids {
      get { return interest_scene_ids_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserPortraits);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserPortraits other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (interest_goal != other.interest_goal) return false;
      if(!interest_scenes_.Equals(other.interest_scenes_)) return false;
      if (language_level != other.language_level) return false;
      if (interest_goal_id != other.interest_goal_id) return false;
      if(!interest_scene_ids_.Equals(other.interest_scene_ids_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (interest_goal.Length != 0) hash ^= interest_goal.GetHashCode();
      hash ^= interest_scenes_.GetHashCode();
      if (language_level.Length != 0) hash ^= language_level.GetHashCode();
      if (interest_goal_id != global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE) hash ^= interest_goal_id.GetHashCode();
      hash ^= interest_scene_ids_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (interest_goal.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(interest_goal);
      }
      interest_scenes_.WriteTo(output, _repeated_interest_scenes_codec);
      if (language_level.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(language_level);
      }
      if (interest_goal_id != global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE) {
        output.WriteRawTag(32);
        output.WriteEnum((int) interest_goal_id);
      }
      interest_scene_ids_.WriteTo(output, _repeated_interest_scene_ids_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (interest_goal.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(interest_goal);
      }
      interest_scenes_.WriteTo(ref output, _repeated_interest_scenes_codec);
      if (language_level.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(language_level);
      }
      if (interest_goal_id != global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE) {
        output.WriteRawTag(32);
        output.WriteEnum((int) interest_goal_id);
      }
      interest_scene_ids_.WriteTo(ref output, _repeated_interest_scene_ids_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (interest_goal.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(interest_goal);
      }
      size += interest_scenes_.CalculateSize(_repeated_interest_scenes_codec);
      if (language_level.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(language_level);
      }
      if (interest_goal_id != global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) interest_goal_id);
      }
      size += interest_scene_ids_.CalculateSize(_repeated_interest_scene_ids_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserPortraits other) {
      if (other == null) {
        return;
      }
      if (other.interest_goal.Length != 0) {
        interest_goal = other.interest_goal;
      }
      interest_scenes_.Add(other.interest_scenes_);
      if (other.language_level.Length != 0) {
        language_level = other.language_level;
      }
      if (other.interest_goal_id != global::Msg.basic.PB_LearningGoalEnum.GOAL_NONE) {
        interest_goal_id = other.interest_goal_id;
      }
      interest_scene_ids_.Add(other.interest_scene_ids_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            interest_goal = input.ReadString();
            break;
          }
          case 18: {
            interest_scenes_.AddEntriesFrom(input, _repeated_interest_scenes_codec);
            break;
          }
          case 26: {
            language_level = input.ReadString();
            break;
          }
          case 32: {
            interest_goal_id = (global::Msg.basic.PB_LearningGoalEnum) input.ReadEnum();
            break;
          }
          case 42:
          case 40: {
            interest_scene_ids_.AddEntriesFrom(input, _repeated_interest_scene_ids_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            interest_goal = input.ReadString();
            break;
          }
          case 18: {
            interest_scenes_.AddEntriesFrom(ref input, _repeated_interest_scenes_codec);
            break;
          }
          case 26: {
            language_level = input.ReadString();
            break;
          }
          case 32: {
            interest_goal_id = (global::Msg.basic.PB_LearningGoalEnum) input.ReadEnum();
            break;
          }
          case 42:
          case 40: {
            interest_scene_ids_.AddEntriesFrom(ref input, _repeated_interest_scene_ids_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 获取用户画像信息请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserPortraitsReq : pb::IMessage<SS_GetUserPortraitsReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserPortraitsReq> _parser = new pb::MessageParser<SS_GetUserPortraitsReq>(() => new SS_GetUserPortraitsReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserPortraitsReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserPortraitsReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserPortraitsReq(SS_GetUserPortraitsReq other) : this() {
      user_id_ = other.user_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserPortraitsReq Clone() {
      return new SS_GetUserPortraitsReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    /// <summary>
    /// 用户ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserPortraitsReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserPortraitsReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserPortraitsReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 获取用户画像信息响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserPortraitsAck : pb::IMessage<SS_GetUserPortraitsAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserPortraitsAck> _parser = new pb::MessageParser<SS_GetUserPortraitsAck>(() => new SS_GetUserPortraitsAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserPortraitsAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserPortraitsAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserPortraitsAck(SS_GetUserPortraitsAck other) : this() {
      err_code_ = other.err_code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserPortraitsAck Clone() {
      return new SS_GetUserPortraitsAck(this);
    }

    /// <summary>Field number for the "err_code" field.</summary>
    public const int err_codeFieldNumber = 1;
    private int err_code_;
    /// <summary>
    /// 错误代码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int err_code {
      get { return err_code_; }
      set {
        err_code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_UserPortraits data_;
    /// <summary>
    /// 用户画像信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserPortraits data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserPortraitsAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserPortraitsAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (err_code != other.err_code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (err_code != 0) hash ^= err_code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (err_code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(err_code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserPortraitsAck other) {
      if (other == null) {
        return;
      }
      if (other.err_code != 0) {
        err_code = other.err_code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_UserPortraits();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_UserPortraits();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_UserPortraits();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 设置用户语言等级请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_SetUserLanguageLevelReq : pb::IMessage<CS_SetUserLanguageLevelReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_SetUserLanguageLevelReq> _parser = new pb::MessageParser<CS_SetUserLanguageLevelReq>(() => new CS_SetUserLanguageLevelReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_SetUserLanguageLevelReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[23]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserLanguageLevelReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserLanguageLevelReq(CS_SetUserLanguageLevelReq other) : this() {
      language_level_ = other.language_level_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserLanguageLevelReq Clone() {
      return new CS_SetUserLanguageLevelReq(this);
    }

    /// <summary>Field number for the "language_level" field.</summary>
    public const int language_levelFieldNumber = 1;
    private string language_level_ = "";
    /// <summary>
    /// 语言等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string language_level {
      get { return language_level_; }
      set {
        language_level_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_SetUserLanguageLevelReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_SetUserLanguageLevelReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (language_level != other.language_level) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (language_level.Length != 0) hash ^= language_level.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (language_level.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(language_level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (language_level.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(language_level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (language_level.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(language_level);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_SetUserLanguageLevelReq other) {
      if (other == null) {
        return;
      }
      if (other.language_level.Length != 0) {
        language_level = other.language_level;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            language_level = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            language_level = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 设置用户语言等级响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_SetUserLanguageLevelAck : pb::IMessage<SC_SetUserLanguageLevelAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_SetUserLanguageLevelAck> _parser = new pb::MessageParser<SC_SetUserLanguageLevelAck>(() => new SC_SetUserLanguageLevelAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_SetUserLanguageLevelAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[24]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserLanguageLevelAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserLanguageLevelAck(SC_SetUserLanguageLevelAck other) : this() {
      err_code_ = other.err_code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserLanguageLevelAck Clone() {
      return new SC_SetUserLanguageLevelAck(this);
    }

    /// <summary>Field number for the "err_code" field.</summary>
    public const int err_codeFieldNumber = 1;
    private int err_code_;
    /// <summary>
    /// 错误代码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int err_code {
      get { return err_code_; }
      set {
        err_code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_SetUserLanguageLevelAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_SetUserLanguageLevelAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (err_code != other.err_code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (err_code != 0) hash ^= err_code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (err_code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(err_code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (err_code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(err_code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_SetUserLanguageLevelAck other) {
      if (other == null) {
        return;
      }
      if (other.err_code != 0) {
        err_code = other.err_code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            err_code = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 用户进度统计信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserProgressStat : pb::IMessage<PB_UserProgressStat>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserProgressStat> _parser = new pb::MessageParser<PB_UserProgressStat>(() => new PB_UserProgressStat());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserProgressStat> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[25]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserProgressStat() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserProgressStat(PB_UserProgressStat other) : this() {
      vocab_list_ = other.vocab_list_.Clone();
      xp_list_ = other.xp_list_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserProgressStat Clone() {
      return new PB_UserProgressStat(this);
    }

    /// <summary>Field number for the "vocab_list" field.</summary>
    public const int vocab_listFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.incentive.PB_UserProgressStatItem> _repeated_vocab_list_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.incentive.PB_UserProgressStatItem.Parser);
    private readonly pbc::RepeatedField<global::Msg.incentive.PB_UserProgressStatItem> vocab_list_ = new pbc::RepeatedField<global::Msg.incentive.PB_UserProgressStatItem>();
    /// <summary>
    /// 词汇列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.incentive.PB_UserProgressStatItem> vocab_list {
      get { return vocab_list_; }
    }

    /// <summary>Field number for the "xp_list" field.</summary>
    public const int xp_listFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Msg.incentive.PB_UserProgressStatItem> _repeated_xp_list_codec
        = pb::FieldCodec.ForMessage(18, global::Msg.incentive.PB_UserProgressStatItem.Parser);
    private readonly pbc::RepeatedField<global::Msg.incentive.PB_UserProgressStatItem> xp_list_ = new pbc::RepeatedField<global::Msg.incentive.PB_UserProgressStatItem>();
    /// <summary>
    /// 经验值列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.incentive.PB_UserProgressStatItem> xp_list {
      get { return xp_list_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserProgressStat);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserProgressStat other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!vocab_list_.Equals(other.vocab_list_)) return false;
      if(!xp_list_.Equals(other.xp_list_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= vocab_list_.GetHashCode();
      hash ^= xp_list_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      vocab_list_.WriteTo(output, _repeated_vocab_list_codec);
      xp_list_.WriteTo(output, _repeated_xp_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      vocab_list_.WriteTo(ref output, _repeated_vocab_list_codec);
      xp_list_.WriteTo(ref output, _repeated_xp_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += vocab_list_.CalculateSize(_repeated_vocab_list_codec);
      size += xp_list_.CalculateSize(_repeated_xp_list_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserProgressStat other) {
      if (other == null) {
        return;
      }
      vocab_list_.Add(other.vocab_list_);
      xp_list_.Add(other.xp_list_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            vocab_list_.AddEntriesFrom(input, _repeated_vocab_list_codec);
            break;
          }
          case 18: {
            xp_list_.AddEntriesFrom(input, _repeated_xp_list_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            vocab_list_.AddEntriesFrom(ref input, _repeated_vocab_list_codec);
            break;
          }
          case 18: {
            xp_list_.AddEntriesFrom(ref input, _repeated_xp_list_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 用户进度统计信息项
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserProgressStatItem : pb::IMessage<PB_UserProgressStatItem>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserProgressStatItem> _parser = new pb::MessageParser<PB_UserProgressStatItem>(() => new PB_UserProgressStatItem());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserProgressStatItem> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[26]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserProgressStatItem() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserProgressStatItem(PB_UserProgressStatItem other) : this() {
      ts_ = other.ts_;
      num_ = other.num_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserProgressStatItem Clone() {
      return new PB_UserProgressStatItem(this);
    }

    /// <summary>Field number for the "ts" field.</summary>
    public const int tsFieldNumber = 1;
    private long ts_;
    /// <summary>
    /// 时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long ts {
      get { return ts_; }
      set {
        ts_ = value;
      }
    }

    /// <summary>Field number for the "num" field.</summary>
    public const int numFieldNumber = 2;
    private long num_;
    /// <summary>
    /// 数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long num {
      get { return num_; }
      set {
        num_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserProgressStatItem);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserProgressStatItem other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ts != other.ts) return false;
      if (num != other.num) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ts != 0L) hash ^= ts.GetHashCode();
      if (num != 0L) hash ^= num.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ts != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(ts);
      }
      if (num != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(num);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ts != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(ts);
      }
      if (num != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(num);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ts != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(ts);
      }
      if (num != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(num);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserProgressStatItem other) {
      if (other == null) {
        return;
      }
      if (other.ts != 0L) {
        ts = other.ts;
      }
      if (other.num != 0L) {
        num = other.num;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ts = input.ReadInt64();
            break;
          }
          case 16: {
            num = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            ts = input.ReadInt64();
            break;
          }
          case 16: {
            num = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 设置用户头像信息请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_SetUserHeadItemReq : pb::IMessage<CS_SetUserHeadItemReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_SetUserHeadItemReq> _parser = new pb::MessageParser<CS_SetUserHeadItemReq>(() => new CS_SetUserHeadItemReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_SetUserHeadItemReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[27]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserHeadItemReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserHeadItemReq(CS_SetUserHeadItemReq other) : this() {
      head_item_id_ = other.head_item_id_;
      head_item_type_ = other.head_item_type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserHeadItemReq Clone() {
      return new CS_SetUserHeadItemReq(this);
    }

    /// <summary>Field number for the "head_item_id" field.</summary>
    public const int head_item_idFieldNumber = 1;
    private long head_item_id_;
    /// <summary>
    /// 头像ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long head_item_id {
      get { return head_item_id_; }
      set {
        head_item_id_ = value;
      }
    }

    /// <summary>Field number for the "head_item_type" field.</summary>
    public const int head_item_typeFieldNumber = 2;
    private global::PB_HeadItemType head_item_type_ = global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED;
    /// <summary>
    /// 头像类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_HeadItemType head_item_type {
      get { return head_item_type_; }
      set {
        head_item_type_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_SetUserHeadItemReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_SetUserHeadItemReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (head_item_id != other.head_item_id) return false;
      if (head_item_type != other.head_item_type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (head_item_id != 0L) hash ^= head_item_id.GetHashCode();
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) hash ^= head_item_type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (head_item_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(16);
        output.WriteEnum((int) head_item_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (head_item_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(16);
        output.WriteEnum((int) head_item_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (head_item_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) head_item_type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_SetUserHeadItemReq other) {
      if (other == null) {
        return;
      }
      if (other.head_item_id != 0L) {
        head_item_id = other.head_item_id;
      }
      if (other.head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        head_item_type = other.head_item_type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 16: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 16: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 设置用户头像信息响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_SetUserHeadItemAck : pb::IMessage<SC_SetUserHeadItemAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_SetUserHeadItemAck> _parser = new pb::MessageParser<SC_SetUserHeadItemAck>(() => new SC_SetUserHeadItemAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_SetUserHeadItemAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.ProfileReflection.Descriptor.MessageTypes[28]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserHeadItemAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserHeadItemAck(SC_SetUserHeadItemAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserHeadItemAck Clone() {
      return new SC_SetUserHeadItemAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    /// <summary>
    /// 响应码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_SetUserHeadItemAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_SetUserHeadItemAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_SetUserHeadItemAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

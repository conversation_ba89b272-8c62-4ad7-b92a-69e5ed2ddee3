/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Main
{
    public partial class VoiceChatMatchWindw : UIBindT
    {
        public override string pkgName => "Main";
        public override string comName => "VoiceChatMatchWindw";

        public Controller preMatchState;
        public GImage BG;
        public GImage cir3;
        public GImage cir2;
        public GTextField headerTf;
        public GTextField countDownTf;
        public GTextField despTf;
        public GButton cancelBtn;
        public GButton retryBtn;
        public GButton startBtn;
        public GGroup Btn;
        public GTextField MatchResult1;
        public GTextField MatchResult2;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            preMatchState = com.GetControllerAt(0);
            BG = (GImage)com.GetChildAt(0);
            cir3 = (GImage)com.GetChildAt(1);
            cir2 = (GImage)com.GetChildAt(2);
            headerTf = (GTextField)com.GetChildAt(5);
            countDownTf = (GTextField)com.GetChildAt(6);
            despTf = (GTextField)com.GetChildAt(7);
            cancelBtn = (GButton)com.GetChildAt(8);
            retryBtn = (GButton)com.GetChildAt(9);
            startBtn = (GButton)com.GetChildAt(10);
            Btn = (GGroup)com.GetChildAt(11);
            MatchResult1 = (GTextField)com.GetChildAt(12);
            MatchResult2 = (GTextField)com.GetChildAt(13);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            preMatchState = null;
            BG = null;
            cir3 = null;
            cir2 = null;
            headerTf = null;
            countDownTf = null;
            despTf = null;
            cancelBtn = null;
            retryBtn = null;
            startBtn = null;
            Btn = null;
            MatchResult1 = null;
            MatchResult2 = null;
        }
    }
}
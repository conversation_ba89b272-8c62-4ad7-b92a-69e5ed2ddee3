﻿using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;

public class AudioStreamPlayer : MonoBehaviour
{
    private Queue<float> _audioBuffer = new Queue<float>(); // 缓冲队列
   
    public int audioBuffer2Len = 0;
    private int _sampleRate = 16000; // PCM 音频采样率
    private int _channels = 1; // 单声道
    private bool _isBegin = false;
    private bool _isPlaying = false; // 播放标志
    private bool _isStreamEnded = false; // 是否收到最后一段数据
    private bool _stopRequested = false; // 主线程中处理停止请求
    private object _bufferLock = new object(); // 锁对象
    private AudioSource _audioSource;

    private long _recordId;
    private Action<long> _playbackEnded; // 播放结束事件

    public void SetAudioSource(AudioSource audioSource)
    {
        this._audioSource = audioSource;
    }

    
    public void Begin(long recordId,Action<long> endBack)
    {
        VFDebug.Log("ASP::  begin!");
        if (_audioSource == null)
        {
            _audioSource = gameObject.AddComponent<AudioSource>();
            Debug.LogWarning("ASP::Create audio source");
        }

        
        _audioSource.playOnAwake = false;
        _audioSource.loop = true; // 循环播放以支持流式数据
        var ac = AudioClip.Create("AudioStream", _sampleRate *10, _channels, _sampleRate, true,OnAudioFilterReadHandler);
        
        if (ac == null)
        {
            Debug.LogError("ASP::Create AudioClip=null ");
        }
        
        _audioSource.clip = ac;
            
        _isBegin = true;
        _recordId = recordId;
        _playbackEnded = endBack;
    }
    

    /// <summary>
    /// 开始播放音频流
    /// </summary>
    public void StartAudio()
    {
        if (_isPlaying) return;

        lock (_bufferLock)
        {
            _audioBuffer.Clear(); // 清空旧数据
        }

        _isStreamEnded = false; // 重置流结束标志
        _audioSource.Play();
        _isPlaying = true;
        VFDebug.Log("ASP::Audio streaming started.");
    }

    private bool allowStartAudioSlient = false;
    public void StartAudioSlient()
    {   
        if (_isPlaying) return;

        allowStartAudioSlient = true;
        
        VFDebug.Log("ASP::Audio streaming started Slient.");
    }



    public void Update()
    {
        // 在主线程中处理停止请求
        if (_stopRequested)
        {
            _stopRequested = false;
            // Debug.LogError("Update  -- _stopRequested：：" + _stopRequested);

            if (_audioSource && _audioSource.isPlaying)
            {
                Debug.Log("ASP:: STOP BY _stopRequested");
                _audioSource.Stop();
            }

            VFDebug.Log("ASP::Audio Update-> Stop +End.");
            _isPlaying = false;
            _audioBuffer.Clear(); // 清空缓冲区

            _playbackEnded?.Invoke(_recordId);

            //重置口型
            if (GSoundManager.instance.OVRLipSyncContext)
                GSoundManager.instance.OVRLipSyncContext.ResetContext();
        }

        //由于在 非主线程里（audioclip的 CALLBACK）驱动了 startaudio的回调，所以单独做了如下处理，否则会崩溃unity
        if (allowStartAudioSlient)
        {
            lock (_bufferLock)
            {
                _audioBuffer.Clear(); // 清空旧数据
            }

            _isStreamEnded = false; // 重置流结束标志
            
            //特殊特性备注：由于使用声网1v1时，回流的音频data数据本身就已经被声望播放过了，我们为了驱动口型不能让audioSource再播一遍
            //所以要将其整体声音（PCM）实际播放值压低，但不能为0；因为还要让OvrLip在处理音频时反向缩放回来。VolRatio是反向缩放率
            _audioSource.volume = 0.001f;
            GSoundManager.instance.OVRLipSyncContext?.SetVolRatio(0.9f/_audioSource.volume);////macOSX下此行 无效果,用0.9f避免过爆炸
            
            _audioSource.Play();
            _isPlaying = true;
            
            allowStartAudioSlient = false;
        }
    }

    /// <summary>
    /// 停止播放音频流
    /// </summary>
    public void StopAudio()
    {

        VFDebug.Log("ASP:: StopAudio!!!!!!!!!!");
        _isBegin = false;

        // 请求在主线程中停止音频
        _stopRequested = true;
        
        //1v1 对话中可能会关闭音量只用口型
        if (_audioSource != null)
        {
            //_audioSource.volume = 1;
        }
        else
        {
            VFDebug.LogError("ASP:: fail 2 recover vol");
        }

        //macOSX下不执行
        GSoundManager.instance.OVRLipSyncContext?.SetVolRatio(1);
        
        if (System.Threading.Thread.CurrentThread.ManagedThreadId == 1) 
        {
            //主线程时候 同一帧 停止
            Update();
        }
    }

    /// <summary>
    /// 添加音频数据到缓冲队列
    /// </summary>
    public void AddAudioData(byte[] audioData, bool isLastChunk = false)
    {
        if (!_isBegin) return;
        int initCount = _audioBuffer.Count;
        int sampleCount = audioData.Length / 2; // 每个样本 16 位（2 字节）
        lock (_bufferLock)
        {
            for (int i = 0; i < sampleCount; i++)
            {
                // PCM 数据转换为浮点值（范围 -1.0 到 1.0）
                short sample = (short)(audioData[i * 2] | (audioData[i * 2 + 1] << 8)); // 低字节 + 高字节
                _audioBuffer.Enqueue(sample / 32768.0f);
            }
            
            // 如果这是最后一段数据
            if (isLastChunk)
            {
                _isStreamEnded = true;
                // Debug.LogError("Received last audio chunk.");
            }
        }
        
        //Debug.LogWarning("ASP::增加样本 len="+ (_audioBuffer.Count - initCount));
    }


    /// <summary>
    /// 音频数据回调
    /// </summary>
    private void OnAudioFilterReadHandler(float[] data)
    {
        if (!_isPlaying)
        {
            Array.Clear(data, 0, data.Length); // 填充静音数据
            //Debug.LogError("not playing");
            return;
        }
        
        lock (_bufferLock)
        {
            int intCount = _audioBuffer.Count;
            for (int i = 0; i < data.Length; i++)
            {
                if (_audioBuffer.Count > 0)
                {
                    data[i] = _audioBuffer.Dequeue(); // 从缓冲队列取数据
                }
                else
                {
                    data[i] = 0; // 队列为空时填充静音

                    // 当队列为空且流已结束，触发播放结束逻辑
                    if (_isStreamEnded)
                    {
                        StopAudio(); // 停止播放
                        break;
                    }
                }
            }
            //Debug.LogWarning($"ASP::ReadHandler 被调用! 需要 {data.Length} 个样本,当前实际buffer样本量{_audioBuffer.Count} 消费了{intCount-_audioBuffer.Count}个样本");
        }
    }
    
    void OnAudioFilterRead(float[] data, int channels)
    {
        if (!_isBegin) return;
        Debug.LogWarning($"ASP::OnAudioFilterRead");
        
        if (GSoundManager.instance.LinkOVRLip && GSoundManager.instance.OVRLipSyncContext &&
            GSoundManager.instance.StreamAudioLinkAvatar)
        {
       
            GSoundManager.instance.OVRLipSyncContext.ProcessAudioSamples(data,channels);
        }
            
        
    }

    /// <summary>
    /// 检查缓冲区是否准备好播放
    /// </summary>
    public bool IsBufferReady()
    {
        lock (_bufferLock)
        {
            return _audioBuffer.Count > _sampleRate * 0.1f; // 至少缓冲 100ms 的音频数据
        }
    }
}

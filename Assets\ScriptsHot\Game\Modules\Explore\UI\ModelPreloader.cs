﻿using System.Collections.Generic;
using System.Threading.Tasks;
using ScriptsHot.Game.Modules.Explore.UI;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using ScriptsHot.Game.Modules.Scene.Utils;
using UIBind.Explore.Item;
using UnityEngine;

public class ModelPreloader:SingletonClass<ModelPreloader>
{
    private Dictionary<long, GameObject> _preloadedModels = new Dictionary<long, GameObject>();
    /// <summary>
    /// 预加载数量
    /// </summary>
    private int _preloadCount = 3; 

    private SceneController _sceneController;
    public ModelPreloader()
    {
    }

    /// <summary>
    /// 预加载某个范围内的 3D 模型
    /// </summary>
    public async void PreloadModels(List<ExploreItemData> dataList, int firstIndex,int preLoadCount = 2)
    {
        _preloadCount = preLoadCount;
        int start = Mathf.Max(0, firstIndex - _preloadCount);
        int end = Mathf.Min(dataList.Count - 1, firstIndex + _preloadCount);
        //VFDebug.Log($"[PreloadModels] 预载模型  dataList: {dataList.Count} preLoadCount : {preLoadCount}  start: {start} end: {end}");

        Dictionary<int,GameObject> models = new();

        for (int i = start; i <= end; i++)
        {
            bool is3d = dataList[i].Data.dialogTaskPreloadData.avatar.is3D;
            if(!is3d) continue;
            long entityId = dataList[i].Data.entityId;
            long avatarId = dataList[i].Data.dialogTaskPreloadData.avatar.avatarId;

            if (!_preloadedModels.ContainsKey(avatarId))
            {
                // Debug.LogError($"[PreloadModels] 预载模型  avatarId: {avatarId}");
                var model = await LoadModel(avatarId);
                if (model != null)
                {
                    model.name = entityId.ToString() + avatarId;
                    model.transform.SetParent(GameObject.Find("RenderTextureContainer").transform, false);
                    model.transform.localPosition = Vector3.zero;
                    model.transform.localRotation = Quaternion.Euler(0, 180, 0);
                    model.transform.localScale = Vector3.one; 
                    
                    models.Add(i,model);
                   
                    _preloadedModels[avatarId] = model;
                    ModelPool.ReleaseModel(avatarId, model); // 存入对象池
                }
            }
        }
        //预载图片
        ImagePreloader.Instance.PreloadImages(dataList, firstIndex,models,preLoadCount);
    }

    /// <summary>
    /// 清理不再需要的模型
    /// </summary>
    public void ClearUnusedModels(List<ExploreItemData> dataList, int firstIndex)
    {
        int start = Mathf.Max(0, firstIndex - _preloadCount);
        int end = Mathf.Min(dataList.Count - 1, firstIndex + _preloadCount);

        List<long> activeIds = new List<long>();
        for (int i = start; i <= end; i++)
        {
            activeIds.Add(dataList[i].Data.dialogTaskPreloadData.avatar.avatarId);
        }

        foreach (var key in new List<long>(_preloadedModels.Keys))
        {
            if (!activeIds.Contains(key))
            {
                ModelPool.ReleaseModel(key, _preloadedModels[key]);
                _preloadedModels.Remove(key);
            }
        }
    }

    /// <summary>
    /// 加载 3D 模型（异步）
    /// </summary>
    public async Task<GameObject> LoadModel(long avatarId)
    {
        if (_sceneController == null)
        {
            _sceneController = ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene);
        }
        var styleName = _sceneController.scene.GetComponent<UnitComponent>().GetStyleNameByAvatarId(avatarId);
        GameObject modelGo = null;
        if(_sceneController.scene.GetComponent<AvatarComponent>().avatarLoader != null)
            modelGo = await _sceneController.scene.GetComponent<AvatarComponent>().avatarLoader.LoadNAvatar(styleName,null,true);
        
        //加载GameObject
        GameObject parentObject = _sceneController.GetCharacterGameObject(this.GetCharacterGOPath());
        if (modelGo != null)
        {
            modelGo.transform.SetParent(parentObject.transform);
            GAvatarCtrl avatarCtrl = GetComponentOrAdd<GAvatarCtrl>(parentObject);
            
            //1初始化｜添加 口型插件
            this.InitLipSyncPlugin(avatarCtrl);
            //2口型插件相关绑定
            this.MatchLipSyncToHeadNode(modelGo, styleName, avatarCtrl);
            parentObject.SetActive(false);
        }
        else
        {
            return null;
        }
   
        return parentObject;
    }
    
    public  T GetComponentOrAdd<T>( GameObject obj) where T : Component
    {
        var t = obj.GetComponent<T>();

        if (t == null)
        {
            t = obj.AddComponent<T>();
        }

        return t;
    }
    
    public void InitLipSyncPlugin(GAvatarCtrl bindLipSyncAvatarCtrl)
    {
        var bindObj = bindLipSyncAvatarCtrl.audioSource.gameObject;
        //Debug.LogError("InitLipSyncPlugin obj="+bindObj.transform.parent.name+"/"+bindObj.name);
        
        //口型插件相关绑定
#if !UNITY_EDITOR_OSX //macOS上OVRLipSync无效
        bool added = bindObj.TryGetComponent<OVRLipSyncContext>(out OVRLipSyncContext lsc);
        if (!added)
        {
            lsc = bindObj.AddComponent<OVRLipSyncContext>();
        }
        lsc.audioLoopback = true;
        lsc.audioSource = bindLipSyncAvatarCtrl.audioSource;

        added =
 bindLipSyncAvatarCtrl.audioSource.gameObject.TryGetComponent<OVRLipSyncContextMorphTarget>(out OVRLipSyncContextMorphTarget lsm);
        if (!added)
        {
            lsm = bindLipSyncAvatarCtrl.audioSource.gameObject.AddComponent<OVRLipSyncContextMorphTarget>();
        }
#endif
    }
    public void MatchLipSyncToHeadNode(GameObject modelGo, string modelStyleName, GAvatarCtrl avatarCtrl)
    {
        var headGo = ObjectUtils.FindChild(modelGo.transform, "Head", true);
        if (headGo != null)
        {
#if !UNITY_EDITOR_OSX //macOS上OVRLipSync无效
            if (avatarCtrl != null)
            {

                var lsm = avatarCtrl.audioSource.GetComponent<OVRLipSyncContextMorphTarget>();
                if (lsm != null)
                {
                    var smr = headGo.GetComponent<SkinnedMeshRenderer>();
                    if (smr != null)
                    {
                        lsm.skinnedMeshRenderer = smr;
                        Debug.Log("Bind" + modelStyleName + " lsm.smr succ");
                    }
                    else
                    {
                        Debug.LogError("Failed to get SkinnedMeshRenderer from headGo, ModelName=" + modelStyleName);
                    }

                }
                else
                {
                    Debug.LogError("Failed to find OVRLipSyncContextMorphTarget on Character.ModelName=" + modelStyleName);
                }


            }
            else
            {
                Debug.LogError("Failed to find GAvatarCtrl on Character.ModelName=" + modelStyleName);
            }
#endif
        }
        else
        {
            Debug.LogError("Failed to find head-Node of Model name=" + modelStyleName);
        }
    }
    
    public string GetCharacterGOPath()
    {
        return "BaseCharacter";
    }
}

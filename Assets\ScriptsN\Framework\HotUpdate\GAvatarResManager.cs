//由于热更还没完善，先临时自己写一套加载avatar的功能,后续需要基建方合并到加载过程中

using System;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using LitJson;
using UnityEngine;
using YooAsset;
using Object = UnityEngine.Object;
using Texture = UnityEngine.Texture;

public class GAvatarResManager : MonoSingleton<GAvatarResManager>
{
    private ResourcePackage avatarPackage;
    public static string YooAssetPackage = "avatar";
    private HotUpdateFlow HotUpdate;

    // 保存RemoteServices实例，用于后续动态更新URL
    public HotUpdateCore.RemoteServices RemoteServices { get; private set; }
    
    
    public async UniTask InitPackage()
    {
        avatarPackage = YooAssets.CreatePackage(YooAssetPackage);
        var cp = new OfflinePlayModeParameters();
#if (UNITY_IOS && !UNITY_EDITOR) || UNITY_EDITOR_OSX
        cp.BuildinRootDirectory = Application.streamingAssetsPath+"/ArtRes/iOS";
#elif UNITY_ANDROID && !UNITY_EDITOR
        cp.BuildinRootDirectory = Application.streamingAssetsPath+"/ArtRes/android";
#elif UNITY_EDITOR||UNITY_STANDALONE
        cp.BuildinRootDirectory = Application.streamingAssetsPath+"/ArtRes/Standalone";
#endif
        var initOperation = avatarPackage.InitializeAsync(cp);
        await initOperation.Task;
    }

    public async Task<GameObject> LoadAsset<T>(string assetName, Transform parent = null) where T : Object
    {
        if(string.IsNullOrEmpty(assetName))
        {
            Debug.LogError("assetName is Empty");
            return null;
        }
        var assetHandle = avatarPackage.LoadAssetAsync<T>(assetName);
        await assetHandle.Task;
        if (assetHandle.AssetObject == null)
        {
            Debug.LogError($"Failed to load asset: {assetName}");
            return null;
        }
        GameObject instantiatedObject = Instantiate(assetHandle.AssetObject as GameObject, parent);
        return instantiatedObject;
    }
    
    public AssetHandle SyncLoad(string path)
    {
        //Debug.Log($"Loading asset [{path}]");
        return instance.avatarPackage.LoadAssetSync(path);
    }

    public bool CheckPath(string path)
    {
        return instance.avatarPackage.CheckLocationValid(path);
    }

    public async Task<Texture> LoadTexture(string assetName)
    {
        var assetHandle = avatarPackage.LoadAssetAsync<Texture>(assetName);
        await assetHandle.Task;
        if (assetHandle.AssetObject == null)
        {
            Debug.LogError($"Failed to load asset: {assetName}");
            return null;
        }
        return assetHandle.AssetObject as Texture;
    }

    public async Task<TextAsset> LoadText(string assetName)
    {
        var assetHandle = avatarPackage.LoadAssetAsync<TextAsset>(assetName);
        await assetHandle.Task;
        if (assetHandle.AssetObject == null)
        {
            Debug.LogError($"Failed to load asset: {assetName}");
            return null;
        }
        return assetHandle.AssetObject as TextAsset;
    }


    #region hotUpdate
    public async UniTask<HotUpdateCore.ReadyHotUpdateResult> ReadyHotUpdate(HotUpdateCore core , HotUpdateResult result)
    {
        if (result == null || core == null)
        {
            return HotUpdateCore.ReadyHotUpdateResult.Fail;
        }
        
        Debug.Log($"avatarRes.IsHotUpdateMode = {result.Mode}");
        
        if (result.Mode == HotUpdateMode.Offline)
        {
            await InitPackage();
            return HotUpdateCore.ReadyHotUpdateResult.Success;
        }
        else if (result.Mode == HotUpdateMode.Hotupdate)
        {
            try
            {
                string hotUpdateUrl = result.CdnUrl;
                if (string.IsNullOrEmpty(hotUpdateUrl))
                {
                    return HotUpdateCore.ReadyHotUpdateResult.Fail;
                }
                Debug.Log("HotUpdateFlow cdn: " + hotUpdateUrl);
                var initResult = await this.StartYooAssetsHotUpdate(hotUpdateUrl);
                if (!initResult)
                {
                    var errorMsg = HotUpdateLangCfg.GetStr(HUKey.updateSysException, (int) HotUpdateCore.ErrorCode.YooAssets_Init);    
                    VFDebug.LogError(errorMsg);
                    // core.OpenConfirmUI(errorMsg, null, null, 0);
                    return HotUpdateCore.ReadyHotUpdateResult.CanRetry;
                }
 
                //这步因为PackageVersion 和 cdn文件夹用的同一个名字，尝试优化调，可以省100ms时间
                var packageVersion = result.PkgVer;
                // var packageVersion = await this.UpdatePackageVersion();
                // if (string.IsNullOrEmpty(packageVersion))
                // {
                //     var errorMsg = HotUpdateLangCfg.GetStr(HUKey.updateSysException, (int) HotUpdateCore.ErrorCode.YooAssets_Update_Package_Version);  
                //     VFDebug.LogError(errorMsg);
                //     // core.OpenConfirmUI(errorMsg, null, null, 0);
                //     return HotUpdateCore.ReadyHotUpdateResult.CanRetry;
                // }

                var maifestResult = await this.UpdatePackageManifest(packageVersion);
                if (!maifestResult)
                {
                    var errorMsg = HotUpdateLangCfg.GetStr(HUKey.updateSysException, (int) HotUpdateCore.ErrorCode.YooAssets_Update_Package_Manifest);  
                    VFDebug.LogError(errorMsg);
                    // core.OpenConfirmUI(errorMsg, null, null, 0);
                    return HotUpdateCore.ReadyHotUpdateResult.CanRetry;
                }    
                await this.ReadyPackageDownload(result);
                return HotUpdateCore.ReadyHotUpdateResult.Success;
            }
            catch (Exception e)
            {
                Debug.LogError($"HotUpdateFlow Exception.{e.Message}");
                return HotUpdateCore.ReadyHotUpdateResult.Fail;
            }
        }
        return HotUpdateCore.ReadyHotUpdateResult.Fail;
    }

    private void InitYooAssetsPackage()
    {
        if (avatarPackage == null)
        {
            avatarPackage = YooAssets.CreatePackage(YooAssetPackage);
        }
    }

    private ResourcePackage GetPackage()
    {
        return avatarPackage;
    }
    
    private async UniTask<bool> StartYooAssetsHotUpdate(string cdnUrl)
    {
        InitYooAssetsPackage();

        var package = GetPackage();
        var initParameters = new HostPlayModeParameters();
        initParameters.DeliveryLoadServices = new DeliveryLoadServices();
        initParameters.DeliveryQueryServices = new DeliveryQueryServices();
        initParameters.BuildinQueryServices = new GameQueryServices();
        RemoteServices = new HotUpdateCore.RemoteServices(cdnUrl, cdnUrl);
        initParameters.RemoteServices = RemoteServices;

        if (package.InitializeStatus != EOperationStatus.Succeed)
        {
            var initOperation = package.InitializeAsync(initParameters);
            await initOperation;
            //
            if (initOperation.Status == EOperationStatus.Succeed)
            {
                Debug.Log("资源包初始化成功！");
                return true;
            }
            else
            {
                Debug.LogError($"资源包初始化失败：{initOperation.Error}");
                return false;
            }
        }
        return true;
    }
    
    private async UniTask<string> UpdatePackageVersion()
    {
        var package = GetPackage();
        var operation = package.UpdatePackageVersionAsync();
        await operation;
        //
        if (operation.Status == EOperationStatus.Succeed)
        {
            //更新成功
            string packageVersion = operation.PackageVersion;
            Debug.Log($"Updated package Version : {packageVersion}");
            return packageVersion;
        }
        else
        {
            //更新失败
            Debug.Log($"Updated package Version Error: {operation.Error}");
            return string.Empty;
        }
    }
    
    private async UniTask<bool> UpdatePackageManifest(string packageVersion)
    {
        var package = GetPackage();
        var operation = package.UpdatePackageManifestAsync(packageVersion, true);
        await operation;
        //
        if (operation.Status == EOperationStatus.Succeed)
        {
            Debug.Log($"Updated package Manifest Success");
            return true;
        }
        else
        {
            //更新失败
            Debug.Log($"Updated package Manifest Error. {operation.Error}");
            return false;
        }
    }
    
    private async UniTask<bool> ReadyPackageDownload(HotUpdateResult result)
    {
        int downloadingMaxNum = 10;
        int failedTryAgain = 3;
        var package = GetPackage();
        var downloader = package.CreateResourceDownloader(downloadingMaxNum, failedTryAgain);
        result.downloader = downloader;
        return true;
    }
    #endregion

}

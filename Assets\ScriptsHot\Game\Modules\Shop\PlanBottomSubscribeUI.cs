/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2025/02/11 16:03:34 星期二
# 功能：Nothing
****************************************************
*/

using System;
using FairyGUI;
using Modules.DataDot;
using Msg.incentive;

namespace ScriptsHot.Game.Modules.Shop
{
    [Obsolete]
    [TransitionUI(CutInEffect.BottomToTop)]
    public class PlanBottomSubscribeUI: BaseUI<UIBind.Shop.PlanBottomSubscribePanel>
    {
        public PlanBottomSubscribeUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top; //主UI层
        
        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(ui.btnSubscribe.onClick, OnBtnSubscribeClicked);
            AddUIEvent(ui.btnClose.onClick, OnBgClicked);
        }
        
        private void OnBgClicked()
        {
            var dot = new PopupClose();
            DataDotMgr.Collect(dot); 
            Hide();
        }

        private void OnBtnSubscribeClicked()
        {
            PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.popup;
            GetUI(UIConsts.PlanPromotionStep1UI).Show(1);
            var dot = new PopupMainIcon();
            DataDotMgr.Collect(dot);
            AFDots.Click_popup_main_icon();
            Hide();
        }

        protected override void OnShow()
        {
            base.OnShow();

            ui.tfDesc.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_planBottomSubscribe_desc");
            ui.tfTitle.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_planBottomSubscribe_title");
            CheckDiscountAndLifeTime();
            // ui.tfBtnPractice.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_planBottomSubscribe_btn_confirm");
            MsgManager.instance.SendMsg(new CS_SetShowStateReq()
            {
                show_item_type = PB_ShowItemType.CommercialPopup,
            });
            var dot = new SubscribePopup();
            dot.popup_title = ui.tfTitle.text;
            dot.popup_text = ui.tfDesc.text;
            DataDotMgr.Collect(dot);
            AFDots.Appear_subscribe_popup();
        }
        
        private void CheckDiscountAndLifeTime()
        {
            bool isLifeTime = GameEntry.ShopC.ShopModel.IsLifeTime;

            if (!isLifeTime)
            {
                bool isDiscount = GameEntry.ShopC.ShopModel.IsDiscountType;
                
                ShopUIUtil.UpdateDiscountNode(ui.discountNode , GameEntry.ShopC.ShopModel);
  
                if (isDiscount)
                {
                    ui.tfBtnPractice.text = string.Format(I18N.inst.MoStr("ui_shop_discount_01")  , GameEntry.ShopC.ShopModel.GetMaxDiscountStr());
                }
                else
                {
                    ui.tfBtnPractice.text = I18N.inst.MoStr("ui_planBottomSubscribe_btn_confirm");
                }
            }
            else
            {
                ui.discountNode.visible = false;
            }
            CheckLifeTime(isLifeTime);
        }

        /// <summary>
        /// 显示优先级比首次折扣高，so CheckDiscount之后处理覆盖
        /// </summary>
        private void CheckLifeTime(bool isLifeTime)
        {
            ui.lifetimeNode.visible = isLifeTime;
            if (isLifeTime)
            {
                ui.lifetimeTxt.text = I18N.inst.MoStr("ui_shop_lifetime_1");
                ui.tfBtnPractice.text = I18N.inst.MoStr("ui_shop_lifetime_4");
                
                ui.tfDesc.text = I18N.inst.MoStr("ui_shop_lifetime_2") + "\n" + I18N.inst.MoStr("ui_shop_lifetime_3");
            }
            
        }
    }
}
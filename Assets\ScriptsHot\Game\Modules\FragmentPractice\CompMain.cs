using System.Collections;
using ScriptsHot.Game.Modules.FragmentPractice;
using Game.Modules.FragmentPractice;
using UnityEngine;
using FairyGUI;
using System;
using System.Linq;
using UIBind.Main;
using StarX5Event = UIBind.Main.StarX5Background.StarX5Event;

namespace UIBind.FragmentPractice
{
    public partial class CompMain : UIBindT, IQuestionEventListener
    {

        private static FragmentPracticeModel FragModel => ModelManager.instance.GetModel<FragmentPracticeModel>(ModelConsts.FragmentPractice);

        private static FragmentPracticeController FragController =>
            ControllerManager.instance.GetController<FragmentPracticeController>(ModelConsts.FragmentPractice);
        private StarX5Background background;


        public void Init(FragmentPracticeUI panel, Main.StarX5Background background = null)
        {
            this.background = background;

            CompPractice.Init(true);
            CompPracticeNext.Init(false);
            CompHead.Init(panel);
            tfDebug.visible = AppConst.IsDebug;


            // Register for input focus events
            CompPractice.AddEventListener("onInputFocusIn", OnInputFocusIn);
            CompPractice.AddEventListener("onInputFocusOut", OnInputFocusOut);
            
            // CompPractice.onFocusIn.Add(OnInputFocusIn);
            // CompPractice.onFocusOut.Add(OnInputFocusOut);
        }

        public void OnShow()
        {
            reset.Play();

            SetTransitionArgs();

            QuestionEventManager.Instance.ClearAnswer();

            QuestionEventManager.Instance.AddListener(this);
        }

        private void SetTransitionArgs()
        {
            next_1.SetValue("bottomStart", CompBottom.position.x, placeHolder.position.y);
            next_1.SetValue("bottomEnd", CompBottom.position.x, GRoot.inst.height);
            next_2.SetValue("bottomStart", CompBottom.position.x, placeHolder.position.y);
            next_2.SetValue("bottomEnd", CompBottom.position.x, GRoot.inst.height);
        }

        public void OnHide()
        {
            CompBottom.OnHide();
            CompHead.OnHide();
            tfDebug.text = string.Empty;
            QuestionEventManager.Instance.RemoveListener(this);

            DotPracticeManager.Instance.qid = 0;
            DotPracticeManager.Instance.questionIndex = 0;
            
            jumpTip.onGiveUp -= CompHead.Exit;
            jumpTip.onContinue -= OnJumpContinue;
        }

        private void OnInputFocusIn()
        {
// #if !UNITY_EDITOR
            // if (context.inputEvent == null) return;
            CompPractice.y = CompHead.com.y + CompHead.com.height + CompPractice.height + 50;
// #endif
        }

        private void OnInputFocusOut()
        {
// #if !UNITY_EDITOR
            // if (context.inputEvent == null) return;
            CompPractice.y = 1253;
// #endif
        }

        #region refresh
        public void ShowPractices()
        {
            CompBottom.touchable = true;
            CompPracticeNext.visible = true;

            CompHead.InRowState(0);

            feedback.SetContent(FeedbackPopup.State.start, "fragment_feedback_start");

            SetTransitionArgs();

            if (FragModel.JumpMode)
            {
                JumpStart();
            }
            else
            {
                NormalStart();
            }
        }

        private void JumpStart()
        {
            CompBottom.visible = false;
            CompPractice.visible = false;
            jumpTip.visible = true;
            jumpTip.onGiveUp += OnJumpExit;
            jumpTip.onContinue += OnJumpContinue;
        }

        private void OnJumpExit()
        {
            // 回退 JumpStart 的修改
            CompBottom.visible = true;
            CompPractice.visible = true;
            jumpTip.visible = false;
            CompHead.Exit();
        }

        private void OnJumpContinue()
        {
            // 回退 JumpStart 的修改
            CompBottom.visible = true;
            CompPractice.visible = true;
            jumpTip.visible = false;
            NormalStart();
        }

        private void NormalStart()
        {
            start.Play(1, 0, () =>
            {
                PlaySpecialAction(StarX5Event.OnStart, (_) =>
                {
                    ShowNextQuestion();
                    next_2.Play();
                });
            });
        }

        public void CollectData()
        {
            DotPracticeManager.Instance.qid = FragModel.CurQuestion.QuestionId;
            DotPracticeManager.Instance.questionIndex = FragModel.CurQuestionNum + 1;
            DotPracticeManager.Instance.Collect(new DataDot_AppearPage());
        }

        //首次 + 下一题 + 纠错的continue后触发 
        private void ShowNextQuestion()
        {
            CompHead.RefreshProgress();
            CompBottom.OnNewQuestion();//reset的refresh没有题目数据 无法判定状态
            
            VFDebug.LogWarning($"CurQuestionID -- {FragModel.CurQuestion.QuestionId} #qid");
            CompPractice.ShowPractice(FragModel.CurQuestion);
            CompPracticeNext.ShowPractice(FragController.GetNextQuestion());

            // 调整题板的位置，一次性调整不绑定 Relation
            CompPractice.y = placeHolder.y - CompBottom.height - 50; 
            CompPracticeNext.y = placeHolder.y - CompBottom.height - 50; 

            CollectData();
        }

        #endregion

        #region Bottom

        private bool CheckCombo()
        {
            if (FragModel.ComboNum == 5)
            {
                return true;
            }

            if (FragModel.ComboNum == 10)
            {
                return true;
            }

            return false;
        }

        #endregion

        #region IQuestionEventListener
        public void OnAnswered()
        {
            CollectClickAnswer();
        }

        private void CollectClickAnswer()
        {
            DataDotClick_FragmentPractice_answer dot = new DataDotClick_FragmentPractice_answer();
            dot.List_id = FragController.UnitId;
            dot.Question_id = FragModel.CurQuestion.QuestionId;
            dot.Question_index = FragController.GetCurQuestionIndex();
            dot.Question_num = FragController.GetQuestionNum();
            dot.Exercises_type = FragModel.Round == 0 ? "Planned" : "Corrected";
            DataDotMgr.Collect(dot);
        }

        public void OnSubmit()
        {
            if (QuestionEventManager.Instance.IsRightAnswer != true)
                CompHead.UpdateShield();
            if (GSoundManager.instance.IsPlaying("TTS")) TTSManager.instance.StopTTS();
            SoundManger.instance.PlayUI(QuestionEventManager.Instance.IsRightAnswer != true
                ? "question_error"
                : "question_right");

            CompHead.InRowState(FragModel.ComboNum);
            CompHead.RefreshProgress();
            background?.OnPlayerAnswer(QuestionEventManager.Instance.IsRightAnswer, FragModel.ComboNum, FragModel.CorrectNum, FragModel.ComboErrorNum, FragModel.ErrorQuestionCount);
        }

        public void OnRetry() { }

        public void AutoCheck() { }
        public void OnReset()
        {
            if (GSoundManager.instance.IsPlaying("TTS")) TTSManager.instance.StopTTS();

            // QuestionEventManager.Instance.Reset();
            StarX5Event animEvent = StarX5Event.Default;
            if (FragController.ToNextQuestion(OnStrategyLog))
            {
                if (FragModel.Round == 1 && FragModel.CurQuestionIdx == 0)
                {
                    animEvent = StarX5Event.Review;
                    feedback.SetContent(FeedbackPopup.State.mistake, "fragment_feedback_mistakes");
                    CompBottom.EnableContinueBtn();
                    VFDebug.Log($"CompMain OnReset ShowContinue");
                }
                else if (FragModel.ComboNum == 5)
                {
                    VFDebug.Log($"CompMain OnReset ShowInARow5Page");
                    animEvent = StarX5Event.WinStreak5;
                    feedback.SetContent(FeedbackPopup.State.right, "fragment_feedback_in_row", 5);

                    FairyGUI_Timers.inst.StartCoroutine(this.PlayHapticSequence());

                    SoundManger.instance.PlayUI("question_combo");
                    // //20250710 连胜语音补丁
                    // TimerManager.instance.RegisterTimer((c) =>
                    // {
                    //     bool pickRnd = UnityEngine.Random.Range(0.0f, 1.0f) > 0.5f;
                    //     SoundManger.instance.PlayUI( pickRnd? "win5inrow_1":"win5inrow_2");
                    // }, 900, 1);


                }
                else if (FragModel.ComboNum == 10)
                {
                    animEvent = StarX5Event.WinStreak10;
                    feedback.SetContent(FeedbackPopup.State.right, "fragment_feedback_in_row", 10);
                    FairyGUI_Timers.inst.StartCoroutine(this.PlayHapticSequence());

                    SoundManger.instance.PlayUI("question_combo");
                    // //20250710 连胜语音补丁
                    // TimerManager.instance.RegisterTimer((c) =>
                    // {
                    //     bool pickRnd = UnityEngine.Random.Range(0.0f, 1.0f) > 0.5f;
                    //     SoundManger.instance.PlayUI( pickRnd? "win10inrow_1":"win10inrow_2");
                    // }, 900, 1);
                    // VFDebug.Log($"CompMain OnReset ShowInARow10 Page");
                }
                else if (FragModel.ComboErrorNum == 3)
                {
                    animEvent = StarX5Event.MultiWrong;
                    feedback.SetContent(FeedbackPopup.State.error, "fragment_feedback_error");
                    VFDebug.Log($"CompMain OnReset ShowFalse3Page");
                }
                else
                {
                    if (FragModel.Round > 0 && FragModel.CurQuestionIdx == FragModel.CurQuestions.Count)
                    {
                        CompPracticeNext.ShowPractice(FragController.GetNextQuestion());
                    }
                }

                if (animEvent != StarX5Event.Default)
                {
                    next_1.Play(1, 0, () =>
                    {
                        PlaySpecialAction(animEvent, (_) =>
                        {
                            ShowNextQuestion();
                            next_2.Play();
                        });
                    });
                }
                else
                {
                    next.Play(1, 0, () =>
                    {
                        ShowNextQuestion();
                        reset.Play();
                    });
                }
            }
            else
            {                
                feedback.SetContent(FeedbackPopup.State.exit, "fragment_feedback_exit");
                next_1.Play(1, 0, () =>
                {
                    background?.TriggerEvent(StarX5Background.StarX5Event.OnExit, (_) =>
                    {
                        FragController.ReqSettlement();
                        CompBottom.touchable = false;
                    });
                });
            }
        }

        private void OnStrategyLog(string content)
        {
            var logs = tfDebug.text.Split("\n").ToArray();
            if (logs.Length > 10)
            {
                var newLogs = new string[10];
                Array.Copy(logs, 1, newLogs, 0, 10);
                tfDebug.text = string.Join("\n", newLogs);
            }
            tfDebug.text += "\n" + content;
        }

        private void PlaySpecialAction(StarX5Event evtType, Action<AnimationSystem.StarX5PlayAnimationState.AnimCallBack> onComplete)
        {
            background?.TriggerEvent(evtType, onComplete);
        }


        //question_combo 定制震动
        private IEnumerator PlayHapticSequence()
        {
            VFDebug.Log($"InARow5 haptic");
            float timeStep = 0f;
            float maxTime = 2f;
            VibrationManager.Ins.Vibrate(0.2f, 0.3f, 1.5f);
            yield return null;
            timeStep += Time.deltaTime;

            while (timeStep < maxTime)
            {
                if (timeStep < 0.6f)
                {
                    VibrationManager.Ins.TransientHaptic(
                        intensity: 0.2f + (0.6f * (timeStep / 0.6f)), // 动态强度
                        sharpness: 0.3f + (0.1f * (timeStep / 0.6f))
                    );
                }
                else if (timeStep < 0.6f + 0.4f)
                {
                    VibrationManager.Ins.TransientHaptic(
                        intensity: 0.6f + (-0.2f * ((timeStep - 0.6f) / 0.4f)), // 动态强度
                        sharpness: 0.3f // 峰值处锐度最高
                    );
                }
                else if (timeStep < 0.6f + 0.4f + 0.5f)
                {
                    VibrationManager.Ins.TransientHaptic(
                        intensity: 0.4f + (-0.3f * (timeStep - 1f) / 0.5f),
                        sharpness: 0.2f
                    );
                }

                yield return null;
                timeStep += Time.deltaTime;
            }

        }
        public void OnJumpListenTask()
        {
            FragModel.SetIsJumpListen(true);
            FragController.RemoveAllListenQuestion(FragModel.CurQuestions, FragModel.CurQuestionIdx + 1);
            FragController.RemoveAllListenQuestion(FragModel.NextQuestions, 0);
            UpdatePractice();
        }

        public void OnJumpSpeakTask()
        {
            FragModel.SetIsJumpSpeak(true);
            FragController.RemoveAllSpeakQuestion(FragModel.CurQuestions, FragModel.CurQuestionIdx + 1);
            FragController.RemoveAllSpeakQuestion(FragModel.NextQuestions, 0);
            UpdatePractice();
        }

        private void UpdatePractice()
        {
            // //更新题数据
            // if (FragModel.Round == 0)
            // {
            //     FragModel.CurQuestions.Clear();
            //     FragModel.CurQuestions.AddRange(FragModel.QuestionGrp.CurQuestions);
            // }
            
            //把这道题pass了
            FragController.DoAnswerCorrect(true);
            if (GSoundManager.instance.IsPlaying("TTS")) TTSManager.instance.StopTTS();
            if (!CheckCombo())
                SoundManger.instance.PlayUI(QuestionEventManager.Instance.IsRightAnswer != true
                    ? "question_error"
                    : "question_right");
            CompHead.RefreshProgress();

            //换后面的题
            CompPracticeNext.ShowPractice(FragController.GetNextQuestion());
            TimerManager.instance.RegisterTimer(
                (c) => { CompPracticeNext.displayObject.cacheAsBitmap = true; }, 500);
        }

        #endregion
    }

}
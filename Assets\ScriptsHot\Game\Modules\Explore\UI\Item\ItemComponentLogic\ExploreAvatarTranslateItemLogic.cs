﻿using ScriptsHot.Game.Modules.Explore;

namespace UIBind.Explore.Item.ItemComponentLogic
{
    /// <summary>
    /// avatar  翻译
    /// </summary>
    public class ExploreAvatarTranslateItemLogic:ItemComponentLogicBase
    {
        public ExploreAvatarItem Com;
        public ExploreAvatarItemLogic AvatarCom;
        private long _dialogId;
        private long _round;

        public override void Init()
        {
            base.Init();
            this.Com.comTranslate.com.visible = false;
            this.Com.comTranslate.sp.visible = false;
            this.Com.comTranslate.txtName.SetKey("ui_explore_title_translation");
        }
        
        public void SetInfo(long dialogId,long round)
        {
            _dialogId = dialogId;
            _round = round;
        }

        public void Reset()
        {
            Com.comTranslate.com.alpha = 1;
            Com.comTranslate.com.visible = false;
        }

        public void ShowTranslateTxt(string str)
        {
            Com.comTranslate.txtTran.text = str;
        }
        
        public void AlphaToZore()
        {
            Com.comTranslate.com.TweenFade(0, ExploreConst.CellAphla).OnComplete(() =>
            {
                Com.comTranslate.com.visible = false;
                Com.com.visible = false;
                AvatarCom.AlphaToOne();
            });
        }
        
        public void AlphaToOne()
        {
            Com.com.visible = true;
            Com.comTranslate.com.visible = true;
            Com.comTranslate.com.TweenFade(1, ExploreConst.CellAphla);
        }
        
        public void ToZore()
        {
            Com.comTranslate.com.alpha = 1;
            Com.comTranslate.com.visible = false;
        }
    }
}
// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/user_chat.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/user_chat.proto</summary>
  public static partial class UserChatReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/user_chat.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static UserChatReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiBwcm90b2J1Zi9leHBsb3JlL3VzZXJfY2hhdC5wcm90bxobcHJvdG9idWYv",
            "ZXhwbG9yZS9iYXNlLnByb3RvGhtwcm90b2J1Zi9iYXNpYy9kaWFsb2cucHJv",
            "dG8iwgEKEFBCX1VzZXJDaGF0VXBNc2cSOgoOdXNlcklucHV0QXVkaW8YASAB",
            "KAsyIC5QQl9FeHBsb3JlX1VzZXJDaGF0QXVkaW9VcEZyYW1lSAASNAoKdXBC",
            "aXpFdmVudBgCIAEoDjIeLlBCX0V4cGxvcmVfVXNlckNoYXRVcEJpekV2ZW50",
            "SAASMAoMdXNlckNoYXRFeGl0GAMgASgLMhguUEJfRXhwbG9yZV9Vc2VyQ2hh",
            "dEV4aXRIAEIKCgh1cEJpek1zZyJbCh9QQl9FeHBsb3JlX1VzZXJDaGF0QXVk",
            "aW9VcEZyYW1lEg0KBWF1ZGlvGAEgASgMEhMKC3NhbXBsZV9yYXRlGAIgASgN",
            "EhQKDG51bV9jaGFubmVscxgDIAEoDSJEChdQQl9FeHBsb3JlX1VzZXJDaGF0",
            "RXhpdBISCgp1c2VyQ2hhdElkGAEgASgDEhUKDXBhcnRuZXJVc2VySWQYAiAB",
            "KAMikgEKIVBCX0V4cGxvcmVfVXNlckNoYXRBdWRpb0Rvd25GcmFtZRIKCgJp",
            "ZBgBIAEoBBINCgVhdWRpbxgCIAEoDBITCgtzYW1wbGVfcmF0ZRgDIAEoDRIU",
            "CgxudW1fY2hhbm5lbHMYBCABKA0SEQoJYnViYmxlX2lkGAUgASgJEhQKDGlz",
            "X2xhc3RfY2xpcBgGIAEoCCJ7CiZQQl9Vc2VyQ2hhdENvbW1vbkRhdGFGaWVs",
            "ZEZvckRhdGFDbGFzcxISCgp1c2VyQ2hhdElkGAEgASgDEg0KBW1zZ0lkGAIg",
            "ASgDEg0KBXJvdW5kGAMgASgFEh8KCG1zZ093bmVyGAQgASgOMg0uUEJfTXNn",
            "QmVsb25nIpQBCiRTQ19Vc2VyQ2hhdERvd25Nc2dGb3JVc2VyUmVjb2duaXpp",
            "bmcSIQoEY29kZRgBIAEoDjITLlBCX0V4cGxvcmVfQml6Q29kZRI7Cgpjb21t",
            "b25EYXRhGAIgASgLMicuUEJfVXNlckNoYXRDb21tb25EYXRhRmllbGRGb3JE",
            "YXRhQ2xhc3MSDAoEdGV4dBgDIAEoCSKTAQojU0NfVXNlckNoYXREb3duTXNn",
            "Rm9yVXNlclJlY29nbml6ZWQSIQoEY29kZRgBIAEoDjITLlBCX0V4cGxvcmVf",
            "Qml6Q29kZRI7Cgpjb21tb25EYXRhGAIgASgLMicuUEJfVXNlckNoYXRDb21t",
            "b25EYXRhRmllbGRGb3JEYXRhQ2xhc3MSDAoEdGV4dBgDIAEoCSKZAQopU0Nf",
            "VXNlckNoYXREb3duTXNnRm9yT3RoZXJVc2VyUmVjb2duaXppbmcSIQoEY29k",
            "ZRgBIAEoDjITLlBCX0V4cGxvcmVfQml6Q29kZRI7Cgpjb21tb25EYXRhGAIg",
            "ASgLMicuUEJfVXNlckNoYXRDb21tb25EYXRhRmllbGRGb3JEYXRhQ2xhc3MS",
            "DAoEdGV4dBgDIAEoCSKYAQooU0NfVXNlckNoYXREb3duTXNnRm9yT3RoZXJV",
            "c2VyUmVjb2duaXplZBIhCgRjb2RlGAEgASgOMhMuUEJfRXhwbG9yZV9CaXpD",
            "b2RlEjsKCmNvbW1vbkRhdGEYAiABKAsyJy5QQl9Vc2VyQ2hhdENvbW1vbkRh",
            "dGFGaWVsZEZvckRhdGFDbGFzcxIMCgR0ZXh0GAMgASgJIqoBCixTQ19Vc2Vy",
            "Q2hhdERvd25Nc2dGb3JPdGhlclVzZXJSZXBseVRyYW5zbGF0ZRIhCgRjb2Rl",
            "GAEgASgOMhMuUEJfRXhwbG9yZV9CaXpDb2RlEjsKCmNvbW1vbkRhdGEYAiAB",
            "KAsyJy5QQl9Vc2VyQ2hhdENvbW1vbkRhdGFGaWVsZEZvckRhdGFDbGFzcxIa",
            "ChJyZXBseVRyYW5zbGF0ZVRleHQYAyABKAkinAEKJVNDX1VzZXJDaGF0RG93",
            "bk1zZ0ZvclVzZXJSZXBseUV4YW1wbGUSIQoEY29kZRgBIAEoDjITLlBCX0V4",
            "cGxvcmVfQml6Q29kZRI7Cgpjb21tb25EYXRhGAIgASgLMicuUEJfVXNlckNo",
            "YXRDb21tb25EYXRhRmllbGRGb3JEYXRhQ2xhc3MSEwoLZXhhbXBsZVRleHQY",
            "AyABKAkirgEKLlNDX1VzZXJDaGF0RG93bk1zZ0ZvclVzZXJSZXBseUV4YW1w",
            "bGVUcmFuc2xhdGUSIQoEY29kZRgBIAEoDjITLlBCX0V4cGxvcmVfQml6Q29k",
            "ZRI7Cgpjb21tb25EYXRhGAIgASgLMicuUEJfVXNlckNoYXRDb21tb25EYXRh",
            "RmllbGRGb3JEYXRhQ2xhc3MSHAoUZXhhbXBsZVRyYW5zbGF0ZVRleHQYAyAB",
            "KAkisQEKHVNDX1VzZXJDaGF0RG93bk1zZ0ZvckJpekV2ZW50EiEKBGNvZGUY",
            "ASABKA4yEy5QQl9FeHBsb3JlX0JpekNvZGUSOwoKY29tbW9uRGF0YRgCIAEo",
            "CzInLlBCX1VzZXJDaGF0Q29tbW9uRGF0YUZpZWxkRm9yRGF0YUNsYXNzEjAK",
            "CGJpekV2ZW50GAMgASgOMh4uUEJfRXhwbG9yZV9Vc2VyQ2hhdFVwQml6RXZl",
            "bnQiYgogU0NfVXNlckNoYXREb3duTXNnVXNlckNoYXRTdGF0dXMSEgoKdXNl",
            "ckNoYXRJZBgBIAEoBBIqCgZzdGF0dXMYAiABKA4yGi5QQl9FeHBsb3JlX1Vz",
            "ZXJDaGF0U3RhdHVzIj4KGVNDX1VzZXJDaGF0RG93bk1zZ0ZvckV4aXQSIQoE",
            "Y29kZRgBIAEoDjITLlBCX0V4cGxvcmVfQml6Q29kZSrUAQodUEJfRXhwbG9y",
            "ZV9Vc2VyQ2hhdFVwQml6RXZlbnQSIAocRU9fVUNfVU5LTk9XTl9CSVpfRVZF",
            "TlRfVFlQRRAAEhsKF0VPX1VDX1VTRVJfTUFOVUFMX1NUQVJUEAESHAoYRU9f",
            "VUNfVVNFUl9NQU5VQUxfU1VCTUlUEAISHAoYRU9fVUNfVVNFUl9TV0lUQ0hf",
            "TUFOVUFMEAMSGgoWRU9fVUNfVVNFUl9TV0lUQ0hfQVVUTxAEEhwKGEVPX1VD",
            "X1VTRVJfQ0FOQ0VMX1VQTE9BRBAFKpADCiJQQl9FeHBsb3JlX1VzZXJDaGF0",
            "RG93bkJpekRhdGFUeXBlEh8KG0VPX1VDX1VOS05PV05fQklaX0RBVEFfVFlQ",
            "RRAAEhMKD0VPX1VDX0JJWl9FVkVOVBABEikKJUVPX1VDX1VTRVJfU1BFRUNI",
            "X1RPX1RFWFRfUkVDT0dOSVpJTkcQAhIoCiRFT19VQ19VU0VSX1NQRUVDSF9U",
            "T19URVhUX1JFQ09HTklaRUQQAxIvCitFT19VQ19PVEhFUl9VU0VSX1NQRUVD",
            "SF9UT19URVhUX1JFQ09HTklaSU5HEAQSLgoqRU9fVUNfT1RIRVJfVVNFUl9T",
            "UEVFQ0hfVE9fVEVYVF9SRUNPR05JWkVEEAUSJAogRU9fVUNfT1RIRVJfVVNF",
            "Ul9SRVBMWV9UUkFOU0xBVEUQBhIcChhFT19VQ19VU0VSX1JFUExZX0VYQU1Q",
            "TEUQBxImCiJFT19VQ19VU0VSX1JFUExZX0VYQU1QTEVfVFJBTlNMQVRFEAgS",
            "EgoORU9fVUNfTExNX1RURkIQCSp7ChlQQl9FeHBsb3JlX1VzZXJDaGF0U3Rh",
            "dHVzEhgKFEVPX1VDX1NUQVRVU19VTktOT1dOEAASFgoSRU9fVUNfU1RBVFVT",
            "X1NUQVJUEAESFgoSRU9fVUNfU1RBVFVTX1JFQURZEAISFAoQRU9fVUNfU1RB",
            "VFVTX0VORBADQipaGnZmX3Byb3RvYnVmL3NlcnZlci9leHBsb3JlqgILTXNn",
            "LmV4cGxvcmViBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.explore.BaseReflection.Descriptor, global::Msg.basic.DialogReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.explore.PB_Explore_UserChatUpBizEvent), typeof(global::Msg.explore.PB_Explore_UserChatDownBizDataType), typeof(global::Msg.explore.PB_Explore_UserChatStatus), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_UserChatUpMsg), global::Msg.explore.PB_UserChatUpMsg.Parser, new[]{ "userInputAudio", "upBizEvent", "userChatExit" }, new[]{ "upBizMsg" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Explore_UserChatAudioUpFrame), global::Msg.explore.PB_Explore_UserChatAudioUpFrame.Parser, new[]{ "audio", "sample_rate", "num_channels" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Explore_UserChatExit), global::Msg.explore.PB_Explore_UserChatExit.Parser, new[]{ "userChatId", "partnerUserId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Explore_UserChatAudioDownFrame), global::Msg.explore.PB_Explore_UserChatAudioDownFrame.Parser, new[]{ "id", "audio", "sample_rate", "num_channels", "bubble_id", "is_last_clip" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_UserChatCommonDataFieldForDataClass), global::Msg.explore.PB_UserChatCommonDataFieldForDataClass.Parser, new[]{ "userChatId", "msgId", "round", "msgOwner" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_UserChatDownMsgForUserRecognizing), global::Msg.explore.SC_UserChatDownMsgForUserRecognizing.Parser, new[]{ "code", "commonData", "text" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_UserChatDownMsgForUserRecognized), global::Msg.explore.SC_UserChatDownMsgForUserRecognized.Parser, new[]{ "code", "commonData", "text" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_UserChatDownMsgForOtherUserRecognizing), global::Msg.explore.SC_UserChatDownMsgForOtherUserRecognizing.Parser, new[]{ "code", "commonData", "text" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_UserChatDownMsgForOtherUserRecognized), global::Msg.explore.SC_UserChatDownMsgForOtherUserRecognized.Parser, new[]{ "code", "commonData", "text" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_UserChatDownMsgForOtherUserReplyTranslate), global::Msg.explore.SC_UserChatDownMsgForOtherUserReplyTranslate.Parser, new[]{ "code", "commonData", "replyTranslateText" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_UserChatDownMsgForUserReplyExample), global::Msg.explore.SC_UserChatDownMsgForUserReplyExample.Parser, new[]{ "code", "commonData", "exampleText" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_UserChatDownMsgForUserReplyExampleTranslate), global::Msg.explore.SC_UserChatDownMsgForUserReplyExampleTranslate.Parser, new[]{ "code", "commonData", "exampleTranslateText" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_UserChatDownMsgForBizEvent), global::Msg.explore.SC_UserChatDownMsgForBizEvent.Parser, new[]{ "code", "commonData", "bizEvent" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_UserChatDownMsgUserChatStatus), global::Msg.explore.SC_UserChatDownMsgUserChatStatus.Parser, new[]{ "userChatId", "status" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_UserChatDownMsgForExit), global::Msg.explore.SC_UserChatDownMsgForExit.Parser, new[]{ "code" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  ///
  /// 用户聊天上行 - 业务事件枚举
  /// </summary>
  public enum PB_Explore_UserChatUpBizEvent {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("EO_UC_UNKNOWN_BIZ_EVENT_TYPE")] EO_UC_UNKNOWN_BIZ_EVENT_TYPE = 0,
    /// <summary>
    /// 用户手动开始
    /// </summary>
    [pbr::OriginalName("EO_UC_USER_MANUAL_START")] EO_UC_USER_MANUAL_START = 1,
    /// <summary>
    /// 用户手动提交
    /// </summary>
    [pbr::OriginalName("EO_UC_USER_MANUAL_SUBMIT")] EO_UC_USER_MANUAL_SUBMIT = 2,
    /// <summary>
    /// 切换手动模式
    /// </summary>
    [pbr::OriginalName("EO_UC_USER_SWITCH_MANUAL")] EO_UC_USER_SWITCH_MANUAL = 3,
    /// <summary>
    /// 切换自动模式
    /// </summary>
    [pbr::OriginalName("EO_UC_USER_SWITCH_AUTO")] EO_UC_USER_SWITCH_AUTO = 4,
    /// <summary>
    /// 用户取消收音
    /// </summary>
    [pbr::OriginalName("EO_UC_USER_CANCEL_UPLOAD")] EO_UC_USER_CANCEL_UPLOAD = 5,
  }

  /// <summary>
  ///
  /// 用户聊天下行 - 业务数据类型枚举
  /// </summary>
  public enum PB_Explore_UserChatDownBizDataType {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("EO_UC_UNKNOWN_BIZ_DATA_TYPE")] EO_UC_UNKNOWN_BIZ_DATA_TYPE = 0,
    /// <summary>
    /// 业务事件
    /// </summary>
    [pbr::OriginalName("EO_UC_BIZ_EVENT")] EO_UC_BIZ_EVENT = 1,
    /// <summary>
    /// 用户语音识别过程结果
    /// </summary>
    [pbr::OriginalName("EO_UC_USER_SPEECH_TO_TEXT_RECOGNIZING")] EO_UC_USER_SPEECH_TO_TEXT_RECOGNIZING = 2,
    /// <summary>
    /// 用户语音识别最终结果
    /// </summary>
    [pbr::OriginalName("EO_UC_USER_SPEECH_TO_TEXT_RECOGNIZED")] EO_UC_USER_SPEECH_TO_TEXT_RECOGNIZED = 3,
    /// <summary>
    /// 他人语音识别过程结果
    /// </summary>
    [pbr::OriginalName("EO_UC_OTHER_USER_SPEECH_TO_TEXT_RECOGNIZING")] EO_UC_OTHER_USER_SPEECH_TO_TEXT_RECOGNIZING = 4,
    /// <summary>
    /// 他人语音识别最终结果
    /// </summary>
    [pbr::OriginalName("EO_UC_OTHER_USER_SPEECH_TO_TEXT_RECOGNIZED")] EO_UC_OTHER_USER_SPEECH_TO_TEXT_RECOGNIZED = 5,
    /// <summary>
    /// 他人回复文本翻译
    /// </summary>
    [pbr::OriginalName("EO_UC_OTHER_USER_REPLY_TRANSLATE")] EO_UC_OTHER_USER_REPLY_TRANSLATE = 6,
    /// <summary>
    /// 用户回复示例
    /// </summary>
    [pbr::OriginalName("EO_UC_USER_REPLY_EXAMPLE")] EO_UC_USER_REPLY_EXAMPLE = 7,
    /// <summary>
    /// 用户回复示例翻译
    /// </summary>
    [pbr::OriginalName("EO_UC_USER_REPLY_EXAMPLE_TRANSLATE")] EO_UC_USER_REPLY_EXAMPLE_TRANSLATE = 8,
    /// <summary>
    /// LLM首包时延（TTFB 代表 "Time to First Byte"（首字节时间））
    /// </summary>
    [pbr::OriginalName("EO_UC_LLM_TTFB")] EO_UC_LLM_TTFB = 9,
  }

  /// <summary>
  ///
  /// 用户聊天状态枚举
  /// </summary>
  public enum PB_Explore_UserChatStatus {
    [pbr::OriginalName("EO_UC_STATUS_UNKNOWN")] EO_UC_STATUS_UNKNOWN = 0,
    [pbr::OriginalName("EO_UC_STATUS_START")] EO_UC_STATUS_START = 1,
    [pbr::OriginalName("EO_UC_STATUS_READY")] EO_UC_STATUS_READY = 2,
    [pbr::OriginalName("EO_UC_STATUS_END")] EO_UC_STATUS_END = 3,
  }

  #endregion

  #region Messages
  /// <summary>
  ///
  /// 用户聊天功能上行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserChatUpMsg : pb::IMessage<PB_UserChatUpMsg>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserChatUpMsg> _parser = new pb::MessageParser<PB_UserChatUpMsg>(() => new PB_UserChatUpMsg());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserChatUpMsg> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserChatUpMsg() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserChatUpMsg(PB_UserChatUpMsg other) : this() {
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.userInputAudio:
          userInputAudio = other.userInputAudio.Clone();
          break;
        case upBizMsgOneofCase.upBizEvent:
          upBizEvent = other.upBizEvent;
          break;
        case upBizMsgOneofCase.userChatExit:
          userChatExit = other.userChatExit.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserChatUpMsg Clone() {
      return new PB_UserChatUpMsg(this);
    }

    /// <summary>Field number for the "userInputAudio" field.</summary>
    public const int userInputAudioFieldNumber = 1;
    /// <summary>
    /// 用户输入音频
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_UserChatAudioUpFrame userInputAudio {
      get { return upBizMsgCase_ == upBizMsgOneofCase.userInputAudio ? (global::Msg.explore.PB_Explore_UserChatAudioUpFrame) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.userInputAudio;
      }
    }

    /// <summary>Field number for the "upBizEvent" field.</summary>
    public const int upBizEventFieldNumber = 2;
    /// <summary>
    /// 上行业务事件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_UserChatUpBizEvent upBizEvent {
      get { return HasupBizEvent ? (global::Msg.explore.PB_Explore_UserChatUpBizEvent) upBizMsg_ : global::Msg.explore.PB_Explore_UserChatUpBizEvent.EO_UC_UNKNOWN_BIZ_EVENT_TYPE; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = upBizMsgOneofCase.upBizEvent;
      }
    }
    /// <summary>Gets whether the "upBizEvent" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasupBizEvent {
      get { return upBizMsgCase_ == upBizMsgOneofCase.upBizEvent; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "upBizEvent" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearupBizEvent() {
      if (HasupBizEvent) {
        ClearupBizMsg();
      }
    }

    /// <summary>Field number for the "userChatExit" field.</summary>
    public const int userChatExitFieldNumber = 3;
    /// <summary>
    /// 退出聊天
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_UserChatExit userChatExit {
      get { return upBizMsgCase_ == upBizMsgOneofCase.userChatExit ? (global::Msg.explore.PB_Explore_UserChatExit) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.userChatExit;
      }
    }

    private object upBizMsg_;
    /// <summary>Enum of possible cases for the "upBizMsg" oneof.</summary>
    public enum upBizMsgOneofCase {
      None = 0,
      userInputAudio = 1,
      upBizEvent = 2,
      userChatExit = 3,
    }
    private upBizMsgOneofCase upBizMsgCase_ = upBizMsgOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public upBizMsgOneofCase upBizMsgCase {
      get { return upBizMsgCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearupBizMsg() {
      upBizMsgCase_ = upBizMsgOneofCase.None;
      upBizMsg_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserChatUpMsg);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserChatUpMsg other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(userInputAudio, other.userInputAudio)) return false;
      if (upBizEvent != other.upBizEvent) return false;
      if (!object.Equals(userChatExit, other.userChatExit)) return false;
      if (upBizMsgCase != other.upBizMsgCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) hash ^= userInputAudio.GetHashCode();
      if (HasupBizEvent) hash ^= upBizEvent.GetHashCode();
      if (upBizMsgCase_ == upBizMsgOneofCase.userChatExit) hash ^= userChatExit.GetHashCode();
      hash ^= (int) upBizMsgCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
        output.WriteRawTag(10);
        output.WriteMessage(userInputAudio);
      }
      if (HasupBizEvent) {
        output.WriteRawTag(16);
        output.WriteEnum((int) upBizEvent);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.userChatExit) {
        output.WriteRawTag(26);
        output.WriteMessage(userChatExit);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
        output.WriteRawTag(10);
        output.WriteMessage(userInputAudio);
      }
      if (HasupBizEvent) {
        output.WriteRawTag(16);
        output.WriteEnum((int) upBizEvent);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.userChatExit) {
        output.WriteRawTag(26);
        output.WriteMessage(userChatExit);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(userInputAudio);
      }
      if (HasupBizEvent) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) upBizEvent);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.userChatExit) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(userChatExit);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserChatUpMsg other) {
      if (other == null) {
        return;
      }
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.userInputAudio:
          if (userInputAudio == null) {
            userInputAudio = new global::Msg.explore.PB_Explore_UserChatAudioUpFrame();
          }
          userInputAudio.MergeFrom(other.userInputAudio);
          break;
        case upBizMsgOneofCase.upBizEvent:
          upBizEvent = other.upBizEvent;
          break;
        case upBizMsgOneofCase.userChatExit:
          if (userChatExit == null) {
            userChatExit = new global::Msg.explore.PB_Explore_UserChatExit();
          }
          userChatExit.MergeFrom(other.userChatExit);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::Msg.explore.PB_Explore_UserChatAudioUpFrame subBuilder = new global::Msg.explore.PB_Explore_UserChatAudioUpFrame();
            if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
              subBuilder.MergeFrom(userInputAudio);
            }
            input.ReadMessage(subBuilder);
            userInputAudio = subBuilder;
            break;
          }
          case 16: {
            upBizMsg_ = input.ReadEnum();
            upBizMsgCase_ = upBizMsgOneofCase.upBizEvent;
            break;
          }
          case 26: {
            global::Msg.explore.PB_Explore_UserChatExit subBuilder = new global::Msg.explore.PB_Explore_UserChatExit();
            if (upBizMsgCase_ == upBizMsgOneofCase.userChatExit) {
              subBuilder.MergeFrom(userChatExit);
            }
            input.ReadMessage(subBuilder);
            userChatExit = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            global::Msg.explore.PB_Explore_UserChatAudioUpFrame subBuilder = new global::Msg.explore.PB_Explore_UserChatAudioUpFrame();
            if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
              subBuilder.MergeFrom(userInputAudio);
            }
            input.ReadMessage(subBuilder);
            userInputAudio = subBuilder;
            break;
          }
          case 16: {
            upBizMsg_ = input.ReadEnum();
            upBizMsgCase_ = upBizMsgOneofCase.upBizEvent;
            break;
          }
          case 26: {
            global::Msg.explore.PB_Explore_UserChatExit subBuilder = new global::Msg.explore.PB_Explore_UserChatExit();
            if (upBizMsgCase_ == upBizMsgOneofCase.userChatExit) {
              subBuilder.MergeFrom(userChatExit);
            }
            input.ReadMessage(subBuilder);
            userChatExit = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天上行 - 音频框架（用户录音）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Explore_UserChatAudioUpFrame : pb::IMessage<PB_Explore_UserChatAudioUpFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Explore_UserChatAudioUpFrame> _parser = new pb::MessageParser<PB_Explore_UserChatAudioUpFrame>(() => new PB_Explore_UserChatAudioUpFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Explore_UserChatAudioUpFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_UserChatAudioUpFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_UserChatAudioUpFrame(PB_Explore_UserChatAudioUpFrame other) : this() {
      audio_ = other.audio_;
      sample_rate_ = other.sample_rate_;
      num_channels_ = other.num_channels_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_UserChatAudioUpFrame Clone() {
      return new PB_Explore_UserChatAudioUpFrame(this);
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 1;
    private pb::ByteString audio_ = pb::ByteString.Empty;
    /// <summary>
    /// 音频数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString audio {
      get { return audio_; }
      set {
        audio_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sample_rate" field.</summary>
    public const int sample_rateFieldNumber = 2;
    private uint sample_rate_;
    /// <summary>
    /// 采样率
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint sample_rate {
      get { return sample_rate_; }
      set {
        sample_rate_ = value;
      }
    }

    /// <summary>Field number for the "num_channels" field.</summary>
    public const int num_channelsFieldNumber = 3;
    private uint num_channels_;
    /// <summary>
    /// 声道数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint num_channels {
      get { return num_channels_; }
      set {
        num_channels_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Explore_UserChatAudioUpFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Explore_UserChatAudioUpFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (audio != other.audio) return false;
      if (sample_rate != other.sample_rate) return false;
      if (num_channels != other.num_channels) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (audio.Length != 0) hash ^= audio.GetHashCode();
      if (sample_rate != 0) hash ^= sample_rate.GetHashCode();
      if (num_channels != 0) hash ^= num_channels.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (audio.Length != 0) {
        output.WriteRawTag(10);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(num_channels);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (audio.Length != 0) {
        output.WriteRawTag(10);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(num_channels);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (audio.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(audio);
      }
      if (sample_rate != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(sample_rate);
      }
      if (num_channels != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(num_channels);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Explore_UserChatAudioUpFrame other) {
      if (other == null) {
        return;
      }
      if (other.audio.Length != 0) {
        audio = other.audio;
      }
      if (other.sample_rate != 0) {
        sample_rate = other.sample_rate;
      }
      if (other.num_channels != 0) {
        num_channels = other.num_channels;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            audio = input.ReadBytes();
            break;
          }
          case 16: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 24: {
            num_channels = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            audio = input.ReadBytes();
            break;
          }
          case 16: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 24: {
            num_channels = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天上行 - 退出聊天
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Explore_UserChatExit : pb::IMessage<PB_Explore_UserChatExit>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Explore_UserChatExit> _parser = new pb::MessageParser<PB_Explore_UserChatExit>(() => new PB_Explore_UserChatExit());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Explore_UserChatExit> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_UserChatExit() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_UserChatExit(PB_Explore_UserChatExit other) : this() {
      userChatId_ = other.userChatId_;
      partnerUserId_ = other.partnerUserId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_UserChatExit Clone() {
      return new PB_Explore_UserChatExit(this);
    }

    /// <summary>Field number for the "userChatId" field.</summary>
    public const int userChatIdFieldNumber = 1;
    private long userChatId_;
    /// <summary>
    /// 用户聊天id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long userChatId {
      get { return userChatId_; }
      set {
        userChatId_ = value;
      }
    }

    /// <summary>Field number for the "partnerUserId" field.</summary>
    public const int partnerUserIdFieldNumber = 2;
    private long partnerUserId_;
    /// <summary>
    /// 聊伴用户id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long partnerUserId {
      get { return partnerUserId_; }
      set {
        partnerUserId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Explore_UserChatExit);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Explore_UserChatExit other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (userChatId != other.userChatId) return false;
      if (partnerUserId != other.partnerUserId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (userChatId != 0L) hash ^= userChatId.GetHashCode();
      if (partnerUserId != 0L) hash ^= partnerUserId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (userChatId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(userChatId);
      }
      if (partnerUserId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(partnerUserId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (userChatId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(userChatId);
      }
      if (partnerUserId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(partnerUserId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (userChatId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(userChatId);
      }
      if (partnerUserId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(partnerUserId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Explore_UserChatExit other) {
      if (other == null) {
        return;
      }
      if (other.userChatId != 0L) {
        userChatId = other.userChatId;
      }
      if (other.partnerUserId != 0L) {
        partnerUserId = other.partnerUserId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            userChatId = input.ReadInt64();
            break;
          }
          case 16: {
            partnerUserId = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            userChatId = input.ReadInt64();
            break;
          }
          case 16: {
            partnerUserId = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天音频下行框架（TTS）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Explore_UserChatAudioDownFrame : pb::IMessage<PB_Explore_UserChatAudioDownFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Explore_UserChatAudioDownFrame> _parser = new pb::MessageParser<PB_Explore_UserChatAudioDownFrame>(() => new PB_Explore_UserChatAudioDownFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Explore_UserChatAudioDownFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_UserChatAudioDownFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_UserChatAudioDownFrame(PB_Explore_UserChatAudioDownFrame other) : this() {
      id_ = other.id_;
      audio_ = other.audio_;
      sample_rate_ = other.sample_rate_;
      num_channels_ = other.num_channels_;
      bubble_id_ = other.bubble_id_;
      is_last_clip_ = other.is_last_clip_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_UserChatAudioDownFrame Clone() {
      return new PB_Explore_UserChatAudioDownFrame(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int idFieldNumber = 1;
    private ulong id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 2;
    private pb::ByteString audio_ = pb::ByteString.Empty;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString audio {
      get { return audio_; }
      set {
        audio_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sample_rate" field.</summary>
    public const int sample_rateFieldNumber = 3;
    private uint sample_rate_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint sample_rate {
      get { return sample_rate_; }
      set {
        sample_rate_ = value;
      }
    }

    /// <summary>Field number for the "num_channels" field.</summary>
    public const int num_channelsFieldNumber = 4;
    private uint num_channels_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint num_channels {
      get { return num_channels_; }
      set {
        num_channels_ = value;
      }
    }

    /// <summary>Field number for the "bubble_id" field.</summary>
    public const int bubble_idFieldNumber = 5;
    private string bubble_id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bubble_id {
      get { return bubble_id_; }
      set {
        bubble_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is_last_clip" field.</summary>
    public const int is_last_clipFieldNumber = 6;
    private bool is_last_clip_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_last_clip {
      get { return is_last_clip_; }
      set {
        is_last_clip_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Explore_UserChatAudioDownFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Explore_UserChatAudioDownFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (id != other.id) return false;
      if (audio != other.audio) return false;
      if (sample_rate != other.sample_rate) return false;
      if (num_channels != other.num_channels) return false;
      if (bubble_id != other.bubble_id) return false;
      if (is_last_clip != other.is_last_clip) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (id != 0UL) hash ^= id.GetHashCode();
      if (audio.Length != 0) hash ^= audio.GetHashCode();
      if (sample_rate != 0) hash ^= sample_rate.GetHashCode();
      if (num_channels != 0) hash ^= num_channels.GetHashCode();
      if (bubble_id.Length != 0) hash ^= bubble_id.GetHashCode();
      if (is_last_clip != false) hash ^= is_last_clip.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(num_channels);
      }
      if (bubble_id.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(bubble_id);
      }
      if (is_last_clip != false) {
        output.WriteRawTag(48);
        output.WriteBool(is_last_clip);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(num_channels);
      }
      if (bubble_id.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(bubble_id);
      }
      if (is_last_clip != false) {
        output.WriteRawTag(48);
        output.WriteBool(is_last_clip);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(id);
      }
      if (audio.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(audio);
      }
      if (sample_rate != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(sample_rate);
      }
      if (num_channels != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(num_channels);
      }
      if (bubble_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bubble_id);
      }
      if (is_last_clip != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Explore_UserChatAudioDownFrame other) {
      if (other == null) {
        return;
      }
      if (other.id != 0UL) {
        id = other.id;
      }
      if (other.audio.Length != 0) {
        audio = other.audio;
      }
      if (other.sample_rate != 0) {
        sample_rate = other.sample_rate;
      }
      if (other.num_channels != 0) {
        num_channels = other.num_channels;
      }
      if (other.bubble_id.Length != 0) {
        bubble_id = other.bubble_id;
      }
      if (other.is_last_clip != false) {
        is_last_clip = other.is_last_clip;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            audio = input.ReadBytes();
            break;
          }
          case 24: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 32: {
            num_channels = input.ReadUInt32();
            break;
          }
          case 42: {
            bubble_id = input.ReadString();
            break;
          }
          case 48: {
            is_last_clip = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            audio = input.ReadBytes();
            break;
          }
          case 24: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 32: {
            num_channels = input.ReadUInt32();
            break;
          }
          case 42: {
            bubble_id = input.ReadString();
            break;
          }
          case 48: {
            is_last_clip = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天下行 - 通用数据字段（数据类）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserChatCommonDataFieldForDataClass : pb::IMessage<PB_UserChatCommonDataFieldForDataClass>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserChatCommonDataFieldForDataClass> _parser = new pb::MessageParser<PB_UserChatCommonDataFieldForDataClass>(() => new PB_UserChatCommonDataFieldForDataClass());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserChatCommonDataFieldForDataClass> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserChatCommonDataFieldForDataClass() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserChatCommonDataFieldForDataClass(PB_UserChatCommonDataFieldForDataClass other) : this() {
      userChatId_ = other.userChatId_;
      msgId_ = other.msgId_;
      round_ = other.round_;
      msgOwner_ = other.msgOwner_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserChatCommonDataFieldForDataClass Clone() {
      return new PB_UserChatCommonDataFieldForDataClass(this);
    }

    /// <summary>Field number for the "userChatId" field.</summary>
    public const int userChatIdFieldNumber = 1;
    private long userChatId_;
    /// <summary>
    /// 用户聊天id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long userChatId {
      get { return userChatId_; }
      set {
        userChatId_ = value;
      }
    }

    /// <summary>Field number for the "msgId" field.</summary>
    public const int msgIdFieldNumber = 2;
    private long msgId_;
    /// <summary>
    /// 消息id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long msgId {
      get { return msgId_; }
      set {
        msgId_ = value;
      }
    }

    /// <summary>Field number for the "round" field.</summary>
    public const int roundFieldNumber = 3;
    private int round_;
    /// <summary>
    /// 对话轮次
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int round {
      get { return round_; }
      set {
        round_ = value;
      }
    }

    /// <summary>Field number for the "msgOwner" field.</summary>
    public const int msgOwnerFieldNumber = 4;
    private global::Msg.basic.PB_MsgBelong msgOwner_ = global::Msg.basic.PB_MsgBelong.BNone;
    /// <summary>
    /// 消息归属方
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_MsgBelong msgOwner {
      get { return msgOwner_; }
      set {
        msgOwner_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserChatCommonDataFieldForDataClass);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserChatCommonDataFieldForDataClass other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (userChatId != other.userChatId) return false;
      if (msgId != other.msgId) return false;
      if (round != other.round) return false;
      if (msgOwner != other.msgOwner) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (userChatId != 0L) hash ^= userChatId.GetHashCode();
      if (msgId != 0L) hash ^= msgId.GetHashCode();
      if (round != 0) hash ^= round.GetHashCode();
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) hash ^= msgOwner.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (userChatId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(userChatId);
      }
      if (msgId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(msgId);
      }
      if (round != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(round);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        output.WriteRawTag(32);
        output.WriteEnum((int) msgOwner);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (userChatId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(userChatId);
      }
      if (msgId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(msgId);
      }
      if (round != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(round);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        output.WriteRawTag(32);
        output.WriteEnum((int) msgOwner);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (userChatId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(userChatId);
      }
      if (msgId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(msgId);
      }
      if (round != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(round);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) msgOwner);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserChatCommonDataFieldForDataClass other) {
      if (other == null) {
        return;
      }
      if (other.userChatId != 0L) {
        userChatId = other.userChatId;
      }
      if (other.msgId != 0L) {
        msgId = other.msgId;
      }
      if (other.round != 0) {
        round = other.round;
      }
      if (other.msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        msgOwner = other.msgOwner;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            userChatId = input.ReadInt64();
            break;
          }
          case 16: {
            msgId = input.ReadInt64();
            break;
          }
          case 24: {
            round = input.ReadInt32();
            break;
          }
          case 32: {
            msgOwner = (global::Msg.basic.PB_MsgBelong) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            userChatId = input.ReadInt64();
            break;
          }
          case 16: {
            msgId = input.ReadInt64();
            break;
          }
          case 24: {
            round = input.ReadInt32();
            break;
          }
          case 32: {
            msgOwner = (global::Msg.basic.PB_MsgBelong) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天下行 - 用户语音识别过程结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_UserChatDownMsgForUserRecognizing : pb::IMessage<SC_UserChatDownMsgForUserRecognizing>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_UserChatDownMsgForUserRecognizing> _parser = new pb::MessageParser<SC_UserChatDownMsgForUserRecognizing>(() => new SC_UserChatDownMsgForUserRecognizing());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_UserChatDownMsgForUserRecognizing> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserRecognizing() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserRecognizing(SC_UserChatDownMsgForUserRecognizing other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      text_ = other.text_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserRecognizing Clone() {
      return new SC_UserChatDownMsgForUserRecognizing(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 3;
    private string text_ = "";
    /// <summary>
    /// 语音识别过程结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_UserChatDownMsgForUserRecognizing);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_UserChatDownMsgForUserRecognizing other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (text != other.text) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_UserChatDownMsgForUserRecognizing other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天下行 - 用户语音识别最终结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_UserChatDownMsgForUserRecognized : pb::IMessage<SC_UserChatDownMsgForUserRecognized>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_UserChatDownMsgForUserRecognized> _parser = new pb::MessageParser<SC_UserChatDownMsgForUserRecognized>(() => new SC_UserChatDownMsgForUserRecognized());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_UserChatDownMsgForUserRecognized> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserRecognized() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserRecognized(SC_UserChatDownMsgForUserRecognized other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      text_ = other.text_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserRecognized Clone() {
      return new SC_UserChatDownMsgForUserRecognized(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 3;
    private string text_ = "";
    /// <summary>
    /// 语音识别最终结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_UserChatDownMsgForUserRecognized);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_UserChatDownMsgForUserRecognized other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (text != other.text) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_UserChatDownMsgForUserRecognized other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天下行 - 他人语音识别过程结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_UserChatDownMsgForOtherUserRecognizing : pb::IMessage<SC_UserChatDownMsgForOtherUserRecognizing>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_UserChatDownMsgForOtherUserRecognizing> _parser = new pb::MessageParser<SC_UserChatDownMsgForOtherUserRecognizing>(() => new SC_UserChatDownMsgForOtherUserRecognizing());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_UserChatDownMsgForOtherUserRecognizing> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForOtherUserRecognizing() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForOtherUserRecognizing(SC_UserChatDownMsgForOtherUserRecognizing other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      text_ = other.text_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForOtherUserRecognizing Clone() {
      return new SC_UserChatDownMsgForOtherUserRecognizing(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 3;
    private string text_ = "";
    /// <summary>
    /// 语音识别过程结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_UserChatDownMsgForOtherUserRecognizing);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_UserChatDownMsgForOtherUserRecognizing other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (text != other.text) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_UserChatDownMsgForOtherUserRecognizing other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天下行 - 他人语音识别最终结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_UserChatDownMsgForOtherUserRecognized : pb::IMessage<SC_UserChatDownMsgForOtherUserRecognized>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_UserChatDownMsgForOtherUserRecognized> _parser = new pb::MessageParser<SC_UserChatDownMsgForOtherUserRecognized>(() => new SC_UserChatDownMsgForOtherUserRecognized());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_UserChatDownMsgForOtherUserRecognized> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForOtherUserRecognized() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForOtherUserRecognized(SC_UserChatDownMsgForOtherUserRecognized other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      text_ = other.text_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForOtherUserRecognized Clone() {
      return new SC_UserChatDownMsgForOtherUserRecognized(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 3;
    private string text_ = "";
    /// <summary>
    /// 语音识别最终结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_UserChatDownMsgForOtherUserRecognized);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_UserChatDownMsgForOtherUserRecognized other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (text != other.text) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_UserChatDownMsgForOtherUserRecognized other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天下行 - 他人回复翻译
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_UserChatDownMsgForOtherUserReplyTranslate : pb::IMessage<SC_UserChatDownMsgForOtherUserReplyTranslate>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_UserChatDownMsgForOtherUserReplyTranslate> _parser = new pb::MessageParser<SC_UserChatDownMsgForOtherUserReplyTranslate>(() => new SC_UserChatDownMsgForOtherUserReplyTranslate());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_UserChatDownMsgForOtherUserReplyTranslate> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForOtherUserReplyTranslate() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForOtherUserReplyTranslate(SC_UserChatDownMsgForOtherUserReplyTranslate other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      replyTranslateText_ = other.replyTranslateText_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForOtherUserReplyTranslate Clone() {
      return new SC_UserChatDownMsgForOtherUserReplyTranslate(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "replyTranslateText" field.</summary>
    public const int replyTranslateTextFieldNumber = 3;
    private string replyTranslateText_ = "";
    /// <summary>
    /// 他人回复文本翻译
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyTranslateText {
      get { return replyTranslateText_; }
      set {
        replyTranslateText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_UserChatDownMsgForOtherUserReplyTranslate);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_UserChatDownMsgForOtherUserReplyTranslate other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (replyTranslateText != other.replyTranslateText) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (replyTranslateText.Length != 0) hash ^= replyTranslateText.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyTranslateText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyTranslateText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (replyTranslateText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyTranslateText);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_UserChatDownMsgForOtherUserReplyTranslate other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.replyTranslateText.Length != 0) {
        replyTranslateText = other.replyTranslateText;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            replyTranslateText = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            replyTranslateText = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天下行 - 用户回复示例（user_reply_example: 也叫事前脚手架）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_UserChatDownMsgForUserReplyExample : pb::IMessage<SC_UserChatDownMsgForUserReplyExample>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_UserChatDownMsgForUserReplyExample> _parser = new pb::MessageParser<SC_UserChatDownMsgForUserReplyExample>(() => new SC_UserChatDownMsgForUserReplyExample());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_UserChatDownMsgForUserReplyExample> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserReplyExample() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserReplyExample(SC_UserChatDownMsgForUserReplyExample other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      exampleText_ = other.exampleText_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserReplyExample Clone() {
      return new SC_UserChatDownMsgForUserReplyExample(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "exampleText" field.</summary>
    public const int exampleTextFieldNumber = 3;
    private string exampleText_ = "";
    /// <summary>
    /// 用户回复示例文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleText {
      get { return exampleText_; }
      set {
        exampleText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_UserChatDownMsgForUserReplyExample);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_UserChatDownMsgForUserReplyExample other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (exampleText != other.exampleText) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (exampleText.Length != 0) hash ^= exampleText.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (exampleText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(exampleText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (exampleText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(exampleText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (exampleText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleText);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_UserChatDownMsgForUserReplyExample other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.exampleText.Length != 0) {
        exampleText = other.exampleText;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            exampleText = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            exampleText = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天下行 - 用户回复示例翻译
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_UserChatDownMsgForUserReplyExampleTranslate : pb::IMessage<SC_UserChatDownMsgForUserReplyExampleTranslate>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_UserChatDownMsgForUserReplyExampleTranslate> _parser = new pb::MessageParser<SC_UserChatDownMsgForUserReplyExampleTranslate>(() => new SC_UserChatDownMsgForUserReplyExampleTranslate());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_UserChatDownMsgForUserReplyExampleTranslate> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserReplyExampleTranslate() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserReplyExampleTranslate(SC_UserChatDownMsgForUserReplyExampleTranslate other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      exampleTranslateText_ = other.exampleTranslateText_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForUserReplyExampleTranslate Clone() {
      return new SC_UserChatDownMsgForUserReplyExampleTranslate(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "exampleTranslateText" field.</summary>
    public const int exampleTranslateTextFieldNumber = 3;
    private string exampleTranslateText_ = "";
    /// <summary>
    /// 用户回复示例文本翻译
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleTranslateText {
      get { return exampleTranslateText_; }
      set {
        exampleTranslateText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_UserChatDownMsgForUserReplyExampleTranslate);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_UserChatDownMsgForUserReplyExampleTranslate other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (exampleTranslateText != other.exampleTranslateText) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (exampleTranslateText.Length != 0) hash ^= exampleTranslateText.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (exampleTranslateText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(exampleTranslateText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (exampleTranslateText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(exampleTranslateText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (exampleTranslateText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleTranslateText);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_UserChatDownMsgForUserReplyExampleTranslate other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.exampleTranslateText.Length != 0) {
        exampleTranslateText = other.exampleTranslateText;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            exampleTranslateText = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            exampleTranslateText = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天下行 - 业务事件
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_UserChatDownMsgForBizEvent : pb::IMessage<SC_UserChatDownMsgForBizEvent>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_UserChatDownMsgForBizEvent> _parser = new pb::MessageParser<SC_UserChatDownMsgForBizEvent>(() => new SC_UserChatDownMsgForBizEvent());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_UserChatDownMsgForBizEvent> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForBizEvent() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForBizEvent(SC_UserChatDownMsgForBizEvent other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      bizEvent_ = other.bizEvent_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForBizEvent Clone() {
      return new SC_UserChatDownMsgForBizEvent(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_UserChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "bizEvent" field.</summary>
    public const int bizEventFieldNumber = 3;
    private global::Msg.explore.PB_Explore_UserChatUpBizEvent bizEvent_ = global::Msg.explore.PB_Explore_UserChatUpBizEvent.EO_UC_UNKNOWN_BIZ_EVENT_TYPE;
    /// <summary>
    /// 业务事件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_UserChatUpBizEvent bizEvent {
      get { return bizEvent_; }
      set {
        bizEvent_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_UserChatDownMsgForBizEvent);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_UserChatDownMsgForBizEvent other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (bizEvent != other.bizEvent) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (bizEvent != global::Msg.explore.PB_Explore_UserChatUpBizEvent.EO_UC_UNKNOWN_BIZ_EVENT_TYPE) hash ^= bizEvent.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (bizEvent != global::Msg.explore.PB_Explore_UserChatUpBizEvent.EO_UC_UNKNOWN_BIZ_EVENT_TYPE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) bizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (bizEvent != global::Msg.explore.PB_Explore_UserChatUpBizEvent.EO_UC_UNKNOWN_BIZ_EVENT_TYPE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) bizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (bizEvent != global::Msg.explore.PB_Explore_UserChatUpBizEvent.EO_UC_UNKNOWN_BIZ_EVENT_TYPE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) bizEvent);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_UserChatDownMsgForBizEvent other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.bizEvent != global::Msg.explore.PB_Explore_UserChatUpBizEvent.EO_UC_UNKNOWN_BIZ_EVENT_TYPE) {
        bizEvent = other.bizEvent;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 24: {
            bizEvent = (global::Msg.explore.PB_Explore_UserChatUpBizEvent) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_UserChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 24: {
            bizEvent = (global::Msg.explore.PB_Explore_UserChatUpBizEvent) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 用户聊天状态下行
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_UserChatDownMsgUserChatStatus : pb::IMessage<SC_UserChatDownMsgUserChatStatus>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_UserChatDownMsgUserChatStatus> _parser = new pb::MessageParser<SC_UserChatDownMsgUserChatStatus>(() => new SC_UserChatDownMsgUserChatStatus());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_UserChatDownMsgUserChatStatus> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgUserChatStatus() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgUserChatStatus(SC_UserChatDownMsgUserChatStatus other) : this() {
      userChatId_ = other.userChatId_;
      status_ = other.status_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgUserChatStatus Clone() {
      return new SC_UserChatDownMsgUserChatStatus(this);
    }

    /// <summary>Field number for the "userChatId" field.</summary>
    public const int userChatIdFieldNumber = 1;
    private ulong userChatId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong userChatId {
      get { return userChatId_; }
      set {
        userChatId_ = value;
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int statusFieldNumber = 2;
    private global::Msg.explore.PB_Explore_UserChatStatus status_ = global::Msg.explore.PB_Explore_UserChatStatus.EO_UC_STATUS_UNKNOWN;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_UserChatStatus status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_UserChatDownMsgUserChatStatus);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_UserChatDownMsgUserChatStatus other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (userChatId != other.userChatId) return false;
      if (status != other.status) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (userChatId != 0UL) hash ^= userChatId.GetHashCode();
      if (status != global::Msg.explore.PB_Explore_UserChatStatus.EO_UC_STATUS_UNKNOWN) hash ^= status.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (userChatId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(userChatId);
      }
      if (status != global::Msg.explore.PB_Explore_UserChatStatus.EO_UC_STATUS_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (userChatId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(userChatId);
      }
      if (status != global::Msg.explore.PB_Explore_UserChatStatus.EO_UC_STATUS_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (userChatId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(userChatId);
      }
      if (status != global::Msg.explore.PB_Explore_UserChatStatus.EO_UC_STATUS_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) status);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_UserChatDownMsgUserChatStatus other) {
      if (other == null) {
        return;
      }
      if (other.userChatId != 0UL) {
        userChatId = other.userChatId;
      }
      if (other.status != global::Msg.explore.PB_Explore_UserChatStatus.EO_UC_STATUS_UNKNOWN) {
        status = other.status;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            userChatId = input.ReadUInt64();
            break;
          }
          case 16: {
            status = (global::Msg.explore.PB_Explore_UserChatStatus) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            userChatId = input.ReadUInt64();
            break;
          }
          case 16: {
            status = (global::Msg.explore.PB_Explore_UserChatStatus) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_UserChatDownMsgForExit : pb::IMessage<SC_UserChatDownMsgForExit>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_UserChatDownMsgForExit> _parser = new pb::MessageParser<SC_UserChatDownMsgForExit>(() => new SC_UserChatDownMsgForExit());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_UserChatDownMsgForExit> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.UserChatReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForExit() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForExit(SC_UserChatDownMsgForExit other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_UserChatDownMsgForExit Clone() {
      return new SC_UserChatDownMsgForExit(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_UserChatDownMsgForExit);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_UserChatDownMsgForExit other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_UserChatDownMsgForExit other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

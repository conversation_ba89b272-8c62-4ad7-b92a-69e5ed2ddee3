using System.Collections.Generic;
using FairyGUI;
using Msg.basic;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Settlement
{
    public class SettlementModel : BaseModel
    {
        public SettlementModel() : base(ModelConsts.Settlement){ }

        //总结算数据
        public PB_GetDialogSettlementResp DialogResultListData { get; private set; }
        
        //训练中心结算
        public PB_TrainResultData DrillHubResultInfo { get; private set; }
        
        //所有结算页数据
        public PB_DialogSettlementData SettlementData { get; private set; }
        
        //对话结算选择
        public PB_DialogSettlementSelectData SelectData { get; private set; }
        
        //打卡数据
        public PB_CheckinForSettlementData CheckInData { get; private set; }
        
        //里程碑数据
        public PB_MilestoneForSettlementData MilestoneData { get; private set; }
        
        //激励每日任务数据
        public PB_IncentiveQuestProgress SignDailyData { get; private set; }
        
        //经验弹窗数据
        public PB_DialogSettlementPopData SettlementPopData { get; private set; }

        //好友推荐数据
        public PB_RecommendedFriendListData SettlementFriendsData { get; private set; }
        
        public PB_FriendshipTaskMatchData FriendsQuestTeammateData{ get; private set; }
        
        public PB_FriendshipTaskData FriendsQuestProgressData{ get; private set; }

        //对话模式
        public PB_DialogMode DialogMode { get; private set; }
        
        //平均分数
        private int _averageScore = 0; 
        public int averageScore { get { return this._averageScore; } }
        //发音分数
        private int _pronounceScore = 0; 
        public int pronounceScore { get { return this._pronounceScore; } }
        //语调分数
        private int _intonationScore = 0; 
        public int intonationScore { get { return this._intonationScore; } }
        //丰富度分数
        private int _richnessScore = 0; 
        public int richnessScore { get { return this._richnessScore; } }
        //流利度分数
        private int _fluencyScore = 0; 
        public int fluencyScore { get { return this._fluencyScore; } }
        //完整度分数
        private int _completionScore = 0; 

        public Dictionary<PB_DialogMode, string> DicDialogModeByView { get; } = new()
        {
            { PB_DialogMode.WarmupPractice, UIConsts.SettlementCommon },
            { PB_DialogMode.Challenge, UIConsts.SettlementCommon },
            { PB_DialogMode.Tutor, UIConsts.SettlementCommon },
            { PB_DialogMode.Career, UIConsts.SettlementFree },
            { PB_DialogMode.Exercise, UIConsts.SettlementPractice },
            { PB_DialogMode.Intensify, UIConsts.SettlementCommon },
            { PB_DialogMode.QuickPractice, UIConsts.SettlementFrag },
            { PB_DialogMode.RolePlay, UIConsts.SettlementCommon },
            { PB_DialogMode.Quiz, UIConsts.SettlementFrag },
            { PB_DialogMode.Flash, UIConsts.SettlementAUA },
            { PB_DialogMode.Video ,UIConsts.SettlementVideoTask},
            { PB_DialogMode.MNone ,UIConsts.SettlementFrag},
        };

        public Dictionary<PB_SpineTypeEnum, string> DicSpineTypeByView { get; } = new()
        {
            { PB_SpineTypeEnum.SpineTNone, "ui_fragment_settle_4" },
            { PB_SpineTypeEnum.SpineTPerfect, "ui_fragment_settle_0" },
            { PB_SpineTypeEnum.SpineTFast, "ui_fragment_settle_1" },
            { PB_SpineTypeEnum.SpineTHoldOn, "ui_fragment_settle_3" },
            { PB_SpineTypeEnum.SpineTFinish, "ui_fragment_settle_4" }
        };
        
        public void SetDialogResultListData(PB_GetDialogSettlementResp data)
        {
            DialogResultListData = data;
        }

        public void SetDialogMode(PB_DialogMode mode)
        {
            DialogMode = mode;
        }
        
        public void SetTrainResultData(PB_TrainResultData data)
        {
            DrillHubResultInfo = data;
        }

        public void SetSettlementData(PB_DialogSettlementData data)
        {
            SettlementData = data;
            _averageScore = (int)data.avg_sorce;
            _intonationScore = data.intonation != null ? (int)data.intonation.avg_sorce : 0;
            _pronounceScore = data.pronunce != null ? (int)data.pronunce.avg_sorce : 0;
            _fluencyScore = data.fluency != null ? (int)data.fluency.avg_sorce : 0;
        }

        public void SetSelectData(PB_DialogSettlementSelectData data)
        {
            SelectData = data;
        }

        public void SetCheckInData(PB_CheckinForSettlementData data)
        {
            CheckInData = data;
        }

        public void SetMilestoneData(PB_MilestoneForSettlementData data)
        {
            MilestoneData = data;
        }

        public void SetSignDailyData(PB_IncentiveQuestProgress data)
        {
            SignDailyData = data;
        }

        public void SetSettlementPopData(PB_DialogSettlementPopData data)
        {
            SettlementPopData = data;
        }
        
        public void SetSettlementFriendsData(PB_RecommendedFriendListData data)
        {
            SettlementFriendsData = data;
        }
        
        public void SetFriendsQuestTeammateData(PB_FriendshipTaskMatchData data)
        {
            FriendsQuestTeammateData = data;
        }
        
        public void SetFriendsQuestProgressData(PB_FriendshipTaskData data)
        {
            FriendsQuestProgressData = data;
        }

        public void ClearTaskSettlementInfo()
        {
            DialogResultListData = null;
            DrillHubResultInfo = null;
            SettlementData = null;
            SelectData = null;
            DialogMode = PB_DialogMode.MNone;
        }

        private StaminaController Stamina => GetController<StaminaController>(ModelConsts.Stamina);
        public void RefreshStamina(GTextField gtext, int num)
        {
            gtext.text = "-" + num;
            Color nowColor;

            if (!Stamina.IsEnough(num))
            {
                ColorUtility.TryParseHtmlString("#FF007A", out nowColor);
            }
            else
            {
                ColorUtility.TryParseHtmlString("#FFFFFF", out nowColor);
            }
            gtext.color = nowColor;
        }

        #region mockData
        public void SetCheckinInfoTmp()
        {
            SignDailyData = new PB_IncentiveQuestProgress()
            {
                quest_list =
                {
                    new PB_CheckinReward()
                    {
                        reward_materia_type = PB_RewardMateriaType.PB_RewardMateriaType_StreakFreeze,
                        reward_status = PB_CheckinRewardStatus.PB_CheckinRewardStatus_Acquired,
                        threshold = 3,
                        isUnlock = true,
                        task_title = "hello",
                        finish_cnt = 3,
                        pre_finish_cnt = 2,
                        reward_value = 30,
                        
                    },
                    new PB_CheckinReward()
                    {
                        reward_materia_type = PB_RewardMateriaType.PB_RewardMateriaType_Diamond,
                        reward_status = PB_CheckinRewardStatus.PB_CheckinRewardStatus_Acquired,
                        threshold = 3,
                        isUnlock = true,
                        task_title = "hello2",
                        finish_cnt = 3,
                        pre_finish_cnt = 2,
                        reward_value = 30,
                    },
                    new PB_CheckinReward()
                    {
                        reward_materia_type = PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_1_5,
                        reward_status = PB_CheckinRewardStatus.PB_CheckinRewardStatus_Acquired,
                        threshold = 3,
                        isUnlock = true,
                        task_title = "hello2",
                        finish_cnt = 3,
                        pre_finish_cnt = 2,
                        reward_value = 30,
                    },
                },
                reward_aggr_map =
                {
                    {
                        "PB_RewardMateriaType_TimeXPBoost",
                        new PB_DrawRewardData()
                        {
                            reward_materia_type = PB_RewardMateriaType.PB_RewardMateriaType_TimeXPBoost_2,
                            reward_materia_num = 20,
                        }
                    },
                    {
                        "PB_RewardMateriaType_UnlimitedStamina",
                        new PB_DrawRewardData()
                        {
                            reward_materia_type = PB_RewardMateriaType.PB_RewardMateriaType_StreakFreeze,
                            reward_materia_num = 30,
                        }
                    },
                    {
                        "PB_RewardMateriaType_Diamond",
                        new PB_DrawRewardData()
                        {
                            reward_materia_type = PB_RewardMateriaType.PB_RewardMateriaType_Diamond,
                            reward_materia_num = 80,
                        }
                    },
                },
            };

            CheckInData = new PB_CheckinForSettlementData()
            {
                continue_checkin_days = 1,
                is_checkin = true,
                is_complete_recheckin = false,
                checkin_list =
                {
                    new PB_CheckinItem()
                    {
                        date = "2024-10-27",
                        is_checkin = true,
                        is_recheckin = false,
                        first_checkin_date = "2024-09-02"
                    },
                    new PB_CheckinItem()
                    {
                        date = "2024-10-28",
                        is_checkin = true,
                        is_recheckin = false,
                        first_checkin_date = "2024-09-02"
                    },
                    new PB_CheckinItem()
                    {
                        date = "2024-10-29",
                        is_checkin = false,
                        is_recheckin = true,
                        // update_date = TimeExt.serverTime.ToString("yyyy-MM-dd")
                    },
                    new PB_CheckinItem()
                    {
                        date = "2024-10-30",
                        is_checkin = true,
                        is_recheckin = false,
                        // update_date = TimeExt.serverTime.ToString("yyyy-MM-dd")
                    },
                    new PB_CheckinItem()
                    {
                        date = "2024-10-31",
                        is_checkin = true,
                        is_recheckin = false,
                        // update_date = TimeExt.serverTime.ToString("yyyy-MM-dd")
                    },
                    new PB_CheckinItem()
                    {
                        date = "2024-11-01",
                        is_checkin = true,
                        is_recheckin = false,
                        // update_date = TimeExt.serverTime.AddDays(5).ToString("yyyy-MM-dd")
                    },
                    new PB_CheckinItem()
                    {
                        date = "2024-11-02",
                        is_checkin = true,
                        is_recheckin = false,
                        // update_date = TimeExt.serverTime.AddDays(5).ToString("yyyy-MM-dd")
                    },
                },
                milestone = new PB_UserCheckinMilestone()
                {
                    current_milestone = new PB_UserCheckinMilestoneItem()
                    {
                        target_checkin_days = 1,
                        update_days_till_now = 0,
                        target_checkin_status = PB_CheckinMilestoneStatus.PB_CheckinMilestoneStatus_Complete,
                        diamond_reward = 70
                    },
                    target_milestone = new PB_UserCheckinMilestoneItem()
                    {
                        target_checkin_days = 1,
                    },
                    pre_milestone = new PB_UserCheckinMilestoneItem()
                    {
                        target_checkin_days = 0,
                    },
                    milestone_list =
                    {
                        new PB_UserCheckinMilestoneItem()
                        {
                            target_checkin_days = 1,
                            target_checkin_status = PB_CheckinMilestoneStatus.PB_CheckinMilestoneStatus_Progressing,
                            diamond_reward = 30
                        },
                        new PB_UserCheckinMilestoneItem()
                        {
                            target_checkin_days = 7,
                            target_checkin_status = PB_CheckinMilestoneStatus.PB_CheckinMilestoneStatus_Progressing,
                            diamond_reward = 50
                        },
                        new PB_UserCheckinMilestoneItem()
                        {
                            target_checkin_days = 14,
                            target_checkin_status = PB_CheckinMilestoneStatus.PB_CheckinMilestoneStatus_Incomplete,
                            diamond_reward = 100
                        },
                        new PB_UserCheckinMilestoneItem()
                        {
                            target_checkin_days = 30,
                            target_checkin_status = PB_CheckinMilestoneStatus.PB_CheckinMilestoneStatus_NotSet,
                            diamond_reward = 150
                        },
                        new PB_UserCheckinMilestoneItem()
                        {
                            target_checkin_days = 50,
                            target_checkin_status = PB_CheckinMilestoneStatus.PB_CheckinMilestoneStatus_NotSet,
                            diamond_reward = 250
                        },
                        new PB_UserCheckinMilestoneItem()
                        {
                            target_checkin_days = 70,
                            target_checkin_status = PB_CheckinMilestoneStatus.PB_CheckinMilestoneStatus_NotSet,
                            diamond_reward = 450
                        },
                    },
                },
            };
        }
        #endregion
        // for test 求求不要删 做任务测试麻烦

        public void testFriendsData()
        {
            FriendsQuestProgressData = new PB_FriendshipTaskData();
            FriendsQuestProgressData.finish_cnt = 100;
            FriendsQuestProgressData.task_status = PB_CheckinRewardStatus.PB_CheckinRewardStatus_Finish;
            FriendsQuestProgressData.threshold = 200;
        }
        
        private List<long> testUserIdList = new List<long>()
        {
            1250105208852123648,
            7233891395233529856,
            748626294816702464,
            1250070001166618624,
            748501614511980544,
            7233832508486664192,
            7233881796955275264,
            748501789773611008,
            7233833684616085504,
            748609117643112448
        };

        public void TestFriendsTeammateList()
        {
            PB_FriendshipTaskMatchBaseInfo baseInfo = new PB_FriendshipTaskMatchBaseInfo();
            baseInfo.remain_days = 4;
            
            List<PB_FriendshipTaskUserItem> list = new List<PB_FriendshipTaskUserItem>();
            for (int i = 0; i < testUserIdList.Count; i++)
            {
                PB_FriendshipTaskUserItem info = new PB_FriendshipTaskUserItem();
                info.user_id = testUserIdList[i];
                info.player_name = i.ToString();
                info.is_self = i == 0 ? true : false;
             

                PB_DressUpData dresupData = new PB_DressUpData();
                dresupData.frame_id = (102100100 + i % 3).ToString();
                dresupData.name_style_id = (102101100 + i % 3).ToString();
                info.user_dress_up_data = dresupData;
                info.head_item_type = PB_HeadItemType.HEAD_ITEM_TYPE_MATERIA;
                info.head_item_id = 102102100 + i % 11;
                info.growth_level = 7;
                info.pre_finish_cnt = 3;
                info.finish_cnt = 8;
                
                list.Add(info);
            }
            
            PB_FriendshipTaskMatchData data = new PB_FriendshipTaskMatchData();
            data.base_info = baseInfo;
            data.user_list.AddRange(list);

            FriendsQuestTeammateData = data;
        }
    }   
}
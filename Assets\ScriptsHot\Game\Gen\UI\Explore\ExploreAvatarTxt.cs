/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreAvatarTxt : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreAvatarTxt";

        public GComponent tfOrigin;
        public GTextField txtTran;
        public GGroup Container;
        public GLoader3D sp;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            tfOrigin = (GComponent)com.GetChildAt(1);
            txtTran = (GTextField)com.GetChildAt(2);
            Container = (GGroup)com.GetChildAt(3);
            sp = (GLoader3D)com.GetChildAt(4);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            tfOrigin = null;
            txtTran = null;
            Container = null;
            sp = null;
        }
    }
}
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Threading.Tasks;

using System;
using Agora.Rtc;
using LitJson;

namespace ScriptsHot.Game.Modules.AgoraRtc
{
    public class RtcEngineManager
    {
        public static int CHANNEL = 1;//单声道
        public int PULL_FREQ_PER_SEC = 100;
        public static int SAMPLE_RATE = 16000;//16k 不用48k

        //internal RingBuffer<float> _audioBuffer;

        private static RtcEngineManager _instance;
        public static RtcEngineManager Instance => _instance ??= new RtcEngineManager();

        public IRtcEngine RtcEngine { get; private set; }
        private bool _initialized = false;

        private RtcEngineManager() { }
        private string _appID;

        private string _channel;
        public void Initialize(string appId,AREA_CODE areaCode = AREA_CODE.AREA_CODE_GLOB)
        {
            if (_initialized) return;

            var bufferLength = SAMPLE_RATE * CHANNEL; // 1-sec-length buffer
            //_audioBuffer = new RingBuffer<float>(bufferLength, true);


            RtcEngine = Agora.Rtc.RtcEngine.CreateAgoraRtcEngine();
            
          
            var context = new RtcEngineContext
            {
                appId = appId,
                channelProfile = CHANNEL_PROFILE_TYPE.CHANNEL_PROFILE_LIVE_BROADCASTING,
                audioScenario = AUDIO_SCENARIO_TYPE.AUDIO_SCENARIO_MEETING,
                areaCode = areaCode
            };
            
     
            _appID = appId;
            
            var result = RtcEngine.Initialize(context);
            if (result != 0) {
                VFDebug.LogError("RtcEngine init Error,result="+result);
            }
            // 事件处理器需要外部设置
            RtcEngine.InitEventHandler(new RtcEngineUserEventHandler());

            
            //RtcEngine.SetParameters("{\"che.audio.keep.audiosession\": true, \"che.audio.enable.mixing\": true}");//允许unity的声音同时触发播放
            result= RtcEngine.SetParameters("{\"che.audio.keep.audiosession\":true}");
            if (result != 0) {
                VFDebug.LogError("RtcEngine audiosession=true json config fail");
            }
          

            RtcEngine.SetChannelProfile(CHANNEL_PROFILE_TYPE.CHANNEL_PROFILE_COMMUNICATION);
            RtcEngine.SetClientRole(CLIENT_ROLE_TYPE.CLIENT_ROLE_BROADCASTER);
            RtcEngine.EnableAudioVolumeIndication(200, 3, true);


            RtcEngine.SetRecordingAudioFrameParameters(
                SAMPLE_RATE, 
                CHANNEL,
                RAW_AUDIO_FRAME_OP_MODE_TYPE.RAW_AUDIO_FRAME_OP_MODE_READ_WRITE, 
                1024);
            RtcEngine.SetPlaybackAudioFrameParameters(
                sampleRate: SAMPLE_RATE,    // 注意：采样率必须与采集端实际发出的数据匹配，建议与 `SetRecordingAudioFrameParameters` 一致且支持（如44100/48000）
                channel: CHANNEL,           // 声道数，1=单声道(Mono)，2=双声道(Stereo)
                mode: RAW_AUDIO_FRAME_OP_MODE_TYPE.RAW_AUDIO_FRAME_OP_MODE_READ_ONLY, // 或 READ_WRITE
                samplesPerCall: 1024  // 每帧采样点数
            );
            
            RtcEngine.RegisterAudioFrameObserver(new AudioFrameObserver(),
                AUDIO_FRAME_POSITION.AUDIO_FRAME_POSITION_PLAYBACK |
                AUDIO_FRAME_POSITION.AUDIO_FRAME_POSITION_RECORD |
                AUDIO_FRAME_POSITION.AUDIO_FRAME_POSITION_MIXED |
                AUDIO_FRAME_POSITION.AUDIO_FRAME_POSITION_BEFORE_MIXING |
                AUDIO_FRAME_POSITION.AUDIO_FRAME_POSITION_EAR_MONITORING,
               OBSERVER_MODE.RAW_DATA);
            
         
            _initialized = true;
        }

        public int JoinChannel(string token, string channelName, uint uid = 0)
        {
            int audioRet= RtcEngine.EnableAudio();
            if (audioRet != 0) {
                VFDebug.LogError("JoinChannel enable时失败，retCode=" +audioRet );
              
            }

            var ret = RtcEngine.JoinChannel(token, channelName, "", uid);
            if (ret == 0) {
                _channel = channelName;
              
            }

            return ret;
        }

        public int LeaveChannel()
        {
            _channel = string.Empty;
            int ret =RtcEngine.LeaveChannel();
            int audioRet = RtcEngine.DisableAudio();
            
            if (audioRet != 0)
            {
                VFDebug.LogError("LeaveChannel RtcEngine.DisableAudio failed，retCode=" +audioRet );
            }
           
            if (ret != 0 || audioRet != 0)
            {
                VFDebug.LogError("LeaveChannel  存在异常");
            }

            return ret;
        }

        //public int StartEchoTest(EchoTestConfiguration config)
        //{
        //    return RtcEngine.StartEchoTest(config);
        //}

        //public int StopEchoTest()
        //{
        //    return RtcEngine.StopEchoTest();
        //}

        public bool ConfigMicAudio(bool isTurnOn)
        {
            var options = new ChannelMediaOptions();
            options.publishMicrophoneTrack.SetValue(isTurnOn);
            var ret = this.UpdateChannelMediaOptions(options);
            if (ret == 0)
            {
                Debug.Log("ConfigMicAudio mic.isTurnOn:" + isTurnOn);//UpdateChannelMediaOptions: " + nRet);
                return true;
            }
            else {
                Debug.LogError($"ConfigMicAudio to isTurnOn:{isTurnOn}  failed,errcoe={ret} ");
                return false;
            }
            
        }

        public int UpdateChannelMediaOptions(ChannelMediaOptions options)
        {
            return RtcEngine.UpdateChannelMediaOptions(options);
        }

        public IAudioDeviceManager GetAudioDeviceManager()
        {
            return RtcEngine.GetAudioDeviceManager();
        }

        public void Dispose()
        {
            if (RtcEngine != null)
            {
                RtcEngine.Dispose();
                RtcEngine = null;
                _initialized = false;
            }
        }

        public void SetEventHandler(IRtcEngineEventHandler handler)
        {
            RtcEngine.InitEventHandler(handler);
        }

        //额外的 API查询
        //https://docportal.shengwang.cn/cn/voice-legacy/rtc_channel_management_restfulapi?platform=iOS#查询用户列表

        //https://api.sd-rtn.com/dev/v1/channel/user/{appid}/{channelName}
        //https://api.sd-rtn.com/dev/v1/channel/user/{appid}/{channelName}/hosts_only
        public async UniTask<bool> IsChattingBothJoin()
        {
            //if (string.IsNullOrEmpty(this._channel))
            //{
            //    Debug.LogError("尚未加入有效的channel 就查询内部情况了");
            //    return false;
            //}
            //string url = $"https://api.sd-rtn.com/dev/v1/channel/user/{this._appID }/{this._channel}/hosts_only";

            //string retString = await GHttpManager.instance.GetAsync(url);

            //if (string.IsNullOrEmpty(retString))
            //{
            //    return false;
            //}
            //try {
            //    var userList = JsonMapper.ToObject<RestfulResult_ChannelUserList>(retString);
            //    return userList != null && userList.success && userList.data.channel_exist && userList.data.total >= 2;//1v1房间只看总人数》=2即可
            //}
            //catch (Exception e)
            //{
            //    Debug.LogError("IsChattingBothJoin expception:"+e.ToString());
            //    return false;
            //}

            return true;
        }
    }

    public class RestfulResult_ChannelUserList
    {
        public bool success;
        public RestfulResult_ChannelUserListDetail data;
    }
    public class RestfulResult_ChannelUserListDetail
    {
        public bool channel_exist;
        public int mode;
        public int total;//内部人数
        public List<long> users;//uid
    }
}
﻿using System;
using System.Collections.Generic;
using System.Reflection;
using AnimationSystem;
using CartoonScripts.TakePhoto.Runtime;
using UIBind.Profile;
using UnityEngine;
using YooAsset;

public partial class CartoonAvatarRoot : MonoBehaviour
{
    //确认每个Part都是 [1个] SkinnedMeshRenderer吗?
    public Dictionary<string, SkinnedMeshRenderer> parts = new Dictionary<string, SkinnedMeshRenderer>();
    
    //骨骼的Transform列表
    public Dictionary<string, Transform> boneDictionary = new Dictionary<string, Transform>();
    
    public CartoonAvatarData avatarData;

    //此角色的id
    private string id;

    public void SetAvatarData(CartoonAvatarData avatarData)
    {
        if (avatarData == null)
        {
            Debug.LogError("avatarData is null");
        }
        this.avatarData = avatarData;
        avatarData.avatarRoot = this;
    }

    public void LoadAvatar()
    {
        // 删除所有子节点
        for(int i= transform.childCount-1; i>=0;i--)
        {
            var child = transform.GetChild(i);
            DestroyImmediate(child.gameObject);//必须要立即同步执行，后续还有同步加载节点 以及 根据命名检索，如果不立即删除掉会有一个瞬间存在重复的部件
        }
        parts.Clear();

        
        // 动态加载属性并生成对象
        Type type = typeof(CartoonAvatarData);

        FieldInfo[] infos= type.GetFields(BindingFlags.Public | BindingFlags.Instance);

        FieldInfo field;
        
        //Debug.LogWarning("待加载部件数" + infos.Length);
        
        for (int i=0;i<infos.Length;i++)
        {
            field = infos[i];
            PartData pData = field.GetValue(this.avatarData) as PartData;
            if (pData == null)
            {
                //Debug.Log("OnLoad pData null skip,  Name="+ field.Name);
                continue;
            }
            pData.SetName(field.Name);//注意这一行不要删除，是用来填充part的类别名称

            if (string.IsNullOrEmpty(pData.res)) {
                //Debug.Log("OnLoad pData-res empty,  Name=" + field.Name);
                continue;
            }

            //根据部件 自定义执行各自的逻辑
            LoadPart(pData);
            //  Debug.LogWarning();
        }
        
        //2.19由于A部件加载依赖B部件信息。添加一个后加载项目,确保所有的常规加载完成后再加载。
        for (int i = 0; i < infos.Length; i++)
        {
            field = infos[i];
            PartData pData = field.GetValue(avatarData) as PartData;
            if (pData == null)
            {
                continue;
            }
            pData.SetName(field.Name);//注意这一行不要删除，是用来填充part的类别名称

            if (string.IsNullOrEmpty(pData.res)) {
                continue;
            }
            
            OnPostLoadPart(pData);
        }
    }

    public void FaceAdjust()
    {
        
    }

    #region 骨骼同步
    public void SyncBone()
    {
        parts.TryGetValue(AvatarGenericData.PART_BODY, out SkinnedMeshRenderer bodyRenderer);

        if (!bodyRenderer)
        {
            Debug.LogError("未找到bodyRenderer");
            return;
        }
        
        RegisterBones(bodyRenderer);
        boneDictionary.TryGetValue(AvatarGenericData.HEAD_BONE_NAME,out Transform headBone);
        
        if (bodyRenderer)
        {
            foreach (KeyValuePair<string, SkinnedMeshRenderer> kvp in parts)
            {
                if (kvp.Key != AvatarGenericData.PART_BODY)
                {
                    var part = avatarData.GetPartDataByPartName(kvp.Key);
                    if (part.partRunTimeData.isHeadPart && headBone)
                    {
                        part.partRunTimeData.partObject.transform.SetParent(headBone);
                    }
                    else
                    {
                        ReplaceBones(bodyRenderer,kvp.Value);
                    }
                }
            }
        }
        
        //6.16.2025 同步头
        parts.TryGetValue(AvatarGenericData.PART_HEAD, out SkinnedMeshRenderer headRenderer);

        if (!headRenderer)
        {
            Debug.LogError("未找到headRenderer");
            return;
        }
        
        RegisterBones(headRenderer);
        
        if (headRenderer)
        {
            foreach (KeyValuePair<string, SkinnedMeshRenderer> kvp in parts)
            {
                if (kvp.Key != AvatarGenericData.PART_HEAD)
                {
                    var part = avatarData.GetPartDataByPartName(kvp.Key);
                    if (part.partRunTimeData.isHeadPart)
                    {
                        ReplaceBones(headRenderer,kvp.Value);
                    }
                }
            }
        }
    }
    
    public void ReplaceBones(SkinnedMeshRenderer smr1, SkinnedMeshRenderer smr2)
    {
        if (!smr1 || !smr2)
        {
            VFDebug.LogError("SkinnedMeshRenderer 参数不能为空！");
            return;
        }

        // 获取骨骼名称与 Transform 的映射表
        var smr1BoneDict = boneDictionary;
        
        // 检查并替换骨骼
        var smr2Bones = smr2.bones;
        for (int i = 0; i < smr2Bones.Length; i++)
        {
            var bone = smr2Bones[i];
            if (bone && smr1BoneDict.TryGetValue(bone.name, out var matchingBone))
            {
                smr2Bones[i] = matchingBone;
            }
            else
            {
                VFDebug.LogWarning($"无法找到名称为 {bone?.name ?? "null"} 的骨骼于 目标SMR 中！");
              //  return; // 终止函数
            }
        }
        
        smr2.bones = smr2Bones;
        smr2.rootBone = smr2.bones[0];
    }

    public void RegisterBones(SkinnedMeshRenderer smr)
    {
        if (smr.bones == null)
        {
            Debug.LogError("无法读出smr.bones");
        }
        
        foreach (var bone in smr.bones)
        {
            if (bone != null)
            {
                boneDictionary[bone.name] = bone;
            }
        }
    }
    
    #endregion
    
    #region 口型同步

    public void SetupBlendShapeSynchronization()
    {
        var head = avatarData.head;
        parts.TryGetValue(AvatarGenericData.PART_HEAD, out SkinnedMeshRenderer headRenderer);
        
        if (!headRenderer)
        {
            Debug.LogError("未找到headRenderer");
            return;
        }
        
        if (head != null)
        {
            foreach (KeyValuePair<string, SkinnedMeshRenderer> kvp in parts)
            {
                if (kvp.Key != AvatarGenericData.PART_HEAD)
                {
                    var part = avatarData.GetPartDataByPartName(kvp.Key);
                    if (part.partRunTimeData.isHeadPart)
                    {
                        BlendShapeDuplicationManager.instance.Add
                            (headRenderer,
                                kvp.Value,
                                BlendShapeDuplicationManager.instance.AutoGenericBlendShapeMap(headRenderer,kvp.Value));
                    }
                }
            }
        }
    }
    
    private void CloseBlendShapeSynchronization()
    {
        var head = avatarData.head;
        parts.TryGetValue(AvatarGenericData.PART_HEAD, out SkinnedMeshRenderer headRenderer);
        if (headRenderer != null)
        {
            BlendShapeDuplicationManager.instance.Remove(headRenderer);
        }
    }
    
    #endregion
    
    #region 初始化动画

    private void BeforeSetupAnimator()
    {
        GetBoundsBeforeAnimatorInitialize();
    }

    public void SetupAnimator(RuntimeAnimatorController controller)
    {
        this.BeforeSetupAnimator();

        // if (controller == null)
        // {
        //     Debug.LogError("controller加载失败");
        //     return;
        // }
        // else
        // {
        //     foreach (var clip in controller.animationClips)
        //     {
        //         Debug.LogWarning(clip.name + "|" + clip.length + "|" + clip.isLooping + "|" +clip.averageDuration);
        //     }
        // }
       
        var body = avatarData.GetPartDataByPartName(AvatarGenericData.PART_BODY);
        if (body != null)
        {
            if (body.partRunTimeData.partObject == null)
            {
                VFDebug.LogError("partObject加载失败");
                return;
            }
            
            var animator = body.partRunTimeData.partObject?.GetComponent<Animator>();
            if (animator)
            {
                if (controller)animator.runtimeAnimatorController = controller;
                animator.applyRootMotion = false;
                //   Debug.LogWarning("controller加载成功。");
            }
            else
            {
              //  Debug.LogWarning("角色Animator未找到。");
                //临时兜底。原则上还是美术设置。
                body.partRunTimeData.partObject.AddComponent<Animator>();
                animator = body.partRunTimeData.partObject.GetComponent<Animator>();
                animator.applyRootMotion = false;
                if (controller)animator.runtimeAnimatorController = controller;
            }
        }
        else
        {
            Debug.LogError("没找到Body");
        }
        
        //4.7.2025给head上一个animator
        var head = avatarData.GetPartDataByPartName(AvatarGenericData.PART_HEAD);
        if (head != null)
        {
            if (head.partRunTimeData.partObject == null)
            {
                Debug.LogError("head没加载出来!");
                return;
            }
            
            head.partRunTimeData.partObject.AddComponent<Animator>();
        }
    }

    public void InitDialogAnim(bool isStandalone = false)
    {
        AnimationAvatarManager manager = gameObject.AddComponent<AnimationAvatarManager>();
        
        var body = avatarData.GetPartDataByPartName(AvatarGenericData.PART_BODY);
        if (body != null)
        {
            if (body.partRunTimeData.partObject == null)
            {
                Debug.LogError("partObject加载失败");
                return;
            }
            
            var animator = body.partRunTimeData.partObject?.GetComponent<Animator>();
            if (animator)
            {
                manager.animator = animator;
            }
        }
        else
        {
            Debug.LogError("没找到Body");
        }
        
        var head = avatarData.GetPartDataByPartName(AvatarGenericData.PART_HEAD);
        if (head != null)
        {
            if (head.partRunTimeData.partObject == null)
            {
                Debug.LogError("head没加载出来!");
                return;
            }
            
            var animator = head.partRunTimeData.partObject?.GetComponent<Animator>();
            if (animator)
            {
                manager.emotionAnimator = animator;
            }
            
            parts.TryGetValue(AvatarGenericData.PART_HEAD, out SkinnedMeshRenderer headRenderer);
            if (headRenderer != null)
            {
                manager.emotionSkinnedMeshRenderer = headRenderer;
            }
        }
        
        manager.SetHeadMask(AvatarMaskHelper.CreateHeadMask());
    //    manager.SetBodyMask(AvatarMaskHelper.CreateBodyMask());

        var handle = YooAssets.LoadAssetSync<AvatarAnimationDataObject>("DialogAnimationData");
        if (handle != null && handle.AssetObject != null)
        {
            manager.SetAnimDataObject(handle.AssetObject as AvatarAnimationDataObject);
        }
        else
        {
            Debug.LogError("加载失败>>AvatarDataObject");
        }

        if (!isStandalone)
        {
            manager.animTypeId = avatarData.isMale ? 1 : 0;
        }
        else
        {
            AnimationSystem.Cfg.T.TBStandAloneAvatars.DataMap.TryGetValue(id, out var v);
            if (v != null)
            {
                string s = v.avatarAnimType;
                manager.animTypeId = ClipResourceManager.Instance.AddClipType(s);
            }
            else
            {
                VFDebug.LogError($"{id}没有拿到动作id");
            }

            manager.animIsGeneric = true;
            this.parts.TryGetValue(AvatarGenericData.PART_BODY, out SkinnedMeshRenderer bodyRenderer);
            manager.bodySkinnedMeshRenderer = bodyRenderer;
        }
        
        manager.InitializePlayableGraph();

        DialogManager dmanager = gameObject.AddComponent<DialogManager>();
        dmanager.manager = manager;
    }
    
    #endregion

    #region 工程接口需求

    private Bounds _boundsTPose;

    private Bounds GetBoundsBeforeAnimatorInitialize()
    {
        if (_boundsTPose != default(Bounds))
        {
            return _boundsTPose;
        }
        else
        {
            Bounds bound = GenericUtils.GetBoundsIncludingChildren(this.gameObject);
            //bounds会随动作变化.
            //Debug.LogWarning(bound);
            _boundsTPose = bound;
            
            return _boundsTPose;
        }
    }

//Bounds不会自动刷新...[
    private Bounds GetBounds()
    {
        var animator = avatarData.body.partRunTimeData.partObject?.GetComponent<Animator>();
        if (animator != null)
        {
            GameObject tempCharacter = Instantiate(this.gameObject,this.gameObject.transform.parent);
            
            // **2. 获取 Animator，并切换到 Idle 动画**
            Animator tempAnimator = tempCharacter.GetComponentInChildren<Animator>();
            if (tempAnimator == null)
            {
                Debug.LogError("克隆对象缺少 Animator 组件！");
                Destroy(tempCharacter);
                return new Bounds(Vector3.zero, Vector3.zero);
            }
            
            tempAnimator.Play("Idle", 0, 0); // 播放 Idle 状态动画
            tempAnimator.Update(0); // 立即更新动画
            
            // **3. 计算包围盒**
            SkinnedMeshRenderer[] skinnedMeshes = tempCharacter.GetComponentsInChildren<SkinnedMeshRenderer>();
            if (skinnedMeshes.Length == 0)
            {
                Debug.LogWarning("未找到 SkinnedMeshRenderer 组件！");
                Destroy(tempCharacter);
                return new Bounds(Vector3.zero, Vector3.zero);
            }
            
            // 初始化包围盒
            Bounds totalBounds = skinnedMeshes[0].bounds;
            foreach (var skinnedMesh in skinnedMeshes)
            {
                totalBounds.Encapsulate(skinnedMesh.bounds);
            }
            
            // **4. 删除克隆对象，避免影响游戏**
            Destroy(tempCharacter);
        
            // Bounds bound = GenericUtils.GetBoundsIncludingChildren(this.gameObject);
             Debug.LogWarning(totalBounds);
            _boundsTPose = totalBounds;
        }
        else
        {
            
        }
        return _boundsTPose;
    }
    
    /// <summary>
    /// 获取角色的头顶高度(y)
    /// 此高度在角色创建时立刻初始化,之后不跟随角色骨骼动画运动变更.如果需要更新这个高度,请提需求...
    /// </summary>
    /// <returns></returns>
    public Vector3 GetHeight()
    {
        if (this._boundsTPose.max.y != 0)
        {
            return new Vector3(_boundsTPose.center.x,+_boundsTPose.max.y,_boundsTPose.center.z);
        }
        else
        {
            Debug.LogWarning("boundsTPose未初始化,返回默认值.");
            //兜底,如果莫名其妙初始化失败返回默认值.
            if (this.avatarData.isMale) 
                return new Vector3(
                    this.gameObject.transform.position.x,
                    this.gameObject.transform.position.y + 1.84f,
                    this.gameObject.transform.position.z);
            else return new Vector3(
                this.gameObject.transform.position.x,
                this.gameObject.transform.position.y + 1.74f,
                this.gameObject.transform.position.z);
        }
    }
    #endregion
    
    #region 兜底处理

    public void DOUDICHULI()
    {
        parts.TryGetValue(AvatarGenericData.PART_BODY, out SkinnedMeshRenderer bodyRenderer);
        if (bodyRenderer)
        {
            bodyRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
        }
    }
    
    #endregion
    
    public void Update()
    {
       // Debug.LogWarning(this.GetHeight());
        // GetBounds();
        //
        // SceneController sceneController = ControllerManager.instance.GetController(ModelConsts.Scene ) as SceneController;
        // var level = sceneController.scene as CenterHomeLevel;
        // if (level != null)
        // {
        //     var height = level.GetComponent<AvatarComponent>().GetHomePageChatAvatar().GetModelHeight();
        //     Debug.Log(height);
        // }
    }

    private static AssetHandle Load(string path)
    {
        return GAvatarResManager.instance.SyncLoad(path);
    }

    private static bool CheckPath(string path)
    {
        return GAvatarResManager.instance.CheckPath(path);
    }

    public void OnAvatarDestroy()
    {
        CloseBlendShapeSynchronization();
    }

    public void OnDestroy()
    {
        //兜底逻辑防止有人直接杀GO，但是不保证解决所有问题。请使用UnLoadNAvatar.
        this.OnAvatarDestroy();
    }
    
    //逻辑就到这了。再多，就要新写类型了，太复杂了。等什么时候确认要用这种模式了，把这里重构成接口与继承。
    #region 5.13.2025 有一种新的模型,走一体化加载逻辑。
    
    public GameObject head, body;
    
    public void LoadAvatar(string id)
    {
        AnimationSystem.Cfg.T.TBStandAloneAvatars.DataMap.TryGetValue(id, out var value);
        if (value != null)
        {
            
            GameObject transformAdjust = new GameObject();
            transformAdjust.name = $"TransformAdjustFor{id}";
            transformAdjust.transform.SetParent(this.transform);
            transformAdjust.transform.localPosition = new Vector3(value.avatarOffsetX,value.avatarOffsetY,value.avatarOffsetZ);
            transformAdjust.transform.localScale = new Vector3(value.avatarScale,value.avatarScale,value.avatarScale);
            transformAdjust.transform.localRotation = Quaternion.Euler(value.avatarRotation[0],value.avatarRotation[1],value.avatarRotation[2]);
            
            this.id = id;
            
            GameObject head = null, body = null;
            var handle = Load(value.headResPath);
            if (handle != null && handle.AssetObject && handle.IsDone)
            {
                head = handle.AssetObject as GameObject;
            }
            
            var handleBd = Load(value.bodyResPath);
            if (handleBd != null && handleBd.AssetObject && handleBd.IsDone)
            {
                body = handleBd.AssetObject as GameObject;
            }

            this.avatarData = new CartoonAvatarData();
            
            if (head)
            {
                this.head = head;
                head = Instantiate(head, transformAdjust.transform, false);
                head.SetActive(true);
                
                var headGo = head.transform.Find("Head"); //约定名称.
                if (headGo)
                {
                    var smr = headGo.GetComponent<SkinnedMeshRenderer>();
                    if (smr)
                    {
                        this.parts.TryAdd(AvatarGenericData.PART_HEAD, smr);
                        this.parts.TryAdd(AvatarGenericData.PART_ACC, smr);
                        this.parts.TryAdd(AvatarGenericData.PART_BEARD, smr);
                        this.parts.TryAdd(AvatarGenericData.PART_BROW, smr);
                    
                        this.avatarData.head.partRunTimeData = new PartRunTimeData(true, head);
                        this.avatarData.acc.partRunTimeData = new PartRunTimeData(true, head);
                        this.avatarData.beard.partRunTimeData = new PartRunTimeData(true, head);
                        this.avatarData.brow.partRunTimeData = new PartRunTimeData(true, head);
                    }
                    else
                    {
                        VFDebug.LogError($"{id}这个独立角色特么头上没SMR。后续大概率报错");
                    }
                }
                else
                {
                    VFDebug.LogError($"{id}这个独立角色没头(Head)啊");
                }
                
                var hairGo = head.transform.Find("Hair"); //约定名称.
                if (hairGo)
                {
                    var smr = hairGo.GetComponent<SkinnedMeshRenderer>();
                    if (smr)
                    {
                        this.parts.TryAdd(AvatarGenericData.PART_HAIR, smr);
                        
                        this.avatarData.hair.partRunTimeData = new PartRunTimeData(true, head);
                    }
                    else
                    {
                        VFDebug.LogError($"{id}这个独立角色特么头上没SMR。后续大概率报错");
                    }
                }
                else
                {
                    VFDebug.LogError($"{id}这个独立角色没头发(Hair)");
                }
            }
            else
            {
                VFDebug.LogError($"{id}这个独立角色特么没头(Prefab),查配表或资源");
            }

            if (body)
            {
                this.body = body;
                body = Instantiate(body, transformAdjust.transform, false);
                body.SetActive(true);
                
                var bodyGo = body.transform.Find("Body"); //约定名称.
                if (bodyGo)
                {
                    var smr = bodyGo.GetComponent<SkinnedMeshRenderer>();
                    if (smr)
                    {
                        this.parts.TryAdd(AvatarGenericData.PART_BODY, smr);
                        this.parts.TryAdd(AvatarGenericData.PART_PANTS, smr);
                        this.parts.TryAdd(AvatarGenericData.PART_TOPS, smr);
                        this.parts.TryAdd(AvatarGenericData.PART_SUIT, smr);
                        this.parts.TryAdd(AvatarGenericData.PART_STOCKINGS, smr);
                    
                        this.avatarData.body.partRunTimeData = new PartRunTimeData(false,body);
                        this.avatarData.pants.partRunTimeData = new PartRunTimeData(false,body);
                        this.avatarData.tops.partRunTimeData = new PartRunTimeData(false,body);
                        this.avatarData.suit.partRunTimeData = new PartRunTimeData(false,body);
                        this.avatarData.stockings.partRunTimeData = new PartRunTimeData(false,body);
                    }
                    else
                    {
                        VFDebug.LogError($"{id}这个独立角色特么身上没SMR。后续大概率报错");
                    }
                }
                else
                {
                    VFDebug.LogError($"{id}这个独立角色特么没身体(Body)。后续大概率报错");
                }
                
            }
            else
            {
                VFDebug.LogError($"{id}这个独立角色特么没身(Prefab),查配表或资源");
            }
        }
        else
        {
            VFDebug.LogError($"{id}这个角色读表梅毒到！");
        }
    }

    /// <summary>
    /// 结构都变了,不写兜底了。
    /// </summary>
    public void CheckAnimator()
    {
        if (this.body != null)
        {
            var animator = body.GetComponent<Animator>();
            if (animator)
            {
            }
            else
            {
                VFDebug.LogWarning("角色Animator未找到。");
                if (body != null)
                {
                    body.AddComponent<Animator>();
                    animator = body.GetComponent<Animator>();
                    animator.applyRootMotion = false;
                }
            }
        }
        else
        {
            Debug.LogError("没找到Body");
        }
        
        if (head != null)
        {
            var animator = head.GetComponent<Animator>();
            if (animator)
            {
            }
            else
            {
                VFDebug.LogWarning("角色头Animator未找到。");
                head.AddComponent<Animator>();
                animator = head.GetComponent<Animator>();
                animator.applyRootMotion = false;
            }
        }
    }
    
    /// <summary>
    /// 给新的角色类型同步骨骼,只需要将头挂载到正确的骨骼上去。
    /// </summary>
    public void SyncBoneForStandalone()
    {
        parts.TryGetValue(AvatarGenericData.PART_BODY, out SkinnedMeshRenderer bodyRenderer);

        if (!bodyRenderer)
        {
            Debug.LogError("未找到bodyRenderer");
            return;
        }
        
        RegisterBones(bodyRenderer);
        boneDictionary.TryGetValue(AvatarGenericData.HEAD_BONE_NAME,out Transform headBone);
        
        if (bodyRenderer)
        {
            
            
            foreach (KeyValuePair<string, SkinnedMeshRenderer> kvp in parts)
            {
                if (kvp.Key != AvatarGenericData.PART_BODY)
                {
                    var part = avatarData.GetPartDataByPartName(kvp.Key);
                    if (part.partRunTimeData.isHeadPart && headBone)
                    {
                        part.partRunTimeData.partObject.transform.SetParent(headBone);
                        continue;
                    }
                }
            }
        }
    }
    
    #endregion
}



public static class TransformExtension
{
    public static Transform RecursiveFind(this Transform t, string name)
    {
        if (t == null) return null;
        if (t.name == name) return t;
        for (int i = 0; i < t.childCount; i++)
        {  
            var child = RecursiveFind(t.GetChild(i), name);
            if (child != null) return child;
        }

        return null;
    }
}





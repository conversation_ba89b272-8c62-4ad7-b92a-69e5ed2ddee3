using System.Threading.Tasks;
using FairyGUI;
using FairyGUI.Utils;
using UnityEngine;
using YooAsset;

namespace UIBind.common
{
    public partial class LocalImageLoader : ExtendedComponent
    {
        private AssetHandle _handle;

        public async Task LoadExploreBg(string tag)
        {
            if (string.IsNullOrEmpty(tag))
            {
                VFDebug.LogError("没有背景图 tag");
                return;
            }
            await LoadImage(ResUtils.GetExploreBgImgPath(tag));
        }
        public async Task LoadAvatarHead(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                VFDebug.LogError("没有背景图 tag");
                return;
            }
            await LoadImage(ResUtils.GetAvatarHeadPath(name));
        }
        public async Task LoadAvatarBody(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                VFDebug.LogError("没有背景图 tag");
                return;
            }
            await LoadImage(ResUtils.GetAvatarBodyPath(name));
        }

        private async Task LoadImage(string path)
        {
            _handle?.Release();

            Debug.Log($"path {path} #tag");
            _handle = YooAssets.LoadAssetAsync<Texture2D>(path);
            await _handle.Task;

            if (_handle != null && _handle.Status == EOperationStatus.Succeed)
            {
                Texture2D texture = _handle.AssetObject as Texture2D;
                if (texture != null)
                {
                    ldrImage.texture = new NTexture(texture);
                }
            }
            else
            {
                VFDebug.LogError($"LocalImageLoader 图片加载失败: {path}");
            }
            _handle?.Release();
            _handle = null;
        }

        public Vector2 GetOriginalSize()
        {
            if (ldrImage.texture != null)
            {
                return ldrImage.texture.originalSize;
            }
            return Vector2.zero;
        }

        public void SetAsHeight(float targetHeight)
        { 
            var originSize = GetOriginalSize();
            SetSize(originSize.x * targetHeight / originSize.y, targetHeight);
        }

        public void SetAsWidth(float targetWidth)
        { 
            var originSize = GetOriginalSize();
            SetSize(targetWidth, originSize.y * targetWidth / originSize.x);
        }
    }
}
using System.Collections.Generic;
using UnityEngine;

namespace UIBind.ExploreFriends
{
    /// <summary>
    /// 弧形列表使用示例
    /// </summary>
    public class ArcListUsageExample
    {
        /// <summary>
        /// 示例：如何在其他地方使用弧形列表
        /// </summary>
        public void ExampleUsage()
        {
            // 获取UI实例
            var ui = UIManager.instance.GetUI<ExploreFriendsIntroducePanelUI>("ExploreFriendsIntroducePanelUI");
            if (ui == null) return;
            
            // 1. 准备数据
            List<ArcListItemData> dataList = new List<ArcListItemData>
            {
                new ArcListItemData(1, "角色A", "ui://package/icon1", "强力的战士", true),
                new ArcListItemData(2, "角色B", "ui://package/icon2", "敏捷的刺客", true),
                new ArcListItemData(3, "角色C", "ui://package/icon3", "智慧的法师", false), // 未解锁
                new ArcListItemData(4, "角色D", "ui://package/icon4", "神秘的牧师", true),
                new ArcListItemData(5, "角色E", "ui://package/icon5", "勇敢的骑士", true),
                new ArcListItemData(6, "角色F", "ui://package/icon6", "狡猾的盗贼", true),
                new ArcListItemData(7, "角色G", "ui://package/icon7", "强大的巫师", false), // 未解锁
                new ArcListItemData(8, "角色H", "ui://package/icon8", "忠诚的守卫", true),
                new ArcListItemData(9, "角色I", "ui://package/icon9", "灵活的游侠", true),
                new ArcListItemData(10, "角色J", "ui://package/icon10", "神圣的圣骑士", true)
            };
            
            // 2. 设置数据到列表
            ui.SetListData(dataList);
            
            // 3. 程序化选中某个item（可选）
            ui.SelectItemByIndex(2); // 选中第3个item
            
            // 4. 获取当前选中的数据
            var currentSelected = ui.GetCurrentSelectedData();
            if (currentSelected != null)
            {
                Debug.Log($"当前选中: {currentSelected.name}");
            }
        }
        
        /// <summary>
        /// 示例：监听选中事件的方法
        /// 注意：这需要在ExploreFriendsIntroducePanelUI中添加事件委托
        /// </summary>
        public void ExampleEventHandling()
        {
            var ui = UIManager.instance.GetUI<ExploreFriendsIntroducePanelUI>("ExploreFriendsIntroducePanelUI");
            if (ui == null) return;
            
            // 如果在ExploreFriendsIntroducePanelUI中添加了事件委托，可以这样监听：
            // ui.OnItemSelectedEvent += OnItemSelected;
        }
        
        /// <summary>
        /// 示例事件处理方法
        /// </summary>
        /// <param name="selectedData">选中的数据</param>
        /// <param name="selectedIndex">选中的索引</param>
        private void OnItemSelected(ArcListItemData selectedData, int selectedIndex)
        {
            Debug.Log($"选中了: {selectedData.name}, 索引: {selectedIndex}");
            
            // 根据选中的数据执行相应逻辑
            if (!selectedData.isUnlocked)
            {
                // 显示解锁提示
                Debug.Log("该角色尚未解锁！");
                return;
            }
            
            // 执行选中后的逻辑
            // 例如：播放音效、更新其他UI、保存选择等
        }
        
        /// <summary>
        /// 示例：动态更新数据
        /// </summary>
        public void ExampleUpdateData()
        {
            var ui = UIManager.instance.GetUI<ExploreFriendsIntroducePanelUI>("ExploreFriendsIntroducePanelUI");
            if (ui == null) return;
            
            // 获取当前数据
            var currentData = ui.GetCurrentSelectedData();
            if (currentData != null)
            {
                // 解锁某个角色
                currentData.isUnlocked = true;
                
                // 重新设置数据以刷新显示
                // 注意：这里需要重新获取完整的数据列表
                // ui.SetListData(updatedDataList);
            }
        }
    }
}

/*
使用步骤总结：

1. 在FairyGUI编辑器中设置好列表UI
   - 确保GList设置为横向布局
   - 设置合适的item模板

2. 准备数据
   - 创建ArcListItemData列表
   - 设置每个item的属性

3. 调用SetListData设置数据

4. 可选：使用SelectItemByIndex程序化选中

5. 可选：监听选中事件进行后续处理

注意事项：
- 确保item数量 > 8 以获得最佳效果
- 根据实际item UI结构调整RenderListItem方法
- 可以自定义_itemScales和_itemYOffsets来调整弧形效果
- 动画过程中会阻止交互，避免冲突
*/

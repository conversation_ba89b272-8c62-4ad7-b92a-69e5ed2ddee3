﻿using System;
using CommonUI;
using Msg.basic;
using Msg.dialog_task;
using Msg.explore;
using Msg.task_process;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.ChatLogicNew.ChatState;
using ScriptsHot.Game.Modules.Explore.State.Base;
using ScriptsHot.Game.Modules.Procedure;
using ScriptsHot.Game.Modules.Task;
using UIBind.Explore.Item;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Explore.ExploreType.Base
{
    public class ExploreEntityBase:ComponentObject,IComponetOwner
    {
        protected ExploreStateMachine _stateMachine;
        
        protected ExploreParam _param;
        public ExploreParam CurInfo => _param;
        
        protected ExploreController _controller;
        

        /// <summary>
        /// 当前正在播放的TTS 
        /// </summary>
        public ulong CurTtsAudioId = 0;

        /// <summary>
        /// 当前实体ID
        /// </summary>
        public long EntityId = 0;

        /// <summary>
        /// 一个实体 可以生成不同的对话
        /// </summary>
        public long DialogId = 0;

        private int _round = 0;

        /// <summary>
        /// 当前实体对话轮次
        /// </summary>
        public int Round
        {
            get { return _round;}
            set
            {
                _round = value;
            }
        }

        /// <summary>
        /// 当前实体 阶段
        /// </summary>
        public ExploreStep Step = ExploreStep.Normal;
        
        public PB_DialogMode DialogMode = PB_DialogMode.MNone;
        
        
        protected ExploreCellBase _ui;
        public ExploreCellBase UI => _ui;
        
        private PB_DialogTaskPreloadData _initData;
        public PB_DialogTaskPreloadData InitData => _initData;
        
        public ExploreEntityBase()
        {
            InitStateMachine();
        }

        public virtual void Init(ExploreParam param,PB_DialogTaskPreloadData data,ExploreCellBase ui)
        {
            _param = param;
            _initData = data;
            _ui = ui;
            EntityId = data.taskId;
            DialogMode = data.dialogMode;
            this._controller = ControllerManager.instance.GetController(ModelConsts.Explore) as ExploreController;
        }
        public virtual void Enter()
        {
            EntityId = _initData.taskId;
            DialogMode = _initData.dialogMode;
            _controller.CurNetStrategy.ClearASRWithCancel();
            Step = ExploreStep.Enter;
        }
        
        // 清理数据
        public virtual void Clear()
        {
            Round = 0;
            CurTtsAudioId = 0;
            EntityId = 0;
            DialogId = 0;
            Step = ExploreStep.Normal;
            DialogMode = PB_DialogMode.MNone;
        }
        //退出 
        public virtual void Exit()
        {
            UnEvent();
            Clear();
            Step = ExploreStep.Exit;
        }
        
        public virtual void AddEvent()
        {
        }

        public virtual void UnEvent()
        {
        }

        protected virtual void InitStateMachine()
        {
            _stateMachine = new ExploreStateMachine(this);
        }
        
        public virtual void Update(int interval)
        {
            if (Step == ExploreStep.Normal || Step == ExploreStep.Exit) return;
            _stateMachine?.Update(interval);
            this.UpdateComponents(interval);
        }
        
        public void ChangeState(string stateName,params object[] args)
        {
            VFDebug.Log("Explore State:::" +  stateName);
            _stateMachine.ChangeState(stateName,args);
        }

        public ExploreStateBase GetCurState()
        {
            return this._stateMachine.currState != null? this._stateMachine.currState as ExploreStateBase: null;
        }
        
        public virtual void OnTopLeftBack()
        {
        }
        
        
        /// <summary>
        /// 显示 loading ui
        /// </summary>
        public void AddLoadingBubble()
        {
            Notifier.instance.SendNotification(NotifyConsts.AddLoadingCell);
        }

        /// <summary>
        /// 移除 loading ui
        /// </summary>
        public void RemoveLoadingBubble()
        {
            Notifier.instance.SendNotification(NotifyConsts.RemoveLoadingCell);
        }

        public virtual void OnNewWorldAudioStart()
        {
        }


        protected virtual void Dispose()
        {
        }
    }


    public class ExploreParam
    {
        public ExploreEntityBase Entity;
        public ExploreCellBase UI;
        public PB_DialogMode ChatMode = PB_DialogMode.MNone;
    }
    
}
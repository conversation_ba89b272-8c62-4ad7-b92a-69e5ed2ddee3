/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Shop
{
    public partial class SpeakPlanPromotionStep1Panel : UIBindT
    {
        public override string pkgName => "Shop";
        public override string comName => "SpeakPlanPromotionStep1Panel";

        public GButton maskBox;
        public GImage imgBG;
        public SpeakPlanPromotionContent comPlanPromotionContent;
        public GButton btnExit;
        public GGroup grpHeader;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            maskBox = (GButton)com.GetChildAt(0);
            imgBG = (GImage)com.GetChildAt(1);
            comPlanPromotionContent = new SpeakPlanPromotionContent();
            comPlanPromotionContent.Construct(com.GetChildAt(2).asCom);
            btnExit = (GButton)com.GetChildAt(3);
            grpHeader = (GGroup)com.GetChildAt(4);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            maskBox = null;
            imgBG = null;
            comPlanPromotionContent.Dispose();
            comPlanPromotionContent = null;
            btnExit = null;
            grpHeader = null;
        }
    }
}
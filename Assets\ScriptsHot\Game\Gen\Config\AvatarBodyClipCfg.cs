
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;



public sealed partial class AvatarBodyClipCfg : Luban.BeanBase
{
    public AvatarBodyClipCfg(ByteBuf _buf) 
    {
        clipKey = _buf.ReadString();
        clipName = _buf.ReadString();
        canBlend = _buf.ReadBool();
        blendIn = _buf.ReadFloat();
        blendOut = _buf.ReadFloat();
        canLoop = _buf.ReadBool();
        loopMax = _buf.ReadFloat();
        loopBlendTime = _buf.ReadFloat();
        canChangeSpeed = _buf.ReadBool();
        speedRangeMin = _buf.ReadFloat();
        speedRangeMax = _buf.ReadFloat();
        footIK = _buf.ReadBool();
        ModifierI = _buf.ReadBool();
    }

    public static AvatarBodyClipCfg DeserializeAvatarBodyClipCfg(ByteBuf _buf)
    {
        return new AvatarBodyClipCfg(_buf);
    }

    /// <summary>
    /// body动画的key
    /// </summary>
    public readonly string clipKey;
    /// <summary>
    /// 动画Clip的名字<br/>加载资源使用<br/>Unity中的命名规范<br/>男性Boy* 女性Girl*
    /// </summary>
    public readonly string clipName;
    /// <summary>
    /// 是否支持融合
    /// </summary>
    public readonly bool canBlend;
    /// <summary>
    /// 融合进入的最晚百分比
    /// </summary>
    public readonly float blendIn;
    /// <summary>
    /// 融合退出的最早百分比
    /// </summary>
    public readonly float blendOut;
    /// <summary>
    /// 是否支持循环
    /// </summary>
    public readonly bool canLoop;
    /// <summary>
    /// 该动画最多循环的遍数<br/>min:1<br/>max:10000
    /// </summary>
    public readonly float loopMax;
    /// <summary>
    /// 循环强制向后融合的时间
    /// </summary>
    public readonly float loopBlendTime;
    /// <summary>
    /// 该动画是否支持变速
    /// </summary>
    public readonly bool canChangeSpeed;
    /// <summary>
    /// 动画最小速度(倍数)
    /// </summary>
    public readonly float speedRangeMin;
    /// <summary>
    /// 动画最大速度(倍数)
    /// </summary>
    public readonly float speedRangeMax;
    /// <summary>
    /// 是否开启footIK
    /// </summary>
    public readonly bool footIK;
    /// <summary>
    /// 临时特征:<br/>标记为 TRUE<br/>表示此动作组<br/>在播放最后一段时允许<br/>直接跳过,仅探索生效
    /// </summary>
    public readonly bool ModifierI;
   
    public const int __ID__ = -1891529799;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
        
        
        
        
        
        
        
        
        
        
        
        
        
    }

    public override string ToString()
    {
        return "{ "
        + "clipKey:" + clipKey + ","
        + "clipName:" + clipName + ","
        + "canBlend:" + canBlend + ","
        + "blendIn:" + blendIn + ","
        + "blendOut:" + blendOut + ","
        + "canLoop:" + canLoop + ","
        + "loopMax:" + loopMax + ","
        + "loopBlendTime:" + loopBlendTime + ","
        + "canChangeSpeed:" + canChangeSpeed + ","
        + "speedRangeMin:" + speedRangeMin + ","
        + "speedRangeMax:" + speedRangeMax + ","
        + "footIK:" + footIK + ","
        + "ModifierI:" + ModifierI + ","
        + "}";
    }
}



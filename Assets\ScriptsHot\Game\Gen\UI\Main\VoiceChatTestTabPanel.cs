/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Main
{
    public partial class VoiceChatTestTabPanel : UIBindT
    {
        public override string pkgName => "Main";
        public override string comName => "VoiceChatTestTabPanel";

        public Controller chatState;
        public GImage imgBG;
        public GLoader comLoader3D;
        public GButton EngineBtn;
        public GButton EnterBtn;
        public GButton ExitBtn;
        public GButton PreMatchBtn;
        public GButton CancelPreMatchBtn;
        public GButton ExitChannel;
        public VoiceChatMatchWindw matchWindow;
        public MicVolBtn micVolBtn;
        public GButton exitChattingBtn;
        public GGroup ChattingHeaderGrp;
        public GImage avatarBoxBg;
        public GTextField avatarTf;
        public GTextField avatarTransTf;
        public GImage avatarBoxBg_2;
        public GImage avatarBoxBg_3;
        public GImage ExBoxBg;
        public GTextField exempleLabel;
        public GTextField exempleTf;
        public GTextField userTf;
        public GGroup ChattingTextGrp;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            chatState = com.GetControllerAt(0);
            imgBG = (GImage)com.GetChildAt(1);
            comLoader3D = (GLoader)com.GetChildAt(2);
            EngineBtn = (GButton)com.GetChildAt(3);
            EnterBtn = (GButton)com.GetChildAt(4);
            ExitBtn = (GButton)com.GetChildAt(5);
            PreMatchBtn = (GButton)com.GetChildAt(6);
            CancelPreMatchBtn = (GButton)com.GetChildAt(7);
            ExitChannel = (GButton)com.GetChildAt(8);
            matchWindow = new VoiceChatMatchWindw();
            matchWindow.Construct(com.GetChildAt(9).asCom);
            micVolBtn = new MicVolBtn();
            micVolBtn.Construct(com.GetChildAt(12).asCom);
            exitChattingBtn = (GButton)com.GetChildAt(13);
            ChattingHeaderGrp = (GGroup)com.GetChildAt(14);
            avatarBoxBg = (GImage)com.GetChildAt(15);
            avatarTf = (GTextField)com.GetChildAt(16);
            avatarTransTf = (GTextField)com.GetChildAt(17);
            avatarBoxBg_2 = (GImage)com.GetChildAt(18);
            avatarBoxBg_3 = (GImage)com.GetChildAt(19);
            ExBoxBg = (GImage)com.GetChildAt(20);
            exempleLabel = (GTextField)com.GetChildAt(21);
            exempleTf = (GTextField)com.GetChildAt(22);
            userTf = (GTextField)com.GetChildAt(23);
            ChattingTextGrp = (GGroup)com.GetChildAt(24);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            chatState = null;
            imgBG = null;
            comLoader3D = null;
            EngineBtn = null;
            EnterBtn = null;
            ExitBtn = null;
            PreMatchBtn = null;
            CancelPreMatchBtn = null;
            ExitChannel = null;
            matchWindow.Dispose();
            matchWindow = null;
            micVolBtn.Dispose();
            micVolBtn = null;
            exitChattingBtn = null;
            ChattingHeaderGrp = null;
            avatarBoxBg = null;
            avatarTf = null;
            avatarTransTf = null;
            avatarBoxBg_2 = null;
            avatarBoxBg_3 = null;
            ExBoxBg = null;
            exempleLabel = null;
            exempleTf = null;
            userTf = null;
            ChattingTextGrp = null;
        }
    }
}
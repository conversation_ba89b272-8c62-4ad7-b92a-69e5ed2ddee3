
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


public partial class Tables
{
    public TbScene TbScene {get; }
    public TbBuilding TbBuilding {get; }
    public TBLanguage TBLanguage {get; }
    public TbConst TbConst {get; }
    public TbGm TbGm {get; }
    public TbSound TbSound {get; }
    public TBSceneJumpPoint TBSceneJumpPoint {get; }
    public TBOnBoardRoles TBOnBoardRoles {get; }
    public TBShopItems TBShopItems {get; }
    public TBMemberTyps TBMemberTyps {get; }
    public TBOnBoardingGoals TBOnBoardingGoals {get; }
    public TBOnBoardingHabits TBOnBoardingHabits {get; }
    public TBOnBoardingLevels TBOnBoardingLevels {get; }
    public TBAvatarNode TBAvatarNode {get; }
    public TBavatarEditorPreset TBavatarEditorPreset {get; }
    public TBbodyRes TBbodyRes {get; }
    public TBheadRes TBheadRes {get; }
    public TBValueBarComponent TBValueBarComponent {get; }
    public TBNodeModifierUnit TBNodeModifierUnit {get; }
    public TBavatarEditorPresetGroup TBavatarEditorPresetGroup {get; }
    public TBColorBarComponent TBColorBarComponent {get; }
    public TBRankItems TBRankItems {get; }
    public TBAvatars TBAvatars {get; }
    public TBRoleInfo TBRoleInfo {get; }
    public TbMapPart TbMapPart {get; }
    public TbDramaAvatarSound TbDramaAvatarSound {get; }
    public TBChatBG TBChatBG {get; }
    public TBItemTable TBItemTable {get; }
    public TBEmojiIcon TBEmojiIcon {get; }
    public TBRedDot TBRedDot {get; }
    public TBAvatarActionSet TBAvatarActionSet {get; }
    public TBAvatarBodyAction TBAvatarBodyAction {get; }
    public TBAvatarBodyClip TBAvatarBodyClip {get; }
    public TBAvatarSemanticMap TBAvatarSemanticMap {get; }
    public TBAvatarDialogGenericDefine TBAvatarDialogGenericDefine {get; }
    public TBExploreBG TBExploreBG {get; }
    public TBMusic TBMusic {get; }
    public TBStandAloneAvatars TBStandAloneAvatars {get; }
    public TBBgLights TBBgLights {get; }
    public TBBgLightTypes TBBgLightTypes {get; }
    public TBOnBoardingAge TBOnBoardingAge {get; }
    public TBOnBoardingGender TBOnBoardingGender {get; }
    public TBOnBoardingHobbies TBOnBoardingHobbies {get; }
    public TBOnBoardingTime TBOnBoardingTime {get; }
    public TBExploreFriend TBExploreFriend {get; }

    public Tables(System.Func<string, ByteBuf> loader)
    {
        TbScene = new TbScene(loader("tbscene"));
        TbBuilding = new TbBuilding(loader("tbbuilding"));
        TBLanguage = new TBLanguage(loader("tblanguage"));
        TbConst = new TbConst(loader("tbconst"));
        TbGm = new TbGm(loader("tbgm"));
        TbSound = new TbSound(loader("tbsound"));
        TBSceneJumpPoint = new TBSceneJumpPoint(loader("tbscenejumppoint"));
        TBOnBoardRoles = new TBOnBoardRoles(loader("tbonboardroles"));
        TBShopItems = new TBShopItems(loader("tbshopitems"));
        TBMemberTyps = new TBMemberTyps(loader("tbmembertyps"));
        TBOnBoardingGoals = new TBOnBoardingGoals(loader("tbonboardinggoals"));
        TBOnBoardingHabits = new TBOnBoardingHabits(loader("tbonboardinghabits"));
        TBOnBoardingLevels = new TBOnBoardingLevels(loader("tbonboardinglevels"));
        TBAvatarNode = new TBAvatarNode(loader("tbavatarnode"));
        TBavatarEditorPreset = new TBavatarEditorPreset(loader("tbavatareditorpreset"));
        TBbodyRes = new TBbodyRes(loader("tbbodyres"));
        TBheadRes = new TBheadRes(loader("tbheadres"));
        TBValueBarComponent = new TBValueBarComponent(loader("tbvaluebarcomponent"));
        TBNodeModifierUnit = new TBNodeModifierUnit(loader("tbnodemodifierunit"));
        TBavatarEditorPresetGroup = new TBavatarEditorPresetGroup(loader("tbavatareditorpresetgroup"));
        TBColorBarComponent = new TBColorBarComponent(loader("tbcolorbarcomponent"));
        TBRankItems = new TBRankItems(loader("tbrankitems"));
        TBAvatars = new TBAvatars(loader("tbavatars"));
        TBRoleInfo = new TBRoleInfo(loader("tbroleinfo"));
        TbMapPart = new TbMapPart(loader("tbmappart"));
        TbDramaAvatarSound = new TbDramaAvatarSound(loader("tbdramaavatarsound"));
        TBChatBG = new TBChatBG(loader("tbchatbg"));
        TBItemTable = new TBItemTable(loader("tbitemtable"));
        TBEmojiIcon = new TBEmojiIcon(loader("tbemojiicon"));
        TBRedDot = new TBRedDot(loader("tbreddot"));
        TBAvatarActionSet = new TBAvatarActionSet(loader("tbavataractionset"));
        TBAvatarBodyAction = new TBAvatarBodyAction(loader("tbavatarbodyaction"));
        TBAvatarBodyClip = new TBAvatarBodyClip(loader("tbavatarbodyclip"));
        TBAvatarSemanticMap = new TBAvatarSemanticMap(loader("tbavatarsemanticmap"));
        TBAvatarDialogGenericDefine = new TBAvatarDialogGenericDefine(loader("tbavatardialoggenericdefine"));
        TBExploreBG = new TBExploreBG(loader("tbexplorebg"));
        TBMusic = new TBMusic(loader("tbmusic"));
        TBStandAloneAvatars = new TBStandAloneAvatars(loader("tbstandaloneavatars"));
        TBBgLights = new TBBgLights(loader("tbbglights"));
        TBBgLightTypes = new TBBgLightTypes(loader("tbbglighttypes"));
        TBOnBoardingAge = new TBOnBoardingAge(loader("tbonboardingage"));
        TBOnBoardingGender = new TBOnBoardingGender(loader("tbonboardinggender"));
        TBOnBoardingHobbies = new TBOnBoardingHobbies(loader("tbonboardinghobbies"));
        TBOnBoardingTime = new TBOnBoardingTime(loader("tbonboardingtime"));
        TBExploreFriend = new TBExploreFriend(loader("tbexplorefriend"));
        ResolveRef();
    }
    
    private void ResolveRef()
    {
        TbScene.ResolveRef(this);
        TbBuilding.ResolveRef(this);
        TBLanguage.ResolveRef(this);
        TbConst.ResolveRef(this);
        TbGm.ResolveRef(this);
        TbSound.ResolveRef(this);
        TBSceneJumpPoint.ResolveRef(this);
        TBOnBoardRoles.ResolveRef(this);
        TBShopItems.ResolveRef(this);
        TBMemberTyps.ResolveRef(this);
        TBOnBoardingGoals.ResolveRef(this);
        TBOnBoardingHabits.ResolveRef(this);
        TBOnBoardingLevels.ResolveRef(this);
        TBAvatarNode.ResolveRef(this);
        TBavatarEditorPreset.ResolveRef(this);
        TBbodyRes.ResolveRef(this);
        TBheadRes.ResolveRef(this);
        TBValueBarComponent.ResolveRef(this);
        TBNodeModifierUnit.ResolveRef(this);
        TBavatarEditorPresetGroup.ResolveRef(this);
        TBColorBarComponent.ResolveRef(this);
        TBRankItems.ResolveRef(this);
        TBAvatars.ResolveRef(this);
        TBRoleInfo.ResolveRef(this);
        TbMapPart.ResolveRef(this);
        TbDramaAvatarSound.ResolveRef(this);
        TBChatBG.ResolveRef(this);
        TBItemTable.ResolveRef(this);
        TBEmojiIcon.ResolveRef(this);
        TBRedDot.ResolveRef(this);
        TBAvatarActionSet.ResolveRef(this);
        TBAvatarBodyAction.ResolveRef(this);
        TBAvatarBodyClip.ResolveRef(this);
        TBAvatarSemanticMap.ResolveRef(this);
        TBAvatarDialogGenericDefine.ResolveRef(this);
        TBExploreBG.ResolveRef(this);
        TBMusic.ResolveRef(this);
        TBStandAloneAvatars.ResolveRef(this);
        TBBgLights.ResolveRef(this);
        TBBgLightTypes.ResolveRef(this);
        TBOnBoardingAge.ResolveRef(this);
        TBOnBoardingGender.ResolveRef(this);
        TBOnBoardingHobbies.ResolveRef(this);
        TBOnBoardingTime.ResolveRef(this);
        TBExploreFriend.ResolveRef(this);
    }
}



﻿using System.Collections.Generic;
using System.Linq;
using Msg.economic;
using Msg.incentive;

public class StaminaModel : BaseModel
{
    public StaminaModel() : base(ModelConsts.Stamina) { }
    
    public PB_UserStaminaData staminaData { get; private set; }

    public int maxStamina
    {
        get
        {
            if (_testMaxStamina > 0)
                return _testMaxStamina;
            else
                return (int)staminaData?.max_stamina;
        }
    }

    private int _testMaxStamina;

    public void SetStaminaData(PB_UserStaminaData data)
    {
        staminaData = data;
        SendNotification(NotifyConsts.ChatStartStaminaRefreshEvent);
    }
    
    public void SetStamina(long value)
    {
        staminaData.stamina = value;
    }

    public void SetMaxStamina(int stamina)
    {
        _testMaxStamina = stamina;
    }
}
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class IntroduceItem : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "IntroduceItem";

        public GTextField ContentTxt1;
        public GTextField ContentTxt2;
        public GGroup Node;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ContentTxt1 = (GTextField)com.GetChildAt(1);
            ContentTxt2 = (GTextField)com.GetChildAt(2);
            Node = (GGroup)com.GetChildAt(3);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ContentTxt1 = null;
            ContentTxt2 = null;
            Node = null;
        }
    }
}
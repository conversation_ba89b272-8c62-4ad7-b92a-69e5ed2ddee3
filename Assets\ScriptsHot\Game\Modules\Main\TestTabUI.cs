using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using CommonUI;
using FairyGUI;
using UIBind.Main;
using ScriptsHot.Game.Modules.AgoraRtc;
using ScriptsHot.Game.Modules.Scene.Level;

//FGUI 主状态：ChatState
//FGUI 主状态：
public class TestTabUI : BaseUI<VoiceChatTestTabPanel>
{
    private enum FGUI_Chat_State {
        notChatting,
        chatting,
    }


    private enum FGUI_MatchWindow_State
    {
        start,
        cancel,
        retry,
        confirming
    }

    public TestTabUI(string name) : base(name) { }

    public override string uiLayer => UILayerConsts.Home; //主UI层 
    protected override bool isFullScreen => true;

    private int cdTime = 120;//单位s
    private int currCD = 120;//单位s
    private string cdTimerKey = string.Empty;//单位s

    private VoiceChatManager chatMngr;

    bool cancelActionLocker = false;

    private DemoAvatar _avatar3d;
    protected override void OnInit(GComponent uiCom)
    {
        chatMngr = VoiceChatManager.instance;

        
        #region testBtn region
        //AddUIEvent(this.ui.EngineBtn.onClick, OnInitEngine);
        //AddUIEvent(this.ui.EnterBtn.onClick, OnEnterChatChannel);
        //AddUIEvent(this.ui.ExitBtn.onClick, OnExitChatChannel);

        //match with backend
        AddUIEvent(this.ui.PreMatchBtn.onClick, OnStartPreMatch);

        AddUIEvent(this.ui.CancelPreMatchBtn.onClick, OnCancelPreMatch);
        AddUIEvent(this.ui.ExitChannel.onClick, OnSelfExitChannel);

        #endregion

        this.ui.chatState.selectedPage = FGUI_Chat_State.notChatting.ToString();
        this.ui.matchWindow.preMatchState.selectedIndex = 0;//start 页面

        AddUIEvent(this.ui.matchWindow.startBtn.onClick, OnStartPreMatch);
        AddUIEvent(this.ui.matchWindow.cancelBtn.onClick, OnCancelPreMatch);

        AddUIEvent(this.ui.matchWindow.retryBtn.onClick, OnStartPreMatch);//超时类事件触发后 切换retry
        AddUIEvent(this.ui.exitChattingBtn.onClick, OnSelfExitChannel);//超时类事件触发后 切换retry

        AddUIEvent(this.ui.micVolBtn.com.onClick,OnConfigMicVol);
  


        AddNotify();
        AddAvatar();
    }

    private void AddNotify()
    {
        //chat 状态类的切换通知
        Notifier.instance.RegisterNotification(NotifyConsts.VoiceChat_CancelPreMatchComplete, OnCancelPreMatchComplete);
        Notifier.instance.RegisterNotification(NotifyConsts.VoiceChat_StartPreMatchConfirming, OnPreMatchConfirming);
        Notifier.instance.RegisterNotification(NotifyConsts.VoiceChat_ChattingBothJoin, OnMatchSuccAndChattingBothJoin);
        Notifier.instance.RegisterNotification(NotifyConsts.VoiceChat_RecvOtherEndExitChannel, OnRecvOtherEndExitChannel);
        Notifier.instance.RegisterNotification(NotifyConsts.VoiceChat_ExitChannel, OnExitChannel);
        

        //chat过程中的文本消息
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_UserDialog, OnP2PUserDialog);
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_UserDialog2, OnP2PUserDialog2);//我的识别中

        Notifier.instance.RegisterNotification(NotifyConsts.P2P_OtherDialog, OnP2POtherDialog);
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_OtherDialog2, OnP2POtherDialog2);//对端的识别中
         
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_OtherDialogTranslate, OnP2POtherDialogTranslate);
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_DialogExample, OnP2PDialogExample);
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_DialogExampleTranslate, OnP2PDialogExampleTranslate);
        
        Notifier.instance.RegisterNotification(NotifyConsts.MultiTabShow,TryCancelMatchOnTabShow);
    }

    private void AddAvatar()
    {        
        _avatar3d = new DemoAvatar();
        _avatar3d.SetLoader(this.ui.comLoader3D); //外部只需要一个 comLoader3D
        _avatar3d.SetVisible(false);
        
    }

    private GameObject myAvatarModelGameObj;
    private async void ShowModel3d()
    {
        //PB_OnboardingChatPreloadData data = _flowController.Model.PreloadInfo.preloadData;
        
        _avatar3d.SetVisible(true);
        
        var level = ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene).scene;
        
        //,2982119962383892482,Julie Lefevre,10072,205,2
    
        //外部需要一个  几个数据 ，level，1，avatarID，bgID
        myAvatarModelGameObj= await  _avatar3d.SetModel( level ,1,2982119962383892482,"cafe");

        _avatar3d.UpdateAudioSource();
        _avatar3d.StartAudio();
    }

    private void HideModel3d()
    {
        ScriptsHot.Game.Modules.Explore.UI.ModelPool.ReleaseModel(2982119962383892482, myAvatarModelGameObj);
        _avatar3d.SetVisible(false);
        _avatar3d.StopAudio();
    }

    //private void OnInitEngine()
    //{

    //    Debug.Log("OnInitEngine");
    //    string appID = "f645bd50539a474db622874ede6e3d5c";
    //    RtcEngineManager.Instance.Initialize(appID);

    //}

    //private void OnEnterChatChannel()
    //{
    //    Debug.Log("OnEnterChatChannel");
    //    string token = "007eJxTYBBc8+Gl/bUyLsX5l28k9snMvDXD6NjdGQqf8+RtNE+8ED6mwJBmZmKalGJqYGpsmWhibpKSZGZkZGFukpqSapZqnGKa7O9hnNEQyMhwLX8iMyMDBIL4nAy5lckZiXl5qTkMDAA/wCIv";
    //    string channelName  = "mychannel";

    //    int ret=RtcEngineManager.Instance.JoinChannel(token, channelName);
    //    if (ret < 0) {
    //        Debug.LogError("JoinChannel failed, ret="+ret );
    //    }
    //}

    //private void OnExitChatChannel()
    //{
    //    Debug.Log("OnExitChatChannel");
    //    RtcEngineManager.Instance.LeaveChannel();
    //}

    #region 状态切换类的 行为与响应
    //用户点击
    private void OnStartPreMatch()
    {
        var ret= chatMngr.StartPreMatch();
        if (ret) {
            this.ui.matchWindow.preMatchState.selectedPage = FGUI_MatchWindow_State.cancel.ToString();// "cancel";//切到 匹配中可以 cancel的状态
            //todo-tanglei startTimer 倒计时
            currCD = this.cdTime;

            this.ui.matchWindow.countDownTf.text = currCD.ToString()+"s";

            this.GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage).HideTabs(TabIndex.Test,false,false);//关闭底tab, 且不显示icon


            cdTimerKey = TimerManager.instance.RegisterTimer(counter => {
                currCD--;
                this.ui.matchWindow.countDownTf.text = currCD.ToString() + "s";//刷新时间信息
                if (currCD ==0 ||counter == 0)//这时的currCD也应该是0
                {
                    Debug.Log("currCD ="+currCD + " counter="+ counter);
                    //客户端自己超时，强制cancel，这时服务器也应该超时了
                    chatMngr.CancelPreMatch();
                }
            },1000, this.cdTime);
        }
    }



    //用户点击,非瞬间切换
    private void OnCancelPreMatch()
    {
        if (!cancelActionLocker)
        {
            cancelActionLocker = true;
            chatMngr.CancelPreMatch();

        }
        else
        {
            Debug.LogWarning("上一个cancel的处理尚未完毕");
            //todo-tanglei 
            //增加timeout机制保证 过期后还能再次触发
        }

    }

    //出现tab
    private void TryCancelMatchOnTabShow(string name, object body )
    {
        //
        OnCancelPreMatch();
    }

    //系统事件
    private void OnCancelPreMatchComplete(string name, object body )
    {
        cancelActionLocker = false;
        this.ui.chatState.selectedPage = FGUI_Chat_State.notChatting.ToString(); //"notChatting";
        this.ui.matchWindow.preMatchState.selectedPage = FGUI_MatchWindow_State.start.ToString();// "start";//切到 匹配中可以 cancel的状态

        if (!string.IsNullOrEmpty(cdTimerKey))
        {
            TimerManager.instance.UnRegisterTimer(cdTimerKey);
            cdTimerKey = string.Empty;
        }
        
        //todo 可能有时间缝隙 如果严格这里应该增加 是否已经展示了 showTabsBtn的逻辑
        this.GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage).ShowTabs();//展开底tab
    }

    //系统事件
    private void OnPreMatchConfirming(string name, object body)
    {

        this.ui.matchWindow.preMatchState.selectedPage = FGUI_MatchWindow_State.confirming.ToString();// "confirming";//切到 匹配中可以 cancel的状态
        //禁止切换
        
        //此时倒计时，防止后续的 join失败 
    }

    //系统事件
    private void OnMatchSuccAndChattingBothJoin(string name, object body)
    {
        Debug.Log("OnMatchSuccAndChattingBothJoin");
        this.ui.chatState.selectedPage = FGUI_Chat_State.chatting.ToString();// "chatting";
        
        //重置mic状态和文本状态
        this.ui.micVolBtn.micState.selectedPage = "On";
        chatMngr.ConfigMicVol(true);

        this.ui.avatarTf.text = string.Empty;
        this.ui.userTf.text = string.Empty;
        this.ui.exempleTf.text = string.Empty;
        
        this.ShowModel3d();
      
        //this.ui.matchWindow.preMatchState.selectedPage = "start";//可有可无。因为 matchwindow已经隐藏
    }

    //用户点击
    private void OnSelfExitChannel()
    {
        chatMngr.LeaveChannel(true);
        //todo-tanglei 后续+确认按钮
        this.HideModel3d();
        
        this.ui.chatState.selectedPage = FGUI_Chat_State.notChatting.ToString();// "notChatting"; 
        this.ui.matchWindow.preMatchState.selectedPage = FGUI_MatchWindow_State.start.ToString();// "start";//切到 匹配中可以 cancel的状态
    }

    //系统事件，对端的发生退出or离线行为时
    private void OnRecvOtherEndExitChannel(string name, object body)
    {
        //todo-tanglei 补提示window
        this.HideModel3d();
        
        this.ui.chatState.selectedPage = FGUI_Chat_State.notChatting.ToString();// "notChatting";
        this.ui.matchWindow.preMatchState.selectedPage = FGUI_MatchWindow_State.start.ToString();// "start";//切到 匹配中可以 cancel的状态
    }

    private void OnExitChannel(string name, object body)
    {
        this.chatMngr.DisposeVoiceEngine();
        //todo-tanglei 在dispose过程中 不显示preMatch的页面 100-200ms
        this.GetUI<MultiTabHomepageUI>(UIConsts.MultiTabHomepage).ShowTabs();//展开底tab
        
    }

    
    #endregion 
    #region text部分的系统通知
    private void OnP2PUserDialog(string s, object body)
    {
        //Debug.LogError("OnP2PUserDialog:" + _controller.Model.UserDialogInfo.text);
        //PtoPSelfCell cell = new PtoPSelfCell();
        //AddCellCom(cell, PtoPCellType.Player);
        //cell.SetData(_controller.Model.UserDialogInfo);

        this.ui.userTf.text = chatMngr.textModel.UserDialogInfo.text;
    }

    private void OnP2PUserDialog2(string s, object body)
    {
        this.ui.userTf.text = chatMngr.textModel.UserDialogInfo2.text;
    }


    private void OnP2POtherDialog(string s, object body)
    {
        //Debug.LogError("OnP2POtherDialog:" + _controller.Model.OtherDialogInfo.text);
        //return;
        //PtoPTargetCell cell = new PtoPTargetCell();
        //AddCellCom(cell, PtoPCellType.Avatar);
        //cell.SetData(_controller.Model.OtherDialogInfo);

        this.ui.avatarTf.text = chatMngr.textModel.OtherDialogInfo.text;
    }
    private void OnP2POtherDialog2(string s, object body)
    {

        this.ui.avatarTf.text = chatMngr.textModel.OtherDialogInfo2.text;
    }

    private void OnP2POtherDialogTranslate(string s, object body)
    {
        this.ui.avatarTransTf.text = chatMngr.textModel.OtherDialogTranslate.replyTranslateText;
    }

    private void OnP2PDialogExample(string s, object body)
    {
        //Debug.LogError("OnP2PDialogExample:" + _controller.Model.DialogExample.exampleText);
        //return;
        //PtoPScaffoldCell cell = new PtoPScaffoldCell();
        //AddCellCom(cell, PtoPCellType.Example);
        //cell.SetData(_controller.Model.DialogExample);

        this.ui.exempleTf.text = chatMngr.textModel.DialogExample.exampleText;
    }

    private void OnP2PDialogExampleTranslate(string s, object body)
    {

        //Debug.LogError("OnP2PDialogExampleTranslate:" + _controller.Model.DialogExampleTranslate.exampleTranslateText);
        return;
    }

    #endregion

    #region other functions

    void OnConfigMicVol() {
        if (this.ui.micVolBtn.micState.selectedPage == "On")
        {
            chatMngr.ConfigMicVol(false);
            this.ui.micVolBtn.micState.selectedPage = "Off";
        }
        else
        {
            chatMngr.ConfigMicVol(true);
            this.ui.micVolBtn.micState.selectedPage = "On";
        }
        
    }
    #endregion 
}

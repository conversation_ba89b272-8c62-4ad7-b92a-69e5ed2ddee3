/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreScaffoldTxt : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreScaffoldTxt";

        public GComponent tfOrigin;
        public GTextField txtTran;
        public GGroup Container;
        public GRichTextField txtName;
        public GGroup grpTitle;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            tfOrigin = (GComponent)com.GetChildAt(1);
            txtTran = (GTextField)com.GetChildAt(2);
            Container = (GGroup)com.GetChildAt(3);
            txtName = (GRichTextField)com.GetChildAt(5);
            grpTitle = (GGroup)com.GetChildAt(6);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            tfOrigin = null;
            txtTran = null;
            Container = null;
            txtName = null;
            grpTitle = null;
        }
    }
}
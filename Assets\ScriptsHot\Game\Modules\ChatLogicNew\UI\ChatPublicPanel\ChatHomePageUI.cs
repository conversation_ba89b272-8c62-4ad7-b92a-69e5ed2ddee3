﻿

using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using FairyGUI;
using Google.Protobuf.Collections;
using Msg.basic;
using Msg.dialog_task;
using ScriptsHot.Game.Modules.Chat.ChatCell;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.ChatLogicNew.UI.Item;
using ScriptsHot.Game.Modules.Common;

using ScriptsHot.Game.Modules.ReviewQuestion;
using ScriptsHot.Game.Modules.Setting;
using ScriptsHot.Game.Modules.Task;
using TMPro;
using UIBind.Chat;
using UIBind.common;
using UnityEngine;
using UnityEngine.UIElements;
using Object = UnityEngine.Object;

public class ChatHomePageUI : BaseUI<UIBind.Chat.ChatHomePagePanel>, IBaseUIUpdate
{
    private Dictionary<ChatCellType, List<GComponent>> _pools = new Dictionary<ChatCellType, List<GComponent>>();

    private ChatLogicController _chatController => this.GetController<ChatLogicController>(ModelConsts.ChatLogic);
    private CurrencyController _currencyController => GetController<CurrencyController>(ModelConsts.CurrencyController);
    protected override bool isFullScreen => true;
    
    private ChatLogicModel _chatModel => this.GetModel<ChatLogicModel>(ModelConsts.ChatLogic);
    private RecommendCardModel _recommendCardModel => GetModel<RecommendCardModel>(ModelConsts.RecommendCard);
    private ReviewQuestionModel _reviewQuestionModel => GetModel<ReviewQuestionModel>
        (ModelConsts.ReviewQuestionModel);
    private Dictionary<int,ChatCellBase> _allCell = new Dictionary<int,ChatCellBase>();
    private UIPackage _uiPackage;
    private int _bubbleId = 0;
    private const int MAXNUM = 0;
    private float _bottomY = 0;
    private float _containHeight = 660;
    private int _emptyBubbleId = 0;
    private int _combo = 0;
    private GComponent _curNewWordCmp;
    private SpinePanelExtension _spCollect;
    private Animator _flowAnimator;
    
    private SettingModel _settingModel => this.GetModel<SettingModel>(ModelConsts.Setting);
    
    public ChatHomePageUI(string name) : base(name)
    {
    }

    public override string uiLayer => UILayerConsts.Home;

    public override EUIDeleteRule deleteRule => EUIDeleteRule.Never;

    protected override void OnInit(GComponent uiCom)
    {
        _uiPackage = FairyGUI.UIPackage.GetByName("Chat");

        _bottomY = this.ui.comCellContainer.comContent.com.height+ui.comCellContainer.comContent.com.position.y;
        ui.comCellContainer.comContent.com.scrollPane.onScroll.Clear();
        ui.comCellContainer.comContent.com.scrollPane.onScroll.Add(() =>
        {
            if (_curNewWordCmp != null)
            {
                _curNewWordCmp.Dispose();
                _curNewWordCmp = null;
            }
        });
        this.AddUIEvent(ui.imgCloseTask.onClick, OnImgCloseTaskClicked);
        _spCollect = ui.spLoader as SpinePanelExtension;
        _spCollect.SetModel(ResUtils.GetSpinePath("spCollectWord"),
            () => { 
                Vector2 screenPos = ui.posCollectEnd.LocalToGlobal(Vector2.zero);
                screenPos.y = Screen.height - screenPos.y; 
                _spCollect.go.transform.Find("root/root/end").position = StageCamera.main.ScreenToWorldPoint(screenPos);
            });
        _spCollect.visible = false;
        _spCollect.loop = false;
        _spCollect.fill = SpinePanelFill.None;
        ui.posCollectEnd.visible = false;
        ui.loader.visible = false;
        ui.comChatCombo.com.visible = false;
    }

    private void ResetCurContainer()
    {
        if (_chatController.CurDialogMode == PB_DialogMode.RolePlay)
        {
            ui.RightLayout.selectedPage = "Type1";
        }
        else
        {
            ui.RightLayout.selectedPage = "Type2";
        }
    }

    private void AddEvent()
    {
        Notifier.instance.RegisterNotification(NotifyConsts.NewChatAddCell,OnAddCell);
        Notifier.instance.RegisterNotification(NotifyConsts.RemoveLoadingCell,OnRemoveCell);
        Notifier.instance.RegisterNotification(NotifyConsts.ChatAudioPlayStart,OnAudioPlay);
        Notifier.instance.RegisterNotification(NotifyConsts.AddLoadingCell,OnAddLoadingCell);
        Notifier.instance.RegisterNotification(NotifyConsts.ShowTranslateInfo,OnShowTranslate);
        Notifier.instance.RegisterNotification(NotifyConsts.PlayTweenShowCell,PlayTweenShowCell);
        Notifier.instance.RegisterNotification(NotifyConsts.HitKnowledgeUpdate,OnHitKnowledgeUpdate);
        Notifier.instance.RegisterNotification(NotifyConsts.UpdateSuggestionState,UpdateSuggestionState);
        Notifier.instance.RegisterNotification(NotifyConsts.ShowAvatarNameAndProfession,ShowAvatarName);
        
    }

    private void RemoveEvent()
    {
        Notifier.instance.UnRegisterNotification(NotifyConsts.NewChatAddCell,OnAddCell);
        Notifier.instance.UnRegisterNotification(NotifyConsts.RemoveLoadingCell,OnRemoveCell);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ChatAudioPlayStart,OnAudioPlay);
        Notifier.instance.UnRegisterNotification(NotifyConsts.AddLoadingCell,OnAddLoadingCell);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ShowTranslateInfo,OnShowTranslate);
        Notifier.instance.UnRegisterNotification(NotifyConsts.PlayTweenShowCell,PlayTweenShowCell);
        Notifier.instance.UnRegisterNotification(NotifyConsts.HitKnowledgeUpdate,OnHitKnowledgeUpdate);
        Notifier.instance.UnRegisterNotification(NotifyConsts.UpdateSuggestionState,UpdateSuggestionState); 
        Notifier.instance.UnRegisterNotification(NotifyConsts.ShowAvatarNameAndProfession,ShowAvatarName);
    }

    private void OnAudioPlay(string s, object body)
    {
        ChatCellInfo info = (ChatCellInfo)body;

        foreach (var kv in _allCell)
        {
            ChatCellBase item = kv.Value;
            if(info.type != item.cellType)
                continue;
            switch (info.type)
            {
                case ChatCellType.AvatarNormal:
                    if (item.AvatarData.avatar_reply_data.tts_record_id == info.tts_record_id && item.AvatarData.bubble_id == info.bubbleId)
                    {
                        VFDebug.Log("找到avatar item  开始播放：" + info.tts_record_id);
                        item.TriggerBtnPlay();
                    }

                    break;
                case ChatCellType.Narration:
                    if (item.AvatarData.dialog_narration.tts_record_id == info.tts_record_id)
                    {
                        VFDebug.Log("找到narration item  开始播放：" + info.tts_record_id);
                        item.TriggerBtnPlay();
                    }
                    break;
            }
        }
    }

    private void OnImgCloseTaskClicked()
    {
        GetController<TaskController>(ModelConsts.Task).OnClosePanel();
    }

    private void OnAddCell(string s, object body)
    {
        ChatCellInfo info = (ChatCellInfo) body;
        ChatCellBase cell = null;
        _bubbleId = info.bubbleId;
        _chatController.CurChat.CurBubbleId = _bubbleId;
        switch (info.type)
        {
            case ChatCellType.AvatarNormal:
                cell = new ChatAvatarNormalCell();
                AddChatCellCom(cell,ChatCellType.AvatarNormal,info.data);
                cell.UpdateText(_bubbleId);
                break;
            case ChatCellType.PlayerNormal:
                cell = new ChatPlayerNormalCell();
                AddChatCellCom(cell,ChatCellType.PlayerNormal,info.data);
                // Debug.LogError("player bubbleId::" + bubbleId);
                cell.UpdateText(_bubbleId);
                break;
            case ChatCellType.Example:
                break;
            case ChatCellType.Narration:
                cell = new ChatNarrationCell();
                AddChatCellCom(cell,ChatCellType.Narration,info.data);
                cell.UpdateText(_bubbleId);
                break;
            case ChatCellType.ChatSceneDesc:
                cell = new ChatSceneDesc();
                AddChatCellCom(cell,ChatCellType.ChatSceneDesc,info.data);
                cell.UpdateText(_bubbleId);
                break;
            case ChatCellType.NameAndProfession:
                cell = new ChatNameAndProfession();
                AddChatCellCom(cell,ChatCellType.NameAndProfession,info.data);
                cell.UpdateText(_bubbleId);
                break;
            case ChatCellType.Scaffold:
                cell = new ChatScaffoldCell();
                AddChatCellCom(cell,ChatCellType.Scaffold,info.data);
                cell.UpdateText(_bubbleId);
                break;
            case ChatCellType.Image:
                cell = new ChatImageCell();
                AddChatCellCom(cell,ChatCellType.Image,info.data);
                cell.InitData();
                break;
        }
        Debug.Log("_bubbleId::------------------------------------------------ " + _bubbleId);
        _allCell.Add(_bubbleId,cell);
    }
    
    private void OnHitKnowledgeUpdate(string s, object body)
    {
        long bubble_id = (long) body;
        if(_allCell.TryGetValue((int)bubble_id,out ChatCellBase value))
        {
            if (value is ChatPlayerNormalCell || value is ChatAvatarNormalCell)
            {
                // Debug.LogError("获取是否有知识点命中：bubble_id：：" + bubble_id);
                RepeatedField<string> infoList = _chatModel.GetHitKnowledge(bubble_id);
                if (infoList == null || infoList.Count <= 0) return;
           
                //更新 player 自己命中知识点
                if (value is ChatPlayerNormalCell)
                {
                    this.ui.comChatCombo.spChatCombo.animationName = "great";
                    this.ui.comChatCombo.com.visible = true;
                    TimerManager.instance.RegisterTimer(cc =>
                    {
                        this.ui.comChatCombo.com.visible = false;
                        //结束玩家阶段
                        Notifier.instance.SendNotification(NotifyConsts.ChatPlayerCellDoOver);
                        
                    }, 1500, 1);
                    (value as ChatPlayerNormalCell).ShowHitKnowledge(infoList);
                }
                else if ( value is ChatAvatarNormalCell)
                {
                    (value as ChatAvatarNormalCell).ShowHitKnowledge(infoList);
                }

            }
        }
    }
    /// <summary>
    /// 更新建议
    /// </summary>
    /// <param name="s"></param>
    /// <param name="body"></param>
    private void UpdateSuggestionState(string s, object body)
    {
        DialogSuggestionData data = body as DialogSuggestionData;
        if (_allCell.TryGetValue((int) data.bubble_id, out ChatCellBase value))
        {
            (value as ChatPlayerNormalCell).ChangeSuggestionState(data.Data);
        }
    }

    private int _loadingBubbleId;
    private void OnAddLoadingCell(string s, object body)
    {
        if (_loadingBubbleId != 0)
            return;
        
        _loadingBubbleId = _chatController.GetBubbleId();
        _chatController.CurChat.CurBubbleId = _loadingBubbleId;
        ChatPreTalkCell chatPreCell = new ChatPreTalkCell();
        AddChatCellCom(chatPreCell, ChatCellType.PreTalk, null);
        _allCell[_loadingBubbleId] = chatPreCell;
    }
    
    private void OnRemoveCell(string s, object body)
    {
        RemoveCellInfo info = (RemoveCellInfo) body;
        if (info == null || info.Type == ChatCellType.PreTalk)
        {
            ReturnChatCellItem(_loadingBubbleId, ChatCellType.PreTalk);
            _loadingBubbleId = 0;
        }
        else
            ReturnChatCellItem(info.BubbleId, info.Type);
    }

    private void OnShowTranslate(string s, object body)
    {
        SC_DialogTranslateAck info = (SC_DialogTranslateAck) body;
        if (_allCell.TryGetValue(info.data.bubble_id, out ChatCellBase cell))
        {
            if (cell is ChatAvatarNormalCell)
            {
                ChatAvatarNormalCell chatAvatarNormalCell = cell as ChatAvatarNormalCell;
                RichContent richContent = new RichContent();
                richContent.Content = info.data.trans_content;
                richContent.Type = RichContentType.Text;
                richContent.IsLast = true;
                chatAvatarNormalCell.AppendTransRichContent(richContent);
            }
            else if (cell is ChatNarrationCell)
            {
                ChatNarrationCell chatNarrationCell = cell as ChatNarrationCell;
                RichContent richContent = new RichContent();
                richContent.Content = info.data.trans_content;
                richContent.Type = RichContentType.Text;
                richContent.IsLast = true;
                chatNarrationCell.AppendTransRichContent(richContent);
            }
        }
    }

    protected override void OnShow()
    {
        ResetCurContainer();
        AddEvent();
        HideAvatarName();
        this.ui.comCellContainer.comContent.Contents.visible = true;
        ui.posCollectEnd.visible = false;
        this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(1f, false);
        this.ui.testBtn2.visible = false;// AppConst.IsDebug;
        this.ui.cellType.visible = false;// AppConst.IsDebug;
        ui.comChatCombo.com.visible = false;
        ui.comChatCombo.tfCombo.visible = false;
        _emptyBubbleId = 0;
        _combo = 0;

        ShowAvatarName(String.Empty,null);
        // CheckMic();
    }

    private async void CheckMic()
    {
        Debug.LogError("请求麦克风权限");
        //判断是否是第一次出现
        if (!Application.HasUserAuthorization(UserAuthorization.Microphone) && !_settingModel.isAppearMicPermission)
        {
            await RequestMicrophonePermissionAsync();
            _settingModel.SetMicrophonePermission(true);
            return;
        }
        
        if (!ControllerManager.instance.GetController<PermissMsgController>(ModelConsts.PermissMsg).CheckPermissMic())
        {
            return;
        }
    }
    
    private async UniTask RequestMicrophonePermissionAsync()
    {
        await Application.RequestUserAuthorization(UserAuthorization.Microphone).ToUniTask();
    }

    private void DoAssistLevel()
    {
        PB_UserAssistLevelEnum level = GetModel<SettingModel>(ModelConsts.Setting).freeTalkDifficultyLevel;
        switch (level)
        {
            case PB_UserAssistLevelEnum.UserAssistLevelHigh:
                ClickHighCallBack();
                break;
            case PB_UserAssistLevelEnum.UserAssistLevelMedium:
                ClickMediumCallBack();
                break;
            case PB_UserAssistLevelEnum.UserAssistLevelLow:
                ClickLowCallBack();
                break;
        }
    }

    protected override void OnHide()
    {
        RemoveEvent();
        ClearData();
    }

    public int AddChatCellCom(ChatCellBase baseChatCell, ChatCellType cellType, object data)
    {
        if (_emptyBubbleId != 0)
        {
            ReturnChatCellItem(_emptyBubbleId,ChatCellType.Empty);
            _emptyBubbleId = 0;
        }
        GComponent gCom;
        string resName = _chatController.GetChatCellResName(cellType);
        if (_pools.ContainsKey(cellType) && _pools[cellType].Count > 0)
        {
            gCom = _pools[cellType][0];
            _pools[cellType].RemoveAt(0);
        }
        else
            gCom = _uiPackage.CreateObject(resName).asCom;

        gCom.name = _bubbleId.ToString();
        int roundId = _chatModel.CurRoundId;
        baseChatCell.OnInit(gCom, cellType, _bubbleId,roundId);
        baseChatCell.SetUI(ui.com);
        if (cellType == ChatCellType.AvatarNormal || cellType == ChatCellType.Narration)
        {
            baseChatCell.SetAvatarData((PB_DialogTaskMsgItem)data,_bubbleId);
        }
        else if (cellType == ChatCellType.PlayerNormal)
        {
            baseChatCell.SetPlayerData((ASRCompleteVO)data,_bubbleId);
        }
        else if (cellType == ChatCellType.Scaffold)
        {
            baseChatCell.SetScaffoldData((SC_GetDialogScaffoldAck)data,_bubbleId);
        }
        else if (cellType == ChatCellType.NameAndProfession)
        {
            baseChatCell.SetCreateDialogData((PB_CreateDialogTaskResp) data, _bubbleId);
        }
        else if (cellType == ChatCellType.ChatSceneDesc)
        {
            baseChatCell.SetCreateDialogData((PB_CreateDialogTaskResp) data, _bubbleId);
        }
        else if (cellType == ChatCellType.Image)
        {
            baseChatCell.SetImageData((PB_DialogImageInfo)data,_bubbleId);
        }

        this.ui.comCellContainer.comContent.com.AddChild(gCom);
        gCom.group = this.ui.comCellContainer.comContent.Contents;
        var pos = gCom.position;
        if (cellType == ChatCellType.AvatarNormal || cellType == ChatCellType.PreTalk || cellType == ChatCellType.ChatSceneDesc || cellType == ChatCellType.Narration || cellType == ChatCellType.NameAndProfession || cellType == ChatCellType.Image)
            gCom.position = new Vector3(0, pos.y, pos.z);
        else
            gCom.position = new Vector3(ui.comCellContainer.com.width - gCom.width , pos.y, pos.z);
        if (cellType == ChatCellType.Empty)
            gCom.height = 0;

        TimerManager.instance.RegisterNextFrame((a) =>
        {
            TimerManager.instance.RegisterNextFrame((a) =>
            {
                this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(1, false);
            });
        });
        DotAppearBubble(cellType);
        return _bubbleId;
    }

    private void DotAppearBubble(ChatCellType cellType)
    {
        string bubbleType = "";
        switch (cellType)
        {
            case ChatCellType.AvatarNormal:
                bubbleType = "Avatar";
                break;
            case ChatCellType.PlayerFollow:
                bubbleType = "User";
                break;
            case ChatCellType.Toast:
                bubbleType = "Toast";
                break;
            case ChatCellType.PlayerNormal:
                bubbleType = "User";
                break;
            case ChatCellType.PreTalk:
                bubbleType = "Loading";
                break;
            case ChatCellType.Scaffold:
                bubbleType = "Scaffold";
                break;
            case ChatCellType.Suggest:
                bubbleType = "Suggest";
                break;
            case ChatCellType.Advice:
                bubbleType = "Advice";
                break;
            case ChatCellType.Empty:
                bubbleType = "Empty";
                break;
            case ChatCellType.Narration:
                bubbleType = "Narration";
                break;
            case ChatCellType.ChatSceneDesc:
                bubbleType = "ChatSceneDesc";
                break;
            case ChatCellType.NameAndProfession:
                bubbleType = "NameAndProfession";
                break;
        }
        DataDotAppear_Dialogue_bubble dot = new DataDotAppear_Dialogue_bubble();
        dot.Dialogue_id = _chatController.CurChat.DialogTaskAck.data.dialog_id;
        dot.Bubble_type = bubbleType;
        dot.Dialogue_round = _chatModel.CurRoundId;
        DataDotMgr.Collect(dot);
    }

    //刷新布局
    public void RefreshCellContainer(int bubbleId)
    {
        this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(1, false);
     
    }

    public void ForceRefershScroll(float progressValue)
    {
        TimerManager.instance.RegisterNextFrame((a) =>
        {
            this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(progressValue, true);
        });
    }

    public void Update(int interval)
    {
        RefreshBubbleVisibility();
    }
    
    private void RefreshBubbleVisibility()
    {
        var scrollY = ui.comCellContainer.comContent.com.scrollPane.scrollingPosY;
        foreach (var keyValue in _allCell)
        {
            // if(!keyValue.Value.isInit)
            //     continue;
            if (keyValue.Value.GCom == null)
            {
                VFDebug.LogError("bindT is null " + keyValue.Value.bubbleId + " type " + keyValue.Value.cellType);
                break;
            }
            if (keyValue.Value.GCom == null)
            {
                VFDebug.LogError("bindT.com is null " + keyValue.Value.bubbleId + " type " + keyValue.Value.cellType);
                break;
            }
            if (!keyValue.Value.GCom.visible && keyValue.Value.GCom.y <= ui.comCellContainer.comContent.com.height + scrollY &&
                keyValue.Value.GCom.y + keyValue.Value.GCom.height >= scrollY)
            {
                keyValue.Value.GCom.visible = true;
            }

            if (keyValue.Value.GCom.visible && keyValue.Value.GCom.y > ui.comCellContainer.comContent.com.height + scrollY)
            {
                keyValue.Value.GCom.visible = false;
            }

            if (keyValue.Value.GCom.visible && keyValue.Value.GCom.y + keyValue.Value.GCom.height < scrollY)
            {
                keyValue.Value.GCom.visible = false;
            }
        }
    }

    public void ChangeChatContainHeight(float y)
    {
        // VFDebug.LogError("y  "+y);
        float contentY = ui.comCellContainer.com.LocalToGlobal(new Vector2(0, ui.comCellContainer.comContent.com.y)).y;
       
        Vector2 aaa = ui.comCellContainer.com.GlobalToLocal(new Vector2(0, contentY));
        Vector2 bbbb = ui.comCellContainer.com.GlobalToLocal(new Vector2(0, y));
        var height = (Mathf.Abs(bbbb.y - aaa.y));
        // VFDebug.LogError("height "+height+"  aaa  "+aaa+"  "+bbbb);
        if (height >= _containHeight)
        {
            ui.comCellContainer.comContent.com.height = _containHeight;
            return;
        }
        this.ui.comCellContainer.comContent.com.height = height;
        this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(1, false);
        this.ui.comCellContainer.comContent.com.EnsureBoundsCorrect();
    }

    public void ShowAvatarName(string s, object body)
    {
        if (_chatController.CurChat == null || _chatController.CurChat.DialogTaskAck == null) return;
        // 都已经发了要showName为什么还要判断模式？
        //if (_chatController.CurChat.DialogTaskAck.data.task_mode != PB_DialogMode.Tutor) return;
        string avatarName = _chatController.CurChat.DialogTaskAck.data.avatar_info.avatar_name;
        string avatarJop = _chatController.CurChat.DialogTaskAck.data.avatar_info.avatar_job;
        if (!string.IsNullOrEmpty(avatarName))
        {
            this.ui.comAvatarName.textAvatarName.text = avatarName;
            var num = CalculateMaxVisibleChars(this.ui.comAvatarName.textAvatarName.displayObject as RichTextField);
            this.ui.comAvatarName.textAvatarName.text = GetEllipsizedText(avatarName, num);


            ui.comAvatarName.textAvatarJob.text = avatarJop;
            var num1 = CalculateMaxVisibleChars(this.ui.comAvatarName.textAvatarJob.displayObject as RichTextField);
            this.ui.comAvatarName.textAvatarJob.text = GetEllipsizedText(avatarJop, num1);

            this.ui.comAvatarName.ObjavatarName.visible = true;
            ui.comAvatarName.com.position = new Vector3(32, ui.comAvatarName.com.position.y);
           
        }
        else
            VFDebug.LogError("avatarName is null");
    }
  
    public float maxWidth = 200f; // 目标宽度



    private int CalculateMaxVisibleChars(RichTextField richTextField)
    {

        float totalWidth = 0f;
        int characterCount = 0;

        // 逐字符计算宽度
        for (int i = 0; i < richTextField.text.Length; i++)
        {
            richTextField.textField.EnableCharPositionSupport();
            float characterWidth = richTextField.textField.charPositions[i].width;

            if (totalWidth + characterWidth > maxWidth)
            {
                break;
            }

            totalWidth += characterWidth;
            characterCount++;
        }

        return characterCount;
    }

        //
    private string GetEllipsizedText(string text,int maxChars)
        {
        
        if (text.Length <= maxChars)
        {
            return text;
        }

        // 截取文本并添加省略号
        string ellipsizedText = text.Substring(0, maxChars - 3) + "...";
        return ellipsizedText;
    }


    public void HideAvatarName()
    {
        this.ui.comAvatarName.ObjavatarName.visible = false;
    }

    //播放显示的动画
    public void PlayTweenShowCell(string name, object param)
    {
        //float duration,float outDuration,int moveDeltaX
        Vector3 data = (Vector3)param;
        float duration = data.x;
        float outDuration = data.y;
        float moveDeltaX = data.z;
        
        ui.comChatCombo.com.visible = false;
        foreach (var item in _allCell)
        {
            ChatCellBase cell = item.Value;
            if (cell.cellType == ChatCellType.AvatarNormal)
            {
                cell.GCom.TweenMoveX(cell.GCom.position.x +moveDeltaX, duration).SetEase(EaseType.QuadIn).OnComplete(() =>
                {
                    // 再向右移动500单位，耗时400ms
                    cell.GCom.TweenMoveX(-cell.GCom.width, outDuration).SetEase(EaseType.QuadIn);;
                });
            }
            else
            {
                cell.GCom.TweenMoveX(cell.GCom.position.x -moveDeltaX, duration).SetEase(EaseType.QuadIn).OnComplete(() =>
                {
                    // 再向右移动500单位，耗时400ms
                    cell.GCom.TweenMoveX(width+100, outDuration).SetEase(EaseType.QuadIn);;
                });
            }
        }
        ui.comAvatarName.com.TweenMoveX(ui.comAvatarName.com.position.x +moveDeltaX, duration).SetEase(EaseType.QuadIn).OnComplete(() =>
        {
            // 再向右移动500单位，耗时400ms
            ui.comAvatarName.com.TweenMoveX(-ui.comAvatarName.com.width, outDuration).SetEase(EaseType.QuadIn);;
        });
        //ui.ObjavatarName.asCom.TweenMoveX()
        //ui.TweenAvatarName.selectedPage = "Out";
        //ui.Out.Play();
    }
    public void ClearData()
    {
        _bubbleId = 0;
        Dictionary<int, ChatCellType> datas = new Dictionary<int, ChatCellType>();
        foreach (var item in _allCell)
        {
            datas[item.Key] = item.Value.cellType;
        }
        foreach (var item in datas)
        {
            ReturnChatCellItem(item.Key, item.Value);
        }
    }

    //还对象 清数据
    public void ReturnChatCellItem(int bubbleId, ChatCellType cellType)
    {
        if (_allCell.TryGetValue(bubbleId, out ChatCellBase chatCell))
        {
            if (chatCell.cellType != cellType) return;
            GComponent gCom = chatCell.GCom;
            if (gCom.parent != null)
                gCom.parent.RemoveChild(gCom);
            if (!_pools.ContainsKey(cellType))
                _pools[cellType] = new List<GComponent>();
            bool isRecycle = _pools[cellType].Count < MAXNUM;

            _allCell.Remove(bubbleId);
            if (!isRecycle)
            {
                gCom.Dispose(); 
            }
            else
                _pools[cellType].Add(gCom);
        }
    }

    public void PlayCollectEffect(List<Vector2> posList, Vector2 pos, Action cb = null, bool showIcon = false, bool disableNext = false)
    {
        if (showIcon)
            ui.posCollectEnd.visible = true;
        _spCollect.visible = true;
        Vector2 screenPos = pos;
        screenPos.y = Screen.height - screenPos.y;
        _spCollect.go.transform.Find("root/root/origin").position = StageCamera.main.ScreenToWorldPoint(screenPos);
        if (posList.Count == 1)
        {
            _spCollect.PlayWithCallback("1", () =>
            {
                ui.posCollectEnd.visible = false;
                _spCollect.visible = false;
                // if (!disableNext)
                //     _chatController.GetChatState().SetNextAble();
                cb?.Invoke();
            });
            
        }
        else if (posList.Count > 1)
        {
            Transform root = _spCollect.go.transform.Find("root/root");
            for (var i = 0; i < 10; i++)
            {
                if (posList.Count > i)
                {
                    Vector2 p = posList[i];
                    p.y = Screen.height - p.y; 
                    root.Find($"spot{i+1}").position = StageCamera.main.ScreenToWorldPoint(p); 
                    root.Find($"spot{i+1}").localScale = Vector3.one;
                }
                else
                {
                    root.Find($"spot{i+1}").localScale = Vector3.zero;
                }
            }
            _spCollect.PlayWithCallback("2", () =>
            {
                ui.posCollectEnd.visible = false;
                _spCollect.visible = false;
                // if(!disableNext)
                //     _chatController.GetChatState().SetNextAble();
                cb?.Invoke();
            });
        }
    }

    public void ShowFlow()
    {
        if (_flowAnimator == null)
        {
            ui.loader.visible = true;
            (this.ui.loader as ModelCameraLoaderExtension)?.SetModel(ResUtils.GetUIModelPath("flow"));
            var model = ui.loader as ModelCameraLoaderExtension;
            var animators = model.go.GetComponentsInChildren<Animator>();
            if (animators.Length > 0)
            {
                _flowAnimator = animators[0];
                _flowAnimator.Play("Flow_Idle_Stand_Appear_Listen");
            }  
        }
        else
        {
            ui.loader.visible = true;
            _flowAnimator.Play("Flow_Idle_Stand_Appear_Listen");
        }
    }

    public void CloseFlow()
    {
        ui.loader.visible = false;
    }

    public void PlayComboAni(string name, bool isPerfect)
    {
        ui.comChatCombo.com.visible = true;
        ui.comChatCombo.spChatCombo.spineAnimation.AnimationState.ClearListenerNotifications();
        ui.comChatCombo.tfCombo.visible = false;
        if (isPerfect && _combo == 0)
            name = "prefec2";
        ui.comChatCombo.spChatCombo.spineAnimation.AnimationState.SetAnimation(0, name, false).Complete += _ =>
        {
            if (isPerfect)
            {
                _combo++;
                ui.comChatCombo.tfCombo.visible = _combo > 1;
                ui.comChatCombo.tfCombo.asTextField.text = $"<font size=48>x</font>{_combo}";
                ui.comChatCombo.Combo.Play(() =>
                {
                    ui.comChatCombo.com.visible = false;
                });   
            }
            else
            {
                _combo = 0;
                ui.comChatCombo.com.visible = false;
            }
        };

    }

    public void ResetCombo()
    {
        _combo = 0;
    }
    
    private void ClickHighCallBack()
    {
        SetDifficultyLevel(PB_UserAssistLevelEnum.UserAssistLevelHigh);
    }
    private void ClickMediumCallBack()
    {
        SetDifficultyLevel(PB_UserAssistLevelEnum.UserAssistLevelMedium);
    }
    private void ClickLowCallBack()
    {
        SetDifficultyLevel(PB_UserAssistLevelEnum.UserAssistLevelLow);
    }
    
    private void SetDifficultyLevel(PB_UserAssistLevelEnum level)
    {
        GetModel<SettingModel>(ModelConsts.Setting).SetFreeTalkDifficultyLevel(level);
        CS_SetUserAssistLevelReq req = new CS_SetUserAssistLevelReq();
        req.user_assist_level = level;
        MsgManager.instance.SendMsg(req);
        DataDotDifficulty_change dot = new DataDotDifficulty_change();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        dot.Difficulty_level = (int)level;
        DataDotMgr.Collect(dot);
    }
}

public class ChatCellInfo
{
    public long tts_record_id = 0;
    /// <summary>
    /// 气泡的唯一id
    /// </summary>
    public int bubbleId = 0;
    public ChatCellType type = ChatCellType.Empty;
    public object data;
}
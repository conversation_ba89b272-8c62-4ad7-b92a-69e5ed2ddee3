using System;
using System.Runtime.Serialization;
using FairyGUI;
using Game.Modules.FragmentPractice;
using Game.Modules.Record;
using Modules.DataDot;
using Msg.question;
using ScriptsHot.Game.Modules.FragmentPractice;
using UnityEngine;

namespace UIBind.FragmentPractice
{
    public partial class CompCheck : ExtendedComponent, IQuestionEventListener,IRecordEventListener
    {
        private static FragmentPracticeModel FragModel => ModelManager.instance.GetModel<FragmentPracticeModel>(ModelConsts.FragmentPractice);
        private static FragmentPracticeController FragController =>
            ControllerManager.instance.GetController<FragmentPracticeController>(ModelConsts.FragmentPractice);

        private GButton checkBtn;

        override protected void OnConstructed()
        {
            checkBtn = btnCheck.com as GButton;
            checkBtn.onClick.Add(OnClickCheck);
            btnJump.com.onClick.Add(OnClickJumpQuestion);
            compCheckContent.Init();
            QuestionEventManager.Instance.AddListener(this);
            RecordEventManager.Instance.AddListener(this);
        }

        override protected void OnWillDispose()
        {
            checkBtn.onClick.Remove(OnClickCheck);
            checkBtn = null;
            QuestionEventManager.Instance.RemoveListener(this);
            RecordEventManager.Instance.RemoveListener(this);
        }

        public void OnHide()
        {
            compCheckContent.OnHide();
        }


        public void OnNewQuestion()
        {
            RefreshJumpState();
            Refresh();            
        }

        public void ChangeBtnJumpState(bool state)
        {
            btnJump.com.enabled = state;
            btnJump.com.touchable = state;
        }

        // todo 应该按状态拆分
        private void Refresh()
        {
            checkBtn.enabled = true;
            checkBtn.touchable = true;
            if (QuestionEventManager.Instance.State == QuestStateEnum.Normal)
            {
                state.selectedPage = "disable check";
                checkBtn.enabled = false;
                (checkBtn as GButton).SetKey("common_check");

                APracticeData question = FragModel.CurQuestion;
                compCheckContent.SetCorrectAnswer(question.CorrectAnswerContent);
                compCheckContent.SetCorrectMeaning(question.CorrectAnswerMeaning);
                compCheckContent.OnShow(question.QuestionType);
            }
            else if (QuestionEventManager.Instance.State == QuestStateEnum.Check)
            {
                // ShowJumpBtn(false);
                state.selectedPage = "check";
                (checkBtn as GButton).SetKey("common_check");
            }
            else if (QuestionEventManager.Instance.State == QuestStateEnum.Retry)
            {
                // ShowJumpBtn(false);
                state.selectedPage = "retry";
                checkBtn.SetKey("common_continue");
                CollectData();
            }
            else if (QuestionEventManager.Instance.State == QuestStateEnum.Submit)
            {
                ShowJumpBtn(false);
                APracticeData question = FragModel.CurQuestion;
                var isRightAnswer = QuestionEventManager.Instance.IsRightAnswer == true;
                SubmitRefresh(question, isRightAnswer);
                CollectData();
            }

            grp.EnsureBoundsCorrect();
            EnsureBoundsCorrect();
        }

        private void ShowJumpBtn(bool show)
        {
            jump.selectedIndex = show ? 1 : 0;
        }

        private void CollectData()
        {
            try
            {
                DotPracticeManager.Instance.Collect(new DataDot_CheckShow(state.selectedPage, (int)FragModel.CurQuestion.StartTimeSecond, (int)Time.realtimeSinceStartup));
            }
            catch (Exception e)
            {
                VFDebug.LogError($"CollectData failed: {e.GetType()} {e.Message}");
            }
        }

        private void CollectSkipData()
        {
            try
            {
                DotPracticeManager.Instance.Collect(new DataDot_CheckShow("skip", (int)FragModel.CurQuestion.StartTimeSecond, (int)Time.realtimeSinceStartup));
            }catch(Exception e)
            {
                VFDebug.LogError($"CollectData failed: {e.GetType()} {e.Message}");
            }
        }

        private void RefreshJumpState()//TODO:还是和数据耦合了 不太好 有空再改改
        {
            if (FragModel.CurQuestion != null)
            {
                if (FragModel.CurQuestion.IsListenPractice())
                {
                    btnJump.title0.SetKey("ui_fragment_no_listen");
                    ShowJumpBtn(true);
                }
                else if (FragModel.CurQuestion.IsSpeakPractice())
                {
                    btnJump.title0.SetKey("ui_fragment_no_speak");
                    ShowJumpBtn(true);
                }
                else
                {
                    ShowJumpBtn(false);
                }
            }
            else
            {
                ShowJumpBtn(false);
            }
        }

        //虽然叫SubmitRefresh但是也不能走两次refresh 只是针对于submit的refresh 还是一个submit对一个refresh
        private void SubmitRefresh(APracticeData question, bool isRightAnswer)
        {
            // 记录题目数据
            if (QuestionEventManager.Instance.IsRightAnswer != true)
                FragController.DoAnswerError();
            else
                FragController.DoAnswerCorrect(useHint:QuestionEventManager.Instance.HasHinted);

            // ShowJumpBtn(false);
            if (isRightAnswer)
            {
                state.selectedPage = "correct";
                compCheckContent.spCor.spineAnimation.AnimationState.SetAnimation(0, "animation", false);
            }
            else
            {
                state.selectedPage = "error";
                compCheckContent.compIncor.com.alpha = 0f;
                compCheckContent.compIncor.com.alpha = 1f;
                compCheckContent.compIncor.t0.Play();
            }
            
            checkBtn.SetKey(isRightAnswer ? "common_continue" : "common_got_it");
        }

        public void EnableContinueBtn() //因为这个函数调false 不行 还得切控制器
        {
            state.selectedPage = "check";
            checkBtn.enabled = true;
            btnCheck.state.selectedIndex = 2;
            checkBtn.SetKey("common_continue");
        }
        
        private void OnClickCheck()
        {            
            DotPracticeManager.Instance.Collect(new DataDot_CheckClick(state.selectedPage));
            
            if (QuestionEventManager.Instance.State == QuestStateEnum.Normal) //只有纠错界面才可进入点击状态
            {
                Refresh();
            }
            if (QuestionEventManager.Instance.State == QuestStateEnum.Check)
            {
                QuestionEventManager.Instance.SubmitAnswer();
            }
            else if (QuestionEventManager.Instance.State == QuestStateEnum.Retry)
            {
                QuestionEventManager.Instance.DispatchRetry();
                QuestionEventManager.Instance.Reset(true);
                Refresh();
            }
            else if (QuestionEventManager.Instance.State == QuestStateEnum.AutoCheck)
            {
                QuestionEventManager.Instance.SubmitAnswer();
                if (QuestionEventManager.Instance.State == QuestStateEnum.Retry)//如果是submit就不用走了
                {
                    SoundManger.instance.PlayUI("question_warning");
                    Refresh();
                }
            }
            else if (QuestionEventManager.Instance.State == QuestStateEnum.JumpSpeakTask ||
                     QuestionEventManager.Instance.State == QuestStateEnum.JumpListenTask ||
                     QuestionEventManager.Instance.State == QuestStateEnum.Submit)
            {
                if (GSoundManager.instance.IsPlaying("TTS")) TTSManager.instance.StopTTS();
                if (FragModel.JumpMode && FragModel.JumpTaskShieldCurNum <= 0)
                {
                    FragController.ReqSettlement();
                    touchable = false;
                    return;
                }
                QuestionEventManager.Instance.Reset();
                if (FragController.IsLastQuestion())
                {
                    if (FragModel.Round == 1 && FragModel.CurQuestionIdx == 0)
                    {
                        EnableContinueBtn();
                    }
                }
            }
        }

        private void OnClickJumpQuestion()
        {
            ShowJumpBtn(false);
            checkBtn.enabled = true;
            checkBtn.SetKey("common_continue");
            state.selectedPage = "retry";
            compCheckContent.retryType.selectedPage = "try";
            compCheckContent.retryType.selectedPage = "onlyTitle";

            if (FragModel.CurQuestion.IsListenPractice())
            {
                CollectSkipData();
                compCheckContent.tfRetry.SetKey("ui_fragment_check_btn_jumpListen");
                QuestionEventManager.Instance.JumpListenTask();
                
            }
            else if (FragModel.CurQuestion.IsSpeakPractice())
            {
                CollectSkipData();
                compCheckContent.tfRetry.SetKey("ui_fragment_check_btn_jumpSpeak");
                QuestionEventManager.Instance.JumpSpeakTask();
            }
            
            compCheckContent.grpTry.EnsureBoundsCorrect();
            compCheckContent.grpRetry.EnsureBoundsCorrect();
            compCheckContent.grpAll.EnsureBoundsCorrect();

            DotPracticeManager.Instance.Collect(new DataDot_JumpClick());
        }

        public void OnAnswered()
        {
            Refresh();
        }

        public void OnSubmit()
        {
            Refresh();
        }

        public void OnRetry()
        {
            Refresh();
        }

        public void AutoCheck()
        {
            OnClickCheck();
        }

        public void OnReset()
        {
            //Refresh();//有跳过状态了 这里刷新就太早了
        }

        public void OnJumpListenTask() { }
        public void OnJumpSpeakTask() { }

        public bool IsReceivingEvents => true;
        public void OnRecordStart(string rawString, int recordId)
        {
            ChangeBtnJumpState(false);
        }

        public void OnRecordStop()
        {
            ChangeBtnJumpState(true);
        }

        public void OnRecordCancel()
        {
            ChangeBtnJumpState(true);
        }

        public void OnVad()
        {
            ChangeBtnJumpState(true);
        }

        public void OnCountDown() { }

        public void OnMatchAll()
        {
            ChangeBtnJumpState(true);
        }

        public void OnTranscription(string transcribedText, int recordId) { }
    }
}
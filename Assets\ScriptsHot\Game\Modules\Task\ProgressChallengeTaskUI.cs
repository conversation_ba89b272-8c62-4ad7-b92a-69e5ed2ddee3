using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using FairyGUI;
using Google.Protobuf.Collections;
using Msg.basic;
using Msg.dialog_flow;
using Msg.dialog_task;
using Msg.task;
using Msg.task_process;
using Msg.tts;
using ScriptsHot.Game.Modules.Chat.ChatScore;
using ScriptsHot.Game.Modules.Common;

using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using ScriptsHot.Game.Modules.Task;
using UIBind.Progress;
using UnityEngine;
using Sequence = DG.Tweening.Sequence;

public class ProgressChallengeTaskUI : FlowHelpUIBase<UIBind.Progress.ProgressChallengeTaskPanel>, IBaseUIUpdate
{
    private bool _isShowDetail = false;
    private bool _isShowAll = false;
    private string[] taskStatusAni = new[] { "a1", "c1", "b1" }; //任务状态动画 ：a1 - 默认，b1 - 成功，c1 - 失败
    private GTweener _tweener = new GTweener();
    private Sequence _sequence = DOTween.Sequence();
    private int _nodeOneIndex = 0;
    
    

    public ProgressChallengeTaskUI(string name) : base(name)
    {
    }

    public override string uiLayer => UILayerConsts.Home; //主UI层
    protected override bool isFullScreen => true;
    // private TaskModel model => this.GetModel<TaskModel>(ModelConsts.Task);
    private TaskController controller => this.GetController<TaskController>(ModelConsts.Task);
    
    private ChatUI _chatUI => this.GetUI<ChatUI>(UIConsts.Chat);
    private List<TaskNode> nodes = new List<TaskNode>();
    private TextFieldExtension _promptExtLeft;
    private TextFieldExtension _promptExtRight;
    private TextFieldExtension _promptExtMid;

    // private TextFieldExtension _tfExtFlow;

    private List<TipsItem> tipItems = new List<TipsItem>();
    private NewWordComponent popoverComp;
    
    private Coroutine scoreTimesCoroutine = null;
    
    private Coroutine rollNumbersCoroutine = null;
    
    private Coroutine starFlyCoroutine = null;

    //翻倍动画每个单元时长
    private float _scoreTime = 0.3f;
    //进度条长度
    private float _scoreProgressLength = 0;

    private float _curScore = 0;

    private int _currentMilestone = 0;//当前进度点

    private int _totalScore = 0;//总分
    private PB_DialogMode _chatMode;
    private Vector2 _initSpineProgressPos = Vector2.zero;
    
    private string timerPorgress = string.Empty;

    protected override GLoader3D _spFlowHelp => ui.spFlowHelp;

    protected override GImage _imgPlayerBubble => ui.comTaskProgress.imgPlayerBubble;
    protected override GImage _imgFlowBubble => ui.comTaskProgress.imgFlowBubble;
    protected override GTextField _tfPlaceHolder => ui.comTaskProgress.tfPlaceHolder;
    protected override GGroup _grpFlowHelper => ui.comTaskProgress.grpFlowHelper;
    protected override GGroup _grpDetail => ui.comTaskProgress.grpDetail;
    protected override GGroup _grpContent => ui.comTaskProgress.grpContent;
    protected override GComponent _tfFlow => ui.comTaskProgress.tfFlow;
    protected override TextFieldExtension _tfExtFlow => ui.comTaskProgress.tfFlow as TextFieldExtension;
    
    public class ProgressColorEnum
    {
        public const string Init = "#111111";//初始化颜色
        public const string used = "#21D089";//使用过的颜色
    }

    protected override void OnInit(GComponent uiCom)
    {
        GRoot.inst.onClick.Add(OnFullScreenClicked);

        InitTextExt();
        // InitStarFlySpineEndPos();
        // AddUIEvent(this.ui.barTaskProgress.com.onClick, OnProgressBarClicked);
        // // AddUIEvent(this.ui.btnHide2Panel.onClick, HideDetail);
        // AddUIEvent(this.ui.maskSlider.onClick, ShowPanel);
        // AddUIEvent(this.ui.maskSlider2.onClick, HidePanel);
        // AddUIEvent(this.ui.comTaskProgress.comTaskDetail.btnShowDetail.onClick, ShowTaskDetail);
        AddUIEvent(this.ui.comTaskProgress.comPrompt1.com.onClick, (c) => ClickPrompt(_promptExtLeft, ui.comTaskProgress.comPrompt1));
        AddUIEvent(this.ui.comTaskProgress.comPrompt2.com.onClick, (c) => ClickPrompt(_promptExtRight, ui.comTaskProgress.comPrompt2));
        AddUIEvent(this.ui.comTaskProgress.comPrompt3.com.onClick, (c) => ClickPrompt(_promptExtMid, ui.comTaskProgress.comPrompt3));
        AddUIEvent(this.ui.btnScoreProgress.onClick, ClickScoreProgress);
        //向下滑隐藏
        // AddUIEvent(ui.comTaskProgress.com.onTouchBegin,TouchBeginSliderDown);
        // AddUIEvent(ui.comTaskProgress.com.onTouchEnd,TouchEndSliderDown);
        // AddUIEvent(ui.btnHide2Panel.onTouchBegin,TouchBeginSliderDown);
        // AddUIEvent(ui.btnHide2Panel.onTouchEnd,TouchEndSliderDown);

        //向上滑显示
        // AddUIEvent(ui.maskSlider.onTouchBegin, TouchBeginSliderUp);
        // AddUIEvent(ui.maskSlider.onTouchEnd, context => TouchEndSliderUp(context));
        // AddUIEvent(ui.maskSlider2.onTouchBegin, TouchBeginSliderUp);
        // AddUIEvent(ui.maskSlider2.onTouchEnd, context => TouchEndSliderUp(context));
        // AddUIEvent(ui.barTaskProgress.com.onTouchBegin,TouchBeginSliderUp);
        // AddUIEvent(ui.barTaskProgress.com.onTouchEnd,TouchEndSliderUp);
        
        _nodeOneIndex = this.ui.barTaskProgress.com.GetChildIndex(this.ui.barTaskProgress.comNode1.com);
        
        // InitFlowHelp(ui.loader);
    }
    #region progress
    //***************************************通用逻辑和任务提示相关*******************************************************
    protected override void OnShow()
    {
        Debug.Log("ProgressChallengeTaskUI OnShow");
        _chatMode = GetModel<ChatModel>(ModelConsts.Chat).chatMode;
        _spFlowHelp.visible = false;
        nodes.Clear();
        nodesPercent.Clear();
        InitHidePanel();
        this.InitBarUI();
        this.InitNodes();
        ResetFlowHelp();
        PlayNodeEnterAnimation(0);
        this.SetTaskDetail(); 
        this.InitScoreProgress();
        
        this.InitTaskProgressColor();
        if (shouldShowDetail)
        {
            InnerShowDetailPanel(showOrClose,true);
        }
        HideTaskDetail(true);

    }
    public void InitNodes()
    {
        for (int i = 0; i < 10; i++)
        {
            var taskNode = this.ui.barTaskProgress.com.GetChildAt(_nodeOneIndex + i).asCom;
            TaskNode node = new TaskNode();
            node.Construct(taskNode); //ui和脚本绑定
            node.tfNum.text = (i + 1).ToString();

            nodes.Add(node);
        }
    }
    
    public GGraph GetDragInputPanel()
    {
        return ui.maskSlider2;
    }

    public GComponent GetDragTarget()
    {
        return ui.comTaskProgress.com;
    }

    public float GetAutoScrollRatio()
    {
        return 0.85f;
    }

    public void OnDrag(Vector2 pos)
    {
        AdjustPanel();
    }
    
    public bool IsDragAble()
    {
        return _curFlowState == FlowHelpState.ShowResult || _curFlowState == FlowHelpState.Idle || _curFlowState == FlowHelpState.UnUsed ||
               _curFlowState == FlowHelpState.SendAble || _curFlowState == FlowHelpState.Recording;
    }

    public void OnDragIn()
    {
        _isShowProgress = true;
        _isShowFlowHelp = false;
        ui.maskSlider.visible = false;
        ui.maskSlider2.visible = true;
        Debug.LogError("OnDragIn");
    }
    
    public void OnDragOut()
    {
        _isShowProgress = false;
        _isShowFlowHelp = false;
        ui.maskSlider.visible = true;
        ui.maskSlider2.visible = false;
        Debug.LogError("OnDragOut");
        if (_curFlowState == FlowHelpState.SendAble || _curFlowState == FlowHelpState.Recording)
        {
            //CloseFLowHelp(false, false);
            ui.spFlowHelp.visible = false;
            _isShowBell = false;
            _lastClickedTime = Time.time;
            if (_flowAnimator != null)
                _flowAnimator.SetTrigger(ANI_TRIGGER_QUIT_LISTEN);
        }
    }

    public void HideFlow()
    {
        if(ui!= null && ui.loader != null)
            ui.loader.visible = false;
    }

    public void ShowFlow()
    {
        if(ui!= null && ui.loader != null)
            ui.loader.visible = true;
    }
    //面板隐藏
    protected override void HidePanel()
    {
        if (_isOnChanging || !_isShowProgress)
            return;
        _isOnChanging = true;
        // ui.comTaskProgress.grpContent.
        var pos = ui.com.GlobalToLocal(new Vector2(0, Screen.height));//左下角位置（有问题
        // ui.comTaskProgress.com.SetBoundsChangedFlag();
        // ui.comTaskProgress.com.EnsureBoundsCorrect();
        
        AdjustDetailSize();
        ui.comTaskProgress.com.TweenMoveY(pos.y, 0.2f).OnComplete(() =>
        {
            TimerManager.instance.RegisterNextFrame((a) =>
            {
                _isOnChanging = false;
            });
            // ui.btnHide2Panel.visible = false;
            _isShowProgress = false;
            _isShowFlowHelp = false;
            ui.maskSlider.visible = true;
            ui.maskSlider2.visible = false;
            AdjustPanel();
        });
        
        //新埋点：点击隐藏下方目标
        DataDotClickTaskGoalHide dot = new DataDotClickTaskGoalHide();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        DataDotMgr.Collect(dot);
        
        
        // Screen.width
        // var offsetY = (_startPos.y - ui.comContent.grpContent.size.y) - ui.comContent.com.y;
        // ui.comContent.com.TweenMoveY(ui.comContent.com.y + offsetY, 0.2f);
    }

    private void InitHidePanel()
    {
        var pos = ui.com.GlobalToLocal(new Vector2(0, Screen.height));
        ui.comTaskProgress.com.position = new Vector2(pos.y, 0.2f);
        _isShowProgress = false;
        _isShowFlowHelp = false;
        ui.maskSlider.visible = true;
        ui.maskSlider2.visible = false;
        _isOnChanging = false;
    }

    //面板显示
    protected override void ShowPanel()
    {
        if (_isOnChanging || _isShowProgress)
            return;
        _isOnChanging = true;
        var chatModel = GetModel<ChatModel>(ModelConsts.Chat);
        var _startPos = ui.com.GlobalToLocal(new Vector2(0, Screen.height));//面板左下角坐标
        ui.comTaskProgress.com.xy = ui.com.GlobalToLocal(new Vector2(0, Screen.height));//让任务面板的xy和上面这个坐标对齐
        AdjustDetailSize();
        ui.comTaskProgress.com.TweenMoveY(_startPos.y - ui.comTaskProgress.com.size.y, 0.2f).OnComplete(() =>
        {//让面板的y值往上移
            TimerManager.instance.RegisterNextFrame((a) =>
            {
                _isOnChanging = false;
            });
            // ui.btnHide2Panel.visible = true;
            _isShowProgress = true;
            ui.maskSlider.visible = false;
            ui.maskSlider2.visible = true;
        });
        
        //新埋点：点击展示下方目标
        DataDotClickTaskGoalShow dot = new DataDotClickTaskGoalShow();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        DataDotMgr.Collect(dot);
    }

    private void OnProgressBarClicked()
    {
        if (_curFlowState != FlowHelpState.Idle && _curFlowState != FlowHelpState.ShowResult && _curFlowState != FlowHelpState.UnUsed)
            return;

        if (_isShowProgress)
            HidePanel();
        else
            ShowPanel();
    }

    private void SetUI()
    {
        // this.InitBarUI();
        //this.SetTaskStatus();
        //this.SetTaskDetail();
        BindPromptsData(model.curTaskIndex);
        var chatModel = GetModel<ChatModel>(ModelConsts.Chat);
        if (_lastUseHelpRound < chatModel.curRoundId)
        {
            ui.comTaskProgress.grpFlowHelper.visible = false;
            ChangeFlowHelpState(FlowHelpState.Idle);
        }
        InitTaskProgressColor();
    }

    public void Update(int interval)
    {
        // if (_isOnChanging)
        //     AdjustPanel();

        if (_curFlowState == FlowHelpState.Idle)
        {
            if (Time.time - _lastClickedTime > 6f && !_isShowBell)
            {
                ShowFlowBell();
            }
        }
    }

    //初始化进度条信息
    private void InitBarUI()
    {
        ui.barTaskProgress.com.visible = true;
        if (this.model.taskTotalNum <= 0) return;
        
        ProgressBarCom barCom = this.ui.barTaskProgress;
        barCom.barTaskProgress.min = 0;
        barCom.barTaskProgress.max = this.model.taskTotalNum;
        barCom.barTaskProgress.value = model.curTaskIndex + 1;

        var columnGap =
            (barCom.imgPlaceHolder.width -
             (this.model.taskTotalNum + 1) * this.ui.barTaskProgress.comNode1.defaultNode.width) /
            this.model.taskTotalNum;
        barCom.grpTaskNode.columnGap = (int)columnGap;
        barCom.grpTaskNode.SetBoundsChangedFlag();


       
        //设置显隐
        for (int i = 0; i < 10; i++)
        {
            var gComponent = this.ui.barTaskProgress.com.GetChildAt(_nodeOneIndex + i).asCom;
            if (gComponent != null)
            {
                gComponent.width = this.ui.barTaskProgress.comNode1.defaultNode.width;
                gComponent.height = this.ui.barTaskProgress.comNode1.defaultNode.height;
                gComponent.y = this.ui.barTaskProgress.com.height / 2;
                gComponent.visible = this.model.taskTotalNum  > i;
            }
        }
        
        barCom.grpTaskNode.width = barCom.imgPlaceHolder.width;
        barCom.grpTaskNode.SetBoundsChangedFlag();
        barCom.grpTaskNode.EnsureBoundsCorrect();
        
        //获取节点坐标位置
        for (int i = 0; i < 10; i++)
        {
            var gComponent = this.ui.barTaskProgress.com.GetChildAt(_nodeOneIndex + i).asCom;
            if (gComponent != null && gComponent.visible)
            {
                float nodePercent = (gComponent.x - barCom.imgBar.x) / barCom.imgBar.width;
                float nodePercent1 = nodePercent * (float)barCom.barTaskProgress.max;
                nodesPercent.Add(nodePercent1);
            }
        }

    }
    
    //初始化分数进度条
    private void InitScoreProgress()
    {
        ui.grpStarProgress.visible = false;
        return;
        ui.comScore.com.visible = false;
        ui.imgMask.visible = false;
        if (_chatMode != PB_DialogMode.Challenge)
        {
            ui.grpStarProgress.visible = false;
            return;
        }
        ui.grpStarProgress.visible = true;
        ui.Reset.Play();
        //设置名字
        // string name = this.GetModel<MainModel>(ModelConsts.Main).playerName;;
        string name = this.GetController<SceneController>(ModelConsts.Scene).
            scene.GetComponent<ChatComponent>().currAvatarName;
        if (name.Length >= 2)
            name = name.Substring(0, 2);
        ui.tfName.text = name;
        //计算spine进度条动画初始位置
        Vector2 progressPos = ui.btnScoreProgress.position;
        ui.comScoreProgress.spineScoreProgress.position = new Vector2(progressPos.x-10,ui.comScoreProgress.spineScoreProgress.position.y);
        
        //靠近头像是1  算出头像最右边x
        float headRightPosX = ui.imgAvatarNameBg.parent.LocalToGlobal(ui.imgAvatarNameBg.position).x + ui.imgAvatarNameBg.width;

        //算出进度条最右边的x
        float progressRightPosX = ui.btnScoreProgress.parent.LocalToGlobal(progressPos).x + ui.btnScoreProgress.width;
        _scoreProgressLength = progressRightPosX - headRightPosX;
        
        ui.comScoreProgress.spineScoreProgress.animationName = "2";
        Vector2 curPos = ui.comScoreProgress.spineScoreProgress.position;
        Vector2 nameGlobalBgPos = ui.imgAvatarNameBg.parent.LocalToGlobal(ui.imgAvatarNameBg.position);
        Vector2 nameLocalBgPos = ui.comScoreProgress.spineScoreProgress.parent.GlobalToLocal(nameGlobalBgPos);
        _initSpineProgressPos = new Vector2(nameLocalBgPos.x+ui.imgAvatarNameBg.width,curPos.y);
        ui.comScoreProgress.spineScoreProgress.position = _initSpineProgressPos;
        
        
        Vector2 globalProgressPos = ui.comScoreProgress.spineScoreProgress.parent.LocalToGlobal(_initSpineProgressPos);
        Vector2 localGrpNumPos = ui.grpNum.parent.GlobalToLocal(globalProgressPos);
        ui.grpNum.position = new Vector2(localGrpNumPos.x - ui.grpNum.width/2,ui.grpNum.position.y);
        //设置tips默认状态及数据
        // ui.comScoreProgress.grpNum.visible = false;
        // ui.comScoreProgress.tfScore.text = "0";
        ui.grpNum.visible = false;
        ui.tfScore.text = "0";
        InitStarState();
    }



    //初始化星星状态及位置
    private void InitStarState()
    {
        //初始化星星的状态及位置
        ui.comStar1.spineStar.animationName = "1";
        ui.comStar1.purpleBg.visible = false;
        ui.comStar2.spineStar.animationName = "1";
        ui.comStar2.purpleBg.visible = false;
        ui.comStar3.spineStar.animationName = "1";
        ui.comStar3.purpleBg.visible = false;

         PB_StarInfo pbStarInfo = GetModel<TaskModel>(ModelConsts.Task).initStarInfo;
         if (pbStarInfo.stages.Count != 3)
         {
             Debug.LogError("当前星星数分段信息不对  长度不是3");
             return;
         }
        List<PB_StarProgressStage> list = pbStarInfo.stages.ToList();
        _totalScore = pbStarInfo.total_score;
        
        
        // //=======================测试代码
        // _totalScore = 900;
        // List<PB_StarProgressStage> list = new List<PB_StarProgressStage>();
        // PB_StarProgressStage data = new PB_StarProgressStage();
        // data.star_idx = 0;
        // data.score_end = 300;
        // list.Add(data);
        //
        // PB_StarProgressStage data1 = new PB_StarProgressStage();
        // data1.star_idx = 1;
        // data1.score_end = 600;
        // list.Add(data1);
        //
        // PB_StarProgressStage data2 = new PB_StarProgressStage();
        // data2.star_idx = 2;
        // data2.score_end = 900;
        // list.Add(data2);
        for (int i = 0; i < list.Count; i++)
        {
            PB_StarProgressStage progressStage = list[i];
            PorgressStarCom progressStar = null;
            switch (progressStage.star_idx)
            {
                case 0:
                    progressStar = ui.comStar1;
                    break;
                case 1:
                    progressStar = ui.comStar2;
                    break;
                case 2:
                    progressStar = ui.comStar3;
                    break;
            }

            if (progressStar != null)
            {
                float movePosX = ui.imgAvatarNameBg.position.x+ui.imgAvatarNameBg.width + (float)progressStage.score_end /(float) _totalScore* _scoreProgressLength;
                progressStar.com.position = new Vector2(movePosX,progressStar.com.position.y);
                progressStar.tfNum.text = progressStage.score_end.ToString();
            }
        }
    }

    //显示所有面板信息
    public void ShowAllTaskInfo(bool isShow)
    {
        shouldShowDetail = true;
        showOrClose = isShow;

        if (!this.isShow)
        {
            Show();
        }
        else
        {
            InnerShowDetailPanel(isShow);
        }
    }

    //隐藏详情
    public void HideTaskDetail(bool isShow)
    {
        this.ui.comTaskProgress.com.visible = isShow;
        var chatModel = GetModel<ChatModel>(ModelConsts.Chat);
        if (chatModel.curRoundId == 1)
        {
            if (!string.IsNullOrEmpty(timerPorgress))
            {
                TimerManager.instance.UnRegisterTimer(timerPorgress);
                timerPorgress = string.Empty;
            }

            TimerManager.instance.RegisterTimer((a) =>
            {
                if(isShow)
                    AdjustPanel();
            },500,1);
        }

        
        
    }


    void InnerShowDetailPanel(bool isShow,bool isFirst = false)
    {
        SetUI();
        Debug.Log("ProgressChallengeTaskUI ShowAllTaskInfo");


        // this.ui.ctrlPanelStatus.selectedPage = showStatus;
        this._isShowAll = isShow;

        //this.SetTaskStatus();
        if(!isFirst)
            this.SetBarUI();

        // this.RegisterTimer(c => { controller.NotifyChatPageChangeSize(ui.comTaskProgress.com.yMin); },
        //     controller.OutInAnimationDelayTime, 1);
        if (isShow)
        {
            TimerManager.instance.RegisterNextFrame((a) => { ShowPanel(); });

            DataDotAppearTaskGoalShow dot = new DataDotAppearTaskGoalShow();
            dot.Dialogue_id = DataDotMgr.GetDialogId();
            DataDotMgr.Collect(dot);
        }
        else
        {
            HidePanel();

            DataDotAppearTaskGoalHide dot = new DataDotAppearTaskGoalHide();
            dot.Dialogue_id = DataDotMgr.GetDialogId();
            DataDotMgr.Collect(dot);
        }
    }


    //展开任务信息:TODO 待ui出图，具体修改
    private void ShowTaskDetail()
    {
        if (!_isShowDetail)
        {
            this.ui.comTaskProgress.comTaskDetail.ctrShowDetail.selectedIndex = 1;
            this.ui.comTaskProgress.comTaskDetail.tfTaskDetail.height =
                ui.comTaskProgress.comTaskDetail.tfTaskDetail.textHeight;
            _isShowDetail = true;
        }
        else
        {
            this.ui.comTaskProgress.comTaskDetail.ctrShowDetail.selectedIndex = 0;
            this.ui.comTaskProgress.comTaskDetail.tfTaskDetail.height = 72;
            _isShowDetail = false;
        }
        
        ui.comTaskProgress.grpDetail.SetBoundsChangedFlag();
        ui.comTaskProgress.grpDetail.EnsureBoundsCorrect();
        ui.comTaskProgress.grpContent.SetBoundsChangedFlag();
        ui.comTaskProgress.grpContent.EnsureBoundsCorrect();
        ui.comTaskProgress.com.SetBoundsChangedFlag();
        ui.comTaskProgress.com.EnsureBoundsCorrect();

    }


    //进度条及节点变化
    private void SetBarUI()
    {
        Debug.Log("SetBarUI " + this.model.curTaskIndex);
        //进度增长
        if (this.model.curTaskStatus != TaskStepStatus.NoneStep)
        {
            this.ui.barTaskProgress.barTaskProgress.TweenValue(model.curTaskIndex + 1 + 1, 0.6f).OnComplete(() =>
            {
                if (this.model.curTaskStatus == TaskStepStatus.FinishStep)
                {
                    SetNodeUI(true);
                }
                else if (this.model.curTaskStatus == TaskStepStatus.UnFinishStep)
                {
                    SetNodeUI(false);
                }
            });
        }
    }
    
    public void ChangeNode()
    {
        HideTaskDetail(true);
        this.PlayTaskSwitch();
        // PlayNodeEnterAnimation(this.model.curTaskIndex + 1);
    }

    //播放节点动画
    void SetNodeUI(bool isSuccess)
    {
        TaskNode node = nodes[this.model.curTaskIndex];
        if (isSuccess)
        {
            node.success.Play(() => {         PlayNodeEnterAnimation(this.model.curTaskIndex + 1);});
        }
        else
        {
            node.failed.Play(() => {         PlayNodeEnterAnimation(this.model.curTaskIndex + 1);});
        }
    }

    //播放任务切换动画
    void PlayTaskSwitch()
    {
        this.SetTaskDetail();
        this.model.SetTaskStatus(TaskStepStatus.UnStartStep);
        this.SetTaskStatus(false);
        BindPromptsData(model.curTaskIndex + 1);
    }

    void PlayNodeEnterAnimation(int idx)
    {
        if (idx < nodes.Count)
        {
            TaskNode node = nodes[idx];
            node.enter.Play(() => { });
        }
    }

    //更新任务详情
    private void SetTaskDetail()
    {
        Debug.Log("Task progress SetTaskDetail " + this.model.curTaskIndex+" model.curTaskDetail "+model.curTaskDetail);
        if (this.model.allTaskInfos.Count != 0)
            this.ui.comTaskProgress.comTaskDetail.tfTaskDetail.text = this.model.curTaskDetail;
        if (ui.comTaskProgress.comTaskDetail.tfTaskDetail.richTextField.textField.lines.Count > 2)
        {
            ui.comTaskProgress.comTaskDetail.icon_down.visible = true;
            ui.comTaskProgress.comTaskDetail.icon_up.visible = true;
        }
        else
        {
            ui.comTaskProgress.comTaskDetail.icon_down.visible = false;
            ui.comTaskProgress.comTaskDetail.icon_up.visible = false;
        }
    }

    //是否等待评分详情界面关闭
    private bool isWaitScoreUIClose = false;
    //更新任务状态
    private void SetTaskStatus(bool isPlayScoreTimes)
    {
        this.ui.comTaskProgress.comTaskDetail.aniTaskStatus.spineAnimation.AnimationState.ClearListenerNotifications();
        if ((int)this.model.curTaskStatus >= 1 && (int)this.model.curTaskStatus <= taskStatusAni.Length)
            this.ui.comTaskProgress.comTaskDetail.aniTaskStatus.spineAnimation.AnimationState.SetAnimation(0,taskStatusAni[(int)this.model.curTaskStatus - 1], false).Complete +=
                (t) =>
                {
                    // //只有挑战模式才播放该动效
                    // if (_chatMode == PB_DialogMode.Challenge && isPlayScoreTimes)
                    // {
                    //     //如果播放前打开了评分详情界面 就等待
                    //     if (GetUI<ChatScoreUI>(UIConsts.ChatScore).isShow)
                    //         isWaitScoreUIClose = true;
                    //     else
                    //     {
                    //                 
                    //         if (scoreTimesCoroutine != null)
                    //         {
                    //             Timers.inst.StopCoroutine(scoreTimesCoroutine);
                    //             scoreTimesCoroutine = null;
                    //         }
                    //         scoreTimesCoroutine = Timers.inst.StartCoroutine(ShowScoreTimesAnim(model.curStepScore));
                    //     }
                    //
                    //    
                    // }

                    
                    this.ui.comTaskProgress.comTaskDetail.aniTaskStatus.spineAnimation.AnimationState.ClearListenerNotifications();
                };
    }

    //处理事件
    protected override void HandleNotification(string name, object body)
    {
        if (this.ui.com.visible)
        {
            // if (name == NotifyConsts.TaskDetailChange)
            // {
            //     this.SetTaskDetail();
            //     BindPromptsData();
            // }
            if (name == NotifyConsts.TaskStatusChange)
            {
                this.SetTaskStatus(true);
            }

            // if (name == NotifyConsts.CloseChatScoreUI)
            // {
            //     if (isWaitScoreUIClose)
            //     {
            //         isWaitScoreUIClose = false;
            //         if (scoreTimesCoroutine != null)
            //         {
            //             Timers.inst.StopCoroutine(scoreTimesCoroutine);
            //             scoreTimesCoroutine = null;
            //         }
            //         scoreTimesCoroutine = Timers.inst.StartCoroutine(ShowScoreTimesAnim(model.curStepScore));
            //     }
            // }

            // if (name == NotifyConsts.ShowTaskPanel)
            // {
            //     this.ShowAllTaskInfo(true);
            // }
        }
    }

    private int _tipsCount = 0;
    private void BindPromptsData(int index)
    {
        _tipsCount = 0;
        PB_TaskStepContentData data = model.curTaskMsg;
        if (data == null)
        {
            Debug.LogError("没有任务数据 大概率是服务器没有下发任务数据 SC_TaskStepContentPushReq");
            return;
        }

        List<SC_TaskStepContentItem> items = data.content.ToList();
        if (items.Count == 0) return;

        Debug.Log("items " + items.Count + " index " + index);
        if (index >= items.Count)
        {
            return;
        }

        SC_TaskStepContentItem item = items[index];
        List<TipsItem> tips = item.tips.ToList();
        tipItems = tips;
        _tipsCount = tips.Count;
        if (tips.Count == 0)
        {
            ui.comTaskProgress.comPrompt1.com.visible = false;
            ui.comTaskProgress.comPrompt2.com.visible = false;
            ui.comTaskProgress.comPrompt3.com.visible = false;
            ui.comTaskProgress.grpDetail.height = 130;
            // ui.comTaskProgress.grpDetail.EnsureBoundsCorrect();
            ui.comTaskProgress.grpContent.EnsureBoundsCorrect();
        }
        else if (tips.Count > 1)
        {
            SetContent(tips[0].text, _promptExtLeft);
            SetContent(tips[1].text, _promptExtRight);
            ui.comTaskProgress.comPrompt1.com.visible = true;
            ui.comTaskProgress.comPrompt2.com.visible = true;
            ui.comTaskProgress.comPrompt3.com.visible = false;
            ui.comTaskProgress.grpDetail.height = 215;
            // ui.comTaskProgress.grpDetail.EnsureBoundsCorrect();
            ui.comTaskProgress.comPrompt1.buttonPrompt.container.EnsureBoundsCorrect();
            ui.comTaskProgress.comPrompt2.buttonPrompt.container.EnsureBoundsCorrect();
            ui.comTaskProgress.grpContent.EnsureBoundsCorrect();
        }
        else
        {
            SetContent(tips[0].text, _promptExtMid);
            ui.comTaskProgress.comPrompt1.com.visible = false;
            ui.comTaskProgress.comPrompt2.com.visible = false;
            ui.comTaskProgress.comPrompt3.com.visible = true;
            ui.comTaskProgress.grpDetail.height = 215;
            // ui.comTaskProgress.grpDetail.EnsureBoundsCorrect();
            ui.comTaskProgress.comPrompt3.buttonPrompt.container.EnsureBoundsCorrect();
            ui.comTaskProgress.grpContent.EnsureBoundsCorrect();
        }

        var chatModel = GetModel<ChatModel>(ModelConsts.Chat);
        if (chatModel.curRoundId > 1)
            AdjustPanel();
    }

    protected override void AdjustDetailSize()
    {
        ui.comTaskProgress.grpFlowHelper.EnsureBoundsCorrect();
        // ui.comTaskProgress.grpDetail.EnsureBoundsCorrect();
        ui.comTaskProgress.grpContent.EnsureBoundsCorrect();
        var flowTopPos = ui.com.LocalToGlobal(new Vector2(ui.spFlowHelp.x, ui.spFlowHelp.y));
        var offsetY = ui.com.GlobalToLocal(new Vector2(0, Screen.height)).y - ui.com.GlobalToLocal(flowTopPos).y;
        ui.comTaskProgress.com.height = ui.comTaskProgress.grpContent.height + offsetY + 32 + 30;// 32固定间隔，30修正尺寸
        // ui.comTaskProgress.com.height = ui.comTaskProgress.imgBg.height;
        ui.comTaskProgress.grpContent.y = 32;
    }
    
    private void AdjustPanel()
    {
        float pos = ui.com.LocalToGlobal(new Vector2(0, ui.comTaskProgress.com.y)).y - 20;
        // VFDebug.LogError("  "+ui.comTaskProgress.com.y+"   "+pos);
        controller.NotifyChatPageChangeSize(pos);
    }

    private void SetContent(string s, TextFieldExtension tfExt)
    {
        var textContent = new RichContent()
        {
            Type = RichContentType.Text,
            Content = s,
            Color = Color.white,
            IsLast = true,
        };
        tfExt.Clear();
        tfExt.AppendContent(textContent).Display(ShowMode.Normal);
    }

    //绑定事件
    protected override string[] ListNotificationInterests()
    {
        return new string[]
        {
            // NotifyConsts.TaskDetailChange,
            NotifyConsts.TaskStatusChange,
            NotifyConsts.ShowTaskPanel,
            NotifyConsts.CloseChatScoreUI
        };
    }

    void ClearUI()
    {
        if (_flowAudioSource != null)
            _flowAudioSource.Stop();
        _tfExtFlow.Reset();
        this.ui.comTaskProgress.comTaskDetail.aniTaskStatus.animationName = "a1";
        this.ui.comTaskProgress.comTaskDetail.tfTaskDetail.text = "";
        this.ui.barTaskProgress.barTaskProgress.value = 0;

        if (popoverComp != null)
        {
            popoverComp.Dispose();
        }

        foreach (var node in nodes)
        {
            if (node.com.visible)
            {
                node.Reset.Play();
                node.com.RemoveEventListeners();
            }
        }

        shouldShowDetail = false;
    }
    //================================评分翻倍外化================================================

    //点击分数进度条
    private void ClickScoreProgress()
    {
        // ui.comScoreProgress.showTipsAnim.Play();
        ui.showTipsAnim.Play();
    }
    
    
    //展示星星进度条
    private void ShowScoreProgress(int deltaNum)
    {
        // deltaNum = 300;
        ui.comScoreProgress.spineScoreProgress.animationName = "1";
        float rate = ((float)deltaNum + _curScore) / (float)_totalScore;
        rate = rate > 1 ? 1 : rate;
        float movePosX = _initSpineProgressPos.x + rate* _scoreProgressLength;
        GTweener tweener = ui.comScoreProgress.spineScoreProgress.TweenMoveX(movePosX, 1f);
        //位置第一次超过了星星就开始发光
        tweener.OnUpdate
        (() => {
            // 检查当前进度是否达到下一个进度点
            Vector2 progressGlobalPos = ui.comScoreProgress.spineScoreProgress.parent.LocalToGlobal(ui.comScoreProgress.spineScoreProgress.position);
            Vector2 localGrpNumPos = ui.grpNum.parent.GlobalToLocal(progressGlobalPos);
            ui.grpNum.position = new Vector2(localGrpNumPos.x - ui.grpNum.width/2,ui.grpNum.position.y);
            PorgressStarCom progressStar = null;
            switch (_currentMilestone)
            {
                case 0:
                    progressStar = ui.comStar1;
                    break;
                case 1:
                    progressStar = ui.comStar2;
                    break;
                case 2:
                    progressStar = ui.comStar3;
                    break;
            }
            
            if (progressStar != null)
            {
                Vector2 starGlobalPos = progressStar.com.parent.LocalToGlobal(progressStar.com.position);
                if (Math.Round(progressGlobalPos.x, 4) >= Math.Round(starGlobalPos.x, 4))
                {
                    _currentMilestone++;
                    progressStar.purpleBg.visible = true;
                    progressStar.spineStar.animationName = "2";
                    // progressStar.spineStar.spineAnimation.AnimationState.SetAnimation(0,"2", false).Complete +=
                    //     (t) =>
                    //     {
                    //
                    //         progressStar.spineStar.spineAnimation.AnimationState.ClearListenerNotifications();
                    //         //progressStar.spineStar.animationName = "3";
                    //         progressStar.purpleBg.visible = true;
                    //     };
                }
            }
        });
        tweener.OnComplete
        (() => {
            // 在Tween动画结束后执行一些处理逻辑
            ui.comScoreProgress.spineScoreProgress.animationName = "2";
            _curScore += deltaNum;
            // ui.comScoreProgress.tfScore.text = _curScore.ToString();
            ui.tfScore.text = _curScore.ToString();
            // ui.comScoreProgress.showTipsAnim.Play();
            ui.showTipsAnim.Play();
        });

    }

    
    //展示翻倍动画
    private IEnumerator ShowScoreTimesAnim(PB_TaskStepScore stepScore)
    {
        ui.comScore.com.scale = new Vector2(0.4f,0.4f);
        Vector2 scoreInitPos = controller.bubbleScorePos;
        
        scoreInitPos = ui.comScore.com.parent.GlobalToLocal(scoreInitPos);
        //最后加10是打个补丁
        scoreInitPos = new Vector2(scoreInitPos.x - ui.comScore.com.width * 0.4f,(scoreInitPos.y - ui.comScore.com.height * 0.4f)+10);
        // stepScore = new PB_TaskStepScore();
        // stepScore.score = 88;
        List<PB_TaskStepUnitScore> unitScores = stepScore.units.ToList();
        ui.comScore.com.rotation = -10;
        ui.comScore.com.position = new Vector2(scoreInitPos.x - ui.comScore.com.width*0.6f,scoreInitPos.y + ui.comScore.com.height*0.6f);
        ui.comScore.com.position = scoreInitPos;
        ui.comScore.com.alpha = 0.5f;
        ui.comScore.com.visible = true;
        ui.imgMask.visible = true;
        int score = 0;
        int countScore = stepScore.score;
        if (unitScores != null)
        {
            
            Debug.Log(""+unitScores.Count+" count "+_tipsCount);
            for (int i = 0; i < unitScores.Count; i++)
            {
                PB_TaskStepUnitScore taskStepUnitScore = unitScores[i];
                //发音评测
                if (taskStepUnitScore.score_reason_type == PB_TaskStepUnitScoreReasonType
                        .PB_TaskStepUnitScoreReasonType_PronunciationAssessment)
                {
                    score = taskStepUnitScore.score;
                    ui.comScore.com.TweenScale(Vector2.one, _scoreTime);
                    ui.comScore.com.TweenRotate(0, _scoreTime);
                    ui.comScore.com.TweenFade(1, _scoreTime/2);
                    ui.comScore.tfScore.text = "<font color=#FF69F0,#C107DD>"+score+"</font>";
                    //ui.tfScore.TweenScale(Vector2.one, scoreTime);
                    Vector2 movePos = new Vector2(ui.centerPos.position.x - ui.comScore.imgScore.width/2,ui.centerPos.position.y - ui.comScore.imgScore.height/2);
                    ui.comScore.com.TweenMove(movePos,_scoreTime);

                    yield return new WaitForSeconds(_scoreTime+0.01f);

                }
                //播放动画改变颜色 动画结束后播放下一个动画同时隐藏x2
                else if (taskStepUnitScore.score_reason_type == PB_TaskStepUnitScoreReasonType
                              .PB_TaskStepUnitScoreReasonType_TaskGoal||
                         taskStepUnitScore.score_reason_type == PB_TaskStepUnitScoreReasonType.PB_TaskStepUnitScoreReasonType_Tips)
                {
                    scoreTimesCom scoreTimesCom = null;
                    GImage gImage = null;
                    GLoader3D loader3D = null;
                    if (taskStepUnitScore.score_reason_type == PB_TaskStepUnitScoreReasonType
                            .PB_TaskStepUnitScoreReasonType_TaskGoal)
                    {
                        gImage = ui.comTaskProgress.comTaskDetail.imgTaskBg;
                        scoreTimesCom = ui.comTaskProgress.comTaskDetail.comScoreTimes;
                    }
                    else if (taskStepUnitScore.tips_status.tips_idx == 0)
                    {

                        if (_tipsCount > 1)
                        {
                            VFDebug.Log("comPrompt1 ");
                            gImage = ui.comTaskProgress.comPrompt1.imgTaskBg;
                            loader3D = ui.comTaskProgress.comPrompt1.imgUsed;
                            scoreTimesCom = ui.comTaskProgress.comPrompt1.comScoreTimes;
                        }
                        else
                        { 
                            VFDebug.Log("comPrompt3 ");
                            gImage = ui.comTaskProgress.comPrompt3.imgTaskBg;
                            loader3D = ui.comTaskProgress.comPrompt3.imgUsed;
                            scoreTimesCom = ui.comTaskProgress.comPrompt3.comScoreTimes;
                        }
                        
                    }
                    else if (taskStepUnitScore.tips_status.tips_idx == 1)
                    {
                        VFDebug.Log(" comPrompt2 ");
                        gImage = ui.comTaskProgress.comPrompt2.imgTaskBg;
                        loader3D = ui.comTaskProgress.comPrompt2.imgUsed;
                        scoreTimesCom = ui.comTaskProgress.comPrompt2.comScoreTimes;
                    }
                    
                    if(scoreTimesCom == null || gImage == null)
                        continue;
                    //任务描述上面先显示绿色 在显示x2 在飞到中间 
                    Vector2 initTimesPos = scoreTimesCom.com.position;
                    scoreTimesCom.tfTimes.text = "x"+(i+1);
                    VFDebug.Log(" text "+ scoreTimesCom.tfTimes.text);
                    SoundManger.instance.PlayUI("score_double");
                    scoreTimesCom.com.visible = true;
                    Color nowColor;
                    ColorUtility.TryParseHtmlString(ProgressColorEnum.used, out nowColor);
                    gImage.color = nowColor;
                    if (loader3D != null)
                        loader3D.visible = true;
                    
                    Vector2 centerGlobal = ui.com.LocalToGlobal(ui.centerPos.position);
                    //分数飞到中间厚后消息
                    Vector2 movePos = scoreTimesCom.com.parent.GlobalToLocal(centerGlobal);
                    
                    movePos = new Vector2(movePos.x - scoreTimesCom.com.width / 2, movePos.y - scoreTimesCom.com.height/2);
                    scoreTimesCom.com.TweenMove(movePos, _scoreTime);
                    yield return new WaitForSeconds(_scoreTime+0.05f);
                    //飞到中间后消失  分数显示x2并放大
                    scoreTimesCom.com.position = initTimesPos;
                    scoreTimesCom.com.visible = false;

                    ui.comScore.tfTimes.visible = true;
                    ui.comScore.tfTimes.text = "<font color=#FF69F0,#C107DD>"+"x"+(i+1)+"</font>";
                    // ui.tfTimes.scale = new Vector2(i+1,i+1);
                    Vector2 curScale = ui.comScore.com.scale;
                    
                    ui.comScore.com.TweenScale(curScale*1.15f, _scoreTime/2);
                    yield return new WaitForSeconds(_scoreTime/2);
                    
                    ui.comScore.com.TweenScale(curScale*1.1f, _scoreTime/2);
                    yield return new WaitForSeconds(_scoreTime/2);
                    //scoreTimesCom.tweenTimes.Play();
                    yield return new WaitForSeconds(0.05f);

                }
            }
        }

        if (rollNumbersCoroutine != null)
        {
            Timers.inst.StopCoroutine(rollNumbersCoroutine);
            rollNumbersCoroutine = null;
        }

        rollNumbersCoroutine = Timers.inst.StartCoroutine(RollNumbers(score,countScore));
    }
    
    //数字滚动 播完所有动画才能通知挑战状态机里面显示next icon
    private IEnumerator RollNumbers(int startNumber,int endNumber)
    {
        float timer = 0f;
        ui.comScore.tfTimes.visible = false;
        VFDebug.Log("RollNumbers "+_scoreTime);
        SoundManger.instance.PlayUI("score_total");
        while (timer < _scoreTime)
        {
            int currentNumber = (int)(Mathf.Lerp(startNumber, endNumber, timer / _scoreTime));

            // 在这里将当前数字应用到你的UI文本或其他显示方式
            ui.comScore.tfScore.text =  "<font color=#FF69F0,#C107DD>"+currentNumber+"</font>";
            timer += Time.deltaTime;
            // VFDebug.Log("RollNumbers11  "+Time.deltaTime);
            yield return null;
        }
        ui.comScore.tfScore.text =  "<font color=#FF69F0,#C107DD>"+endNumber+"</font>";

        VFDebug.Log("PlayStarFlyAnim");
        
        SetStarFlySpineModel(endNumber);

    }

    //播放星星飞上进度条的动画
    private IEnumerator PlayStarFlyAnim(int score)
    {
        var spineLoader = this.ui.comScore.spineLoader as SpinePanelExtension;
        ui.comScore.spineLoader.visible = true;
        Transform go = spineLoader.go.transform.Find("root/root/end");
        GLoader3D gLoader3D = ui.comScoreProgress.spineScoreProgress;
        Vector2 screenPos = ui.comScoreProgress.spineScoreProgress.parent.LocalToGlobal(new Vector2(gLoader3D.position.x,gLoader3D.position.y+gLoader3D.height/2));
        // Debug.LogError("screenPos "+screenPos);
        screenPos.y = Screen.height - screenPos.y;
        go.position = StageCamera.main.ScreenToWorldPoint(screenPos);
        spineLoader.PlayWithCallback("animation", () =>
        {
            spineLoader.ClearModel();
          
        });
        yield return new WaitForSeconds(1.333f);
        SoundManger.instance.PlayUI("star_fly_progress");
        ui.comScore.com.visible = false;
        ui.imgMask.visible = false;
        var chatState = GetController<ChatController>(ModelConsts.Chat).GetChatState();
        (chatState as ChatStatePlayerChallenge).ShowNextIcon();
        ShowScoreProgress(score);
        spineLoader.visible = false;
        
    }
    
    private void SetStarFlySpineModel(int score)
    {
        VFDebug.Log("SetStarFlySpineModel ");
        var spineLoader = this.ui.comScore.spineLoader as SpinePanelExtension;
        spineLoader.fill = SpinePanelFill.None;
        spineLoader.SetModel(ResUtils.GetSpinePath("starFly"), () =>
        {
            //骨骼的父结点
            Transform go = spineLoader.go.transform.Find("root/root/end");
            Vector2 screenPos = ui.btnScoreProgress.LocalToGlobal(ui.btnScoreProgress.position);
            screenPos.y = Screen.height - screenPos.y;
            go.position = StageCamera.main.ScreenToWorldPoint(screenPos);
            if (starFlyCoroutine != null)
            {
                Timers.inst.StopCoroutine(starFlyCoroutine);
                starFlyCoroutine = null;
            }
            spineLoader.loop = false;
            VFDebug.Log("SetStarFlySpineModel SetModel");
            starFlyCoroutine = Timers.inst.StartCoroutine(PlayStarFlyAnim(score));
        });
        
    }
    
    //初始化进度条颜色
    private void InitTaskProgressColor()
    {
        Color nowColor;
        ColorUtility.TryParseHtmlString(ProgressColorEnum.Init, out nowColor);
        ui.comTaskProgress.comTaskDetail.imgTaskBg.color = nowColor;
        ui.comTaskProgress.comPrompt1.imgTaskBg.color = nowColor;
        ui.comTaskProgress.comPrompt2.imgTaskBg.color = nowColor;
        ui.comTaskProgress.comPrompt3.imgTaskBg.color = nowColor;

        ui.comTaskProgress.comPrompt1.imgUsed.visible = false;
        ui.comTaskProgress.comPrompt2.imgUsed.visible = false;
        ui.comTaskProgress.comPrompt3.imgUsed.visible = false;
    }

    void ClearData()
    {
        this.nodesPercent.Clear();
        this.nodes.Clear();
        isWaitScoreUIClose = false;
        _totalScore = 0;
        _curScore = 0;
        _currentMilestone = 0;
        if (scoreTimesCoroutine != null)
        {
            Timers.inst.StopCoroutine(scoreTimesCoroutine);
            scoreTimesCoroutine = null;
        }
        
        if (rollNumbersCoroutine != null)
        {
            Timers.inst.StopCoroutine(rollNumbersCoroutine);
            rollNumbersCoroutine = null;
        }
        
        if (starFlyCoroutine != null)
        {
            Timers.inst.StopCoroutine(starFlyCoroutine);
            starFlyCoroutine = null;
        }
        
        if (!string.IsNullOrEmpty(timerPorgress))
        {
            TimerManager.instance.UnRegisterTimer(timerPorgress);
            timerPorgress = string.Empty;
        }
        
        //清除动画
        this.ui.comTaskProgress.comTaskDetail.aniTaskStatus.spineAnimation.AnimationState.ClearListenerNotifications();
        // this.ui.comScore.spineStarFly.spineAnimation.AnimationState.ClearListenerNotifications();
        
        ui.comStar1.spineStar.spineAnimation.AnimationState.ClearListenerNotifications();
        ui.comStar2.spineStar.spineAnimation.AnimationState.ClearListenerNotifications();
        ui.comStar3.spineStar.spineAnimation.AnimationState.ClearListenerNotifications();
    }
    
    protected override void OnHide()
    {
        base.OnHide();
        this.ClearUI();
        this.ClearData();
    }

    private void InitTextExt()
    {
        _promptExtLeft = ui.comTaskProgress.comPrompt1.buttonPrompt.tfContent as TextFieldExtension;
        _promptExtRight = ui.comTaskProgress.comPrompt2.buttonPrompt.tfContent as TextFieldExtension;
        _promptExtMid = ui.comTaskProgress.comPrompt3.buttonPrompt.tfContent as TextFieldExtension;

        InitTextExt(_promptExtLeft, ui.comTaskProgress.comPrompt1);
        InitTextExt(_promptExtRight, ui.comTaskProgress.comPrompt2);
        InitTextExt(_promptExtMid, ui.comTaskProgress.comPrompt3);
    }

    private void InitTextExt(TextFieldExtension tfExt, ProgressPromptVoiceCom com)
    {
        tfExt.SetOutline(0.1f, 0.2f, Color.white);
        tfExt.SetFormat(Color.white, 28);
        tfExt.SetMaxRow(2);

        tfExt.OnTypingEffectEnd += () =>
        {
            tfExt.ClearContent();
            tfExt.Display(ShowMode.Normal);
            com.buttonPrompt.ctrlImg.selectedPage = "default";
        };
    }

    private void ClickPrompt(TextFieldExtension tfExt, ProgressPromptVoiceCom com)
    {
        PlayTip(tfExt, com);
    }

    private void PlayTip(TextFieldExtension txtField, ProgressPromptVoiceCom promptPos)
    {
        TipsItem tipItem = new TipsItem();
        if (tipItems.Count == 1)
        {
            tipItem = tipItems[0];
        }
        else if (tipItems.Count > 1)
        {
            if (txtField == _promptExtLeft)
            {
                tipItem = tipItems[0];
                
                DataDotClickDialogueChunk1 chunk = new DataDotClickDialogueChunk1();
                chunk.Dialogue_id = DataDotMgr.GetDialogId();
                chunk.chunk_text = tipItems[0].mother_tongue_text;
                DataDotMgr.Collect(chunk);
            }
            if (txtField == _promptExtRight)
            {
                tipItem = tipItems[1];

                DataDotClickDialogueChunk2 chunk = new DataDotClickDialogueChunk2();
                chunk.Dialogue_id = DataDotMgr.GetDialogId();
                chunk.chunk_text = tipItems[1].mother_tongue_text;
                DataDotMgr.Collect(chunk);
            }
        }
        long tts_record_id = tipItem.tts_id;
        TimelineManager.instance.GetTimelineForShadowingQuestionAsync(tts_record_id,
            (audioTranscript) => TTSTimerLineCallBack(tts_record_id, txtField, audioTranscript, promptPos),
            ()=>ErrorTTSTimerLineCallBack(tts_record_id, txtField, promptPos));
        ShowTransPopover(tipItem.mother_tongue_text, promptPos);
    }

    private void TTSTimerLineCallBack(long tts_record_id, TextFieldExtension textFieldExtension,
        List<PB_AudioTranscript> audioTranscript, ProgressPromptVoiceCom promptPos)
    {
        RichContentUtil.AddFollowAvatarTimeline(textFieldExtension, audioTranscript);
        ASRManager.instance.StopASRWithCancel();
        GetController<ChatController>(ModelConsts.Chat).CancelRecordUI();
        TTSManager.instance.PlayTTS(tts_record_id);

        textFieldExtension.ClearContent();
        textFieldExtension.Play(ShowMode.Follow);
        promptPos.buttonPrompt.ctrlImg.selectedPage = "playing";

    }
    
    private void ErrorTTSTimerLineCallBack(long tts_record_id, TextFieldExtension textFieldExtension, ProgressPromptVoiceCom promptPos)
    {
        ASRManager.instance.StopASRWithCancel();
        GetController<ChatController>(ModelConsts.Chat).CancelRecordUI();
        TTSManager.instance.PlayTTS(tts_record_id);
        textFieldExtension.ClearContent();
        textFieldExtension.Play(ShowMode.Normal);
        promptPos.buttonPrompt.ctrlImg.selectedPage = "playing";
        ChatModel chatModel = GetModel<ChatModel>(ModelConsts.Chat);
        DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
        dataDotCutApiCase.Dialogue_id = chatModel.dialogId;
        dataDotCutApiCase.Task_id = this.GetController<ChatController>(ModelConsts.Chat).curTaskId;
        dataDotCutApiCase.Dialogue_step = chatModel.GetStepIdByRoundId(chatModel.curRoundId);
        dataDotCutApiCase.Task_mode = (int)chatModel.chatMode;
        dataDotCutApiCase.Api_address = "CS_GetTTSAudioTranscriptReq";
        dataDotCutApiCase.Cut_mode = 3;
        DataDotMgr.Collect(dataDotCutApiCase);

    }

    public void DismissWithAnimation()
    {
        if (_chatMode == PB_DialogMode.Challenge)
        {
            ui.Out.Play(() => { });
        }
        ui.barTaskProgress.com.visible = false;
        HidePanel();
    }
    
    private void ShowTransPopover(string trans, ProgressPromptVoiceCom promptPos)
    {
        NewWordComponent t = UIPackage.CreateObject("common", "NewWordCom") as NewWordComponent;
        popoverComp = t;

        var comp = promptPos.com;
        comp.AddChild(t);
        var pos = t.position;
        t.SetPosition(comp.size.x * 0.5f, 0, 0);
        t.touchable = false;
        // t.SetSize(comp.size.x, 0);
        t.SetWord(trans);
    }

    private void TouchBeginSliderDown(EventContext context)
    {
        context.CaptureTouch();
        _startDowmSliderPosY = context.inputEvent.y;
    }

    private void TouchEndSliderDown(EventContext context)
    {
        context.CaptureTouch();
        if (context.inputEvent.y - _startDowmSliderPosY > 10)
        {
            if (_curFlowState == FlowHelpState.Recording || _curFlowState == FlowHelpState.SendAble)
            {
                CloseFLowHelp(true);
            }
            else
            {
                HidePanel();
            }
        }
        _startDowmSliderPosY = 0;
    }

    private void TouchBeginSliderUp(EventContext context)
    {
        context.CaptureTouch();
        _startUpSliderPosY = context.inputEvent.y;
    }

    private void TouchEndSliderUp(EventContext context)
    {
        context.CaptureTouch();
        if (_startUpSliderPosY - context.inputEvent.y > 10)
        {
            if (_curFlowState != FlowHelpState.WaitResult && _curFlowState != FlowHelpState.PlayResult && _curFlowState != FlowHelpState.Recording && _curFlowState != FlowHelpState.SendAble)
            {
                ShowPanel();

                //新埋点：滑动下方目标展示
                DataDotSwtichTaskGoalShow dot = new DataDotSwtichTaskGoalShow();
                dot.Dialogue_id = DataDotMgr.GetDialogId();
                DataDotMgr.Collect(dot);
            }
        }

        if (_startUpSliderPosY - context.inputEvent.y < -10)
        {
            if (_curFlowState == FlowHelpState.Recording || _curFlowState == FlowHelpState.SendAble)
            {
                CloseFLowHelp();
            }
            else if (_curFlowState == FlowHelpState.ShowResult || _curFlowState == FlowHelpState.Idle || _curFlowState == FlowHelpState.UnUsed)
            {
                HidePanel();

                //新埋点：滑动下方目标隐藏
                DataDotSwtichTaskGoalHide dot = new DataDotSwtichTaskGoalHide();
                dot.Dialogue_id = DataDotMgr.GetDialogId();
                DataDotMgr.Collect(dot);
            }
        }
        _startUpSliderPosY = 0;
    }
    
    #endregion
}

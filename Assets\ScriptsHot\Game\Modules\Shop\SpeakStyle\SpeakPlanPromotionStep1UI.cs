/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/16 15:50:42 星期四
# 功能：Nothing
****************************************************
*/

using FairyGUI;
using Firebase.Analytics;
using Game;
using Modules.DataDot;
using Msg.economic;
using ScriptsHot.Game.Modules.Settlement;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Shop
{
    public enum SpeakShowType
    {
        None,
        Speak,
        Homepage,
        Explore
    }

    public class SpeakPlanPromotionStep1UI: ShopUIBase<UIBind.Shop.SpeakPlanPromotionStep1Panel>
    {
        public SpeakPlanPromotionStep1UI(string name) : base(name)
        {
        }
        public override string uiLayer => UILayerConsts.Top; //主UI层

        protected override bool isFullScreen => true;

        private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);
        
        private ShopModel _shopModel => GetModel<ShopModel>(ModelConsts.Shop);

        private string _cfgTop;

        private enum PageState
        {
            normal = 0,
            premium_month,
            premium_year,
        }

        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(this.ui.btnExit.onClick, OnBtnExitClicked);
            AddUIEvent(this.ui.comPlanPromotionContent.btnNext.onClick, OnbBtnNextClicked);
            InitUI();
        }
        private SpeakShowType _type = SpeakShowType.None;

        protected override void OnShow()
        {
            // 250327 中国版过审修改
            if (AppConst.IsCN)
            {
                Hide();
                return;
            }

            _type = SpeakShowType.None;
            if (args != null && args.Length > 0)
            {
                _type = (SpeakShowType)args[0];
                if (_type == SpeakShowType.Speak || _type == SpeakShowType.Homepage)
                {
                    var dot = new MembershipPage();
                    DataDotMgr.Collect(dot);
                    AFDots.Appear_membership_page();
                    MyFirebaseAnalytics.LogEvent("membership");
                }
            }
            ui.comPlanPromotionContent.Cutin.Play();
            
            PageState pageState = PageState.normal;
            if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Subscribing && _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.MonthPremium && _mainModel.incentiveData.homepage_economic_info.member_info.is_member)
            {
                pageState = PageState.premium_month;
            }
            else if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Subscribing &&
                     (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.YearPremium ||
                      _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.QuarterPremium ||
                      _mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.LifetimePremium)&&
                     _mainModel.incentiveData.homepage_economic_info.member_info.is_member)
            {
                pageState = PageState.premium_year;
            }
            else if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.FreeTrial && _mainModel.incentiveData.homepage_economic_info.member_info.is_member)
            {
                pageState = PageState.premium_year;
            }

            if (pageState != PageState.normal)
            {
                if (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.LifetimePremium)
                {
                    var str  = I18N.inst.MoStr("ui_shop_lifetime_12");
                    ui.comPlanPromotionContent.tfSubscribeEndtime2.text = str;
                }
                else
                {
                    var str = string.Format(I18N.inst.MoStr("ui_plan_instruction_desc13"),
                        GetModel<ShopModel>(ModelConsts.Shop).GetMemberExpiredTime().ToString(I18N.inst.MoStr("ui_plan_promotion_desc20")));
                    ui.comPlanPromotionContent.tfSubscribeEndtime2.text = str;
                }
            }
            
            if(_shopModel.IsPayWallType)
                ui.comPlanPromotionContent.tfbtnPromotion.SetKeyArgs("ui_shop_discount_01",
                    $"{ShopUIUtil.GetSaleOffValue(_shopModel.PayWallData.subscription_infos[0])}%");
            else
                ui.comPlanPromotionContent.tfbtnPromotion.SetLanguage(LanguageType.MotherTongue, refresh: false).SetKey(
                    _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Retrying? "ui_shop_btn_plan_retry":
                    _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.UnSubscribe? "ui_plan_promotion_desc1"
                        : _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.FreeTrialCanceled || _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Canceled
                            ? "ui_plan_common_btn_stay"
                            : "ui_plan_common_btn_subscribe");

        
            
            ui.comPlanPromotionContent.tfPromotionTitle.SetLanguage(LanguageType.MotherTongue, refresh: false).SetKey(
                _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Retrying
                    ? "ui_planPromotion_desc_plan_retry"
                    : "ui_plan_promotion_desc26");

            if (_type == SpeakShowType.Explore)
            {
                ui.comPlanPromotionContent.tfPromotionTitle.SetLanguage(LanguageType.MotherTongue, refresh: false)
                    .SetKey("ui_shop_paywall_explore_desc");
            }
            if (_shopModel.IsPayWallType)
            {
                ui.comPlanPromotionContent.tfPromotionTitle.SetLanguage(LanguageType.MotherTongue, refresh: false)
                    .SetKeyArgs("ui_pay_wall_premium",
                        $"{ShopUIUtil.GetSaleOffValue(_shopModel.PayWallData.subscription_infos[0])}%");
            }

            if (_mainModel.incentiveData.homepage_economic_info.member_info.member_type == MemberType.LifetimePremium)
            {
                ui.comPlanPromotionContent.tfPromotionTitle.SetLanguage(LanguageType.MotherTongue, refresh: false)
                    .SetKeyArgs("ui_shop_lifetime_5");
            }
            
            
            ui.comPlanPromotionContent.tfFreeTrail.SetLanguage(LanguageType.MotherTongue, refresh: false).SetKey("ui_plan_promotion_desc24");
            
            ui.comPlanPromotionContent.state.selectedIndex = (int)pageState;
            
            if (_shopModel.TryGetMemberTypeByName(ShopController.MEMBER_TYPE_QUARTER, out var cfg_quarterly))
            {
                _cfgTop = ShopController.MEMBER_TYPE_QUARTER;
            }
            else if (_shopModel.TryGetMemberTypeByName(ShopController.MEMBER_TYPE_YEAR, out var cfg_yearly))
            {
                _cfgTop = ShopController.MEMBER_TYPE_YEAR;
            }
        }

        private void InitUI()
        {
            ui.comPlanPromotionContent.tfPromotionDesc1.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("item_desc_i0003");
            ui.comPlanPromotionContent.tfPromotionDesc2.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_promotion_desc22");
            ui.comPlanPromotionContent.tfPromotionDesc3.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_shop_paywall_umlimited_conversations");
            ui.comPlanPromotionContent.tfBtnUpgrade.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_common_btn_upgrade");
        }

        private void OnbBtnNextClicked()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);
            if (_shopModel.IsPayWallType)
            {
                DoNext();
            }
            else if (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus == SubscribeStatus.Retrying)
            {
                DoUpdate();
            }
            else if (ui.comPlanPromotionContent.state.selectedIndex == (int)PageState.normal && _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus != SubscribeStatus.UnSubscribe)
            {
                //250424 业务调整为暂时下架直接在此页订阅，进入订阅比较页流程
                //DoSubscribe();
                DoNext();
            }
            else if (ui.comPlanPromotionContent.state.selectedIndex == (int)PageState.premium_month)
            {
                DoUpgrade();
            }
            else
            {
                DoNext();
            }
        }
        
        private void OnBtnExitClicked()
        {
            //新埋点：未免费订阅过的用户，点击开始体验后，在详细描述页点击退出
            DataDotDiamondFreeQuit dot = new DataDotDiamondFreeQuit();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            DataDotMgr.Collect(dot);

            bool needHide = true;
            if (args != null && args.Length > 0)
            {
                var source = (int)args[0];
                if (source == 4)
                {
                    needHide = false;
                    GetController<SettlementController>(ModelConsts.Settlement).ShowNextView(() => { Hide(); });
                }
            }
            if (needHide) {
                
                if (_type == SpeakShowType.Explore)
                {
                    // Notifier.instance.SendNotification(NotifyConsts.ExploreBGMopen, true);
                }
                if (_shopModel.IsPayWallType)
                {
                    _shopModel.ClearPayWallTye();
                }
                
                Hide();
            }
        }

        private void DoNext()
        {
            GetUI<SpeakPlanSelectUI>(UIConsts.SpeakPlanSelectUI).Show(args);
            Hide();
        }

        private void DoUpdate()
        {
            GetController<ShopController>(ModelConsts.Shop).OpenSubscriptionUpdate();
            Hide();
        }
        
        private void DoUpgrade()
        {
            var cfg = _shopModel.GetMemberTypeByName(_cfgTop);
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            PurchasingManager.instance.StartPurchasing(cfg.product_id, PB_ProductType.PB_ProductType_Subscribe, OnBuySuccess, OnBuyFail);
            DataDotSubscriptionPurchase dot = new DataDotSubscriptionPurchase();
            
            
            dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
            dot.clicked_member_type = "YearPremium";
            dot.subscribe_price = cfg.price_in_cents;
            dot.price_currency = AppStoreInfoProvider.GetCountryCode();
            dot.source_page = nameof(PayWallDotHelper.LastSourcePage);
            DataDotMgr.Collect(dot);
            
           
            
            product_id = cfg.product_id;
            product_type = PB_ProductType.PB_ProductType_Subscribe.ToString();
        }

    }
}
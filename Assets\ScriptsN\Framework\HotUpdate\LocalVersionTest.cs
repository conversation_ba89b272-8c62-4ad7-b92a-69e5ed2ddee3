using UnityEngine;

/// <summary>
/// 本地版本管理测试脚本
/// 测试非强更版本的本地保存和使用逻辑
/// </summary>
public class LocalVersionTest : MonoBehaviour
{
    [Header("本地版本测试")]
    public bool runTest = false;
    
    [Header("测试参数")]
    public string packageName = "main";
    public string testVersion = "250101023000";
    
    private void Start()
    {
        if (runTest)
        {
            RunLocalVersionTests();
        }
    }

    /// <summary>
    /// 运行本地版本测试
    /// </summary>
    private void RunLocalVersionTests()
    {
        Debug.Log("=== 本地版本管理测试开始 ===");
        
        // 测试1：保存和读取本地版本
        TestSaveAndLoadLocalVersion();
        
        // 测试2：版本比较逻辑
        TestVersionComparisonLogic();
        
        // 测试3：模拟完整的版本升级流程
        TestCompleteUpgradeFlow();
        
        Debug.Log("=== 本地版本管理测试完成 ===");
    }

    /// <summary>
    /// 测试保存和读取本地版本
    /// </summary>
    private void TestSaveAndLoadLocalVersion()
    {
        Debug.Log("--- 测试保存和读取本地版本 ---");
        
        // 清除之前的记录
        string key = $"LocalLatestVersion_{packageName}";
        PlayerPrefs.DeleteKey(key);
        
        // 测试读取不存在的版本
        string emptyVersion = GetLocalLatestVersion(packageName);
        Debug.Log($"读取不存在的版本: {emptyVersion ?? "null"}");
        
        // 保存版本
        SaveLocalLatestVersion(packageName, testVersion);
        
        // 读取保存的版本
        string savedVersion = GetLocalLatestVersion(packageName);
        Debug.Log($"读取保存的版本: {savedVersion}");
        
        // 验证结果
        if (savedVersion == testVersion)
        {
            Debug.Log("✓ 本地版本保存和读取测试通过");
        }
        else
        {
            Debug.LogError("✗ 本地版本保存和读取测试失败");
        }
    }

    /// <summary>
    /// 测试版本比较逻辑
    /// </summary>
    private void TestVersionComparisonLogic()
    {
        Debug.Log("--- 测试版本比较逻辑 ---");
        
        // 模拟场景：强更版本1，非强更版本2，本地已有版本2
        string forceUpdateVersion = "250101022000"; // 强更版本
        string optionalUpdateVersion = "250101023000"; // 非强更版本
        string localVersion = "250101023000"; // 本地已有版本
        
        Debug.Log($"强更版本: {forceUpdateVersion}");
        Debug.Log($"非强更版本: {optionalUpdateVersion}");
        Debug.Log($"本地版本: {localVersion}");
        
        // 比较本地版本和强更版本
        int compareWithForce = CompareVersionNumbers(localVersion, forceUpdateVersion);
        Debug.Log($"本地版本 vs 强更版本: {compareWithForce} (1表示本地更新)");
        
        // 比较本地版本和非强更版本
        int compareWithOptional = CompareVersionNumbers(localVersion, optionalUpdateVersion);
        Debug.Log($"本地版本 vs 非强更版本: {compareWithOptional} (0表示相等)");
        
        // 判断应该使用哪个版本
        string versionToUse = forceUpdateVersion;
        if (compareWithForce > 0 && compareWithOptional >= 0)
        {
            versionToUse = localVersion;
            Debug.Log("✓ 应该使用本地版本");
        }
        else
        {
            Debug.Log("✓ 应该使用强更版本");
        }
        
        Debug.Log($"最终使用版本: {versionToUse}");
    }

    /// <summary>
    /// 测试完整的版本升级流程
    /// </summary>
    private void TestCompleteUpgradeFlow()
    {
        Debug.Log("--- 测试完整的版本升级流程 ---");
        
        // 清除本地记录，模拟全新用户
        string key = $"LocalLatestVersion_{packageName}";
        PlayerPrefs.DeleteKey(key);
        
        // 第一次启动：强更到版本1，非强更到版本2
        Debug.Log("第一次启动:");
        SimulateAppStart("250101022000", "250101023000", null);
        
        // 第二次启动：应该直接使用本地的版本2
        Debug.Log("第二次启动:");
        SimulateAppStart("250101022000", "250101023000", "250101023000");
        
        // 第三次启动：服务器有新的非强更版本3
        Debug.Log("第三次启动（有新的非强更版本）:");
        SimulateAppStart("250101022000", "250101024000", "250101023000");
    }

    /// <summary>
    /// 模拟应用启动过程
    /// </summary>
    private void SimulateAppStart(string serverForceVersion, string serverOptionalVersion, string localVersion)
    {
        Debug.Log($"  服务器强更版本: {serverForceVersion}");
        Debug.Log($"  服务器非强更版本: {serverOptionalVersion}");
        Debug.Log($"  本地版本: {localVersion ?? "无"}");
        
        // 模拟版本选择逻辑
        string actualVersionToUse = serverForceVersion;
        bool hasOptionalUpdate = false;
        
        if (!string.IsNullOrEmpty(localVersion))
        {
            if (CompareVersionNumbers(localVersion, serverForceVersion) > 0)
            {
                if (CompareVersionNumbers(localVersion, serverOptionalVersion) >= 0)
                {
                    actualVersionToUse = localVersion;
                    Debug.Log($"  → 使用本地版本: {actualVersionToUse}");
                }
            }
        }
        
        // 检查是否需要非强更
        if (CompareVersionNumbers(serverOptionalVersion, actualVersionToUse) > 0)
        {
            hasOptionalUpdate = true;
            Debug.Log($"  → 需要非强更到: {serverOptionalVersion}");
            
            // 模拟非强更下载成功
            SaveLocalLatestVersion(packageName, serverOptionalVersion);
            Debug.Log($"  → 非强更下载完成，保存版本: {serverOptionalVersion}");
        }
        else
        {
            Debug.Log($"  → 无需非强更");
        }
        
        Debug.Log($"  最终使用版本: {actualVersionToUse}");
        Debug.Log("");
    }

    // 复制HotUpdateCore中的方法用于测试
    private string GetLocalLatestVersion(string packageName)
    {
        string key = $"LocalLatestVersion_{packageName}";
        string localVersion = PlayerPrefs.GetString(key, "");
        return string.IsNullOrEmpty(localVersion) ? null : localVersion;
    }

    private void SaveLocalLatestVersion(string packageName, string version)
    {
        string key = $"LocalLatestVersion_{packageName}";
        PlayerPrefs.SetString(key, version);
        PlayerPrefs.Save();
    }

    private int CompareVersionNumbers(string version1, string version2)
    {
        if (string.IsNullOrEmpty(version1) || string.IsNullOrEmpty(version2))
        {
            return 0;
        }

        if (long.TryParse(version1, out long v1) && long.TryParse(version2, out long v2))
        {
            if (v1 > v2) return 1;
            if (v1 < v2) return -1;
            return 0;
        }
        else
        {
            return string.Compare(version1, version2, System.StringComparison.Ordinal);
        }
    }

    private void OnGUI()
    {
        if (!runTest) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 400, 200));
        GUILayout.Label("本地版本管理测试", GUI.skin.box);
        
        GUILayout.BeginHorizontal();
        GUILayout.Label("包名:");
        packageName = GUILayout.TextField(packageName, GUILayout.Width(100));
        GUILayout.EndHorizontal();
        
        GUILayout.BeginHorizontal();
        GUILayout.Label("测试版本:");
        testVersion = GUILayout.TextField(testVersion, GUILayout.Width(120));
        GUILayout.EndHorizontal();
        
        if (GUILayout.Button("运行全部测试"))
        {
            RunLocalVersionTests();
        }
        
        if (GUILayout.Button("清除本地版本记录"))
        {
            string key = $"LocalLatestVersion_{packageName}";
            PlayerPrefs.DeleteKey(key);
            PlayerPrefs.Save();
            Debug.Log($"已清除{packageName}的本地版本记录");
        }
        
        if (GUILayout.Button("查看当前本地版本"))
        {
            string version = GetLocalLatestVersion(packageName);
            Debug.Log($"{packageName}的当前本地版本: {version ?? "无"}");
        }
        
        GUILayout.EndArea();
    }
}

﻿
using System;
using Random = UnityEngine.Random;

public class SoundManger
{    
    private static SoundManger _instance = null;

    public class SoundChannel
    {
        public const string UI = "UI";
        public const string Scene = "Scene";
    }
    public static SoundManger instance
    {
        get
        {
            if (_instance == null)
                _instance = new SoundManger();
            return _instance;
        }
    }
    
    public void Play(string channel,string name,bool isLoop = true,float delay = 0f, float volume = -1f)
    {
        GSoundManager.instance.Play(channel,name,isLoop);
    }

    public void StopUI()
    {
        GSoundManager.instance.StopUI();
    }
    
    public void StopBGM()
    {
        GSoundManager.instance.StopBGM();
        
    }
    public void PlayOneShot(string channel, string name,float delay = 0f, float volume = 1f)
    {
        GSoundManager.instance.PlayOneShot(channel,name);
    }

    private static int randomVal = 0;

    public string GetStarX5SoundPath(string id)
    {
        Cfg.T.TbSound.DataMap.TryGetValue(id, out SoundCfg cfg);
        if (cfg != null)
        {
            string[] path = cfg.Path.Split(";");
            if (path.Length > 0)
            {
                return ResUtils.GetStarX5SoundPath(path[randomVal++ % path.Length]);
            }
            else
            {
                VFDebug.LogError("五星题:无此音效");
                return null;
            }
        }
        return null;
    }
    
    public void PlayBGM(string id)
    {
        MusicCfg cfg = Cfg.T.TBMusic.Get(id);
        string url = ResUtils.GetMusicPath(cfg.Path);
        if (url == "") return;
        var channels = GSoundManager.instance.GetChannels();

        GSoundManager.instance.Pause(SoundChannel.Scene,false);
        GSoundManager.instance.Play(SoundChannel.Scene, url, true, 0, cfg.Volume / 100f);
    }

    public void PlayUI(string id)
    {
        SoundCfg cfg =  Cfg.T.TbSound.Get(id);
        string url = ResUtils.GetSoundPath(cfg.Path);
        if (url == "") return;
        var channels = GSoundManager.instance.GetChannels();
            
        if (cfg.IsLoop)
        {
            GSoundManager.instance.Play(SoundChannel.UI,url,cfg.IsLoop,0,cfg.Volume/100f);
        }
        else
        {
            GSoundManager.instance.PlayOneShot(SoundChannel.UI,url,0,cfg.Volume/100f);
        }

        switch (cfg.VibrateType)
        {
            case 0://无震动
                break;
            case 1://短震
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
                break;
            case 2://长震
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Vibrate);
                break;
            case 3://？震
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Peek);
                break;
            case 4://？震
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Nope);
                break;
            case 5:
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Success);
                break;
            case 6:
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Error);
                break;
            case 7:
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Continuous);
                break;
            case 8:
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Warning);
                break;
            default:
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
                break;
        }
    }

}
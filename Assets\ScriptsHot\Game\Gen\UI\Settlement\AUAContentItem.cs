/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Settlement
{
    public partial class AUAContentItem : UIBindT
    {
        public override string pkgName => "Settlement";
        public override string comName => "AUAContentItem";

        public Controller showLine;
        public GTextField tfTitle;
        public GTextField tfContent;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            showLine = com.GetControllerAt(0);
            tfTitle = (GTextField)com.GetChildAt(1);
            tfContent = (GTextField)com.GetChildAt(2);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            showLine = null;
            tfTitle = null;
            tfContent = null;
        }
    }
}
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AppConstExt
{
    //逻辑版本号(代码版本号)，影响后端接口行为,所有协议会携带
    public static string LogicVersion = "0.15.7";//捏脸3.0开始分线后，提升到了0.13.x

    public static string DiscardUrl = "https://discord.com/invite/ANQ4munPzS";

    public static string AgoraSDKAppId = "eb245b5e2465487bba7546471af89d61"; //声网的appid由后端配置决定

#if UNITY_IOS && !UNITY_EDITOR
    public static string grpcClientType = "ios";
#elif UNITY_ANDROID && !UNITY_EDITOR
    public static string grpcClientType = "Android";
#else
    public static string grpcClientType = "editor";
#endif

}

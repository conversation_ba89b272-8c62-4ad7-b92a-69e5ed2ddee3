﻿
using System;
using System.Collections.Generic;
using Google.Protobuf.Collections;
using Msg.basic;
using Msg.dialog_task;
using Msg.explore;
using ScriptsHot.Game.Modules.ChatLogicNew;
using UnityEngine;
using Random = UnityEngine.Random;


public partial class ExploreModel : BaseModel
{
    private const int DescBackCount = 6;
    
    private int _curRoundId = 0;
    public int CurRoundId => _curRoundId;
    /// <summary>
    /// 通过roundId获得Suggestion
    /// </summary>
    private Dictionary<int, SuggestionMsgData> _dicSuggestionMsgData = new();


    /// <summary>
    /// 本轮命中知识点
    /// </summary>
    private Dictionary<long,RepeatedField<string>> _hitKnowledge = new Dictionary<long, RepeatedField<string>>();

    public ExploreModel() : base(ModelConsts.Explore)
    {
    }
    public void AddRoundId()
    {
        _curRoundId++;
    }

    private Dictionary<long, int> _describeBack = new Dictionary<long, int>();
    
    private List<ExploreItemData> _allData = new List<ExploreItemData>();
    public List<ExploreItemData> AllData => _allData;
    public void SetPreLoadDataList(List<PB_PreloadData> preloadDataList)
    {
        Debug.Log($"刷新数据 preloadDataList::{preloadDataList.Count}");
        foreach (var value in preloadDataList)
        {
            ExploreItemData item = new ExploreItemData();
            item.Data = value;
            _allData.Add(item);
            int backValue = Random.Range(1, DescBackCount + 1);
            _describeBack[item.Data.entityId] = backValue;
        }
    }

    public int GetDescribeBack(long entityId)
    {
        if(_describeBack.TryGetValue(entityId, out int value ))
        {
            return value;
        }

        return 1;
    }

    /// <summary>
    /// 获取还没有读取的数据个数
    /// </summary>
    /// <returns></returns>
    public int GetLastDatas()
    {
        int count = 0;
        foreach (var item in _allData)
        {
            if (item.HasLook == false)
            {
                count++;
            }
        }

        return count;
    }

    public void Reset()
    {
        _curRoundId = 0;
        _dicSuggestionMsgData.Clear();
        _hitKnowledge.Clear();
    }

    public SuggestionMsgData GetDicSuggestionMsgData(int roundId)
    {
        _dicSuggestionMsgData.TryGetValue(roundId, out SuggestionMsgData data);
        return data;
    }

    public void SetDicSuggestionMsgData(int bubbleId, string content, int roundId)
    {
  
        _dicSuggestionMsgData[roundId] = new SuggestionMsgData()
        {
            BubbleId = bubbleId,
            Content = content,
            RoundId = roundId,
        };
    }
    

    public void SetHitKnowledge(long bubble_id,RepeatedField<string> value)
    {
        _hitKnowledge[bubble_id] = value;
        Notifier.instance.SendNotification(NotifyConsts.HitKnowledgeUpdate,bubble_id);
        if (value.Count <= 0)
        {
            Notifier.instance.SendNotification(NotifyConsts.ChatPlayerCellDoOver);
        }
    }
    /// <summary>
    /// 获取当前 player  cell 的知识点命中数据
    /// </summary>
    /// <param name="bubble_id"></param>
    /// <returns></returns>
    public RepeatedField<string> GetHitKnowledge(long bubble_id)
    {
        if (_hitKnowledge.TryGetValue(bubble_id, out RepeatedField<string> value))
        {
            return value;
        }
        else
        {
            return null;
        }
    }

    public void SaveIntToLocal(string key,int value)
    {
        PlayerPrefs.SetInt(key,value);
        PlayerPrefs.Save();
    }

    public int GetIntFromLocal(string key)
    {
        return PlayerPrefs.GetInt(key,0);
    }

    #region setting
    public Msg.explore.PB_Explore_UserSetting_SpeakingSpeed SpeakingSpeed = Msg.explore.PB_Explore_UserSetting_SpeakingSpeed.EO_US_SS_SLOW; // 语速
    public Msg.explore.PB_Explore_UserSetting_AutoTranslateDisplay AutoTranslateDisplay = Msg.explore.PB_Explore_UserSetting_AutoTranslateDisplay.EO_US_ATD_ON; // 自动显示翻译
    public Msg.explore.PB_Explore_UserSetting_BackgroundMusic BackgroundMusic = Msg.explore.PB_Explore_UserSetting_BackgroundMusic.EO_US_BM_ON; // 背景音乐
    #endregion
}

// public class PlayComboParam
// {
//     public string EffectName = String.Empty;
//     public bool isPerfect = false;
// }

public class ExploreItemData
{
    public PB_PreloadData Data;
    public bool HasLook = false;
}


/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ImageQuestion : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "ImageQuestion";
        public static string url => "ui://cmoz5osjcki6uvptd3";

        public Controller ctrlTitle;
        public GLoader ldrImage;
        public GTextField tfWord;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ImageQuestion));
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            ctrlTitle = GetControllerAt(0);
            ldrImage = GetChildAt(1) as GLoader;
            tfWord = GetChildAt(2) as GTextField;
        }
        public override void Dispose()
        {
            ctrlTitle = null;
            ldrImage = null;
            tfWord = null;

            base.Dispose();
        }
    }
}
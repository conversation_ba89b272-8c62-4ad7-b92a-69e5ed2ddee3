/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Main
{
    public partial class StaminaPurchaseComponent : UIBindT
    {
        public override string pkgName => "Main";
        public override string comName => "StaminaPurchaseComponent";

        public Controller state;
        public GGraph bg;
        public GRichTextField tfDesc;
        public GComponent comHeart;
        public GGraph btnSubscribe;
        public GRichTextField tfBtnSubscribe;
        public GRichTextField tfGetPremium;
        public GComponent discountNode;
        public GGraph btnRefill;
        public GRichTextField tfBtnRefill;
        public GRichTextField tfBtnRefillCost;
        public GGraph btnPractice;
        public GRichTextField tfBtnPractice;
        public GRichTextField tfTimes;
        public GGroup grpPractice;
        public GRichTextField tfDescUnlimited;
        public GLoader loaderUnlimitedStaminaIcon;
        public GGroup grpUnlimited;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            state = com.GetControllerAt(0);
            bg = (GGraph)com.GetChildAt(0);
            tfDesc = (GRichTextField)com.GetChildAt(2);
            comHeart = (GComponent)com.GetChildAt(3);
            btnSubscribe = (GGraph)com.GetChildAt(5);
            tfBtnSubscribe = (GRichTextField)com.GetChildAt(7);
            tfGetPremium = (GRichTextField)com.GetChildAt(8);
            discountNode = (GComponent)com.GetChildAt(9);
            btnRefill = (GGraph)com.GetChildAt(10);
            tfBtnRefill = (GRichTextField)com.GetChildAt(12);
            tfBtnRefillCost = (GRichTextField)com.GetChildAt(14);
            btnPractice = (GGraph)com.GetChildAt(15);
            tfBtnPractice = (GRichTextField)com.GetChildAt(17);
            tfTimes = (GRichTextField)com.GetChildAt(18);
            grpPractice = (GGroup)com.GetChildAt(19);
            tfDescUnlimited = (GRichTextField)com.GetChildAt(23);
            loaderUnlimitedStaminaIcon = (GLoader)com.GetChildAt(24);
            grpUnlimited = (GGroup)com.GetChildAt(25);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            state = null;
            bg = null;
            tfDesc = null;
            comHeart = null;
            btnSubscribe = null;
            tfBtnSubscribe = null;
            tfGetPremium = null;
            discountNode = null;
            btnRefill = null;
            tfBtnRefill = null;
            tfBtnRefillCost = null;
            btnPractice = null;
            tfBtnPractice = null;
            tfTimes = null;
            grpPractice = null;
            tfDescUnlimited = null;
            loaderUnlimitedStaminaIcon = null;
            grpUnlimited = null;
        }
    }
}
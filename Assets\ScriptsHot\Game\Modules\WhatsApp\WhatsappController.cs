using System.Collections;
using System.Collections.Generic;
using Game.Modules.WhatsApp;
using Msg.incentive;
using UnityEngine;

public class WhatsappController : BaseController
{
    public WhatsappController() : base(ModelConsts.Whatsapp) { }
    
    public override void OnUIInit()
    {
        RegisterUI(new WhatsappGuiderUI(UIConsts.WhatsappGuider));
    }


    private PB_WhatsappInfo whatsappInfo;
    
    
    public bool ShowMainWhatsappBtn
    {
        get
        {
            if (whatsappInfo == null)
            {
                return false;
            }
            return whatsappInfo.is_show_whatsapp;
        }
    }
    
    public string WhatsappUrl
    {
        get
        {
            if (whatsappInfo == null)
            {
                return string.Empty;
            }
            return whatsappInfo.whatsapp_url;
        }
    }
    
    
    public void SetDatas(PB_WhatsappInfo _whatsappInfo)
    {
        whatsappInfo = _whatsappInfo;
    }
}

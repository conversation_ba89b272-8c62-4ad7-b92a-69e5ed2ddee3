/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class AnswerPopup : GComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "AnswerPopup";
        public static string url => "ui://cmoz5osjp3vduvptc7";

        public GGraph answerBg;
        public GTextField title;
        public GTextField tfDebugInfo;
        public GComponent loaderAnswer;
        public GGraph layout;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(AnswerPopup));
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            answerBg = GetChildAt(0) as GGraph;
            title = GetChildAt(1) as GTextField;
            tfDebugInfo = GetChildAt(2) as GTextField;
            loaderAnswer = GetChildAt(3) as GComponent;
            layout = GetChildAt(4) as GGraph;
        }
        public override void Dispose()
        {
            answerBg = null;
            title = null;
            tfDebugInfo = null;
            loaderAnswer = null;
            layout = null;

            base.Dispose();
        }
    }
}
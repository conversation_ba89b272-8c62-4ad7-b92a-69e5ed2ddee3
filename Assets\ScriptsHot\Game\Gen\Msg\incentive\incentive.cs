// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/incentive/incentive.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.incentive {

  /// <summary>Holder for reflection information generated from protobuf/incentive/incentive.proto</summary>
  public static partial class IncentiveReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/incentive/incentive.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static IncentiveReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiJwcm90b2J1Zi9pbmNlbnRpdmUvaW5jZW50aXZlLnByb3RvGiBwcm90b2J1",
            "Zi9pbmNlbnRpdmUvY2hlY2tpbi5wcm90bxogcHJvdG9idWYvaW5jZW50aXZl",
            "L3JhbmtpbmcucHJvdG8aH3Byb3RvYnVmL2Vjb25vbWljL2JlbmVmaXQucHJv",
            "dG8aH3Byb3RvYnVmL2Jhc2ljL3NldHRsZW1lbnQucHJvdG8aG3Byb3RvYnVm",
            "L2Jhc2ljL2RpYWxvZy5wcm90bxodcHJvdG9idWYvYmFzaWMvZWNvbm9taWMu",
            "cHJvdG8aHnByb3RvYnVmL2Jhc2ljL2luY2VudGl2ZS5wcm90bxofcHJvdG9i",
            "dWYvaW5jZW50aXZlL2dyb3d0aC5wcm90bxoZcHJvdG9idWYvYmFzaWMvY29k",
            "ZS5wcm90bxobcHJvdG9idWYvYmFzaWMvY291cnNlLnByb3RvIjIKH0NTX0dl",
            "dEluY2VudGl2ZURhdGFGb3JQb3J0YWxSZXESDwoHdXNlcl9pZBgBIAEoAyJZ",
            "Ch9TQ19HZXRJbmNlbnRpdmVEYXRhRm9yUG9ydGFsQWNrEgwKBGNvZGUYASAB",
            "KAUSKAoEZGF0YRgCIAEoCzIaLlBCX0luY2VudGl2ZURhdGFGb3JQb3J0YWwi",
            "1AMKGVBCX0luY2VudGl2ZURhdGFGb3JQb3J0YWwSKwoPY2hlY2tpbl9zdW1t",
            "YXJ5GAEgASgLMhIuUEJfQ2hlY2tpblN1bW1hcnkSKwoPcmFua2luZ19zdW1t",
            "YXJ5GAIgASgLMhIuUEJfUmFua2luZ1N1bW1hcnkSKQoMc3RhbWluYV9kYXRh",
            "GAMgASgLMhMuUEJfVXNlclN0YW1pbmFEYXRhEicKC2dyb3d0aF9kYXRhGAQg",
            "ASgLMhIuUEJfVXNlckdyb3d0aERhdGESJwoJdXNlcl9kYXRhGAUgASgLMhQu",
            "UEJfSG9tZXBhZ2VVc2VySW5mbxI4ChZob21lcGFnZV9lY29ub21pY19pbmZv",
            "GAYgASgLMhguUEJfSG9tZXBhZ2VFY29ub21pY0luZm8SQQobaG9tZXBhZ2Vf",
            "ZnJpZW5kX3N0cmVha19pbmZvGAcgASgLMhwuUEJfSG9tZXBhZ2VGcmllbmRT",
            "dHJlYWtJbmZvEjoKF3N5c3RlbV9vcGVyYXRpb25fY29uZmlnGAggASgLMhku",
            "UEJfU3lzdGVtT3BlcmF0aW9uQ29uZmlnEicKDXdoYXRzYXBwX2luZm8YCSAB",
            "KAsyEC5QQl9XaGF0c2FwcEluZm8iQQoPUEJfV2hhdHNhcHBJbmZvEhgKEGlz",
            "X3Nob3dfd2hhdHNhcHAYASABKAgSFAoMd2hhdHNhcHBfdXJsGAIgASgJIjYK",
            "GFBCX1N5c3RlbU9wZXJhdGlvbkNvbmZpZxIaChJpc19zaG93X2FwcF9yYXRp",
            "bmcYASABKAgivwEKF1BCX0hvbWVwYWdlRWNvbm9taWNJbmZvEh0KFXNob3df",
            "Y29tbWVyY2lhbF9wb3B1cBgBIAEoCBIeChZzaG93X2NvbW1lcmNpYWxfYmFu",
            "bmVyGAIgASgIEicKC21lbWJlcl9pbmZvGAMgASgLMhIuUEJfTWVtYmVySW5m",
            "b1Jlc3ASHAoUc2hvd190b3BfaGVhcnRfZ3VpZGUYBCABKAgSHgoWaXNfY29t",
            "bWVyY2lhbF9uZXdfdXNlchgFIAEoCCLGAQoTUEJfSG9tZXBhZ2VVc2VySW5m",
            "bxIQCghoZWFkX3VybBgBIAEoCRIrChJ1c2VyX2RyZXNzX3VwX2RhdGEYCCAB",
            "KAsyDy5QQl9EcmVzc1VwRGF0YRIUCgxoZWFkX2l0ZW1faWQYCSABKAMSKAoO",
            "aGVhZF9pdGVtX3R5cGUYCiABKA4yEC5QQl9IZWFkSXRlbVR5cGUSGAoQaGFz",
            "X2NyZWF0ZWRfcm9sZRgLIAEoCBIWCg5sYW5ndWFnZV9sZXZlbBgMIAEoCSIm",
            "ChNTU19HZXRVc2VyRGV0YWlsUmVxEg8KB3VzZXJfaWQYASABKAMiRQoTU1Nf",
            "R2V0VXNlckRldGFpbEFjaxIMCgRjb2RlGAEgASgFEiAKBGRhdGEYAiABKAsy",
            "Ei5QQl9Vc2VyRGV0YWlsSW5mbyKNAgoRUEJfVXNlckRldGFpbEluZm8SDwoH",
            "dXNlcl9pZBgBIAEoAxITCgtwbGF5ZXJfbmFtZRgCIAEoCRIQCghoZWFkX3Vy",
            "bBgDIAEoCRIYChByb2xlX3VuaXF1ZV9uYW1lGAQgASgJEiYKDWRyZXNzX3Vw",
            "X2RhdGEYBSABKAsyDy5QQl9EcmVzc1VwRGF0YRIUCgxoZWFkX2l0ZW1faWQY",
            "BiABKAMSKAoOaGVhZF9pdGVtX3R5cGUYByABKA4yEC5QQl9IZWFkSXRlbVR5",
            "cGUSEAoIcmVnX3RpbWUYCCABKAMSFAoMY291bnRyeV9jb2RlGAkgASgJEhYK",
            "Dmxhbmd1YWdlX2xldmVsGAogASgJItgDChlTU19JbmNlbnRpdmVTZXR0bGVt",
            "ZW50UmVxEg8KB3Rhc2tfaWQYASABKAMSFgoOdGFza19yZWNvcmRfaWQYAiAB",
            "KAMSIwoLZGlhbG9nX21vZGUYAyABKA4yDi5QQl9EaWFsb2dNb2RlEhUKDXRh",
            "c2tfc3Rhcl9jbnQYBCABKAUSEQoJdGFza19jb3N0GAUgASgDEg0KBXJvdW5k",
            "GAYgASgDEisKDWRpYWxvZ19zb3VyY2UYByABKA4yFC5QQl9EaWFsb2dTb3Vy",
            "Y2VFbnVtEhIKCnN0YXJ0X3RpbWUYCCABKAMSEAoIZW5kX3RpbWUYCSABKAMS",
            "GgoSaXNfZmluaXNoX25ld19ub2RlGAogASgIEiEKGWlzX2ZpbmlzaF9jdXJy",
            "ZW50X2NoYXB0ZXIYCyABKAgSDwoHdXNlcl9pZBgMIAEoAxIWCg50b3RhbF9p",
            "bnRpbWFjeRgNIAEoAxIZChFoaXRfa25vd2xlZGdlX2NudBgOIAEoBRIUCgxj",
            "b3JyZWN0X3JhdGUYDyABKAMSJQoKbGV2ZWxfdHlwZRgQIAEoDjIRLlBCX0xl",
            "dmVsVHlwZUVudW0SIQoIZXhwX3R5cGUYESABKA4yDy5QQl9FeHBUeXBlRW51",
            "bSJUChlTU19JbmNlbnRpdmVTZXR0bGVtZW50QWNrEgwKBGNvZGUYASABKAUS",
            "KQoEZGF0YRgCIAEoCzIbLlBCX0luY2VudGl2ZVNldHRsZW1lbnRJbmZvInEK",
            "GlBCX0luY2VudGl2ZVNldHRsZW1lbnRJbmZvEi4KEmRpYWxvZ19yZXN1bHRf",
            "bGlzdBgBIAMoCzISLlBCX1NldHRsZW1lbnREYXRhEhIKCmV4cGVyaWVuY2UY",
            "AiABKAUSDwoHc3RhbWluYRgDIAEoBSI6ChxTU19TeW5jT3JpZ2luRGF0YUZv",
            "ckthZmthUmVxEg0KBWJlZ2luGAEgASgDEgsKA2VuZBgCIAEoAyIeChxTU19T",
            "eW5jT3JpZ2luRGF0YUZvckthZmthQWNrIj0KFENTX1NldFVzZXJEcmVzc1Vw",
            "UmVxEiUKBWl0ZW1zGAEgAygLMhYuUEJfU2V0VXNlckRyZXNzVXBJdGVtImUK",
            "FVBCX1NldFVzZXJEcmVzc1VwSXRlbRIWCg5tZXJjaGFuZGlzZV9pZBgBIAEo",
            "CRI0ChRtZXJjaGFuZGlzZV9zdWJfdHlwZRgCIAEoDjIWLlBCX01lcmNoYW5k",
            "aXNlU3ViVHlwZSIkChRTQ19TZXRVc2VyRHJlc3NVcEFjaxIMCgRjb2RlGAEg",
            "ASgFIiUKI0NTX0dldFVzZXJEcmVzc1VwTWVyY2hhbmRpc2VEYXRhUmVxImEK",
            "I1NDX0dldFVzZXJEcmVzc1VwTWVyY2hhbmRpc2VEYXRhQWNrEgwKBGNvZGUY",
            "ASABKAUSLAoEZGF0YRgCIAEoCzIeLlBCX1VzZXJEcmVzc1VwTWVyY2hhbmRp",
            "c2VEYXRhIpsBCh1QQl9Vc2VyRHJlc3NVcE1lcmNoYW5kaXNlRGF0YRInCgZm",
            "cmFtZXMYASADKAsyFy5QQl9Vc2VyTWVyY2hhbmRpc2VJdGVtEisKCm5hbWVf",
            "c3R5bGUYAiADKAsyFy5QQl9Vc2VyTWVyY2hhbmRpc2VJdGVtEiQKCmhlYWRf",
            "aXRlbXMYAyADKAsyEC5QQl9IZWFkSXRlbUluZm8iWgoWUEJfVXNlck1lcmNo",
            "YW5kaXNlSXRlbRIWCg5tZXJjaGFuZGlzZV9pZBgBIAEoCRIOCgZhbW91bnQY",
            "AiABKAMSGAoQcmVkZWVtX3RocmVzaG9sZBgDIAEoAyJjCg9QQl9IZWFkSXRl",
            "bUluZm8SFAoMaGVhZF9pdGVtX2lkGAEgASgDEigKDmhlYWRfaXRlbV90eXBl",
            "GAIgASgOMhAuUEJfSGVhZEl0ZW1UeXBlEhAKCGlzX293bmVkGAMgASgIIp8B",
            "ChdDU19TZW5kR2lmdEZvckZyaWVuZFJlcRIxChBtZXJjaGFuZGlzZV90eXBl",
            "GAEgASgOMhcuUEJfTWVyY2hhbmRpc2VJdGVtVHlwZRIXCg9tZXJjaGFuZGlz",
            "ZV92YWwYAiABKAMSDQoFY291bnQYAyABKAMSEQoJZnJpZW5kX2lkGAQgASgD",
            "EhYKDnJlYmF0ZV9naWZ0X2lkGAUgASgDIjEKF1NDX1NlbmRHaWZ0Rm9yRnJp",
            "ZW5kQWNrEhYKBGNvZGUYASABKA4yCC5QQl9Db2RlIlQKEkNTX1NldFNob3dT",
            "dGF0ZVJlcRIoCg5zaG93X2l0ZW1fdHlwZRgBIAEoDjIQLlBCX1Nob3dJdGVt",
            "VHlwZRIUCgxzaG93X2l0ZW1faWQYAiABKAkiLAoSU0NfU2V0U2hvd1N0YXRl",
            "QWNrEhYKBGNvZGUYASABKA4yCC5QQl9Db2RlImMKH1NTX0luY2VudGl2ZVNl",
            "dHRsZUZvckV4cGxvcmVSZXESDwoHdXNlcl9pZBgBIAEoAxIcChR0aW1lX2Nv",
            "c3RfaW5fc2Vjb25kcxgCIAEoAxIRCglkaWFsb2dfaWQYAyABKAkiWAofU1Nf",
            "SW5jZW50aXZlU2V0dGxlRm9yRXhwbG9yZUFjaxIMCgRjb2RlGAEgASgFEicK",
            "BGRhdGEYAiABKAsyGS5QQl9FeHBsb3JlU2V0dGxlbWVudERhdGEimAIKGFBC",
            "X0V4cGxvcmVTZXR0bGVtZW50RGF0YRIUCgxwcmV2aW91c19leHAYASABKAUS",
            "EgoKZXhwX2NoYW5nZRgCIAEoBRIPCgduZXdfZXhwGAMgASgFEhcKD3ByZXZp",
            "b3VzX2dyb3d0aBgEIAEoBRIVCg1ncm93dGhfY2hhbmdlGAUgASgFEhIKCm5l",
            "d19ncm93dGgYBiABKAUSEQoJdG9kYXlfZXhwGAcgASgFEiIKGnRvZGF5X3Rp",
            "bWVfY29zdF9pbl9zZWNvbmRzGAggASgDEhUKDXJlYWNoX2V4cF9jYXAYCSAB",
            "KAgSLwoMY2hlY2tpbl9pdGVtGAogASgLMhkuUEJfQ2hlY2tpbkl0ZW1Gb3JF",
            "eHBsb3JlIlgKGFBCX0NoZWNraW5JdGVtRm9yRXhwbG9yZRIVCg1jb250aW51",
            "ZV9kYXlzGAEgASgDEiUKDGNoZWNraW5fbGlzdBgCIAMoCzIPLlBCX0NoZWNr",
            "aW5JdGVtImQKGlNTX1NldFVzZXJMYW5ndWFnZUxldmVsUmVxEhYKDmxhbmd1",
            "YWdlX2xldmVsGAEgASgJEi4KBnNvdXJjZRgCIAEoDjIeLlBCX1NldFVzZXJM",
            "YW5ndWFnZUxldmVsU291cmNlImAKGlNTX1NldFVzZXJMYW5ndWFnZUxldmVs",
            "QWNrEhYKBGNvZGUYASABKA4yCC5QQl9Db2RlEioKBGRhdGEYAiABKAsyHC5Q",
            "Ql9TZXRVc2VyTGFuZ3VhZ2VMZXZlbERhdGEidQobUEJfU2V0VXNlckxhbmd1",
            "YWdlTGV2ZWxEYXRhEhYKDmxhbmd1YWdlX2xldmVsGAEgASgJEhUKDXNlY3Rp",
            "b25faW5kZXgYAiABKAUSEgoKdW5pdF9pbmRleBgDIAEoBRITCgtsZXZlbF9p",
            "bmRleBgEIAEoBSIiCiBDU19HZXRJbmNlbnRpdmVEYXRhRm9yRXhwbG9yZVJl",
            "cSJbCiBTQ19HZXRJbmNlbnRpdmVEYXRhRm9yRXhwbG9yZUFjaxIMCgRjb2Rl",
            "GAEgASgFEikKBGRhdGEYAiABKAsyGy5QQl9JbmNlbnRpdmVEYXRhRm9yRXhw",
            "bG9yZSJPChpQQl9JbmNlbnRpdmVEYXRhRm9yRXhwbG9yZRIxCg1lY29ub21p",
            "Y19pbmZvGAEgASgLMhouUEJfRWNvbm9taWNJbmZvRm9yRXhwbG9yZSJ4ChlQ",
            "Ql9FY29ub21pY0luZm9Gb3JFeHBsb3JlEicKC21lbWJlcl9pbmZvGAEgASgL",
            "MhIuUEJfTWVtYmVySW5mb1Jlc3ASGQoRcmVtYWluaW5nX3NlY29uZHMYAiAB",
            "KAUSFwoPaXNfY29uc3VtZV90aW1lGAMgASgIIjsKEkNTX1N0YXJ0Q29uc3Vt",
            "ZVJlcRIlCgxjb25zdW1lX3R5cGUYASABKA4yDy5QQl9Db25zdW1lVHlwZSIs",
            "ChJTQ19TdGFydENvbnN1bWVBY2sSFgoEY29kZRgBIAEoDjIILlBCX0NvZGUq",
            "+gEKD1BCX1Nob3dJdGVtVHlwZRIKCgZTVE5vbmUQABITCg9Db21tZXJjaWFs",
            "UG9wdXAQARIUChBDb21tZXJjaWFsQmFubmVyEAISGAoUQ29tbWVyY2lhbFNl",
            "dHRsZW1lbnQQAxIVChFOZXdGcmllbmRzaGlwVGFzaxAEEhkKFUZyaWVuZHNo",
            "aXBTdHJlYWtCcmVhaxAFEh4KGkZyaWVuZHNoaXBTdHJlYWtPdGhlckJyZWFr",
            "EAYSHgoaRnJpZW5kc2hpcFN0cmVha0ludml0YXRpb24QBxINCglBcHBSYXRp",
            "bmcQCBIVChFFeHBsb3JlTmF2aWdhdGlvbhAJKmAKHVBCX1NldFVzZXJMYW5n",
            "dWFnZUxldmVsU291cmNlEgoKBlNMTm9uZRAAEhkKFVNMX09uQm9hcmRpbmdf",
            "RXhwbG9yZRABEhgKFFNMX09uQm9hcmRpbmdfT3B0aW9uEAIqMQoOUEJfQ29u",
            "c3VtZVR5cGUSCwoHQ1RfTm9uZRAAEhIKDkNUX0V4cGxvcmVUaW1lEAFCLloc",
            "dmZfcHJvdG9idWYvc2VydmVyL2luY2VudGl2ZaoCDU1zZy5pbmNlbnRpdmVi",
            "BnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.incentive.CheckinReflection.Descriptor, global::Msg.incentive.RankingReflection.Descriptor, global::Msg.economic.BenefitReflection.Descriptor, global::Msg.basic.SettlementReflection.Descriptor, global::Msg.basic.DialogReflection.Descriptor, global::Msg.basic.EconomicReflection.Descriptor, global::IncentiveReflection.Descriptor, global::Msg.incentive.GrowthReflection.Descriptor, global::Msg.basic.CodeReflection.Descriptor, global::Msg.basic.CourseReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.incentive.PB_ShowItemType), typeof(global::Msg.incentive.PB_SetUserLanguageLevelSource), typeof(global::Msg.incentive.PB_ConsumeType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_GetIncentiveDataForPortalReq), global::Msg.incentive.CS_GetIncentiveDataForPortalReq.Parser, new[]{ "user_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_GetIncentiveDataForPortalAck), global::Msg.incentive.SC_GetIncentiveDataForPortalAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_IncentiveDataForPortal), global::Msg.incentive.PB_IncentiveDataForPortal.Parser, new[]{ "checkin_summary", "ranking_summary", "stamina_data", "growth_data", "user_data", "homepage_economic_info", "homepage_friend_streak_info", "system_operation_config", "whatsapp_info" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_WhatsappInfo), global::Msg.incentive.PB_WhatsappInfo.Parser, new[]{ "is_show_whatsapp", "whatsapp_url" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_SystemOperationConfig), global::Msg.incentive.PB_SystemOperationConfig.Parser, new[]{ "is_show_app_rating" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_HomepageEconomicInfo), global::Msg.incentive.PB_HomepageEconomicInfo.Parser, new[]{ "show_commercial_popup", "show_commercial_banner", "member_info", "show_top_heart_guide", "is_commercial_new_user" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_HomepageUserInfo), global::Msg.incentive.PB_HomepageUserInfo.Parser, new[]{ "head_url", "user_dress_up_data", "head_item_id", "head_item_type", "has_created_role", "language_level" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_GetUserDetailReq), global::Msg.incentive.SS_GetUserDetailReq.Parser, new[]{ "user_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_GetUserDetailAck), global::Msg.incentive.SS_GetUserDetailAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_UserDetailInfo), global::Msg.incentive.PB_UserDetailInfo.Parser, new[]{ "user_id", "player_name", "head_url", "role_unique_name", "dress_up_data", "head_item_id", "head_item_type", "reg_time", "country_code", "language_level" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_IncentiveSettlementReq), global::Msg.incentive.SS_IncentiveSettlementReq.Parser, new[]{ "task_id", "task_record_id", "dialog_mode", "task_star_cnt", "task_cost", "round", "dialog_source", "start_time", "end_time", "is_finish_new_node", "is_finish_current_chapter", "user_id", "total_intimacy", "hit_knowledge_cnt", "correct_rate", "level_type", "exp_type" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_IncentiveSettlementAck), global::Msg.incentive.SS_IncentiveSettlementAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_IncentiveSettlementInfo), global::Msg.incentive.PB_IncentiveSettlementInfo.Parser, new[]{ "dialog_result_list", "experience", "stamina" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_SyncOriginDataForKafkaReq), global::Msg.incentive.SS_SyncOriginDataForKafkaReq.Parser, new[]{ "begin", "end" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_SyncOriginDataForKafkaAck), global::Msg.incentive.SS_SyncOriginDataForKafkaAck.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_SetUserDressUpReq), global::Msg.incentive.CS_SetUserDressUpReq.Parser, new[]{ "items" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_SetUserDressUpItem), global::Msg.incentive.PB_SetUserDressUpItem.Parser, new[]{ "merchandise_id", "merchandise_sub_type" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_SetUserDressUpAck), global::Msg.incentive.SC_SetUserDressUpAck.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_GetUserDressUpMerchandiseDataReq), global::Msg.incentive.CS_GetUserDressUpMerchandiseDataReq.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_GetUserDressUpMerchandiseDataAck), global::Msg.incentive.SC_GetUserDressUpMerchandiseDataAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_UserDressUpMerchandiseData), global::Msg.incentive.PB_UserDressUpMerchandiseData.Parser, new[]{ "frames", "name_style", "head_items" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_UserMerchandiseItem), global::Msg.incentive.PB_UserMerchandiseItem.Parser, new[]{ "merchandise_id", "amount", "redeem_threshold" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_HeadItemInfo), global::Msg.incentive.PB_HeadItemInfo.Parser, new[]{ "head_item_id", "head_item_type", "is_owned" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_SendGiftForFriendReq), global::Msg.incentive.CS_SendGiftForFriendReq.Parser, new[]{ "merchandise_type", "merchandise_val", "count", "friend_id", "rebate_gift_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_SendGiftForFriendAck), global::Msg.incentive.SC_SendGiftForFriendAck.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_SetShowStateReq), global::Msg.incentive.CS_SetShowStateReq.Parser, new[]{ "show_item_type", "show_item_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_SetShowStateAck), global::Msg.incentive.SC_SetShowStateAck.Parser, new[]{ "code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_IncentiveSettleForExploreReq), global::Msg.incentive.SS_IncentiveSettleForExploreReq.Parser, new[]{ "user_id", "time_cost_in_seconds", "dialog_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_IncentiveSettleForExploreAck), global::Msg.incentive.SS_IncentiveSettleForExploreAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_ExploreSettlementData), global::Msg.incentive.PB_ExploreSettlementData.Parser, new[]{ "previous_exp", "exp_change", "new_exp", "previous_growth", "growth_change", "new_growth", "today_exp", "today_time_cost_in_seconds", "reach_exp_cap", "checkin_item" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_CheckinItemForExplore), global::Msg.incentive.PB_CheckinItemForExplore.Parser, new[]{ "continue_days", "checkin_list" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_SetUserLanguageLevelReq), global::Msg.incentive.SS_SetUserLanguageLevelReq.Parser, new[]{ "language_level", "source" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SS_SetUserLanguageLevelAck), global::Msg.incentive.SS_SetUserLanguageLevelAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_SetUserLanguageLevelData), global::Msg.incentive.PB_SetUserLanguageLevelData.Parser, new[]{ "language_level", "section_index", "unit_index", "level_index" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_GetIncentiveDataForExploreReq), global::Msg.incentive.CS_GetIncentiveDataForExploreReq.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_GetIncentiveDataForExploreAck), global::Msg.incentive.SC_GetIncentiveDataForExploreAck.Parser, new[]{ "code", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_IncentiveDataForExplore), global::Msg.incentive.PB_IncentiveDataForExplore.Parser, new[]{ "economic_info" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.PB_EconomicInfoForExplore), global::Msg.incentive.PB_EconomicInfoForExplore.Parser, new[]{ "member_info", "remaining_seconds", "is_consume_time" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.CS_StartConsumeReq), global::Msg.incentive.CS_StartConsumeReq.Parser, new[]{ "consume_type" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.incentive.SC_StartConsumeAck), global::Msg.incentive.SC_StartConsumeAck.Parser, new[]{ "code" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum PB_ShowItemType {
    [pbr::OriginalName("STNone")] STNone = 0,
    /// <summary>
    /// 商业化弹窗
    /// </summary>
    [pbr::OriginalName("CommercialPopup")] CommercialPopup = 1,
    /// <summary>
    /// 商业化banner
    /// </summary>
    [pbr::OriginalName("CommercialBanner")] CommercialBanner = 2,
    /// <summary>
    /// 商业化结算
    /// </summary>
    [pbr::OriginalName("CommercialSettlement")] CommercialSettlement = 3,
    /// <summary>
    /// 新好友任务
    /// </summary>
    [pbr::OriginalName("NewFriendshipTask")] NewFriendshipTask = 4,
    /// <summary>
    /// 好友连胜中断
    /// </summary>
    [pbr::OriginalName("FriendshipStreakBreak")] FriendshipStreakBreak = 5,
    /// <summary>
    /// 好友连胜对方中断
    /// </summary>
    [pbr::OriginalName("FriendshipStreakOtherBreak")] FriendshipStreakOtherBreak = 6,
    /// <summary>
    /// 好友连胜邀请
    /// </summary>
    [pbr::OriginalName("FriendshipStreakInvitation")] FriendshipStreakInvitation = 7,
    /// <summary>
    /// 评分
    /// </summary>
    [pbr::OriginalName("AppRating")] AppRating = 8,
    /// <summary>
    /// 探索引导
    /// </summary>
    [pbr::OriginalName("ExploreNavigation")] ExploreNavigation = 9,
  }

  public enum PB_SetUserLanguageLevelSource {
    [pbr::OriginalName("SLNone")] SLNone = 0,
    /// <summary>
    /// 引导选项-探索业务
    /// </summary>
    [pbr::OriginalName("SL_OnBoarding_Explore")] SL_OnBoarding_Explore = 1,
    /// <summary>
    /// 引导选项-引导选项
    /// </summary>
    [pbr::OriginalName("SL_OnBoarding_Option")] SL_OnBoarding_Option = 2,
  }

  public enum PB_ConsumeType {
    [pbr::OriginalName("CT_None")] CT_None = 0,
    /// <summary>
    /// 探索业务时间
    /// </summary>
    [pbr::OriginalName("CT_ExploreTime")] CT_ExploreTime = 1,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetIncentiveDataForPortalReq : pb::IMessage<CS_GetIncentiveDataForPortalReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetIncentiveDataForPortalReq> _parser = new pb::MessageParser<CS_GetIncentiveDataForPortalReq>(() => new CS_GetIncentiveDataForPortalReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetIncentiveDataForPortalReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetIncentiveDataForPortalReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetIncentiveDataForPortalReq(CS_GetIncentiveDataForPortalReq other) : this() {
      user_id_ = other.user_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetIncentiveDataForPortalReq Clone() {
      return new CS_GetIncentiveDataForPortalReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetIncentiveDataForPortalReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetIncentiveDataForPortalReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetIncentiveDataForPortalReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetIncentiveDataForPortalAck : pb::IMessage<SC_GetIncentiveDataForPortalAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetIncentiveDataForPortalAck> _parser = new pb::MessageParser<SC_GetIncentiveDataForPortalAck>(() => new SC_GetIncentiveDataForPortalAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetIncentiveDataForPortalAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetIncentiveDataForPortalAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetIncentiveDataForPortalAck(SC_GetIncentiveDataForPortalAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetIncentiveDataForPortalAck Clone() {
      return new SC_GetIncentiveDataForPortalAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_IncentiveDataForPortal data_;
    /// <summary>
    /// 首页激励信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_IncentiveDataForPortal data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetIncentiveDataForPortalAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetIncentiveDataForPortalAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetIncentiveDataForPortalAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_IncentiveDataForPortal();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_IncentiveDataForPortal();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_IncentiveDataForPortal();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_IncentiveDataForPortal : pb::IMessage<PB_IncentiveDataForPortal>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_IncentiveDataForPortal> _parser = new pb::MessageParser<PB_IncentiveDataForPortal>(() => new PB_IncentiveDataForPortal());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_IncentiveDataForPortal> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IncentiveDataForPortal() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IncentiveDataForPortal(PB_IncentiveDataForPortal other) : this() {
      checkin_summary_ = other.checkin_summary_ != null ? other.checkin_summary_.Clone() : null;
      ranking_summary_ = other.ranking_summary_ != null ? other.ranking_summary_.Clone() : null;
      stamina_data_ = other.stamina_data_ != null ? other.stamina_data_.Clone() : null;
      growth_data_ = other.growth_data_ != null ? other.growth_data_.Clone() : null;
      user_data_ = other.user_data_ != null ? other.user_data_.Clone() : null;
      homepage_economic_info_ = other.homepage_economic_info_ != null ? other.homepage_economic_info_.Clone() : null;
      homepage_friend_streak_info_ = other.homepage_friend_streak_info_ != null ? other.homepage_friend_streak_info_.Clone() : null;
      system_operation_config_ = other.system_operation_config_ != null ? other.system_operation_config_.Clone() : null;
      whatsapp_info_ = other.whatsapp_info_ != null ? other.whatsapp_info_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IncentiveDataForPortal Clone() {
      return new PB_IncentiveDataForPortal(this);
    }

    /// <summary>Field number for the "checkin_summary" field.</summary>
    public const int checkin_summaryFieldNumber = 1;
    private global::Msg.incentive.PB_CheckinSummary checkin_summary_;
    /// <summary>
    /// 打卡信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_CheckinSummary checkin_summary {
      get { return checkin_summary_; }
      set {
        checkin_summary_ = value;
      }
    }

    /// <summary>Field number for the "ranking_summary" field.</summary>
    public const int ranking_summaryFieldNumber = 2;
    private global::Msg.incentive.PB_RankingSummary ranking_summary_;
    /// <summary>
    /// 排行榜信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_RankingSummary ranking_summary {
      get { return ranking_summary_; }
      set {
        ranking_summary_ = value;
      }
    }

    /// <summary>Field number for the "stamina_data" field.</summary>
    public const int stamina_dataFieldNumber = 3;
    private global::Msg.economic.PB_UserStaminaData stamina_data_;
    /// <summary>
    /// 体力值信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_UserStaminaData stamina_data {
      get { return stamina_data_; }
      set {
        stamina_data_ = value;
      }
    }

    /// <summary>Field number for the "growth_data" field.</summary>
    public const int growth_dataFieldNumber = 4;
    private global::Msg.incentive.PB_UserGrowthData growth_data_;
    /// <summary>
    /// Growth值信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserGrowthData growth_data {
      get { return growth_data_; }
      set {
        growth_data_ = value;
      }
    }

    /// <summary>Field number for the "user_data" field.</summary>
    public const int user_dataFieldNumber = 5;
    private global::Msg.incentive.PB_HomepageUserInfo user_data_;
    /// <summary>
    /// 用户信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_HomepageUserInfo user_data {
      get { return user_data_; }
      set {
        user_data_ = value;
      }
    }

    /// <summary>Field number for the "homepage_economic_info" field.</summary>
    public const int homepage_economic_infoFieldNumber = 6;
    private global::Msg.incentive.PB_HomepageEconomicInfo homepage_economic_info_;
    /// <summary>
    /// economic信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_HomepageEconomicInfo homepage_economic_info {
      get { return homepage_economic_info_; }
      set {
        homepage_economic_info_ = value;
      }
    }

    /// <summary>Field number for the "homepage_friend_streak_info" field.</summary>
    public const int homepage_friend_streak_infoFieldNumber = 7;
    private global::Msg.incentive.PB_HomepageFriendStreakInfo homepage_friend_streak_info_;
    /// <summary>
    /// 好友连胜信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_HomepageFriendStreakInfo homepage_friend_streak_info {
      get { return homepage_friend_streak_info_; }
      set {
        homepage_friend_streak_info_ = value;
      }
    }

    /// <summary>Field number for the "system_operation_config" field.</summary>
    public const int system_operation_configFieldNumber = 8;
    private global::Msg.incentive.PB_SystemOperationConfig system_operation_config_;
    /// <summary>
    /// 系统操作配置
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_SystemOperationConfig system_operation_config {
      get { return system_operation_config_; }
      set {
        system_operation_config_ = value;
      }
    }

    /// <summary>Field number for the "whatsapp_info" field.</summary>
    public const int whatsapp_infoFieldNumber = 9;
    private global::Msg.incentive.PB_WhatsappInfo whatsapp_info_;
    /// <summary>
    /// whatsapp信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_WhatsappInfo whatsapp_info {
      get { return whatsapp_info_; }
      set {
        whatsapp_info_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_IncentiveDataForPortal);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_IncentiveDataForPortal other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(checkin_summary, other.checkin_summary)) return false;
      if (!object.Equals(ranking_summary, other.ranking_summary)) return false;
      if (!object.Equals(stamina_data, other.stamina_data)) return false;
      if (!object.Equals(growth_data, other.growth_data)) return false;
      if (!object.Equals(user_data, other.user_data)) return false;
      if (!object.Equals(homepage_economic_info, other.homepage_economic_info)) return false;
      if (!object.Equals(homepage_friend_streak_info, other.homepage_friend_streak_info)) return false;
      if (!object.Equals(system_operation_config, other.system_operation_config)) return false;
      if (!object.Equals(whatsapp_info, other.whatsapp_info)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (checkin_summary_ != null) hash ^= checkin_summary.GetHashCode();
      if (ranking_summary_ != null) hash ^= ranking_summary.GetHashCode();
      if (stamina_data_ != null) hash ^= stamina_data.GetHashCode();
      if (growth_data_ != null) hash ^= growth_data.GetHashCode();
      if (user_data_ != null) hash ^= user_data.GetHashCode();
      if (homepage_economic_info_ != null) hash ^= homepage_economic_info.GetHashCode();
      if (homepage_friend_streak_info_ != null) hash ^= homepage_friend_streak_info.GetHashCode();
      if (system_operation_config_ != null) hash ^= system_operation_config.GetHashCode();
      if (whatsapp_info_ != null) hash ^= whatsapp_info.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (checkin_summary_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(checkin_summary);
      }
      if (ranking_summary_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(ranking_summary);
      }
      if (stamina_data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(stamina_data);
      }
      if (growth_data_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(growth_data);
      }
      if (user_data_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(user_data);
      }
      if (homepage_economic_info_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(homepage_economic_info);
      }
      if (homepage_friend_streak_info_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(homepage_friend_streak_info);
      }
      if (system_operation_config_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(system_operation_config);
      }
      if (whatsapp_info_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(whatsapp_info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (checkin_summary_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(checkin_summary);
      }
      if (ranking_summary_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(ranking_summary);
      }
      if (stamina_data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(stamina_data);
      }
      if (growth_data_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(growth_data);
      }
      if (user_data_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(user_data);
      }
      if (homepage_economic_info_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(homepage_economic_info);
      }
      if (homepage_friend_streak_info_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(homepage_friend_streak_info);
      }
      if (system_operation_config_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(system_operation_config);
      }
      if (whatsapp_info_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(whatsapp_info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (checkin_summary_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(checkin_summary);
      }
      if (ranking_summary_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ranking_summary);
      }
      if (stamina_data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(stamina_data);
      }
      if (growth_data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(growth_data);
      }
      if (user_data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(user_data);
      }
      if (homepage_economic_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(homepage_economic_info);
      }
      if (homepage_friend_streak_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(homepage_friend_streak_info);
      }
      if (system_operation_config_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(system_operation_config);
      }
      if (whatsapp_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(whatsapp_info);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_IncentiveDataForPortal other) {
      if (other == null) {
        return;
      }
      if (other.checkin_summary_ != null) {
        if (checkin_summary_ == null) {
          checkin_summary = new global::Msg.incentive.PB_CheckinSummary();
        }
        checkin_summary.MergeFrom(other.checkin_summary);
      }
      if (other.ranking_summary_ != null) {
        if (ranking_summary_ == null) {
          ranking_summary = new global::Msg.incentive.PB_RankingSummary();
        }
        ranking_summary.MergeFrom(other.ranking_summary);
      }
      if (other.stamina_data_ != null) {
        if (stamina_data_ == null) {
          stamina_data = new global::Msg.economic.PB_UserStaminaData();
        }
        stamina_data.MergeFrom(other.stamina_data);
      }
      if (other.growth_data_ != null) {
        if (growth_data_ == null) {
          growth_data = new global::Msg.incentive.PB_UserGrowthData();
        }
        growth_data.MergeFrom(other.growth_data);
      }
      if (other.user_data_ != null) {
        if (user_data_ == null) {
          user_data = new global::Msg.incentive.PB_HomepageUserInfo();
        }
        user_data.MergeFrom(other.user_data);
      }
      if (other.homepage_economic_info_ != null) {
        if (homepage_economic_info_ == null) {
          homepage_economic_info = new global::Msg.incentive.PB_HomepageEconomicInfo();
        }
        homepage_economic_info.MergeFrom(other.homepage_economic_info);
      }
      if (other.homepage_friend_streak_info_ != null) {
        if (homepage_friend_streak_info_ == null) {
          homepage_friend_streak_info = new global::Msg.incentive.PB_HomepageFriendStreakInfo();
        }
        homepage_friend_streak_info.MergeFrom(other.homepage_friend_streak_info);
      }
      if (other.system_operation_config_ != null) {
        if (system_operation_config_ == null) {
          system_operation_config = new global::Msg.incentive.PB_SystemOperationConfig();
        }
        system_operation_config.MergeFrom(other.system_operation_config);
      }
      if (other.whatsapp_info_ != null) {
        if (whatsapp_info_ == null) {
          whatsapp_info = new global::Msg.incentive.PB_WhatsappInfo();
        }
        whatsapp_info.MergeFrom(other.whatsapp_info);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (checkin_summary_ == null) {
              checkin_summary = new global::Msg.incentive.PB_CheckinSummary();
            }
            input.ReadMessage(checkin_summary);
            break;
          }
          case 18: {
            if (ranking_summary_ == null) {
              ranking_summary = new global::Msg.incentive.PB_RankingSummary();
            }
            input.ReadMessage(ranking_summary);
            break;
          }
          case 26: {
            if (stamina_data_ == null) {
              stamina_data = new global::Msg.economic.PB_UserStaminaData();
            }
            input.ReadMessage(stamina_data);
            break;
          }
          case 34: {
            if (growth_data_ == null) {
              growth_data = new global::Msg.incentive.PB_UserGrowthData();
            }
            input.ReadMessage(growth_data);
            break;
          }
          case 42: {
            if (user_data_ == null) {
              user_data = new global::Msg.incentive.PB_HomepageUserInfo();
            }
            input.ReadMessage(user_data);
            break;
          }
          case 50: {
            if (homepage_economic_info_ == null) {
              homepage_economic_info = new global::Msg.incentive.PB_HomepageEconomicInfo();
            }
            input.ReadMessage(homepage_economic_info);
            break;
          }
          case 58: {
            if (homepage_friend_streak_info_ == null) {
              homepage_friend_streak_info = new global::Msg.incentive.PB_HomepageFriendStreakInfo();
            }
            input.ReadMessage(homepage_friend_streak_info);
            break;
          }
          case 66: {
            if (system_operation_config_ == null) {
              system_operation_config = new global::Msg.incentive.PB_SystemOperationConfig();
            }
            input.ReadMessage(system_operation_config);
            break;
          }
          case 74: {
            if (whatsapp_info_ == null) {
              whatsapp_info = new global::Msg.incentive.PB_WhatsappInfo();
            }
            input.ReadMessage(whatsapp_info);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (checkin_summary_ == null) {
              checkin_summary = new global::Msg.incentive.PB_CheckinSummary();
            }
            input.ReadMessage(checkin_summary);
            break;
          }
          case 18: {
            if (ranking_summary_ == null) {
              ranking_summary = new global::Msg.incentive.PB_RankingSummary();
            }
            input.ReadMessage(ranking_summary);
            break;
          }
          case 26: {
            if (stamina_data_ == null) {
              stamina_data = new global::Msg.economic.PB_UserStaminaData();
            }
            input.ReadMessage(stamina_data);
            break;
          }
          case 34: {
            if (growth_data_ == null) {
              growth_data = new global::Msg.incentive.PB_UserGrowthData();
            }
            input.ReadMessage(growth_data);
            break;
          }
          case 42: {
            if (user_data_ == null) {
              user_data = new global::Msg.incentive.PB_HomepageUserInfo();
            }
            input.ReadMessage(user_data);
            break;
          }
          case 50: {
            if (homepage_economic_info_ == null) {
              homepage_economic_info = new global::Msg.incentive.PB_HomepageEconomicInfo();
            }
            input.ReadMessage(homepage_economic_info);
            break;
          }
          case 58: {
            if (homepage_friend_streak_info_ == null) {
              homepage_friend_streak_info = new global::Msg.incentive.PB_HomepageFriendStreakInfo();
            }
            input.ReadMessage(homepage_friend_streak_info);
            break;
          }
          case 66: {
            if (system_operation_config_ == null) {
              system_operation_config = new global::Msg.incentive.PB_SystemOperationConfig();
            }
            input.ReadMessage(system_operation_config);
            break;
          }
          case 74: {
            if (whatsapp_info_ == null) {
              whatsapp_info = new global::Msg.incentive.PB_WhatsappInfo();
            }
            input.ReadMessage(whatsapp_info);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_WhatsappInfo : pb::IMessage<PB_WhatsappInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_WhatsappInfo> _parser = new pb::MessageParser<PB_WhatsappInfo>(() => new PB_WhatsappInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_WhatsappInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_WhatsappInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_WhatsappInfo(PB_WhatsappInfo other) : this() {
      is_show_whatsapp_ = other.is_show_whatsapp_;
      whatsapp_url_ = other.whatsapp_url_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_WhatsappInfo Clone() {
      return new PB_WhatsappInfo(this);
    }

    /// <summary>Field number for the "is_show_whatsapp" field.</summary>
    public const int is_show_whatsappFieldNumber = 1;
    private bool is_show_whatsapp_;
    /// <summary>
    /// 是否展示whatsapp
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_show_whatsapp {
      get { return is_show_whatsapp_; }
      set {
        is_show_whatsapp_ = value;
      }
    }

    /// <summary>Field number for the "whatsapp_url" field.</summary>
    public const int whatsapp_urlFieldNumber = 2;
    private string whatsapp_url_ = "";
    /// <summary>
    /// whatsapp url；如果是绑定，则是绑定链接；如果已经绑定，则是跳转whatsapp链接
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string whatsapp_url {
      get { return whatsapp_url_; }
      set {
        whatsapp_url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_WhatsappInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_WhatsappInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (is_show_whatsapp != other.is_show_whatsapp) return false;
      if (whatsapp_url != other.whatsapp_url) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (is_show_whatsapp != false) hash ^= is_show_whatsapp.GetHashCode();
      if (whatsapp_url.Length != 0) hash ^= whatsapp_url.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (is_show_whatsapp != false) {
        output.WriteRawTag(8);
        output.WriteBool(is_show_whatsapp);
      }
      if (whatsapp_url.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(whatsapp_url);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (is_show_whatsapp != false) {
        output.WriteRawTag(8);
        output.WriteBool(is_show_whatsapp);
      }
      if (whatsapp_url.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(whatsapp_url);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (is_show_whatsapp != false) {
        size += 1 + 1;
      }
      if (whatsapp_url.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(whatsapp_url);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_WhatsappInfo other) {
      if (other == null) {
        return;
      }
      if (other.is_show_whatsapp != false) {
        is_show_whatsapp = other.is_show_whatsapp;
      }
      if (other.whatsapp_url.Length != 0) {
        whatsapp_url = other.whatsapp_url;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            is_show_whatsapp = input.ReadBool();
            break;
          }
          case 18: {
            whatsapp_url = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            is_show_whatsapp = input.ReadBool();
            break;
          }
          case 18: {
            whatsapp_url = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 系统操作配置类
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_SystemOperationConfig : pb::IMessage<PB_SystemOperationConfig>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_SystemOperationConfig> _parser = new pb::MessageParser<PB_SystemOperationConfig>(() => new PB_SystemOperationConfig());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_SystemOperationConfig> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SystemOperationConfig() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SystemOperationConfig(PB_SystemOperationConfig other) : this() {
      is_show_app_rating_ = other.is_show_app_rating_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SystemOperationConfig Clone() {
      return new PB_SystemOperationConfig(this);
    }

    /// <summary>Field number for the "is_show_app_rating" field.</summary>
    public const int is_show_app_ratingFieldNumber = 1;
    private bool is_show_app_rating_;
    /// <summary>
    /// 是否展示app评分
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_show_app_rating {
      get { return is_show_app_rating_; }
      set {
        is_show_app_rating_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_SystemOperationConfig);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_SystemOperationConfig other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (is_show_app_rating != other.is_show_app_rating) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (is_show_app_rating != false) hash ^= is_show_app_rating.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (is_show_app_rating != false) {
        output.WriteRawTag(8);
        output.WriteBool(is_show_app_rating);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (is_show_app_rating != false) {
        output.WriteRawTag(8);
        output.WriteBool(is_show_app_rating);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (is_show_app_rating != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_SystemOperationConfig other) {
      if (other == null) {
        return;
      }
      if (other.is_show_app_rating != false) {
        is_show_app_rating = other.is_show_app_rating;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            is_show_app_rating = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            is_show_app_rating = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 首页商业化信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_HomepageEconomicInfo : pb::IMessage<PB_HomepageEconomicInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_HomepageEconomicInfo> _parser = new pb::MessageParser<PB_HomepageEconomicInfo>(() => new PB_HomepageEconomicInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_HomepageEconomicInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_HomepageEconomicInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_HomepageEconomicInfo(PB_HomepageEconomicInfo other) : this() {
      show_commercial_popup_ = other.show_commercial_popup_;
      show_commercial_banner_ = other.show_commercial_banner_;
      member_info_ = other.member_info_ != null ? other.member_info_.Clone() : null;
      show_top_heart_guide_ = other.show_top_heart_guide_;
      is_commercial_new_user_ = other.is_commercial_new_user_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_HomepageEconomicInfo Clone() {
      return new PB_HomepageEconomicInfo(this);
    }

    /// <summary>Field number for the "show_commercial_popup" field.</summary>
    public const int show_commercial_popupFieldNumber = 1;
    private bool show_commercial_popup_;
    /// <summary>
    /// 是否展示商业弹窗
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool show_commercial_popup {
      get { return show_commercial_popup_; }
      set {
        show_commercial_popup_ = value;
      }
    }

    /// <summary>Field number for the "show_commercial_banner" field.</summary>
    public const int show_commercial_bannerFieldNumber = 2;
    private bool show_commercial_banner_;
    /// <summary>
    /// 是否展示商业banner
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool show_commercial_banner {
      get { return show_commercial_banner_; }
      set {
        show_commercial_banner_ = value;
      }
    }

    /// <summary>Field number for the "member_info" field.</summary>
    public const int member_infoFieldNumber = 3;
    private global::Msg.economic.PB_MemberInfoResp member_info_;
    /// <summary>
    /// 会员信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_MemberInfoResp member_info {
      get { return member_info_; }
      set {
        member_info_ = value;
      }
    }

    /// <summary>Field number for the "show_top_heart_guide" field.</summary>
    public const int show_top_heart_guideFieldNumber = 4;
    private bool show_top_heart_guide_;
    /// <summary>
    /// 是否展示头部红心引导
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool show_top_heart_guide {
      get { return show_top_heart_guide_; }
      set {
        show_top_heart_guide_ = value;
      }
    }

    /// <summary>Field number for the "is_commercial_new_user" field.</summary>
    public const int is_commercial_new_userFieldNumber = 5;
    private bool is_commercial_new_user_;
    /// <summary>
    /// 是否是商业化新用户。注：以是否开始过会员为判断依据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_commercial_new_user {
      get { return is_commercial_new_user_; }
      set {
        is_commercial_new_user_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_HomepageEconomicInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_HomepageEconomicInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (show_commercial_popup != other.show_commercial_popup) return false;
      if (show_commercial_banner != other.show_commercial_banner) return false;
      if (!object.Equals(member_info, other.member_info)) return false;
      if (show_top_heart_guide != other.show_top_heart_guide) return false;
      if (is_commercial_new_user != other.is_commercial_new_user) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (show_commercial_popup != false) hash ^= show_commercial_popup.GetHashCode();
      if (show_commercial_banner != false) hash ^= show_commercial_banner.GetHashCode();
      if (member_info_ != null) hash ^= member_info.GetHashCode();
      if (show_top_heart_guide != false) hash ^= show_top_heart_guide.GetHashCode();
      if (is_commercial_new_user != false) hash ^= is_commercial_new_user.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (show_commercial_popup != false) {
        output.WriteRawTag(8);
        output.WriteBool(show_commercial_popup);
      }
      if (show_commercial_banner != false) {
        output.WriteRawTag(16);
        output.WriteBool(show_commercial_banner);
      }
      if (member_info_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(member_info);
      }
      if (show_top_heart_guide != false) {
        output.WriteRawTag(32);
        output.WriteBool(show_top_heart_guide);
      }
      if (is_commercial_new_user != false) {
        output.WriteRawTag(40);
        output.WriteBool(is_commercial_new_user);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (show_commercial_popup != false) {
        output.WriteRawTag(8);
        output.WriteBool(show_commercial_popup);
      }
      if (show_commercial_banner != false) {
        output.WriteRawTag(16);
        output.WriteBool(show_commercial_banner);
      }
      if (member_info_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(member_info);
      }
      if (show_top_heart_guide != false) {
        output.WriteRawTag(32);
        output.WriteBool(show_top_heart_guide);
      }
      if (is_commercial_new_user != false) {
        output.WriteRawTag(40);
        output.WriteBool(is_commercial_new_user);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (show_commercial_popup != false) {
        size += 1 + 1;
      }
      if (show_commercial_banner != false) {
        size += 1 + 1;
      }
      if (member_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(member_info);
      }
      if (show_top_heart_guide != false) {
        size += 1 + 1;
      }
      if (is_commercial_new_user != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_HomepageEconomicInfo other) {
      if (other == null) {
        return;
      }
      if (other.show_commercial_popup != false) {
        show_commercial_popup = other.show_commercial_popup;
      }
      if (other.show_commercial_banner != false) {
        show_commercial_banner = other.show_commercial_banner;
      }
      if (other.member_info_ != null) {
        if (member_info_ == null) {
          member_info = new global::Msg.economic.PB_MemberInfoResp();
        }
        member_info.MergeFrom(other.member_info);
      }
      if (other.show_top_heart_guide != false) {
        show_top_heart_guide = other.show_top_heart_guide;
      }
      if (other.is_commercial_new_user != false) {
        is_commercial_new_user = other.is_commercial_new_user;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            show_commercial_popup = input.ReadBool();
            break;
          }
          case 16: {
            show_commercial_banner = input.ReadBool();
            break;
          }
          case 26: {
            if (member_info_ == null) {
              member_info = new global::Msg.economic.PB_MemberInfoResp();
            }
            input.ReadMessage(member_info);
            break;
          }
          case 32: {
            show_top_heart_guide = input.ReadBool();
            break;
          }
          case 40: {
            is_commercial_new_user = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            show_commercial_popup = input.ReadBool();
            break;
          }
          case 16: {
            show_commercial_banner = input.ReadBool();
            break;
          }
          case 26: {
            if (member_info_ == null) {
              member_info = new global::Msg.economic.PB_MemberInfoResp();
            }
            input.ReadMessage(member_info);
            break;
          }
          case 32: {
            show_top_heart_guide = input.ReadBool();
            break;
          }
          case 40: {
            is_commercial_new_user = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_HomepageUserInfo : pb::IMessage<PB_HomepageUserInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_HomepageUserInfo> _parser = new pb::MessageParser<PB_HomepageUserInfo>(() => new PB_HomepageUserInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_HomepageUserInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_HomepageUserInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_HomepageUserInfo(PB_HomepageUserInfo other) : this() {
      head_url_ = other.head_url_;
      user_dress_up_data_ = other.user_dress_up_data_ != null ? other.user_dress_up_data_.Clone() : null;
      head_item_id_ = other.head_item_id_;
      head_item_type_ = other.head_item_type_;
      has_created_role_ = other.has_created_role_;
      language_level_ = other.language_level_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_HomepageUserInfo Clone() {
      return new PB_HomepageUserInfo(this);
    }

    /// <summary>Field number for the "head_url" field.</summary>
    public const int head_urlFieldNumber = 1;
    private string head_url_ = "";
    /// <summary>
    /// 用户头像
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string head_url {
      get { return head_url_; }
      set {
        head_url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "user_dress_up_data" field.</summary>
    public const int user_dress_up_dataFieldNumber = 8;
    private global::PB_DressUpData user_dress_up_data_;
    /// <summary>
    /// 用户装饰数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_DressUpData user_dress_up_data {
      get { return user_dress_up_data_; }
      set {
        user_dress_up_data_ = value;
      }
    }

    /// <summary>Field number for the "head_item_id" field.</summary>
    public const int head_item_idFieldNumber = 9;
    private long head_item_id_;
    /// <summary>
    /// 头像ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long head_item_id {
      get { return head_item_id_; }
      set {
        head_item_id_ = value;
      }
    }

    /// <summary>Field number for the "head_item_type" field.</summary>
    public const int head_item_typeFieldNumber = 10;
    private global::PB_HeadItemType head_item_type_ = global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED;
    /// <summary>
    /// 头像类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_HeadItemType head_item_type {
      get { return head_item_type_; }
      set {
        head_item_type_ = value;
      }
    }

    /// <summary>Field number for the "has_created_role" field.</summary>
    public const int has_created_roleFieldNumber = 11;
    private bool has_created_role_;
    /// <summary>
    ///是否已创角
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool has_created_role {
      get { return has_created_role_; }
      set {
        has_created_role_ = value;
      }
    }

    /// <summary>Field number for the "language_level" field.</summary>
    public const int language_levelFieldNumber = 12;
    private string language_level_ = "";
    /// <summary>
    /// 语言等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string language_level {
      get { return language_level_; }
      set {
        language_level_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_HomepageUserInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_HomepageUserInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (head_url != other.head_url) return false;
      if (!object.Equals(user_dress_up_data, other.user_dress_up_data)) return false;
      if (head_item_id != other.head_item_id) return false;
      if (head_item_type != other.head_item_type) return false;
      if (has_created_role != other.has_created_role) return false;
      if (language_level != other.language_level) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (head_url.Length != 0) hash ^= head_url.GetHashCode();
      if (user_dress_up_data_ != null) hash ^= user_dress_up_data.GetHashCode();
      if (head_item_id != 0L) hash ^= head_item_id.GetHashCode();
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) hash ^= head_item_type.GetHashCode();
      if (has_created_role != false) hash ^= has_created_role.GetHashCode();
      if (language_level.Length != 0) hash ^= language_level.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (head_url.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(head_url);
      }
      if (user_dress_up_data_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(user_dress_up_data);
      }
      if (head_item_id != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(80);
        output.WriteEnum((int) head_item_type);
      }
      if (has_created_role != false) {
        output.WriteRawTag(88);
        output.WriteBool(has_created_role);
      }
      if (language_level.Length != 0) {
        output.WriteRawTag(98);
        output.WriteString(language_level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (head_url.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(head_url);
      }
      if (user_dress_up_data_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(user_dress_up_data);
      }
      if (head_item_id != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(80);
        output.WriteEnum((int) head_item_type);
      }
      if (has_created_role != false) {
        output.WriteRawTag(88);
        output.WriteBool(has_created_role);
      }
      if (language_level.Length != 0) {
        output.WriteRawTag(98);
        output.WriteString(language_level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (head_url.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(head_url);
      }
      if (user_dress_up_data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(user_dress_up_data);
      }
      if (head_item_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) head_item_type);
      }
      if (has_created_role != false) {
        size += 1 + 1;
      }
      if (language_level.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(language_level);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_HomepageUserInfo other) {
      if (other == null) {
        return;
      }
      if (other.head_url.Length != 0) {
        head_url = other.head_url;
      }
      if (other.user_dress_up_data_ != null) {
        if (user_dress_up_data_ == null) {
          user_dress_up_data = new global::PB_DressUpData();
        }
        user_dress_up_data.MergeFrom(other.user_dress_up_data);
      }
      if (other.head_item_id != 0L) {
        head_item_id = other.head_item_id;
      }
      if (other.head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        head_item_type = other.head_item_type;
      }
      if (other.has_created_role != false) {
        has_created_role = other.has_created_role;
      }
      if (other.language_level.Length != 0) {
        language_level = other.language_level;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            head_url = input.ReadString();
            break;
          }
          case 66: {
            if (user_dress_up_data_ == null) {
              user_dress_up_data = new global::PB_DressUpData();
            }
            input.ReadMessage(user_dress_up_data);
            break;
          }
          case 72: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 80: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
          case 88: {
            has_created_role = input.ReadBool();
            break;
          }
          case 98: {
            language_level = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            head_url = input.ReadString();
            break;
          }
          case 66: {
            if (user_dress_up_data_ == null) {
              user_dress_up_data = new global::PB_DressUpData();
            }
            input.ReadMessage(user_dress_up_data);
            break;
          }
          case 72: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 80: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
          case 88: {
            has_created_role = input.ReadBool();
            break;
          }
          case 98: {
            language_level = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserDetailReq : pb::IMessage<SS_GetUserDetailReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserDetailReq> _parser = new pb::MessageParser<SS_GetUserDetailReq>(() => new SS_GetUserDetailReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserDetailReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserDetailReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserDetailReq(SS_GetUserDetailReq other) : this() {
      user_id_ = other.user_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserDetailReq Clone() {
      return new SS_GetUserDetailReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserDetailReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserDetailReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserDetailReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_GetUserDetailAck : pb::IMessage<SS_GetUserDetailAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_GetUserDetailAck> _parser = new pb::MessageParser<SS_GetUserDetailAck>(() => new SS_GetUserDetailAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_GetUserDetailAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserDetailAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserDetailAck(SS_GetUserDetailAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_GetUserDetailAck Clone() {
      return new SS_GetUserDetailAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_UserDetailInfo data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserDetailInfo data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_GetUserDetailAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_GetUserDetailAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_GetUserDetailAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_UserDetailInfo();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_UserDetailInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_UserDetailInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserDetailInfo : pb::IMessage<PB_UserDetailInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserDetailInfo> _parser = new pb::MessageParser<PB_UserDetailInfo>(() => new PB_UserDetailInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserDetailInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserDetailInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserDetailInfo(PB_UserDetailInfo other) : this() {
      user_id_ = other.user_id_;
      player_name_ = other.player_name_;
      head_url_ = other.head_url_;
      role_unique_name_ = other.role_unique_name_;
      dress_up_data_ = other.dress_up_data_ != null ? other.dress_up_data_.Clone() : null;
      head_item_id_ = other.head_item_id_;
      head_item_type_ = other.head_item_type_;
      reg_time_ = other.reg_time_;
      country_code_ = other.country_code_;
      language_level_ = other.language_level_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserDetailInfo Clone() {
      return new PB_UserDetailInfo(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "player_name" field.</summary>
    public const int player_nameFieldNumber = 2;
    private string player_name_ = "";
    /// <summary>
    /// 用户名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string player_name {
      get { return player_name_; }
      set {
        player_name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "head_url" field.</summary>
    public const int head_urlFieldNumber = 3;
    private string head_url_ = "";
    /// <summary>
    /// 用户头像
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string head_url {
      get { return head_url_; }
      set {
        head_url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "role_unique_name" field.</summary>
    public const int role_unique_nameFieldNumber = 4;
    private string role_unique_name_ = "";
    /// <summary>
    /// 唯一名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string role_unique_name {
      get { return role_unique_name_; }
      set {
        role_unique_name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "dress_up_data" field.</summary>
    public const int dress_up_dataFieldNumber = 5;
    private global::PB_DressUpData dress_up_data_;
    /// <summary>
    /// 用户装扮
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_DressUpData dress_up_data {
      get { return dress_up_data_; }
      set {
        dress_up_data_ = value;
      }
    }

    /// <summary>Field number for the "head_item_id" field.</summary>
    public const int head_item_idFieldNumber = 6;
    private long head_item_id_;
    /// <summary>
    /// 头像ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long head_item_id {
      get { return head_item_id_; }
      set {
        head_item_id_ = value;
      }
    }

    /// <summary>Field number for the "head_item_type" field.</summary>
    public const int head_item_typeFieldNumber = 7;
    private global::PB_HeadItemType head_item_type_ = global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED;
    /// <summary>
    /// 头像类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_HeadItemType head_item_type {
      get { return head_item_type_; }
      set {
        head_item_type_ = value;
      }
    }

    /// <summary>Field number for the "reg_time" field.</summary>
    public const int reg_timeFieldNumber = 8;
    private long reg_time_;
    /// <summary>
    /// 注册时间戳（单位：毫秒）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long reg_time {
      get { return reg_time_; }
      set {
        reg_time_ = value;
      }
    }

    /// <summary>Field number for the "country_code" field.</summary>
    public const int country_codeFieldNumber = 9;
    private string country_code_ = "";
    /// <summary>
    /// 国家代码（ISO 3166-1 alpha-3）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string country_code {
      get { return country_code_; }
      set {
        country_code_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "language_level" field.</summary>
    public const int language_levelFieldNumber = 10;
    private string language_level_ = "";
    /// <summary>
    /// 语言等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string language_level {
      get { return language_level_; }
      set {
        language_level_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserDetailInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserDetailInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (player_name != other.player_name) return false;
      if (head_url != other.head_url) return false;
      if (role_unique_name != other.role_unique_name) return false;
      if (!object.Equals(dress_up_data, other.dress_up_data)) return false;
      if (head_item_id != other.head_item_id) return false;
      if (head_item_type != other.head_item_type) return false;
      if (reg_time != other.reg_time) return false;
      if (country_code != other.country_code) return false;
      if (language_level != other.language_level) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (player_name.Length != 0) hash ^= player_name.GetHashCode();
      if (head_url.Length != 0) hash ^= head_url.GetHashCode();
      if (role_unique_name.Length != 0) hash ^= role_unique_name.GetHashCode();
      if (dress_up_data_ != null) hash ^= dress_up_data.GetHashCode();
      if (head_item_id != 0L) hash ^= head_item_id.GetHashCode();
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) hash ^= head_item_type.GetHashCode();
      if (reg_time != 0L) hash ^= reg_time.GetHashCode();
      if (country_code.Length != 0) hash ^= country_code.GetHashCode();
      if (language_level.Length != 0) hash ^= language_level.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (player_name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(player_name);
      }
      if (head_url.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(head_url);
      }
      if (role_unique_name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(role_unique_name);
      }
      if (dress_up_data_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(dress_up_data);
      }
      if (head_item_id != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(56);
        output.WriteEnum((int) head_item_type);
      }
      if (reg_time != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(reg_time);
      }
      if (country_code.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(country_code);
      }
      if (language_level.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(language_level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (player_name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(player_name);
      }
      if (head_url.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(head_url);
      }
      if (role_unique_name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(role_unique_name);
      }
      if (dress_up_data_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(dress_up_data);
      }
      if (head_item_id != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(56);
        output.WriteEnum((int) head_item_type);
      }
      if (reg_time != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(reg_time);
      }
      if (country_code.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(country_code);
      }
      if (language_level.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(language_level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (player_name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(player_name);
      }
      if (head_url.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(head_url);
      }
      if (role_unique_name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(role_unique_name);
      }
      if (dress_up_data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(dress_up_data);
      }
      if (head_item_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) head_item_type);
      }
      if (reg_time != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(reg_time);
      }
      if (country_code.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(country_code);
      }
      if (language_level.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(language_level);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserDetailInfo other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.player_name.Length != 0) {
        player_name = other.player_name;
      }
      if (other.head_url.Length != 0) {
        head_url = other.head_url;
      }
      if (other.role_unique_name.Length != 0) {
        role_unique_name = other.role_unique_name;
      }
      if (other.dress_up_data_ != null) {
        if (dress_up_data_ == null) {
          dress_up_data = new global::PB_DressUpData();
        }
        dress_up_data.MergeFrom(other.dress_up_data);
      }
      if (other.head_item_id != 0L) {
        head_item_id = other.head_item_id;
      }
      if (other.head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        head_item_type = other.head_item_type;
      }
      if (other.reg_time != 0L) {
        reg_time = other.reg_time;
      }
      if (other.country_code.Length != 0) {
        country_code = other.country_code;
      }
      if (other.language_level.Length != 0) {
        language_level = other.language_level;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            player_name = input.ReadString();
            break;
          }
          case 26: {
            head_url = input.ReadString();
            break;
          }
          case 34: {
            role_unique_name = input.ReadString();
            break;
          }
          case 42: {
            if (dress_up_data_ == null) {
              dress_up_data = new global::PB_DressUpData();
            }
            input.ReadMessage(dress_up_data);
            break;
          }
          case 48: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 56: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
          case 64: {
            reg_time = input.ReadInt64();
            break;
          }
          case 74: {
            country_code = input.ReadString();
            break;
          }
          case 82: {
            language_level = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 18: {
            player_name = input.ReadString();
            break;
          }
          case 26: {
            head_url = input.ReadString();
            break;
          }
          case 34: {
            role_unique_name = input.ReadString();
            break;
          }
          case 42: {
            if (dress_up_data_ == null) {
              dress_up_data = new global::PB_DressUpData();
            }
            input.ReadMessage(dress_up_data);
            break;
          }
          case 48: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 56: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
          case 64: {
            reg_time = input.ReadInt64();
            break;
          }
          case 74: {
            country_code = input.ReadString();
            break;
          }
          case 82: {
            language_level = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_IncentiveSettlementReq : pb::IMessage<SS_IncentiveSettlementReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_IncentiveSettlementReq> _parser = new pb::MessageParser<SS_IncentiveSettlementReq>(() => new SS_IncentiveSettlementReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_IncentiveSettlementReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettlementReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettlementReq(SS_IncentiveSettlementReq other) : this() {
      task_id_ = other.task_id_;
      task_record_id_ = other.task_record_id_;
      dialog_mode_ = other.dialog_mode_;
      task_star_cnt_ = other.task_star_cnt_;
      task_cost_ = other.task_cost_;
      round_ = other.round_;
      dialog_source_ = other.dialog_source_;
      start_time_ = other.start_time_;
      end_time_ = other.end_time_;
      is_finish_new_node_ = other.is_finish_new_node_;
      is_finish_current_chapter_ = other.is_finish_current_chapter_;
      user_id_ = other.user_id_;
      total_intimacy_ = other.total_intimacy_;
      hit_knowledge_cnt_ = other.hit_knowledge_cnt_;
      correct_rate_ = other.correct_rate_;
      level_type_ = other.level_type_;
      exp_type_ = other.exp_type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettlementReq Clone() {
      return new SS_IncentiveSettlementReq(this);
    }

    /// <summary>Field number for the "task_id" field.</summary>
    public const int task_idFieldNumber = 1;
    private long task_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long task_id {
      get { return task_id_; }
      set {
        task_id_ = value;
      }
    }

    /// <summary>Field number for the "task_record_id" field.</summary>
    public const int task_record_idFieldNumber = 2;
    private long task_record_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long task_record_id {
      get { return task_record_id_; }
      set {
        task_record_id_ = value;
      }
    }

    /// <summary>Field number for the "dialog_mode" field.</summary>
    public const int dialog_modeFieldNumber = 3;
    private global::Msg.basic.PB_DialogMode dialog_mode_ = global::Msg.basic.PB_DialogMode.MNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogMode dialog_mode {
      get { return dialog_mode_; }
      set {
        dialog_mode_ = value;
      }
    }

    /// <summary>Field number for the "task_star_cnt" field.</summary>
    public const int task_star_cntFieldNumber = 4;
    private int task_star_cnt_;
    /// <summary>
    ///任务星星数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int task_star_cnt {
      get { return task_star_cnt_; }
      set {
        task_star_cnt_ = value;
      }
    }

    /// <summary>Field number for the "task_cost" field.</summary>
    public const int task_costFieldNumber = 5;
    private long task_cost_;
    /// <summary>
    /// 任务耗时（单位：秒）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long task_cost {
      get { return task_cost_; }
      set {
        task_cost_ = value;
      }
    }

    /// <summary>Field number for the "round" field.</summary>
    public const int roundFieldNumber = 6;
    private long round_;
    /// <summary>
    /// 自由对话轮数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long round {
      get { return round_; }
      set {
        round_ = value;
      }
    }

    /// <summary>Field number for the "dialog_source" field.</summary>
    public const int dialog_sourceFieldNumber = 7;
    private global::Msg.basic.PB_DialogSourceEnum dialog_source_ = global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone;
    /// <summary>
    /// 来源入口（比如碎片化练习有补充体力入口和常规入口）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogSourceEnum dialog_source {
      get { return dialog_source_; }
      set {
        dialog_source_ = value;
      }
    }

    /// <summary>Field number for the "start_time" field.</summary>
    public const int start_timeFieldNumber = 8;
    private long start_time_;
    /// <summary>
    /// 会话开始时间戳（单位：毫秒）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long start_time {
      get { return start_time_; }
      set {
        start_time_ = value;
      }
    }

    /// <summary>Field number for the "end_time" field.</summary>
    public const int end_timeFieldNumber = 9;
    private long end_time_;
    /// <summary>
    /// 会话结束时间戳（单位：毫秒）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long end_time {
      get { return end_time_; }
      set {
        end_time_ = value;
      }
    }

    /// <summary>Field number for the "is_finish_new_node" field.</summary>
    public const int is_finish_new_nodeFieldNumber = 10;
    private bool is_finish_new_node_;
    /// <summary>
    ///是否是首次完成当前 node（chapter 前进一步）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_finish_new_node {
      get { return is_finish_new_node_; }
      set {
        is_finish_new_node_ = value;
      }
    }

    /// <summary>Field number for the "is_finish_current_chapter" field.</summary>
    public const int is_finish_current_chapterFieldNumber = 11;
    private bool is_finish_current_chapter_;
    /// <summary>
    /// 是否完成当前 chapter
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_finish_current_chapter {
      get { return is_finish_current_chapter_; }
      set {
        is_finish_current_chapter_ = value;
      }
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 12;
    private long user_id_;
    /// <summary>
    /// 用户ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "total_intimacy" field.</summary>
    public const int total_intimacyFieldNumber = 13;
    private long total_intimacy_;
    /// <summary>
    /// 用户总亲密度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long total_intimacy {
      get { return total_intimacy_; }
      set {
        total_intimacy_ = value;
      }
    }

    /// <summary>Field number for the "hit_knowledge_cnt" field.</summary>
    public const int hit_knowledge_cntFieldNumber = 14;
    private int hit_knowledge_cnt_;
    /// <summary>
    /// 命中知识点数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int hit_knowledge_cnt {
      get { return hit_knowledge_cnt_; }
      set {
        hit_knowledge_cnt_ = value;
      }
    }

    /// <summary>Field number for the "correct_rate" field.</summary>
    public const int correct_rateFieldNumber = 15;
    private long correct_rate_;
    /// <summary>
    /// 正确率
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long correct_rate {
      get { return correct_rate_; }
      set {
        correct_rate_ = value;
      }
    }

    /// <summary>Field number for the "level_type" field.</summary>
    public const int level_typeFieldNumber = 16;
    private global::Msg.basic.PB_LevelTypeEnum level_type_ = global::Msg.basic.PB_LevelTypeEnum.LTNone;
    /// <summary>
    /// 等级类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_LevelTypeEnum level_type {
      get { return level_type_; }
      set {
        level_type_ = value;
      }
    }

    /// <summary>Field number for the "exp_type" field.</summary>
    public const int exp_typeFieldNumber = 17;
    private global::Msg.basic.PB_ExpTypeEnum exp_type_ = global::Msg.basic.PB_ExpTypeEnum.ETNone;
    /// <summary>
    /// 经验类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_ExpTypeEnum exp_type {
      get { return exp_type_; }
      set {
        exp_type_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_IncentiveSettlementReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_IncentiveSettlementReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (task_id != other.task_id) return false;
      if (task_record_id != other.task_record_id) return false;
      if (dialog_mode != other.dialog_mode) return false;
      if (task_star_cnt != other.task_star_cnt) return false;
      if (task_cost != other.task_cost) return false;
      if (round != other.round) return false;
      if (dialog_source != other.dialog_source) return false;
      if (start_time != other.start_time) return false;
      if (end_time != other.end_time) return false;
      if (is_finish_new_node != other.is_finish_new_node) return false;
      if (is_finish_current_chapter != other.is_finish_current_chapter) return false;
      if (user_id != other.user_id) return false;
      if (total_intimacy != other.total_intimacy) return false;
      if (hit_knowledge_cnt != other.hit_knowledge_cnt) return false;
      if (correct_rate != other.correct_rate) return false;
      if (level_type != other.level_type) return false;
      if (exp_type != other.exp_type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (task_id != 0L) hash ^= task_id.GetHashCode();
      if (task_record_id != 0L) hash ^= task_record_id.GetHashCode();
      if (dialog_mode != global::Msg.basic.PB_DialogMode.MNone) hash ^= dialog_mode.GetHashCode();
      if (task_star_cnt != 0) hash ^= task_star_cnt.GetHashCode();
      if (task_cost != 0L) hash ^= task_cost.GetHashCode();
      if (round != 0L) hash ^= round.GetHashCode();
      if (dialog_source != global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone) hash ^= dialog_source.GetHashCode();
      if (start_time != 0L) hash ^= start_time.GetHashCode();
      if (end_time != 0L) hash ^= end_time.GetHashCode();
      if (is_finish_new_node != false) hash ^= is_finish_new_node.GetHashCode();
      if (is_finish_current_chapter != false) hash ^= is_finish_current_chapter.GetHashCode();
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (total_intimacy != 0L) hash ^= total_intimacy.GetHashCode();
      if (hit_knowledge_cnt != 0) hash ^= hit_knowledge_cnt.GetHashCode();
      if (correct_rate != 0L) hash ^= correct_rate.GetHashCode();
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) hash ^= level_type.GetHashCode();
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) hash ^= exp_type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (task_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(task_id);
      }
      if (task_record_id != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(task_record_id);
      }
      if (dialog_mode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(24);
        output.WriteEnum((int) dialog_mode);
      }
      if (task_star_cnt != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(task_star_cnt);
      }
      if (task_cost != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(task_cost);
      }
      if (round != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(round);
      }
      if (dialog_source != global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone) {
        output.WriteRawTag(56);
        output.WriteEnum((int) dialog_source);
      }
      if (start_time != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(start_time);
      }
      if (end_time != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(end_time);
      }
      if (is_finish_new_node != false) {
        output.WriteRawTag(80);
        output.WriteBool(is_finish_new_node);
      }
      if (is_finish_current_chapter != false) {
        output.WriteRawTag(88);
        output.WriteBool(is_finish_current_chapter);
      }
      if (user_id != 0L) {
        output.WriteRawTag(96);
        output.WriteInt64(user_id);
      }
      if (total_intimacy != 0L) {
        output.WriteRawTag(104);
        output.WriteInt64(total_intimacy);
      }
      if (hit_knowledge_cnt != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(hit_knowledge_cnt);
      }
      if (correct_rate != 0L) {
        output.WriteRawTag(120);
        output.WriteInt64(correct_rate);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(128, 1);
        output.WriteEnum((int) level_type);
      }
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        output.WriteRawTag(136, 1);
        output.WriteEnum((int) exp_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (task_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(task_id);
      }
      if (task_record_id != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(task_record_id);
      }
      if (dialog_mode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(24);
        output.WriteEnum((int) dialog_mode);
      }
      if (task_star_cnt != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(task_star_cnt);
      }
      if (task_cost != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(task_cost);
      }
      if (round != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(round);
      }
      if (dialog_source != global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone) {
        output.WriteRawTag(56);
        output.WriteEnum((int) dialog_source);
      }
      if (start_time != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(start_time);
      }
      if (end_time != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(end_time);
      }
      if (is_finish_new_node != false) {
        output.WriteRawTag(80);
        output.WriteBool(is_finish_new_node);
      }
      if (is_finish_current_chapter != false) {
        output.WriteRawTag(88);
        output.WriteBool(is_finish_current_chapter);
      }
      if (user_id != 0L) {
        output.WriteRawTag(96);
        output.WriteInt64(user_id);
      }
      if (total_intimacy != 0L) {
        output.WriteRawTag(104);
        output.WriteInt64(total_intimacy);
      }
      if (hit_knowledge_cnt != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(hit_knowledge_cnt);
      }
      if (correct_rate != 0L) {
        output.WriteRawTag(120);
        output.WriteInt64(correct_rate);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(128, 1);
        output.WriteEnum((int) level_type);
      }
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        output.WriteRawTag(136, 1);
        output.WriteEnum((int) exp_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (task_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(task_id);
      }
      if (task_record_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(task_record_id);
      }
      if (dialog_mode != global::Msg.basic.PB_DialogMode.MNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) dialog_mode);
      }
      if (task_star_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(task_star_cnt);
      }
      if (task_cost != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(task_cost);
      }
      if (round != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(round);
      }
      if (dialog_source != global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) dialog_source);
      }
      if (start_time != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(start_time);
      }
      if (end_time != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(end_time);
      }
      if (is_finish_new_node != false) {
        size += 1 + 1;
      }
      if (is_finish_current_chapter != false) {
        size += 1 + 1;
      }
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (total_intimacy != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(total_intimacy);
      }
      if (hit_knowledge_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(hit_knowledge_cnt);
      }
      if (correct_rate != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(correct_rate);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) level_type);
      }
      if (exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) exp_type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_IncentiveSettlementReq other) {
      if (other == null) {
        return;
      }
      if (other.task_id != 0L) {
        task_id = other.task_id;
      }
      if (other.task_record_id != 0L) {
        task_record_id = other.task_record_id;
      }
      if (other.dialog_mode != global::Msg.basic.PB_DialogMode.MNone) {
        dialog_mode = other.dialog_mode;
      }
      if (other.task_star_cnt != 0) {
        task_star_cnt = other.task_star_cnt;
      }
      if (other.task_cost != 0L) {
        task_cost = other.task_cost;
      }
      if (other.round != 0L) {
        round = other.round;
      }
      if (other.dialog_source != global::Msg.basic.PB_DialogSourceEnum.DialogSourceNone) {
        dialog_source = other.dialog_source;
      }
      if (other.start_time != 0L) {
        start_time = other.start_time;
      }
      if (other.end_time != 0L) {
        end_time = other.end_time;
      }
      if (other.is_finish_new_node != false) {
        is_finish_new_node = other.is_finish_new_node;
      }
      if (other.is_finish_current_chapter != false) {
        is_finish_current_chapter = other.is_finish_current_chapter;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.total_intimacy != 0L) {
        total_intimacy = other.total_intimacy;
      }
      if (other.hit_knowledge_cnt != 0) {
        hit_knowledge_cnt = other.hit_knowledge_cnt;
      }
      if (other.correct_rate != 0L) {
        correct_rate = other.correct_rate;
      }
      if (other.level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        level_type = other.level_type;
      }
      if (other.exp_type != global::Msg.basic.PB_ExpTypeEnum.ETNone) {
        exp_type = other.exp_type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            task_id = input.ReadInt64();
            break;
          }
          case 16: {
            task_record_id = input.ReadInt64();
            break;
          }
          case 24: {
            dialog_mode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 32: {
            task_star_cnt = input.ReadInt32();
            break;
          }
          case 40: {
            task_cost = input.ReadInt64();
            break;
          }
          case 48: {
            round = input.ReadInt64();
            break;
          }
          case 56: {
            dialog_source = (global::Msg.basic.PB_DialogSourceEnum) input.ReadEnum();
            break;
          }
          case 64: {
            start_time = input.ReadInt64();
            break;
          }
          case 72: {
            end_time = input.ReadInt64();
            break;
          }
          case 80: {
            is_finish_new_node = input.ReadBool();
            break;
          }
          case 88: {
            is_finish_current_chapter = input.ReadBool();
            break;
          }
          case 96: {
            user_id = input.ReadInt64();
            break;
          }
          case 104: {
            total_intimacy = input.ReadInt64();
            break;
          }
          case 112: {
            hit_knowledge_cnt = input.ReadInt32();
            break;
          }
          case 120: {
            correct_rate = input.ReadInt64();
            break;
          }
          case 128: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 136: {
            exp_type = (global::Msg.basic.PB_ExpTypeEnum) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            task_id = input.ReadInt64();
            break;
          }
          case 16: {
            task_record_id = input.ReadInt64();
            break;
          }
          case 24: {
            dialog_mode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 32: {
            task_star_cnt = input.ReadInt32();
            break;
          }
          case 40: {
            task_cost = input.ReadInt64();
            break;
          }
          case 48: {
            round = input.ReadInt64();
            break;
          }
          case 56: {
            dialog_source = (global::Msg.basic.PB_DialogSourceEnum) input.ReadEnum();
            break;
          }
          case 64: {
            start_time = input.ReadInt64();
            break;
          }
          case 72: {
            end_time = input.ReadInt64();
            break;
          }
          case 80: {
            is_finish_new_node = input.ReadBool();
            break;
          }
          case 88: {
            is_finish_current_chapter = input.ReadBool();
            break;
          }
          case 96: {
            user_id = input.ReadInt64();
            break;
          }
          case 104: {
            total_intimacy = input.ReadInt64();
            break;
          }
          case 112: {
            hit_knowledge_cnt = input.ReadInt32();
            break;
          }
          case 120: {
            correct_rate = input.ReadInt64();
            break;
          }
          case 128: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 136: {
            exp_type = (global::Msg.basic.PB_ExpTypeEnum) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_IncentiveSettlementAck : pb::IMessage<SS_IncentiveSettlementAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_IncentiveSettlementAck> _parser = new pb::MessageParser<SS_IncentiveSettlementAck>(() => new SS_IncentiveSettlementAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_IncentiveSettlementAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettlementAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettlementAck(SS_IncentiveSettlementAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettlementAck Clone() {
      return new SS_IncentiveSettlementAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_IncentiveSettlementInfo data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_IncentiveSettlementInfo data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_IncentiveSettlementAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_IncentiveSettlementAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_IncentiveSettlementAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_IncentiveSettlementInfo();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_IncentiveSettlementInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_IncentiveSettlementInfo();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_IncentiveSettlementInfo : pb::IMessage<PB_IncentiveSettlementInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_IncentiveSettlementInfo> _parser = new pb::MessageParser<PB_IncentiveSettlementInfo>(() => new PB_IncentiveSettlementInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_IncentiveSettlementInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IncentiveSettlementInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IncentiveSettlementInfo(PB_IncentiveSettlementInfo other) : this() {
      dialog_result_list_ = other.dialog_result_list_.Clone();
      experience_ = other.experience_;
      stamina_ = other.stamina_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IncentiveSettlementInfo Clone() {
      return new PB_IncentiveSettlementInfo(this);
    }

    /// <summary>Field number for the "dialog_result_list" field.</summary>
    public const int dialog_result_listFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.basic.PB_SettlementData> _repeated_dialog_result_list_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.basic.PB_SettlementData.Parser);
    private readonly pbc::RepeatedField<global::Msg.basic.PB_SettlementData> dialog_result_list_ = new pbc::RepeatedField<global::Msg.basic.PB_SettlementData>();
    /// <summary>
    /// 结算数据列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.basic.PB_SettlementData> dialog_result_list {
      get { return dialog_result_list_; }
    }

    /// <summary>Field number for the "experience" field.</summary>
    public const int experienceFieldNumber = 2;
    private int experience_;
    /// <summary>
    /// 经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int experience {
      get { return experience_; }
      set {
        experience_ = value;
      }
    }

    /// <summary>Field number for the "stamina" field.</summary>
    public const int staminaFieldNumber = 3;
    private int stamina_;
    /// <summary>
    /// 体力值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int stamina {
      get { return stamina_; }
      set {
        stamina_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_IncentiveSettlementInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_IncentiveSettlementInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!dialog_result_list_.Equals(other.dialog_result_list_)) return false;
      if (experience != other.experience) return false;
      if (stamina != other.stamina) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= dialog_result_list_.GetHashCode();
      if (experience != 0) hash ^= experience.GetHashCode();
      if (stamina != 0) hash ^= stamina.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      dialog_result_list_.WriteTo(output, _repeated_dialog_result_list_codec);
      if (experience != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(experience);
      }
      if (stamina != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(stamina);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      dialog_result_list_.WriteTo(ref output, _repeated_dialog_result_list_codec);
      if (experience != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(experience);
      }
      if (stamina != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(stamina);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += dialog_result_list_.CalculateSize(_repeated_dialog_result_list_codec);
      if (experience != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(experience);
      }
      if (stamina != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(stamina);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_IncentiveSettlementInfo other) {
      if (other == null) {
        return;
      }
      dialog_result_list_.Add(other.dialog_result_list_);
      if (other.experience != 0) {
        experience = other.experience;
      }
      if (other.stamina != 0) {
        stamina = other.stamina;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            dialog_result_list_.AddEntriesFrom(input, _repeated_dialog_result_list_codec);
            break;
          }
          case 16: {
            experience = input.ReadInt32();
            break;
          }
          case 24: {
            stamina = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            dialog_result_list_.AddEntriesFrom(ref input, _repeated_dialog_result_list_codec);
            break;
          }
          case 16: {
            experience = input.ReadInt32();
            break;
          }
          case 24: {
            stamina = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SyncOriginDataForKafkaReq : pb::IMessage<SS_SyncOriginDataForKafkaReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SyncOriginDataForKafkaReq> _parser = new pb::MessageParser<SS_SyncOriginDataForKafkaReq>(() => new SS_SyncOriginDataForKafkaReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SyncOriginDataForKafkaReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SyncOriginDataForKafkaReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SyncOriginDataForKafkaReq(SS_SyncOriginDataForKafkaReq other) : this() {
      begin_ = other.begin_;
      end_ = other.end_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SyncOriginDataForKafkaReq Clone() {
      return new SS_SyncOriginDataForKafkaReq(this);
    }

    /// <summary>Field number for the "begin" field.</summary>
    public const int beginFieldNumber = 1;
    private long begin_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long begin {
      get { return begin_; }
      set {
        begin_ = value;
      }
    }

    /// <summary>Field number for the "end" field.</summary>
    public const int endFieldNumber = 2;
    private long end_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long end {
      get { return end_; }
      set {
        end_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SyncOriginDataForKafkaReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SyncOriginDataForKafkaReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (begin != other.begin) return false;
      if (end != other.end) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (begin != 0L) hash ^= begin.GetHashCode();
      if (end != 0L) hash ^= end.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (begin != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(begin);
      }
      if (end != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(end);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (begin != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(begin);
      }
      if (end != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(end);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (begin != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(begin);
      }
      if (end != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(end);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SyncOriginDataForKafkaReq other) {
      if (other == null) {
        return;
      }
      if (other.begin != 0L) {
        begin = other.begin;
      }
      if (other.end != 0L) {
        end = other.end;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            begin = input.ReadInt64();
            break;
          }
          case 16: {
            end = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            begin = input.ReadInt64();
            break;
          }
          case 16: {
            end = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SyncOriginDataForKafkaAck : pb::IMessage<SS_SyncOriginDataForKafkaAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SyncOriginDataForKafkaAck> _parser = new pb::MessageParser<SS_SyncOriginDataForKafkaAck>(() => new SS_SyncOriginDataForKafkaAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SyncOriginDataForKafkaAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SyncOriginDataForKafkaAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SyncOriginDataForKafkaAck(SS_SyncOriginDataForKafkaAck other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SyncOriginDataForKafkaAck Clone() {
      return new SS_SyncOriginDataForKafkaAck(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SyncOriginDataForKafkaAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SyncOriginDataForKafkaAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SyncOriginDataForKafkaAck other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_SetUserDressUpReq : pb::IMessage<CS_SetUserDressUpReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_SetUserDressUpReq> _parser = new pb::MessageParser<CS_SetUserDressUpReq>(() => new CS_SetUserDressUpReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_SetUserDressUpReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserDressUpReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserDressUpReq(CS_SetUserDressUpReq other) : this() {
      items_ = other.items_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetUserDressUpReq Clone() {
      return new CS_SetUserDressUpReq(this);
    }

    /// <summary>Field number for the "items" field.</summary>
    public const int itemsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.incentive.PB_SetUserDressUpItem> _repeated_items_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.incentive.PB_SetUserDressUpItem.Parser);
    private readonly pbc::RepeatedField<global::Msg.incentive.PB_SetUserDressUpItem> items_ = new pbc::RepeatedField<global::Msg.incentive.PB_SetUserDressUpItem>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.incentive.PB_SetUserDressUpItem> items {
      get { return items_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_SetUserDressUpReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_SetUserDressUpReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!items_.Equals(other.items_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= items_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      items_.WriteTo(output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      items_.WriteTo(ref output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += items_.CalculateSize(_repeated_items_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_SetUserDressUpReq other) {
      if (other == null) {
        return;
      }
      items_.Add(other.items_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            items_.AddEntriesFrom(input, _repeated_items_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            items_.AddEntriesFrom(ref input, _repeated_items_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_SetUserDressUpItem : pb::IMessage<PB_SetUserDressUpItem>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_SetUserDressUpItem> _parser = new pb::MessageParser<PB_SetUserDressUpItem>(() => new PB_SetUserDressUpItem());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_SetUserDressUpItem> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SetUserDressUpItem() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SetUserDressUpItem(PB_SetUserDressUpItem other) : this() {
      merchandise_id_ = other.merchandise_id_;
      merchandise_sub_type_ = other.merchandise_sub_type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SetUserDressUpItem Clone() {
      return new PB_SetUserDressUpItem(this);
    }

    /// <summary>Field number for the "merchandise_id" field.</summary>
    public const int merchandise_idFieldNumber = 1;
    private string merchandise_id_ = "";
    /// <summary>
    /// 物品ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchandise_id {
      get { return merchandise_id_; }
      set {
        merchandise_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "merchandise_sub_type" field.</summary>
    public const int merchandise_sub_typeFieldNumber = 2;
    private global::Msg.basic.PB_MerchandiseSubType merchandise_sub_type_ = global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None;
    /// <summary>
    /// 商品子类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_MerchandiseSubType merchandise_sub_type {
      get { return merchandise_sub_type_; }
      set {
        merchandise_sub_type_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_SetUserDressUpItem);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_SetUserDressUpItem other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (merchandise_id != other.merchandise_id) return false;
      if (merchandise_sub_type != other.merchandise_sub_type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (merchandise_id.Length != 0) hash ^= merchandise_id.GetHashCode();
      if (merchandise_sub_type != global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None) hash ^= merchandise_sub_type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(merchandise_id);
      }
      if (merchandise_sub_type != global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) merchandise_sub_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(merchandise_id);
      }
      if (merchandise_sub_type != global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) merchandise_sub_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (merchandise_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchandise_id);
      }
      if (merchandise_sub_type != global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) merchandise_sub_type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_SetUserDressUpItem other) {
      if (other == null) {
        return;
      }
      if (other.merchandise_id.Length != 0) {
        merchandise_id = other.merchandise_id;
      }
      if (other.merchandise_sub_type != global::Msg.basic.PB_MerchandiseSubType.MerchandiseSubType_None) {
        merchandise_sub_type = other.merchandise_sub_type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            merchandise_id = input.ReadString();
            break;
          }
          case 16: {
            merchandise_sub_type = (global::Msg.basic.PB_MerchandiseSubType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            merchandise_id = input.ReadString();
            break;
          }
          case 16: {
            merchandise_sub_type = (global::Msg.basic.PB_MerchandiseSubType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_SetUserDressUpAck : pb::IMessage<SC_SetUserDressUpAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_SetUserDressUpAck> _parser = new pb::MessageParser<SC_SetUserDressUpAck>(() => new SC_SetUserDressUpAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_SetUserDressUpAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserDressUpAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserDressUpAck(SC_SetUserDressUpAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetUserDressUpAck Clone() {
      return new SC_SetUserDressUpAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_SetUserDressUpAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_SetUserDressUpAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_SetUserDressUpAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetUserDressUpMerchandiseDataReq : pb::IMessage<CS_GetUserDressUpMerchandiseDataReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetUserDressUpMerchandiseDataReq> _parser = new pb::MessageParser<CS_GetUserDressUpMerchandiseDataReq>(() => new CS_GetUserDressUpMerchandiseDataReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetUserDressUpMerchandiseDataReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserDressUpMerchandiseDataReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserDressUpMerchandiseDataReq(CS_GetUserDressUpMerchandiseDataReq other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserDressUpMerchandiseDataReq Clone() {
      return new CS_GetUserDressUpMerchandiseDataReq(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetUserDressUpMerchandiseDataReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetUserDressUpMerchandiseDataReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetUserDressUpMerchandiseDataReq other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetUserDressUpMerchandiseDataAck : pb::IMessage<SC_GetUserDressUpMerchandiseDataAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetUserDressUpMerchandiseDataAck> _parser = new pb::MessageParser<SC_GetUserDressUpMerchandiseDataAck>(() => new SC_GetUserDressUpMerchandiseDataAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetUserDressUpMerchandiseDataAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserDressUpMerchandiseDataAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserDressUpMerchandiseDataAck(SC_GetUserDressUpMerchandiseDataAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserDressUpMerchandiseDataAck Clone() {
      return new SC_GetUserDressUpMerchandiseDataAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_UserDressUpMerchandiseData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_UserDressUpMerchandiseData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetUserDressUpMerchandiseDataAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetUserDressUpMerchandiseDataAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetUserDressUpMerchandiseDataAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_UserDressUpMerchandiseData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_UserDressUpMerchandiseData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_UserDressUpMerchandiseData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserDressUpMerchandiseData : pb::IMessage<PB_UserDressUpMerchandiseData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserDressUpMerchandiseData> _parser = new pb::MessageParser<PB_UserDressUpMerchandiseData>(() => new PB_UserDressUpMerchandiseData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserDressUpMerchandiseData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserDressUpMerchandiseData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserDressUpMerchandiseData(PB_UserDressUpMerchandiseData other) : this() {
      frames_ = other.frames_.Clone();
      name_style_ = other.name_style_.Clone();
      head_items_ = other.head_items_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserDressUpMerchandiseData Clone() {
      return new PB_UserDressUpMerchandiseData(this);
    }

    /// <summary>Field number for the "frames" field.</summary>
    public const int framesFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.incentive.PB_UserMerchandiseItem> _repeated_frames_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.incentive.PB_UserMerchandiseItem.Parser);
    private readonly pbc::RepeatedField<global::Msg.incentive.PB_UserMerchandiseItem> frames_ = new pbc::RepeatedField<global::Msg.incentive.PB_UserMerchandiseItem>();
    /// <summary>
    /// 头像框列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.incentive.PB_UserMerchandiseItem> frames {
      get { return frames_; }
    }

    /// <summary>Field number for the "name_style" field.</summary>
    public const int name_styleFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Msg.incentive.PB_UserMerchandiseItem> _repeated_name_style_codec
        = pb::FieldCodec.ForMessage(18, global::Msg.incentive.PB_UserMerchandiseItem.Parser);
    private readonly pbc::RepeatedField<global::Msg.incentive.PB_UserMerchandiseItem> name_style_ = new pbc::RepeatedField<global::Msg.incentive.PB_UserMerchandiseItem>();
    /// <summary>
    /// 名字样式列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.incentive.PB_UserMerchandiseItem> name_style {
      get { return name_style_; }
    }

    /// <summary>Field number for the "head_items" field.</summary>
    public const int head_itemsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Msg.incentive.PB_HeadItemInfo> _repeated_head_items_codec
        = pb::FieldCodec.ForMessage(26, global::Msg.incentive.PB_HeadItemInfo.Parser);
    private readonly pbc::RepeatedField<global::Msg.incentive.PB_HeadItemInfo> head_items_ = new pbc::RepeatedField<global::Msg.incentive.PB_HeadItemInfo>();
    /// <summary>
    /// 头像列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.incentive.PB_HeadItemInfo> head_items {
      get { return head_items_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserDressUpMerchandiseData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserDressUpMerchandiseData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!frames_.Equals(other.frames_)) return false;
      if(!name_style_.Equals(other.name_style_)) return false;
      if(!head_items_.Equals(other.head_items_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= frames_.GetHashCode();
      hash ^= name_style_.GetHashCode();
      hash ^= head_items_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      frames_.WriteTo(output, _repeated_frames_codec);
      name_style_.WriteTo(output, _repeated_name_style_codec);
      head_items_.WriteTo(output, _repeated_head_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      frames_.WriteTo(ref output, _repeated_frames_codec);
      name_style_.WriteTo(ref output, _repeated_name_style_codec);
      head_items_.WriteTo(ref output, _repeated_head_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += frames_.CalculateSize(_repeated_frames_codec);
      size += name_style_.CalculateSize(_repeated_name_style_codec);
      size += head_items_.CalculateSize(_repeated_head_items_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserDressUpMerchandiseData other) {
      if (other == null) {
        return;
      }
      frames_.Add(other.frames_);
      name_style_.Add(other.name_style_);
      head_items_.Add(other.head_items_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            frames_.AddEntriesFrom(input, _repeated_frames_codec);
            break;
          }
          case 18: {
            name_style_.AddEntriesFrom(input, _repeated_name_style_codec);
            break;
          }
          case 26: {
            head_items_.AddEntriesFrom(input, _repeated_head_items_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            frames_.AddEntriesFrom(ref input, _repeated_frames_codec);
            break;
          }
          case 18: {
            name_style_.AddEntriesFrom(ref input, _repeated_name_style_codec);
            break;
          }
          case 26: {
            head_items_.AddEntriesFrom(ref input, _repeated_head_items_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_UserMerchandiseItem : pb::IMessage<PB_UserMerchandiseItem>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_UserMerchandiseItem> _parser = new pb::MessageParser<PB_UserMerchandiseItem>(() => new PB_UserMerchandiseItem());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_UserMerchandiseItem> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserMerchandiseItem() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserMerchandiseItem(PB_UserMerchandiseItem other) : this() {
      merchandise_id_ = other.merchandise_id_;
      amount_ = other.amount_;
      redeem_threshold_ = other.redeem_threshold_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_UserMerchandiseItem Clone() {
      return new PB_UserMerchandiseItem(this);
    }

    /// <summary>Field number for the "merchandise_id" field.</summary>
    public const int merchandise_idFieldNumber = 1;
    private string merchandise_id_ = "";
    /// <summary>
    /// 物品ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string merchandise_id {
      get { return merchandise_id_; }
      set {
        merchandise_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "amount" field.</summary>
    public const int amountFieldNumber = 2;
    private long amount_;
    /// <summary>
    /// 数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long amount {
      get { return amount_; }
      set {
        amount_ = value;
      }
    }

    /// <summary>Field number for the "redeem_threshold" field.</summary>
    public const int redeem_thresholdFieldNumber = 3;
    private long redeem_threshold_;
    /// <summary>
    /// 领取的阈值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long redeem_threshold {
      get { return redeem_threshold_; }
      set {
        redeem_threshold_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_UserMerchandiseItem);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_UserMerchandiseItem other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (merchandise_id != other.merchandise_id) return false;
      if (amount != other.amount) return false;
      if (redeem_threshold != other.redeem_threshold) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (merchandise_id.Length != 0) hash ^= merchandise_id.GetHashCode();
      if (amount != 0L) hash ^= amount.GetHashCode();
      if (redeem_threshold != 0L) hash ^= redeem_threshold.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(merchandise_id);
      }
      if (amount != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(amount);
      }
      if (redeem_threshold != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(redeem_threshold);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (merchandise_id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(merchandise_id);
      }
      if (amount != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(amount);
      }
      if (redeem_threshold != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(redeem_threshold);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (merchandise_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(merchandise_id);
      }
      if (amount != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(amount);
      }
      if (redeem_threshold != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(redeem_threshold);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_UserMerchandiseItem other) {
      if (other == null) {
        return;
      }
      if (other.merchandise_id.Length != 0) {
        merchandise_id = other.merchandise_id;
      }
      if (other.amount != 0L) {
        amount = other.amount;
      }
      if (other.redeem_threshold != 0L) {
        redeem_threshold = other.redeem_threshold;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            merchandise_id = input.ReadString();
            break;
          }
          case 16: {
            amount = input.ReadInt64();
            break;
          }
          case 24: {
            redeem_threshold = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            merchandise_id = input.ReadString();
            break;
          }
          case 16: {
            amount = input.ReadInt64();
            break;
          }
          case 24: {
            redeem_threshold = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 头像信息结构体
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_HeadItemInfo : pb::IMessage<PB_HeadItemInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_HeadItemInfo> _parser = new pb::MessageParser<PB_HeadItemInfo>(() => new PB_HeadItemInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_HeadItemInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_HeadItemInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_HeadItemInfo(PB_HeadItemInfo other) : this() {
      head_item_id_ = other.head_item_id_;
      head_item_type_ = other.head_item_type_;
      is_owned_ = other.is_owned_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_HeadItemInfo Clone() {
      return new PB_HeadItemInfo(this);
    }

    /// <summary>Field number for the "head_item_id" field.</summary>
    public const int head_item_idFieldNumber = 1;
    private long head_item_id_;
    /// <summary>
    /// 头像ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long head_item_id {
      get { return head_item_id_; }
      set {
        head_item_id_ = value;
      }
    }

    /// <summary>Field number for the "head_item_type" field.</summary>
    public const int head_item_typeFieldNumber = 2;
    private global::PB_HeadItemType head_item_type_ = global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED;
    /// <summary>
    /// 头像类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PB_HeadItemType head_item_type {
      get { return head_item_type_; }
      set {
        head_item_type_ = value;
      }
    }

    /// <summary>Field number for the "is_owned" field.</summary>
    public const int is_ownedFieldNumber = 3;
    private bool is_owned_;
    /// <summary>
    /// 是否拥有
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_owned {
      get { return is_owned_; }
      set {
        is_owned_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_HeadItemInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_HeadItemInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (head_item_id != other.head_item_id) return false;
      if (head_item_type != other.head_item_type) return false;
      if (is_owned != other.is_owned) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (head_item_id != 0L) hash ^= head_item_id.GetHashCode();
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) hash ^= head_item_type.GetHashCode();
      if (is_owned != false) hash ^= is_owned.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (head_item_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(16);
        output.WriteEnum((int) head_item_type);
      }
      if (is_owned != false) {
        output.WriteRawTag(24);
        output.WriteBool(is_owned);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (head_item_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        output.WriteRawTag(16);
        output.WriteEnum((int) head_item_type);
      }
      if (is_owned != false) {
        output.WriteRawTag(24);
        output.WriteBool(is_owned);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (head_item_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(head_item_id);
      }
      if (head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) head_item_type);
      }
      if (is_owned != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_HeadItemInfo other) {
      if (other == null) {
        return;
      }
      if (other.head_item_id != 0L) {
        head_item_id = other.head_item_id;
      }
      if (other.head_item_type != global::PB_HeadItemType.HEAD_ITEM_TYPE_UNSPECIFIED) {
        head_item_type = other.head_item_type;
      }
      if (other.is_owned != false) {
        is_owned = other.is_owned;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 16: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
          case 24: {
            is_owned = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            head_item_id = input.ReadInt64();
            break;
          }
          case 16: {
            head_item_type = (global::PB_HeadItemType) input.ReadEnum();
            break;
          }
          case 24: {
            is_owned = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_SendGiftForFriendReq : pb::IMessage<CS_SendGiftForFriendReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_SendGiftForFriendReq> _parser = new pb::MessageParser<CS_SendGiftForFriendReq>(() => new CS_SendGiftForFriendReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_SendGiftForFriendReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[23]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SendGiftForFriendReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SendGiftForFriendReq(CS_SendGiftForFriendReq other) : this() {
      merchandise_type_ = other.merchandise_type_;
      merchandise_val_ = other.merchandise_val_;
      count_ = other.count_;
      friend_id_ = other.friend_id_;
      rebate_gift_id_ = other.rebate_gift_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SendGiftForFriendReq Clone() {
      return new CS_SendGiftForFriendReq(this);
    }

    /// <summary>Field number for the "merchandise_type" field.</summary>
    public const int merchandise_typeFieldNumber = 1;
    private global::Msg.basic.PB_MerchandiseItemType merchandise_type_ = global::Msg.basic.PB_MerchandiseItemType.MerchandiseItemType_None;
    /// <summary>
    /// 礼物类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_MerchandiseItemType merchandise_type {
      get { return merchandise_type_; }
      set {
        merchandise_type_ = value;
      }
    }

    /// <summary>Field number for the "merchandise_val" field.</summary>
    public const int merchandise_valFieldNumber = 2;
    private long merchandise_val_;
    /// <summary>
    /// 礼物数值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long merchandise_val {
      get { return merchandise_val_; }
      set {
        merchandise_val_ = value;
      }
    }

    /// <summary>Field number for the "count" field.</summary>
    public const int countFieldNumber = 3;
    private long count_;
    /// <summary>
    /// 数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long count {
      get { return count_; }
      set {
        count_ = value;
      }
    }

    /// <summary>Field number for the "friend_id" field.</summary>
    public const int friend_idFieldNumber = 4;
    private long friend_id_;
    /// <summary>
    /// 好友user_id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long friend_id {
      get { return friend_id_; }
      set {
        friend_id_ = value;
      }
    }

    /// <summary>Field number for the "rebate_gift_id" field.</summary>
    public const int rebate_gift_idFieldNumber = 5;
    private long rebate_gift_id_;
    /// <summary>
    /// 回赠礼物id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long rebate_gift_id {
      get { return rebate_gift_id_; }
      set {
        rebate_gift_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_SendGiftForFriendReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_SendGiftForFriendReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (merchandise_type != other.merchandise_type) return false;
      if (merchandise_val != other.merchandise_val) return false;
      if (count != other.count) return false;
      if (friend_id != other.friend_id) return false;
      if (rebate_gift_id != other.rebate_gift_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (merchandise_type != global::Msg.basic.PB_MerchandiseItemType.MerchandiseItemType_None) hash ^= merchandise_type.GetHashCode();
      if (merchandise_val != 0L) hash ^= merchandise_val.GetHashCode();
      if (count != 0L) hash ^= count.GetHashCode();
      if (friend_id != 0L) hash ^= friend_id.GetHashCode();
      if (rebate_gift_id != 0L) hash ^= rebate_gift_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (merchandise_type != global::Msg.basic.PB_MerchandiseItemType.MerchandiseItemType_None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) merchandise_type);
      }
      if (merchandise_val != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(merchandise_val);
      }
      if (count != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(count);
      }
      if (friend_id != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(friend_id);
      }
      if (rebate_gift_id != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(rebate_gift_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (merchandise_type != global::Msg.basic.PB_MerchandiseItemType.MerchandiseItemType_None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) merchandise_type);
      }
      if (merchandise_val != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(merchandise_val);
      }
      if (count != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(count);
      }
      if (friend_id != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(friend_id);
      }
      if (rebate_gift_id != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(rebate_gift_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (merchandise_type != global::Msg.basic.PB_MerchandiseItemType.MerchandiseItemType_None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) merchandise_type);
      }
      if (merchandise_val != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(merchandise_val);
      }
      if (count != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(count);
      }
      if (friend_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(friend_id);
      }
      if (rebate_gift_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(rebate_gift_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_SendGiftForFriendReq other) {
      if (other == null) {
        return;
      }
      if (other.merchandise_type != global::Msg.basic.PB_MerchandiseItemType.MerchandiseItemType_None) {
        merchandise_type = other.merchandise_type;
      }
      if (other.merchandise_val != 0L) {
        merchandise_val = other.merchandise_val;
      }
      if (other.count != 0L) {
        count = other.count;
      }
      if (other.friend_id != 0L) {
        friend_id = other.friend_id;
      }
      if (other.rebate_gift_id != 0L) {
        rebate_gift_id = other.rebate_gift_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            merchandise_type = (global::Msg.basic.PB_MerchandiseItemType) input.ReadEnum();
            break;
          }
          case 16: {
            merchandise_val = input.ReadInt64();
            break;
          }
          case 24: {
            count = input.ReadInt64();
            break;
          }
          case 32: {
            friend_id = input.ReadInt64();
            break;
          }
          case 40: {
            rebate_gift_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            merchandise_type = (global::Msg.basic.PB_MerchandiseItemType) input.ReadEnum();
            break;
          }
          case 16: {
            merchandise_val = input.ReadInt64();
            break;
          }
          case 24: {
            count = input.ReadInt64();
            break;
          }
          case 32: {
            friend_id = input.ReadInt64();
            break;
          }
          case 40: {
            rebate_gift_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_SendGiftForFriendAck : pb::IMessage<SC_SendGiftForFriendAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_SendGiftForFriendAck> _parser = new pb::MessageParser<SC_SendGiftForFriendAck>(() => new SC_SendGiftForFriendAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_SendGiftForFriendAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[24]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SendGiftForFriendAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SendGiftForFriendAck(SC_SendGiftForFriendAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SendGiftForFriendAck Clone() {
      return new SC_SendGiftForFriendAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_SendGiftForFriendAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_SendGiftForFriendAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_SendGiftForFriendAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_SetShowStateReq : pb::IMessage<CS_SetShowStateReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_SetShowStateReq> _parser = new pb::MessageParser<CS_SetShowStateReq>(() => new CS_SetShowStateReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_SetShowStateReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[25]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetShowStateReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetShowStateReq(CS_SetShowStateReq other) : this() {
      show_item_type_ = other.show_item_type_;
      show_item_id_ = other.show_item_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_SetShowStateReq Clone() {
      return new CS_SetShowStateReq(this);
    }

    /// <summary>Field number for the "show_item_type" field.</summary>
    public const int show_item_typeFieldNumber = 1;
    private global::Msg.incentive.PB_ShowItemType show_item_type_ = global::Msg.incentive.PB_ShowItemType.STNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_ShowItemType show_item_type {
      get { return show_item_type_; }
      set {
        show_item_type_ = value;
      }
    }

    /// <summary>Field number for the "show_item_id" field.</summary>
    public const int show_item_idFieldNumber = 2;
    private string show_item_id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string show_item_id {
      get { return show_item_id_; }
      set {
        show_item_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_SetShowStateReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_SetShowStateReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (show_item_type != other.show_item_type) return false;
      if (show_item_id != other.show_item_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (show_item_type != global::Msg.incentive.PB_ShowItemType.STNone) hash ^= show_item_type.GetHashCode();
      if (show_item_id.Length != 0) hash ^= show_item_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (show_item_type != global::Msg.incentive.PB_ShowItemType.STNone) {
        output.WriteRawTag(8);
        output.WriteEnum((int) show_item_type);
      }
      if (show_item_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(show_item_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (show_item_type != global::Msg.incentive.PB_ShowItemType.STNone) {
        output.WriteRawTag(8);
        output.WriteEnum((int) show_item_type);
      }
      if (show_item_id.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(show_item_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (show_item_type != global::Msg.incentive.PB_ShowItemType.STNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) show_item_type);
      }
      if (show_item_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(show_item_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_SetShowStateReq other) {
      if (other == null) {
        return;
      }
      if (other.show_item_type != global::Msg.incentive.PB_ShowItemType.STNone) {
        show_item_type = other.show_item_type;
      }
      if (other.show_item_id.Length != 0) {
        show_item_id = other.show_item_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            show_item_type = (global::Msg.incentive.PB_ShowItemType) input.ReadEnum();
            break;
          }
          case 18: {
            show_item_id = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            show_item_type = (global::Msg.incentive.PB_ShowItemType) input.ReadEnum();
            break;
          }
          case 18: {
            show_item_id = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_SetShowStateAck : pb::IMessage<SC_SetShowStateAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_SetShowStateAck> _parser = new pb::MessageParser<SC_SetShowStateAck>(() => new SC_SetShowStateAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_SetShowStateAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[26]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetShowStateAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetShowStateAck(SC_SetShowStateAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_SetShowStateAck Clone() {
      return new SC_SetShowStateAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_SetShowStateAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_SetShowStateAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_SetShowStateAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Explore业务结算请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_IncentiveSettleForExploreReq : pb::IMessage<SS_IncentiveSettleForExploreReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_IncentiveSettleForExploreReq> _parser = new pb::MessageParser<SS_IncentiveSettleForExploreReq>(() => new SS_IncentiveSettleForExploreReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_IncentiveSettleForExploreReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[27]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettleForExploreReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettleForExploreReq(SS_IncentiveSettleForExploreReq other) : this() {
      user_id_ = other.user_id_;
      time_cost_in_seconds_ = other.time_cost_in_seconds_;
      dialog_id_ = other.dialog_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettleForExploreReq Clone() {
      return new SS_IncentiveSettleForExploreReq(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int user_idFieldNumber = 1;
    private long user_id_;
    /// <summary>
    /// 用户ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long user_id {
      get { return user_id_; }
      set {
        user_id_ = value;
      }
    }

    /// <summary>Field number for the "time_cost_in_seconds" field.</summary>
    public const int time_cost_in_secondsFieldNumber = 2;
    private long time_cost_in_seconds_;
    /// <summary>
    /// 时间消耗
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long time_cost_in_seconds {
      get { return time_cost_in_seconds_; }
      set {
        time_cost_in_seconds_ = value;
      }
    }

    /// <summary>Field number for the "dialog_id" field.</summary>
    public const int dialog_idFieldNumber = 3;
    private string dialog_id_ = "";
    /// <summary>
    /// 会话ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string dialog_id {
      get { return dialog_id_; }
      set {
        dialog_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_IncentiveSettleForExploreReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_IncentiveSettleForExploreReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (user_id != other.user_id) return false;
      if (time_cost_in_seconds != other.time_cost_in_seconds) return false;
      if (dialog_id != other.dialog_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_id != 0L) hash ^= user_id.GetHashCode();
      if (time_cost_in_seconds != 0L) hash ^= time_cost_in_seconds.GetHashCode();
      if (dialog_id.Length != 0) hash ^= dialog_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (time_cost_in_seconds != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(time_cost_in_seconds);
      }
      if (dialog_id.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(dialog_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(user_id);
      }
      if (time_cost_in_seconds != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(time_cost_in_seconds);
      }
      if (dialog_id.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(dialog_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(user_id);
      }
      if (time_cost_in_seconds != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(time_cost_in_seconds);
      }
      if (dialog_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(dialog_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_IncentiveSettleForExploreReq other) {
      if (other == null) {
        return;
      }
      if (other.user_id != 0L) {
        user_id = other.user_id;
      }
      if (other.time_cost_in_seconds != 0L) {
        time_cost_in_seconds = other.time_cost_in_seconds;
      }
      if (other.dialog_id.Length != 0) {
        dialog_id = other.dialog_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            time_cost_in_seconds = input.ReadInt64();
            break;
          }
          case 26: {
            dialog_id = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            user_id = input.ReadInt64();
            break;
          }
          case 16: {
            time_cost_in_seconds = input.ReadInt64();
            break;
          }
          case 26: {
            dialog_id = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Explore业务结算响应
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_IncentiveSettleForExploreAck : pb::IMessage<SS_IncentiveSettleForExploreAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_IncentiveSettleForExploreAck> _parser = new pb::MessageParser<SS_IncentiveSettleForExploreAck>(() => new SS_IncentiveSettleForExploreAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_IncentiveSettleForExploreAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[28]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettleForExploreAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettleForExploreAck(SS_IncentiveSettleForExploreAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_IncentiveSettleForExploreAck Clone() {
      return new SS_IncentiveSettleForExploreAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    /// <summary>
    /// 响应码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_ExploreSettlementData data_;
    /// <summary>
    /// 结算数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_ExploreSettlementData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_IncentiveSettleForExploreAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_IncentiveSettleForExploreAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_IncentiveSettleForExploreAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_ExploreSettlementData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_ExploreSettlementData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_ExploreSettlementData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ExploreSettlementData : pb::IMessage<PB_ExploreSettlementData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ExploreSettlementData> _parser = new pb::MessageParser<PB_ExploreSettlementData>(() => new PB_ExploreSettlementData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ExploreSettlementData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[29]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ExploreSettlementData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ExploreSettlementData(PB_ExploreSettlementData other) : this() {
      previous_exp_ = other.previous_exp_;
      exp_change_ = other.exp_change_;
      new_exp_ = other.new_exp_;
      previous_growth_ = other.previous_growth_;
      growth_change_ = other.growth_change_;
      new_growth_ = other.new_growth_;
      today_exp_ = other.today_exp_;
      today_time_cost_in_seconds_ = other.today_time_cost_in_seconds_;
      reach_exp_cap_ = other.reach_exp_cap_;
      checkin_item_ = other.checkin_item_ != null ? other.checkin_item_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ExploreSettlementData Clone() {
      return new PB_ExploreSettlementData(this);
    }

    /// <summary>Field number for the "previous_exp" field.</summary>
    public const int previous_expFieldNumber = 1;
    private int previous_exp_;
    /// <summary>
    /// 之前的经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int previous_exp {
      get { return previous_exp_; }
      set {
        previous_exp_ = value;
      }
    }

    /// <summary>Field number for the "exp_change" field.</summary>
    public const int exp_changeFieldNumber = 2;
    private int exp_change_;
    /// <summary>
    /// 变更的经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int exp_change {
      get { return exp_change_; }
      set {
        exp_change_ = value;
      }
    }

    /// <summary>Field number for the "new_exp" field.</summary>
    public const int new_expFieldNumber = 3;
    private int new_exp_;
    /// <summary>
    /// 变更后的经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int new_exp {
      get { return new_exp_; }
      set {
        new_exp_ = value;
      }
    }

    /// <summary>Field number for the "previous_growth" field.</summary>
    public const int previous_growthFieldNumber = 4;
    private int previous_growth_;
    /// <summary>
    /// 之前的Growth值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int previous_growth {
      get { return previous_growth_; }
      set {
        previous_growth_ = value;
      }
    }

    /// <summary>Field number for the "growth_change" field.</summary>
    public const int growth_changeFieldNumber = 5;
    private int growth_change_;
    /// <summary>
    /// 变更的Growth值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int growth_change {
      get { return growth_change_; }
      set {
        growth_change_ = value;
      }
    }

    /// <summary>Field number for the "new_growth" field.</summary>
    public const int new_growthFieldNumber = 6;
    private int new_growth_;
    /// <summary>
    /// 变更后的Growth值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int new_growth {
      get { return new_growth_; }
      set {
        new_growth_ = value;
      }
    }

    /// <summary>Field number for the "today_exp" field.</summary>
    public const int today_expFieldNumber = 7;
    private int today_exp_;
    /// <summary>
    /// 今天累计经验值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int today_exp {
      get { return today_exp_; }
      set {
        today_exp_ = value;
      }
    }

    /// <summary>Field number for the "today_time_cost_in_seconds" field.</summary>
    public const int today_time_cost_in_secondsFieldNumber = 8;
    private long today_time_cost_in_seconds_;
    /// <summary>
    /// 今天累计时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long today_time_cost_in_seconds {
      get { return today_time_cost_in_seconds_; }
      set {
        today_time_cost_in_seconds_ = value;
      }
    }

    /// <summary>Field number for the "reach_exp_cap" field.</summary>
    public const int reach_exp_capFieldNumber = 9;
    private bool reach_exp_cap_;
    /// <summary>
    /// 是否达到经验上限
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool reach_exp_cap {
      get { return reach_exp_cap_; }
      set {
        reach_exp_cap_ = value;
      }
    }

    /// <summary>Field number for the "checkin_item" field.</summary>
    public const int checkin_itemFieldNumber = 10;
    private global::Msg.incentive.PB_CheckinItemForExplore checkin_item_;
    /// <summary>
    /// 签到日历
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_CheckinItemForExplore checkin_item {
      get { return checkin_item_; }
      set {
        checkin_item_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ExploreSettlementData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ExploreSettlementData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (previous_exp != other.previous_exp) return false;
      if (exp_change != other.exp_change) return false;
      if (new_exp != other.new_exp) return false;
      if (previous_growth != other.previous_growth) return false;
      if (growth_change != other.growth_change) return false;
      if (new_growth != other.new_growth) return false;
      if (today_exp != other.today_exp) return false;
      if (today_time_cost_in_seconds != other.today_time_cost_in_seconds) return false;
      if (reach_exp_cap != other.reach_exp_cap) return false;
      if (!object.Equals(checkin_item, other.checkin_item)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (previous_exp != 0) hash ^= previous_exp.GetHashCode();
      if (exp_change != 0) hash ^= exp_change.GetHashCode();
      if (new_exp != 0) hash ^= new_exp.GetHashCode();
      if (previous_growth != 0) hash ^= previous_growth.GetHashCode();
      if (growth_change != 0) hash ^= growth_change.GetHashCode();
      if (new_growth != 0) hash ^= new_growth.GetHashCode();
      if (today_exp != 0) hash ^= today_exp.GetHashCode();
      if (today_time_cost_in_seconds != 0L) hash ^= today_time_cost_in_seconds.GetHashCode();
      if (reach_exp_cap != false) hash ^= reach_exp_cap.GetHashCode();
      if (checkin_item_ != null) hash ^= checkin_item.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (previous_exp != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(previous_exp);
      }
      if (exp_change != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(exp_change);
      }
      if (new_exp != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(new_exp);
      }
      if (previous_growth != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(previous_growth);
      }
      if (growth_change != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(growth_change);
      }
      if (new_growth != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(new_growth);
      }
      if (today_exp != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(today_exp);
      }
      if (today_time_cost_in_seconds != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(today_time_cost_in_seconds);
      }
      if (reach_exp_cap != false) {
        output.WriteRawTag(72);
        output.WriteBool(reach_exp_cap);
      }
      if (checkin_item_ != null) {
        output.WriteRawTag(82);
        output.WriteMessage(checkin_item);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (previous_exp != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(previous_exp);
      }
      if (exp_change != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(exp_change);
      }
      if (new_exp != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(new_exp);
      }
      if (previous_growth != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(previous_growth);
      }
      if (growth_change != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(growth_change);
      }
      if (new_growth != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(new_growth);
      }
      if (today_exp != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(today_exp);
      }
      if (today_time_cost_in_seconds != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(today_time_cost_in_seconds);
      }
      if (reach_exp_cap != false) {
        output.WriteRawTag(72);
        output.WriteBool(reach_exp_cap);
      }
      if (checkin_item_ != null) {
        output.WriteRawTag(82);
        output.WriteMessage(checkin_item);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (previous_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(previous_exp);
      }
      if (exp_change != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(exp_change);
      }
      if (new_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(new_exp);
      }
      if (previous_growth != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(previous_growth);
      }
      if (growth_change != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(growth_change);
      }
      if (new_growth != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(new_growth);
      }
      if (today_exp != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(today_exp);
      }
      if (today_time_cost_in_seconds != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(today_time_cost_in_seconds);
      }
      if (reach_exp_cap != false) {
        size += 1 + 1;
      }
      if (checkin_item_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(checkin_item);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ExploreSettlementData other) {
      if (other == null) {
        return;
      }
      if (other.previous_exp != 0) {
        previous_exp = other.previous_exp;
      }
      if (other.exp_change != 0) {
        exp_change = other.exp_change;
      }
      if (other.new_exp != 0) {
        new_exp = other.new_exp;
      }
      if (other.previous_growth != 0) {
        previous_growth = other.previous_growth;
      }
      if (other.growth_change != 0) {
        growth_change = other.growth_change;
      }
      if (other.new_growth != 0) {
        new_growth = other.new_growth;
      }
      if (other.today_exp != 0) {
        today_exp = other.today_exp;
      }
      if (other.today_time_cost_in_seconds != 0L) {
        today_time_cost_in_seconds = other.today_time_cost_in_seconds;
      }
      if (other.reach_exp_cap != false) {
        reach_exp_cap = other.reach_exp_cap;
      }
      if (other.checkin_item_ != null) {
        if (checkin_item_ == null) {
          checkin_item = new global::Msg.incentive.PB_CheckinItemForExplore();
        }
        checkin_item.MergeFrom(other.checkin_item);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            previous_exp = input.ReadInt32();
            break;
          }
          case 16: {
            exp_change = input.ReadInt32();
            break;
          }
          case 24: {
            new_exp = input.ReadInt32();
            break;
          }
          case 32: {
            previous_growth = input.ReadInt32();
            break;
          }
          case 40: {
            growth_change = input.ReadInt32();
            break;
          }
          case 48: {
            new_growth = input.ReadInt32();
            break;
          }
          case 56: {
            today_exp = input.ReadInt32();
            break;
          }
          case 64: {
            today_time_cost_in_seconds = input.ReadInt64();
            break;
          }
          case 72: {
            reach_exp_cap = input.ReadBool();
            break;
          }
          case 82: {
            if (checkin_item_ == null) {
              checkin_item = new global::Msg.incentive.PB_CheckinItemForExplore();
            }
            input.ReadMessage(checkin_item);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            previous_exp = input.ReadInt32();
            break;
          }
          case 16: {
            exp_change = input.ReadInt32();
            break;
          }
          case 24: {
            new_exp = input.ReadInt32();
            break;
          }
          case 32: {
            previous_growth = input.ReadInt32();
            break;
          }
          case 40: {
            growth_change = input.ReadInt32();
            break;
          }
          case 48: {
            new_growth = input.ReadInt32();
            break;
          }
          case 56: {
            today_exp = input.ReadInt32();
            break;
          }
          case 64: {
            today_time_cost_in_seconds = input.ReadInt64();
            break;
          }
          case 72: {
            reach_exp_cap = input.ReadBool();
            break;
          }
          case 82: {
            if (checkin_item_ == null) {
              checkin_item = new global::Msg.incentive.PB_CheckinItemForExplore();
            }
            input.ReadMessage(checkin_item);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_CheckinItemForExplore : pb::IMessage<PB_CheckinItemForExplore>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_CheckinItemForExplore> _parser = new pb::MessageParser<PB_CheckinItemForExplore>(() => new PB_CheckinItemForExplore());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_CheckinItemForExplore> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[30]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_CheckinItemForExplore() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_CheckinItemForExplore(PB_CheckinItemForExplore other) : this() {
      continue_days_ = other.continue_days_;
      checkin_list_ = other.checkin_list_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_CheckinItemForExplore Clone() {
      return new PB_CheckinItemForExplore(this);
    }

    /// <summary>Field number for the "continue_days" field.</summary>
    public const int continue_daysFieldNumber = 1;
    private long continue_days_;
    /// <summary>
    /// 连续签到天数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long continue_days {
      get { return continue_days_; }
      set {
        continue_days_ = value;
      }
    }

    /// <summary>Field number for the "checkin_list" field.</summary>
    public const int checkin_listFieldNumber = 2;
    private static readonly pb::FieldCodec<global::PB_CheckinItem> _repeated_checkin_list_codec
        = pb::FieldCodec.ForMessage(18, global::PB_CheckinItem.Parser);
    private readonly pbc::RepeatedField<global::PB_CheckinItem> checkin_list_ = new pbc::RepeatedField<global::PB_CheckinItem>();
    /// <summary>
    /// 签到日历
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PB_CheckinItem> checkin_list {
      get { return checkin_list_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_CheckinItemForExplore);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_CheckinItemForExplore other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (continue_days != other.continue_days) return false;
      if(!checkin_list_.Equals(other.checkin_list_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (continue_days != 0L) hash ^= continue_days.GetHashCode();
      hash ^= checkin_list_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (continue_days != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(continue_days);
      }
      checkin_list_.WriteTo(output, _repeated_checkin_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (continue_days != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(continue_days);
      }
      checkin_list_.WriteTo(ref output, _repeated_checkin_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (continue_days != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(continue_days);
      }
      size += checkin_list_.CalculateSize(_repeated_checkin_list_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_CheckinItemForExplore other) {
      if (other == null) {
        return;
      }
      if (other.continue_days != 0L) {
        continue_days = other.continue_days;
      }
      checkin_list_.Add(other.checkin_list_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            continue_days = input.ReadInt64();
            break;
          }
          case 18: {
            checkin_list_.AddEntriesFrom(input, _repeated_checkin_list_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            continue_days = input.ReadInt64();
            break;
          }
          case 18: {
            checkin_list_.AddEntriesFrom(ref input, _repeated_checkin_list_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SetUserLanguageLevelReq : pb::IMessage<SS_SetUserLanguageLevelReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SetUserLanguageLevelReq> _parser = new pb::MessageParser<SS_SetUserLanguageLevelReq>(() => new SS_SetUserLanguageLevelReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SetUserLanguageLevelReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[31]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserLanguageLevelReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserLanguageLevelReq(SS_SetUserLanguageLevelReq other) : this() {
      language_level_ = other.language_level_;
      source_ = other.source_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserLanguageLevelReq Clone() {
      return new SS_SetUserLanguageLevelReq(this);
    }

    /// <summary>Field number for the "language_level" field.</summary>
    public const int language_levelFieldNumber = 1;
    private string language_level_ = "";
    /// <summary>
    /// 语言等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string language_level {
      get { return language_level_; }
      set {
        language_level_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "source" field.</summary>
    public const int sourceFieldNumber = 2;
    private global::Msg.incentive.PB_SetUserLanguageLevelSource source_ = global::Msg.incentive.PB_SetUserLanguageLevelSource.SLNone;
    /// <summary>
    /// 来源
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_SetUserLanguageLevelSource source {
      get { return source_; }
      set {
        source_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SetUserLanguageLevelReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SetUserLanguageLevelReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (language_level != other.language_level) return false;
      if (source != other.source) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (language_level.Length != 0) hash ^= language_level.GetHashCode();
      if (source != global::Msg.incentive.PB_SetUserLanguageLevelSource.SLNone) hash ^= source.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (language_level.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(language_level);
      }
      if (source != global::Msg.incentive.PB_SetUserLanguageLevelSource.SLNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) source);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (language_level.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(language_level);
      }
      if (source != global::Msg.incentive.PB_SetUserLanguageLevelSource.SLNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) source);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (language_level.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(language_level);
      }
      if (source != global::Msg.incentive.PB_SetUserLanguageLevelSource.SLNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) source);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SetUserLanguageLevelReq other) {
      if (other == null) {
        return;
      }
      if (other.language_level.Length != 0) {
        language_level = other.language_level;
      }
      if (other.source != global::Msg.incentive.PB_SetUserLanguageLevelSource.SLNone) {
        source = other.source;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            language_level = input.ReadString();
            break;
          }
          case 16: {
            source = (global::Msg.incentive.PB_SetUserLanguageLevelSource) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            language_level = input.ReadString();
            break;
          }
          case 16: {
            source = (global::Msg.incentive.PB_SetUserLanguageLevelSource) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SS_SetUserLanguageLevelAck : pb::IMessage<SS_SetUserLanguageLevelAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SS_SetUserLanguageLevelAck> _parser = new pb::MessageParser<SS_SetUserLanguageLevelAck>(() => new SS_SetUserLanguageLevelAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SS_SetUserLanguageLevelAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[32]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserLanguageLevelAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserLanguageLevelAck(SS_SetUserLanguageLevelAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SS_SetUserLanguageLevelAck Clone() {
      return new SS_SetUserLanguageLevelAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_SetUserLanguageLevelData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_SetUserLanguageLevelData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SS_SetUserLanguageLevelAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SS_SetUserLanguageLevelAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SS_SetUserLanguageLevelAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_SetUserLanguageLevelData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_SetUserLanguageLevelData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_SetUserLanguageLevelData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_SetUserLanguageLevelData : pb::IMessage<PB_SetUserLanguageLevelData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_SetUserLanguageLevelData> _parser = new pb::MessageParser<PB_SetUserLanguageLevelData>(() => new PB_SetUserLanguageLevelData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_SetUserLanguageLevelData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[33]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SetUserLanguageLevelData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SetUserLanguageLevelData(PB_SetUserLanguageLevelData other) : this() {
      language_level_ = other.language_level_;
      section_index_ = other.section_index_;
      unit_index_ = other.unit_index_;
      level_index_ = other.level_index_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_SetUserLanguageLevelData Clone() {
      return new PB_SetUserLanguageLevelData(this);
    }

    /// <summary>Field number for the "language_level" field.</summary>
    public const int language_levelFieldNumber = 1;
    private string language_level_ = "";
    /// <summary>
    /// 语言等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string language_level {
      get { return language_level_; }
      set {
        language_level_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "section_index" field.</summary>
    public const int section_indexFieldNumber = 2;
    private int section_index_;
    /// <summary>
    /// 章节索引
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int section_index {
      get { return section_index_; }
      set {
        section_index_ = value;
      }
    }

    /// <summary>Field number for the "unit_index" field.</summary>
    public const int unit_indexFieldNumber = 3;
    private int unit_index_;
    /// <summary>
    /// 单元索引
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int unit_index {
      get { return unit_index_; }
      set {
        unit_index_ = value;
      }
    }

    /// <summary>Field number for the "level_index" field.</summary>
    public const int level_indexFieldNumber = 4;
    private int level_index_;
    /// <summary>
    /// 等级索引
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int level_index {
      get { return level_index_; }
      set {
        level_index_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_SetUserLanguageLevelData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_SetUserLanguageLevelData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (language_level != other.language_level) return false;
      if (section_index != other.section_index) return false;
      if (unit_index != other.unit_index) return false;
      if (level_index != other.level_index) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (language_level.Length != 0) hash ^= language_level.GetHashCode();
      if (section_index != 0) hash ^= section_index.GetHashCode();
      if (unit_index != 0) hash ^= unit_index.GetHashCode();
      if (level_index != 0) hash ^= level_index.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (language_level.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(language_level);
      }
      if (section_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(section_index);
      }
      if (unit_index != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(unit_index);
      }
      if (level_index != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(level_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (language_level.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(language_level);
      }
      if (section_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(section_index);
      }
      if (unit_index != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(unit_index);
      }
      if (level_index != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(level_index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (language_level.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(language_level);
      }
      if (section_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(section_index);
      }
      if (unit_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(unit_index);
      }
      if (level_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(level_index);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_SetUserLanguageLevelData other) {
      if (other == null) {
        return;
      }
      if (other.language_level.Length != 0) {
        language_level = other.language_level;
      }
      if (other.section_index != 0) {
        section_index = other.section_index;
      }
      if (other.unit_index != 0) {
        unit_index = other.unit_index;
      }
      if (other.level_index != 0) {
        level_index = other.level_index;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            language_level = input.ReadString();
            break;
          }
          case 16: {
            section_index = input.ReadInt32();
            break;
          }
          case 24: {
            unit_index = input.ReadInt32();
            break;
          }
          case 32: {
            level_index = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            language_level = input.ReadString();
            break;
          }
          case 16: {
            section_index = input.ReadInt32();
            break;
          }
          case 24: {
            unit_index = input.ReadInt32();
            break;
          }
          case 32: {
            level_index = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetIncentiveDataForExploreReq : pb::IMessage<CS_GetIncentiveDataForExploreReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetIncentiveDataForExploreReq> _parser = new pb::MessageParser<CS_GetIncentiveDataForExploreReq>(() => new CS_GetIncentiveDataForExploreReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetIncentiveDataForExploreReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[34]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetIncentiveDataForExploreReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetIncentiveDataForExploreReq(CS_GetIncentiveDataForExploreReq other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetIncentiveDataForExploreReq Clone() {
      return new CS_GetIncentiveDataForExploreReq(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetIncentiveDataForExploreReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetIncentiveDataForExploreReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetIncentiveDataForExploreReq other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetIncentiveDataForExploreAck : pb::IMessage<SC_GetIncentiveDataForExploreAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetIncentiveDataForExploreAck> _parser = new pb::MessageParser<SC_GetIncentiveDataForExploreAck>(() => new SC_GetIncentiveDataForExploreAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetIncentiveDataForExploreAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[35]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetIncentiveDataForExploreAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetIncentiveDataForExploreAck(SC_GetIncentiveDataForExploreAck other) : this() {
      code_ = other.code_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetIncentiveDataForExploreAck Clone() {
      return new SC_GetIncentiveDataForExploreAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private int code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 2;
    private global::Msg.incentive.PB_IncentiveDataForExplore data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_IncentiveDataForExplore data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetIncentiveDataForExploreAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetIncentiveDataForExploreAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != 0) hash ^= code.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(code);
      }
      if (data_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(code);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetIncentiveDataForExploreAck other) {
      if (other == null) {
        return;
      }
      if (other.code != 0) {
        code = other.code;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.incentive.PB_IncentiveDataForExplore();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_IncentiveDataForExplore();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = input.ReadInt32();
            break;
          }
          case 18: {
            if (data_ == null) {
              data = new global::Msg.incentive.PB_IncentiveDataForExplore();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_IncentiveDataForExplore : pb::IMessage<PB_IncentiveDataForExplore>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_IncentiveDataForExplore> _parser = new pb::MessageParser<PB_IncentiveDataForExplore>(() => new PB_IncentiveDataForExplore());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_IncentiveDataForExplore> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[36]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IncentiveDataForExplore() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IncentiveDataForExplore(PB_IncentiveDataForExplore other) : this() {
      economic_info_ = other.economic_info_ != null ? other.economic_info_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_IncentiveDataForExplore Clone() {
      return new PB_IncentiveDataForExplore(this);
    }

    /// <summary>Field number for the "economic_info" field.</summary>
    public const int economic_infoFieldNumber = 1;
    private global::Msg.incentive.PB_EconomicInfoForExplore economic_info_;
    /// <summary>
    /// economic信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_EconomicInfoForExplore economic_info {
      get { return economic_info_; }
      set {
        economic_info_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_IncentiveDataForExplore);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_IncentiveDataForExplore other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(economic_info, other.economic_info)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (economic_info_ != null) hash ^= economic_info.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (economic_info_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(economic_info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (economic_info_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(economic_info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (economic_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(economic_info);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_IncentiveDataForExplore other) {
      if (other == null) {
        return;
      }
      if (other.economic_info_ != null) {
        if (economic_info_ == null) {
          economic_info = new global::Msg.incentive.PB_EconomicInfoForExplore();
        }
        economic_info.MergeFrom(other.economic_info);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (economic_info_ == null) {
              economic_info = new global::Msg.incentive.PB_EconomicInfoForExplore();
            }
            input.ReadMessage(economic_info);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (economic_info_ == null) {
              economic_info = new global::Msg.incentive.PB_EconomicInfoForExplore();
            }
            input.ReadMessage(economic_info);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_EconomicInfoForExplore : pb::IMessage<PB_EconomicInfoForExplore>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_EconomicInfoForExplore> _parser = new pb::MessageParser<PB_EconomicInfoForExplore>(() => new PB_EconomicInfoForExplore());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_EconomicInfoForExplore> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[37]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EconomicInfoForExplore() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EconomicInfoForExplore(PB_EconomicInfoForExplore other) : this() {
      member_info_ = other.member_info_ != null ? other.member_info_.Clone() : null;
      remaining_seconds_ = other.remaining_seconds_;
      is_consume_time_ = other.is_consume_time_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EconomicInfoForExplore Clone() {
      return new PB_EconomicInfoForExplore(this);
    }

    /// <summary>Field number for the "member_info" field.</summary>
    public const int member_infoFieldNumber = 1;
    private global::Msg.economic.PB_MemberInfoResp member_info_;
    /// <summary>
    /// 会员信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.economic.PB_MemberInfoResp member_info {
      get { return member_info_; }
      set {
        member_info_ = value;
      }
    }

    /// <summary>Field number for the "remaining_seconds" field.</summary>
    public const int remaining_secondsFieldNumber = 2;
    private int remaining_seconds_;
    /// <summary>
    /// 剩余时间（秒）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int remaining_seconds {
      get { return remaining_seconds_; }
      set {
        remaining_seconds_ = value;
      }
    }

    /// <summary>Field number for the "is_consume_time" field.</summary>
    public const int is_consume_timeFieldNumber = 3;
    private bool is_consume_time_;
    /// <summary>
    /// 是否消耗时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_consume_time {
      get { return is_consume_time_; }
      set {
        is_consume_time_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_EconomicInfoForExplore);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_EconomicInfoForExplore other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(member_info, other.member_info)) return false;
      if (remaining_seconds != other.remaining_seconds) return false;
      if (is_consume_time != other.is_consume_time) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (member_info_ != null) hash ^= member_info.GetHashCode();
      if (remaining_seconds != 0) hash ^= remaining_seconds.GetHashCode();
      if (is_consume_time != false) hash ^= is_consume_time.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (member_info_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(member_info);
      }
      if (remaining_seconds != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(remaining_seconds);
      }
      if (is_consume_time != false) {
        output.WriteRawTag(24);
        output.WriteBool(is_consume_time);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (member_info_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(member_info);
      }
      if (remaining_seconds != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(remaining_seconds);
      }
      if (is_consume_time != false) {
        output.WriteRawTag(24);
        output.WriteBool(is_consume_time);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (member_info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(member_info);
      }
      if (remaining_seconds != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(remaining_seconds);
      }
      if (is_consume_time != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_EconomicInfoForExplore other) {
      if (other == null) {
        return;
      }
      if (other.member_info_ != null) {
        if (member_info_ == null) {
          member_info = new global::Msg.economic.PB_MemberInfoResp();
        }
        member_info.MergeFrom(other.member_info);
      }
      if (other.remaining_seconds != 0) {
        remaining_seconds = other.remaining_seconds;
      }
      if (other.is_consume_time != false) {
        is_consume_time = other.is_consume_time;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (member_info_ == null) {
              member_info = new global::Msg.economic.PB_MemberInfoResp();
            }
            input.ReadMessage(member_info);
            break;
          }
          case 16: {
            remaining_seconds = input.ReadInt32();
            break;
          }
          case 24: {
            is_consume_time = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (member_info_ == null) {
              member_info = new global::Msg.economic.PB_MemberInfoResp();
            }
            input.ReadMessage(member_info);
            break;
          }
          case 16: {
            remaining_seconds = input.ReadInt32();
            break;
          }
          case 24: {
            is_consume_time = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_StartConsumeReq : pb::IMessage<CS_StartConsumeReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_StartConsumeReq> _parser = new pb::MessageParser<CS_StartConsumeReq>(() => new CS_StartConsumeReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_StartConsumeReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[38]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_StartConsumeReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_StartConsumeReq(CS_StartConsumeReq other) : this() {
      consume_type_ = other.consume_type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_StartConsumeReq Clone() {
      return new CS_StartConsumeReq(this);
    }

    /// <summary>Field number for the "consume_type" field.</summary>
    public const int consume_typeFieldNumber = 1;
    private global::Msg.incentive.PB_ConsumeType consume_type_ = global::Msg.incentive.PB_ConsumeType.CT_None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_ConsumeType consume_type {
      get { return consume_type_; }
      set {
        consume_type_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_StartConsumeReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_StartConsumeReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (consume_type != other.consume_type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (consume_type != global::Msg.incentive.PB_ConsumeType.CT_None) hash ^= consume_type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (consume_type != global::Msg.incentive.PB_ConsumeType.CT_None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) consume_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (consume_type != global::Msg.incentive.PB_ConsumeType.CT_None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) consume_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (consume_type != global::Msg.incentive.PB_ConsumeType.CT_None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) consume_type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_StartConsumeReq other) {
      if (other == null) {
        return;
      }
      if (other.consume_type != global::Msg.incentive.PB_ConsumeType.CT_None) {
        consume_type = other.consume_type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            consume_type = (global::Msg.incentive.PB_ConsumeType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            consume_type = (global::Msg.incentive.PB_ConsumeType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_StartConsumeAck : pb::IMessage<SC_StartConsumeAck>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_StartConsumeAck> _parser = new pb::MessageParser<SC_StartConsumeAck>(() => new SC_StartConsumeAck());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_StartConsumeAck> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.incentive.IncentiveReflection.Descriptor.MessageTypes[39]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_StartConsumeAck() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_StartConsumeAck(SC_StartConsumeAck other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_StartConsumeAck Clone() {
      return new SC_StartConsumeAck(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.basic.PB_Code code_ = global::Msg.basic.PB_Code.Normal;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_StartConsumeAck);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_StartConsumeAck other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.basic.PB_Code.Normal) hash ^= code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.basic.PB_Code.Normal) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.basic.PB_Code.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_StartConsumeAck other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.basic.PB_Code.Normal) {
        code = other.code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.basic.PB_Code) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code

// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/notification/base.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.notification {

  /// <summary>Holder for reflection information generated from protobuf/notification/base.proto</summary>
  public static partial class BaseReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/notification/base.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static BaseReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiBwcm90b2J1Zi9ub3RpZmljYXRpb24vYmFzZS5wcm90bypyCg1QQl9EZXZp",
            "Y2VUeXBlEhYKElBCX0RldmljZVR5cGVfTm9uZRAAEhkKFVBCX0RldmljZVR5",
            "cGVfQW5kcm9pZBABEhUKEVBCX0RldmljZVR5cGVfSU9TEAISFwoTUEJfRGV2",
            "aWNlVHlwZV9FbWFpbBADKlAKClBCX0NoYW5uZWwSEwoPUEJfQ2hhbm5lbF9O",
            "b25lEAASGAoUUEJfQ2hhbm5lbF9FbmdhZ2VsYWIQARITCg9QQl9DaGFubmVs",
            "X1NNVFAQAip0ChNQQl9Vc2VyRGV2aWNlU3RhdHVzEhwKGFBCX1VzZXJEZXZp",
            "Y2VTdGF0dXNfTm9uZRAAEh4KGlBCX1VzZXJEZXZpY2VTdGF0dXNfT25saW5l",
            "EAESHwobUEJfVXNlckRldmljZVN0YXR1c19PZmZsaW5lEAIqtQEKGlBCX05v",
            "dGlmaWNhdGlvbkNoYW5uZWxUeXBlEiMKH1BCX05vdGlmaWNhdGlvbkNoYW5u",
            "ZWxUeXBlX05vbmUQABIjCh9QQl9Ob3RpZmljYXRpb25DaGFubmVsVHlwZV9Q",
            "dXNoEAESJAogUEJfTm90aWZpY2F0aW9uQ2hhbm5lbFR5cGVfRW1haWwQAhIn",
            "CiNQQl9Ob3RpZmljYXRpb25DaGFubmVsVHlwZV9XaGF0c2FwcBADKp4CChhQ",
            "Ql9Ob3RpZmljYXRpb25Nc2dTdGF0dXMSIQodUEJfTm90aWZpY2F0aW9uTXNn",
            "U3RhdHVzX05vbmUQABIlCiFQQl9Ob3RpZmljYXRpb25Nc2dTdGF0dXNfV2Fp",
            "dFNlbmQQARIkCiBQQl9Ob3RpZmljYXRpb25Nc2dTdGF0dXNfRHJvcHBlZBAC",
            "EiQKIFBCX05vdGlmaWNhdGlvbk1zZ1N0YXR1c19TZW5kaW5nEAMSIQodUEJf",
            "Tm90aWZpY2F0aW9uTXNnU3RhdHVzX1NlbnQQBBIjCh9QQl9Ob3RpZmljYXRp",
            "b25Nc2dTdGF0dXNfRmFpbGVkEAUSJAogUEJfTm90aWZpY2F0aW9uTXNnU3Rh",
            "dHVzX0NsaWNrZWQQBkI0Wh92Zl9wcm90b2J1Zi9zZXJ2ZXIvbm90aWZpY2F0",
            "aW9uqgIQTXNnLm5vdGlmaWNhdGlvbmIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.notification.PB_DeviceType), typeof(global::Msg.notification.PB_Channel), typeof(global::Msg.notification.PB_UserDeviceStatus), typeof(global::Msg.notification.PB_NotificationChannelType), typeof(global::Msg.notification.PB_NotificationMsgStatus), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum PB_DeviceType {
    [pbr::OriginalName("PB_DeviceType_None")] PB_DeviceType_None = 0,
    [pbr::OriginalName("PB_DeviceType_Android")] PB_DeviceType_Android = 1,
    [pbr::OriginalName("PB_DeviceType_IOS")] PB_DeviceType_IOS = 2,
    [pbr::OriginalName("PB_DeviceType_Email")] PB_DeviceType_Email = 3,
  }

  public enum PB_Channel {
    [pbr::OriginalName("PB_Channel_None")] PB_Channel_None = 0,
    /// <summary>
    /// 极光通道
    /// </summary>
    [pbr::OriginalName("PB_Channel_Engagelab")] PB_Channel_Engagelab = 1,
    /// <summary>
    /// 邮件
    /// </summary>
    [pbr::OriginalName("PB_Channel_SMTP")] PB_Channel_SMTP = 2,
  }

  public enum PB_UserDeviceStatus {
    [pbr::OriginalName("PB_UserDeviceStatus_None")] PB_UserDeviceStatus_None = 0,
    [pbr::OriginalName("PB_UserDeviceStatus_Online")] PB_UserDeviceStatus_Online = 1,
    [pbr::OriginalName("PB_UserDeviceStatus_Offline")] PB_UserDeviceStatus_Offline = 2,
  }

  public enum PB_NotificationChannelType {
    [pbr::OriginalName("PB_NotificationChannelType_None")] PB_NotificationChannelType_None = 0,
    [pbr::OriginalName("PB_NotificationChannelType_Push")] PB_NotificationChannelType_Push = 1,
    [pbr::OriginalName("PB_NotificationChannelType_Email")] PB_NotificationChannelType_Email = 2,
    [pbr::OriginalName("PB_NotificationChannelType_Whatsapp")] PB_NotificationChannelType_Whatsapp = 3,
  }

  /// <summary>
  /// 消息状态机 https://visionflow.feishu.cn/wiki/STrfwJvSxiwd62kDBRlcfi1nnUe#share-CzDId4WUSoz5rWxHsACc9dojn5d
  /// </summary>
  public enum PB_NotificationMsgStatus {
    [pbr::OriginalName("PB_NotificationMsgStatus_None")] PB_NotificationMsgStatus_None = 0,
    /// <summary>
    /// 消息待发送
    /// </summary>
    [pbr::OriginalName("PB_NotificationMsgStatus_WaitSend")] PB_NotificationMsgStatus_WaitSend = 1,
    /// <summary>
    /// 消息丢弃
    /// </summary>
    [pbr::OriginalName("PB_NotificationMsgStatus_Dropped")] PB_NotificationMsgStatus_Dropped = 2,
    /// <summary>
    /// 消息发送中
    /// </summary>
    [pbr::OriginalName("PB_NotificationMsgStatus_Sending")] PB_NotificationMsgStatus_Sending = 3,
    /// <summary>
    /// 消息已送达
    /// </summary>
    [pbr::OriginalName("PB_NotificationMsgStatus_Sent")] PB_NotificationMsgStatus_Sent = 4,
    /// <summary>
    /// 发送失败
    /// </summary>
    [pbr::OriginalName("PB_NotificationMsgStatus_Failed")] PB_NotificationMsgStatus_Failed = 5,
    /// <summary>
    /// 用户已点击
    /// </summary>
    [pbr::OriginalName("PB_NotificationMsgStatus_Clicked")] PB_NotificationMsgStatus_Clicked = 6,
  }

  #endregion

}

#endregion Designer generated code

using FairyGUI;
using UnityEngine;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsIntroducePanelUI : BaseUI<ExploreFriendsIntroducePanel>
    {
        public ExploreFriendsIntroducePanelUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top;
        
        protected override bool isFullScreen => true;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);

            // 注册按钮点击事件
            AddUIEvent(ui.NextBtn.com.onClick, OnNextBtnClick);
            AddUIEvent(ui.selectList.onClickItem, OnSelectListClick);
            AddUIEvent(ui.selectList.onDragStart , OnSelectDragStartClick);
            AddUIEvent(ui.selectList.onDragMove , OnSelectDragMoveClick);
            AddUIEvent(ui.selectList.onDragEnd , OnSelectDragEndClick);
            AddUIEvent(ui.selectList.onDrop , OnSelectDropClick);
            // AddUIEvent(ui.closeBtn.onClick, OnCloseBtnClick);
            // AddUIEvent(ui.closeBtn2.onClick, OnCloseBtnClick);
            // AddUIEvent(ui.btn1.onClick, OnBtn1Click);

        }

        protected override void OnShow()
        {
            base.OnShow();

            // ui.ContentTxt1.text = "gaga";
            // ui.ContentTxt2.text = "gaga";
        }

        protected override void OnHide()
        {
            base.OnHide();
            
        }

        private void OnNextBtnClick()
        {
            //todo 抽奖动画
            
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void OnCloseBtnClick()
        {
            Hide();
        }
        
        private void OnSelectListClick()
        {
            Debug.Log("OnSelectListClick");
            
        }
        private void OnSelectDragStartClick()
        {
            Debug.Log("OnSelectDragStartClick");
        }        
        private void OnSelectDragMoveClick()
        {
            Debug.Log("OnSelectDragMoveClick");
        }    
        private void OnSelectDragEndClick()
        {
            Debug.Log("OnSelectDragEndClick");
        }    
        private void OnSelectDropClick()
        {
            Debug.Log("OnSelectDropClick");
        }           
    }
}
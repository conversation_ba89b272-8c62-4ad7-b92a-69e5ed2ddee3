using FairyGUI;
using UnityEngine;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsIntroducePanelUI : BaseUI<ExploreFriendsIntroducePanel>
    {
        // 弧形列表配置
        private readonly float[] _itemScales = { 0.4f, 0.6f, 1f, 0.6f, 0.4f }; // 5个位置的缩放比例
        private readonly float[] _itemYOffsets = { -30f, -15f, 0f, -15f, -30f }; // Y方向偏移，形成弧形
        private readonly int _centerIndex = 2; // 中心位置索引
        private readonly int _visibleItemCount = 5; // 可见item数量

        private bool _isDragging = false;
        private bool _isAnimating = false;
        private int _currentCenterItemIndex = 0; // 当前中心item的数据索引
        private List<GObject> _visibleItems = new List<GObject>(); // 当前可见的items
        private List<ArcListItemData> _listData = new List<ArcListItemData>(); // 列表数据

        public ExploreFriendsIntroducePanelUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top;

        protected override bool isFullScreen => true;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);

            // 注册按钮点击事件
            AddUIEvent(ui.NextBtn.com.onClick, OnNextBtnClick);
            AddUIEvent(ui.selectList.onClickItem, OnSelectListClick);
            AddUIEvent(ui.selectList.onDragStart , OnSelectDragStartClick);
            AddUIEvent(ui.selectList.onDragMove , OnSelectDragMoveClick);
            AddUIEvent(ui.selectList.onDragEnd , OnSelectDragEndClick);
            AddUIEvent(ui.selectList.onDrop , OnSelectDropClick);
            // AddUIEvent(ui.closeBtn.onClick, OnCloseBtnClick);
            // AddUIEvent(ui.closeBtn2.onClick, OnCloseBtnClick);
            // AddUIEvent(ui.btn1.onClick, OnBtn1Click);

            // 初始化列表设置
            InitializeList();
        }

        protected override void OnShow()
        {
            base.OnShow();

            // ui.ContentTxt1.text = "gaga";
            // ui.ContentTxt2.text = "gaga";

            // 初始化列表数据和显示
            SetupListData();
            UpdateItemsDisplay();
        }

        protected override void OnHide()
        {
            base.OnHide();

        }

        /// <summary>
        /// 初始化列表设置
        /// </summary>
        private void InitializeList()
        {
            // 设置列表为横向布局
            ui.selectList.layout = ListLayoutType.SingleRow;
            ui.selectList.scrollItemToViewOnClick = false; // 禁用默认的滚动到视图功能

            // 设置列表渲染回调
            ui.selectList.itemRenderer = RenderListItem;
        }

        /// <summary>
        /// 设置列表数据
        /// </summary>
        private void SetupListData()
        {
            // 初始化测试数据（实际使用时替换为真实数据）
            _listData.Clear();
            for (int i = 0; i < 10; i++)
            {
                _listData.Add(new ArcListItemData(
                    id: i,
                    name: $"Item {i + 1}",
                    description: $"Description for item {i + 1}",
                    isUnlocked: true
                ));
            }

            // 设置列表item数量
            ui.selectList.numItems = _listData.Count;

            // 初始化当前中心item索引
            _currentCenterItemIndex = 0;
        }

        /// <summary>
        /// 列表item渲染回调
        /// </summary>
        /// <param name="index">item索引</param>
        /// <param name="item">item对象</param>
        private void RenderListItem(int index, GObject item)
        {
            if (index >= _listData.Count) return;

            ArcListItemData data = _listData[index];

            // 设置item数据显示
            if (item.asButton != null)
            {
                item.asButton.title = data.name;
                item.data = index; // 存储索引用于点击事件

                // 根据解锁状态设置可交互性
                item.asButton.enabled = data.isUnlocked;
            }

            // 如果item有其他子组件，可以在这里设置
            // 例如：图标、描述文本等
            // GTextField descText = item.GetChild("descText").asTextField;
            // if (descText != null) descText.text = data.description;
        }

        private void OnNextBtnClick()
        {
            //todo 抽奖动画

        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void OnCloseBtnClick()
        {
            Hide();
        }

        /// <summary>
        /// 列表item点击事件
        /// </summary>
        private void OnSelectListClick(EventContext context)
        {
            if (_isAnimating) return;

            // 从事件上下文获取被点击的item
            GObject clickedItem = context.sender as GObject;
            if (clickedItem?.data != null)
            {
                int clickedIndex = (int)clickedItem.data;
                MoveToCenter(clickedIndex);
            }
        }

        private void OnSelectDragStartClick()
        {
            _isDragging = true;
            Debug.Log("OnSelectDragStartClick");
        }

        private void OnSelectDragMoveClick()
        {
            if (_isDragging)
            {
                // 在拖拽过程中实时更新item显示
                UpdateItemsDisplay();
            }
            Debug.Log("OnSelectDragMoveClick");
        }

        private void OnSelectDragEndClick()
        {
            _isDragging = false;
            Debug.Log("OnSelectDragEndClick");

            // 拖拽结束后，吸附到最近的中心位置
            SnapToCenter();
        }

        private void OnSelectDropClick()
        {
            Debug.Log("OnSelectDropClick");
        }

        /// <summary>
        /// 将指定索引的item移动到中心位置
        /// </summary>
        /// <param name="targetIndex">目标item的数据索引</param>
        private void MoveToCenter(int targetIndex)
        {
            if (_isAnimating || targetIndex == _currentCenterItemIndex) return;

            _isAnimating = true;
            _currentCenterItemIndex = targetIndex;

            // 滚动到目标位置
            ui.selectList.ScrollToView(targetIndex, true, false);

            // 延迟一帧后更新显示，确保滚动完成
            GTween.DelayedCall(0.1f).OnComplete(() =>
            {
                UpdateItemsDisplay();
                _isAnimating = false;
            });
        }

        /// <summary>
        /// 吸附到最近的中心位置
        /// </summary>
        private void SnapToCenter()
        {
            if (_isAnimating) return;

            // 计算当前最接近中心的item
            int nearestCenterIndex = CalculateNearestCenterIndex();

            if (nearestCenterIndex != _currentCenterItemIndex)
            {
                MoveToCenter(nearestCenterIndex);
            }
            else
            {
                // 如果已经在中心，只需要更新显示
                UpdateItemsDisplay();
            }
        }

        /// <summary>
        /// 计算最接近中心的item索引
        /// </summary>
        /// <returns>最接近中心的item索引</returns>
        private int CalculateNearestCenterIndex()
        {
            // 获取列表的滚动位置
            float scrollPos = ui.selectList.scrollPane.posX;
            float itemWidth = ui.selectList.defaultItemSize.x + ui.selectList.columnGap;

            // 计算最接近中心的item索引
            int nearestIndex = Mathf.RoundToInt(scrollPos / itemWidth);

            // 确保索引在有效范围内
            nearestIndex = Mathf.Clamp(nearestIndex, 0, ui.selectList.numItems - 1);

            return nearestIndex;
        }

        /// <summary>
        /// 更新items的显示效果（缩放和位置）
        /// </summary>
        private void UpdateItemsDisplay()
        {
            if (ui.selectList.numItems == 0) return;

            // 获取当前可见的items
            _visibleItems.Clear();
            for (int i = 0; i < ui.selectList.numChildren; i++)
            {
                GObject item = ui.selectList.GetChildAt(i);
                if (item.visible)
                {
                    _visibleItems.Add(item);
                }
            }

            // 计算中心位置
            float listCenterX = ui.selectList.width * 0.5f;

            // 为每个可见item设置弧形效果
            for (int i = 0; i < _visibleItems.Count; i++)
            {
                GObject item = _visibleItems[i];
                if (item.data == null) continue;

                int itemDataIndex = (int)item.data;

                // 计算item相对于中心的距离
                float itemCenterX = item.x + item.width * 0.5f;
                float distanceFromCenter = Mathf.Abs(itemCenterX - listCenterX);

                // 根据距离计算在5个位置中的索引
                int positionIndex = CalculatePositionIndex(itemCenterX, listCenterX);

                // 应用缩放和Y偏移
                ApplyArcEffect(item, positionIndex);

                // 如果是中心位置，标记为选中
                if (positionIndex == _centerIndex)
                {
                    SelectItem(item, itemDataIndex);
                }
            }
        }

        /// <summary>
        /// 计算item在5个位置中的索引
        /// </summary>
        /// <param name="itemCenterX">item中心X坐标</param>
        /// <param name="listCenterX">列表中心X坐标</param>
        /// <returns>位置索引(0-4)</returns>
        private int CalculatePositionIndex(float itemCenterX, float listCenterX)
        {
            float distance = itemCenterX - listCenterX;
            float itemWidth = ui.selectList.defaultItemSize.x + ui.selectList.columnGap;

            // 计算相对位置
            int relativePosition = Mathf.RoundToInt(distance / itemWidth);

            // 映射到0-4的范围，中心为2
            int positionIndex = _centerIndex + relativePosition;

            // 确保在有效范围内
            positionIndex = Mathf.Clamp(positionIndex, 0, _visibleItemCount - 1);

            return positionIndex;
        }

        /// <summary>
        /// 应用弧形效果（缩放和Y偏移）
        /// </summary>
        /// <param name="item">要应用效果的item</param>
        /// <param name="positionIndex">位置索引(0-4)</param>
        private void ApplyArcEffect(GObject item, int positionIndex)
        {
            if (positionIndex < 0 || positionIndex >= _itemScales.Length) return;

            float targetScale = _itemScales[positionIndex];
            float targetYOffset = _itemYOffsets[positionIndex];

            // 计算目标Y位置（原始Y + 偏移）
            float originalY = item.y;
            float targetY = originalY + targetYOffset;

            // 使用GTween进行平滑动画
            float animDuration = 0.3f;

            // 缩放动画
            item.TweenScale(new Vector2(targetScale, targetScale), animDuration)
                .SetEase(EaseType.QuadOut);

            // Y位置动画
            item.TweenMoveY(targetY, animDuration)
                .SetEase(EaseType.QuadOut);
        }

        /// <summary>
        /// 选中指定item
        /// </summary>
        /// <param name="item">要选中的item</param>
        /// <param name="itemIndex">item的数据索引</param>
        private void SelectItem(GObject item, int itemIndex)
        {
            // 更新当前选中的item索引
            _currentCenterItemIndex = itemIndex;

            // 这里可以添加选中状态的视觉反馈
            // 例如：改变颜色、添加边框等
            if (item.asButton != null)
            {
                item.asButton.selected = true;
            }

            // 触发选中事件回调
            OnItemSelected(itemIndex);
        }

        /// <summary>
        /// item选中回调
        /// </summary>
        /// <param name="selectedIndex">选中的item索引</param>
        private void OnItemSelected(int selectedIndex)
        {
            if (selectedIndex >= 0 && selectedIndex < _listData.Count)
            {
                ArcListItemData selectedData = _listData[selectedIndex];
                Debug.Log($"Item selected: {selectedData.name} (Index: {selectedIndex})");

                // 这里可以添加选中后的逻辑
                // 例如：更新UI显示、播放音效等

                // 示例：更新选中item的名称显示
                if (ui.SelectItemName != null)
                {
                    ui.SelectItemName.text = selectedData.name;
                }

                // 可以触发自定义事件
                // EventManager.Instance.TriggerEvent("ArcListItemSelected", selectedData);
            }
        }

        #region 公共接口方法

        /// <summary>
        /// 设置列表数据（外部调用）
        /// </summary>
        /// <param name="dataList">数据列表</param>
        public void SetListData(List<ArcListItemData> dataList)
        {
            _listData.Clear();
            _listData.AddRange(dataList);

            ui.selectList.numItems = _listData.Count;
            _currentCenterItemIndex = 0;

            // 刷新显示
            UpdateItemsDisplay();
        }

        /// <summary>
        /// 获取当前选中的item数据
        /// </summary>
        /// <returns>当前选中的item数据</returns>
        public ArcListItemData GetCurrentSelectedData()
        {
            if (_currentCenterItemIndex >= 0 && _currentCenterItemIndex < _listData.Count)
            {
                return _listData[_currentCenterItemIndex];
            }
            return null;
        }

        /// <summary>
        /// 获取当前选中的item索引
        /// </summary>
        /// <returns>当前选中的item索引</returns>
        public int GetCurrentSelectedIndex()
        {
            return _currentCenterItemIndex;
        }

        /// <summary>
        /// 程序化选中指定索引的item
        /// </summary>
        /// <param name="index">要选中的item索引</param>
        public void SelectItemByIndex(int index)
        {
            if (index >= 0 && index < _listData.Count)
            {
                MoveToCenter(index);
            }
        }

        #endregion
    }
}
﻿using System.Collections.Generic;
using FairyGUI;
using UIBind.Explore;
using UIBind.Explore.Item;
using UnityEngine;
using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.Explore
{
    public class ExploreAwardNoticeUI : BaseUI<ExploreAwardNotice>
    {
        public override string uiLayer => UILayerConsts.Guide;
        protected override bool isFullScreen => true;

        private GList _list;

        private ExploreController _controller;

        private List<ExploreAwardNewHandData> _datas;
        public ExploreAwardNoticeUI(string name) : base(name)
        {
        }
        
        protected override void OnInit(GComponent uiCom)
        {
            _controller = ControllerManager.instance.GetController<ExploreController>(ModelConsts.Explore) as ExploreController;

            _list = ui.listContainer;
            _list.itemRenderer = RenderItem;
            this.AddUIEvent(ui.btnClose.onClick, OnCloseBtn);
        }
        
        private async void RenderItem(int index, GObject obj)
        {
            GComponent modelItem = obj.asCom;
            ExploreNoticeItemUI uiItem = new ExploreNoticeItemUI(modelItem);
            uiItem.SetData(_datas[index]);
            
        }

        protected override void OnShow()
        {
            _datas = this.args[0] as List<ExploreAwardNewHandData>;
            _list.numItems = _datas.Count;

            APPEAR_EXPLORE_TIPS_POPUP dat = new APPEAR_EXPLORE_TIPS_POPUP();
            dat.tips_target = GetTips();
            dat.task_id = _controller.CurTaskId;
            dat.dialogue_id = _controller.CurEnterEntity.LogicEntity.DialogId;
            dat.dialogue_round = _controller.CurEnterEntity.LogicEntity.Round;
            DataDotMgr.Collect(dat);
        }

        private string GetTips()
        {
            string tips = "";
            foreach (var data in _datas)
            {
                if (data.Type == ExploreAwardNewHandDataType.Exp)
                {
                    tips += "ui_explore_xp_first";
                }
                else if (data.Type == ExploreAwardNewHandDataType.Vocabulary)
                {
                    tips += "ui_explore_vocabulary_first";
                }
            }

            return tips;
        }

        private void OnCloseBtn()
        {
            this.Hide();

            CLICK_EXPLORE_TIPS_POPUP_CLOSE data = new CLICK_EXPLORE_TIPS_POPUP_CLOSE();
            data.task_id = _controller.CurTaskId;
            data.dialogue_id = _controller.CurEnterEntity.LogicEntity.DialogId;
            data.dialogue_round = _controller.CurEnterEntity.LogicEntity.Round;
            DataDotMgr.Collect(data);
        }
    }
}
/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.FragmentPractice
{
    public partial class AduioCom : UIBindT
    {
        public override string pkgName => "FragmentPractice";
        public override string comName => "AduioCom";

        public Controller playCtrl;
        public Controller btnState;
        public GLoader btnPlay;
        public GLoader backup;
        public GLoader foward;
        public BtnAudio btn;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            playCtrl = com.GetControllerAt(0);
            btnState = com.GetControllerAt(1);
            btnPlay = (GLoader)com.GetChildAt(0);
            backup = (GLoader)com.GetChildAt(1);
            foward = (GLoader)com.GetChildAt(2);
            btn = (BtnAudio)com.GetChildAt(3);

            SetMultiLanguageInChildren();
            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            playCtrl = null;
            btnState = null;
            btnPlay = null;
            backup = null;
            foward = null;
            btn = null;
        }

        public void SetMultiLanguageInChildren()
        {
            this.btnPlay.SetKey("extends=ABtnAudio");  // ""
        }
    }
}
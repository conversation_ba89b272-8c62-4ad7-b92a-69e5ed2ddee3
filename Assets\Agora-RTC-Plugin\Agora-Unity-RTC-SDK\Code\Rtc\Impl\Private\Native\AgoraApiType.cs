#define AGORA_RTC

using System;

namespace Agora.Rtc
{
    internal static class AgoraApiType
    {
        internal const string FUNC_RTCENGINE_UNREGISTERAUDIOENCODEDFRAMEOBSERVER = "RtcEngine_unregisterAudioEncodedFrameObserver";
        internal const string FUNC_RTCENGINE_SETAPPTYPE = "RtcEngine_setAppType";
        internal const string FUNC_RTCENGINE_RELEASESCREENCAPTURESOURCES = "RtcEngine_releaseScreenCaptureSources";
        internal const string FUNC_RTCENGINE_GETNATIVEHANDLE = "RtcEngine_getNativeHandle";
        internal const string FUNC_RTCENGINE_SETMAXMETADATASIZE = "RtcEngine_setMaxMetadataSize";
        internal const string FUNC_RTCENGINE_SENDMETADATA = "RtcEngine_sendMetaData";
        internal const string FUNC_RTCENGINE_RELEASE = "RtcEngine_release";
        #region terra IRtcEngine
        internal const string FUNC_RTCENGINE_INITIALIZE = "RtcEngine_initialize_0320339";
        internal const string FUNC_RTCENGINE_QUERYINTERFACE = "RtcEngine_queryInterface_257d192";
        internal const string FUNC_RTCENGINE_GETVERSION = "RtcEngine_getVersion_915cb25";
        internal const string FUNC_RTCENGINE_GETERRORDESCRIPTION = "RtcEngine_getErrorDescription_46f8ab7";
        internal const string FUNC_RTCENGINE_QUERYCODECCAPABILITY = "RtcEngine_queryCodecCapability_ddf4f31";
        internal const string FUNC_RTCENGINE_QUERYDEVICESCORE = "RtcEngine_queryDeviceScore";
        internal const string FUNC_RTCENGINE_PRELOADCHANNEL = "RtcEngine_preloadChannel_a0779eb";
        internal const string FUNC_RTCENGINE_PRELOADCHANNELWITHUSERACCOUNT = "RtcEngine_preloadChannelWithUserAccount_0e4f59e";
        internal const string FUNC_RTCENGINE_UPDATEPRELOADCHANNELTOKEN = "RtcEngine_updatePreloadChannelToken_3a2037f";
        internal const string FUNC_RTCENGINE_JOINCHANNEL = "RtcEngine_joinChannel_f097389";
        internal const string FUNC_RTCENGINE_JOINCHANNEL2 = "RtcEngine_joinChannel_cdbb747";
        internal const string FUNC_RTCENGINE_UPDATECHANNELMEDIAOPTIONS = "RtcEngine_updateChannelMediaOptions_7bfc1d7";
        internal const string FUNC_RTCENGINE_LEAVECHANNEL = "RtcEngine_leaveChannel";
        internal const string FUNC_RTCENGINE_LEAVECHANNEL2 = "RtcEngine_leaveChannel_2c0e3aa";
        internal const string FUNC_RTCENGINE_RENEWTOKEN = "RtcEngine_renewToken_3a2037f";
        internal const string FUNC_RTCENGINE_SETCHANNELPROFILE = "RtcEngine_setChannelProfile_a78fa4f";
        internal const string FUNC_RTCENGINE_SETCLIENTROLE = "RtcEngine_setClientRole_3426fa6";
        internal const string FUNC_RTCENGINE_SETCLIENTROLE2 = "RtcEngine_setClientRole_b46cc48";
        internal const string FUNC_RTCENGINE_STARTECHOTEST = "RtcEngine_startEchoTest_16140d7";
        internal const string FUNC_RTCENGINE_STOPECHOTEST = "RtcEngine_stopEchoTest";
        internal const string FUNC_RTCENGINE_ENABLEMULTICAMERA = "RtcEngine_enableMultiCamera_bffe023";
        internal const string FUNC_RTCENGINE_ENABLEVIDEO = "RtcEngine_enableVideo";
        internal const string FUNC_RTCENGINE_DISABLEVIDEO = "RtcEngine_disableVideo";
        internal const string FUNC_RTCENGINE_STARTPREVIEW = "RtcEngine_startPreview";
        internal const string FUNC_RTCENGINE_STARTPREVIEW2 = "RtcEngine_startPreview_4fd718e";
        internal const string FUNC_RTCENGINE_STOPPREVIEW = "RtcEngine_stopPreview";
        internal const string FUNC_RTCENGINE_STOPPREVIEW2 = "RtcEngine_stopPreview_4fd718e";
        internal const string FUNC_RTCENGINE_STARTLASTMILEPROBETEST = "RtcEngine_startLastmileProbeTest_c4de423";
        internal const string FUNC_RTCENGINE_STOPLASTMILEPROBETEST = "RtcEngine_stopLastmileProbeTest";
        internal const string FUNC_RTCENGINE_SETVIDEOENCODERCONFIGURATION = "RtcEngine_setVideoEncoderConfiguration_89677d8";
        internal const string FUNC_RTCENGINE_SETBEAUTYEFFECTOPTIONS = "RtcEngine_setBeautyEffectOptions_e7635d1";
        internal const string FUNC_RTCENGINE_SETFACESHAPEBEAUTYOPTIONS = "RtcEngine_setFaceShapeBeautyOptions_a862ce7";
        internal const string FUNC_RTCENGINE_SETFACESHAPEAREAOPTIONS = "RtcEngine_setFaceShapeAreaOptions_2e242a3";
        internal const string FUNC_RTCENGINE_GETFACESHAPEBEAUTYOPTIONS = "RtcEngine_getFaceShapeBeautyOptions_8382895";
        internal const string FUNC_RTCENGINE_GETFACESHAPEAREAOPTIONS = "RtcEngine_getFaceShapeAreaOptions_0783e2c";
        internal const string FUNC_RTCENGINE_SETFILTEREFFECTOPTIONS = "RtcEngine_setFilterEffectOptions_53b4be3";
        internal const string FUNC_RTCENGINE_SETLOWLIGHTENHANCEOPTIONS = "RtcEngine_setLowlightEnhanceOptions_4f9f013";
        internal const string FUNC_RTCENGINE_SETVIDEODENOISEROPTIONS = "RtcEngine_setVideoDenoiserOptions_4e9fccc";
        internal const string FUNC_RTCENGINE_SETCOLORENHANCEOPTIONS = "RtcEngine_setColorEnhanceOptions_ecae2b3";
        internal const string FUNC_RTCENGINE_ENABLEVIRTUALBACKGROUND = "RtcEngine_enableVirtualBackground_6dd8ee4";
        internal const string FUNC_RTCENGINE_SETUPREMOTEVIDEO = "RtcEngine_setupRemoteVideo_acc9c38";
        internal const string FUNC_RTCENGINE_SETUPLOCALVIDEO = "RtcEngine_setupLocalVideo_acc9c38";
        internal const string FUNC_RTCENGINE_SETVIDEOSCENARIO = "RtcEngine_setVideoScenario_c02cd1c";
        internal const string FUNC_RTCENGINE_SETVIDEOQOEPREFERENCE = "RtcEngine_setVideoQoEPreference_c4a3d9f";
        internal const string FUNC_RTCENGINE_ENABLEAUDIO = "RtcEngine_enableAudio";
        internal const string FUNC_RTCENGINE_DISABLEAUDIO = "RtcEngine_disableAudio";
        internal const string FUNC_RTCENGINE_SETAUDIOPROFILE = "RtcEngine_setAudioProfile_d944543";
        internal const string FUNC_RTCENGINE_SETAUDIOPROFILE2 = "RtcEngine_setAudioProfile_ac39c15";
        internal const string FUNC_RTCENGINE_SETAUDIOSCENARIO = "RtcEngine_setAudioScenario_c36f5c1";
        internal const string FUNC_RTCENGINE_ENABLELOCALAUDIO = "RtcEngine_enableLocalAudio_5039d15";
        internal const string FUNC_RTCENGINE_MUTELOCALAUDIOSTREAM = "RtcEngine_muteLocalAudioStream_5039d15";
        internal const string FUNC_RTCENGINE_MUTEALLREMOTEAUDIOSTREAMS = "RtcEngine_muteAllRemoteAudioStreams_5039d15";
        internal const string FUNC_RTCENGINE_MUTEREMOTEAUDIOSTREAM = "RtcEngine_muteRemoteAudioStream_dbdc15a";
        internal const string FUNC_RTCENGINE_MUTELOCALVIDEOSTREAM = "RtcEngine_muteLocalVideoStream_5039d15";
        internal const string FUNC_RTCENGINE_ENABLELOCALVIDEO = "RtcEngine_enableLocalVideo_5039d15";
        internal const string FUNC_RTCENGINE_MUTEALLREMOTEVIDEOSTREAMS = "RtcEngine_muteAllRemoteVideoStreams_5039d15";
        internal const string FUNC_RTCENGINE_SETREMOTEDEFAULTVIDEOSTREAMTYPE = "RtcEngine_setRemoteDefaultVideoStreamType_5a94eb0";
        internal const string FUNC_RTCENGINE_MUTEREMOTEVIDEOSTREAM = "RtcEngine_muteRemoteVideoStream_dbdc15a";
        internal const string FUNC_RTCENGINE_SETREMOTEVIDEOSTREAMTYPE = "RtcEngine_setRemoteVideoStreamType_9e6406e";
        internal const string FUNC_RTCENGINE_SETREMOTEVIDEOSUBSCRIPTIONOPTIONS = "RtcEngine_setRemoteVideoSubscriptionOptions_0b6b258";
        internal const string FUNC_RTCENGINE_SETSUBSCRIBEAUDIOBLOCKLIST = "RtcEngine_setSubscribeAudioBlocklist_2d31fd5";
        internal const string FUNC_RTCENGINE_SETSUBSCRIBEAUDIOALLOWLIST = "RtcEngine_setSubscribeAudioAllowlist_2d31fd5";
        internal const string FUNC_RTCENGINE_SETSUBSCRIBEVIDEOBLOCKLIST = "RtcEngine_setSubscribeVideoBlocklist_2d31fd5";
        internal const string FUNC_RTCENGINE_SETSUBSCRIBEVIDEOALLOWLIST = "RtcEngine_setSubscribeVideoAllowlist_2d31fd5";
        internal const string FUNC_RTCENGINE_ENABLEAUDIOVOLUMEINDICATION = "RtcEngine_enableAudioVolumeIndication_39794a0";
        internal const string FUNC_RTCENGINE_STARTAUDIORECORDING = "RtcEngine_startAudioRecording_6161339";
        internal const string FUNC_RTCENGINE_STARTAUDIORECORDING2 = "RtcEngine_startAudioRecording_410d265";
        internal const string FUNC_RTCENGINE_STARTAUDIORECORDING3 = "RtcEngine_startAudioRecording_e32bb3b";
        internal const string FUNC_RTCENGINE_REGISTERAUDIOENCODEDFRAMEOBSERVER = "RtcEngine_registerAudioEncodedFrameObserver_ed4a177";
        internal const string FUNC_RTCENGINE_STOPAUDIORECORDING = "RtcEngine_stopAudioRecording";
        internal const string FUNC_RTCENGINE_CREATEMEDIAPLAYER = "RtcEngine_createMediaPlayer";
        internal const string FUNC_RTCENGINE_DESTROYMEDIAPLAYER = "RtcEngine_destroyMediaPlayer_328a49b";
        internal const string FUNC_RTCENGINE_CREATEMEDIARECORDER = "RtcEngine_createMediaRecorder_f779617";
        internal const string FUNC_RTCENGINE_DESTROYMEDIARECORDER = "RtcEngine_destroyMediaRecorder_95cdef5";
        internal const string FUNC_RTCENGINE_STARTAUDIOMIXING = "RtcEngine_startAudioMixing_f3db86c";
        internal const string FUNC_RTCENGINE_STARTAUDIOMIXING2 = "RtcEngine_startAudioMixing_1ee1b1e";
        internal const string FUNC_RTCENGINE_STOPAUDIOMIXING = "RtcEngine_stopAudioMixing";
        internal const string FUNC_RTCENGINE_PAUSEAUDIOMIXING = "RtcEngine_pauseAudioMixing";
        internal const string FUNC_RTCENGINE_RESUMEAUDIOMIXING = "RtcEngine_resumeAudioMixing";
        internal const string FUNC_RTCENGINE_SELECTAUDIOTRACK = "RtcEngine_selectAudioTrack_46f8ab7";
        internal const string FUNC_RTCENGINE_GETAUDIOTRACKCOUNT = "RtcEngine_getAudioTrackCount";
        internal const string FUNC_RTCENGINE_ADJUSTAUDIOMIXINGVOLUME = "RtcEngine_adjustAudioMixingVolume_46f8ab7";
        internal const string FUNC_RTCENGINE_ADJUSTAUDIOMIXINGPUBLISHVOLUME = "RtcEngine_adjustAudioMixingPublishVolume_46f8ab7";
        internal const string FUNC_RTCENGINE_GETAUDIOMIXINGPUBLISHVOLUME = "RtcEngine_getAudioMixingPublishVolume";
        internal const string FUNC_RTCENGINE_ADJUSTAUDIOMIXINGPLAYOUTVOLUME = "RtcEngine_adjustAudioMixingPlayoutVolume_46f8ab7";
        internal const string FUNC_RTCENGINE_GETAUDIOMIXINGPLAYOUTVOLUME = "RtcEngine_getAudioMixingPlayoutVolume";
        internal const string FUNC_RTCENGINE_GETAUDIOMIXINGDURATION = "RtcEngine_getAudioMixingDuration";
        internal const string FUNC_RTCENGINE_GETAUDIOMIXINGCURRENTPOSITION = "RtcEngine_getAudioMixingCurrentPosition";
        internal const string FUNC_RTCENGINE_SETAUDIOMIXINGPOSITION = "RtcEngine_setAudioMixingPosition_46f8ab7";
        internal const string FUNC_RTCENGINE_SETAUDIOMIXINGDUALMONOMODE = "RtcEngine_setAudioMixingDualMonoMode_38a5515";
        internal const string FUNC_RTCENGINE_SETAUDIOMIXINGPITCH = "RtcEngine_setAudioMixingPitch_46f8ab7";
        internal const string FUNC_RTCENGINE_SETAUDIOMIXINGPLAYBACKSPEED = "RtcEngine_setAudioMixingPlaybackSpeed_46f8ab7";
        internal const string FUNC_RTCENGINE_GETEFFECTSVOLUME = "RtcEngine_getEffectsVolume";
        internal const string FUNC_RTCENGINE_SETEFFECTSVOLUME = "RtcEngine_setEffectsVolume_46f8ab7";
        internal const string FUNC_RTCENGINE_PRELOADEFFECT = "RtcEngine_preloadEffect_282ba8c";
        internal const string FUNC_RTCENGINE_PLAYEFFECT = "RtcEngine_playEffect_531a783";
        internal const string FUNC_RTCENGINE_PLAYALLEFFECTS = "RtcEngine_playAllEffects_20d7df2";
        internal const string FUNC_RTCENGINE_GETVOLUMEOFEFFECT = "RtcEngine_getVolumeOfEffect_46f8ab7";
        internal const string FUNC_RTCENGINE_SETVOLUMEOFEFFECT = "RtcEngine_setVolumeOfEffect_4e92b3c";
        internal const string FUNC_RTCENGINE_PAUSEEFFECT = "RtcEngine_pauseEffect_46f8ab7";
        internal const string FUNC_RTCENGINE_PAUSEALLEFFECTS = "RtcEngine_pauseAllEffects";
        internal const string FUNC_RTCENGINE_RESUMEEFFECT = "RtcEngine_resumeEffect_46f8ab7";
        internal const string FUNC_RTCENGINE_RESUMEALLEFFECTS = "RtcEngine_resumeAllEffects";
        internal const string FUNC_RTCENGINE_STOPEFFECT = "RtcEngine_stopEffect_46f8ab7";
        internal const string FUNC_RTCENGINE_STOPALLEFFECTS = "RtcEngine_stopAllEffects";
        internal const string FUNC_RTCENGINE_UNLOADEFFECT = "RtcEngine_unloadEffect_46f8ab7";
        internal const string FUNC_RTCENGINE_UNLOADALLEFFECTS = "RtcEngine_unloadAllEffects";
        internal const string FUNC_RTCENGINE_GETEFFECTDURATION = "RtcEngine_getEffectDuration_3a2037f";
        internal const string FUNC_RTCENGINE_SETEFFECTPOSITION = "RtcEngine_setEffectPosition_4e92b3c";
        internal const string FUNC_RTCENGINE_GETEFFECTCURRENTPOSITION = "RtcEngine_getEffectCurrentPosition_46f8ab7";
        internal const string FUNC_RTCENGINE_ENABLESOUNDPOSITIONINDICATION = "RtcEngine_enableSoundPositionIndication_5039d15";
        internal const string FUNC_RTCENGINE_SETREMOTEVOICEPOSITION = "RtcEngine_setRemoteVoicePosition_250b42d";
        internal const string FUNC_RTCENGINE_ENABLESPATIALAUDIO = "RtcEngine_enableSpatialAudio_5039d15";
        internal const string FUNC_RTCENGINE_SETREMOTEUSERSPATIALAUDIOPARAMS = "RtcEngine_setRemoteUserSpatialAudioParams_65a7855";
        internal const string FUNC_RTCENGINE_SETVOICEBEAUTIFIERPRESET = "RtcEngine_setVoiceBeautifierPreset_4dd6319";
        internal const string FUNC_RTCENGINE_SETAUDIOEFFECTPRESET = "RtcEngine_setAudioEffectPreset_92ea92c";
        internal const string FUNC_RTCENGINE_SETVOICECONVERSIONPRESET = "RtcEngine_setVoiceConversionPreset_d14ee73";
        internal const string FUNC_RTCENGINE_SETAUDIOEFFECTPARAMETERS = "RtcEngine_setAudioEffectParameters_73bc670";
        internal const string FUNC_RTCENGINE_SETVOICEBEAUTIFIERPARAMETERS = "RtcEngine_setVoiceBeautifierParameters_f3cf745";
        internal const string FUNC_RTCENGINE_SETVOICECONVERSIONPARAMETERS = "RtcEngine_setVoiceConversionParameters_2f5022e";
        internal const string FUNC_RTCENGINE_SETLOCALVOICEPITCH = "RtcEngine_setLocalVoicePitch_bdb36bb";
        internal const string FUNC_RTCENGINE_SETLOCALVOICEFORMANT = "RtcEngine_setLocalVoiceFormant_bdb36bb";
        internal const string FUNC_RTCENGINE_SETLOCALVOICEEQUALIZATION = "RtcEngine_setLocalVoiceEqualization_d14012c";
        internal const string FUNC_RTCENGINE_SETLOCALVOICEREVERB = "RtcEngine_setLocalVoiceReverb_29c2013";
        internal const string FUNC_RTCENGINE_SETHEADPHONEEQPRESET = "RtcEngine_setHeadphoneEQPreset_b679644";
        internal const string FUNC_RTCENGINE_SETHEADPHONEEQPARAMETERS = "RtcEngine_setHeadphoneEQParameters_4e92b3c";
        internal const string FUNC_RTCENGINE_ENABLEVOICEAITUNER = "RtcEngine_enableVoiceAITuner_28f5d5b";
        internal const string FUNC_RTCENGINE_SETLOGFILE = "RtcEngine_setLogFile_3a2037f";
        internal const string FUNC_RTCENGINE_SETLOGFILTER = "RtcEngine_setLogFilter_2626ac7";
        internal const string FUNC_RTCENGINE_SETLOGLEVEL = "RtcEngine_setLogLevel_f125d83";
        internal const string FUNC_RTCENGINE_SETLOGFILESIZE = "RtcEngine_setLogFileSize_2626ac7";
        internal const string FUNC_RTCENGINE_UPLOADLOGFILE = "RtcEngine_uploadLogFile_66d4ecd";
        internal const string FUNC_RTCENGINE_WRITELOG = "RtcEngine_writeLog_62889f6";
        internal const string FUNC_RTCENGINE_SETLOCALRENDERMODE = "RtcEngine_setLocalRenderMode_cfb201b";
        internal const string FUNC_RTCENGINE_SETREMOTERENDERMODE = "RtcEngine_setRemoteRenderMode_6771ce0";
        internal const string FUNC_RTCENGINE_SETLOCALRENDERTARGETFPS = "RtcEngine_setLocalRenderTargetFps_2ad83d8";
        internal const string FUNC_RTCENGINE_SETREMOTERENDERTARGETFPS = "RtcEngine_setRemoteRenderTargetFps_46f8ab7";
        internal const string FUNC_RTCENGINE_SETLOCALRENDERMODE2 = "RtcEngine_setLocalRenderMode_bedb5ae";
        internal const string FUNC_RTCENGINE_SETLOCALVIDEOMIRRORMODE = "RtcEngine_setLocalVideoMirrorMode_b8a6c69";
        internal const string FUNC_RTCENGINE_ENABLEDUALSTREAMMODE = "RtcEngine_enableDualStreamMode_5039d15";
        internal const string FUNC_RTCENGINE_ENABLEDUALSTREAMMODE2 = "RtcEngine_enableDualStreamMode_9822d8a";
        internal const string FUNC_RTCENGINE_SETDUALSTREAMMODE = "RtcEngine_setDualStreamMode_3a7f662";
        internal const string FUNC_RTCENGINE_SETSIMULCASTCONFIG = "RtcEngine_setSimulcastConfig_3dcdfd7";
        internal const string FUNC_RTCENGINE_SETDUALSTREAMMODE2 = "RtcEngine_setDualStreamMode_b3a4f6c";
        internal const string FUNC_RTCENGINE_ENABLECUSTOMAUDIOLOCALPLAYBACK = "RtcEngine_enableCustomAudioLocalPlayback_9566341";
        internal const string FUNC_RTCENGINE_SETRECORDINGAUDIOFRAMEPARAMETERS = "RtcEngine_setRecordingAudioFrameParameters_bd46d1d";
        internal const string FUNC_RTCENGINE_SETPLAYBACKAUDIOFRAMEPARAMETERS = "RtcEngine_setPlaybackAudioFrameParameters_bd46d1d";
        internal const string FUNC_RTCENGINE_SETMIXEDAUDIOFRAMEPARAMETERS = "RtcEngine_setMixedAudioFrameParameters_ee7e270";
        internal const string FUNC_RTCENGINE_SETEARMONITORINGAUDIOFRAMEPARAMETERS = "RtcEngine_setEarMonitoringAudioFrameParameters_bd46d1d";
        internal const string FUNC_RTCENGINE_SETPLAYBACKAUDIOFRAMEBEFOREMIXINGPARAMETERS = "RtcEngine_setPlaybackAudioFrameBeforeMixingParameters_4e92b3c";
        internal const string FUNC_RTCENGINE_ENABLEAUDIOSPECTRUMMONITOR = "RtcEngine_enableAudioSpectrumMonitor_46f8ab7";
        internal const string FUNC_RTCENGINE_DISABLEAUDIOSPECTRUMMONITOR = "RtcEngine_disableAudioSpectrumMonitor";
        internal const string FUNC_RTCENGINE_REGISTERAUDIOSPECTRUMOBSERVER = "RtcEngine_registerAudioSpectrumObserver_0406ea7";
        internal const string FUNC_RTCENGINE_UNREGISTERAUDIOSPECTRUMOBSERVER = "RtcEngine_unregisterAudioSpectrumObserver_0406ea7";
        internal const string FUNC_RTCENGINE_ADJUSTRECORDINGSIGNALVOLUME = "RtcEngine_adjustRecordingSignalVolume_46f8ab7";
        internal const string FUNC_RTCENGINE_MUTERECORDINGSIGNAL = "RtcEngine_muteRecordingSignal_5039d15";
        internal const string FUNC_RTCENGINE_ADJUSTPLAYBACKSIGNALVOLUME = "RtcEngine_adjustPlaybackSignalVolume_46f8ab7";
        internal const string FUNC_RTCENGINE_ADJUSTUSERPLAYBACKSIGNALVOLUME = "RtcEngine_adjustUserPlaybackSignalVolume_88641bf";
        internal const string FUNC_RTCENGINE_SETLOCALPUBLISHFALLBACKOPTION = "RtcEngine_setLocalPublishFallbackOption_c29b788";
        internal const string FUNC_RTCENGINE_SETREMOTESUBSCRIBEFALLBACKOPTION = "RtcEngine_setRemoteSubscribeFallbackOption_c29b788";
        internal const string FUNC_RTCENGINE_SETHIGHPRIORITYUSERLIST = "RtcEngine_setHighPriorityUserList_ab88726";
        internal const string FUNC_RTCENGINE_ENABLEEXTENSION = "RtcEngine_enableExtension_d8b3874";
        internal const string FUNC_RTCENGINE_SETEXTENSIONPROPERTY = "RtcEngine_setExtensionProperty_f746b51";
        internal const string FUNC_RTCENGINE_GETEXTENSIONPROPERTY = "RtcEngine_getExtensionProperty_18768d4";
        internal const string FUNC_RTCENGINE_ENABLELOOPBACKRECORDING = "RtcEngine_enableLoopbackRecording_0b8eb79";
        internal const string FUNC_RTCENGINE_ADJUSTLOOPBACKSIGNALVOLUME = "RtcEngine_adjustLoopbackSignalVolume_46f8ab7";
        internal const string FUNC_RTCENGINE_GETLOOPBACKRECORDINGVOLUME = "RtcEngine_getLoopbackRecordingVolume";
        internal const string FUNC_RTCENGINE_ENABLEINEARMONITORING = "RtcEngine_enableInEarMonitoring_077cf5f";
        internal const string FUNC_RTCENGINE_SETINEARMONITORINGVOLUME = "RtcEngine_setInEarMonitoringVolume_46f8ab7";
        internal const string FUNC_RTCENGINE_LOADEXTENSIONPROVIDER = "RtcEngine_loadExtensionProvider_7a174df";
        internal const string FUNC_RTCENGINE_SETEXTENSIONPROVIDERPROPERTY = "RtcEngine_setExtensionProviderProperty_0e4f59e";
        internal const string FUNC_RTCENGINE_REGISTEREXTENSION = "RtcEngine_registerExtension_fd62af4";
        internal const string FUNC_RTCENGINE_ENABLEEXTENSION2 = "RtcEngine_enableExtension_0b60a2c";
        internal const string FUNC_RTCENGINE_SETEXTENSIONPROPERTY2 = "RtcEngine_setExtensionProperty_520ac55";
        internal const string FUNC_RTCENGINE_GETEXTENSIONPROPERTY2 = "RtcEngine_getExtensionProperty_38c9723";
        internal const string FUNC_RTCENGINE_SETCAMERACAPTURERCONFIGURATION = "RtcEngine_setCameraCapturerConfiguration_afa93b3";
        internal const string FUNC_RTCENGINE_CREATECUSTOMVIDEOTRACK = "RtcEngine_createCustomVideoTrack";
        internal const string FUNC_RTCENGINE_CREATECUSTOMENCODEDVIDEOTRACK = "RtcEngine_createCustomEncodedVideoTrack_0e9dc99";
        internal const string FUNC_RTCENGINE_DESTROYCUSTOMVIDEOTRACK = "RtcEngine_destroyCustomVideoTrack_3019423";
        internal const string FUNC_RTCENGINE_DESTROYCUSTOMENCODEDVIDEOTRACK = "RtcEngine_destroyCustomEncodedVideoTrack_3019423";
        internal const string FUNC_RTCENGINE_SWITCHCAMERA = "RtcEngine_switchCamera";
        internal const string FUNC_RTCENGINE_ISCAMERAZOOMSUPPORTED = "RtcEngine_isCameraZoomSupported";
        internal const string FUNC_RTCENGINE_ISCAMERAFACEDETECTSUPPORTED = "RtcEngine_isCameraFaceDetectSupported";
        internal const string FUNC_RTCENGINE_ISCAMERATORCHSUPPORTED = "RtcEngine_isCameraTorchSupported";
        internal const string FUNC_RTCENGINE_ISCAMERAFOCUSSUPPORTED = "RtcEngine_isCameraFocusSupported";
        internal const string FUNC_RTCENGINE_ISCAMERAAUTOFOCUSFACEMODESUPPORTED = "RtcEngine_isCameraAutoFocusFaceModeSupported";
        internal const string FUNC_RTCENGINE_SETCAMERAZOOMFACTOR = "RtcEngine_setCameraZoomFactor_685e803";
        internal const string FUNC_RTCENGINE_ENABLEFACEDETECTION = "RtcEngine_enableFaceDetection_5039d15";
        internal const string FUNC_RTCENGINE_GETCAMERAMAXZOOMFACTOR = "RtcEngine_getCameraMaxZoomFactor";
        internal const string FUNC_RTCENGINE_SETCAMERAFOCUSPOSITIONINPREVIEW = "RtcEngine_setCameraFocusPositionInPreview_f282d50";
        internal const string FUNC_RTCENGINE_SETCAMERATORCHON = "RtcEngine_setCameraTorchOn_5039d15";
        internal const string FUNC_RTCENGINE_SETCAMERAAUTOFOCUSFACEMODEENABLED = "RtcEngine_setCameraAutoFocusFaceModeEnabled_5039d15";
        internal const string FUNC_RTCENGINE_ISCAMERAEXPOSUREPOSITIONSUPPORTED = "RtcEngine_isCameraExposurePositionSupported";
        internal const string FUNC_RTCENGINE_SETCAMERAEXPOSUREPOSITION = "RtcEngine_setCameraExposurePosition_f282d50";
        internal const string FUNC_RTCENGINE_ISCAMERAEXPOSURESUPPORTED = "RtcEngine_isCameraExposureSupported";
        internal const string FUNC_RTCENGINE_SETCAMERAEXPOSUREFACTOR = "RtcEngine_setCameraExposureFactor_685e803";
        internal const string FUNC_RTCENGINE_ISCAMERAAUTOEXPOSUREFACEMODESUPPORTED = "RtcEngine_isCameraAutoExposureFaceModeSupported";
        internal const string FUNC_RTCENGINE_SETCAMERAAUTOEXPOSUREFACEMODEENABLED = "RtcEngine_setCameraAutoExposureFaceModeEnabled_5039d15";
        internal const string FUNC_RTCENGINE_SETCAMERASTABILIZATIONMODE = "RtcEngine_setCameraStabilizationMode_701b981";
        internal const string FUNC_RTCENGINE_SETDEFAULTAUDIOROUTETOSPEAKERPHONE = "RtcEngine_setDefaultAudioRouteToSpeakerphone_5039d15";
        internal const string FUNC_RTCENGINE_SETENABLESPEAKERPHONE = "RtcEngine_setEnableSpeakerphone_5039d15";
        internal const string FUNC_RTCENGINE_ISSPEAKERPHONEENABLED = "RtcEngine_isSpeakerphoneEnabled";
        internal const string FUNC_RTCENGINE_SETROUTEINCOMMUNICATIONMODE = "RtcEngine_setRouteInCommunicationMode_46f8ab7";
        internal const string FUNC_RTCENGINE_ISCAMERACENTERSTAGESUPPORTED = "RtcEngine_isCameraCenterStageSupported";
        internal const string FUNC_RTCENGINE_ENABLECAMERACENTERSTAGE = "RtcEngine_enableCameraCenterStage_5039d15";
        internal const string FUNC_RTCENGINE_GETSCREENCAPTURESOURCES = "RtcEngine_getScreenCaptureSources_f3e02cb";
        internal const string FUNC_RTCENGINE_SETAUDIOSESSIONOPERATIONRESTRICTION = "RtcEngine_setAudioSessionOperationRestriction_c492897";
        internal const string FUNC_RTCENGINE_STARTSCREENCAPTUREBYDISPLAYID = "RtcEngine_startScreenCaptureByDisplayId_ce89867";
        internal const string FUNC_RTCENGINE_STARTSCREENCAPTUREBYSCREENRECT = "RtcEngine_startScreenCaptureByScreenRect_e286286";
        internal const string FUNC_RTCENGINE_GETAUDIODEVICEINFO = "RtcEngine_getAudioDeviceInfo_505aa0c";
        internal const string FUNC_RTCENGINE_STARTSCREENCAPTUREBYWINDOWID = "RtcEngine_startScreenCaptureByWindowId_ce89867";
        internal const string FUNC_RTCENGINE_SETSCREENCAPTURECONTENTHINT = "RtcEngine_setScreenCaptureContentHint_8ad2c79";
        internal const string FUNC_RTCENGINE_UPDATESCREENCAPTUREREGION = "RtcEngine_updateScreenCaptureRegion_6b327a8";
        internal const string FUNC_RTCENGINE_UPDATESCREENCAPTUREPARAMETERS = "RtcEngine_updateScreenCaptureParameters_a2eef93";
        internal const string FUNC_RTCENGINE_STARTSCREENCAPTURE = "RtcEngine_startScreenCapture_270da41";
        internal const string FUNC_RTCENGINE_UPDATESCREENCAPTURE = "RtcEngine_updateScreenCapture_270da41";
        internal const string FUNC_RTCENGINE_QUERYSCREENCAPTURECAPABILITY = "RtcEngine_queryScreenCaptureCapability";
        internal const string FUNC_RTCENGINE_QUERYCAMERAFOCALLENGTHCAPABILITY = "RtcEngine_queryCameraFocalLengthCapability_2dee6af";
        internal const string FUNC_RTCENGINE_SETEXTERNALMEDIAPROJECTION = "RtcEngine_setExternalMediaProjection_f337cbf";
        internal const string FUNC_RTCENGINE_SETSCREENCAPTURESCENARIO = "RtcEngine_setScreenCaptureScenario_13de7b4";
        internal const string FUNC_RTCENGINE_STOPSCREENCAPTURE = "RtcEngine_stopScreenCapture";
        internal const string FUNC_RTCENGINE_GETCALLID = "RtcEngine_getCallId_66d4ecd";
        internal const string FUNC_RTCENGINE_RATE = "RtcEngine_rate_f1a0070";
        internal const string FUNC_RTCENGINE_COMPLAIN = "RtcEngine_complain_ccad422";
        internal const string FUNC_RTCENGINE_STARTRTMPSTREAMWITHOUTTRANSCODING = "RtcEngine_startRtmpStreamWithoutTranscoding_3a2037f";
        internal const string FUNC_RTCENGINE_STARTRTMPSTREAMWITHTRANSCODING = "RtcEngine_startRtmpStreamWithTranscoding_f76aa1a";
        internal const string FUNC_RTCENGINE_UPDATERTMPTRANSCODING = "RtcEngine_updateRtmpTranscoding_91368d4";
        internal const string FUNC_RTCENGINE_STARTLOCALVIDEOTRANSCODER = "RtcEngine_startLocalVideoTranscoder_90f9e33";
        internal const string FUNC_RTCENGINE_UPDATELOCALTRANSCODERCONFIGURATION = "RtcEngine_updateLocalTranscoderConfiguration_90f9e33";
        internal const string FUNC_RTCENGINE_STOPRTMPSTREAM = "RtcEngine_stopRtmpStream_3a2037f";
        internal const string FUNC_RTCENGINE_STOPLOCALVIDEOTRANSCODER = "RtcEngine_stopLocalVideoTranscoder";
        internal const string FUNC_RTCENGINE_STARTLOCALAUDIOMIXER = "RtcEngine_startLocalAudioMixer_a7ff78e";
        internal const string FUNC_RTCENGINE_UPDATELOCALAUDIOMIXERCONFIGURATION = "RtcEngine_updateLocalAudioMixerConfiguration_a7ff78e";
        internal const string FUNC_RTCENGINE_STOPLOCALAUDIOMIXER = "RtcEngine_stopLocalAudioMixer";
        internal const string FUNC_RTCENGINE_STARTCAMERACAPTURE = "RtcEngine_startCameraCapture_f3692cc";
        internal const string FUNC_RTCENGINE_STOPCAMERACAPTURE = "RtcEngine_stopCameraCapture_4fd718e";
        internal const string FUNC_RTCENGINE_SETCAMERADEVICEORIENTATION = "RtcEngine_setCameraDeviceOrientation_025aae8";
        internal const string FUNC_RTCENGINE_SETSCREENCAPTUREORIENTATION = "RtcEngine_setScreenCaptureOrientation_025aae8";
        internal const string FUNC_RTCENGINE_STARTSCREENCAPTURE2 = "RtcEngine_startScreenCapture_9ebb320";
        internal const string FUNC_RTCENGINE_STOPSCREENCAPTURE2 = "RtcEngine_stopScreenCapture_4fd718e";
        internal const string FUNC_RTCENGINE_GETCONNECTIONSTATE = "RtcEngine_getConnectionState";
        internal const string FUNC_RTCENGINE_REGISTEREVENTHANDLER = "RtcEngine_registerEventHandler_5fc0465";
        internal const string FUNC_RTCENGINE_UNREGISTEREVENTHANDLER = "RtcEngine_unregisterEventHandler_5fc0465";
        internal const string FUNC_RTCENGINE_SETREMOTEUSERPRIORITY = "RtcEngine_setRemoteUserPriority_f34115b";
        internal const string FUNC_RTCENGINE_REGISTERPACKETOBSERVER = "RtcEngine_registerPacketObserver_f8b44dd";
        internal const string FUNC_RTCENGINE_ENABLEENCRYPTION = "RtcEngine_enableEncryption_421c27b";
        internal const string FUNC_RTCENGINE_CREATEDATASTREAM = "RtcEngine_createDataStream_b897a63";
        internal const string FUNC_RTCENGINE_CREATEDATASTREAM2 = "RtcEngine_createDataStream_5862815";
        internal const string FUNC_RTCENGINE_SENDSTREAMMESSAGE = "RtcEngine_sendStreamMessage_8715a45";
        internal const string FUNC_RTCENGINE_ADDVIDEOWATERMARK = "RtcEngine_addVideoWatermark_eaef16d";
        internal const string FUNC_RTCENGINE_ADDVIDEOWATERMARK2 = "RtcEngine_addVideoWatermark_7480410";
        internal const string FUNC_RTCENGINE_CLEARVIDEOWATERMARKS = "RtcEngine_clearVideoWatermarks";
        internal const string FUNC_RTCENGINE_PAUSEAUDIO = "RtcEngine_pauseAudio";
        internal const string FUNC_RTCENGINE_RESUMEAUDIO = "RtcEngine_resumeAudio";
        internal const string FUNC_RTCENGINE_ENABLEWEBSDKINTEROPERABILITY = "RtcEngine_enableWebSdkInteroperability_5039d15";
        internal const string FUNC_RTCENGINE_SENDCUSTOMREPORTMESSAGE = "RtcEngine_sendCustomReportMessage_56d6589";
        internal const string FUNC_RTCENGINE_REGISTERMEDIAMETADATAOBSERVER = "RtcEngine_registerMediaMetadataObserver_8701fec";
        internal const string FUNC_RTCENGINE_UNREGISTERMEDIAMETADATAOBSERVER = "RtcEngine_unregisterMediaMetadataObserver_8701fec";
        internal const string FUNC_RTCENGINE_STARTAUDIOFRAMEDUMP = "RtcEngine_startAudioFrameDump_aad7331";
        internal const string FUNC_RTCENGINE_STOPAUDIOFRAMEDUMP = "RtcEngine_stopAudioFrameDump_a4c9af4";
        internal const string FUNC_RTCENGINE_SETAINSMODE = "RtcEngine_setAINSMode_4df3049";
        internal const string FUNC_RTCENGINE_REGISTERLOCALUSERACCOUNT = "RtcEngine_registerLocalUserAccount_ccad422";
        internal const string FUNC_RTCENGINE_JOINCHANNELWITHUSERACCOUNT = "RtcEngine_joinChannelWithUserAccount_0e4f59e";
        internal const string FUNC_RTCENGINE_JOINCHANNELWITHUSERACCOUNT2 = "RtcEngine_joinChannelWithUserAccount_4685af9";
        internal const string FUNC_RTCENGINE_JOINCHANNELWITHUSERACCOUNTEX = "RtcEngine_joinChannelWithUserAccountEx_268b977";
        internal const string FUNC_RTCENGINE_GETUSERINFOBYUSERACCOUNT = "RtcEngine_getUserInfoByUserAccount_c6a8f08";
        internal const string FUNC_RTCENGINE_GETUSERINFOBYUID = "RtcEngine_getUserInfoByUid_6b7aee8";
        internal const string FUNC_RTCENGINE_STARTORUPDATECHANNELMEDIARELAY = "RtcEngine_startOrUpdateChannelMediaRelay_e68f0a4";
        internal const string FUNC_RTCENGINE_STOPCHANNELMEDIARELAY = "RtcEngine_stopChannelMediaRelay";
        internal const string FUNC_RTCENGINE_PAUSEALLCHANNELMEDIARELAY = "RtcEngine_pauseAllChannelMediaRelay";
        internal const string FUNC_RTCENGINE_RESUMEALLCHANNELMEDIARELAY = "RtcEngine_resumeAllChannelMediaRelay";
        internal const string FUNC_RTCENGINE_SETDIRECTCDNSTREAMINGAUDIOCONFIGURATION = "RtcEngine_setDirectCdnStreamingAudioConfiguration_ac39c15";
        internal const string FUNC_RTCENGINE_SETDIRECTCDNSTREAMINGVIDEOCONFIGURATION = "RtcEngine_setDirectCdnStreamingVideoConfiguration_89677d8";
        internal const string FUNC_RTCENGINE_STARTDIRECTCDNSTREAMING = "RtcEngine_startDirectCdnStreaming_ed8d77b";
        internal const string FUNC_RTCENGINE_STOPDIRECTCDNSTREAMING = "RtcEngine_stopDirectCdnStreaming";
        internal const string FUNC_RTCENGINE_UPDATEDIRECTCDNSTREAMINGMEDIAOPTIONS = "RtcEngine_updateDirectCdnStreamingMediaOptions_d2556c8";
        internal const string FUNC_RTCENGINE_STARTRHYTHMPLAYER = "RtcEngine_startRhythmPlayer_e1f6565";
        internal const string FUNC_RTCENGINE_STOPRHYTHMPLAYER = "RtcEngine_stopRhythmPlayer";
        internal const string FUNC_RTCENGINE_CONFIGRHYTHMPLAYER = "RtcEngine_configRhythmPlayer_b36c805";
        internal const string FUNC_RTCENGINE_TAKESNAPSHOT = "RtcEngine_takeSnapshot_1922dd1";
        internal const string FUNC_RTCENGINE_TAKESNAPSHOT2 = "RtcEngine_takeSnapshot_5669ea6";
        internal const string FUNC_RTCENGINE_ENABLECONTENTINSPECT = "RtcEngine_enableContentInspect_e15e514";
        internal const string FUNC_RTCENGINE_ADJUSTCUSTOMAUDIOPUBLISHVOLUME = "RtcEngine_adjustCustomAudioPublishVolume_f8da2ca";
        internal const string FUNC_RTCENGINE_ADJUSTCUSTOMAUDIOPLAYOUTVOLUME = "RtcEngine_adjustCustomAudioPlayoutVolume_f8da2ca";
        internal const string FUNC_RTCENGINE_SETCLOUDPROXY = "RtcEngine_setCloudProxy_39d115e";
        internal const string FUNC_RTCENGINE_SETLOCALACCESSPOINT = "RtcEngine_setLocalAccessPoint_798c8c7";
        internal const string FUNC_RTCENGINE_SETADVANCEDAUDIOOPTIONS = "RtcEngine_setAdvancedAudioOptions_38d986b";
        internal const string FUNC_RTCENGINE_SETAVSYNCSOURCE = "RtcEngine_setAVSyncSource_bf26e54";
        internal const string FUNC_RTCENGINE_ENABLEVIDEOIMAGESOURCE = "RtcEngine_enableVideoImageSource_5f39ea0";
        internal const string FUNC_RTCENGINE_GETCURRENTMONOTONICTIMEINMS = "RtcEngine_getCurrentMonotonicTimeInMs";
        internal const string FUNC_RTCENGINE_ENABLEWIRELESSACCELERATE = "RtcEngine_enableWirelessAccelerate_5039d15";
        internal const string FUNC_RTCENGINE_GETNETWORKTYPE = "RtcEngine_getNetworkType";
        internal const string FUNC_RTCENGINE_SETPARAMETERS = "RtcEngine_setParameters_3a2037f";
        internal const string FUNC_RTCENGINE_STARTMEDIARENDERINGTRACING = "RtcEngine_startMediaRenderingTracing";
        internal const string FUNC_RTCENGINE_ENABLEINSTANTMEDIARENDERING = "RtcEngine_enableInstantMediaRendering";
        internal const string FUNC_RTCENGINE_GETNTPWALLTIMEINMS = "RtcEngine_getNtpWallTimeInMs";
        internal const string FUNC_RTCENGINE_ISFEATUREAVAILABLEONDEVICE = "RtcEngine_isFeatureAvailableOnDevice_a694b62";
        internal const string FUNC_RTCENGINE_SENDAUDIOMETADATA = "RtcEngine_sendAudioMetadata_878f309";
        internal const string FUNC_RTCENGINE_QUERYHDRCAPABILITY = "RtcEngine_queryHDRCapability_bebdacb";
        #endregion terra IRtcEngine

        #region terra IRtcEngineEx
        internal const string FUNC_RTCENGINEEX_JOINCHANNELEX = "RtcEngineEx_joinChannelEx_a3cd08c";
        internal const string FUNC_RTCENGINEEX_LEAVECHANNELEX = "RtcEngineEx_leaveChannelEx_c81e1a4";
        internal const string FUNC_RTCENGINEEX_LEAVECHANNELEX2 = "RtcEngineEx_leaveChannelEx_b03ee9a";
        internal const string FUNC_RTCENGINEEX_LEAVECHANNELWITHUSERACCOUNTEX = "RtcEngineEx_leaveChannelWithUserAccountEx_ccad422";
        internal const string FUNC_RTCENGINEEX_LEAVECHANNELWITHUSERACCOUNTEX2 = "RtcEngineEx_leaveChannelWithUserAccountEx_8bbe372";
        internal const string FUNC_RTCENGINEEX_UPDATECHANNELMEDIAOPTIONSEX = "RtcEngineEx_updateChannelMediaOptionsEx_457bb35";
        internal const string FUNC_RTCENGINEEX_SETVIDEOENCODERCONFIGURATIONEX = "RtcEngineEx_setVideoEncoderConfigurationEx_4670c1e";
        internal const string FUNC_RTCENGINEEX_SETUPREMOTEVIDEOEX = "RtcEngineEx_setupRemoteVideoEx_522a409";
        internal const string FUNC_RTCENGINEEX_MUTEREMOTEAUDIOSTREAMEX = "RtcEngineEx_muteRemoteAudioStreamEx_6d93082";
        internal const string FUNC_RTCENGINEEX_MUTEREMOTEVIDEOSTREAMEX = "RtcEngineEx_muteRemoteVideoStreamEx_6d93082";
        internal const string FUNC_RTCENGINEEX_SETREMOTEVIDEOSTREAMTYPEEX = "RtcEngineEx_setRemoteVideoStreamTypeEx_01dc428";
        internal const string FUNC_RTCENGINEEX_MUTELOCALAUDIOSTREAMEX = "RtcEngineEx_muteLocalAudioStreamEx_3cf17a4";
        internal const string FUNC_RTCENGINEEX_MUTELOCALVIDEOSTREAMEX = "RtcEngineEx_muteLocalVideoStreamEx_3cf17a4";
        internal const string FUNC_RTCENGINEEX_MUTEALLREMOTEAUDIOSTREAMSEX = "RtcEngineEx_muteAllRemoteAudioStreamsEx_3cf17a4";
        internal const string FUNC_RTCENGINEEX_MUTEALLREMOTEVIDEOSTREAMSEX = "RtcEngineEx_muteAllRemoteVideoStreamsEx_3cf17a4";
        internal const string FUNC_RTCENGINEEX_SETSUBSCRIBEAUDIOBLOCKLISTEX = "RtcEngineEx_setSubscribeAudioBlocklistEx_9f1e85c";
        internal const string FUNC_RTCENGINEEX_SETSUBSCRIBEAUDIOALLOWLISTEX = "RtcEngineEx_setSubscribeAudioAllowlistEx_9f1e85c";
        internal const string FUNC_RTCENGINEEX_SETSUBSCRIBEVIDEOBLOCKLISTEX = "RtcEngineEx_setSubscribeVideoBlocklistEx_9f1e85c";
        internal const string FUNC_RTCENGINEEX_SETSUBSCRIBEVIDEOALLOWLISTEX = "RtcEngineEx_setSubscribeVideoAllowlistEx_9f1e85c";
        internal const string FUNC_RTCENGINEEX_SETREMOTEVIDEOSUBSCRIPTIONOPTIONSEX = "RtcEngineEx_setRemoteVideoSubscriptionOptionsEx_3cd36bc";
        internal const string FUNC_RTCENGINEEX_SETREMOTEVOICEPOSITIONEX = "RtcEngineEx_setRemoteVoicePositionEx_fc0471c";
        internal const string FUNC_RTCENGINEEX_SETREMOTEUSERSPATIALAUDIOPARAMSEX = "RtcEngineEx_setRemoteUserSpatialAudioParamsEx_40ca9fb";
        internal const string FUNC_RTCENGINEEX_SETREMOTERENDERMODEEX = "RtcEngineEx_setRemoteRenderModeEx_a72fe4e";
        internal const string FUNC_RTCENGINEEX_ENABLELOOPBACKRECORDINGEX = "RtcEngineEx_enableLoopbackRecordingEx_4f41542";
        internal const string FUNC_RTCENGINEEX_ADJUSTRECORDINGSIGNALVOLUMEEX = "RtcEngineEx_adjustRecordingSignalVolumeEx_e84d10e";
        internal const string FUNC_RTCENGINEEX_MUTERECORDINGSIGNALEX = "RtcEngineEx_muteRecordingSignalEx_3cf17a4";
        internal const string FUNC_RTCENGINEEX_ADJUSTUSERPLAYBACKSIGNALVOLUMEEX = "RtcEngineEx_adjustUserPlaybackSignalVolumeEx_adbd29c";
        internal const string FUNC_RTCENGINEEX_GETCONNECTIONSTATEEX = "RtcEngineEx_getConnectionStateEx_c81e1a4";
        internal const string FUNC_RTCENGINEEX_ENABLEENCRYPTIONEX = "RtcEngineEx_enableEncryptionEx_10cd872";
        internal const string FUNC_RTCENGINEEX_CREATEDATASTREAMEX = "RtcEngineEx_createDataStreamEx_1767167";
        internal const string FUNC_RTCENGINEEX_CREATEDATASTREAMEX2 = "RtcEngineEx_createDataStreamEx_9f641b6";
        internal const string FUNC_RTCENGINEEX_SENDSTREAMMESSAGEEX = "RtcEngineEx_sendStreamMessageEx_0c34857";
        internal const string FUNC_RTCENGINEEX_ADDVIDEOWATERMARKEX = "RtcEngineEx_addVideoWatermarkEx_ad7daa3";
        internal const string FUNC_RTCENGINEEX_CLEARVIDEOWATERMARKEX = "RtcEngineEx_clearVideoWatermarkEx_c81e1a4";
        internal const string FUNC_RTCENGINEEX_SENDCUSTOMREPORTMESSAGEEX = "RtcEngineEx_sendCustomReportMessageEx_833b8a5";
        internal const string FUNC_RTCENGINEEX_ENABLEAUDIOVOLUMEINDICATIONEX = "RtcEngineEx_enableAudioVolumeIndicationEx_ac84f2a";
        internal const string FUNC_RTCENGINEEX_STARTRTMPSTREAMWITHOUTTRANSCODINGEX = "RtcEngineEx_startRtmpStreamWithoutTranscodingEx_e405325";
        internal const string FUNC_RTCENGINEEX_STARTRTMPSTREAMWITHTRANSCODINGEX = "RtcEngineEx_startRtmpStreamWithTranscodingEx_ab121b5";
        internal const string FUNC_RTCENGINEEX_UPDATERTMPTRANSCODINGEX = "RtcEngineEx_updateRtmpTranscodingEx_77f3ee8";
        internal const string FUNC_RTCENGINEEX_STOPRTMPSTREAMEX = "RtcEngineEx_stopRtmpStreamEx_e405325";
        internal const string FUNC_RTCENGINEEX_STARTORUPDATECHANNELMEDIARELAYEX = "RtcEngineEx_startOrUpdateChannelMediaRelayEx_4ad39a8";
        internal const string FUNC_RTCENGINEEX_STOPCHANNELMEDIARELAYEX = "RtcEngineEx_stopChannelMediaRelayEx_c81e1a4";
        internal const string FUNC_RTCENGINEEX_PAUSEALLCHANNELMEDIARELAYEX = "RtcEngineEx_pauseAllChannelMediaRelayEx_c81e1a4";
        internal const string FUNC_RTCENGINEEX_RESUMEALLCHANNELMEDIARELAYEX = "RtcEngineEx_resumeAllChannelMediaRelayEx_c81e1a4";
        internal const string FUNC_RTCENGINEEX_GETUSERINFOBYUSERACCOUNTEX = "RtcEngineEx_getUserInfoByUserAccountEx_ca39cc6";
        internal const string FUNC_RTCENGINEEX_GETUSERINFOBYUIDEX = "RtcEngineEx_getUserInfoByUidEx_1e78da1";
        internal const string FUNC_RTCENGINEEX_ENABLEDUALSTREAMMODEEX = "RtcEngineEx_enableDualStreamModeEx_4b18f41";
        internal const string FUNC_RTCENGINEEX_SETDUALSTREAMMODEEX = "RtcEngineEx_setDualStreamModeEx_622d0f3";
        internal const string FUNC_RTCENGINEEX_SETSIMULCASTCONFIGEX = "RtcEngineEx_setSimulcastConfigEx_bd8d7d0";
        internal const string FUNC_RTCENGINEEX_SETHIGHPRIORITYUSERLISTEX = "RtcEngineEx_setHighPriorityUserListEx_8736b5c";
        internal const string FUNC_RTCENGINEEX_TAKESNAPSHOTEX = "RtcEngineEx_takeSnapshotEx_de1c015";
        internal const string FUNC_RTCENGINEEX_TAKESNAPSHOTEX2 = "RtcEngineEx_takeSnapshotEx_b856417";
        internal const string FUNC_RTCENGINEEX_ENABLECONTENTINSPECTEX = "RtcEngineEx_enableContentInspectEx_c4e7f69";
        internal const string FUNC_RTCENGINEEX_STARTMEDIARENDERINGTRACINGEX = "RtcEngineEx_startMediaRenderingTracingEx_c81e1a4";
        internal const string FUNC_RTCENGINEEX_SETPARAMETERSEX = "RtcEngineEx_setParametersEx_8225ea3";
        internal const string FUNC_RTCENGINEEX_GETCALLIDEX = "RtcEngineEx_getCallIdEx_b13f7c4";
        internal const string FUNC_RTCENGINEEX_SENDAUDIOMETADATAEX = "RtcEngineEx_sendAudioMetadataEx_e2bf1c4";
        #endregion terra IRtcEngineEx

        internal const string FUNC_MEDIAPLAYER_UNOPENWITHMEDIASOURCE = "MediaPlayer_unOpenWithMediaSource";
        #region terra IMediaPlayer
        internal const string FUNC_MEDIAPLAYER_INITIALIZE = "MediaPlayer_initialize_1972bc2";
        internal const string FUNC_MEDIAPLAYER_GETMEDIAPLAYERID = "MediaPlayer_getMediaPlayerId";
        internal const string FUNC_MEDIAPLAYER_OPEN = "MediaPlayer_open_e43f201";
        internal const string FUNC_MEDIAPLAYER_OPENWITHMEDIASOURCE = "MediaPlayer_openWithMediaSource_3c11499";
        internal const string FUNC_MEDIAPLAYER_PLAY = "MediaPlayer_play";
        internal const string FUNC_MEDIAPLAYER_PAUSE = "MediaPlayer_pause";
        internal const string FUNC_MEDIAPLAYER_STOP = "MediaPlayer_stop";
        internal const string FUNC_MEDIAPLAYER_RESUME = "MediaPlayer_resume";
        internal const string FUNC_MEDIAPLAYER_SEEK = "MediaPlayer_seek_f631116";
        internal const string FUNC_MEDIAPLAYER_SETAUDIOPITCH = "MediaPlayer_setAudioPitch_46f8ab7";
        internal const string FUNC_MEDIAPLAYER_GETDURATION = "MediaPlayer_getDuration_b12f121";
        internal const string FUNC_MEDIAPLAYER_GETPLAYPOSITION = "MediaPlayer_getPlayPosition_b12f121";
        internal const string FUNC_MEDIAPLAYER_GETSTREAMCOUNT = "MediaPlayer_getStreamCount_b12f121";
        internal const string FUNC_MEDIAPLAYER_GETSTREAMINFO = "MediaPlayer_getStreamInfo_0fa63fa";
        internal const string FUNC_MEDIAPLAYER_SETLOOPCOUNT = "MediaPlayer_setLoopCount_46f8ab7";
        internal const string FUNC_MEDIAPLAYER_SETPLAYBACKSPEED = "MediaPlayer_setPlaybackSpeed_46f8ab7";
        internal const string FUNC_MEDIAPLAYER_SELECTAUDIOTRACK = "MediaPlayer_selectAudioTrack_46f8ab7";
        internal const string FUNC_MEDIAPLAYER_SELECTMULTIAUDIOTRACK = "MediaPlayer_selectMultiAudioTrack_4e92b3c";
        internal const string FUNC_MEDIAPLAYER_SETPLAYEROPTION = "MediaPlayer_setPlayerOption_4d05d29";
        internal const string FUNC_MEDIAPLAYER_SETPLAYEROPTION2 = "MediaPlayer_setPlayerOption_ccad422";
        internal const string FUNC_MEDIAPLAYER_TAKESCREENSHOT = "MediaPlayer_takeScreenshot_3a2037f";
        internal const string FUNC_MEDIAPLAYER_SELECTINTERNALSUBTITLE = "MediaPlayer_selectInternalSubtitle_46f8ab7";
        internal const string FUNC_MEDIAPLAYER_SETEXTERNALSUBTITLE = "MediaPlayer_setExternalSubtitle_3a2037f";
        internal const string FUNC_MEDIAPLAYER_GETSTATE = "MediaPlayer_getState";
        internal const string FUNC_MEDIAPLAYER_MUTE = "MediaPlayer_mute_5039d15";
        internal const string FUNC_MEDIAPLAYER_GETMUTE = "MediaPlayer_getMute_c93e9d4";
        internal const string FUNC_MEDIAPLAYER_ADJUSTPLAYOUTVOLUME = "MediaPlayer_adjustPlayoutVolume_46f8ab7";
        internal const string FUNC_MEDIAPLAYER_GETPLAYOUTVOLUME = "MediaPlayer_getPlayoutVolume_9cfaa7e";
        internal const string FUNC_MEDIAPLAYER_ADJUSTPUBLISHSIGNALVOLUME = "MediaPlayer_adjustPublishSignalVolume_46f8ab7";
        internal const string FUNC_MEDIAPLAYER_GETPUBLISHSIGNALVOLUME = "MediaPlayer_getPublishSignalVolume_9cfaa7e";
        internal const string FUNC_MEDIAPLAYER_SETVIEW = "MediaPlayer_setView_cb1a81f";
        internal const string FUNC_MEDIAPLAYER_SETRENDERMODE = "MediaPlayer_setRenderMode_bedb5ae";
        internal const string FUNC_MEDIAPLAYER_REGISTERPLAYERSOURCEOBSERVER = "MediaPlayer_registerPlayerSourceObserver_15621d7";
        internal const string FUNC_MEDIAPLAYER_UNREGISTERPLAYERSOURCEOBSERVER = "MediaPlayer_unregisterPlayerSourceObserver_15621d7";
        internal const string FUNC_MEDIAPLAYER_REGISTERAUDIOFRAMEOBSERVER = "MediaPlayer_registerAudioFrameObserver_89ab9b5";
        internal const string FUNC_MEDIAPLAYER_REGISTERAUDIOFRAMEOBSERVER2 = "MediaPlayer_registerAudioFrameObserver_a5b510b";
        internal const string FUNC_MEDIAPLAYER_UNREGISTERAUDIOFRAMEOBSERVER = "MediaPlayer_unregisterAudioFrameObserver_89ab9b5";
        internal const string FUNC_MEDIAPLAYER_REGISTERVIDEOFRAMEOBSERVER = "MediaPlayer_registerVideoFrameObserver_833bd8d";
        internal const string FUNC_MEDIAPLAYER_UNREGISTERVIDEOFRAMEOBSERVER = "MediaPlayer_unregisterVideoFrameObserver_5165d4c";
        internal const string FUNC_MEDIAPLAYER_REGISTERMEDIAPLAYERAUDIOSPECTRUMOBSERVER = "MediaPlayer_registerMediaPlayerAudioSpectrumObserver_226bb48";
        internal const string FUNC_MEDIAPLAYER_UNREGISTERMEDIAPLAYERAUDIOSPECTRUMOBSERVER = "MediaPlayer_unregisterMediaPlayerAudioSpectrumObserver_09064ce";
        internal const string FUNC_MEDIAPLAYER_SETAUDIODUALMONOMODE = "MediaPlayer_setAudioDualMonoMode_30c9672";
        internal const string FUNC_MEDIAPLAYER_GETPLAYERSDKVERSION = "MediaPlayer_getPlayerSdkVersion";
        internal const string FUNC_MEDIAPLAYER_GETPLAYSRC = "MediaPlayer_getPlaySrc";
        internal const string FUNC_MEDIAPLAYER_OPENWITHAGORACDNSRC = "MediaPlayer_openWithAgoraCDNSrc_e43f201";
        internal const string FUNC_MEDIAPLAYER_GETAGORACDNLINECOUNT = "MediaPlayer_getAgoraCDNLineCount";
        internal const string FUNC_MEDIAPLAYER_SWITCHAGORACDNLINEBYINDEX = "MediaPlayer_switchAgoraCDNLineByIndex_46f8ab7";
        internal const string FUNC_MEDIAPLAYER_GETCURRENTAGORACDNINDEX = "MediaPlayer_getCurrentAgoraCDNIndex";
        internal const string FUNC_MEDIAPLAYER_ENABLEAUTOSWITCHAGORACDN = "MediaPlayer_enableAutoSwitchAgoraCDN_5039d15";
        internal const string FUNC_MEDIAPLAYER_RENEWAGORACDNSRCTOKEN = "MediaPlayer_renewAgoraCDNSrcToken_e43f201";
        internal const string FUNC_MEDIAPLAYER_SWITCHAGORACDNSRC = "MediaPlayer_switchAgoraCDNSrc_7a174df";
        internal const string FUNC_MEDIAPLAYER_SWITCHSRC = "MediaPlayer_switchSrc_7a174df";
        internal const string FUNC_MEDIAPLAYER_PRELOADSRC = "MediaPlayer_preloadSrc_e43f201";
        internal const string FUNC_MEDIAPLAYER_PLAYPRELOADEDSRC = "MediaPlayer_playPreloadedSrc_3a2037f";
        internal const string FUNC_MEDIAPLAYER_UNLOADSRC = "MediaPlayer_unloadSrc_3a2037f";
        internal const string FUNC_MEDIAPLAYER_SETSPATIALAUDIOPARAMS = "MediaPlayer_setSpatialAudioParams_5035667";
        internal const string FUNC_MEDIAPLAYER_SETSOUNDPOSITIONPARAMS = "MediaPlayer_setSoundPositionParams_f282d50";
        #endregion terra IMediaPlayer

        internal const string FUNC_AUDIODEVICEMANAGER_GETPLAYBACKDEFAULTDEVICE = "AudioDeviceManager_getPlaybackDefaultDevice";
        internal const string FUNC_AUDIODEVICEMANAGER_GETRECORDINGDEAFULTDEVICE = "AudioDeviceManager_getRecordingDefaultDevice";
        #region terra IAudioDeviceManager
        internal const string FUNC_AUDIODEVICEMANAGER_ENUMERATEPLAYBACKDEVICES = "AudioDeviceManager_enumeratePlaybackDevices";
        internal const string FUNC_AUDIODEVICEMANAGER_ENUMERATERECORDINGDEVICES = "AudioDeviceManager_enumerateRecordingDevices";
        internal const string FUNC_AUDIODEVICEMANAGER_SETPLAYBACKDEVICE = "AudioDeviceManager_setPlaybackDevice_4ad5f6e";
        internal const string FUNC_AUDIODEVICEMANAGER_GETPLAYBACKDEVICE = "AudioDeviceManager_getPlaybackDevice_73b9872";
        internal const string FUNC_AUDIODEVICEMANAGER_GETPLAYBACKDEVICEINFO = "AudioDeviceManager_getPlaybackDeviceInfo_5540658";
        internal const string FUNC_AUDIODEVICEMANAGER_GETPLAYBACKDEVICEINFO2 = "AudioDeviceManager_getPlaybackDeviceInfo_ed3a96d";
        internal const string FUNC_AUDIODEVICEMANAGER_SETPLAYBACKDEVICEVOLUME = "AudioDeviceManager_setPlaybackDeviceVolume_46f8ab7";
        internal const string FUNC_AUDIODEVICEMANAGER_GETPLAYBACKDEVICEVOLUME = "AudioDeviceManager_getPlaybackDeviceVolume_915cb25";
        internal const string FUNC_AUDIODEVICEMANAGER_SETRECORDINGDEVICE = "AudioDeviceManager_setRecordingDevice_4ad5f6e";
        internal const string FUNC_AUDIODEVICEMANAGER_GETRECORDINGDEVICE = "AudioDeviceManager_getRecordingDevice_73b9872";
        internal const string FUNC_AUDIODEVICEMANAGER_GETRECORDINGDEVICEINFO = "AudioDeviceManager_getRecordingDeviceInfo_5540658";
        internal const string FUNC_AUDIODEVICEMANAGER_GETRECORDINGDEVICEINFO2 = "AudioDeviceManager_getRecordingDeviceInfo_ed3a96d";
        internal const string FUNC_AUDIODEVICEMANAGER_SETRECORDINGDEVICEVOLUME = "AudioDeviceManager_setRecordingDeviceVolume_46f8ab7";
        internal const string FUNC_AUDIODEVICEMANAGER_GETRECORDINGDEVICEVOLUME = "AudioDeviceManager_getRecordingDeviceVolume_915cb25";
        internal const string FUNC_AUDIODEVICEMANAGER_SETLOOPBACKDEVICE = "AudioDeviceManager_setLoopbackDevice_4ad5f6e";
        internal const string FUNC_AUDIODEVICEMANAGER_GETLOOPBACKDEVICE = "AudioDeviceManager_getLoopbackDevice_73b9872";
        internal const string FUNC_AUDIODEVICEMANAGER_SETPLAYBACKDEVICEMUTE = "AudioDeviceManager_setPlaybackDeviceMute_5039d15";
        internal const string FUNC_AUDIODEVICEMANAGER_GETPLAYBACKDEVICEMUTE = "AudioDeviceManager_getPlaybackDeviceMute_d942327";
        internal const string FUNC_AUDIODEVICEMANAGER_SETRECORDINGDEVICEMUTE = "AudioDeviceManager_setRecordingDeviceMute_5039d15";
        internal const string FUNC_AUDIODEVICEMANAGER_GETRECORDINGDEVICEMUTE = "AudioDeviceManager_getRecordingDeviceMute_d942327";
        internal const string FUNC_AUDIODEVICEMANAGER_STARTPLAYBACKDEVICETEST = "AudioDeviceManager_startPlaybackDeviceTest_3a2037f";
        internal const string FUNC_AUDIODEVICEMANAGER_STOPPLAYBACKDEVICETEST = "AudioDeviceManager_stopPlaybackDeviceTest";
        internal const string FUNC_AUDIODEVICEMANAGER_STARTRECORDINGDEVICETEST = "AudioDeviceManager_startRecordingDeviceTest_46f8ab7";
        internal const string FUNC_AUDIODEVICEMANAGER_STOPRECORDINGDEVICETEST = "AudioDeviceManager_stopRecordingDeviceTest";
        internal const string FUNC_AUDIODEVICEMANAGER_STARTAUDIODEVICELOOPBACKTEST = "AudioDeviceManager_startAudioDeviceLoopbackTest_46f8ab7";
        internal const string FUNC_AUDIODEVICEMANAGER_STOPAUDIODEVICELOOPBACKTEST = "AudioDeviceManager_stopAudioDeviceLoopbackTest";
        internal const string FUNC_AUDIODEVICEMANAGER_FOLLOWSYSTEMPLAYBACKDEVICE = "AudioDeviceManager_followSystemPlaybackDevice_5039d15";
        internal const string FUNC_AUDIODEVICEMANAGER_FOLLOWSYSTEMRECORDINGDEVICE = "AudioDeviceManager_followSystemRecordingDevice_5039d15";
        internal const string FUNC_AUDIODEVICEMANAGER_FOLLOWSYSTEMLOOPBACKDEVICE = "AudioDeviceManager_followSystemLoopbackDevice_5039d15";
        internal const string FUNC_AUDIODEVICEMANAGER_RELEASE = "AudioDeviceManager_release";
        #endregion terra IAudioDeviceManager

        #region terra IVideoDeviceManager
        internal const string FUNC_VIDEODEVICEMANAGER_ENUMERATEVIDEODEVICES = "VideoDeviceManager_enumerateVideoDevices";
        internal const string FUNC_VIDEODEVICEMANAGER_SETDEVICE = "VideoDeviceManager_setDevice_4ad5f6e";
        internal const string FUNC_VIDEODEVICEMANAGER_GETDEVICE = "VideoDeviceManager_getDevice_73b9872";
        internal const string FUNC_VIDEODEVICEMANAGER_NUMBEROFCAPABILITIES = "VideoDeviceManager_numberOfCapabilities_3a2037f";
        internal const string FUNC_VIDEODEVICEMANAGER_GETCAPABILITY = "VideoDeviceManager_getCapability_ddeefdd";
        internal const string FUNC_VIDEODEVICEMANAGER_STARTDEVICETEST = "VideoDeviceManager_startDeviceTest_a55f55f";
        internal const string FUNC_VIDEODEVICEMANAGER_STOPDEVICETEST = "VideoDeviceManager_stopDeviceTest";
        internal const string FUNC_VIDEODEVICEMANAGER_RELEASE = "VideoDeviceManager_release";
        #endregion terra IVideoDeviceManager

        #region terra ILocalSpatialAudioEngine
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_RELEASE = "LocalSpatialAudioEngine_release";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_INITIALIZE = "LocalSpatialAudioEngine_initialize_cf94fbf";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_UPDATEREMOTEPOSITION = "LocalSpatialAudioEngine_updateRemotePosition_adc0909";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_UPDATEREMOTEPOSITIONEX = "LocalSpatialAudioEngine_updateRemotePositionEx_f0252d9";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_REMOVEREMOTEPOSITION = "LocalSpatialAudioEngine_removeRemotePosition_c8d091a";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_REMOVEREMOTEPOSITIONEX = "LocalSpatialAudioEngine_removeRemotePositionEx_58a9850";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_CLEARREMOTEPOSITIONSEX = "LocalSpatialAudioEngine_clearRemotePositionsEx_c81e1a4";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_UPDATESELFPOSITIONEX = "LocalSpatialAudioEngine_updateSelfPositionEx_502183a";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_SETMAXAUDIORECVCOUNT = "LocalSpatialAudioEngine_setMaxAudioRecvCount_46f8ab7";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_SETAUDIORECVRANGE = "LocalSpatialAudioEngine_setAudioRecvRange_685e803";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_SETDISTANCEUNIT = "LocalSpatialAudioEngine_setDistanceUnit_685e803";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_UPDATESELFPOSITION = "LocalSpatialAudioEngine_updateSelfPosition_9c9930f";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_UPDATEPLAYERPOSITIONINFO = "LocalSpatialAudioEngine_updatePlayerPositionInfo_b37c59d";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_SETPARAMETERS = "LocalSpatialAudioEngine_setParameters_3a2037f";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_MUTELOCALAUDIOSTREAM = "LocalSpatialAudioEngine_muteLocalAudioStream_5039d15";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_MUTEALLREMOTEAUDIOSTREAMS = "LocalSpatialAudioEngine_muteAllRemoteAudioStreams_5039d15";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_MUTEREMOTEAUDIOSTREAM = "LocalSpatialAudioEngine_muteRemoteAudioStream_dbdc15a";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_SETREMOTEAUDIOATTENUATION = "LocalSpatialAudioEngine_setRemoteAudioAttenuation_74c3e98";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_SETZONES = "LocalSpatialAudioEngine_setZones_414a27e";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_SETPLAYERATTENUATION = "LocalSpatialAudioEngine_setPlayerAttenuation_a15bc51";
        internal const string FUNC_LOCALSPATIALAUDIOENGINE_CLEARREMOTEPOSITIONS = "LocalSpatialAudioEngine_clearRemotePositions";
        #endregion terra ILocalSpatialAudioEngine

        internal const string FUNC_MEDIAENGINE_UNREGISTERAUDIOFRAMEOBSERVER = "MediaEngine_unregisterAudioFrameObserver";
        internal const string FUNC_MEDIAENGINE_UNREGISTERVIDEOFRAMEOBSERVER = "MediaEngine_unregisterVideoFrameObserver";
        internal const string FUNC_MEDIAENGINE_UNREGISTERVIDEOENCODEDFRAMEOBSERVER = "MediaEngine_unregisterVideoEncodedFrameObserver";
        internal const string FUNC_MEDIAENGINE_UNREGISTERFACEINFOOBSERVER = "MediaEngine_unregisterFaceInfoObserver";
        #region terra IMediaEngine
        internal const string FUNC_MEDIAENGINE_REGISTERAUDIOFRAMEOBSERVER = "MediaEngine_registerAudioFrameObserver_d873a64";
        internal const string FUNC_MEDIAENGINE_REGISTERVIDEOFRAMEOBSERVER = "MediaEngine_registerVideoFrameObserver_2cc0ef1";
        internal const string FUNC_MEDIAENGINE_REGISTERVIDEOENCODEDFRAMEOBSERVER = "MediaEngine_registerVideoEncodedFrameObserver_d45d579";
        internal const string FUNC_MEDIAENGINE_REGISTERFACEINFOOBSERVER = "MediaEngine_registerFaceInfoObserver_0303ed6";
        internal const string FUNC_MEDIAENGINE_PUSHAUDIOFRAME = "MediaEngine_pushAudioFrame_c71f4ab";
        internal const string FUNC_MEDIAENGINE_PULLAUDIOFRAME = "MediaEngine_pullAudioFrame_2c74a9c";
        internal const string FUNC_MEDIAENGINE_SETEXTERNALVIDEOSOURCE = "MediaEngine_setExternalVideoSource_fff99b6";
        internal const string FUNC_MEDIAENGINE_SETEXTERNALREMOTEEGLCONTEXT = "MediaEngine_setExternalRemoteEglContext_f337cbf";
        internal const string FUNC_MEDIAENGINE_SETEXTERNALAUDIOSOURCE = "MediaEngine_setExternalAudioSource_e6538be";
        internal const string FUNC_MEDIAENGINE_CREATECUSTOMAUDIOTRACK = "MediaEngine_createCustomAudioTrack_5a0bf1a";
        internal const string FUNC_MEDIAENGINE_DESTROYCUSTOMAUDIOTRACK = "MediaEngine_destroyCustomAudioTrack_6178b5d";
        internal const string FUNC_MEDIAENGINE_SETEXTERNALAUDIOSINK = "MediaEngine_setExternalAudioSink_d275ce0";
        internal const string FUNC_MEDIAENGINE_ENABLECUSTOMAUDIOLOCALPLAYBACK = "MediaEngine_enableCustomAudioLocalPlayback_5f38e8a";
        internal const string FUNC_MEDIAENGINE_PUSHVIDEOFRAME = "MediaEngine_pushVideoFrame_4e544e2";
        internal const string FUNC_MEDIAENGINE_PUSHENCODEDVIDEOIMAGE = "MediaEngine_pushEncodedVideoImage_e71452b";
        internal const string FUNC_MEDIAENGINE_RELEASE = "MediaEngine_release";
        #endregion terra IMediaEngine

        #region terra IMediaPlayerCacheManager
        internal const string FUNC_MEDIAPLAYERCACHEMANAGER_REMOVEALLCACHES = "MediaPlayerCacheManager_removeAllCaches";
        internal const string FUNC_MEDIAPLAYERCACHEMANAGER_REMOVEOLDCACHE = "MediaPlayerCacheManager_removeOldCache";
        internal const string FUNC_MEDIAPLAYERCACHEMANAGER_REMOVECACHEBYURI = "MediaPlayerCacheManager_removeCacheByUri_3a2037f";
        internal const string FUNC_MEDIAPLAYERCACHEMANAGER_SETCACHEDIR = "MediaPlayerCacheManager_setCacheDir_3a2037f";
        internal const string FUNC_MEDIAPLAYERCACHEMANAGER_SETMAXCACHEFILECOUNT = "MediaPlayerCacheManager_setMaxCacheFileCount_46f8ab7";
        internal const string FUNC_MEDIAPLAYERCACHEMANAGER_SETMAXCACHEFILESIZE = "MediaPlayerCacheManager_setMaxCacheFileSize_f631116";
        internal const string FUNC_MEDIAPLAYERCACHEMANAGER_ENABLEAUTOREMOVECACHE = "MediaPlayerCacheManager_enableAutoRemoveCache_5039d15";
        internal const string FUNC_MEDIAPLAYERCACHEMANAGER_GETCACHEDIR = "MediaPlayerCacheManager_getCacheDir_c9551e8";
        internal const string FUNC_MEDIAPLAYERCACHEMANAGER_GETMAXCACHEFILECOUNT = "MediaPlayerCacheManager_getMaxCacheFileCount";
        internal const string FUNC_MEDIAPLAYERCACHEMANAGER_GETMAXCACHEFILESIZE = "MediaPlayerCacheManager_getMaxCacheFileSize";
        internal const string FUNC_MEDIAPLAYERCACHEMANAGER_GETCACHEFILECOUNT = "MediaPlayerCacheManager_getCacheFileCount";
        #endregion terra IMediaPlayerCacheManager

        internal const string FUNC_MEDIARECORDER_UNSETMEDIARECORDEROBSERVER = "MediaRecorder_unsetMediaRecorderObserver";
        #region terra IMediaRecorder
        internal const string FUNC_MEDIARECORDER_SETMEDIARECORDEROBSERVER = "MediaRecorder_setMediaRecorderObserver_e1f7340";
        internal const string FUNC_MEDIARECORDER_STARTRECORDING = "MediaRecorder_startRecording_94480b3";
        internal const string FUNC_MEDIARECORDER_STOPRECORDING = "MediaRecorder_stopRecording";
        #endregion terra IMediaRecorder

        #region terra IMusicContentCenter
        internal const string FUNC_MUSICCONTENTCENTER_INITIALIZE = "MusicContentCenter_initialize_df70304";
        internal const string FUNC_MUSICCONTENTCENTER_RENEWTOKEN = "MusicContentCenter_renewToken_3a2037f";
        internal const string FUNC_MUSICCONTENTCENTER_RELEASE = "MusicContentCenter_release";
        internal const string FUNC_MUSICCONTENTCENTER_REGISTEREVENTHANDLER = "MusicContentCenter_registerEventHandler_ae49451";
        internal const string FUNC_MUSICCONTENTCENTER_UNREGISTEREVENTHANDLER = "MusicContentCenter_unregisterEventHandler";
        internal const string FUNC_MUSICCONTENTCENTER_CREATEMUSICPLAYER = "MusicContentCenter_createMusicPlayer";
        internal const string FUNC_MUSICCONTENTCENTER_DESTROYMUSICPLAYER = "MusicContentCenter_destroyMusicPlayer_876d086";
        internal const string FUNC_MUSICCONTENTCENTER_GETMUSICCHARTS = "MusicContentCenter_getMusicCharts_66d4ecd";
        internal const string FUNC_MUSICCONTENTCENTER_GETMUSICCOLLECTIONBYMUSICCHARTID = "MusicContentCenter_getMusicCollectionByMusicChartId_8cd0b4d";
        internal const string FUNC_MUSICCONTENTCENTER_SEARCHMUSIC = "MusicContentCenter_searchMusic_3f8cf09";
        internal const string FUNC_MUSICCONTENTCENTER_PRELOAD = "MusicContentCenter_preload_bd5a5a3";
        internal const string FUNC_MUSICCONTENTCENTER_PRELOAD2 = "MusicContentCenter_preload_d3baeab";
        internal const string FUNC_MUSICCONTENTCENTER_REMOVECACHE = "MusicContentCenter_removeCache_f631116";
        internal const string FUNC_MUSICCONTENTCENTER_GETCACHES = "MusicContentCenter_getCaches_c4f9978";
        internal const string FUNC_MUSICCONTENTCENTER_ISPRELOADED = "MusicContentCenter_isPreloaded_f631116";
        internal const string FUNC_MUSICCONTENTCENTER_GETLYRIC = "MusicContentCenter_getLyric_5ab5efd";
        internal const string FUNC_MUSICCONTENTCENTER_GETSONGSIMPLEINFO = "MusicContentCenter_getSongSimpleInfo_d3baeab";
        internal const string FUNC_MUSICCONTENTCENTER_GETINTERNALSONGCODE = "MusicContentCenter_getInternalSongCode_3a3d1e7";
        #endregion terra IMusicContentCenter

        #region terra IMusicPlayer
        internal const string FUNC_MUSICPLAYER_OPEN = "MusicPlayer_open_303b92e";
        internal const string FUNC_MUSICPLAYER_SETPLAYMODE = "MusicPlayer_setPlayMode_748bee0";
        #endregion terra IMusicPlayer

        #region terra IH265Transcoder
        internal const string FUNC_H265TRANSCODER_ENABLETRANSCODE = "H265Transcoder_enableTranscode_a0779eb";
        internal const string FUNC_H265TRANSCODER_QUERYCHANNEL = "H265Transcoder_queryChannel_a0779eb";
        internal const string FUNC_H265TRANSCODER_TRIGGERTRANSCODE = "H265Transcoder_triggerTranscode_a0779eb";
        internal const string FUNC_H265TRANSCODER_REGISTERTRANSCODEROBSERVER = "H265Transcoder_registerTranscoderObserver_e1ee996";
        internal const string FUNC_H265TRANSCODER_UNREGISTERTRANSCODEROBSERVER = "H265Transcoder_unregisterTranscoderObserver_e1ee996";
        #endregion terra IH265Transcoder
    }
}